plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.cloudkit_null'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation project(path: ':common:lib_base')
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "com.google.code.gson:gson:${gson}"
}