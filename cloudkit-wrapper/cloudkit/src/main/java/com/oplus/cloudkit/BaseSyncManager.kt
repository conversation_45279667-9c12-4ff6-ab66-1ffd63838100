/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - BaseSyncManager.kt
** Description:
** Version: 1.0
** Date : 2022/8/13
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/13      1.0     create file
****************************************************************/
package com.oplus.cloudkit

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.heytap.cloudkit.libcommon.bean.io.CloudStopType
import com.heytap.cloudkit.libcommon.config.CloudConfig
import com.heytap.cloudkit.libcommon.config.CloudEnv
import com.heytap.cloudkit.libcommon.config.CloudLogLevel
import com.heytap.cloudkit.libcommon.log.CloudKitLogUtil
import com.heytap.cloudkit.libcommon.log.CloudLogConfigMsg
import com.heytap.cloudkit.libcommon.log.CloudPushLogMsg
import com.heytap.cloudkit.libcommon.log.ICloudConsoleLogProvider
import com.heytap.cloudkit.libsync.cloudswitch.bean.CloudSyncState
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.push.CloudPushMessage
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.XorEncryptUtils.enOrDecrypt
import com.oplus.cloudkit.util.CloudAccountAgentImpl
import com.oplus.cloudkit.util.ConstantsWrapper
import com.oplus.note.cloudkit.BuildConfig
import com.oplus.note.logger.AppLogger
import com.oplus.note.push.PushAgentFactory
import com.oplus.note.push.PushMessageReceiver
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

interface SyncStateChangeListener{
    fun onResetState()
    fun onReceivePushMessage()
}

object BaseSyncManager {

    private const val TAG = "SyncManager"
    const val ACTION_SYNC_INIT_FINISH = "com.oplus.note.syncInited"
    private var listener: SyncStateChangeListener? = null
    private val sensitiveTag = arrayOf("CloudMetaDataRecoveryNoIpcAdapter")
    /** init 方法是否执行*/
    var syncInit = false
        private set

    fun setResetStateListener(l: SyncStateChangeListener?) {
        listener = l
    }

    /**
     * @param env 0 正式环境， 1 预发布环境， else 测试环境
     */
    fun init(
        context: Application,
        env: Int,
        accountAppCode: String,
        isExport: Boolean = false,
        isOnePlus: Boolean = false,
    ) {
        ConfigUtils.initSupportCloudKit(true)
        val cloudEnv: CloudEnv = when (env) {
            0 -> {
                CloudEnv.RELEASE
            }
            1 -> {
                CloudEnv.PRE
            }
            else -> {
                CloudEnv.TEST1
            }
        }
        val host = when (env) {
            0 -> {
                if (isExport) {
                    // "heytapmobile.com"
                    enOrDecrypt("idxu`qlnchmd/bnl", 1)
                } else {
                    // "heytapmobi.com"
                    enOrDecrypt("idxu`qlnch/bnl", 1)
                }
            }
            1 -> {
                // "oppomobile.com"
                enOrDecrypt("nqqnlnchmd/bnl", 1)
            }
            else -> {
                // "wanyol.com"
                enOrDecrypt("v`oxnm/bnl", 1)
            }
        }.also {
            AppLogger.DEBUG.d(TAG) {
                "host is $it"
            }
        }
        // 日志控制台输出级别，应用发布release版本得设置为LogLevel.NONE
        val logLevel = when (env) {
            0 -> {
                CloudLogLevel.LEVEL_NONE
            }
            1 -> {
                CloudLogLevel.LEVEL_VERBOSE
            }
            else -> {
                CloudLogLevel.LEVEL_VERBOSE
            }
        }
        val isOnePlusExport = isExport && isOnePlus
        val appKey = when (env) {
            0, 1 -> {
                if (isOnePlusExport) {
                    "826cd256c3744b3bbab3bcf9376bc05d"
                } else {
                    "bd09181d73bd45a18bdd8f46e246551d"
                }
            }
            else -> {
                if (isOnePlusExport) {
                    "cee94627383748ec949a857a29752c46"
                } else {
                    "01ab4ca012464cab9b8ded3254c22db0"
                }
            }
        }
        val appSecretKey = when (env) {
            0, 1 -> {
                if (isOnePlusExport) {
                    "2106c0e8172a88e92ed2360d0ee90478848146cecb55f82db700fdb88c30e0ce"
                } else {
                    "bd476a8a1b293e415d0631f5af4bd164936abc96dbe4fca3103740a6918ff00e"
                }
            }
            else -> {
                if (isOnePlusExport) {
                    "4173be12d70b748766ff56d966d2349a1f3df4358138f4915c749998c2bcb47c"
                } else {
                    "cf153b51484c6f7614e533463053c98494d7181696fb0289ea1090c51c441c14"
                }
            }
        }

        val isOppoRealmeExport = isExport && !isOnePlus
        /**
         * 业务方接入测试环境或者线上环境域名请一定在开放平台确认并联系云服务"服务端"开发确认!!!!!!!!,以下仅仅是目前服务端确认的，使用前请确认
         * 业务测试  wanyol.com
         * http、https, 建议业务使用https，除非需要抓包分析问题可以使用http
         *
         * 业务生产线上  heytapmobi.com
         * 仅https
         */
        //是云服务demo专用的开发和测试环境的话，启用http，因为服务端不支持https
        //云服务demo测试专用域名更多域名相关问题见开发平台文档或者咨询云服务服务端
        val cloudConfig = CloudConfig.Builder(context)
            .setAppId("d8086d7a93f46417")
            .setAppPkgId(if (isOnePlusExport) "ca1c9092e72ddc14" else "7d3b3b0ff69581d6")
            .setAppKey(appKey)
            .setAppSecretKey(appSecretKey)
            .setHost(host)
            .setConsoleLogLevel(if(BuildConfig.DEBUG) CloudLogLevel.LEVEL_VERBOSE else logLevel)
            .setEnv(cloudEnv)
            .setCloudSupportRegionList(getSupportRegionList(isOppoRealmeExport))
            .setWriteLogFile(false)
            .setMaxWaitFileCount(Int.MAX_VALUE)
            .setCloudConsoleLogProvider(object : ICloudConsoleLogProvider {
                override fun d(tag: String?, message: String?) {
                    checkSensitive(tag, message) {
                        AppLogger.BASIC.d(tag, message)
                    }
                }

                override fun i(tag: String?, message: String?) {
                    checkSensitive(tag, message) {
                        AppLogger.BASIC.i(tag, message)
                    }
                }

                override fun w(tag: String?, message: String?) {
                    checkSensitive(tag, message) {
                        AppLogger.BASIC.w(tag, message)
                    }
                }

                override fun e(tag: String?, message: String?) {
                    checkSensitive(tag, message) {
                        AppLogger.BASIC.e(tag, message)
                    }
                }
            })
            .build()
        CloudSyncManager.getInstance().init(cloudConfig, CloudAccountAgentImpl(context, accountAppCode))
        syncInit = true
        registerPushMessage()
        LocalBroadcastManager.getInstance(context).sendBroadcast(Intent(ACTION_SYNC_INIT_FINISH))
    }

    fun clearSyncDataByLogout() {
        AppLogger.CLOUDKIT.d(TAG, "start to clearSyncDataByLogout")
        val csm = CloudSyncManager.getInstance()
        // 1.stop io tasks
        csm.stopTransferFilesByModule(ConstantsWrapper.MODULE_NOTE, CloudStopType.MANUAL)
        csm.stopTransferFilesByModule(ConstantsWrapper.MODULE_TODO, CloudStopType.MANUAL)
        // 2.stop meta data transfer tasks
        csm.stopAllBackupAndRecoveryMetaData()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 3.reset sync switch
            kotlin.runCatching {
                csm.setSyncSwitch(SwitchState.CLOSE, true)
            }.onFailure {
                AppLogger.BASIC.d(TAG, "clearSyncDataByLogout ${it.message}")
            }
            // 4.reset sync state
            csm.setSyncState(CloudSyncState.DEFAULT)
        }
        // 5.clear sysVersion,will trigger full-sync when open sync switch
        csm.clearSysVersionByModule(ConstantsWrapper.MODULE_NOTE)
        csm.clearSysVersionByModule(ConstantsWrapper.MODULE_TODO)
        // 6.clear cloudkit internal data.
        csm.clearUserDataOnLogout()
        // 7.finally stop sync service
        csm.stopCloudSyncService()
        AppLogger.CLOUDKIT.d(TAG, "end to clearSyncDataByLogout")
    }

    /**
     * Different from logout,there are not:
     * 1.set switch close;
     * 2.clear user data
     */
    suspend fun clearSyncDataByClose(): Unit = withContext(Dispatchers.IO) {
        val isLogin = CloudAccountManager.getInstance().isLogin
        if (!isLogin) {
            return@withContext
        }
        AppLogger.CLOUDKIT.d(TAG, "start to clearSyncDataByClose")
        val csm = CloudSyncManager.getInstance()
        // 1.stop io tasks
        csm.stopTransferFilesByModule(ConstantsWrapper.MODULE_NOTE, CloudStopType.MANUAL)
        csm.stopTransferFilesByModule(ConstantsWrapper.MODULE_TODO, CloudStopType.MANUAL)
        // 2.stop meta data transfer tasks
        csm.stopAllBackupAndRecoveryMetaData()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 3.reset sync state
            csm.setSyncState(CloudSyncState.DEFAULT)
        }
        // 4.clear sysVersion,will trigger full-sync when open sync switch
        csm.clearSysVersionByModule(ConstantsWrapper.MODULE_NOTE)
        csm.clearSysVersionByModule(ConstantsWrapper.MODULE_TODO)
        // 5.finally stop sync service
        csm.stopCloudSyncService()
        AppLogger.CLOUDKIT.d(TAG, "end to clearSyncDataByClose")
    }

    @RequiresApi(Build.VERSION_CODES.R)
    fun setSyncState(isStart: Boolean) {
        if (isStart) {
            CloudSyncManager.getInstance().setSyncState(CloudSyncState.START)
        } else {
            CloudSyncManager.getInstance().setSyncState(CloudSyncState.FINISH)
        }
    }

    fun resetRunningState() {
        listener?.onResetState()
    }

    private val mPushListener = object : PushMessageReceiver {
        override fun processMessage(context: Context?, msgContent: String): Boolean {
            val isCloudKitMessage = CloudSyncManager.isCloudPushMessage(msgContent)
            if (isCloudKitMessage) {
                val message = CloudSyncManager.parsePushMessage(msgContent) ?: return false
                //收到同步消息
                if (CloudPushMessage.HandleType.SYNC == message.handleType) {
                    listener?.onReceivePushMessage()
                }
                return true
            } else if (msgContent.contains("\"action\":\"" + CloudPushMessage.ACTION_LOG_UPLOAD)) {
                return handleUploadCloudKitLogMessage(context, msgContent)
            }
            return false
        }
    }

    /**
     * 识别日志打捞的push ，并且调用sdk主动上报接口CloudKitLogUtil.pushReport(context)
     */
    private fun handleUploadCloudKitLogMessage(context: Context?, messageContent: String): Boolean {
        var cloudLogConfigMsg: CloudLogConfigMsg? = null
        kotlin.runCatching {
            cloudLogConfigMsg = Gson().fromJson(messageContent, CloudPushLogMsg::class.java).content
        }.onFailure {
            cloudLogConfigMsg = null
        }
        //上报 cloudkit 日志，或者便签的日志
        if (cloudLogConfigMsg?.tracePkg == CloudKitLogUtil.getCloudKitLogPkg()) {
            GlobalScope.launch(Dispatchers.IO) {
                CloudKitLogUtil.pushReport(context)
            }
            return true
        }
        return false
    }

    //注册 push 回调
    private fun registerPushMessage() {
        PushAgentFactory.get()?.addPushMessageReceiver(mPushListener)
    }

    private fun getSupportRegionList(isOppoRealmeExport: Boolean): List<String> {
        val supportRegionList = mutableListOf<String>()

        if (isOppoRealmeExport) {
            // 中国
            supportRegionList.add("cn")
            // 日本
            supportRegionList.add("jp")
            // 台湾
            supportRegionList.add("tw")
            // 泰国
            supportRegionList.add("th")
            // 印尼
            supportRegionList.add("id")
            // 菲律宾
            supportRegionList.add("ph")
            // 柬埔寨
            supportRegionList.add("kh")
            // 印度
            supportRegionList.add("in")
            // 新加坡
            supportRegionList.add("sg")
            // 马来西亚
            supportRegionList.add("my")
            // 越南
            supportRegionList.add("vn")
            // 亚太，在os12.1及以上os系统会将中国香港、缅甸、老挝、尼泊尔、柬埔寨 4个区域打点结果获取为apc，云服务之前是支持柬埔寨的，所以需要配置
            supportRegionList.add("apc")
        } else { // 一加便签云服务功能支持中国，印度区域
            // 中国
            supportRegionList.add("cn")
            // 印度
            supportRegionList.add("in")
        }

        return supportRegionList
    }

    private fun checkSensitive(
        tag: String?,
        msg: String?,
        logAction: () -> Unit
    ) {
        if (sensitiveTag.any { tag?.contains(it, ignoreCase = true) == true } && !BuildConfig.DEBUG) {
            return
        }
        logAction.invoke()
    }
}