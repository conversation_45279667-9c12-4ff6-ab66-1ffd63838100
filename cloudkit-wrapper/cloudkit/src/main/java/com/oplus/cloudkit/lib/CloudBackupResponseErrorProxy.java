/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudBackupResponseErrorProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError;

import java.util.ArrayList;
import java.util.List;

public class CloudBackupResponseErrorProxy {

    public static List<CloudBackupResponseErrorProxy> toProxyList(List<CloudBackupResponseError> oriList) {
        ArrayList<CloudBackupResponseErrorProxy> list = new ArrayList<>(oriList.size());
        for (CloudBackupResponseError item: oriList){
            list.add(new CloudBackupResponseErrorProxy(item));
        }
        return list;
    }

    private CloudBackupResponseError mError;

    private CloudBackupResponseErrorProxy() {
    }

    CloudBackupResponseErrorProxy(CloudBackupResponseError error) {
        mError = error;
    }

    public CloudBackupResponseError getCloudBackupResponseError() {
        return mError;
    }

    public String getSysRecordId() {
        return mError.getSysRecordId();
    }

    public void setSysRecordId(String sysRecordId) {
        mError.setSysRecordId(sysRecordId);
    }

    public List<String> getErrorOcloudIds() {
        return mError.getErrorOcloudIds();
    }

    public void setErrorOcloudIds(List<String> errorOcloudIds) {
        mError.setErrorOcloudIds(errorOcloudIds);
    }

    public String getSysRecordInfo() {
        return mError.getSysRecordInfo();
    }

    public void setSysRecordInfo(String sysRecordInfo) {
        mError.setSysRecordInfo(sysRecordInfo);
    }

    public String getCustomRecordInfo() {
        return mError.getCustomRecordInfo();
    }

    public void setCustomRecordInfo(String customRecordInfo) {
        mError.setCustomRecordInfo(customRecordInfo);
    }

    public int getSubServerErrorCode() {
        if (mError.getCloudKitError() != null) {
            return mError.getCloudKitError().getSubServerErrorCode();
        } else {
            return 0;
        }
    }

    @Override
    public String toString() {
        return mError.toString();
    }
}
