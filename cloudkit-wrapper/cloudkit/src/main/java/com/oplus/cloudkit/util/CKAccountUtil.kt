package com.oplus.cloudkit.util

import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.oplus.note.logger.AppLogger
import com.platform.usercenter.account.ams.AcAccountConfig
import com.platform.usercenter.account.ams.AcAccountManager
import com.platform.usercenter.account.ams.apis.AcCallback
import com.platform.usercenter.account.ams.apis.beans.AcAccountToken
import com.platform.usercenter.account.ams.apis.beans.AcApiResponse
import com.platform.usercenter.account.ams.ipc.ResponseEnum
import com.platform.usercenter.sdk.verifysystembasic.AcVerifyAgent
import com.platform.usercenter.sdk.verifysystembasic.callback.VerifySysCallBack
import com.platform.usercenter.sdk.verifysystembasic.data.AcOperateType
import com.platform.usercenter.sdk.verifysystembasic.data.AcVerifyResultData
import com.platform.usercenter.sdk.verifysystembasic.data.VerifyParam
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

object CKAccountUtil {
    private const val TAG = "CKAccountUtil"
    //测试环境
//    private const val APP_ID = "********"
//    private const val APP_KEY = "c818550bb41b4b7fa3b195cf0f22dc25"
//    private const val APP_SECRET = "8176bac2a9d940f4955b4a527c88eef6"
//    private const val BUSINESSID = "8b8b6defcaa14dcea85ca6233592d136"
    //正式环境
    private const val APP_ID = "********"
    private const val APP_KEY = "f130b736e0b84126b271d1c098c4d271"
    private const val APP_SECRET = "d9b249acf727419a9b53e9b6acdfeec0"
    private const val BUSINESSID = "a63110acc2fb4c19b12a19c25c45225a"

    private const val CODE_USER_NOT_EXIST = 1112001
    private const val CODE_PARAMETERS_ILLEGAL = 5010000
    private const val CODE_MSP_ERROR = 5410108

    private var lastErrorCode = 0 //记录上次返回的错误码

    interface AuthAccountResultCallback {
        fun onResult(isLogin: Boolean)
    }


    /**
     * This check will auto trigger re-login process when needed
     */
    @JvmStatic
    fun checkAccountIsLogin(activity: AppCompatActivity, needNewSDK: Boolean, authCallback: AuthAccountResultCallback) {
        if (needNewSDK) {
            verify(activity, authCallback)
        } else {
            activity.lifecycleScope.launch {
                val isLogin = withContext(Dispatchers.IO) {
                    CloudAccountManager.getInstance().isLogin
                }

                if (!isLogin) {
                    withContext(Dispatchers.Main) {
                        CloudAccountManager.getInstance().reqSignInAccount {
                        }
                    }
                }

                AppLogger.CLOUDKIT.d(TAG, "checkAccount: isLogin = $isLogin")
                authCallback.onResult(isLogin)
            }
        }
    }

   @JvmStatic
   private fun verify(activity: AppCompatActivity, authCallback: AuthAccountResultCallback) {
        AppLogger.BASIC.d(TAG, "verify")
        val config: AcAccountConfig = AcAccountConfig.Builder().setAppId(APP_ID)
            .setAppKey(APP_KEY).create()
        AcAccountManager.init(activity, config)
        auth(activity, authCallback)
    }

    @JvmStatic
    private fun auth(activity: AppCompatActivity, authCallback: AuthAccountResultCallback) {
        activity.lifecycleScope.launch(Dispatchers.Default) {
            // 获取token
            val response = AcAccountManager.getClient(APP_ID).accountToken
            AppLogger.BASIC.d(TAG, "response = $response")
            if (response.code == ResponseEnum.ERROR_NOT_AUTH.code || lastErrorCode == ResponseEnum.NET_ACCOUNT_EXPIRED.code) {
                // 如果没有授权先调用授权
                AcAccountManager.getClient(APP_ID).login(activity, true, AcCallbackImpl(activity, authCallback))
            } else {
                if (response.isSuccess()) {
                    // 获取成功
                    toVerify(response.data?.accessToken.toString(), activity, authCallback)
                } else {
                    authCallback.onResult(false)
                }
            }
        }
    }

    @JvmStatic
    private fun toVerify(token: String, activity: AppCompatActivity, authCallback: AuthAccountResultCallback) {
        val param =
            VerifyParam.Builder()
                .userToken(token)
                .bizk(APP_KEY)
                .bizs(APP_SECRET)
                .businessId(BUSINESSID)
                .appId(APP_ID)
                .operateType(AcOperateType.VERIFY_TYPE)
                .create()
        //强制走SDK 校验
        AcVerifyAgent.startVerifyForResult(
            activity,
            param,
            VerifySycCallBackImpl(activity, authCallback)
        )
    }

    private class VerifySycCallBackImpl(act: AppCompatActivity, val authCallback: AuthAccountResultCallback) :
        VerifySysCallBack {

        private val weakRef = WeakReference(act)
        override fun callBack(it: AcVerifyResultData) {
            AppLogger.BASIC.d(TAG, "callBack code = ${it.code}")
            authCallback.onResult(it.code == ResponseEnum.SUCCESS.code)
            lastErrorCode = it.code //code = 4043,表示账号登录态失效，需要重新授权
            weakRef.get()?.let { activity ->
                when (it.code) {
                    ResponseEnum.DEVICE_NETWORK_NO_AVAILABLE.code ->
                        Toast.makeText(activity, com.oplus.note.baseres.R.string.network_unavailable, Toast.LENGTH_SHORT).show()
                    ResponseEnum.VERIFY_RESULT_CODE_FAILED.code,
                    ResponseEnum.COMPLETE_RESULT_CODE_FAILED.code,
                    ResponseEnum.AUTH_VERIFY_ERROR.code,
                    CODE_MSP_ERROR ->
                        Toast.makeText(activity, com.oplus.note.baseres.R.string.cloud_service_busy_try_again_later, Toast.LENGTH_SHORT).show()
                    ResponseEnum.NET_ACCOUNT_EXPIRED.code, CODE_USER_NOT_EXIST, CODE_PARAMETERS_ILLEGAL ->
                        Toast.makeText(activity, com.oplus.note.baseres.R.string.cloud_verification_fail, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private class AcCallbackImpl(act: AppCompatActivity, val authCallback: AuthAccountResultCallback) :
        AcCallback<AcApiResponse<AcAccountToken>> {
        val weakRef = WeakReference(act)
        override fun call(response: AcApiResponse<AcAccountToken>) {
            weakRef.get()?.let { activity ->
                if (response.isSuccess()) {
                    toVerify(response.data.toString(), activity, authCallback)
                } else {
                    authCallback.onResult(false)
                }
            }
        }
    }
}