package com.oplus.cloudkit.util

import android.content.Context
import com.heytap.cloudkit.libcommon.account.CloudAccount
import com.heytap.cloudkit.libcommon.account.ICloudAccountAgent
import com.heytap.cloudkit.libcommon.account.ICloudAccountCallback
import com.heytap.cloudkit.libcommon.provider.LogoutDeleteDataResult
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.http.AccountNameTask
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.heytap.usercenter.accountsdk.model.SignInAccount
import com.oplus.note.logger.AppLogger

class CloudAccountAgentImpl(context: Context, private val accountAppCode: String) : ICloudAccountAgent {

    companion object {
        private const val TAG = "CloudAccountAgentImpl"
    }

    private val mContext: Context = context.applicationContext

    override fun getSignInAccount(callback: ICloudAccountCallback) {
        //token、isLogin、ssoId这三个字段是必须的
        val accountEntity: AccountEntity? = AccountAgent.getAccountEntity(mContext, accountAppCode)
        val account = CloudAccount()
        if (accountEntity != null) {
            account.token = accountEntity.authToken
            account.userId = accountEntity.ssoid
            account.username = accountEntity.accountName
            account.isLogin = accountEntity.authToken != null && accountEntity.authToken.isNotEmpty() //获取对象不为null 且authToken 不为空 表示账号已登录
        } else {
            AppLogger.CLOUDKIT.e(TAG, "getSignInAccount failed with null account entity.")
        }
        callback.onComplete(account)
    }

    override fun reqSignInAccount(callback: ICloudAccountCallback) {
        AppLogger.BASIC.d(TAG, "reqSignInAccount")
        AccountAgent.reqSignInAccount(mContext, accountAppCode, OnReqAccountCallbackImpl(callback))
    }

    override fun refreshTokenWhenTokenExpire(callback: ICloudAccountCallback) {
        AppLogger.BASIC.d(TAG, "refreshTokenWhenTokenExpire")
    }

    override fun deleteSyncDataByLogout(isDeleteData: Boolean): LogoutDeleteDataResult {
        return LogoutDeleteDataResult.newSuccessResult()
    }

    private class OnReqAccountCallbackImpl(private val callback: ICloudAccountCallback) : AccountNameTask.onReqAccountCallback<SignInAccount?> {

        override fun onReqStart() {
            AppLogger.BASIC.d(TAG, "onReqStart")
        }

        override fun onReqLoading() {
            AppLogger.BASIC.d(TAG, "onReqLoading")
        }

        override fun onReqFinish(signInAccount: SignInAccount?) {
            AppLogger.BASIC.d(TAG, "onReqFinish: $signInAccount")
            val account = CloudAccount()
            account.token = signInAccount!!.token
            account.isLogin = signInAccount.isLogin
            account.resultCode = signInAccount.resultCode
            account.resultMsg = signInAccount.resultMsg
            val userInfo = signInAccount.userInfo
            if (userInfo != null) {
                account.userId = userInfo.ssoid
                account.username = userInfo.userName
                account.avatar = userInfo.avatarUrl
                account.status = userInfo.status
            }
            callback.onComplete(account)
        }
    }

}