/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudBackupResponseRecordProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import androidx.annotation.NonNull;

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord;

import java.util.ArrayList;
import java.util.List;

public class CloudBackupResponseRecordProxy {

    public static List<CloudBackupResponseRecordProxy> toProxyList(List<CloudBackupResponseRecord> oriList) {
        ArrayList<CloudBackupResponseRecordProxy> list = new ArrayList<>(oriList.size());
        for (CloudBackupResponseRecord item: oriList){
            list.add(new CloudBackupResponseRecordProxy(item));
        }
        return list;
    }

    private CloudBackupResponseRecord mRecord;

    public CloudBackupResponseRecordProxy(){
        mRecord = new CloudBackupResponseRecord();
    }

    CloudBackupResponseRecordProxy(@NonNull CloudBackupResponseRecord record) {
        mRecord = record;
    }

    public String getSysRecordId() {
        return mRecord.getSysRecordId();
    }

    public void setSysRecordId(String sysRecordId) {
        mRecord.setSysRecordId(sysRecordId);
    }

    public long getSysVersion() {
        return mRecord.getSysVersion();
    }

    public void setSysVersion(long sysVersion) {
        mRecord.setSysVersion(sysVersion);
    }

    public String getOperatorType() {
        return mRecord.getOperatorType();
    }

    public void setOperatorType(String operatorType) {
        mRecord.setOperatorType(operatorType);
    }

    public long getSysCreateTime() {
        return mRecord.getSysCreateTime();
    }

    public void setSysCreateTime(long sysCreateTime) {
        mRecord.setSysCreateTime(sysCreateTime);
    }

    public long getSysUpdateTime() {
        return mRecord.getSysUpdateTime();
    }

    public void setSysUpdateTime(long sysUpdateTime) {
        mRecord.setSysUpdateTime(sysUpdateTime);
    }

    @Override
    public String toString() {
        return mRecord.toString();
    }
}
