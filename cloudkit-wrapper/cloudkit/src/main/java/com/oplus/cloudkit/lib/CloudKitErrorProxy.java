/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudKitErrorProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import androidx.annotation.NonNull;

import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError;
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError;

/**未全部代理，需要时自行添加即可*/
public class CloudKitErrorProxy {

    private CloudKitError mCloudKitError;

    private CloudKitErrorProxy() {
    }

    public CloudKitErrorProxy(@NonNull CloudKitError cloudKitError) {
        mCloudKitError = cloudKitError;
    }

    public CloudKitError getCloudKitError() {
        return mCloudKitError;
    }

    public boolean isSuccess(){
        return mCloudKitError.isSuccess();
    }

    public int getSubServerErrorCode() {
        return mCloudKitError.getSubServerErrorCode();
    }

    @Override
    public String toString() {
        return mCloudKitError.toString();
    }
}
