/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudKitSdkWrapper.kt
** Description:
** Version: 1.0
** Date : 2022/8/12
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/12      1.0     create file
****************************************************************/
package com.oplus.cloudkit

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.heytap.cloud.sdk.utils.Constants
import com.heytap.cloud.sdk.utils.SyncManager
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libpay.upgrade.CloudUpgradeAgent
import com.heytap.cloudkit.libpay.upgrade.CloudUpgradeDialogBuilder
import com.heytap.cloudkit.libpay.upgrade.http.request.CloudGetUpgradeActivityRequest
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.io.CloudIOFileListener
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.oplus.cloudkit.lib.CloudIOFileProxy
import com.oplus.cloudkit.lib.CloudKitErrorProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

interface CloudIOFileListenerProxy {
    fun onFinish(file: CloudIOFileProxy?, cloudKitError: CloudKitErrorProxy?)
}

object CloudKitSdkManager {

    val CLOSE_CODE = SwitchState.CLOSE.state
    val OPEN_ALL_CODE = SwitchState.OPEN_ALL.state
    val OPEN_ONLY_WIFI_CODE = SwitchState.OPEN_ONLY_WIFI.state

    @JvmStatic
    fun showUpdateCloudSpaceDialog(mActivity: AppCompatActivity, module: String, isPaySuccessLiveData: MutableLiveData<Boolean>?) {
        mActivity.lifecycleScope.launch(Dispatchers.IO) {
            val request = CloudGetUpgradeActivityRequest(0, 0, false)
            request.module = module
            var switchStatus = false
            val result = CloudSyncManager.getInstance().syncSwitchCompat
            if (result.cloudKitError.isSuccess) {
                switchStatus = result.switchState > SwitchState.CLOSE.state
            }
            request.isSwitchStatus = switchStatus
            val response = CloudUpgradeAgent().getUpgradeInfo(request)
            withContext(Dispatchers.Main) {
                if (mActivity.isFinishing) {
                    return@withContext
                }
                val fragment = CloudUpgradeDialogBuilder(module)
                    .setThemeColorAttr(com.support.appcompat.R.attr.couiColorPrimary)
                    .setCallback { success -> isPaySuccessLiveData?.value = success }
                    .build(response)
                fragment.show(mActivity.supportFragmentManager, "CloudUpgradeDialogBuilder")
            }
        }
    }

    @JvmStatic
    fun getAccountUserId(): String {
        return CloudAccountManager.getInstance().signInAccount.userId
    }

    @JvmStatic
    fun transferFile(oFile: CloudIOFileProxy, listener: CloudIOFileListenerProxy?) {
        CloudSyncManager.getInstance().transferFile(oFile.cloudIOFile, object : SimpleCloudIOFileListener() {
            override fun onFinish(file: CloudIOFile?, cloudDateType: CloudDataType?, cloudKitError: CloudKitError?) {
                listener?.onFinish(
                    if (file == null) null else CloudIOFileProxy(file),
                    if (cloudKitError == null) null else CloudKitErrorProxy(cloudKitError)
                )
            }
        })
    }

    @JvmStatic
    fun registerPush(registerID: String?) {
        CloudSyncManager.getInstance().registerPush(registerID)
    }

    @JvmStatic
    fun sendSyncDataChangeMsg(context: Context) {
        SyncManager.getInstance().sendSyncDataChangeMsg(context, Constants.Module.NOTE, true)
    }

    private abstract class SimpleCloudIOFileListener : CloudIOFileListener {
        override fun onProgress(file: CloudIOFile?, cloudDateType: CloudDataType?, progress: Double) {

        }
    }

    @JvmStatic
    fun clearRichNoteAnchor(
        module: String,
        zone: String
    ) {
        CoroutineScope(Dispatchers.Default).launch {
            CloudSyncManager.getInstance().clearSysVersion(module, zone)
        }
    }

    @JvmStatic
    fun setEnableCtaRequest(enable: Boolean) {
        CloudSyncManager.getInstance().setEnableCtaRequest(enable)
    }
}