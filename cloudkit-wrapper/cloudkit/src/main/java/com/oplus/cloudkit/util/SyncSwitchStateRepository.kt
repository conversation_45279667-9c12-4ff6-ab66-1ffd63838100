package com.oplus.cloudkit.util

import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.view.KeyEvent
import androidx.annotation.WorkerThread
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.heytap.cloud.sdk.base.CloudSdkConstants
import com.heytap.cloud.sdk.base.CloudStatusHelper
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.heytap.cloudkit.libsync.cloudswitch.bean.CloudSyncSwitchObserver
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.cloudswitch.compat.CloudKitSwitchCompatUtil
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.oplus.cloudkit.BaseSyncManager
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.JudgeGlobalStateCallback
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

interface GetSyncSwitchListener {
    fun onGetSyncSwitch(switchStateCode: Int)
}

object SyncSwitchStateRepository {

    private const val TAG = "SyncSwitchStateRepository"
    private const val SHOW_OPENING_DIALOG_DELAY = 300L

    private var isInitiate = false
    private var isSupportSwitch: Boolean? = null
    private var switchStateLiveDataNew = MutableLiveData<Int>()
    private var isFetingCloudState = false
    var isLoginAuthing = false

    interface ChangeSyncSwitchResultListener {
        fun changeSyncSwitchResult(isSuccess: Boolean = false, errorMessage: String?)
        fun noSupportCloudKitSwitch()
    }

    fun init(context: Context) {
        if (!isInitiate) {
            isInitiate = true

            // 1.设置旧同步开关查询逻辑
            CloudKitSwitchCompatUtil.setCloudOldSwitchCompat {
                AppLogger.BASIC.d(TAG, "query switch from compat")
                CloudStatusHelper.queryFromServer(it, CloudSdkConstants.Module.NOTE, CloudStatusHelper.Key.SYNC_SWITCH)
            }

            // 2.监听新同步开关
            val handlerThread = HandlerThread("SyncSwitchStateRepository")
            handlerThread.start()
            val handler = Handler(handlerThread.looper)
            val switchContentObserver = object : CloudSyncSwitchObserver(handler) {
                override fun onSyncSwitchChange(switchState: SwitchState, isChangeBySdk: Boolean) {
                    AppLogger.BASIC.d(TAG, "switch change: $switchState, $isChangeBySdk")
                    onSwitchStateChanged(switchState)
                }
            }

            GlobalScope.launch(Dispatchers.IO) {
                if (checkIsSupportCloudkitSwitch(context) && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 注册新开关监听
                    CloudSyncManager.getInstance().registerSyncSwitchObserver(context.applicationContext, switchContentObserver)
                } else {
                    val cloudStatusObserver = object : CloudStatusHelper.CloudStatusObserver(handler) {
                        override fun onChange(path: String?) {
                            AppLogger.BASIC.d(TAG, "old switch change: $path")
                            if (path == CloudStatusHelper.NotifyPath.NOTE_SYNC) {
                                onSwitchStateChanged(null)
                            }
                        }
                    }
                    CloudStatusHelper.registerCloudOldSwitchChange(context.applicationContext, listOf(CloudStatusHelper.NotifyPath.NOTE_SYNC), cloudStatusObserver)
                }
            }
        }
    }

    private fun onSwitchStateChanged(switchState: SwitchState?) {
        GlobalScope.launch(Dispatchers.IO) {
            val realState = switchState ?: suspendQuerySwitchState()
            if (realState == null) {
                AppLogger.BASIC.e(TAG, "onSwitchStateChanged error: the switch is null")
                return@launch
            }

            AppLogger.BASIC.d(TAG, "onSwitchStateChanged: $realState")
            notifySwitchStateChanged(realState)

            if (realState.state == SwitchState.CLOSE.state) {
                BaseSyncManager.resetRunningState()
                BaseSyncManager.clearSyncDataByClose()
            }
        }
    }

    private fun notifySwitchStateChanged(switchState: SwitchState) {
        switchStateLiveDataNew.postValue(switchState.state)
    }

    /**
     * Check if switch is supported. The switch info is stored in AppSetting in android R and higher.
     *
     */
    fun checkIsSupportCloudkitSwitch(context: Context): Boolean {
        if (isSupportSwitch == null) {
            isSupportSwitch = CloudKitSwitchCompatUtil.isSupportSwitch(context.applicationContext).isSuccess
        }
        return isSupportSwitch!!
    }

    /**
     * 检查云同步服务
     */
    @WorkerThread
    fun checkService(): Boolean {
        if (!CloudSyncManager.getInstance().isServiceAvailable) {
            return CloudSyncManager.getInstance().startCloudSyncService()
        }
        return true
    }

    fun querySwitchState(context: Context): LiveData<Int> {
        init(context.applicationContext)
        triggerCurrentSwitchStateQueryIfNeeded()
        return switchStateLiveDataNew
    }

    fun querySwitchState(context: Context, listener: GetSyncSwitchListener?) {
        init(context.applicationContext)
        if (switchStateLiveDataNew.value != null) {
            listener?.onGetSyncSwitch(switchStateLiveDataNew.value!!)
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            val switchState = suspendQuerySwitchState()
            notifySwitchStateChanged(switchState)
            launch(Dispatchers.Main) {
                listener?.onGetSyncSwitch(switchState.state)
            }
        }
    }

    private fun triggerCurrentSwitchStateQueryIfNeeded() {
        if (switchStateLiveDataNew.value == null) {
            GlobalScope.launch(Dispatchers.IO) {
                val switchState = suspendQuerySwitchState()
                notifySwitchStateChanged(switchState)
            }
        }
    }

    @WorkerThread
    private fun suspendQuerySwitchState(): SwitchState {
        var state: SwitchState? = null
        if (CloudAccountManager.getInstance().isLogin) {
            val result = CloudSyncManager.getInstance().syncSwitchCompat
            AppLogger.BASIC.d(TAG, "suspendQuerySwitchState:$result")
            if (result.cloudKitError.isSuccess) {
                state = SwitchState.getSwitchState(result.switchState)
            }
        } else {
            AppLogger.BASIC.d(TAG, "suspendQuerySwitchState not login")
        }
        if (state == null) {
            return SwitchState.CLOSE
        }
        return state
    }

    @WorkerThread
    fun isCloudClose(context: Context): Boolean {
        return suspendQuerySwitchState().state == SwitchState.CLOSE.state
    }

    /**
     * 调起开关请求，cloudkit支持开关调用就启动开关对话框，否则跳转到云服务设置界面，用户自行开启
     */
    fun showOpenSyncSwitchDialog(
        context: FragmentActivity,
        needNewSDK: Boolean,
        callback: ChangeSyncSwitchResultListener?,
        finishActivity: Boolean = false
    ) {
        isLoginAuthing = true
        CKAccountUtil.checkAccountIsLogin(context as AppCompatActivity, needNewSDK, AuthAccountResultCallbackImpl(context, finishActivity, callback))
    }

    private class AuthAccountResultCallbackImpl(
        activity: FragmentActivity,
        val finishActivity: Boolean,
        val callback: ChangeSyncSwitchResultListener?
    ) : CKAccountUtil.AuthAccountResultCallback {

        private val weakRef = WeakReference(activity)
        override fun onResult(isLogin: Boolean) {
            isLoginAuthing = false
            AppLogger.BASIC.d(TAG, "showOpenSyncSwitchDialog checkAccountIsLogin isLogin=$isLogin isFetingCloudState=$isFetingCloudState")
            if (isLogin && !isFetingCloudState) {
                val context = weakRef.get() ?: return
                if (CloudKitGlobalStateManager.fetchGlobalStateSucceed()) {
                    //已成功获取过，则不再重新获取
                    openSwitchWhenJudgeOCloudStateSuccess(context, callback)
                    return
                }
                // 这里调用全球一体化接口，强卡页面
                var openingDialog: AlertDialog? = null
                var job: Job? = null
                CloudKitGlobalStateManager.judgeOCloudState(object : JudgeGlobalStateCallback {
                    override fun onStart() {
                        isFetingCloudState = true
                        job = context.lifecycleScope.launch(Dispatchers.IO) {
                            delay(SHOW_OPENING_DIALOG_DELAY)
                            withContext(Dispatchers.Main) {
                                openingDialog = showOpeningProcessDialog(context)
                            }
                        }
                    }

                    override fun onSuccess(globalEnable: Boolean, cloudEnable: Boolean) {
                        isFetingCloudState = false
                        job?.cancel()
                        context.lifecycleScope.launch(Dispatchers.Main) {
                            openingDialog?.dismiss()
                        }
                        openSwitchWhenJudgeOCloudStateSuccess(context, callback)
                    }

                    override fun onError(errorType: Int) {
                        isFetingCloudState = false
                        job?.cancel()
                        context.lifecycleScope.launch(Dispatchers.Main) {
                            openingDialog?.dismiss()
                            showGlobalStateErrorDialog(context, errorType, finishActivity)
                        }
                    }
                })
            }
        }
    }

    private fun openSwitchWhenJudgeOCloudStateSuccess(context: FragmentActivity, callback: ChangeSyncSwitchResultListener?) {
        val isSupport = checkIsSupportCloudkitSwitch(context)
        if (isSupport) {
            openSyncSwitchOnlyWIFI(context, object : ChangeSyncSwitchResultListener {
                override fun changeSyncSwitchResult(isSuccess: Boolean, errorMessage: String?) {
                    callback?.changeSyncSwitchResult(isSuccess, errorMessage)
                }

                override fun noSupportCloudKitSwitch() {
                    callback?.noSupportCloudKitSwitch()
                }
            })
        } else {
            callback?.noSupportCloudKitSwitch()
        }
    }

    private fun showOpeningProcessDialog(context: FragmentActivity): AlertDialog {
        return COUIRotatingDialogBuilder(context, context.getString(com.oplus.note.baseres.R.string.text_opening)).show().apply {
            setCanceledOnTouchOutside(false)
            setOnKeyListener { dialog, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    return@setOnKeyListener true
                }
                return@setOnKeyListener false
            }
        }
    }

    fun showGlobalStateErrorDialog(context: FragmentActivity, errorType: Int, finishActivity: Boolean = false) {
        var cloudDisabled = false
        val title = when (errorType) {
            CloudKitGlobalStateManager.TYPE_ACCOUNT_DISABLED -> {
                cloudDisabled = true
                com.oplus.note.baseres.R.string.cloudkit_global_account_disable
            }
            CloudKitGlobalStateManager.TYPE_DEVICE_DISABLED -> {
                cloudDisabled = true
                com.oplus.note.baseres.R.string.cloudkit_global_device_disable
            }
            CloudKitGlobalStateManager.TYPE_NET_ERROR -> com.oplus.note.baseres.R.string.cloudkit_net_error_tip
            else -> com.oplus.note.baseres.R.string.cloud_service_busy_try_again_later
        }
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom).apply {
            setTitle(title)
            setNegativeButton(com.oplus.note.baseres.R.string.got_it) { _, _ ->
                if (cloudDisabled && finishActivity) {
                    context.finish()
                }
            }
        }.show().apply {
            setCanceledOnTouchOutside(false)
            setOnKeyListener { dialog, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    return@setOnKeyListener true
                }
                return@setOnKeyListener false
            }
        }
    }

    /**
     * 在cloudkit支持开关调用的前提下，不启动对话框提示，直接开启默认WLAN自动云同步
     */
    fun openSyncSwitchOnlyWIFI(context: FragmentActivity?, changeSyncSwitchResultListener: ChangeSyncSwitchResultListener?) {
        context?.lifecycleScope?.launch(Dispatchers.IO) {
            if (CloudKitSwitchCompatUtil.isSupportSwitch(context.applicationContext).isSuccess) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    val error = CloudSyncManager.getInstance()
                        .setSyncSwitch(SwitchState.getSwitchState(SwitchState.OPEN_ONLY_WIFI.state))
                    launch(Dispatchers.Main) {
                        changeSyncSwitchResultListener?.changeSyncSwitchResult(
                            error.isSuccess,
                            error.errorMsg
                        )
                    }
                }
            }
        }
    }

    /**
     * 在cloudkit支持开关调用的前提下，不启动对话框提示，直接开启WLAN和移动流量的自动云同步
     */
    fun openSynSwitchALL(context: FragmentActivity?, changeSyncSwitchResultListener: ChangeSyncSwitchResultListener?) {
        context?.lifecycleScope?.launch(Dispatchers.IO) {
            if (CloudKitSwitchCompatUtil.isSupportSwitch(context.applicationContext).isSuccess) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    val error = CloudSyncManager.getInstance()
                        .setSyncSwitch(SwitchState.getSwitchState(SwitchState.OPEN_ALL.state))
                    launch(Dispatchers.Main) {
                        changeSyncSwitchResultListener?.changeSyncSwitchResult(
                            error.isSuccess,
                            error.errorMsg
                        )
                    }
                }
            }
        }
    }

    /**
     * 在cloudkit支持开关调用的前提下，关闭自动云同步
     */
    @JvmStatic
    fun closeSyncSwitch(context: Context, owner: LifecycleOwner, callback: ChangeSyncSwitchResultListener?) {
        owner.lifecycleScope.launch {
            val error = withContext(Dispatchers.IO) {
                val isSupportSwitch = CloudKitSwitchCompatUtil.isSupportSwitch(context.applicationContext).isSuccess
                if (isSupportSwitch && Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    return@withContext CloudSyncManager.getInstance().setSyncSwitch(SwitchState.getSwitchState(SwitchState.CLOSE.state))
                } else {
                    return@withContext null
                }
            }

            if (error != null) {
                callback?.changeSyncSwitchResult(error.isSuccess, error.errorMsg)
            }
        }
    }
}