/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudIOFileProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: yanglin<PERSON>@oppo.com
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import androidx.annotation.NonNull;

import com.heytap.cloudkit.libsync.service.CloudIOFile;

/**未全部代理，需要时自行添加即可*/
public class CloudIOFileProxy {

    private CloudIOFile mCloudIOFile;

    private CloudIOFileProxy() {
    }

    public CloudIOFileProxy(@NonNull CloudIOFile cloudIOFile) {
        mCloudIOFile = cloudIOFile;
    }

    public static CloudIOFileProxy createDownloadFile(String recordId, String module, String zone, String md5, String cloudId, String filePath) {
        return new CloudIOFileProxy(CloudIOFile.createDownloadFile(recordId, module, zone, md5, cloudId, filePath));
    }

    public static CloudIOFileProxy createUploadFile(String module, String zone, String fileUri, String md5) {
        return new CloudIOFileProxy(CloudIOFile.createUploadFile(module, zone, fileUri, md5));
    }

    public String getFilePath() {
        return mCloudIOFile.getFilePath();
    }

    public void setFilePath(String filePath) {
        mCloudIOFile.setFilePath(filePath);
    }

    public CloudIOFile getCloudIOFile() {
        return mCloudIOFile;
    }

    public String getMd5() {
        return mCloudIOFile.getMd5();
    }

    public void setMd5(String md5) {
        mCloudIOFile.setMd5(md5);
    }

    public String getCloudId() {
        return mCloudIOFile.getCloudId();
    }

    public void setCloudId(String cloudId) {
        mCloudIOFile.setCloudId(cloudId);
    }

    public String getCheckPayload() {
        return mCloudIOFile.getCheckPayload();
    }

    public void setCheckPayload(String checkPayload) {
        mCloudIOFile.setCheckPayload(checkPayload);
    }

    public String getCacheUri() {
        return mCloudIOFile.getCacheUri();
    }

    public void setCacheUri(String cacheUri) {
        mCloudIOFile.setCacheUri(cacheUri);
    }

}
