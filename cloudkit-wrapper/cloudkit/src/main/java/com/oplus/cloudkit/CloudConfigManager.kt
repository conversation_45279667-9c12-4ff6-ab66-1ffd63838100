/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudConfigManager.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/7/29
 ** Author: W9028045
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9028045       2024/7/129     1.0     create file
 ****************************************************************/
package com.oplus.cloudkit

import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudOperationManager
import com.heytap.cloudkit.libsync.ext.CloudOperationManager.ICloudOperationsCallback
import com.oplus.cloud.CloudOperationResponseData
import com.oplus.note.logger.AppLogger

object CloudConfigManager {
    interface CloudConfigResult {
        fun success(data: CloudOperationResponseData)
        fun onFail()
    }

    private const val TAG = "CloudConfigManager"

    @JvmStatic
    fun getOperationData(
        mode: String,
        requestPath: String,
        requestData: String,
        cloudConfigResult: CloudConfigResult? = null
    ) {
        CloudKitSdkManager.setEnableCtaRequest(true)
        AppLogger.BASIC.d(TAG, "getOperationData requestData=$requestData")
        CloudOperationManager.getInstance().getOperationData(mode, requestPath, requestData,
            object : ICloudOperationsCallback {
                override fun onSuccess(p0: String?) {
                    AppLogger.BASIC.d(TAG, "onSuccess:$p0")
                    var success = false
                    p0?.let { result ->
                        if (result.isNotEmpty()) {
                            kotlin.runCatching {
                                cloudConfigResult?.success(
                                    Gson().fromJson(
                                        result,
                                        CloudOperationResponseData::class.java
                                    )
                                )
                                success = true
                            }.onFailure {
                                AppLogger.BASIC.d(TAG, "onSuccess but error ${it.message}")
                            }
                        }
                    }
                    if (!success) {
                        cloudConfigResult?.onFail()
                    }
                }

                override fun onError(p0: String?, p1: CloudKitError?) {
                    AppLogger.BASIC.d(TAG, "onError:$p1,$p1")
                    cloudConfigResult?.onFail()
                }
            })
    }
}