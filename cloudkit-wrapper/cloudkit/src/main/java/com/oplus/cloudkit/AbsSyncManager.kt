/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: AbsSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/13
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import androidx.annotation.WorkerThread
import com.heytap.cloudkit.libcommon.netrequest.CloudHttpStatusCode
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.ext.ICloudBackupMetaData
import com.heytap.cloudkit.libsync.ext.ICloudRecoveryMetaData
import com.heytap.cloudkit.libsync.metadata.helper.CloudBackupRequestSource
import com.heytap.cloudkit.libsync.metadata.helper.CloudRecoveryRequestSource
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseError
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.oplus.note.logger.AppLogger
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudDataTypeProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import kotlin.math.min

abstract class AbsSyncManager(private val module: String, private val zone: String, private val recordTypeVersion: Int) {

    companion object {
        private const val BACKUP_PAGE_SIZE = 500
        const val ERROR_CODE_SYS_VERSION_ISNULL = 1104
        /**多端同时编辑造成的 need fetch 错误*/
        const val ERROR_CODE_1202_NEED_FETCH = 1202
        const val ERROR_CODE_1200_EXIST = 1200
        private const val GET_META_DATA_PAGE_SIZE = 100
        const val CLOUD_COLD_STORAGE = 0
        const val CLOUD_STEAM_LIMIT = 1
        const val LOCAL_SPACE_NOT_ENOUGH = 2
    }

    private val tag by lazy {
        "$zone+$recordTypeVersion"
    }

    @Volatile
    var isRunning = false

    @WorkerThread
    fun startSync() {
        if (isRunning) {
            return
        }

        isRunning = true

        onStartRecovery()
        doRecovery()
    }

    abstract fun onStartRecovery()

    private fun doRecovery() {
        if (!isRunning) { // interrupted
            finishSync(CloudKitError.ERROR_RECOVERY_STOPPED_MANUAL)
            return
        }

        kotlin.runCatching {
            onPagingRecoveryStart()
        }.onFailure {
            AppLogger.CLOUDKIT.e(tag, "Error when onPagingRecoveryStart.", it)
        }

        CloudSyncManager.getInstance().startRecoveryMetaData(module, zone, CloudRecoveryRequestSource.MANUAL, recordTypeVersion, object : ICloudRecoveryMetaData {
            override fun onSuccess(cloudDataType: CloudDataType?, data: MutableList<CloudMetaDataRecord>?,
                                   hasMoreData: Boolean, totalCount: Long, remainCount: Long) {
                AppLogger.CLOUDKIT.d(tag, "doRecovery onSuccess hasMoreData:$hasMoreData, totalCount:$totalCount, remainCount:$remainCount dataSize:${data?.size}")

                val mergeSucceed = kotlin.runCatching {
                    onPagingRecoveryEnd(CloudMetaDataRecordProxy.toProxyList(data))
                    true
                }.onFailure {
                    AppLogger.CLOUDKIT.e(tag, "Error when onPagingRecoveryEnd.", it)
                }.getOrDefault(false)

                CloudSyncManager.getInstance().completeRecoveryMetaData(module, zone, mergeSucceed, cloudDataType, getMetaDataCount().toLong())

                if (hasMoreData) {
                    doRecovery()
                } else {
                    onRecoveryEnd { doBackup() }
                }
            }

            override fun onError(cloudDataType: CloudDataType?, error: CloudKitError?) {
                onRecoverError { doBackup() }
                // finish with recovery error
                finishSync(error)
            }
        })
    }

    /**
     * This will be called multiple times if it is a paging recovery
     */
    abstract fun onPagingRecoveryStart()

    /**
     * This will be called multiple times if it is a paging recovery
     */
    abstract fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?)

    /**
     * count the meta data size.
     * 计算规则：拉取完成数据后本地所有数据条数-本地新增的数据条数（还未上云）+ 本地删除的数据条数（还未上云）
     */
    abstract fun getMetaDataCount(): Int

    /**
     * recover完成，进backup前调用
     * @param backUp 开始backup
     */
    abstract fun onRecoveryEnd(backUp: () -> Unit)

    /**
     * recover 异常，部分 SyncManager 需要执行 onRecoveryEnd 操作保证同步流程执行完毕
     * */
    protected open fun onRecoverError(backUp: () -> Unit) {
    }

    /**
     * fetch数据完成后，某些SyncManager需要特殊处理
     */
    open fun afterFetchData() {
        onRecoveryEnd {  }
    }

    private fun doBackup() {
        kotlin.runCatching {
            onStartBackup()
        }.onFailure {
            AppLogger.CLOUDKIT.e(tag, "Error when onStartBackup.", it)
        }

        val dirty = kotlin.runCatching {
            onQueryDirtyData()
        }.onFailure {
            AppLogger.CLOUDKIT.e(tag, "Error when onQueryDirtyData.", it)
        }.getOrDefault(emptyList())

        if (dirty.isNotEmpty()) {
            doBackupImpl(CloudMetaDataRecordProxy.toList(dirty), 0)
        } else {
            // finish with normal
            finishSync()
        }
    }

    abstract fun onStartBackup()

    abstract fun onQueryDirtyData(): List<CloudMetaDataRecordProxy>

    protected open fun getBackUpPageSize(): Int {
        return BACKUP_PAGE_SIZE
    }

    private fun doBackupImpl(dirty: List<CloudMetaDataRecord>, page: Int) {
        if (!isRunning) { // interrupted
            finishSync(CloudKitError.ERROR_BACKUP_STOPPED_MANUAL)
            return
        }
        val pageSize = getBackUpPageSize()

        val pageData = kotlin.runCatching {
            val pageData = dirty.subList(pageSize * page, min(pageSize * (page + 1), dirty.size))
            onPagingBackupStart(CloudMetaDataRecordProxy.toProxyList(pageData))
            return@runCatching pageData
        }.onFailure {
            AppLogger.CLOUDKIT.e(tag, "Error when onPagingBackupStart.", it)
        }.getOrNull()

        CloudSyncManager.getInstance().startBackupMetaData(module, zone, CloudBackupRequestSource.MANUAL, recordTypeVersion, pageData, object : ICloudBackupMetaData {
            override fun onSuccess(cloudDataType: CloudDataType?, successData: MutableList<CloudBackupResponseRecord>?,
                                   errorData: MutableList<CloudBackupResponseError>?) {
                AppLogger.CLOUDKIT.d(tag, "doBackup onSuccess: ${successData?.size} , ${errorData?.size}")

                errorData?.forEach {
                    AppLogger.CLOUDKIT.e(tag, "error record ${it.sysRecordId} ${it.cloudKitError}")
                }
                val successProxyList = CloudBackupResponseRecordProxy.toProxyList(successData)
                val errorProxyList = CloudBackupResponseErrorProxy.toProxyList(errorData)

                kotlin.runCatching {
                    onPagingBackupEnd(successProxyList, errorProxyList)
                }.onFailure {
                    AppLogger.CLOUDKIT.e(tag, "Error when onPagingBackupEnd.", it)
                }

                onDealBackupErrorData(cloudDataType, errorData)

                val hasMore = pageSize * (page + 1) < dirty.size
                if (hasMore) {
                    doBackupImpl(dirty, page + 1)
                } else {
                    // single backup record error, error code: 1200, error msg: EXISTS
                    val shouldClearSysVersion = errorData?.find {
                        it.cloudKitError.subServerErrorCode == 1200
                    } != null
                    if (shouldClearSysVersion) {
                        CloudSyncManager.getInstance().clearSysVersion(module, zone, cloudDataType)
                    }

                    finishSync(
                        getBackUpFinishErrorResult(
                            CloudDataTypeProxy(cloudDataType),
                            successProxyList,
                            errorProxyList
                        )?.cloudBackupResponseError?.cloudKitError
                    )
                }
            }

            override fun onError(cloudDataType: CloudDataType?, error: CloudKitError?) {
                // finish with backup error
                finishSync(error)
            }
        })
    }

    /**
     * 数据全部备份后，处理成功或失败结果
     * @return null 表示同步成功， 不为 null 则返回具体报错信息
     * */
    protected open fun getBackUpFinishErrorResult(
        cloudDataTypeProxy: CloudDataTypeProxy,
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ): CloudBackupResponseErrorProxy? {
        // 过滤 1104的报错  error code: 1104, error msg: SYS_VERSION_ISNULL
        val filterErrorData = errorData?.filter { it.subServerErrorCode != ERROR_CODE_SYS_VERSION_ISNULL }
        val realSuccess = (successData?.size != 0 || errorData?.size != 0) && filterErrorData.isNullOrEmpty()
        val error = if (filterErrorData.isNullOrEmpty()) null else filterErrorData[0]
        return if (realSuccess || error == null) null else error
    }

    /**
     * 备份数据出现 1202 错误时，使用 getMetaDataList 查询云端数据，和本地数据做一次合并操作
     * 1104 1200 等操作其实也可以使用这种方式处理（本次暂不处理）
     * */
    private fun onDealBackupErrorData(
        cloudDataType: CloudDataType?,
        errorData: MutableList<CloudBackupResponseError>?
    ) {
        val filterErrorData = errorData?.filter {
            it.cloudKitError.subServerErrorCode == ERROR_CODE_1202_NEED_FETCH
        }
        filterErrorData?.apply {
            if (isNotEmpty()) {
                fetchItems(cloudDataType, this, 0)
            }
        }
    }

    /**分页操作，获取云端指定 recordId 的数据，和本地数据做一次合并操作*/
    private fun fetchItems(cloudDataType: CloudDataType?, errorList: List<CloudBackupResponseError>, page: Int) {
        val recordIdsPageData = kotlin.runCatching {
            errorList.subList(GET_META_DATA_PAGE_SIZE * page, min(GET_META_DATA_PAGE_SIZE * (page + 1), errorList.size))
                .map { it.sysRecordId }
        }.onFailure {
            AppLogger.CLOUDKIT.e(tag, "fetchItems paging data error: ", it)
        }.getOrNull()

        CloudSyncManager.getInstance().getMetaDataList(module, zone, cloudDataType, recordIdsPageData) { result ->
            AppLogger.CLOUDKIT.d(tag, "fetchItems success size ${result?.cloudMetaDataRecords?.size} error ${result.cloudKitError}")
            if (!result?.cloudMetaDataRecords.isNullOrEmpty()) {
                kotlin.runCatching {
                    onPagingRecoveryEnd(CloudMetaDataRecordProxy.toProxyList(result?.cloudMetaDataRecords))
                }.onFailure {
                    AppLogger.CLOUDKIT.e(tag, "featItems onPagingRecoveryEnd error ${it.message}")
                }
            }
            val hasMore = GET_META_DATA_PAGE_SIZE * (page + 1) < errorList.size
            if (hasMore) {
                fetchItems(cloudDataType, errorList, page + 1)
            } else {
                afterFetchData()
            }
        }
    }

    protected fun clearClearSystemVersion(cloudDataTypeProxy: CloudDataTypeProxy) {
        CloudSyncManager.getInstance().clearSysVersion(module, zone, cloudDataTypeProxy.cloudDataType)
    }

    /**
     * This will be called multiple times if it is a paging backup
     *
     * @param data The data to be backed up on the current page
     */
    abstract fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?)

    /**
     * This will be called multiple times if it is a paging backup
     *
     * @param successData The data of the current page is backed up successfully.
     * @param errorData The data of the current page is backed up failed.
     */
    abstract fun onPagingBackupEnd(successData: List<CloudBackupResponseRecordProxy>?, errorData: List<CloudBackupResponseErrorProxy>?)

    /**
     * 错误码
     */
    private fun finishSync(error: CloudKitError? = null) {
        AppLogger.CLOUDKIT.d(tag, "finishSync: $error")

        isRunning = false

        // finish with give code.
        if (error == null) {
            syncListener?.onSyncFinish(true, false, false)
        } else if (error.subServerErrorCode == CloudHttpStatusCode.BizFocusServerCode.HTTP_NO_CLOUD_SPACE) {
            syncListener?.onSyncFinish(false, true, false)
        } else if (error.subServerErrorCode == CloudHttpStatusCode.BizFocusServerCode.HTTP_REQUEST_TOO_FREQUENT) {
            //云端限流
            syncListener?.onSyncFinishWithBizError(CLOUD_STEAM_LIMIT)
        } else if (error.subServerErrorCode == CloudHttpStatusCode.BizFocusServerCode.HTTP_DATA_COLD_STANDBY) {
            /*用户数据被迁移或者归档变成冷存储**/
            syncListener?.onSyncFinishWithBizError(CLOUD_COLD_STORAGE)
        } else if (error.bizErrorCode == CloudBizError.NO_LOCAL_SPACE.code) {
            /*本地空间不足**/
            syncListener?.onSyncFinishWithBizError(LOCAL_SPACE_NOT_ENOUGH)
        } else if (error.bizErrorCode == CloudBizError.MANUAL_STOP.code) {
            syncListener?.onSyncFinish(false, false, true)
        } else {
            syncListener?.onSyncFinish(false, false, false)
        }
    }

    interface SyncFinishListener{
        fun onSyncFinish(isSuccess: Boolean, isSpaceNotEnough: Boolean, isInterrupted: Boolean)
        fun onSyncFinishWithBizError(code: Int)
    }

    private var syncListener: SyncFinishListener? = null

    fun setSyncFinishListener(listener: SyncFinishListener) {
        syncListener = listener
    }
}