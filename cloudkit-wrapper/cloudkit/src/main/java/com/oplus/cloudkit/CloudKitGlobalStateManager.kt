/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudKitGlobalStateManager.kt
 ** Description: 处理 云同步全球一体化相关逻辑
 ** Version: 1.0
 ** Date : 2025/3/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2025/3/18      1.0     create file
 ****************************************************************/
package com.oplus.cloudkit

import androidx.lifecycle.LifecycleCoroutineScope
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.heytap.cloudkit.libcommon.netrequest.ICloudResponseCallback
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.service.FunctionScopeRsp
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

interface JudgeGlobalStateCallback {
    fun onStart()
    fun onSuccess(globalEnable: Boolean, cloudEnable: Boolean)
    fun onError(errorType: Int)
}

object CloudKitGlobalStateManager {

    private const val TAG = "CloudKitGlobalStateManager"

    const val TYPE_ACCOUNT_DISABLED = 0
    const val TYPE_DEVICE_DISABLED = 1
    const val TYPE_SERVER_ERROR = 2
    const val TYPE_NET_ERROR = 3

    const val ERROR_CODE_ACCOUNT_DISABLED = 100007
    const val ERROR_CODE_DEVICE_DISABLED = 100008
    const val ERROR_CODE_SERVER_ERROR_MIN = 502
    const val ERROR_CODE_SERVER_ERROR_MAX = 504
    const val ERROR_CODE_NEW_WORK_ERROR = 2

    private var functionScopeRsp: FunctionScopeRsp? = null
    private var cloudKitError: CloudKitError? = null

    /** 是否成功获取到 ck 接口下发的全球一体化信息，冷启动后重新获取 */
    @JvmStatic
    fun fetchGlobalStateSucceed(): Boolean {
        return functionScopeRsp != null
    }

    /** 全球一体化是否启用 */
    @JvmStatic
    fun globalEnable(): Boolean {
        return functionScopeRsp?.isGlobalEnabled ?: false
    }

    /** 云同步功能是否可以正常使用 */
    fun cloudEnable(): Boolean {
        return fetchGlobalStateSucceed() && (!globalEnable() || (functionScopeRsp?.ocloudRegionState == FunctionScopeRsp.ENABLE))
    }

    @JvmStatic
    fun deviceOrAccountDisable(): Boolean {
        return functionScopeRsp?.ocloudRegionState == FunctionScopeRsp.DEVICE_DISABLED
                || functionScopeRsp?.ocloudRegionState == FunctionScopeRsp.ACCOUNT_DISABLED
                || cloudKitError?.subServerErrorCode == ERROR_CODE_DEVICE_DISABLED
                || cloudKitError?.subServerErrorCode == ERROR_CODE_ACCOUNT_DISABLED
    }

    @JvmStatic
    fun resetState() {
        functionScopeRsp = null
    }

    @JvmStatic
    fun judgeOCloudState(callback: JudgeGlobalStateCallback? = null) {
        AppLogger.BASIC.d(TAG, "judgeOCloudState fetchGlobalStateSucceed=${fetchGlobalStateSucceed()}")
        if (fetchGlobalStateSucceed()) {
            return
        }
        callback?.onStart()
        CloudSyncManager.getInstance().judgeOCloudState(object : ICloudResponseCallback<FunctionScopeRsp> {
            override fun onSuccess(result: FunctionScopeRsp) {
                functionScopeRsp = result
                AppLogger.CLOUDKIT.d(TAG, "judgeOCloudState result=$result")
                if (result.globalEnabled) {
                    when (result.ocloudRegionState) {
                        FunctionScopeRsp.ENABLE -> {
                            //显示国内完整云同步功能
                            callback?.onSuccess(globalEnable = true, cloudEnable = true)
                        }
                        FunctionScopeRsp.ACCOUNT_DISABLED -> {
                            //该账号不允许使用云服务
                            callback?.onError(TYPE_ACCOUNT_DISABLED)
                        }
                        FunctionScopeRsp.DEVICE_DISABLED -> {
                            //该设备不允许使用云服务
                            callback?.onError(TYPE_DEVICE_DISABLED)
                        }
                        else -> callback?.onError(TYPE_SERVER_ERROR)
                    }
                } else {
                    //全球一体化功能未开启，默认走以前的逻辑，根据设备打点地显示对应的功能
                    callback?.onSuccess(globalEnable = false, cloudEnable = true)
                }
            }

            override fun onError(error: CloudKitError) {
                cloudKitError = error
                AppLogger.CLOUDKIT.e(TAG, "judgeOCloudState error=$error")
                //根据CloudKitError的信息提示用户，后续的同步服务可能会受到影响
                when {
                    error.subServerErrorCode in ERROR_CODE_SERVER_ERROR_MIN..ERROR_CODE_SERVER_ERROR_MAX -> {
                        //服务异常
                        callback?.onError(TYPE_SERVER_ERROR)
                    }
                    error.bizErrorCode == ERROR_CODE_NEW_WORK_ERROR -> {
                        //客户端网络异常
                        callback?.onError(TYPE_NET_ERROR)
                    }
                    error.subServerErrorCode == ERROR_CODE_ACCOUNT_DISABLED -> {
                        //该账号不允许使用云服务
                        callback?.onError(TYPE_ACCOUNT_DISABLED)
                    }
                    error.subServerErrorCode == ERROR_CODE_DEVICE_DISABLED -> {
                        //该设备不允许使用云服务
                        callback?.onError(TYPE_DEVICE_DISABLED)
                    }
                    else -> callback?.onError(TYPE_SERVER_ERROR)
                }
            }
        })
    }

    @JvmStatic
    fun judgeOCloudStateCheckLogin(scope: LifecycleCoroutineScope, callback: JudgeGlobalStateCallback? = null) {
        if (fetchGlobalStateSucceed()) {
            return
        }
        scope.launch(Dispatchers.IO) {
            val isLogin = CloudAccountManager.getInstance().isLogin
            AppLogger.BASIC.d(TAG, "judgeOCloudStateCheckLogin isLogin=$isLogin")
            if (!isLogin) {
                return@launch
            }
            judgeOCloudState(callback)
        }
    }
}