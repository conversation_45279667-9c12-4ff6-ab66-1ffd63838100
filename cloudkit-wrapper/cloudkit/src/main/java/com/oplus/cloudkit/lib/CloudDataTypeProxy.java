/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudDataTypeProxy.java
** Description:
** Version: 1.0
** Date : 2023/8/8
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/8/8      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import com.heytap.cloudkit.libsync.service.CloudDataType;

public class CloudDataTypeProxy {

    private CloudDataType mCloudDataType;

    public CloudDataTypeProxy(CloudDataType cloudDataType) {
        mCloudDataType = cloudDataType;
    }

    public CloudDataType getCloudDataType() {
        return mCloudDataType;
    }
}
