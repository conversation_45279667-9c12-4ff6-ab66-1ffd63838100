/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudMetaDataRecordProxy.java
** Description:
** Version: 1.0
** Date : 2022/8/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/15      1.0     create file
****************************************************************/
package com.oplus.cloudkit.lib;

import androidx.annotation.NonNull;

import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataFileInfo;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;

import java.util.ArrayList;
import java.util.List;

public class CloudMetaDataRecordProxy {

    public static List<CloudMetaDataRecordProxy> toProxyList(List<CloudMetaDataRecord> oriList) {
        ArrayList<CloudMetaDataRecordProxy> list = new ArrayList<>(oriList.size());
        for (CloudMetaDataRecord item: oriList){
            list.add(new CloudMetaDataRecordProxy(item));
        }
        return list;
    }

    public static List<CloudMetaDataRecord> toList(List<CloudMetaDataRecordProxy> oriList) {
        ArrayList<CloudMetaDataRecord> list = new ArrayList<>(oriList.size());
        for (CloudMetaDataRecordProxy item: oriList){
            list.add(item.getRecordItem());
        }
        return list;
    }

    private CloudMetaDataRecord mRecord;

    public CloudMetaDataRecordProxy() {
        mRecord = new CloudMetaDataRecord();
    }

    CloudMetaDataRecordProxy(@NonNull CloudMetaDataRecord record) {
        mRecord = record;
    }

    public CloudMetaDataRecord getRecordItem() {
        return mRecord;
    }

    public String getSysRecordId() {
        return mRecord.getSysRecordId();
    }

    public void setSysRecordId(String sysRecordId) {
        mRecord.setSysRecordId(sysRecordId);
    }

    public String getSysUniqueId() {
        return mRecord.getSysUniqueId();
    }

    public void setSysUniqueId(String sysUniqueId) {
        mRecord.setSysUniqueId(sysUniqueId);
    }

    public long getSysVersion() {
        return mRecord.getSysVersion();
    }

    public void setSysVersion(long sysVersion) {
        mRecord.setSysVersion(sysVersion);
    }

    public long getSysProtocolVersion() {
        return mRecord.getSysProtocolVersion();
    }

    public void setSysProtocolVersion(long sysProtocolVersion) {
        mRecord.setSysProtocolVersion(sysProtocolVersion);
    }

    public String getSysRecordType() {
        return mRecord.getSysRecordType();
    }

    public void setSysRecordType(String sysRecordType) {
       mRecord.setSysRecordType(sysRecordType);
    }

    public int getSysDataType() {
        return mRecord.getSysDataType();
    }

    public void setSysDataType(int sysDataType) {
        mRecord.setSysDataType(sysDataType);
    }

    public String getOperatorType() {
        return mRecord.getOperatorType();
    }

    public void setOperatorType(String operatorType) {
        mRecord.setOperatorType(operatorType);
    }

    public int getSysStatus() {
        return mRecord.getSysStatus();
    }

    public void setSysStatus(int sysStatus) {
        mRecord.setSysStatus(sysStatus);
    }

    public long getSysCreateTime() {
        return mRecord.getSysCreateTime();
    }

    public void setSysCreateTime(long sysCreateTime) {
        mRecord.setSysCreateTime(sysCreateTime);
    }

    public long getSysUpdateTime() {
        return mRecord.getSysUpdateTime();
    }

    public void setSysUpdateTime(long sysUpdateTime) {
        mRecord.setSysUpdateTime(sysUpdateTime);
    }

    public String getFields() {
        return mRecord.getFields();
    }

    public void setFields(String fields) {
        mRecord.setFields(fields);
    }

    public List<CloudMetaDataFileInfo> getFileInfos() {
        return mRecord.getFileInfos();
    }

    public void setFileInfos(List<CloudMetaDataFileInfoProxy> fileInfos) {
        mRecord.setFileInfos(CloudMetaDataFileInfoProxy.toList(fileInfos));
    }

    @Override
    public String toString() {
        return mRecord.toString();
    }
}
