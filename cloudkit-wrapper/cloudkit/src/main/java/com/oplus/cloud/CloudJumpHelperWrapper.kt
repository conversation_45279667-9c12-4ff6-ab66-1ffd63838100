/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudJumpHelperWrapper.kt
** Description:
** Version: 1.0
** Date : 2022/8/16
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/16      1.0     create file
****************************************************************/
package com.oplus.cloud

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.annotation.VisibleForTesting
import com.heytap.cloud.sdk.base.CloudJumpHelper
import com.heytap.cloud.sdk.base.CloudSdkConstants
import com.oplus.note.logger.AppLogger

object CloudJumpHelperWrapper {
    private const val URI_CLOUD_QUICK_APP = "hap://app/com.heytap.cloudservice/page/note"
    private const val TAG = "CloudJumpHelperWrapper"

    @JvmStatic
    fun jumpMain(context: Context, module: String): Boolean {
        return CloudJumpHelper.jumpMain(context, module)
    }

    @JvmStatic
    @VisibleForTesting
    fun jumpMainNoteDetail(context: Context, module: String): Boolean {
        kotlin.runCatching {
            val uri = Uri.parse(URI_CLOUD_QUICK_APP)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            context.startActivity(intent)
            return true
        }.onFailure {
            AppLogger.BASIC.e(TAG, "jumpMainNoteDetail fail $it")
            return jumpMain(context, module)
        }
        return false
    }

    @JvmStatic
    fun jumpModuleSetting(context: Context) {
        CloudJumpHelper.jumpModuleSetting(context, CloudSdkConstants.Module.NOTE)
    }

    @JvmStatic
    fun jumpCloudNote(
        isExport: Boolean,
        context: Context,
        module: String
    ) {
        if (isExport) {
            AppLogger.CLOUDKIT.d(TAG, "jump cloud note on export")
            jumpMain(context, module)
        } else {
            jumpMainNoteDetail(context, module)
        }
    }
}