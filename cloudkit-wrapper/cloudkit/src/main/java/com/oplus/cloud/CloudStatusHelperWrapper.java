/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudStatusHelperWrapper.java
** Description:
** Version: 1.0
** Date : 2022/8/16
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/8/16      1.0     create file
****************************************************************/
package com.oplus.cloud;

import android.content.Context;
import android.os.Handler;

import com.heytap.cloud.sdk.base.CloudStatusHelper;

import java.util.Map;

public class CloudStatusHelperWrapper {
    public static final String LOGIN = CloudStatusHelper.Key.LOGIN;
    public static final String SYNC_SWITCH = CloudStatusHelper.Key.SYNC_SWITCH;
    public static final String NOTIFY_PATH_SPACE = CloudStatusHelper.NotifyPath.SPACE;
    public static final String NOTIFY_PATH_LOGIN = CloudStatusHelper.NotifyPath.LOGIN;

    public abstract static class CloudStatusObserverWrapper{

        CloudStatusHelper.CloudStatusObserver realObserver;

        public CloudStatusObserverWrapper(Handler handler) {
            realObserver = new CloudStatusHelper.CloudStatusObserver(handler) {
                @Override
                public void onChange(String path) {
                    onPathChange(path);
                }
            };
        }

        public abstract void onPathChange(String path);
    }

    public static boolean registerCloudStatusChange(Context context, String module, CloudStatusObserverWrapper observer) {
        return CloudStatusHelper.registerCloudStatusChange(context, module, observer.realObserver);
    }

    public static void unRegisterCloudStatusChange(Context context, CloudStatusObserverWrapper observer) {
        CloudStatusHelper.unRegisterCloudStatusChange(context, observer.realObserver);
    }


    public static Map<String, Integer> queryAll(Context context, String module) {
        return CloudStatusHelper.queryAll(context, module);
    }
}
