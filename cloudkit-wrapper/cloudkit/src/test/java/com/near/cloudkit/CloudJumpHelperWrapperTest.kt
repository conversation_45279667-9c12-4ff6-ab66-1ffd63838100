/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - CloudJumpHelperWrapperTest
 * Description:
 * v1.0:   Create CloudJumpHelperWrapperTest file
 *
 * Version: 1.0
 * Date: 2024/06/27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                 <date>       <version>      <desc>
 * ------------------------------------------------------------------------------
 * Ji<PERSON><PERSON>.<EMAIL>       2024/6/27   1.0      Create this module
 *********************************************************************************/
package com.near.cloudkit

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.heytap.cloud.sdk.base.CloudJumpHelper
import com.oplus.cloud.CloudJumpHelperWrapper
import com.oplus.note.test.callOriginal
import com.oplus.note.test.relaxMockk
import com.oplus.note.test.verifyNot
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkConstructor
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Test

class CloudJumpHelperWrapperTest {

    @Test
    fun `should call CloudJumpHelper when jumpMain`(): Unit = mockkObject(CloudJumpHelperWrapper) {
        // Given
        mockkStatic(CloudJumpHelperWrapper::jumpMain)
        callOriginal { CloudJumpHelperWrapper.jumpMain(any(), any()) }
        mockkStatic(CloudJumpHelper::jumpMain)
        every {
            CloudJumpHelper.jumpMain(any(), any())
        } returns true
        val context = relaxMockk<Context>()
        // When
        val result = CloudJumpHelperWrapper.jumpMain(context, "")
        // Then
        assert(result)
        verify {
            CloudJumpHelper.jumpMain(any(), any())
        }
        unmockkStatic(CloudJumpHelperWrapper::jumpMain)
        unmockkStatic(CloudJumpHelper::jumpMain)
    }

    @Test
    fun `should call jumpModuleSetting when jumpModuleSetting`(): Unit =
        mockkObject(CloudJumpHelperWrapper) {
            // Given
            mockkStatic(CloudJumpHelperWrapper::jumpModuleSetting)
            callOriginal { CloudJumpHelperWrapper.jumpModuleSetting(any()) }
            mockkStatic(CloudJumpHelper::jumpModuleSetting)
            every {
                CloudJumpHelper.jumpModuleSetting(any(), any())
            } returns true
            val context = relaxMockk<Context>()
            // When
            val result = CloudJumpHelperWrapper.jumpModuleSetting(context)
            // Then
            verify {
                CloudJumpHelper.jumpModuleSetting(any(), any())
            }
            unmockkStatic(CloudJumpHelper::jumpModuleSetting)
            unmockkStatic(CloudJumpHelper::jumpModuleSetting)
        }

    @Test
    fun `should jumpMainNoteDetail when jumpCloudNote on domestic`() {
        // Given
        mockkStatic(CloudJumpHelperWrapper::jumpCloudNote)
        mockkStatic(CloudJumpHelperWrapper::jumpMain)
        mockkStatic(CloudJumpHelperWrapper::jumpMainNoteDetail)
        every { CloudJumpHelperWrapper.jumpMain(any(), any()) } returns true
        every { CloudJumpHelperWrapper.jumpMainNoteDetail(any(), any()) } returns true
        callOriginal { CloudJumpHelperWrapper.jumpCloudNote(any(), any(), any()) }
        // When
        CloudJumpHelperWrapper.jumpCloudNote(false, relaxMockk(), "module")
        // Then
        verifyNot {
            CloudJumpHelperWrapper.jumpMain(any(), any())
        }
        verify {
            CloudJumpHelperWrapper.jumpMainNoteDetail(any(), any())
        }
        unmockkStatic(CloudJumpHelperWrapper::jumpCloudNote)
        unmockkStatic(CloudJumpHelperWrapper::jumpMain)
        unmockkStatic(CloudJumpHelperWrapper::jumpMainNoteDetail)
    }

    @Test
    fun `should use Intent when jumpMainNoteDetail`() {
        // Given
        mockkStatic(CloudJumpHelperWrapper::jumpMainNoteDetail)
        callOriginal { CloudJumpHelperWrapper.jumpMainNoteDetail(any(), any()) }
        val context = relaxMockk<Context> {
            justRun { startActivity(any()) }
        }
        mockkStatic(Uri::parse)
        every { Uri.parse(any()) } returns relaxMockk()
        mockkConstructor(Intent::class)
        every { anyConstructed<Intent>().addFlags(any()) } returns relaxMockk()
        // When
        CloudJumpHelperWrapper.jumpMainNoteDetail(context, "note")
        // Then
        verify {
            Uri.parse(any())
            anyConstructed<Intent>().addFlags(any())
            context.startActivity(any())
        }
        unmockkStatic(CloudJumpHelperWrapper::jumpMainNoteDetail)
        unmockkConstructor(Intent::class)
    }
}