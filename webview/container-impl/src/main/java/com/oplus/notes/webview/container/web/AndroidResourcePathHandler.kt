/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : AndroidResourcePathHandler.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/5/10
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/5/10        1.0           create
 */
package com.oplus.notes.webview.container.web

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.webkit.WebResourceResponse
import androidx.webkit.WebViewAssetLoader.PathHandler
import com.oplus.note.logger.AppLogger

class AndroidResourcePathHandler(val context: Context) : PathHandler {
    companion object {
        private const val TAG = "AndroidResourcePathHandler"
        private const val SKIN_TIME = "skinTime="

        //WebViewAssetLoader$PathMatcher 要求用来匹配的Path 必须以“/”作为开头和结尾，
        @JvmStatic
        val ANDROID_RESOURCE_PREFIX = "${ContentResolver.SCHEME_ANDROID_RESOURCE}://"
        const val HANDLE_PATH_PREFIX = "/${ContentResolver.SCHEME_ANDROID_RESOURCE}://"
    }

    override fun handle(path: String): WebResourceResponse {
        var realPath = path
        // webview加载个性化皮肤时会添加skinTime的字符串，是为了防止webview因为加载皮肤的资源名时因为名称没有变化而使用缓存
        if (path.contains(SKIN_TIME)) {
            realPath = path.replace(Regex("$SKIN_TIME.*"), "")
        }
        val uri = Uri.parse("$ANDROID_RESOURCE_PREFIX$realPath")
        kotlin.runCatching {
            val afd = context.contentResolver.openAssetFileDescriptor(uri, "r", null)
            if (afd != null) {
                return WebResourceResponse(null, null, afd.createInputStream())
            } else {
                AppLogger.BASIC.w(TAG, "open asset file fail!")
            }
        }.onFailure {
            AppLogger.BASIC.w(TAG, "hand resource fail:$it")
        }
        return WebResourceResponse(null, null, null)
    }
}