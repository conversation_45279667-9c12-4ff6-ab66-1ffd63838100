/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WebViewContainerImpl.kt
 * Description    : WebViewContainerImpl.kt
 * Version        : 1.0
 * Date           : 2023/6/5
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/5         1.0           create
 */
package com.oplus.notes.webview.container.impl

import android.content.Context
import android.content.MutableContextWrapper
import android.view.ContextThemeWrapper
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StyleRes
import com.heytap.tbl.webkit.TBLSdk
import com.heytap.tbl.webkit.WebView
import com.nearme.note.util.Injector
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.CardAttr
import com.oplus.note.view.WVScrollbarView
import com.oplus.notes.webview.cache.api.IWebViewProxyCache
import com.oplus.notes.webview.container.WVSearchData
import com.oplus.notes.webview.container.api.CallJSResponse
import com.oplus.notes.webview.container.api.ContactCardAttr
import com.oplus.notes.webview.container.api.FileCardData
import com.oplus.notes.webview.container.api.IMyJavascriptInterface
import com.oplus.notes.webview.container.api.IScrollChangedListener
import com.oplus.notes.webview.container.api.IWebViewContainer
import com.oplus.notes.webview.container.api.ImageInfo
import com.oplus.notes.webview.container.api.InnerCallJSResponse
import com.oplus.notes.webview.container.api.InputContent
import com.oplus.notes.webview.container.api.PaintInfo
import com.oplus.notes.webview.container.api.RecordAttr
import com.oplus.notes.webview.container.api.ScheduleCardAttr
import com.oplus.notes.webview.container.api.SummaryStreamTipParams
import com.oplus.notes.webview.container.api.VideoData
import com.oplus.notes.webview.container.api.WVActionModeWrapper
import com.oplus.notes.webview.container.api.touchevent.IWVDispatchTouchEventListener
import com.oplus.notes.webview.container.api.touchevent.IWVInterceptStylusTouchEventListener
import com.oplus.notes.webview.container.api.touchevent.IWVTouchEventListener
import com.oplus.notes.webview.container.web.WebViewProxy
import com.oplus.notes.webview.data.BasicCssParams
import com.oplus.notes.webview.data.CaptureElementInfo
import com.oplus.notes.webview.data.SkinCssParams
import com.oplus.notes.webview.data.TiptapContentInfo
import com.oplus.notes.webview.data.clipboard.PasteResult
import org.json.JSONObject

class WebViewContainerImpl : IWebViewContainer {
    companion object {
        private const val TAG = "WebViewContainerImpl"
    }
    private var webViewProxy: WebViewProxy? = null
    private val webViewProxyCache = if (IWebViewProxyCache.ENABLE_CACHE) {
        Injector.injectFactory<IWebViewProxyCache>()
    } else {
        null
    }
    override fun webViewSupported(): Boolean {
        return true
    }

    override fun isUsingTBLWebView(): Boolean {
        return webViewProxy?.webView?.isUsingTBLWebView ?: false
    }

    override fun createWebView(
        context: Context,
        @StyleRes themeResId: Int,
        container: ViewGroup,
        layoutParams: ViewGroup.LayoutParams?,
        backGroundView: View?
    ): WebView? {
        webViewProxy = if (IWebViewProxyCache.ENABLE_CACHE) {
            webViewProxyCache?.acquireWebViewProxy(context, themeResId) as? WebViewProxy
        } else {
            val contextWrapper = MutableContextWrapper(ContextThemeWrapper(context, themeResId))
            WebViewProxy.with(contextWrapper).load()
        }
        val webViewCacheWidth = webViewProxy?.webView?.measuredWidth ?: 0
        AppLogger.BASIC.d(TAG, "createWebView: webViewCacheWidth=$webViewCacheWidth, proxy=$webViewProxy")
        if (webViewCacheWidth != 0) {
            layoutParams?.width = webViewCacheWidth
            backGroundView?.layoutParams?.width = webViewCacheWidth
        }
        container.addView(webViewProxy?.webView, 0, layoutParams)
        AppLogger.BASIC.d(TAG, "createWebView: isUsingTBLWebView=${webViewProxy?.webView?.isUsingTBLWebView}")
        AppLogger.BASIC.d(TAG, "createWebView: TBLSdk.getCoreVersion()=${runCatching { TBLSdk.getCoreVersion() }.getOrNull()}")
        return webViewProxy?.webView
    }

    override fun getCursorPosition(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getCursorPosition(cb)
    }
    override fun setCursorPosition(position: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setCursorPosition(position, cb)
    }

    override fun loadUrl(url: String, javascriptInterface: IMyJavascriptInterface?) {
        checkProxy()
        webViewProxy?.initJsInterface("injectedObject", javascriptInterface)
        webViewProxy?.loadUrl(url)
    }

    override fun setJsInterfaceConsumer(consumer: IMyJavascriptInterface?) {
        checkProxy()
        webViewProxy?.setJsInterfaceConsumer(consumer)
    }

    override fun addScrollChangedListener(listener: IScrollChangedListener?) {
        checkProxy()
        webViewProxy?.addScrollChangedListener(listener)
    }

    override fun removeScrollChangedListener(listener: IScrollChangedListener?) {
        checkProxy()
        webViewProxy?.removeScrollChangedListener(listener)
    }

    override fun requestFocus() {
        checkProxy()
        webViewProxy?.requestFocus()
    }

    override fun refreshScrollbarColor(mode: Int) {
        checkProxy()
        webViewProxy?.refreshScrollbarColor(mode)
    }

    override fun setScrollbarView(scrollbarView: WVScrollbarView?) {
        checkProxy()
        webViewProxy?.setScrollbarView(scrollbarView)
    }

    override fun setPaintScrollScale(scale: Float) {
        checkProxy()
        webViewProxy?.setPaintScrollScale(scale)
    }

    override fun hasJavascriptMethod(name: String, cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.hasJavascriptMethod(name, cb)
    }

    override fun getTextAndHtml(cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.getTextAndHtml(cb)
    }

    override fun getSelectText(cb: CallJSResponse) {
        checkProxy()
        webViewProxy?.getSelectText(cb)
    }

    override fun initContent(contentInfo: TiptapContentInfo, icb: InnerCallJSResponse?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.initContent(contentInfo, false, icb, cb)
    }

    override fun updateContent(contentInfo: TiptapContentInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.initContent(contentInfo, true, null, cb)
    }

    override fun setText(text: InputContent, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setText(text, cb)
    }

    override fun replaceAllContent(text: String, isFinal: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.replaceAllContent(text, isFinal, cb)
    }

    override fun clearContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearContent(cb)
    }

    override fun clearFocus(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearFocus(cb)
    }

    override fun focus(position: String, focusedEditor: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.focus(position, focusedEditor, cb)
    }

    override fun setBold(bold: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBold(bold, cb)
    }

    override fun setItalic(italic: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setItalic(italic, cb)
    }

    override fun setUnderline(lineType: String, colorType: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setUnderline(lineType, colorType, cb)
    }

    override fun setStrikethrough(strikethrough: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setStrikethrough(strikethrough, cb)
    }

    override fun setFontSize(size: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setFontSize(size, cb)
    }

    override fun setBulletList(bullet: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBulletList(bullet, cb)
    }

    override fun setBulletListHX(bullet: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBulletListHX(bullet, cb)
    }

    override fun setOrderedList(ordered: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setOrderedList(ordered, cb)
    }

    override fun setTaskList(task: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTaskList(task, cb)
    }

    override fun setTextAlign(align: String, cb: CallJSResponse?, history: Boolean) {
        checkProxy()
        webViewProxy?.setTextAlign(align, cb, history)
    }

    override fun insertImage(
        imageInfo: ImageInfo,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        afterAttachId: String?,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertImage(imageInfo, addToPrevGroup, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, afterAttachId, cb)
    }

    override fun notifyImageSrcExist(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.notifyImageSrcExist(attachId, cb)
    }

    // 新增带滚动控制的方法
    override fun updateImage(oldAttachId: String?, imageInfo: ImageInfo, shouldScroll: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateImage(oldAttachId, imageInfo, shouldScroll, cb)
    }
    override fun updateCard(cardAttr: CardAttr, oldAttachId: String?, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateCard(cardAttr, oldAttachId, cb)
    }

    override fun replaceNodeByCard(oldAttachId: String, cardAttr: CardAttr, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceNodeByCard(oldAttachId, cardAttr, cb)
    }

    override fun replaceNodeByImage(oldAttachId: String?, imageInfo: ImageInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceNodeByImage(oldAttachId, imageInfo, cb)
    }

    override fun replaceAttachmentByText(attachId: String, text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.replaceAttachmentByText(attachId, text, cb)
    }

    @Suppress("LongParameterList")
    override fun replaceNodeByPaint(
        oldAttachId: String?,
        paintId: String,
        paintSrc: String,
        imageId: String,
        imageSrc: String,
        imageWidth: Int,
        imageHeight: Int,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByPaint(
            oldAttachId = oldAttachId,
            paintInfo = PaintInfo(
                attachId = imageId,
                paintId = paintId,
                imagePath = imageSrc,
                dataPath = paintSrc,
                imageWidth = imageWidth.toFloat(),
                imageHeight = imageHeight.toFloat()
            ),
            cb = cb
        )
    }

    override fun replaceNodeByDocument(
        oldAttachId: String,
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByDocument(oldAttachId, fileCard, addToPrevGroup, cb)
    }

    override fun replaceNodeByVideo(
        oldAttachId: String,
        videoData: VideoData,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByVideo(oldAttachId, videoData, addToPrevGroup, cb)
    }

    override fun replaceNodeByAudio(
        oldAttachId: String,
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.replaceNodeByAudio(oldAttachId, recordAttr, addToPrevGroup, cb)
    }

    override fun focusCoordinate(x: Int, y: Int, textNodeRestricted: Boolean) {
        checkProxy()
        webViewProxy?.focusCoordinate(x, y, textNodeRestricted)
    }

    override fun updateContentHeight(newHeight: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateContentHeight(newHeight, cb)
    }

    override fun onDestroy() {
        checkProxy()
        webViewProxy?.let {
            it.onDestroy()
            if (IWebViewProxyCache.ENABLE_CACHE) {
                webViewProxyCache?.recycleWebViewProxy(it)
            }
            it.setJsInterfaceConsumer(null)
        }
        webViewProxy = null
    }

    override fun scrollIntoView(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.scrollIntoView(cb)
    }

    override fun getNodeRectByCoords(left: Int, top: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getNodeRectByCoords(left, top, cb)
    }

    override fun getImageRectBySrc(src: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getImageRectBySrc(src, cb)
    }

    override fun getRecordDetailRect(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getRecordDetailRect(attachId, cb)
    }

    override fun getRecordCardRect(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getRecordCardRect(attachId, cb)
    }

    override fun scrollBy(interval: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.scrollBy(interval, cb)
    }

    override fun undo(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.undo(cb)
    }

    override fun redo(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.redo(cb)
    }

    override fun setBackgroundColor(colorType: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBackgroundColor(colorType, cb)
    }

    override fun unsetBackgroundColor(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.unsetBackgroundColor(cb)
    }

    override fun setDensityScale(scale: Float, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setDensityScale(scale, cb)
    }

    override fun onTipTapMounted() {
        checkProxy()
        webViewProxy?.onTipTapMounted()
    }

    override fun setTextSelection(fromPos: Int, toPos: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextSelection(fromPos, toPos, cb)
    }

    override fun setTextSelectionAll(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextSelectionAll(cb)
    }

    override fun cancelTextSelectionAll(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.cancelTextSelectionAll(cb)
    }

    override fun setUiMode(mode: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setUiMode(mode, cb)
    }

    override fun updateRecordState(attachId: String, state: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecordState(attachId, state, cb)
    }

    override fun updateRecordHasCallLogs(attachId: String, hasCallLogs: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecordHasCallLogs(attachId, hasCallLogs, cb)
    }

    override fun updateRecord(oldAttachId: String, src: String, attachId: String, recordId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateRecord(oldAttachId, src, attachId, recordId, cb)
    }

    override fun setRecordCurrentTime(attachId: String, time: Long, totalDuration: Long, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setRecordCurrentTime(attachId, time, totalDuration, cb)
    }

    override fun search(searchData: WVSearchData, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.search(searchData, cb)
    }

    override fun matchPrevious(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.matchPrevious(cb)
    }

    override fun matchNext(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.matchNext(cb)
    }

    override fun clearSearchResult(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.clearSearchResult(cb)
    }

    override fun smoothScrollTo(dx: Int, dy: Int, duration: Int) {
        checkProxy()
        webViewProxy?.smoothScrollTo(0, 0, dx, dy, duration)
    }

    override fun smoothScrollStartTo(startX: Int, startY: Int, dx: Int, dy: Int, duration: Int) {
        checkProxy()
        webViewProxy?.smoothScrollTo(startX, startY, dx, dy, duration)
    }

    override fun deleteNodeByAttachId(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.deleteNodeByAttachId(attachId, cb)
    }

    override fun insertCard(
        cardJson: CardAttr,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertCard(cardJson, addToPrevGroup, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, cb)
    }

    override fun insertContactCard(cardJson: ContactCardAttr, addToPrevGroup: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertContactCard(cardJson, addToPrevGroup, cb)
    }

    override fun insertScheduleCard(cardJson: ScheduleCardAttr, addToPrevGroup: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertScheduleCard(cardJson, addToPrevGroup, cb)
    }

    override fun insertRecordCard(
        recordAttr: RecordAttr,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertRecordCard(recordAttr, addToPrevGroup, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, cb)
    }

    override fun insertFileCard(
        fileCard: FileCardData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertFileCard(fileCard, addToPrevGroup, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, cb)
    }

    override fun setSummaryEntity(entities: String, marks: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setSummaryEntity(entities, marks, cb)
    }

    override fun setEditorScale(scale: Float, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setEditorScale(scale, cb)
    }

    override fun getEditorScale(): Float {
        checkProxy()
        return webViewProxy?.getEditorScale() ?: 1.0F
    }

    private fun checkProxy() {
        if (webViewProxy == null) {
            AppLogger.BASIC.i(TAG, "checkProxy: webViewProxy is null, this:$this")
        }
    }

    override fun insertHintText(text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertHintText(text, cb)
    }

    override fun deleteHintText(cb: CallJSResponse?) {
        insertHintText("")
    }

    override fun insertPhoneHintText(text: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.insertPhoneHintText(text, cb)
    }

    override fun onGlobalDragEnter(x: Int, y: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragEnter(x, y, cb)
    }

    override fun onGlobalDragEnd(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragEnd(cb)
    }

    override fun loadImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.loadImageTipsLottieAnimation(attachId, cb)
    }

    override fun destroyImageTipsLottieAnimation(attachId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.destroyImageTipsLottieAnimation(attachId, cb)
    }

    override fun setInterceptTouchEventListener(listener: IWVTouchEventListener?) {
        checkProxy()
        webViewProxy?.setInterceptTouchEventListener(listener)
    }

    override fun setDispatchTouchEventListener(listener: IWVDispatchTouchEventListener?) {
        checkProxy()
        webViewProxy?.setDispatchTouchEventListener(listener)
    }

    override fun setInterceptStylusTouchEventListener(listener: IWVInterceptStylusTouchEventListener?) {
        checkProxy()
        webViewProxy?.setInterceptStylusTouchEventListener(listener)
    }

    override fun setActionModeCallbackCreator(creator: () -> WVActionModeWrapper?) {
        checkProxy()
        webViewProxy?.setActionModeCallbackCreator(creator)
    }

    override fun removeActionModeCallbackCreator() {
        checkProxy()
        webViewProxy?.removeActionModeCallbackCreator()
    }

    override fun clearActionMode(needFinish: Boolean) {
        checkProxy()
        webViewProxy?.clearActionMode(needFinish)
    }

    override fun isViewModeStylusTouch(): Boolean {
        checkProxy()
        return webViewProxy?.isViewModeStylusTouch() == true
    }

    override fun getAttachments(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getAttachments(cb)
    }

    override fun getEditorsHeight(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getEditorsHeight(cb)
    }

    override fun setBasicCssParams(basicCssParams: BasicCssParams, priority: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBasicCssParams(basicCssParams, priority, cb)
    }

    override fun setSkinCssParams(skinCssParams: SkinCssParams, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setSkinCssParams(skinCssParams, cb)
    }

    override fun attachTipTapComponent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.attachTipTapComponent(cb)
    }

    override fun detachTipTapComponent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.detachTipTapComponent(cb)
    }

    override fun insertSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.insertSummaryStreamTip(paras, cb)
    }
    override fun updateSummaryStreamTip(paras: SummaryStreamTipParams, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateSummaryStreamTip(paras, cb)
    }
    override fun deleteSummaryStreamTip(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.deleteSummaryStreamTip(cb)
    }

    override fun captureElement(captureElementInfos: List<CaptureElementInfo>, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.captureElement(captureElementInfos, cb)
    }

    override fun setOnRenderProcessGoneCb(cb: (() -> Unit)?) {
        checkProxy()
        webViewProxy?.setOnRenderProcessGoneCb(cb)
    }
    override fun interceptWebEditorClick(isIntercept: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.interceptWebEditorClick(isIntercept, cb)
    }
    override fun disableRecord(attachId: String, disable: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.disableRecord(attachId, disable, cb)
    }

    override fun performVibrate() {
        checkProxy()
        webViewProxy?.performVibrate()
    }

    override fun enableImageAnimation(enable: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.enableImageAnimation(enable, cb)
    }

    override fun moveAttachToSelection(type: String, attachId: String, originPos: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.moveAttachToSelection(type, attachId, originPos, cb)
    }

    override fun getSelectedText(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getSelectedText(editor, cb)
    }

    override fun selectRangeTextAIGC(from: Int, to: Int, editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectRangeTextAIGC(from, to, editor, cb)
    }

    override fun selectAndGetAllText(isAiKit: Boolean, editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectAndGetAllText(isAiKit, editor, cb)
    }

    override fun getAllHTML(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getAllHTML(editor, cb)
    }
    override fun selectAndGetForwardAllText(editor: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.selectAndGetForwardAllText(editor, cb)
    }

    /**
     * 通知WebView  用于 处理 zikit 弹窗 和上屏框 会重叠问题
     */
    override fun onAIKitUpperScreenScroll(kitMargin: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIKitUpperScreenScroll(kitMargin, cb)
    }
    override fun onAIKitUpdateCacheInfo(jsonString: String, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIKitUpdateCacheInfo(jsonString, cb)
    }

    override fun onAIGCRewriteStart(snackBarMargin: Int, aigcOption: String, isExport: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteStart(snackBarMargin, aigcOption, isExport, cb)
    }

    override fun onAIGCRewriteFinish(manualStop: Boolean, margin: Int, isAiKit: Boolean, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteFinish(manualStop, margin, isAiKit, cb)
    }

    override fun onAIGCRewriteResult(
        content: String,
        isFinish: Boolean,
        aiHint: String,
        prePageText: String,
        nextPageText: String,
        copyText: String,
        retryText: String,
        isAiKit: Boolean,
        isCacheData: Boolean,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.onAIGCRewriteResult(content, isFinish, aiHint, prePageText, nextPageText, copyText, retryText, isAiKit, isCacheData, cb)
    }

    override fun onAIGCRewriteDelete(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteDelete(cb)
    }

    override fun onAIGCRewriteInsert(subStringLength: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteInsert(subStringLength, cb)
    }

    override fun onAIGCRewriteReplace(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCRewriteReplace(cb)
    }

    override fun onAiKitRewriteInsert(html: String, subStringLength: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAiKitRewriteInsert(html, subStringLength, cb)
    }

    override fun onAiKitRewriteReplace(html: String, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAiKitRewriteReplace(html, cb)
    }

    override fun onAIGCTableInsert(tableHtml: String, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onAIGCTableInsert(tableHtml, cb)
    }

    override fun scrollToBottom(scrollToBottom: Boolean, marginBottom: Int, marginExtra: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.scrollToBottom(scrollToBottom, marginBottom, marginExtra, cb)
    }

    override fun setTitleStyle(titleStyle: Boolean, type: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTitleStyle(titleStyle, type, cb)
    }

    override fun setContentStyle(contentStyle: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setContentStyle(contentStyle, cb)
    }

    override fun setTextColorType(colorType: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextColorType(colorType, cb)
    }

    override fun setTableColorType(tableColorPicker: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTableColorType(tableColorPicker, cb)
    }

    override fun setAidTextStyle(aidTextStyle: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setAidTextStyle(aidTextStyle, cb)
    }

    override fun startSpeechRecognize(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.startSpeechRecognize(cb)
    }

    override fun getFocusedEditor(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getFocusedEditor(cb)
    }

    override fun getSelectionAndHasMarks(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.getSelectionAndHasMarks(cb)
    }

    override fun updateElementAttrs(elementSelectors: String, updateAttributes: Map<String, String>, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.updateElementAttrs(elementSelectors, updateAttributes, cb)
    }

    override fun onScrollStateChanged(oldState: Int, newState: Int, cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.onScrollStateChanged(oldState, newState, cb)
    }

    override fun decreaseIndent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.decreaseIndent(cb)
    }

    override fun increaseIndent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.increaseIndent(cb)
    }

    override fun setBlockQuote(set: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setBlockQuote(set, cb)
    }

    override fun getCursorStartPos(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getCursorStartPos(cb)
    }

    override fun ignoreActionModeInvalidate(ignore: Boolean) {
        checkProxy()
        webViewProxy?.ignoreActionModeInvalidate(ignore)
    }

    override fun clearWebkitSelection() {
        checkProxy()
        webViewProxy?.clearWebkitSelection()
    }

    override fun doInsertTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.doInsertTable(cb)
    }

    override fun addRowInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.addRowInTable(cb)
    }

    override fun addColumnInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.addColumnInTable(cb)
    }

    override fun removeRowInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.removeRowInTable(cb)
    }

    override fun removeColumnInTable(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.removeColumnInTable(cb)
    }

    override fun moveRowInTable(dir: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.moveRowInTable(dir, cb)
    }

    override fun moveColumnInTable(dir: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.moveColumnInTable(dir, cb)
    }

    override fun cutInEditor(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.cutInEditor(cb)
    }

    override fun copyInEditor(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.copyInEditor(cb)
    }

    override fun pasteInEditor(pasteResult: PasteResult, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.pasteInEditor(pasteResult, cb)
    }

    override fun shareTableToPicture(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.shareTableToPicture(cb)
    }

    override fun onAISummaryStartStop(isStart: Boolean) {
        checkProxy()
        webViewProxy?.onAISummaryStartStop(isStart)
    }

    override fun replaceSummaryContent(
        all: String,
        end: String,
        isFinal: Boolean,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.setSummaryContent(all, end, isFinal, cb)
    }

    override fun hasLinkCard(cb: ((String) -> Unit)?) {
        checkProxy()
        webViewProxy?.hasLinkCard(cb)
    }

    override fun onChangeWebViewWidthScale(start: Boolean) {
        checkProxy()
        webViewProxy?.onChangeWebViewWidthScale(start)
    }

    override fun getAIGCHtmlContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getAIGCHtmlContent(cb)
    }

    override fun selectAllAIGCText(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.selectAllAigcText(cb)
    }

    override fun getSelectedAIGCHtmlContent(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getSelectedAigcHtmlContent(cb)
    }

    override fun onActionItemClickedBefore(actionId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onActionItemClickedBefore(actionId, cb)
    }

    override fun onActionItemClickedAfter(actionId: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onActionItemClickedAfter(actionId, cb)
    }

    override fun getTableFullDisplayWidth(startCapture: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getTableFullDisplayWidth(startCapture, cb)
    }

    override fun onTableMenuDismiss(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onTableMenuDismiss(cb)
    }

    override fun onTableSelectMenuDismiss(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onTableSelectMenuDismiss(cb)
    }

    override fun insertVideoPlaceHolder(
        videoData: VideoData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertVideoPlaceHolder(videoData, addToPrevGroup, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, cb)
    }

    override fun updateVideo(
        videoData: VideoData,
        addToPrevGroup: Boolean,
        insertToEndInNonEditMode: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.updateVideo(videoData, addToPrevGroup, insertToEndInNonEditMode, cb)
    }

    override fun updateDocThumbnail(
        attachId: String,
        docAttachId: String,
        thumbnail: String,
        cb: ((String) -> Unit)?
    ) {
        checkProxy()
        webViewProxy?.updateDocthumbnail(attachId, docAttachId, thumbnail, cb)
    }

    override fun notifyScrollIdle(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.notifyScrollIdle(cb)
    }

    override fun exitBlockSelectionMode(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.exitBlockSelectionMode(cb)
    }

    override fun delBlockSelectionMode(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.delBlockSelectionMode(cb)
    }

    override fun shareBlockSelectionMode(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.shareBlockSelectionMode(cb)
    }

    override fun getSelectedBlockInfo(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getSelectedBlockInfo(cb)
    }

    override fun addHorizontalRule(hrStyle: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.addHorizontalRule(hrStyle, cb)
    }

    override fun setParagraphStyle(paragraphStyle: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setParagraphStyle(paragraphStyle, cb)
    }

    override fun updateHorizontalRule(hrStyle: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateHorizontalRule(hrStyle, cb)
    }

    override fun switchImageSize(attachId: String, targetScaleClass: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.switchImageSize(attachId, targetScaleClass, cb)
    }

    override fun deleteSelectedNode(blockType: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.deleteSelectedNode(blockType, cb)
    }

    override fun updateTimeBarHeight(height: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateTimeBarHeight(height, cb)
    }

    override fun setPanelHeight(type: Int, height: Int, isFloatPanel: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setPanelHeight(type, height, isFloatPanel, cb)
    }

    override fun onGlobalDragStart(x: Int, y: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragStart(x, y, cb)
    }

    override fun onGlobalDragLocation(x: Int, y: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragLocation(x, y, cb)
    }

    override fun onGlobalDragDrop(outSideNodeDrag: Boolean, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragDrop(outSideNodeDrag, cb)
    }

    override fun onGlobalBlockNotePaste(dragPosition: Int, htmlText: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalBlockNotePaste(dragPosition, htmlText, cb)
    }

    override fun onGlobalDragOtherParseClipEnd(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.onGlobalDragOtherParseClipEnd(cb)
    }

    override fun updateCardInfo(card: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateCardInfo(card, cb)
    }

    override fun convertToLink(info: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.convertToLink(info, cb)
    }

    override fun convertToCard(info: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.convertToCard(info, cb)
    }

    override fun setImageShareNotice(code: Int, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setImageShareNotice(code, cb)
    }

    override fun pastCopyAttachEnd(
        jsonObject: JSONObject,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.pastCopyAttachEnd(jsonObject, cb)
    }

    override fun loadParaStyleCssList(cssList: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.loadParaStyleCssList(cssList, cb)
    }

    override fun loadParaStyleFont(fontInfo: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.loadParaStyleFont(fontInfo, cb)
    }

    override fun loadAllParaStyleFont(cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.loadLocalAllFont()
    }

    override fun setTextHighlight(info: String, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.setTextHighlight(info, cb)
    }

    override fun insertNewPaint(
        paintInfo: PaintInfo,
        insertToEndInNonEditMode: Boolean,
        insertToLastNonEmptyParagraph: Boolean,
        cb: CallJSResponse?
    ) {
        checkProxy()
        webViewProxy?.insertNewPaint(paintInfo, insertToEndInNonEditMode, insertToLastNonEmptyParagraph, cb)
    }

    override fun updateNewPaint(updatePaintAttachId: String?, newPaintInfo: PaintInfo, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateNewPaint(updatePaintAttachId, newPaintInfo, cb)
    }

    override fun deleteNewPaint(paintAttachId: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.deleteNewPaint(paintAttachId, cb)
    }

    override fun getPaintRectInfo(paintAttachId: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.getPaintRectInfo(paintAttachId, cb)
    }

    override fun updateTargetPaintEditing(paintAttachId: String?, cb: CallJSResponse?) {
        checkProxy()
        webViewProxy?.updateTargetPaintEditing(paintAttachId, cb)
    }
}