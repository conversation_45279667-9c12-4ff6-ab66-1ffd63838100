/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : ElementSelectorsBuilder.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/7/28
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/7/28        1.0           create
 */
package com.oplus.notes.webview.container.web

class ElementSelectorsBuilder {

    companion object {
        private const val SELECTOR_TYPE_IMG = "div.v-progressive-image > img"
        private const val SELECTOR_TYPE_RECORD = "div.record"
        private const val SELECTOR_TYPE_CONTACT_CARD = "div.contactCard > div.contact-layout"
        private const val SELECTOR_TYPE_WEB_CARD = "div.card"
        private const val SELECTOR_TYPE_SCHEDULE_CARD = "div.scheduleCard > div.schedule-layout"
        private const val SELECTOR_TYPE_FILE_CARD = "div.filecard"

        private const val KEY_TYPE = "type"
        private const val TYPE_RECORD = "record"
        private const val TYPE_CONTACT_CARD = "contactCard"
        private const val TYPE_WEB_CARD = "card"
        private const val TYPE_SCHEDULE_CARD = "scheduleCard"
        private const val TYPE_FILE_CARD = "filecard"

        private const val KEY_ATTACH_ID = "attachid"

        @JvmStatic
        fun getImageSelectors(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_IMG, mapOf(KEY_ATTACH_ID to attachId))
            return build.builder()
        }

        @JvmStatic
        fun getRecordSelector(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_RECORD, mapOf(KEY_TYPE to TYPE_RECORD, KEY_ATTACH_ID to attachId))
            return build.builder()
        }

        @JvmStatic
        fun getContactCardSelector(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_CONTACT_CARD, mapOf(KEY_TYPE to TYPE_CONTACT_CARD, KEY_ATTACH_ID to attachId))
            return build.builder()
        }

        @JvmStatic
        fun getWebCardSelector(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_WEB_CARD, mapOf(KEY_TYPE to TYPE_WEB_CARD, KEY_ATTACH_ID to attachId))
            return build.builder()
        }

        @JvmStatic
        fun getScheduleCardSelector(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_SCHEDULE_CARD, mapOf(KEY_TYPE to TYPE_SCHEDULE_CARD, KEY_ATTACH_ID to attachId))
            return build.builder()
        }

        @JvmStatic
        fun getFileCardSelector(attachId: String): String {
            val build = Build()
            build.addSelectors(SELECTOR_TYPE_FILE_CARD, mapOf(KEY_TYPE to TYPE_FILE_CARD, KEY_ATTACH_ID to attachId))
            return build.builder()
        }
    }


    class Build {
        private val selectors = mutableMapOf<String, Map<String, Any>>()
        fun addSelectors(selectorType: String, attributes: Map<String, Any>): Build {
            selectors.putIfAbsent(selectorType, attributes)
            return this
        }

        fun builder(): String {
            val stringBuilder = StringBuilder()
            selectors.forEach { (selectorName, attributes) ->
                stringBuilder.append(selectorName)
                attributes.forEach { (attr, value) ->
                    stringBuilder.append("[$attr=\"$value\"]")
                }
                stringBuilder.append(",")
            }
            return stringBuilder.removeSuffix(",").toString()
        }
    }
}