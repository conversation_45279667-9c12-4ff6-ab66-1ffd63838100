/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : Response.kt
 * Description    : Response.kt
 * Version        : 1.0
 * Date           : 2023/6/6
 * Author         : <PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/6/6         1.0           create
 */
package com.oplus.notes.webview.container.common

import java.lang.Exception

sealed class Response {
    data class Success(val code: Int) : Response() {}
    data class Fail(val code: Int, val exception: Exception) : Response() {}
}
