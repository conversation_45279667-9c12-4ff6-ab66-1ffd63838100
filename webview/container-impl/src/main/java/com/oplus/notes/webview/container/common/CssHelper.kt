/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : CssHelper.kt
 * Description    : CssHelper.kt
 * Version        : 1.0
 * Date           : 2024/03/20
 * Author         : PengFei.Ma
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * PengFei.Ma     2024/03/20         1.0           create
 */
package com.oplus.notes.webview.container.common

import android.graphics.Color

object CssHelper {
    private const val MAX_COLOR_VALUE = 255
    /**
     * 将android color转为css3十六进制color，包含alpha通道
     */
    @JvmStatic
    fun convertToCssHexColor(androidColor: Int): String {
        val alpha = Color.alpha(androidColor) // 获取Alpha通道的值（透明度）
        val red = Color.red(androidColor) // 获取红色通道的值
        val green = Color.green(androidColor) // 获取绿色通道的值
        val blue = Color.blue(androidColor) // 获取蓝色通道的值
        val hexBuilder = StringBuilder()
        hexBuilder.append(String.format("#%02X%02X%02X", red, green, blue))
        if (alpha != MAX_COLOR_VALUE) {
            // 如果有透明度，则在前面添加透明度信息
            hexBuilder.append(String.format("%02X", alpha))
        }
        return hexBuilder.toString()
    }
}