/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - SystemFontHandler.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2024/2/5
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                        2024/2/5         1.0    Create this module
 **********************************************************************************/
package com.oplus.notes.webview.container.web

import com.heytap.tbl.webkit.WebResourceResponse
import androidx.webkit.WebViewAssetLoader.PathHandler
import java.io.FileInputStream
import java.net.URLConnection.guessContentTypeFromName

class SystemFontHandler : PathHandler {
    override fun handle(path: String): WebResourceResponse {
        val inputStream = FileInputStream("$SYSTEM_FONT_PATH$path")
        val mimeType = guessContentTypeFromName(path)
        return WebResourceResponse(mimeType, null, inputStream)
    }

    companion object {
        private const val SYSTEM_FONT_PATH = "/system/fonts/"
    }
}