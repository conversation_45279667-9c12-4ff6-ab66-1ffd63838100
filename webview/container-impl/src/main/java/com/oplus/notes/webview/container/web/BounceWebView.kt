/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : BounceWebView.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/7/30
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/7/30        1.0           create
 */
package com.oplus.notes.webview.container.web

import android.content.Context
import android.graphics.Canvas
import android.util.Log
import android.view.MotionEvent
import android.view.View
import com.heytap.tbl.webkit.WebView
import com.oplus.note.logger.AppLogger
import com.oplus.note.view.IScrollChild
import com.oplus.note.view.ScrollChildHelper
import com.oplus.note.view.WVScrollBar
import com.oplus.note.view.WVScrollbarView

open class BounceWebView(context: Context) : WebView(context), IScrollChild {
    companion object {
        private const val TAG = "BounceWebView"
        private const val DEBUG_SCROLL = false
    }

    private val childHelper by lazy { ScrollChildHelper(this) }
    private var scrollbarView: WVScrollbarView? = null
    private var scrollBar: WVScrollBar? = null
    private var scrollbarScrollScale = 1F
    private var initScale = 1F

    override fun canReceiveParentScroll(): Boolean {
        return true
    }

    override fun receiveParentScroll(dy: Int, consumed: IntArray, type: Int) {
        val oldScrollY = scrollY
        val scrollRangeY = getScrollRangeY()
        var newScrollY = oldScrollY + dy
        if (newScrollY > scrollRangeY) {
            consumed[1] = scrollRangeY - oldScrollY
            newScrollY = scrollRangeY
        } else if (newScrollY < 0) {
            consumed[1] = 0 - oldScrollY
            newScrollY = 0
        } else {
            consumed[1] = dy
        }
        if (DEBUG_SCROLL) {
            Log.i(TAG, "onChildScroll, scrollDeltaY:${consumed[1]}, newScrollY:$newScrollY")
        }
        scrollTo(scrollX, newScrollY)
    }

    override fun cancelScroll() {
        childHelper.cancelScroll()
    }


    private fun getScrollRangeY(): Int {
        return computeVerticalScrollRange() - computeVerticalScrollExtent()
    }

    fun setPaintScrollScale(scale: Float) {
        scrollbarScrollScale = scale
    }

    fun setScrollbarView(scrollbarView: WVScrollbarView?) {
        AppLogger.BASIC.d(TAG, "setScrollbar")
        this.scrollbarView = scrollbarView
        this.scrollBar = scrollbarView?.scrollBar
        scrollbarView?.setCallback(scrollbarCallback, WVScrollbarView.VIEW_MODE)
    }


    /**
     * 刷新scrollbar颜色模式
     * 0-> 跟随亮暗色模式变化
     * >0 固定亮色
     * <0 固定暗色
     */
    fun refreshScrollbarColor(mode: Int) {
        AppLogger.BASIC.d(TAG, "refreshScrollbarColor mode=$mode")
        scrollBar?.refreshScrollBarColor(mode)
    }

    fun setInitScale(scale: Float) {
        AppLogger.BASIC.d(TAG, "setInitScale scale=$scale")
        initScale = scale
    }

    fun setIgnoreAwakenScrollbar(ignoreAwakenScrollbar: Boolean) {
        scrollbarView?.setIgnoreAwakenScrollbar(ignoreAwakenScrollbar)
    }

    private val scrollbarCallback = object : WVScrollbarView.WVScrollBarCallback {
        override fun computeVerticalScrollRange(): Int {
//            return (<EMAIL>() * scrollbarScrollScale).toInt()
            val paintRange = scrollbarView?.getPaintViewCallback()?.computeVerticalScrollRange()
            val range = <EMAIL>()
//            AppLogger.BASIC.d(TAG, "computeVerticalScrollRange range=$range,paintRange=$paintRange")
            return paintRange ?: range
        }

        override fun computeVerticalScrollOffset(): Int {
            val paintOffset = scrollbarView?.getPaintViewCallback()?.computeVerticalScrollOffset()
            val offset = <EMAIL>()
//            AppLogger.BASIC.d(TAG, "computeVerticalScrollOffset offset=$offset,paintOffset=$paintOffset")
            return paintOffset ?: offset
        }

        override fun computeVerticalScrollExtent(): Int {
            return <EMAIL>()
        }

        override fun onCOUIScrollStart(view: View?, delegate: WVScrollBar?) {
            AppLogger.BASIC.d(TAG, "onCOUIScrollStart")
            <EMAIL>(true)
        }

        override fun onCOUIScrolled(view: View?, delegate: WVScrollBar?, touchDeltaY: Int, viewScrollDeltaY: Int, scrollPercent: Float) {
            <EMAIL>(0, (viewScrollDeltaY * initScale).toInt())
           /* AppLogger.BASIC.d(
                TAG, "onCOUIScrolled scrollRange=$scrollRange,scrollPosition=$scrollPosition," +
                        "viewScrollDeltaY=$viewScrollDeltaY,scrollPercent=$scrollPercent,scale=$scrollbarScrollScale"
            )*/
        }

        override fun onCOUIScrollEnd(view: View?, delegate: WVScrollBar?) {
            AppLogger.BASIC.d(TAG, "onCOUIScrollEnd")
            <EMAIL>(false)
        }

        override fun getScrollViewScrollX(): Int {
            return <EMAIL>
        }

        override fun getScrollViewScrollY(): Int {
            return <EMAIL>
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        return if (scrollBar?.onInterceptTouchEvent(ev) == true) true else super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        return if (scrollBar?.onTouchEvent(event) == true) true else super.onTouchEvent(event)
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
//        scrollBar?.dispatchDrawOver(canvas)
        scrollbarView?.doOnDispatchDraw()
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        scrollbarView?.doOnVisibilityChanged(visibility)
    }

    override fun awakenScrollBars(): Boolean {
//        AppLogger.BASIC.d(TAG, "awakenScrollBars")
        return if (scrollbarView?.doOnAwakenScrollBars() == true) true else super.awakenScrollBars()
//        return super.awakenScrollBars()
    }
}