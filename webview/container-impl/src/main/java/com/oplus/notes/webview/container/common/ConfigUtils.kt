/**
 * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * File           : ConfigUtils.kt
 * Description    : ConfigUtils.kt
 * Version        : 1.0
 * Date           : 2024/7/18
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2024/7/18         1.0           create
 */
package com.oplus.notes.webview.container.common

import android.content.Context
import android.content.res.Configuration
import android.provider.Settings
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webview.data.CacheRecycleParams

object ConfigUtils {
    private const val TAG = "ConfigUtils"
    private const val SETTINGS_CURRENT_TYPEFACE_NAME = "current_typeface_name"

    @JvmStatic
    fun getWebViewCacheRecycleParams(context: Context): CacheRecycleParams? {
        AppLogger.BASIC.d(TAG, "getWebViewCacheRecycleParams: ")
        kotlin.runCatching {
            return CacheRecycleParams(
                typefaceName = Settings.System.getString(context.contentResolver, SETTINGS_CURRENT_TYPEFACE_NAME) ?: "",
                uiMode = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK,
                uiDensity = context.resources.displayMetrics.densityDpi
            )
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getWebViewCacheRecycleParams fail:${it.message}")
            return null
        }
        return null
    }
}