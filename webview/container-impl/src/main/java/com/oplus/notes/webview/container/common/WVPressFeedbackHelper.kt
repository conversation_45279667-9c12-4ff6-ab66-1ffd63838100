/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: PressFeekbackFrameLayout.kt
 * * Description: PressFeekbackFrameLayout
 * * Version: 1.0
 * * Date: 2020/07/30
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.oplus.notes.webview.container.common

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import androidx.core.view.animation.PathInterpolatorCompat
import com.oplus.notes.webview.container.impl.R
import java.lang.ref.WeakReference

@SuppressLint("ClickableViewAccessibility")
class WVPressFeedbackHelper {
    companion object {
        private const val DURATION_DOWN = 200L
        private const val DURATION_UP = 340L
        private const val MAX_SCALE = 0.98F
        private const val MIN_SCALE = 0.93F
        private const val MIN_VIEW_SIZE = 50
        private const val MAX_VIEW_WIDTH = 328
        private const val MAX_VIEW_HEIGHT = 220
        private const val ZERO_TWO = 0.2F
        private const val ZERO_FOUR = 0.4F
        private const val TWO = 2F
    }

    private var mView: WeakReference<View>
    private var mDownAnimator: Animator? = null
    private var mUpAnimator: Animator? = null
    private var mCurrentScaleX = 1F
    private var mCurrentScaleY = 1F
    private var mBaseScale = 1F
    private var mEnable = true
    private val mIntercept: Boolean
    private val mMaxChange: Float
    private var density: Float = 0f
    private val mDownInterpolator by lazy { PathInterpolatorCompat.create(ZERO_FOUR, 0F, ZERO_TWO, 1F) }
    private val mUpInterpolator by lazy { PathInterpolatorCompat.create(0F, 0F, ZERO_TWO, 1F) }
    private val mScaleXListener by lazy { AnimatorUpdateListener { mCurrentScaleX = it.animatedValue as Float } }
    private val mScaleYListener by lazy { AnimatorUpdateListener { mCurrentScaleY = it.animatedValue as Float } }

    constructor(view: View) : this(view, true)

    constructor(view: View, intercept: Boolean) : this(view, intercept, true)

    constructor(view: View, intercept: Boolean, isNeedInitListener: Boolean) {
        density = view.context.resources.displayMetrics.density
        mView = WeakReference(view)
        mIntercept = intercept
        mMaxChange = view.resources.getDimension(R.dimen.press_feedback_max_change) * TWO
        if (isNeedInitListener) {
            initTouchListener()
        }
    }

    private fun initTouchListener() {
        getView()?.setOnTouchListener { _, motionEvent ->
            if (!mEnable) {
                return@setOnTouchListener false
            }

            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    onActionDown()
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    onActionUp()
                }
            }
            return@setOnTouchListener mIntercept
        }
    }

    fun setAnimationTarget(view: View) {
        mView = WeakReference(view)
    }

    fun enable(enable: Boolean) {
        mEnable = enable
    }

    fun setBaseScale(scale: Float) {
        mBaseScale = scale
    }

    private fun getView(): View? {
        return mView.get()
    }

    fun onActionDown() {
        cancelAnimator()
        mDownAnimator = downAnimator()
        mDownAnimator?.start()
    }

    fun onActionUp() {
        cancelAnimator()
        mUpAnimator = upAnimator()
        mUpAnimator?.start()
    }

    private fun cancelAnimator() {
        mUpAnimator?.cancel()
        mDownAnimator?.cancel()
    }

    private fun downAnimator(): Animator? {
        return getView()?.let {
            val scale = getScale(it.width, it.height)
            val animatorX = ObjectAnimator.ofFloat(it, "scaleX", mBaseScale, scale)
            val animatorY = ObjectAnimator.ofFloat(it, "scaleY", mBaseScale, scale)
            animatorX.addUpdateListener(mScaleXListener)
            animatorY.addUpdateListener(mScaleYListener)
            return AnimatorSet().apply {
                duration = DURATION_DOWN
                interpolator = mDownInterpolator
                playTogether(animatorX, animatorY)
            }
        }
    }

    private fun upAnimator(): Animator? {
        return getView()?.let {
            val scaleX = ObjectAnimator.ofFloat(it, "scaleX", mCurrentScaleX, mBaseScale)
            val scaleY = ObjectAnimator.ofFloat(it, "scaleY", mCurrentScaleY, mBaseScale)
            scaleX.addUpdateListener(mScaleXListener)
            scaleY.addUpdateListener(mScaleYListener)
            return AnimatorSet().apply {
                duration = DURATION_UP
                interpolator = mUpInterpolator
                playTogether(scaleX, scaleY)
            }
        }
    }

    private fun getScale(width: Int, height: Int): Float {
        val scale = calculateScale(width, height)
        val scaleX = getScale(width, mMaxChange, scale)
        val scaleY = getScale(height, mMaxChange, scale)
        return maxOf(scaleX, scaleY)
    }

    private fun calculateScale(width: Int, height: Int): Float {
        val minCardSize: Int = MIN_VIEW_SIZE * MIN_VIEW_SIZE
        val maxCardSize: Int = MAX_VIEW_WIDTH * MAX_VIEW_HEIGHT
        val cardSize: Int = px2dp(width) * px2dp(height)
        val perSizeByScale: Float = (MAX_SCALE - MIN_SCALE) / (maxCardSize - minCardSize)
        return when {
            (cardSize < minCardSize) -> 1f
            (cardSize > maxCardSize) -> MAX_SCALE
            else -> (cardSize - minCardSize) * perSizeByScale + MIN_SCALE
        }
    }

    /**
     * View变化过大，会导致Handler或Timer任务失效，因此给定一个最大的scale
     */
    private fun getScale(distance: Int, maxChange: Float, scale: Float): Float {
        val changeScale = mBaseScale - scale
        val changeHeight = distance * changeScale
        return if ((changeHeight > maxChange) && (distance != 0)) {
            mBaseScale - maxChange / distance
        } else {
            scale
        }
    }

    private fun px2dp(px: Int): Int {
        return if (density == 0f) {
            0
        } else Math.round(px / density)
    }
}