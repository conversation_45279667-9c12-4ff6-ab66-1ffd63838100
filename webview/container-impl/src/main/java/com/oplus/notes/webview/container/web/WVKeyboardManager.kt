/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: WVKeyboardManager.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2025/06/17
 * * Author: W9099175
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.notes.webview.container.web

import android.view.KeyEvent

class KeyboardManagerImpl : IKeyboardManager {

    override fun isCtrlZEvent(event: KeyEvent): <PERSON><PERSON><PERSON> {
        return ctrl(KeyEvent.KEYCODE_Z).matches(event)
    }

    override fun isCtrlSEvent(event: KeyEvent): <PERSON><PERSON>an {
        return ctrl(KeyEvent.KEYCODE_S).matches(event)
    }

    override fun isCtrlFEvent(event: KeyEvent): Boolean {
        return ctrl(KeyEvent.KEYCODE_F).matches(event)
    }

    override fun isCtrlTEvent(event: KeyEvent): Boolean {
        return ctrl(KeyEvent.KEYCODE_T).matches(event)
    }

    override fun isTabEvent(event: KeyEvent): Boolean {
        return KeyCombo(KeyEvent.KEYCODE_TAB).matches(event)
    }

    override fun isShiftTabEvent(event: KeyEvent): Boolean {
        return shift(KeyEvent.KEYCODE_TAB).matches(event)
    }

    override fun isCtrlShiftPeriodEvent(event: KeyEvent): Boolean {
        return ctrlShift(KeyEvent.KEYCODE_PERIOD).matches(event)
    }

    private fun ctrl(keyCode: Int) = KeyCombo(keyCode, isCtrlPressed = true)
    private fun shift(keyCode: Int) = KeyCombo(keyCode, isShiftPressed = true)
    private fun ctrlShift(keyCode: Int) =
        KeyCombo(keyCode, isCtrlPressed = true, isShiftPressed = true)

    data class KeyCombo(
        val keyCode: Int,
        val isCtrlPressed: Boolean = false,
        val isShiftPressed: Boolean = false,
        val isAltPressed: Boolean = false
    ) {
        fun matches(event: KeyEvent): Boolean {
            return event.keyCode == keyCode &&
                    event.isCtrlPressed == isCtrlPressed &&
                    event.isShiftPressed == isShiftPressed &&
                    event.isAltPressed == isAltPressed &&
                    event.action == KeyEvent.ACTION_DOWN
        }
    }
}

interface IKeyboardManager {

    fun isCtrlZEvent(event: KeyEvent): Boolean

    fun isCtrlSEvent(event: KeyEvent): Boolean

    fun isCtrlFEvent(event: KeyEvent): Boolean

    fun isCtrlTEvent(event: KeyEvent): Boolean

    fun isTabEvent(event: KeyEvent): Boolean

    fun isShiftTabEvent(event: KeyEvent): Boolean

    fun isCtrlShiftPeriodEvent(event: KeyEvent): Boolean
}