/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVUndoManager.kt
 * Description    : WVUndoManager.kt
 * Version        : 1.0
 * Date           : 2023/9/4
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/9/4         1.0           create
 */
package com.oplus.notes.webview.container.impl

import android.util.Log
import com.oplus.notes.webview.container.api.IUndoManager
import com.oplus.notes.webview.container.api.IWebViewContainer

class WVUndoManager : IUndoManager {
    companion object {
        private const val TAG = "WVUndoManager"
    }

    private var canUndo: Boolean = false
    private var canRedo: Boolean = false
    private var webViewContainer: IWebViewContainer? = null
    private var interceptUndoManager: IUndoManager? = null

    override fun setWebViewContainer(webViewContainer: IWebViewContainer?) {
        this.webViewContainer = webViewContainer
    }

    override fun setInterceptUndoManager(undoManager: IUndoManager?) {
        interceptUndoManager = undoManager
    }

    override fun undo() {
        Log.d(TAG, "undo: ")
        interceptUndoManager?.undo() ?: webViewContainer?.undo()
    }

    override fun redo() {
        Log.d(TAG, "redo: ")
        interceptUndoManager?.redo() ?: webViewContainer?.redo()
    }

    override fun canUndo(): Boolean {
        return interceptUndoManager?.canUndo() ?: canUndo
    }

    override fun canRedo(): Boolean {
        return interceptUndoManager?.canRedo() ?: canRedo
    }

    override fun setCanUndo(canUndo: Boolean) {
        Log.d(TAG, "setCanUndo: canUndo=$canUndo")
        this.canUndo = canUndo
    }

    override fun setCanRedo(canRedo: Boolean) {
        Log.d(TAG, "setCanUndo: canUndo=$canRedo")
        this.canRedo = canRedo
    }
}