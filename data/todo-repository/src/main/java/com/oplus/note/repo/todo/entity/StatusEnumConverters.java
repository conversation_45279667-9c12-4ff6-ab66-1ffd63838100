package com.oplus.note.repo.todo.entity;

import androidx.room.TypeConverter;

import com.oplus.note.repo.todo.entity.ToDo;

public class StatusEnumConverters {

    @TypeConverter
    public static ToDo.StatusEnum intToEnum(int type) {
        return ToDo.StatusEnum.values()[type];
    }

    @TypeConverter
    public static int enumToInt(ToDo.StatusEnum statusEnum) {
        if (statusEnum == null) {
            return ToDo.StatusEnum.INVALID.ordinal();
        }
        return statusEnum.ordinal();
    }
}
