/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: MainActivity.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/24
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.repo.todo

object TodoRepoFactory {

    private var agent: ToDoRepo? = null

    fun get(): ToDoRepo? {
        return agent
    }

    fun register(agent: ToDoRep<PERSON>) {
        TodoRepoFactory.agent = agent
    }

}