/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ToDo.java
 * * Description: ToDo
 * * Version: 1.0
 * * Date: 2019/9/23
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/9/23 1.0 build this module
 ****************************************************************/

package com.oplus.note.repo.todo.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.oplus.note.repo.todo.ToDoExtra;

import java.util.Date;
import java.util.Objects;
import java.util.UUID;

@Entity(tableName = "todo")
public class ToDo implements Parcelable {

    public enum StatusEnum {
        /**
         * RESTORE 状态 标记已删除的数据需要恢复到云端
         * */
        INVALID, NEW, MODIFIED, UNCHANGE, RESTORE, ARCHIVED
    }

    public ToDo() {
    }

    @SuppressWarnings("IncompleteCopyConstructor")
    public ToDo(ToDo other) {
        if (other == null) {
            return;
        }
        this.mLocalId = other.mLocalId;
        this.mParentId = other.mParentId;
        this.mGlobalId = other.mGlobalId;
        this.mContent = other.mContent;
        this.mAlarmTime = other.mAlarmTime == null ? null : new Date(other.mAlarmTime.getTime());
        this.mCreateTime = other.mCreateTime == null ? null : new Date(other.mCreateTime.getTime());
        this.mUpdateTime = other.mUpdateTime == null ? null : new Date(other.mUpdateTime.getTime());
        this.mFinishTime = other.mFinishTime == null ? null : new Date(other.mFinishTime.getTime());
        this.mTimestamp = other.mTimestamp == null ? null : new Date(other.mTimestamp.getTime());
        this.mStatus = other.mStatus;
        this.mIsDelete = other.mIsDelete;
        if (other.mExtra != null) {
            this.mExtra = new ToDoExtra(other.mExtra);
        }
        this.mIsLocal = other.mIsLocal;
        this.mForceReminderPre = other.mForceReminderPre;
        this.mRepeatRulePre = other.mRepeatRulePre;
        this.mAlarmTimePre = other.mAlarmTimePre;
        this.mFromPackage = other.mFromPackage;
        this.mNextAlarmTime = other.mNextAlarmTime;
        this.mReminded = other.mReminded;
        this.mSysVersion = other.mSysVersion;
        this.mColorIndex = other.mColorIndex;
    }

    @PrimaryKey
    @NonNull
    @ColumnInfo(name = TodoTableColumns.LOCAL_ID)
    @TypeConverters({UUIDConverters.class})
    private UUID mLocalId;

    @Nullable
    @ColumnInfo(name = "parent_id")
    private String mParentId;

    @TypeConverters({UUIDConverters.class})
    @ColumnInfo(name = "global_id")
    private UUID mGlobalId = UUID.randomUUID();

    @Nullable
    @ColumnInfo(name = TodoTableColumns.CONTENT)
    private String mContent;

    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = TodoTableColumns.ALARM_TIME)
    private Date mAlarmTime;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "alarm_next_time")
    private Date mNextAlarmTime;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "create_time")
    private Date mCreateTime;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "update_time")
    private Date mUpdateTime;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = TodoTableColumns.FINISH_TIME)
    private Date mFinishTime;

    @Nullable
    @TypeConverters({StatusEnumConverters.class})
    @ColumnInfo(name = "status")
    private StatusEnum mStatus;

    @Nullable
    @TypeConverters(BooleanConverters.class)
    @ColumnInfo(name = TodoTableColumns.IS_DELETE)
    private Boolean mIsDelete = false;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "timestamp")
    private Date mTimestamp;

    @Nullable
    @TypeConverters(ToDoExtraConverters.class)
    @ColumnInfo(name = TodoTableColumns.EXTRA)
    private ToDoExtra mExtra;

    @Nullable
    @TypeConverters(BooleanConverters.class)
    @ColumnInfo(name = "is_local")
    private Boolean mIsLocal;

    @TypeConverters(BooleanConverters.class)
    @ColumnInfo(name = "is_reminded")
    private Boolean mReminded = false;

    @Nullable
    @TypeConverters(BooleanConverters.class)
    @ColumnInfo(name = "force_reminder_pre")
    private Boolean mForceReminderPre;

    @Nullable
    @ColumnInfo(name = "repeat_rule_pre")
    private String mRepeatRulePre;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "alarm_time_pre")
    private Date mAlarmTimePre;

    @Nullable
    @ColumnInfo(name = "from_package")
    private String mFromPackage;

    @Nullable
    @ColumnInfo(name = "sysVersion", defaultValue = "0")
    private long mSysVersion;

    @Nullable
    @Ignore
    private boolean mIsSorted;

    @Nullable
    @ColumnInfo(name = "color_index", defaultValue = "-1")
    private int mColorIndex = -1;

    public void setIsSort(boolean b) {
        mIsSorted = b;
    }

    @Nullable
    public boolean isSorted() {
        return mIsSorted;
    }

    @Nullable
    public long getSysVersion() {
        return mSysVersion;
    }

    public void setSysVersion(long sysVersion) {
        this.mSysVersion = sysVersion;
    }

    @Nullable
    public Date getNextAlarmTime() {
        return mNextAlarmTime;
    }

    public void setNextAlarmTime(Date nextAlarmTime) {
        mNextAlarmTime = nextAlarmTime;
    }

    @Nullable
    public String getParentId() {
        return mParentId;
    }

    public void setParentId(String parentId) {
        mParentId = parentId;
    }

    @Nullable
    public int getColorIndex() {
        return mColorIndex;
    }

    public void setColorIndex(int colorIndex) {
        this.mColorIndex = colorIndex;
    }

    public Boolean getReminded() {
        return mReminded;
    }

    public void setReminded(Boolean reminded) {
        mReminded = reminded;
    }

    @NonNull
    public UUID getLocalId() {
        return mLocalId;
    }

    public void setLocalId(@NonNull UUID localId) {
        this.mLocalId = localId;
    }

    public UUID getGlobalId() {
        return mGlobalId;
    }

    public void setGlobalId(@NonNull UUID globalId) {
        this.mGlobalId = globalId;
    }

    public String getContent() {
        return mContent;
    }

    public void setContent(String content) {
        this.mContent = content;
    }

    public Date getAlarmTime() {
        return mAlarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        if (alarmTime == null) {
            this.mNextAlarmTime = null;
            this.setRepeatRule(null);
            this.setForceReminder(false);
        }
        this.mAlarmTime = alarmTime;
    }

    public Date getCreateTime() {
        return mCreateTime;
    }

    public void setCreateTime(Date createTime) {
        this.mCreateTime = createTime;
    }

    public Date getUpdateTime() {
        return mUpdateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.mUpdateTime = updateTime;
    }

    public Date getFinishTime() {
        return mFinishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.mFinishTime = finishTime;
    }

    @Nullable
    public StatusEnum getStatus() {
        return mStatus;
    }

    public void setStatus(StatusEnum status) {
        this.mStatus = status;
    }
    public Boolean isDelete() {
        return mIsDelete;
    }

    public void setIsDelete(Boolean delete) {
        mIsDelete = delete;
    }

    /**
     * 仅在使用extra对象本身时使用
     * 如需获取extra的子字段，请用特定的方案如getForceReminder
     */
    public ToDoExtra getExtra() {
        return mExtra;
    }

    public void setExtra(ToDoExtra extra) {
        this.mExtra = extra;
    }

    public void setIsLocal(Boolean isLocal) {
        mIsLocal = isLocal;
    }

    public Boolean isLocal() {
        return (mIsLocal == null ? false : mIsLocal.booleanValue());
    }

    public void setAlarmTimePre(Date alarmTimePre) {
        mAlarmTimePre = alarmTimePre;
    }

    @Nullable
    public Date getAlarmTimePre() {
        return mAlarmTimePre;
    }

    public void setForceReminderPre(Boolean forceReminderPre) {
        mForceReminderPre = forceReminderPre;
    }

    @Nullable
    public Boolean getForceReminderPre() {
        if (mForceReminderPre == null) {
            return false;
        }
        return mForceReminderPre;
    }

    public void setRepeatRulePre(String repeatRulePre) {
        mRepeatRulePre = repeatRulePre;
    }

    @Nullable
    public String getRepeatRulePre() {
        return mRepeatRulePre != null ? mRepeatRulePre : "";
    }

    @Nullable
    public Date getTimestamp() {
        return (mTimestamp != null) ? mTimestamp : new Date(0);
    }

    public void setTimestamp(Date timestamp) {
        mTimestamp = timestamp;
    }

    public boolean isAlarmTimeValid() {
        return mAlarmTime != null && mAlarmTime.getTime() > 0;
    }

    public boolean isComplete() {
        return getFinishTime() != null && getFinishTime().getTime() != 0;
    }

    public boolean isAlarmExpired() {
        if (isComplete()) {
            return false;
        }
        return getAlarmTime() != null && getAlarmTime().getTime() < System.currentTimeMillis();
    }

    @Nullable
    public boolean getForceReminder() {
        return (mExtra != null && mExtra.getForceReminder() != null) ? mExtra.getForceReminder() : false;
    }

    public void setForceReminder(Boolean forceReminder) {
        if (mExtra == null) {
            mExtra = new ToDoExtra();
        }
        mExtra.setForceReminder(forceReminder);
    }
    public long getSortTime() {
        if (mExtra == null) {
            return 0;
        }
        Long sortTime = mExtra.getSortTime();
        if (sortTime == null) {
            return 0;
        } else {
            return sortTime;
        }
    }

    public void setSortTime(long sortTime) {
        if (sortTime == getSortTime()) {
            return;
        }
        if (mExtra == null) {
            mExtra = new ToDoExtra();
        }
        mExtra.setSortTime(sortTime);
    }

    @NonNull
    public String getRepeatRule() {
        return (mExtra != null && mExtra.getRepeatRule() != null) ? mExtra.getRepeatRule() : "";
    }

    public void setRepeatRule(String rule) {
        if (mExtra == null) {
            mExtra = new ToDoExtra();
        }
        mExtra.setRepeatRule(rule);
    }

    public void setFromPackage(String fromPackage) {
        mFromPackage = fromPackage;
    }

    public String getFromPackage() {
        return mFromPackage;
    }

    /**
     * 判断该待办是否已经同步到云端。
     *
     * @return true 已同步至云端
     */

    public boolean hasSyncedToCloud() {
        return mSysVersion > 0;
    }

    /**
     * 重置为新建状态
     */
    public void resetToNewState() {
        mStatus = StatusEnum.NEW;
        if (mGlobalId == null) {
            mGlobalId = UUID.randomUUID();
        }
        mSysVersion = 0;
    }


    public boolean contentEquals(ToDo toDo) {
        if (this == toDo) return true;
        if (toDo == null) return false;
        return Objects.equals(mContent, toDo.mContent) &&
                Objects.equals(mAlarmTime, toDo.mAlarmTime) &&
                Objects.equals(mFinishTime, toDo.mFinishTime) &&
                Objects.equals(mExtra, toDo.mExtra);
    }

    public int getIdentification() {
        return mLocalId != null ? mLocalId.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "ToDo{" +
                "mLocalId=" + mLocalId
                + ", mParentId='" + mParentId + '\''
                + ", mGlobalId=" + mGlobalId
                + ", mAlarmTime=" + mAlarmTime
                + ", mNextAlarmTime=" + mNextAlarmTime
                + ", mCreateTime=" + mCreateTime
                + ", mUpdateTime=" + mUpdateTime
                + ", mFinishTime=" + mFinishTime
                + ", mStatus=" + mStatus
                + ", mIsDelete=" + mIsDelete
                + ", mTimestamp=" + mTimestamp
                + ", mExtra=" + mExtra
                + ", mIsLocal=" + mIsLocal
                + ", mReminded=" + mReminded
                + ", mForceReminderPre=" + mForceReminderPre
                + ", mRepeatRulePre='" + mRepeatRulePre + '\''
                + ", mAlarmTimePre=" + mAlarmTimePre
                + ", mFromPackage='" + mFromPackage + '\''
                + ", mSysVersion=" + mSysVersion
                + ", mColorIndex='" + mColorIndex
                + '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeSerializable(this.mLocalId);
        dest.writeSerializable(this.mGlobalId);
        dest.writeString(this.mParentId);
        dest.writeString(this.mContent);
        dest.writeLong(this.mNextAlarmTime != null ? this.mNextAlarmTime.getTime() : -1);
        dest.writeLong(this.mAlarmTime != null ? this.mAlarmTime.getTime() : -1);
        dest.writeLong(this.mCreateTime != null ? this.mCreateTime.getTime() : -1);
        dest.writeLong(this.mUpdateTime != null ? this.mUpdateTime.getTime() : -1);
        dest.writeLong(this.mFinishTime != null ? this.mFinishTime.getTime() : -1);
        dest.writeInt(this.mStatus == null ? -1 : this.mStatus.ordinal());
        dest.writeValue(this.mIsDelete);
        dest.writeLong(this.mTimestamp != null ? this.mTimestamp.getTime() : -1);
        dest.writeParcelable(this.mExtra, flags);
        dest.writeValue(this.mIsLocal);
        dest.writeValue(this.mReminded);
        dest.writeValue(mForceReminderPre);
        dest.writeString(mRepeatRulePre);
        dest.writeLong(this.mAlarmTimePre != null ? this.mAlarmTimePre.getTime() : -1);
        dest.writeString(mFromPackage);
        dest.writeLong(mSysVersion);
        dest.writeInt(this.mColorIndex);
    }

    protected ToDo(Parcel in) {
        this.mLocalId = (UUID) in.readSerializable();
        this.mGlobalId = (UUID) in.readSerializable();
        this.mParentId = in.readString();
        this.mContent = in.readString();
        long tmpNextAlarmTime = in.readLong();
        this.mNextAlarmTime = tmpNextAlarmTime == -1 ? null : new Date(tmpNextAlarmTime);
        long tmpMAlarmTime = in.readLong();
        this.mAlarmTime = tmpMAlarmTime == -1 ? null : new Date(tmpMAlarmTime);
        long tmpMCreateTime = in.readLong();
        this.mCreateTime = tmpMCreateTime == -1 ? null : new Date(tmpMCreateTime);
        long tmpMUpdateTime = in.readLong();
        this.mUpdateTime = tmpMUpdateTime == -1 ? null : new Date(tmpMUpdateTime);
        long tmpMFinishTime = in.readLong();
        this.mFinishTime = tmpMFinishTime == -1 ? null : new Date(tmpMFinishTime);
        int tmpMStatus = in.readInt();
        this.mStatus = tmpMStatus == -1 ? null : StatusEnum.values()[tmpMStatus];
        this.mIsDelete = (Boolean) in.readValue(Boolean.class.getClassLoader());
        long tmpMTimestamp = in.readLong();
        this.mTimestamp = tmpMTimestamp == -1 ? null : new Date(tmpMTimestamp);
        this.mExtra = in.readParcelable(ToDoExtra.class.getClassLoader());
        this.mIsLocal = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.mReminded = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.mForceReminderPre = (Boolean) in.readValue(Boolean.class.getClassLoader());
        this.mRepeatRulePre = in.readString();
        long tmpMAlarmTimePre = in.readLong();
        this.mAlarmTimePre = tmpMAlarmTimePre == -1 ? null : new Date(tmpMAlarmTimePre);
        mFromPackage = in.readString();
        this.mSysVersion = in.readLong();
        this.mColorIndex = in.readInt();
    }

    public static final Parcelable.Creator<ToDo> CREATOR = new Parcelable.Creator<ToDo>() {
        @Override
        public ToDo createFromParcel(Parcel source) {
            return new ToDo(source);
        }

        @Override
        public ToDo[] newArray(int size) {
            return new ToDo[size];
        }
    };
}
