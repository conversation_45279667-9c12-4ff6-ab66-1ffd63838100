/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: TodoTableColumns.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/7/13
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.repo.todo.entity

class TodoTableColumns {
    companion object {
        const val LOCAL_ID = "local_id"
        const val CONTENT = "content"
        const val ALARM_TIME = "alarm_time"
        const val FINISH_TIME = "finish_time"
        const val IS_DELETE = "is_delete"
        const val EXTRA = "extra"
        const val REPEAT_RULE = "repeat_rule"
    }
}