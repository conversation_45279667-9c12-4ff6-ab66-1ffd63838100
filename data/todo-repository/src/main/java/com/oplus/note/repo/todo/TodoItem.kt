/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: MainActivity.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/24
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.repo.todo

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Ignore

class TodoItem : Parcelable {
    @ColumnInfo(name = "local_id")
    var localId: String? = null

    @ColumnInfo(name = "content")
    var content = ""

    @ColumnInfo(name = "alarm_time")
    var alarmTime: Long? = null

    @ColumnInfo(name = "color_index")
    var colorIndex: Int = -1

    @ColumnInfo(name = "extra")
    var toDoExtraStr: String? = null

    @ColumnInfo(name = "finish_time")
    var finishTime: Long = 0L

    @Ignore
    private var toDoExtra: ToDoExtra? = null

    @Ignore
    private var formatted = false

    constructor()

    constructor(`in`: Parcel) {
        this.localId = `in`.readString()
        this.content = `in`.readString() ?: ""
        val tmpTime = `in`.readLong()
        this.alarmTime = if (tmpTime <= 0L) null else tmpTime
        this.colorIndex = `in`.readInt()
        this.toDoExtraStr = `in`.readString()
        this.finishTime = `in`.readLong()
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeString(this.localId)
        dest.writeString(this.content)
        dest.writeLong(this.alarmTime ?: 0L)
        dest.writeInt(this.colorIndex)
        dest.writeString(this.toDoExtraStr)
        dest.writeLong(this.finishTime)
    }

    override fun toString(): String {
        return "TodoItem(localId=$localId, content=$content, alarmTime=$alarmTime" +
                ",colorIndex=$colorIndex,toDoExtraStr=$toDoExtraStr,getTodoExtra=${getTodoExtra()})"
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<TodoItem> = object : Parcelable.Creator<TodoItem> {
            override fun createFromParcel(source: Parcel): TodoItem {
                return TodoItem(source)
            }

            override fun newArray(size: Int): Array<TodoItem> {
                return newArray(size)
            }
        }
    }

    fun getTodoExtra(): ToDoExtra? {
        if (!formatted) {
            toDoExtra = if (toDoExtraStr.isNullOrEmpty()) {
                ToDoExtra()
            } else {
                com.oplus.note.utils.ExtraJsonHelper.fromJson(toDoExtraStr ?: "", ToDoExtra::class.java)
            }
            formatted = true
        }
        return toDoExtra
    }

    fun setForceRemind(forceRemind: Boolean) {
        if (forceRemind) {
            toDoExtra = getTodoExtra()?.apply {
                forceReminder = true
            }
            toDoExtraStr = toDoExtra.toString()
        }
    }

    fun isAlarmExpired(): Boolean {
        val time = alarmTime
        return time != null && time < System.currentTimeMillis()
    }

    fun contentSame(toDo: TodoItem): Boolean {
        if (this === toDo) return true
        val alarmTime1 = this.alarmTime
        val alarmTime2 = toDo.alarmTime
        val timeSame = if ((alarmTime1 == null || alarmTime1 <= 0) && (alarmTime2 == null || alarmTime2 <= 0)) {
            true
        } else {
            alarmTime1 == alarmTime2
        }
        return (this.localId == toDo.localId)
                && (this.content == toDo.content)
                && timeSame
                && (this.toDoExtraStr == toDo.toDoExtraStr)
    }
}