/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: MainActivity.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/24
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.repo.todo

import android.app.Activity
import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.oplus.note.repo.todo.entity.ToDo
import java.util.*

interface ToDoRepo {
    fun getUndoneTodoCount(): LiveData<Int>?

    fun createTodo(todoItem: TodoItem, result: (id: String) -> Unit)

    fun getUndoneTodoList(dateLiveData: MutableLiveData<Date>): LiveData<List<TodoItem>>?

    fun doneTodo(todoItem: TodoItem, result: (num: Int) -> Unit)

    fun deleteTodo(todoItem: TodoItem, result: (num: Int) -> Unit)

    fun updateTodoColorIndex(todoItem: TodoItem)

    fun updateTodoColorIndexList(todoItem: List<TodoItem>)
    fun checkAllDone(date: Date): Boolean

    fun checkMileStoneCount(): Int

    fun checkLastWeekCount(lastWeekFirst: Date, lastWeekLast: Date): Int

    fun dismissPrivacyPolicyDialog()

    fun checkGrantedPrivacyPolicy(context: Context): Boolean

    fun checkUserPrivacyPolicy(activity: Activity, buttonClickBlock: (agree: Boolean) -> Unit, interceptClickLink: (type: Int) -> Boolean): Boolean


    fun queryTodo(localId: String): LiveData<TodoItem?>

    fun getRepeatHintStr(toDoExtra: ToDoExtra?): String

    fun getTodayDoneList(date: Date, tomorrow: Date): List<TodoItem>?

    fun getAllTodoList(): MutableList<ToDo>?

    fun getAllTodoListInLastMinute(currentTime: Long): MutableList<ToDo>?
}