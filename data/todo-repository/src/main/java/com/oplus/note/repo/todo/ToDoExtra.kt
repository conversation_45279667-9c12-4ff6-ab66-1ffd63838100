package com.oplus.note.repo.todo

import android.os.Parcelable
import com.oplus.note.utils.ExtraJsonHelper
import kotlinx.parcelize.Parcelize

@Parcelize
class ToDoExtra(
    @Transient var extra: String? = null,
    var forceReminder: Boolean? = false,
    var repeatRule: String? = null,
    var sortTime: Long? = 0L
) : Parcelable {
    constructor(todoExtra: ToDoExtra?) : this() {
        if (todoExtra == null) {
            return
        }
        extra = todoExtra.extra
        forceReminder = todoExtra.forceReminder
        repeatRule = todoExtra.repeatRule
        sortTime = todoExtra.sortTime
    }

    companion object {
        fun create(extra: String?): ToDoExtra {
            return if (extra.isNullOrEmpty()) {
                ToDoExtra()
            } else {
                com.oplus.note.utils.ExtraJsonHelper.fromJson(extra ?: "", ToDoExtra::class.java)?.apply {
                    if (extra.isNullOrEmpty()) {
                        this.extra = ""
                    }
                } ?: ToDoExtra()
            }
        }
    }


    override fun toString() = com.oplus.note.utils.ExtraJsonHelper.updateExtraMapJson(extra, this)

    fun updateExtraInfo(updateExtra: ToDoExtra): ToDoExtra {
        return create(ExtraJsonHelper.updateExtraJson(this, updateExtra))
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ToDoExtra

        if (extra != other.extra) return false
        if (forceReminder != other.forceReminder) return false
        if (repeatRule != other.repeatRule) return false
        if (sortTime != other.sortTime) return false

        return true
    }

    override fun hashCode(): Int {
        var result = extra?.hashCode() ?: 0
        result = 31 * result + (forceReminder?.hashCode() ?: 0) + (repeatRule?.hashCode()
                ?: 0) + sortTime.hashCode()
        return result
    }
}

