package com.oplus.note.repo.todo.entity;

import androidx.room.TypeConverter;

import java.util.Date;

public class DateConverters {

    @TypeConverter
    public static Date timestampToDate(Long time) {
        if ((time == null) || (time <= 0)) {
            return null;
        }
        return new Date(time);
    }

    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? 0 : date.getTime();
    }

}
