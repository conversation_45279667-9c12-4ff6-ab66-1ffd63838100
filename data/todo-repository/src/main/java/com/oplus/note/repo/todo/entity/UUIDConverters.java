package com.oplus.note.repo.todo.entity;

import android.text.TextUtils;

import androidx.room.TypeConverter;

import java.util.UUID;

public class UUIDConverters {

    @TypeConverter
    public static UUID stringToUUID(String value) {
        return TextUtils.isEmpty(value) ? null : UUID.fromString(value);
    }

    @TypeConverter
    public static String UUIDToString(UUID uuid) {
        return uuid == null ? null : uuid.toString();
    }

}
