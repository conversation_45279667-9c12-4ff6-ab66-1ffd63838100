/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinParser
 * * Description: SkinParser
 * * Version: 1.0
 * * Date: 2020/5/26
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/26 1.0 build this module
 ****************************************************************/
package com.oplus.note.repo.skin.core

import android.os.Build
import com.google.gson.Gson
import com.oplus.note.repo.skin.bean.Skin
import com.oplus.note.logger.AppLogger
import dalvik.system.ZipPathValidator
import okio.IOException
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

class SkinParser {

    companion object {
        const val TAG = "SkinParser"
    }

    fun readSkin(file: String): String {
        return File(file).readText()
    }

    fun parser(content: String): Skin? {
        return try {
            Gson().fromJson(content, Skin::class.java)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "parser : " + e.message)
            null
        }
    }

    @Throws(IOException::class)
    fun unZipFile(zipPath: String, descDir: String) {
        unZipFile(File(zipPath), descDir)
    }

    /**
     * 解压文件到指定目录
     * 解压后的文件名，和之前一致
     * @param zipFile
     * @param descDir
     */
    @Throws(IOException::class)
    fun unZipFile(zipFile: File, descDir: String) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            ZipPathValidator.clearCallback()
        }
        val zip = ZipFile(zipFile)
        val entries: Enumeration<out ZipEntry?> = zip.entries()
        while (entries.hasMoreElements()) {
            val entry: ZipEntry = entries.nextElement() as ZipEntry
            val zipEntryName: String = entry.name
            val input: InputStream = zip.getInputStream(entry)
            val outPath = descDir + File.separator + zipEntryName
            val file = File(outPath.substring(0, outPath.lastIndexOf('/')))
            if (!file.exists()) {
                file.mkdirs()
            }

            if (File(outPath).isDirectory) {
                continue
            }
            val out = FileOutputStream(outPath)
            val buf1 = ByteArray(1024)
            var len: Int
            while (input.read(buf1).also { len = it } > 0) {
                out.write(buf1, 0, len)
            }
            input.close()
            out.close()
        }
    }
}
