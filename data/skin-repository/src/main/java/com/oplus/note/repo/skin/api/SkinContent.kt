/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : SkinContent.kt
 * Description    : SkinContent.kt
 * Version        : 1.0
 * Date           : 2025/4/10
 * Author         : HeBaoQiang
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * HeBaoqiang     2025/4/10         1.0           create
 */

package com.oplus.note.repo.skin.api

object SkinContent {
        const val SIZE_720 = "720P"
        const val SIZE_1080 = "1080P"
        const val SIZE_2K = "2K"
        const val SIZE_25K = "2.5K"

        const val SP_NAME_MIGRATION = "note_migration"
        const val SP_KEY_PENDING_UPDATE_SKIN = "note_migration_key_update_skin"
        const val SP_KEY_PENDING_DOWNLOAD_SKIN = "note_migration_key_download_skin"

        const val JSON_FILE = "skin.json"
        const val SKIN = "skin"
        const val ZIP_SUFFIX = ".zip"
        const val SKIN_BASE_URL: String = "skin_base_url"
        const val CHECK_SKIN_LIST_INTERVAL_TIME: Long = 60 * 1000L
        const val SKIN_PURE_COLOR_MAX_LENGTH: Int = 9
        const val SKIN_CSS_FONT_SIZE: String = "16"
        const val SKIN_CSS_ALIGN_FROM: String = "left"
        const val SKIN_AID_VERSION: String = "1"
        /**
         *  content padding 边距设置
         */
        const val SIZE_CONTENT_LEFT: String = "24"
        const val SIZE_CONTENT_TOP: String = "0"
        const val SIZE_CONTENT_RIGHT: String = "24"
        const val SIZE_CONTENT_BOTTOM: String = "0"
}