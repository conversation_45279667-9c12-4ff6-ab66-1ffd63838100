/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - SkinDataUtils
 * * Description:
 * * Version: 1.0
 * * Date : 2023/3/9
 * * Author: dengqian03
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * dengqian03 2023/1/31 1.0 create
 ******************************************************************/
package com.oplus.note.repo.skin

object SkinDataUtils {
    //埋点字段使用
    const val ONLINE_SKIN_5 = "img_skin_5"
    const val ONLINE_SKIN_6 = "img_skin_6"
    const val ONLINE_SKIN_7 = "img_skin_7"
    const val ONLINE_SKIN_8 = "img_skin_8"
    const val ONLINE_SKIN_9 = "img_skin_9"
    const val ONLINE_SKIN_10 = "img_skin_10"
    const val ONLINE_SKIN_11 = "img_skin_11"
    const val ONLINE_SKIN_12 = "img_skin_12"

    const val ONLINE_SKIN_5_ID = "5f69905dbc2e4e006f582343"
    const val ONLINE_SKIN_6_ID = "5f3b73ad0629f96b40b5f355" //平铺 + .9
    const val ONLINE_SKIN_7_ID = "5f310338be2ad80067915d2b"
    const val ONLINE_SKIN_8_ID = "5f3126a60629f900669fc118" // 纯色
    const val ONLINE_SKIN_9_ID = "5f31304abe2ad80067915d2c" // 平铺
    const val ONLINE_SKIN_10_ID = "5f313181be2ad80067915d2d"
    const val ONLINE_SKIN_11_ID = "5f3134dcbe2ad80067915d2e"
    const val ONLINE_SKIN_12_ID = "5f313721bc2e4e006672244e"

    @JvmStatic
    fun conversionAidSkinId(id: String): String {
        return when (id) {
            ONLINE_SKIN_5 -> ONLINE_SKIN_5_ID
            ONLINE_SKIN_6 -> ONLINE_SKIN_6_ID
            ONLINE_SKIN_7 -> ONLINE_SKIN_7_ID
            ONLINE_SKIN_8 -> ONLINE_SKIN_8_ID
            ONLINE_SKIN_9 -> ONLINE_SKIN_9_ID
            ONLINE_SKIN_10 -> ONLINE_SKIN_10_ID
            ONLINE_SKIN_11 -> ONLINE_SKIN_11_ID
            ONLINE_SKIN_12 -> ONLINE_SKIN_12_ID
            else -> id
        }
    }
}