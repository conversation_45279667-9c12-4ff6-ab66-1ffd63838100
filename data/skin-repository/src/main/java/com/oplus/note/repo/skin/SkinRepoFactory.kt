/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : SkinRepoFactory.kt
 * Description    : SkinRepoFactory.kt
 * Version        : 1.0
 * Date           : 2025/4/10
 * Author         : He<PERSON><PERSON><PERSON>iang
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * HeBaoqiang     2025/4/10         1.0           create
 */

package com.oplus.note.repo.skin

object SkinRepoFactory {

    private lateinit var skinRepo: SkinRepo

    fun getSkinRepo(): SkinRepo {
        return skinRepo
    }

    fun register(repo: SkinRepo) {
        skinRepo = repo
    }
}