/**
 * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * File: Skin.kt
 * Description: Skin
 * Version: 1.0
 * Date: 2025/04/17
 * Author: W9051314
 *
 * ---------------------- Revision History: -------------------
 * <author>      <date>      <version>       <desc>
 * BaoqiangHe 2025/04/17   1.0 build this module
 */
package com.oplus.note.repo.skin.bean

import androidx.annotation.Keep

@Keep
data class Skin(
    val aid: String = "",
    val card: Card = Card(),
    val editPage: EditPage = EditPage(),
    val resolution: String = "",
    val sharePage: SharePage = SharePage(),
    val versionCode: String = ""
) {
    @Keep
    data class Card(
        val bg: Bg = Bg(),
        val contentColor: CardColor = CardColor(),
        val timeColor: CardColor = CardColor(),
        val titleColor: CardColor = CardColor()
    ) {
        @Keep
        data class Bg(
            val type: String = TYPE_PURE_COLOR,
            val value: String = "#ffffffff"
        ) {
            companion object {
                const val TYPE_PICTURE = "1" //单图
                const val TYPE_PICTURE_TILE = "2" //平铺
                const val TYPE_PURE_COLOR = "3" //纯色
                const val TYPE_GRADIENT_COLOR = "4" //渐变
            }
        }

        @Keep
        data class CardColor(
            val lightMode: String = "", //亮色
            val darkMode: String = "" // 暗色
        )
    }

    @Keep
    data class EditPage(
        val background: Background = Background(), // 背景设置项
        val checkbox: Checkbox = Checkbox(), // 待办项图标
        val content: Content = Content() // 内容设置项
    ) {
        @Keep
        data class Background(
            val toolbarBgColor: EditPageColor = EditPageColor(), // 信息栏上面到通知栏区域的背景色
            val timeInfoBgColor: EditPageColor = EditPageColor(), // 信息栏背景色
            @Deprecated("Please use editorBgColor2 to support light and dark color adaptation")
            val editorBgColor: String = "#FFEBEBEB", //编辑器的背景色，主要是中大屏内部
            val editorBgColor2: EditPageColor = EditPageColor(), // 新版编辑器的背景色，支持亮暗色
            val contentHeadBg: ContentHeadBg = ContentHeadBg(), // 正文的头图，可能没有
            val contentCenterBg: ContentCenterBg = ContentCenterBg(), // 正文的头图，可能没有
            val contentTailBg: ContentTailBg = ContentTailBg() // // 正文的尾图，可能没有
        ) {
            @Keep
            data class ContentHeadBg(
                val type: String = TYPE_PICTURE,
                val img: String = "",
                val color: String = ""
            )

            @Keep
            data class ContentCenterBg(
                val type: String = TYPE_COLOR,
                val color: EditPageColor = EditPageColor(),
                val img: String = ""
            )

            @Keep
            data class ContentTailBg(
                val type: String = TYPE_COLOR,
                val img: String = "",
                val color: String = ""
            )
        }

        @Keep
        data class Checkbox(
            val check: Check = Check(),
            val uncheck: Uncheck = Uncheck()
        ) {
            @Keep
            data class Check(
                val type: String = TYPE_COLOR,
                val value: String = "#4d0000" //"de5bf993-b342-43ab-a090-97dcf3f9ada.png"
            )

            @Keep
            data class Uncheck(
                val type: String = TYPE_COLOR,
                val value: String = "#800000" //"de5bf993-b342-43ab-a090-97dcf3f9ada.png"
            )
        }

        @Keep
        data class Content(
            val textColor: EditPageColor = EditPageColor(), // 信息颜色（包含信息栏、笔记正文等）
            val titleH1Style: ContentStyle = ContentStyle(), // 标题文本样式，可能为空
            val titleH2Style: ContentStyle = ContentStyle(), // 副标题文本样式，可能为空
            val titleH3Style: ContentStyle = ContentStyle(), // 小标题文本样式，可能为空
            val contentStyle: ContentStyle = ContentStyle(), // 正文文本样式，可能为空
            val quoteStyle: ContentStyle = ContentStyle() // 引用样式，可能为空
        ) {
            @Keep
            data class ContentStyle(val associateStyle: String = "")
        }

        @Keep
        data class EditPageColor(
            val lightMode: String = "", //亮色
            val darkMode: String = "" // 暗色
        )

        companion object {
            const val TYPE_PICTURE = "1" // 1：图片
            const val TYPE_COLOR = "2" // 2：颜色
        }
    }

    @Keep
    data class SharePage(
        val background: Background = Background(), // 正文分头和尾两个图片，中间有颜色过渡，信息栏上面的区域还有一个颜色
        val watermark: Watermark = Watermark()
    ) {
        @Keep
        data class Background(val contentBgColor: ShareColor = ShareColor()) // 正文的背景色

        @Keep
        data class Watermark(val textColor: ShareColor = ShareColor()) // 水印文字

        @Keep
        data class ShareColor(
            val lightMode: String = "", //亮色
            val darkMode: String = "" // 暗色
        )
    }
}