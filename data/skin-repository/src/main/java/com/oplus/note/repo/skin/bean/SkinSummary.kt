/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinSummary
 * * Description: SkinSummary
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/25 1.0 build this module
 ****************************************************************/
package com.oplus.note.repo.skin.bean

import android.text.TextUtils
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import com.oplus.note.repo.skin.api.SkinContent

@Entity(tableName = "note_skin", primaryKeys = ["id", "condition"])
data class SkinSummary(
    @ColumnInfo val aid: String = "",
    @ColumnInfo val id: String = "",
    @ColumnInfo val md5: String = "",
    @ColumnInfo val name: String = "",
    @ColumnInfo val preview: String = "",
    @ColumnInfo val thumbnail: String = "",
    @ColumnInfo val url: String = "",
    @ColumnInfo val versionCode: Int = 0,
    @Transient @ColumnInfo var detail: String? = null,
    @Transient @ColumnInfo var condition: String = SkinContent.SIZE_1080,
    @Transient @ColumnInfo val data1: String = "",
    @Transient @ColumnInfo val data2: String = ""
) {

    fun isDownloaded() = !TextUtils.isEmpty(detail)

    @Ignore
    constructor() : this("")
}