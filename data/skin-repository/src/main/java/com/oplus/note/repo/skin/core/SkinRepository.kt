/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinRepository
 * * Description: SkinRepository
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/25 1.0 build this module
 ****************************************************************/
package com.oplus.note.repo.skin.core

import androidx.lifecycle.LiveData
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.nearme.note.util.FileUtil
import com.nearme.note.util.MyAppUtil
import com.oplus.note.repo.note.NoteRepoFactory
import com.oplus.note.repo.skin.SkinRepoFactory
import com.oplus.note.repo.skin.api.SkinContent.SP_KEY_PENDING_DOWNLOAD_SKIN
import com.oplus.note.repo.skin.api.SkinContent.SP_KEY_PENDING_UPDATE_SKIN
import com.oplus.note.repo.skin.api.SkinContent.SP_NAME_MIGRATION
import com.oplus.note.repo.skin.bean.Skin
import com.oplus.note.repo.skin.bean.SkinDao
import com.oplus.note.repo.skin.bean.SkinSummary
import java.lang.reflect.Modifier
import java.lang.reflect.Type

class SkinRepository(private val mSkinDao: SkinDao = SkinRepoFactory.getSkinRepo().getSkinDao()) {

    companion object {
        private const val TAG = "SkinRepository"
        const val SPLIT = "_"
    }

    fun getSkinSummaries(condition: String): LiveData<List<SkinSummary>> {
        return mSkinDao.getSkinSummaries(condition)
    }

    fun getSkin(id: String, condition: String): Skin? {
        AppLogger.BASIC.d(TAG, "getSkin id :$id, condition :$condition")
        val content = mSkinDao.getSkin(id, condition)
        return if (content != null) {
            SkinParser().parser(content)
        } else {
            AppLogger.BASIC.e(TAG, "no match $id in db")
            null
        }
    }

    fun hasSkin(): Boolean {
        return mSkinDao.hasSkin()?.isNotEmpty() ?: false
    }

    fun getSkinSummary(skinId: String, condition: String): SkinSummary? {
        return mSkinDao.getSkinSummarySync(skinId, condition)
    }

    fun getOtherSkinSummariesSync(skin: String, condition: String): List<SkinSummary> {
        return mSkinDao.getOtherSkinSummariesSync(skin, condition)
    }

    fun saveSkin(id: String, condition: String, skin: String) {
        mSkinDao.saveSkin(id, condition, skin)
    }

    fun addSkinList(cloudSkinSummaries: List<SkinSummary>, deviceType: String) {
        AppLogger.CLOUD.i(TAG, " addSkinList=${cloudSkinSummaries.size}, deviceType : $deviceType")

        val device = cloudSkinSummaries[0].condition.split(SPLIT)[0]
        if (device.isEmpty()) {
            return
        }
        val updates = ArrayList<SkinSummary>()
        val willDeleteSkinIdList = ArrayList<SkinSummary>()
        val localSkinSummaries = mSkinDao.getSkinSummariesSync(deviceType)

        cloudSkinSummaries.forEach { cloudSkinSummary ->
            val find = localSkinSummaries.find { cloudSkinSummary.id == it.id && (cloudSkinSummary.condition == it.condition) }
            if (find != null) {
                // 1. 服务端版本号更高，更新
                if (find.versionCode < cloudSkinSummary.versionCode) {
                    updates.add(cloudSkinSummary)
                }
            } else {
                // 2. 服务端存在，本地不存在，新增
                updates.add(cloudSkinSummary)
            }
        }
        localSkinSummaries.forEach { localSkinSummary ->
            val find = cloudSkinSummaries.find { localSkinSummary.id == it.id && (localSkinSummary.condition == it.condition) }
            // 3. 服务端不存在，本地存在，下架
            if (find == null) {
                willDeleteSkinIdList.add(localSkinSummary)
            }
        }
        mSkinDao.insertSkinSummaryList(updates)
        if (willDeleteSkinIdList.isNotEmpty()) {
            deleteStyleSummaryList(willDeleteSkinIdList)
            willDeleteSkinIdList.forEach { item ->
                val skinPath = SkinRepoFactory.getSkinRepo().getSkinPath(item.id, item.condition)
                AppLogger.CLOUD.d(TAG, "delete skin data: $skinPath")
                FileUtil.deleteDirectory(skinPath)
            }
        }
        updateSkinSummaryForMigration()
    }

    private fun updateSkinSummaryForMigration() {
        val gson = GsonBuilder()
            .excludeFieldsWithModifiers(Modifier.STATIC)
            .create()
        val listType: Type = object : TypeToken<List<SkinSummary>>() {}.type

        val sharedPrefs = SharedPreferencesUtil.getInstance()
        val pendingUpdateSkinStr = sharedPrefs.getString(MyAppUtil.getContext(), SP_NAME_MIGRATION, SP_KEY_PENDING_UPDATE_SKIN)
        if (pendingUpdateSkinStr.isNotEmpty()) {
            gson.fromJson<List<SkinSummary>>(pendingUpdateSkinStr, listType).forEach { skin ->
                mSkinDao.saveSkin(skin.id, skin.condition, skin.detail ?: "")
            }
        }

        val pendingDownloadSkinStr = sharedPrefs.getString(MyAppUtil.getContext(), SP_NAME_MIGRATION, SP_KEY_PENDING_DOWNLOAD_SKIN)
        if (pendingDownloadSkinStr.isNotEmpty()) {
            gson.fromJson<List<SkinSummary>>(pendingDownloadSkinStr, listType).forEach { skin ->
                SkinRepoFactory.getSkinRepo().downSkin(skin, null, false)
            }
        }
    }

    fun clean() {
        mSkinDao.deleteAll()
    }

    private fun deleteStyleSummaryList(list: List<SkinSummary>) {
        list.forEach { item ->
            mSkinDao.deleteSkinSummary(item.id, item.condition)
        }
    }
}