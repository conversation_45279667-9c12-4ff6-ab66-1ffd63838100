/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinDao
 * * Description: SkinDao
 * * Version: 1.0
 * * Date: 2020/6/2
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/6/2 1.0 build this module
 ****************************************************************/
package com.oplus.note.repo.skin.bean

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

@Dao
interface SkinDao {

    @Query("SELECT * FROM note_skin")
    fun getAllData(): List<SkinSummary>

    @Query("SELECT * FROM note_skin WHERE condition = :condition")
    fun getSkinSummaries(condition: String): LiveData<List<SkinSummary>>

    @Query("SELECT * FROM note_skin WHERE condition LIKE :device || '%'")
    fun getSkinSummariesSync(device: String): List<SkinSummary>

    @Query("SELECT * FROM note_skin WHERE id = :skinId AND condition != :condition")
    fun getOtherSkinSummariesSync(skinId: String, condition: String): List<SkinSummary>

    @Query("SELECT * FROM note_skin WHERE id = :skinId AND condition = :condition")
    fun getSkinSummarySync(skinId: String, condition: String): SkinSummary?

    @Query("SELECT detail FROM note_skin WHERE id = :id AND condition = :condition")
    fun getSkin(id: String, condition: String): String?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertSkinSummary(skinSummary: SkinSummary)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertSkinSummaryList(list: List<SkinSummary>)

    @Query("DELETE FROM note_skin where id in (:list)")
    fun deleteSkinSummaryList(list: List<String>)

    @Query("DELETE FROM note_skin WHERE id = :id AND condition = :condition")
    fun deleteSkinSummary(id: String, condition: String)

    @Query("UPDATE note_skin SET detail = :detail WHERE id = :id AND condition = :condition")
    fun saveSkin(id: String, condition: String, detail: String)

    @Query("DELETE FROM note_skin")
    fun deleteAll()

    @Query("SELECT aid FROM note_skin LIMIT 1")
    fun hasSkin(): String?
}