/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : SkinRepo.kt
 * Description    : SkinRepo.kt
 * Version        : 1.0
 * Date           : 2025/4/10
 * Author         : HeBaoQiang
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * HeBaoqiang     2025/4/10        1.0           create
 */

package com.oplus.note.repo.skin

import android.content.Context
import com.oplus.note.repo.skin.bean.Skin
import com.oplus.note.repo.skin.bean.SkinDao
import com.oplus.note.repo.skin.bean.SkinSummary
import com.oplus.note.repo.skin.core.SkinRepository
import com.oplus.note.protocol.IHttpTransferListener

interface SkinRepo {
    fun getSkinRepository(): SkinRepository
    fun getSkinDao(): SkinDao
    fun getSkinPath(skinId: String, condition: String): String
    fun downSkin(skinSummary: SkinSummary, listener: IHttpTransferListener<Skin>?, manualDownload: Boolean)
    fun isSupportSkinSettings(): Boolean
    fun isAgreeUserNotice(context: Context): Boolean
    fun isTalkBackAccessibility(context: Context): Boolean
    fun downloadAddSkinList(list: List<SkinSummary>, deviceType: String)
    fun saveSkinJsonDetail(id: String, condition: String, content: String)
    fun getSkinSummary(id: String, condition: String): SkinSummary?
    fun getBaseUrl(): String
}