plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
}

//include generic compile configs
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.repo.note'

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation 'org.jsoup:jsoup:1.16.1' // 这里指定了Jsoup的版本号，你可以根据实际需求更换版本
    implementation project(':common:lib_base')
    implementation project(':common:baseres')
    implementation "androidx.room:room-ktx:$room"
    implementation "com.google.code.gson:gson:${gson}"
    implementation project(':common:logger:logger-api')
}