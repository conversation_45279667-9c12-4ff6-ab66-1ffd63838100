/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperTextViewWrapper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/9/1       1      create
 ***********************************************************************/
package com.oplus.supertext.view.supertext

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.util.AttributeSet
import android.widget.FrameLayout
import com.oplus.supertext.adapter.SuperTextEventAdapterWrapper
import com.oplus.supertext.adapter.SuperTextTouchEventCallbackWrapper
import com.oplus.supertext.core.data.OcrResultWrapProxy
import com.oplus.supertext.core.view.supertext.SuperTextConfig
import com.oplus.supertext.core.view.supertext.SuperTextView
import com.oplus.supertext.core.view.custom.MenuCustomAdapter
import com.oplus.supertext.core.deeplink.menu.LinkPopupListItem
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oplus.supertext.interfaces.PopupItem

class SuperTextViewWrapper @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0, defStyleRes: Int = 0
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {
    companion object {
        const val TAG = "SuperTextViewWrapper"
        const val INSERT_OCR_TEXT = "insert_ocr_text"
        const val OCR_TEXT_SUPER_TEXT_ACTIVITY = "ocr_text_super_text_activity"
        const val ACTION_FINISH_SUPER_TEXT_ACTIVITY = "com.oplus.supertext.ACTION_FINISH_SUPER_TEXT_ACTIVITY"
    }


    var superTextView: SuperTextView

    init {
        superTextView = SuperTextView(context)
        val params = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        addView(superTextView, params)
    }

    fun setDebugMode(isDebug: Boolean) {
        superTextView.setDebugMode(isDebug)
    }
    fun setSuperTextConfig(insertText:String,allSelect:String){
        superTextView.setSuperTextConfig(getSuperConfig(insertText,allSelect))
    }

    private fun getSuperConfig(insertText:String,allSelect:String): SuperTextConfig {
        return SuperTextConfig.Builder()
            .enableClickSelect(true)
            .enableSwipeWithImage(false)
            .enableParagraph(true)
            .enableMultiSelect(false)
            .setEnableDismissWhenInvisible(false)
            .setMenuCustomAdapter(object : MenuCustomAdapter {
                override fun onBuildCustomMenu(
                    menuItems: List<LinkPopupListItem>,
                    showSelectAll: Boolean
                ): ArrayList<LinkPopupListItem> {
                    // 创建自定义菜单，返回 LinkPopupListItem 类型
                    val customMenuItems = ArrayList<LinkPopupListItem>()
                    customMenuItems.add(LinkPopupListItem(null, insertText, PopupItem.CUSTOM))
                    Log.i(TAG, "onBuildCustomMenu showSelectAll $showSelectAll")
                    customMenuItems.add(LinkPopupListItem(null, context.getString(com.oplus.note.baseres.R.string.card_copy), PopupItem.COPY))
                    customMenuItems.add(LinkPopupListItem(null, allSelect, PopupItem.ALL_SELECT))
                    return customMenuItems
                }

                override fun onCustomMenuClick(item: LinkPopupListItem, inputString: String) {
                    Log.i(TAG, "onCustomMenuClick ${item.popupItem} ${inputString.length}")
                    val intent = Intent(ACTION_FINISH_SUPER_TEXT_ACTIVITY)
                    LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
                    if (item.popupItem == PopupItem.CUSTOM) {
                        handleMenuClick(item, inputString)
                    }
                }

                override fun onMenuDismiss(clickItem: LinkPopupListItem?) {
                    superTextView.dismissAllSelect()
                }
            })
            .build()
    }

    private fun handleMenuClick(item: LinkPopupListItem, inputString: String) {
        if (item.popupItem == PopupItem.CUSTOM) {
            Log.d(TAG, "handleMenuClick is custom")
            val intent = Intent(INSERT_OCR_TEXT)
            intent.putExtra(OCR_TEXT_SUPER_TEXT_ACTIVITY, inputString)
            LocalBroadcastManager.getInstance(context)
                .sendBroadcast(intent)
        }
    }

    fun setEnableHighlight(isEnableHighlight: Boolean) {
        superTextView.setEnableHighlight(isEnableHighlight)
    }

    fun setEnableGuide(isEnableGuide: Boolean) {
        superTextView.setEnableGuide(isEnableGuide)
    }

    fun setDataWithMatrix(wrapper: OcrResultWrapProxy, matrix: Matrix?) {
        superTextView.setDataWithMatrix(wrapper.ocrResultWrap.ocrResult!!, matrix)
    }

    fun dismissTextHandler() {
        superTextView.dismissTextHandler()
    }

    fun getSelectedText(): String {
        return superTextView.getSelectedText()
    }

    fun setSuperTextEventListener(wrapper: SuperTextEventAdapterWrapper) {
        superTextView.addSuperTextEventListener(wrapper.superTextEventAdapter)
    }

    fun setSuperTextTouchEventCallback(wrapper: SuperTextTouchEventCallbackWrapper) {
        superTextView.setSuperTextTouchEventCallback(wrapper.touchEventCallback)
    }

    fun dismissAllSelect(dismiss:Boolean){
        superTextView.dismissAllSelect(dismiss)
    }

    fun setLimitTextToolShow(limit: Boolean) {
        superTextView.setLimitTextToolShow(limit)
    }
}