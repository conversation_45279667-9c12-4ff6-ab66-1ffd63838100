/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperTextEventAdapterWrapper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/9/1       1      create
 ***********************************************************************/
package com.oplus.supertext.adapter

open class SuperTextEventAdapterWrapper {

    var superTextEventAdapter: SuperTextEventAdapter

    init {
        superTextEventAdapter = object : SuperTextEventAdapter() {
            override fun onDataLoadCompleted(allText: String?) {
                onDataCompletedCallback(allText)
            }

            override fun onSelectTextChanged(newText: String?) {
                onSelectTextChangedCallback(newText)
            }
        }
    }

    open fun onDataCompletedCallback(allText: String?) {}

    open fun onSelectTextChangedCallback(newText: String?) {}
}