/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperTextTouchEventCallbackWrapper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/9/1       1      create
 ***********************************************************************/
package com.oplus.supertext.adapter

import com.oplus.supertext.interfaces.SuperTextTouchEventCallback

open class SuperTextTouchEventCallbackWrapper {

    var touchEventCallback: SuperTextTouchEventCallback

    init {
        touchEventCallback = object : SuperTextTouchEventCallback {
            override fun onEndSwipe() {

            }

            override fun onStartSwipe() {

            }

            override fun onStartSwipeImage() {
                doNothing()
            }

            override fun onTouchClick(): Boolean {
                return false
            }

            override fun onTouchEventUp() {
                doNothing()
            }

            override fun onTouchedInSideOfSelectedText() {

            }

            override fun onTouchedOutOfText() {
                onTouchedOutOfTextWrapper()
            }

            override fun onTouchedOutSideOfSelectedText() {
                onTouchedOutSideOfSelectedTextWrapper()
            }

            override fun onTouchedText() {

            }

        }
    }
    fun doNothing() {
        // Do nothing
    }

    open fun onTouchedOutOfTextWrapper() {}

    open fun onTouchedOutSideOfSelectedTextWrapper() {}
}