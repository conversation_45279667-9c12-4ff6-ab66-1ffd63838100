/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperTextStaticWrapper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/8/31       1      create
 ***********************************************************************/
package com.oplus.supertext.ostatic

import android.content.Context
import android.graphics.Bitmap
import com.oplus.supertext.core.data.OcrResultWrap
import com.oplus.supertext.core.data.OcrResultWrapProxy

class SuperTextStaticWrapper(context: Context) {

    private var superTextStatic: SuperTextStatic? = null

    init {
        superTextStatic = SuperTextStatic(context)
    }

    fun setDebugMode(debug: Boolean) {
        superTextStatic?.setDebugMode(debug)
    }

    fun setCompressorEnable(compressor: Boolean) {
        superTextStatic?.setCompressorEnable(compressor)
    }

    fun setDumpBitmapAndResultEnable(isEnable: Boolean) {
        superTextStatic?.setDumpBitmapAndResultEnable(isEnable)
    }

    fun ocrBitmap(bitmap: Bitmap, wrapper: StaticOcrCallBackWrapper) {
        superTextStatic?.ocrBitmap(bitmap, wrapper.callback)
    }
    fun ocrBitmapCloud(bitmap: Bitmap, wrapper: StaticOcrCallBackWrapper) {
        superTextStatic?.ocrBitmapCloud(bitmap, wrapper.callback)
    }

    fun release() {
        superTextStatic?.release()
    }

    fun removeBlackRegion(remove: Boolean) {
        superTextStatic?.removeBlackRegion(remove)
    }

    open class StaticOcrCallBackWrapper {

        var callback: SuperTextStatic.StaticOcrCallBack

        init {
            callback = object : SuperTextStatic.StaticOcrCallBack {
                override fun onError(errorCode: Int) {
                    onErrorCallback(errorCode)
                }

                override fun onResult(ocrResultWrap: OcrResultWrap) {
                    OcrResultWrapProxy(ocrResultWrap).apply {
                        onResultCallback(this)
                    }
                }
            }
        }

        open fun onErrorCallback(errorCode: Int) {}

        open fun onResultCallback(proxy: OcrResultWrapProxy) {}
    }
}