/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : VersionUtilsWrapper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/9/1       1      create
 ***********************************************************************/
package com.oplus.supertext.core.util

import android.content.Context
import com.oplus.supertext.core.utils.VersionUtils

object VersionUtilsWrapper {

    fun isSupportSuperText(context: Context): Boolean {
        //isOcrStatic仅相机、扫一扫、扫描输入为false 其他模块isOcrStatic均为true
        return VersionUtils.isSupportSuperText(context, true)
    }
}