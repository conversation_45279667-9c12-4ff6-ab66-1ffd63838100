package com.oplus.cloud.agent.stub

import android.app.Service
import android.content.Intent
import android.os.Bundle
import android.os.IBinder
import android.os.RemoteException
import com.nearme.note.util.AndroidVersionUtils
import com.oplus.cloud.agent.ISyncAgent
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.policy.SyncResult

abstract class SyncRemoteAgent : Service() {

    private lateinit var mSyncAgent: ISyncAgent
    private val mBinder: com.coloros.cloud.ISyncRemoteAgent.Stub = object : com.coloros.cloud.ISyncRemoteAgent.Stub() {
        @Throws(RemoteException::class)
        override fun onAutoSyncEnabled(enable: Boolean) {
            <EMAIL>(enable)
        }

        @Throws(RemoteException::class)
        override fun autoSyncEnabled(enable: Boolean): Boolean {
            return <EMAIL>()
        }

        @Throws(RemoteException::class)
        override fun onPerformSync(extra: Bundle?): Bundle? {
            AppLogger.BASIC.d(TAG, "onPerformSync")
            return <EMAIL>(extra)
        }
    }

    private val mBinderQ: com.heytap.cloud.ISyncRemoteAgent.Stub = object : com.heytap.cloud.ISyncRemoteAgent.Stub() {
        @Throws(RemoteException::class)
        override fun onAutoSyncEnabled(enable: Boolean) {
            <EMAIL>(enable)
        }

        @Throws(RemoteException::class)
        override fun autoSyncEnabled(enable: Boolean): Boolean {
            return <EMAIL>()
        }

        @Throws(RemoteException::class)
        override fun onPerformSync(extra: Bundle?): Bundle? {
            AppLogger.BASIC.d(TAG, "onPerformSync on Q")
            return <EMAIL>(extra)
        }

        @Throws(RemoteException::class)
        override fun onAccountLogin() {
            <EMAIL>()
        }

        @Throws(RemoteException::class)
        override fun onAccountLogout(deleteData: Boolean) {
            //TODO if account logout, and recovery or backup is progressing, how to deal this case?
            AppLogger.BASIC.e(TAG, "onAccountLogout, deleteData = $deleteData")
            <EMAIL>(deleteData)
        }
    }

    override fun onCreate() {
        mSyncAgent = buildSyncAgent()!!
    }

    override fun onBind(arg0: Intent?): IBinder? {
        return if (!AndroidVersionUtils.isHigherAndroidQ()) {
            mBinder
        } else {
            mBinderQ
        }
    }

    private fun onAutoSyncEnabled(enabled: Boolean) {
        if (mSyncAgent != null) {
            mSyncAgent.onAutoSyncEnabled(enabled)
        }
    }

    protected abstract fun onPerformSync(extra: Bundle?): Bundle?

    protected open fun createNullBundle(): Bundle? {
        val nullB = Bundle()
        nullB.putInt(SyncResult.KEY_LOCAL_EXCEPTION, SyncResult.EXCEPTION_NONE)
        nullB.putInt(SyncResult.KEY_NETWORK_EXCEPTION, SyncResult.EXCEPTION_NONE)
        nullB.putInt(SyncResult.KEY_SERVER_RSP_EXCEPTION, SyncResult.EXCEPTION_NONE)
        return nullB
    }

    protected abstract fun autoSyncEnabled(): Boolean

    protected abstract fun buildSyncAgent(): ISyncAgent?

    protected open fun onSyncStart(extra: Bundle?) {}

    protected open fun onSyncEnd(extra: Bundle?, syncResult: SyncResult?) {}

    protected open fun isRunning(): Boolean {
        return false
    }

    protected open fun onAccountLogin() {}

    protected open fun onAccountLogout(deleteData: Boolean) {}

    protected open fun isSyncAllowed(): Boolean {
        return true
    }

    companion object {
        private const val TAG = "SyncRemoteAgent"
    }

}