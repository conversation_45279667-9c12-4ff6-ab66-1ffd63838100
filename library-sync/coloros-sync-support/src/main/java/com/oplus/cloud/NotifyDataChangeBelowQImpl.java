package com.oplus.cloud;

import android.content.Context;
import android.content.Intent;

public class NotifyDataChangeBelowQImpl implements iNotifyDataChangeBelowQ{
    @Override
    public void sendDataChangedBroadcast(Context context) {
        Intent intent = new Intent("com.coloros.cloud.action.DATA_CHANGED");
        intent.putExtra("DATA", "notes");
        intent.putExtra("NEED_RECOVERY", true);
        intent.setPackage("com.coloros.cloud");
        context.sendBroadcast(intent);
    }
}
