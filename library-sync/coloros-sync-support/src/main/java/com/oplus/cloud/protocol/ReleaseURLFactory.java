package com.oplus.cloud.protocol;

import android.content.Context;
import android.text.TextUtils;

import com.oplus.cloud.agent.SyncAgentContants;
import com.oplus.note.logger.AppLogger;
import com.oplus.cloud.policy.SyncRequest;

class ReleaseURLFactory extends AbsReleaseURLFactory implements URLFactory {
    private static final String TAG = "ReleaseURLFactory";
    private static final String HAS_NEW_DATA = "/v1/has-new";

    private static final String TODO_PATH = UrlConstant.TODO_PATH;
    private static final String TODO_BACKUP = "/v1/backup.json";
    private static final String TODO_MANUAL_BACKUP = "/v1/manual_backup.json";
    private static final String TODO_RECOVERY = "/v1/recovery.json";
    private static final String TODO_CONFIRM = "/v1/confirm.json";

    private static final String RICH_NOTE_PATH = UrlConstant.RICH_NOTE_PATH;
    private static final String RICH_NOTE_BACKUP = "/v1/backup.json";
    private static final String RICH_NOTE_MANUAL_BACKUP = "/v1/manual_backup.json";
    private static final String RICH_NOTE_RECOVERY = "/v1/recovery.json";
    private static final String RICH_NOTE_SUFFIX = "?category=2";


    private static final String NOTE_PATH = "hypertext/note";
    private static final String NOTE_SUFFIX = "?category=1";
    private static final String BACKUP = "/v1/backup";
    private static final String RECOVERY = "/v1/recovery";
    private static final String RECOVERY_CONFIRM = "/v1/confirm";
    private static final String MANUAL_BACKUP = "/v1/manual_backup";
    private static final String FILE_UPLOAD = "/file_upload";
    private static final String FILE_DOWNLOAD = "/file_download";
    private static final String AUTHORITY = "/v1/token_get_user.json";
    private static final String CHANGE_QUERY = "/v1/has_new_data";

    private static final String HOST = UrlConstant.HOST;

    @Override
    public String get(int operation, int requestSource, String moduleName, Context context) {
        AppLogger.CLOUD.d(TAG, "operation is " + operation + ", type is " + moduleName);
        String url = HOST;
        if (TextUtils.isEmpty(url)) {
            url = getCloudSyncFeatureUrl(context);
        }
        if (SyncAgentContants.DataType.TODO.equals(moduleName)) {
            return buildToDoUrl(url, operation, requestSource);
        }

        if (SyncAgentContants.DataType.RICH_NOTE.equals(moduleName)) {
            return buildRichNoteUrl(url, operation, requestSource);
        }

        if (SyncAgentContants.DataType.NOTE.equals(moduleName)) {
            return buildNoteUrl(url, operation, requestSource);
        }

        AppLogger.CLOUD.d(TAG, "moduleName is error : " + moduleName);
        return null;
    }

    private String buildToDoUrl(String host, int operation, int requestSource) {
        switch (operation) {
            case ProtocolAdapter.OPERATION_BACKUP:
                if (requestSource == SyncRequest.REQUEST_SOURCE_MANUAL) {
                    return buildUrl(host, TODO_PATH, TODO_MANUAL_BACKUP);
                } else {
                    return buildUrl(host, TODO_PATH, TODO_BACKUP);
                }
            case ProtocolAdapter.OPERATION_RECOVERY:
                return buildUrl(host, TODO_PATH, TODO_RECOVERY);
            case ProtocolAdapter.OPERATION_RECOVERY_CONFIRM:
                return buildUrl(host, TODO_PATH, TODO_CONFIRM);
            case ProtocolAdapter.OPERATION_SERVER_DATA_QUERY:
                return buildUrl(host, UrlConstant.PUB, CHANGE_QUERY);
            default:
                throw new UnsupportedOperationException("no this operation.");
        }
    }

    private String buildRichNoteUrl(String host, int operation, int requestSource) {
        switch (operation) {
            case ProtocolAdapter.OPERATION_BACKUP:
                if (requestSource == SyncRequest.REQUEST_SOURCE_MANUAL) {
                    return buildUrl(host, RICH_NOTE_PATH, RICH_NOTE_MANUAL_BACKUP + RICH_NOTE_SUFFIX);
                } else {
                    return buildUrl(host, RICH_NOTE_PATH, RICH_NOTE_BACKUP + RICH_NOTE_SUFFIX);
                }
            case ProtocolAdapter.OPERATION_RECOVERY:
                return buildUrl(host, RICH_NOTE_PATH, RICH_NOTE_RECOVERY + RICH_NOTE_SUFFIX);
            case ProtocolAdapter.OPERATION_SERVER_DATA_QUERY:
                return buildUrl(host, RICH_NOTE_PATH, HAS_NEW_DATA + RICH_NOTE_SUFFIX);
            default:
                throw new UnsupportedOperationException("no this operation.");
        }
    }

    private String buildNoteUrl(String host, int operation, int requestSource) {
        String path = NOTE_PATH;
        switch (operation) {
            case ProtocolAdapter.OPERATION_BACKUP: {
                return buildUrl(host, path, BACKUP + NOTE_SUFFIX);
            }
            case ProtocolAdapter.OPERATION_RECOVERY: {
                return buildUrl(host, path, RECOVERY + NOTE_SUFFIX);
            }
            case ProtocolAdapter.OPERATION_RECOVERY_CONFIRM: {
                return buildUrl(host, path, RECOVERY_CONFIRM + NOTE_SUFFIX);
            }
            case ProtocolAdapter.OPERATION_FILE_UPLOAD: {
                path = "file";
                return buildUrl(host, path, FILE_UPLOAD);
            }
            case ProtocolAdapter.OPERATION_FILE_DOWNLOAD: {
                path = "file";
                return buildUrl(host, path, FILE_DOWNLOAD);
            }
            case ProtocolAdapter.OPERATION_AUTHORITY: {
                return buildUrl(host, path, AUTHORITY);
            }
            case ProtocolAdapter.OPERATION_SERVER_DATA_QUERY: {
                return buildUrl(host, path, HAS_NEW_DATA + NOTE_SUFFIX);
            }
            default:
                throw new UnsupportedOperationException("no this operation.");
        }
    }

    private static String buildUrl(String host, String path, String param) {
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(path) || TextUtils.isEmpty(param)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(host).append("/").append(path).append(param);

        return sb.toString();
    }
}
