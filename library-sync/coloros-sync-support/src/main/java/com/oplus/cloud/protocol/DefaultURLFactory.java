package com.oplus.cloud.protocol;

import com.oplus.cloudcore.BuildConfig;

public abstract class DefaultURLFactory {

    private static final URLFactory sDebugInstance = new DebugURLFactory();
    private static final URLFactory sReleaseInstance = new ReleaseURLFactory();

    public static URLFactory getInstance() {
        if (BuildConfig.CLOUD_API_ENVIRONMENT_TEST) {
            return sDebugInstance;
        } else {
            return sReleaseInstance;
        }
    }

    private DefaultURLFactory() {
        throw new RuntimeException("Do not initiate instance of this class!");
    }
}
