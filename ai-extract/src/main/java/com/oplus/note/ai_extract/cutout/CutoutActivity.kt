package com.oplus.note.ai_extract.cutout

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.animation.doOnEnd
import androidx.core.content.FileProvider
import androidx.core.graphics.scale
import androidx.lifecycle.lifecycleScope
import com.google.gson.JsonParser
import com.oplus.aiunit.vision.client.MattingClient
import com.oplus.aiunit.vision.result.picture.ContourPointList
import com.oplus.cutoutEffect.Sprite
import com.oplus.cutoutEffect.SpritesTextureData
import com.oplus.cutoutEffect.VFXNative
import com.oplus.cutoutEffect.VFXTextureView
import com.oplus.note.ai_extract.R
import com.oplus.note.ai_extract.manager.AIExtractManager
import com.oplus.note.ai_extract.ui.SuperTextPaint.BitmapBinder
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import kotlin.math.abs
import androidx.core.graphics.drawable.toDrawable

// 使用组合方式替代继承
data class SpriteDataWrapper(
    val spriteData: SpritesTextureData,
    val isMainSprite: Boolean = false,
    val clickArea: Rect = Rect()
)

class CutoutActivity : AppCompatActivity(), VFXTextureView.IZZEngineListener {
    private var currentPopupWindow: PopupWindow? = null

    companion object {
        const val TAG = "CutoutActivity"
        const val CUTOUT_BITMAP = "cutout_bitmap"
        const val CUTOUT_RECT = "cutout_rect"
        const val CUTOUT_OFFSET_Y = "cutout_offset_y"
        const val CUTOUT_OFFSET_X = "cutout_offset_x"
        const val IS_NEED_DENSITY = "is_need_density"
        private const val LNS_BITMAP_THRESHOLD_MIN_WIDTH = 120
        private const val LNS_BITMAP_THRESHOLD_MIN_HEIGHT = 48

        // VFX 动画常量
        private const val BG_FADE_DURATION = 300L
        private const val STRUCTURE_DURATION = 800L
        private const val EDGE_LIGHT_DURATION = 400L
        private const val EXPAND_DURATION = 330L
        private const val BG_ALPHA_MAX = 0.5f
        private const val UNSELECTED_BRIGHTNESS = 0.3f
        private const val EDGE_LIGHT_SPEED = 300f
        private const val ANTS_LINE_SPEED = 50f
    }

    // VFX 相关属性
    private var mVFXView: VFXTextureView? = null
    private var sprites: ArrayList<Sprite>? = null
    private var isVFXInitialized = false
    private var selectedSpriteIndex = 0 // 默认选中第一个
    private val spriteDataList = mutableListOf<SpriteDataWrapper>()

    // 原有属性
    private val mattingClient: MattingClient by lazy {
        MattingClient(this)
    }
    private var contourPointList: ContourPointList? = null
    var beforeRect: Rect = Rect()
    var animalRect: Rect = Rect()
    var mResBitmap: Bitmap? = null
    private var mMattingX = 0f
    private var mMattingY = 0f
    private val cutoutImage by lazy { findViewById<ImageView>(R.id.cutout_image) }
    var density = 1f
    private var imageX = 0
    private var imageY = 0
    private var imageW = 0
    private var imageH = 0
    /*
    * 1. VFX 初始化流程
    在 onCreate 中创建 VFXTextureView 并初始化引擎
    实现 IZZEngineListener 接口等待初始化完成
    在  onInitFinish 回调中设置效果图层区域并创建 Sprites
    2. 多物体支持
    使用 spriteDataList 存储多个抠图数据
    通过 selectedSpriteIndex 管理选中状态
    为选中和未选中物体应用不同的视觉效果
    3. 动画序列
    背景蒙层：0→50% 透明度，300ms，贝塞尔曲线
    结构光动画：所有物体同时执行，800ms
    边界光/蚂蚁线：选中物体显示边界光，未选中显示蚂蚁线
    膨胀动画：仅选中物体执行
    4. 状态管理
    选中物体：原图亮度 + 边界光 + 膨胀动画
    未选中物体：低亮度 + 蚂蚁线
    支持动态切换选中状态
    * */
    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_cutout)

        val bundle: Bundle = intent.extras ?: Bundle()
        val bitmapBinder: BitmapBinder? = bundle.getBinder(CUTOUT_BITMAP) as? BitmapBinder
        val bitmap = bitmapBinder?.getBitMap()
        val nodeRectArg = bundle.getString(CUTOUT_RECT, "")
        val offsetY = bundle.getInt(CUTOUT_OFFSET_Y, 0)
        val offsetX = bundle.getInt(CUTOUT_OFFSET_X, 0)
        parseRectangleJsonSimple(nodeRectArg, offsetY, offsetX)
        val dealBitmap = bitmap?.scale(imageW,imageH)

        // 初始化 VFX
        initVFXView()

        // 设置触摸监听
        mVFXView?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    val clickedIndex = getSpriteIndexAtPosition(event.x, event.y)
                    if (clickedIndex != -1 && clickedIndex != selectedSpriteIndex) {
                        switchSelectedSprite(clickedIndex)
                        true
                    } else {
                        false
                    }
                }
                else -> false
            }
        }

        lifecycleScope.launch {
            withContext(IO) {
                if (dealBitmap != null) {
                    dealBitmap(dealBitmap)
                }
            }
        }
    }
    private fun parseRectangleJsonSimple(jsonString: String, offsetY: Int, offsetX: Int): Pair<Int, Int> {
        try {
            val jsonObject = JsonParser.parseString(jsonString).asJsonObject
            val x = jsonObject.get("x").asDouble
            val y = jsonObject.get("y").asDouble
            val width = jsonObject.get("width").asDouble
            val height = jsonObject.get("height").asDouble
            val bundle: Bundle = intent.extras ?: Bundle()
            val isNeedDensity = bundle.getBoolean(IS_NEED_DENSITY, true)
            // 获取屏幕密度
            density = resources.displayMetrics.density
            if (!isNeedDensity) {
                density = 1F
            }
            // 转换为真实像素值
            val xPx = (x * density).toInt() + abs(offsetX)
            val yPx = (y * density).toInt() + offsetY
            imageX = xPx
            imageY = yPx
            val widthPx = (width * density).toInt()
            val heightPx = (height * density).toInt()
            imageW = widthPx
            imageH = heightPx
            AppLogger.BASIC.d(TAG, "Original values - x: $x, y: $y, width: $width, height: $height")
            AppLogger.BASIC.d(TAG, "Pixel values - x: $xPx, y: $yPx, width: $widthPx, height: $heightPx")
            AppLogger.BASIC.d(TAG, "Screen density: $density  offsetY:$offsetY offsetX:$offsetX")
            setViewBounds(xPx, yPx, widthPx, heightPx)
            return Pair(widthPx, heightPx)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "JSON parsing error: ${e.message}")
            return Pair(0, 0)
        }
    }
    private fun setViewBounds(x: Int, y: Int, width: Int, height: Int) {
        val screenParams = FrameLayout.LayoutParams(width, height)
        screenParams.leftMargin = x
        screenParams.topMargin = y
        cutoutImage.layoutParams = screenParams
        cutoutImage.visibility =View.GONE
    }
    /**
     * 初始化 VFXTextureView
     */
    private fun initVFXView() {
        // 创建 VFXTextureView
        mVFXView = VFXTextureView(this)

        // 初始化引擎
        mVFXView?.init(this.assets, this)

        // 设置效果类型
        mVFXView?.setEffectType(VFXNative.EFFECT_OS)

        // 添加到根布局
        val rootLayout = findViewById<FrameLayout>(R.id.main)
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        rootLayout?.addView(mVFXView, params)
    }

    /**
     * VFX 引擎初始化完成回调
     */
    override fun onInitFinish() {
        AppLogger.BASIC.d(TAG, "VFX Engine initialized")
        isVFXInitialized = true
        // 如果已经有抠图数据，创建 Sprites
        if (spriteDataList.isNotEmpty()) {
            createSprites()
        }
    }

    /**
     * 创建 Sprites
     */
    private fun createSprites() {
        if (!isVFXInitialized || spriteDataList.isEmpty()) {
            AppLogger.BASIC.w(TAG, "VFX not initialized or no sprite data")
            return
        }

        sprites = mVFXView?.createSprite(ArrayList(spriteDataList.map { it.spriteData }))

        if (sprites?.isNotEmpty() == true) {
            setupSpritesProperties()
            startImmersiveEffect()
        }
    }

    /**
     * 设置 Sprites 属性
     */
    private fun setupSpritesProperties() {
        sprites?.forEachIndexed { index, sprite ->
            // 设置轮廓线（如果有的话）
            contourPointList?.contourPointList?.let { contourPoints ->
                val contourArray = IntArray(contourPoints.size * 2)
                val spriteRect = spriteDataList.getOrNull(index)?.spriteData?.mRect ?: Rect()
                contourPoints.forEachIndexed { pointIndex, point ->
                    contourArray[pointIndex * 2] = point.x - spriteRect.width() / 2
                    contourArray[pointIndex * 2 + 1] = point.y - spriteRect.height() / 2
                }
                sprite.setOutLinePosArray(contourArray)
            }

            // 设置膨胀动画范围
            sprite.setExpandAnimationRange(0.5f)

            // 设置速度
            sprite.setAntsLineRunSpeed(ANTS_LINE_SPEED)
            sprite.setEdgeLightRunSpeed(EDGE_LIGHT_SPEED)

            // 根据是否是第一个结果设置初始状态
            if (spriteDataList.getOrNull(index)?.isMainSprite == true) {
                setupSelectedSprite(sprite)
                selectedSpriteIndex = index  // 设置第一个结果为选中状态
            } else {
                setupUnselectedSprite(sprite)
            }
        }
    }

    /**
     * 设置选中物体的属性
     */
    private fun setupSelectedSprite(sprite: Sprite) {
        sprite.setBrightness(1.0f) // 原图亮度
        sprite.setAntsLineEnable(false)

        // 设置结构光中心点（可以根据点击位置动态设置）
        sprite.setCenterX(0.5f)
        sprite.setCenterY(0.5f)

        sprite.updateProperty()
    }

    /**
     * 设置未选中物体的属性
     */
    private fun setupUnselectedSprite(sprite: Sprite) {
        sprite.setBrightness(UNSELECTED_BRIGHTNESS) // 低亮度
        sprite.setEdgeLightEnable(false)
        sprite.updateProperty()
    }

    /**
     * 开始沉浸式特效
     */
    private fun startImmersiveEffect() {
        // 启动渲染
        mVFXView?.vfxEngine?.startAnimator()

        // 展示当前选中sprite的菜单
        showMenuForSelectedSprite()

        // 开始背景蒙层动画
        startBackgroundFadeIn()

        // 开始结构光动画
        startStructureLightAnimation()

        // 开始膨胀动画（选中物体）
        startExpandAnimation()
    }

    private fun showMenuForSelectedSprite() {
        // 关闭之前显示的菜单
        currentPopupWindow?.dismiss()
        currentPopupWindow = null

        // 获取当前选中的sprite数据
        val selectedSpriteData = spriteDataList.getOrNull(selectedSpriteIndex)
        selectedSpriteData?.let { data ->
            // 计算菜单显示位置（在sprite顶部居中）
            val spriteRect = data.spriteData.mRect
            val menuX = spriteRect.centerX()
            val menuY = spriteRect.top - resources.getDimensionPixelSize(R.dimen.menu_margin_top)

            lifecycleScope.launch {
                withContext(Main) {
                    showFloatingMenu(menuX, menuY)
                }
            }
        }
    }

    /**
     * 背景蒙层淡入动画
     */
    private fun startBackgroundFadeIn() {
        val animator = ValueAnimator.ofFloat(0f, BG_ALPHA_MAX)
        animator.duration = BG_FADE_DURATION
        animator.interpolator = PathInterpolator(0.17f, 0.00f, 0.83f, 1.00f)

        animator.addUpdateListener { valueAnimator ->
            val alpha = valueAnimator.animatedValue as Float
            mVFXView?.vfxEngine?.setBGAlpha(alpha)

            // 同时调整未选中物体的亮度
            sprites?.forEachIndexed { index, sprite ->
                if (index != selectedSpriteIndex) {
                    sprite.setBrightness(1 - alpha)
                    sprite.updateProperty()
                }
            }
        }

        animator.start()
    }

    /**
     * 结构光动画
     */
    private fun startStructureLightAnimation() {
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.duration = STRUCTURE_DURATION
        animator.interpolator = PathInterpolator(0.17f, 0.00f, 0.83f, 1.00f)

        animator.addUpdateListener { valueAnimator ->
            val progress = valueAnimator.animatedValue as Float
            // 结构光动画应用于所有Sprite
            sprites?.forEach { sprite ->
                sprite.setScanStructureLight(progress)
                sprite.setScanStructureLightAlpha(progress)
                sprite.updateProperty()
            }
        }

        animator.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                // 结构光动画结束后开始边界光/蚂蚁线动画
                startEdgeLightAnimation()
            }
        })

        animator.start()
    }

    /**
     * 边界光/蚂蚁线动画
     */
    private fun startEdgeLightAnimation() {
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.duration = EDGE_LIGHT_DURATION

        animator.addUpdateListener { valueAnimator ->
            val alpha = valueAnimator.animatedValue as Float
            sprites?.forEachIndexed { index, sprite ->
                if (index == selectedSpriteIndex) {
                    // 选中物体：边界光淡入
                    sprite.setEdgeLightEnable(true)
                    sprite.setEdgeLightAlpha(alpha)
                } else {
                    // 未选中物体：蚂蚁线淡入
                    sprite.setAntsLineEnable(true)
                    sprite.setAntsLineAlpha(alpha)
                }
                sprite.updateProperty()
            }
        }

        animator.start()
    }

    /**
     * 膨胀动画（仅选中物体）
     */
    private fun startExpandAnimation() {
        sprites?.get(selectedSpriteIndex)?.let { sprite ->
            // 膨胀阶段
            val expandAnimator = ValueAnimator.ofFloat(0f, 0.5f)
            expandAnimator.duration = 350L
            expandAnimator.interpolator = PathInterpolator(0.17f, 0.17f, 0.1f, 1f)

            expandAnimator.addUpdateListener { valueAnimator ->
                val progress = valueAnimator.animatedValue as Float
                sprite.setExpandAnimation(progress)
                sprite.updateProperty()
            }

            expandAnimator.doOnEnd {
                // 恢复阶段
                val recoverAnimator = ValueAnimator.ofFloat(0.5f, 0f)
                recoverAnimator.duration = 200L
                recoverAnimator.interpolator = LinearInterpolator()

                recoverAnimator.addUpdateListener { valueAnimator ->
                    val progress = valueAnimator.animatedValue as Float
                    sprite.setExpandAnimation(progress)
                    sprite.updateProperty()
                }
                recoverAnimator.start()
            }

            expandAnimator.start()
        }
    }

    /**
     * 获取点击位置对应的Sprite索引
     */
    private fun getSpriteIndexAtPosition(x: Float, y: Float): Int {
        spriteDataList.forEachIndexed { index, wrapper ->
            if (wrapper.clickArea.contains(x.toInt(), y.toInt())) {
                return index
            }
        }
        return -1
    }

    /**
     * 切换选中的物体
     */
    private fun switchSelectedSprite(newIndex: Int) {
        if (newIndex < 0 || newIndex >= (sprites?.size ?: 0)) return

        val oldIndex = selectedSpriteIndex
        selectedSpriteIndex = newIndex

        // 更新物体状态
        sprites?.get(oldIndex)?.let { setupUnselectedSprite(it) }
        sprites?.get(newIndex)?.let { setupSelectedSprite(it) }

        // 更新菜单位置
        showMenuForSelectedSprite()

        // 重新开始相关动画
        startEdgeLightAnimation()
        startExpandAnimation()
    }

    private fun dealBitmap(bitmap: Bitmap): Bitmap? {
        val results = mattingClient.process(bitmap, options = null)
        if (results.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "results is null")
            return null
        }

        // 处理所有识别结果
        results.forEachIndexed { index, result ->
            val resBitmap = result.bitmap
            contourPointList = result.contourPointList

            // 只保存第一个结果的bitmap用于其他操作
            if (index == 0) {
                mResBitmap = resBitmap
                mMattingX = result.boxInfo.x
                mMattingY = result.boxInfo.y
            }

            // 计算边界框
            val rect = Rect().apply {
                left = imageX + result.boxInfo.x.toInt()
                top = imageY + result.boxInfo.y.toInt()
                right = left + result.boxInfo.w
                bottom = top + result.boxInfo.h
            }

            // 创建点击区域（比实际区域扩大10%）
            val clickArea = Rect(rect).apply {
                val widthPadding = (width() * 0.1f).toInt()
                val heightPadding = (height() * 0.1f).toInt()
                inset(-widthPadding, -heightPadding)
            }

            // 创建 SpriteDataWrapper 并标记是否是第一个结果
            val spriteData = resBitmap?.let { SpritesTextureData(it, rect) }?.let {
                SpriteDataWrapper(
                    it,
                    isMainSprite = index == 0,  // 第一个结果作为主Sprite
                    clickArea = clickArea
                )
            }
            if (spriteData != null) {
                spriteDataList.add(spriteData)
            }
        }

        // 如果 VFX 已初始化，立即创建 Sprites
        if (isVFXInitialized) {
            lifecycleScope.launch {
                withContext(Main) {
                    createSprites()
                }
            }
        }

        AppLogger.BASIC.d(TAG, "contourPointListViewer :${contourPointList?.contourPointList}")
        mattingClient.stop()

        /*lifecycleScope.launch {
            withContext(Main) {
                showFloatingMenu(300, 300)
            }
        }*/
        return mResBitmap
    }

    override fun onDestroy() {
        super.onDestroy()
        mVFXView?.let {
            it.moveZZEngineListener()
            it.exitRenderThread()
        }
    }
    // 在 CutoutActivity 类中添加以下方法
    @SuppressLint("InflateParams")
    private fun showFloatingMenu(x: Int, y: Int) {
        // 创建一个用于显示菜单的视图
        val menuView = LayoutInflater.from(this).inflate(R.layout.menu_layout, null)

        // 创建 PopupWindow
        val popupWindow = PopupWindow(
            menuView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )

        // 保存当前PopupWindow引用
        currentPopupWindow = popupWindow
        // 设置背景和动画
        popupWindow.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        // 设置点击外部区域关闭
        popupWindow.isOutsideTouchable = true
        // 找到菜单中的按钮并设置点击事件
        val copyButton = menuView.findViewById<View>(R.id.btn_copy)
        val insertButton = menuView.findViewById<View>(R.id.btn_insert)
        copyButton.setOnClickListener {
            // 处理复制操作
            Toast.makeText(this, "复制", Toast.LENGTH_SHORT).show()
            popupWindow.dismiss()
            if (mResBitmap != null) {
                val clipboardManager = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                // 创建一个临时文件来存储位图
                val cachePath = File(cacheDir, "images")
                cachePath.mkdirs()
                val file = File(cachePath, "copied_image.png")
                try {
                    val fileOutputStream = FileOutputStream(file)
                    mResBitmap!!.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                    fileOutputStream.close()
                    // 创建 content URI
                    val contentUri = FileProvider.getUriForFile(
                        this,
                        "${packageName}.fileprovider",
                        file
                    )
                    // 创建 ClipData 并设置到剪贴板
                    val clip = ClipData.newUri(contentResolver, "Image", contentUri)
                    clipboardManager.setPrimaryClip(clip)
                    Toast.makeText(this, "图片已复制到剪贴板", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    Toast.makeText(this, "复制失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            } else {
                Toast.makeText(this, "没有可复制的图片", Toast.LENGTH_SHORT).show()
            }
        }

        insertButton.setOnClickListener {
            // 处理插入操作
            Toast.makeText(this, "插入", Toast.LENGTH_SHORT).show()
            mResBitmap?.let {
                // 计算目标宽高（根据密度缩放）
                val targetWidth = (it.width / density).toInt()
                val targetHeight = (it.height / density).toInt()
                // 确保目标宽高至少为1，避免无效尺寸
                val safeWidth = if (targetWidth > 0) targetWidth else 1
                val safeHeight = if (targetHeight > 0) targetHeight else 1
                // 使用系统API进行缩放，保证质量
                val resBitmap = it.scale(safeWidth, safeHeight)
                AIExtractManager.mattingCallback?.insert(resBitmap)
            }
            popupWindow.dismiss()
        }

        // 在指定位置显示菜单
        popupWindow.showAtLocation(window.decorView, Gravity.NO_GRAVITY, x, y)
    }
}