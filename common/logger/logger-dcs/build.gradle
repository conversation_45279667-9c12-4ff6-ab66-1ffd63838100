plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.logger.statistic'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation project(":common:logger:logger-api")
    implementation "androidx.startup:startup-runtime:$startup"

    implementation "com.oplus.statistics:track:$track"
}