<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:autoMirrored="true"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <path
        android:fillColor="?attr/couiColorLabelPrimary"
        android:fillType="evenOdd"
        android:pathData="M3.545,4.616C3.545,4.229 3.858,3.916 4.245,3.916H16.578C16.965,3.916 17.278,4.229 17.278,4.616V8.5C17.278,8.887 16.965,9.2 16.578,9.2C16.192,9.2 15.878,8.887 15.878,8.5V5.316H4.945V16.283C4.945,17.442 4.736,18.301 4.479,18.916H14.078C14.237,18.916 14.685,18.772 15.116,18.283C15.52,17.824 15.878,17.086 15.878,16V15.5C15.878,15.114 16.192,14.8 16.578,14.8C16.965,14.8 17.278,15.114 17.278,15.5V16C17.278,17.415 16.803,18.485 16.166,19.208C15.555,19.902 14.753,20.316 14.078,20.316H3.2C2.903,20.316 2.638,20.128 2.539,19.847C2.443,19.571 2.528,19.265 2.751,19.079C2.753,19.077 2.755,19.075 2.758,19.073C2.77,19.061 2.794,19.037 2.826,18.998C2.891,18.92 2.992,18.783 3.097,18.573C3.306,18.157 3.545,17.432 3.545,16.283V4.616Z" />

    <path
        android:fillColor="?attr/couiColorLabelPrimary"
        android:pathData="M19.343,9L22.2,12M22.2,12L19.343,15M22.2,12H11.7"
        android:strokeWidth="1.4"
        android:strokeColor="?attr/couiColorLabelPrimary"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />

</vector>
