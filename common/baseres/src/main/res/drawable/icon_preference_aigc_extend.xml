<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:autoMirrored="true"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <path
        android:fillColor="?attr/couiColorLabelPrimary"
        android:fillType="evenOdd"
        android:pathData="M17.124,3.768C18.256,3.992 19.141,4.877 19.365,6.01C19.383,6.097 19.355,6.188 19.292,6.251L17.595,7.948C17.139,6.86 16.252,5.999 15.146,5.577L16.882,3.841C16.946,3.778 17.036,3.751 17.124,3.768ZM14.002,6.722L6.735,13.989C5.898,14.826 5.245,15.831 4.82,16.937L3.962,19.171L6.197,18.313C7.303,17.888 8.307,17.236 9.145,16.398L16.477,9.066C16.243,7.842 15.248,6.89 14.002,6.722ZM20.739,5.738C20.405,4.049 19.084,2.729 17.396,2.395C16.85,2.287 16.286,2.458 15.892,2.852L5.745,12.999C4.769,13.975 4.009,15.146 3.513,16.435L2.092,20.135C1.875,20.702 2.431,21.259 2.998,21.041L6.699,19.62C7.988,19.125 9.158,18.365 10.135,17.388L20.282,7.241C20.675,6.848 20.847,6.284 20.739,5.738Z" />

    <path
        android:fillColor="?attr/couiColorLabelPrimary"
        android:pathData="M20.315,17.601H14.615"
        android:strokeWidth="1.4"
        android:strokeColor="?attr/couiColorLabelPrimary"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />

    <path
        android:fillColor="?attr/couiColorLabelPrimary"
        android:pathData="M17.464,20.45V14.75"
        android:strokeWidth="1.4"
        android:strokeColor="?attr/couiColorLabelPrimary"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />

</vector>
