# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-keep class com.oplus.zoomwindow.**{*;}
-dontwarn com.oplus.zoomwindow.**
-keep class com.oplus.note.osdk.proxy.OplusZoomWindowManagerProxy { *; }
-keep class com.oplus.note.osdk.proxy.OplusZoomWindowManagerProxy$ZoomWindowObserver { *; }

-keep class com.oplus.splitscreen.**{*;}
-dontwarn com.oplus.splitscreen.**
-keep class com.oplus.note.osdk.proxy.OplusSplitScreenManagerProxy { *; }
-keep class com.oplus.note.osdk.proxy.OplusSplitScreenManagerProxy$SplitScreenObserver { *; }

-keep class com.oplus.note.osdk.proxy.OplusWindowManagerProxy { *; }

-keep class com.itgsa.opensdk.wm.**{*;}
-dontwarn com.itgsa.opensdk.wm.**
-keep class com.oplus.note.osdk.proxy.MultiWindowTriggerProxy { *; }
-keep class com.oplus.note.osdk.proxy.MultiWindowTriggerProxy$MultiWindowAllowanceObserver { *; }

-keep class com.oplus.note.osdk.proxy.OplusViewSeamlessProxy { *; }
-keep class com.oplus.note.osdk.proxy.OplusViewSeamlessProxy$SeamlessAnimationCallback { *; }