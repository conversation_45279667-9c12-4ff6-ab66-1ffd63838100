plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

//include generic compile configs
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.osdk.proxy'

    defaultConfig {
        consumerProguardFiles 'proguard-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

// NOTE: 在便签架构中作为最底层模块，不能依赖其它任意便签模块
dependencies {
    implementation "androidx.annotation:annotation:$annotation"

    compileOnly "com.oplus.sdk:addon:${prop_addonSdkVersion}@aar"
    implementation "com.oplus.appplatform:sysapi:${prop_SysApiSdkVersion}"
    implementation "com.oplus.support:api-adapter-oplus:$prop_supportSdkVersion"
}