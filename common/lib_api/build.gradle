plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.common'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "com.oplus.coreapp.appfeature:AppFeatureHelper:$app_feature_version"
}