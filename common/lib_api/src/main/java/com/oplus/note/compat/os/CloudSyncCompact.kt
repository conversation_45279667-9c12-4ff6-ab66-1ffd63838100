package com.oplus.note.compat.os

import android.content.Context
import android.util.Log

class CloudSyncCompact {

    companion object {

        private const val TAG = "CloudSyncCompact"

        private val clazzInstance by lazy {
            CloudSyncCompact()
        }

        @JvmStatic
        fun getInstance(): CloudSyncCompact {
            return clazzInstance
        }
    }

    fun isSupportCloudSync(context: Context): Boolean {
        val pm = context.packageManager
        val isSupport = kotlin.runCatching {
            pm.getPackageInfo("com.heytap.cloud", 0)
            return@runCatching true
        }.getOrDefault(false)
        Log.i(TAG, "isSupportCloudSync: $isSupport")
        return isSupport
    }
}