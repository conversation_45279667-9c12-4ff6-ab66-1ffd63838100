/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: Nie<PERSON><EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  NieXiaokang       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.common.utils

import android.content.Context
import android.os.Build
import android.text.TextUtils
import android.util.Log
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils

object AppFeatureHelper {
    //Feature List Start
    private const val FEATURE_CLOUD_SYNC = "com.oplus.note.cloud_url_host"
    private const val FEATURE_SPEECH = "com.oplus.note.speech_url_domain"
    private const val FEATURE_HQ_DOC_SCAN = "com.oplus.feature.hq_doc_scan_enable"
    private const val FEATURE_AI_SPACE = "com.oplus.smartsidebar.space.roulette.support"
    //单词rewrite拼写错误，为了和线上保持一致，不能修改
    private const val FEATURE_AI_SUPPORT = "com.oplus.note.aigc.ai_rewrtie.support"
    // AI 涂鸦功能
    private const val OPLUS_FEATURE_AI_GRAFFITI_SUPPORT = "com.oplus.notes.ai_graffiti_support"
    // 文档缩略图能力
    private const val OPLUS_NOTE_DOC_THUMB = "com.oplus.note.enable_doc_thumb"
    // AI 个人信息保护政策
    private const val OPLUS_FEATURE_AI_PRIVACY_POLICY_SUPPORT = "com.oplus.note.ai_privacy_policy_url_domain"
    // 轻量机型判断,截屏模块OS14及以上生效和维护
    private const val OPLUS_DEVICE_LIGHT = "com.oplus.screenshot.light"
    //Feature List End

    //adapter for R and Q
    private val UPPER_R = Build.VERSION.SDK_INT > Build.VERSION_CODES.Q

    /**
     * featureNameR : feature name define in Android R platform
     * featureNameQ : feature name define in Android Q platform,  null means same with R platform
     */
    @JvmStatic
    fun hasSystemFeature(context: Context, featureNameR: String, featureNameQ: String): Boolean {
        return if (UPPER_R) {
            val cr = context.contentResolver
            AppFeatureProviderUtils.isFeatureSupport(cr, featureNameR)
        } else {
            if (TextUtils.isEmpty(featureNameQ) || (featureNameR == featureNameQ)) {
                context.packageManager.hasSystemFeature(featureNameR)
            } else {
                context.packageManager.hasSystemFeature(featureNameQ)
            }
        }
    }

    @JvmStatic
    fun isCloudSyncFeatureSupport(context: Context): Boolean {
        return hasSystemFeature(context, FEATURE_CLOUD_SYNC, FEATURE_CLOUD_SYNC)
    }

    @JvmStatic
    fun isSpeechFeatureSupport(context: Context): Boolean {
        return hasSystemFeature(context, FEATURE_SPEECH, FEATURE_SPEECH)
    }

    @JvmStatic
    fun isHighQualityDocScanSupport(context: Context): Boolean {
        return hasSystemFeature(context, FEATURE_HQ_DOC_SCAN, FEATURE_HQ_DOC_SCAN)
    }

    @JvmStatic
    fun getCloudSyncFeatureUrl(context: Context): String {
        return AppFeatureProviderUtils.getString(context.contentResolver, FEATURE_CLOUD_SYNC, "")
    }

    @JvmStatic
    fun getSpeechFeatureUrl(context: Context): String {
        return AppFeatureProviderUtils.getString(context.contentResolver, FEATURE_SPEECH, "")
    }

    @JvmStatic
    fun isSupportAISpaceFeature(context: Context): Boolean {
        return hasSystemFeature(context, FEATURE_AI_SPACE, FEATURE_AI_SPACE)
    }

    /**
     * 检查是否显示ai改写保密feature，目前只针对外销配置
     * 未配置feature->true
     */
    @JvmStatic
    fun isSupportAIRewriteFeature(context: Context): Boolean {
        val boolean = AppFeatureProviderUtils.getBoolean(context.contentResolver, FEATURE_AI_SUPPORT, true)
        Log.d("AppFeatureHelper", "isSupportAIRewriteFeature: $boolean")
        return boolean
    }

    /**
     * 是否支持AI 涂鸦功能
     */
    @JvmStatic
    fun isAIGraffitiSupport(context: Context): Boolean {
        val boolean = AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, OPLUS_FEATURE_AI_GRAFFITI_SUPPORT)
        Log.d("AppFeatureHelper", "isAIGraffitiSupport: $boolean")
        return boolean
    }

    /**
     * 是否支持文档缩略图能力
     *
     * @param context
     * @return
     */
    fun isDocThumbnailSupport(context: Context): Boolean {
        val boolean = AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, OPLUS_NOTE_DOC_THUMB)
        Log.d("AppFeatureHelper", "isDocThumbnailSupport: $boolean")
        return boolean
    }

    /**
     * 获取AI个人信息保护政策
     */
    @JvmStatic
    fun getAiPrivacyPolicyFeatureUrl(context: Context): String {
        return AppFeatureProviderUtils.getString(context.contentResolver, OPLUS_FEATURE_AI_PRIVACY_POLICY_SUPPORT, "")
    }

    /**
     * 轻量机型判断,截屏模块OS14及以上生效和维护
     */
    @JvmStatic
    fun isLightDevice(context: Context): Boolean {
        return AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, OPLUS_DEVICE_LIGHT)
    }
}