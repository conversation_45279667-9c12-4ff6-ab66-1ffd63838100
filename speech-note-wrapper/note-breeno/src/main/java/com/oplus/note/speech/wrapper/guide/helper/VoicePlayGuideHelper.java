package com.oplus.note.speech.wrapper.guide.helper;

import android.app.Activity;
import android.widget.FrameLayout;

import com.oplus.note.speech.wrapper.guide.ISpeechPlayGuide;
import com.oplus.note.speech.wrapper.guide.ISpeechPlayGuideCallback;

import org.jetbrains.annotations.NotNull;

public class VoicePlayGuideHelper implements ISpeechPlayGuide {

    private static final String TAG = "VoicePlayGuideHelper";
    private ISpeechPlayGuideCallback mCallback;

    public VoicePlayGuideHelper(Activity mActivity) {
    }

    @Override
    public void initSpeechGuide(@NotNull Activity activity, @NotNull FrameLayout frameLayout, @NotNull ISpeechPlayGuideCallback callback, @NotNull String voiceUri) {
        mCallback = callback;
        if (mCallback != null) {
            mCallback.doAfterPermitted(true, voiceUri);
        }
    }
}