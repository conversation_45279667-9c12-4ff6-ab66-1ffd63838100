/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - DatePredictor
 ** Description:
 **         v1.0:   VoiceInputGuideHelper
 **
 ** Version: 1.0
 ** Date: 2023/9/14
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/9/14   1.0     Java to Kotlin
 ********************************************************************************/
package com.oplus.note.speech.wrapper.guide.helper

import android.app.Activity
import android.view.ViewGroup
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.oplus.note.speech.wrapper.top.guide.ISpeechGuide
import com.oplus.note.speech.wrapper.top.guide.ISpeechGuideCallback

class VoiceInputGuideHelper(val lifecycleOwner: LifecycleOwner?) : ISpeechGuide {
    private var guideCallback: ISpeechGuideCallback? = null
    private var observer: DefaultLifecycleObserver? = null
    override fun initSpeechGuide(
        activity: Activity,
        layout: ViewGroup?,
        callback: ISpeechGuideCallback
    ) {
        guideCallback = callback
        guideCallback?.doAfterPermitted(true)
        observer = object : DefaultLifecycleObserver {
            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                guideCallback = null
                lifecycleOwner?.lifecycle?.removeObserver(this)
            }
        }
        observer?.let {
            lifecycleOwner?.lifecycle?.addObserver(it)
        }
    }

    companion object {
        private const val TAG = "VoiceInputGuideHelper"
    }
}
