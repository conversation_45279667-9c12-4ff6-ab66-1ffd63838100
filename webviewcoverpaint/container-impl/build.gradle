plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file('scripts/common.gradle')
apply from: rootProject.projectDir.path + '/config.gradle'
apply from: rootProject.projectDir.path + "/coui_uikit.gradle"
apply from: 'npm_build.gradle'
android {
    namespace 'com.oplus.notes.webviewcoverpaint.container.impl'
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {

    implementation project(path: ':webviewcoverpaint:container-api')
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "com.google.android.material:material:$material"
    implementation "io.insert-koin:koin-android:$koin_version"
    implementation project(path: ':common:logger:logger-api')
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation "com.oplus.materialcolor:coui-material-color:${prop_COUIMaterialColorVersion}"
    implementation 'androidx.webkit:webkit:1.11.0'
    implementation "com.google.code.gson:gson:${gson}"
    implementation project(path: ':common:lib_base')

    // tbl webview
    implementation "com.oplus.tbl.webview:tbl-webview-sdk:${tbl_webview_sdk_version}"
    implementation project(path: ':common:osdk-proxy')
    implementation project(':data:note-repository')
}