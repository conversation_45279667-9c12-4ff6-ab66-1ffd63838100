tasks.register('npmBuild') {
    doLast {
        def osName = System.properties['os.name']
        println("==== osName:${osName}")
        if (osName.toLowerCase().contains('windows')) {
            def npmCheck = project.exec {
                commandLine 'cmd', '/c', 'npm -v'
                ignoreExitValue = true
            }
            if (npmCheck.exitValue != 0) {
                throw new GradleException('npm 未安装或未设置有效环境变量！！！')
            } else {
                def npmBuild = project.exec {
                    commandLine 'cmd', '/c', 'npm run build'
                    workingDir = file('../web-note')
                    ignoreExitValue = true
                }
                if (npmBuild.exitValue != 0) {
                    throw new GradleException('npm编译失败！！')
                }
            }
        } else {
            println ("====非windows环境=====")
        }
    }
}

tasks.named('preBuild').configure {
    dependsOn tasks.named('npmBuild')
}