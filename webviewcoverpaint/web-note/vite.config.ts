/*
 * @Author: 80230653 <EMAIL>
 * @Date: 2023-04-21 15:59:07
 * @LastEditors: wangweijie <EMAIL>
 * @LastEditTime: 2023-06-15 10:30:02
 * @FilePath: \web-note\vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'node:path'
import UnpluginVueComponents from 'unplugin-vue-components/vite'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import viteCompression from 'vite-plugin-compression'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    UnpluginVueComponents({
      dts: true,
      resolvers: [
        IconsResolver({
          customCollections: ["custom"],
        }),
      ],
    }),
    Icons({
      compiler: "vue3",
      customCollections: {
        // 这里是存放svg图标的文件地址，custom是自定义图标库的名称
        custom: FileSystemIconLoader("./src/assets/icons"),
      },
    }),
    viteCompression({
      deleteOriginFile: true,
    }),
  ],
  base: "./",
  resolve: {
    alias: {
      "@": resolve("src"), // 源码根目录
      "@images": resolve("src/assets/images"), // 图片
      "@styles": resolve("src/assets/styles"), // styles
      "@less": resolve("src/assets/less"), // 预处理器
      "@libs": resolve("src/libs"), // 本地库
      "@plugins": resolve("src/plugins"), // 本地插件
      "@cp": resolve("src/components"), // 公共组件
      "@ts": resolve("src/ts"), // ts目录
      "@views": resolve("src/views"), // 路由组件
      "@mobile-drag-drop": resolve("src/mobile-drag-drop"), // mobile-drag-drop组件
    },
  },
  build: {
    reportCompressedSize: false,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return id
              .toString()
              .split("node_modules/")[1]
              .split("/")[0]
              .toString();
          }
        },
        entryFileNames: `assets/[name].js`,
        chunkFileNames: `assets/[name].js`,
        assetFileNames: (assetInfo) => {
          if (
            assetInfo.name && assetInfo.type === "asset" &&
            /\.(ttf|woff|woff2|eot)$/i.test(assetInfo.name)
          ) {
            return "assets/fonts/[name].[ext]";
          } else {
            return `assets/[name].[ext]`;
          }
        },
      },
      treeshake: true,
    },
  },
});
