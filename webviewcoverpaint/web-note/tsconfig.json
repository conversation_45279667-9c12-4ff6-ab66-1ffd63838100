{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "preserve", "moduleResolution": "node", "experimentalDecorators": true, "isolatedModules": false, "declaration": true, "emitDeclarationOnly": true, "declarationDir": "dist", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "baseUrl": ".", "types": ["vite/client"], "paths": {"@/*": ["src/*"], "@icons/*": ["src/assets/icons/*"], "@cp/*": ["src/components/*"], "@ts/*": ["src/ts/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules"]}