/***********************************************************
 ** Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd.
 ** All rights reserved.
 **
 ** File: - IAIGraffitiPrivacyProxy
 ** Description: IAIGraffitiPrivacyProxy.
 ** Version: 1.0
 ** Date : 2024/9/6
 ** Author: 91001615
 **
 ** ---------------------Revision History: ---------------------
 **  <author>      <data>      <version >     <desc>
 ** ------------------------------------------------------------
 ** 91001615      2024/9/6     1.0         created
 ***************************************************************/
package com.oplus.note.aigraffiti

import android.content.Context

interface IAIGraffitiPrivacyProxy {
    fun startCheckPrivacy(context: Context, block: (isAgree: Boolean) -> Unit)
}