/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: ExportAgentFactory.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/11/01
 * * Author: chenshimin
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.aigraffiti


/**
 * AI 涂鸦访问main层通信代理类
 */
object AIGraffitiProxyFactory {

    private var aiGraffitiProxy: IAIGraffitiPrivacyProxy? = null

    /**
     * 注册涂鸦代理类
     */
    fun register(agent: IAIGraffitiPrivacyProxy) {
        aiGraffitiProxy = agent
    }

    /**
     * 获取代理类
     */
    fun getAgent(): IAIGraffitiPrivacyProxy? {
        return aiGraffitiProxy
    }
}