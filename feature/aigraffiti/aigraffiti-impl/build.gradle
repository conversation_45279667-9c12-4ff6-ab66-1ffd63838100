plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.oplus.note.aigraffiti'
    compileSdkVersion prop_compileSdkVersion
    dataBinding {
        enabled = true
    }
    defaultConfig {
        minSdk 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}
apply from: rootProject.projectDir.path + "/coui_uikit.gradle"
dependencies {
    implementation project(':common:lib_base')
    implementation project(':data:note-repository')
    implementation project(':richtext:core')
    implementation project(':domain:aigc')
    implementation project(':feature:aigraffiti:aigraffiti-api')
    implementation project(":common:logger:logger-api")
    implementation project(':common:logger:logger-dcs')
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    testImplementation 'junit:junit:4.13.2'
    compileOnly "com.oplus.aiunit.open:toolkits:$ai_sdk_version"
    compileOnly "com.oplus.aiunit.open:download:$ai_sdk_version"
    compileOnly "com.oplus.aiunit.open:core:$ai_sdk_version"
    compileOnly "com.oplus.statistics:track:${track}"
    compileOnly "com.airbnb.android:lottie:${lottieview}"
    compileOnly "com.oplus.sdk:addon:${prop_addonSdkVersion}@aar"

    def taskRequests = gradle.startParameter.taskRequests.toString().toLowerCase()
//    def dependency = !taskRequests.contains("realme") && taskRequests.contains("pad") ?
    def dependency = !taskRequests.contains("domestic")?
            "com.oplusos.vfxsdk:doodleengineS:$doodleEngineVersion" :
            "com.oplusos.vfxsdk:doodleengineO:$doodleEngineVersion"
    compileOnly(dependency) {
        exclude group: 'com.coui.support', module: 'coui-support-appcompat'
        exclude group: 'com.oplus.appcompat', module: 'base'
        exclude group: 'com.oplus.appcompat', module: 'toolbar'
        exclude group: 'com.oplus.appcompat', module: 'controls'
        exclude group: 'com.oplus.appcompat', module: 'responsiveui'
        exclude group: 'com.oplus.appcompat', module: 'lists'
        exclude group: 'com.oplus.appcompat', module: 'dialog'
    }

    implementation "com.oplus.vfxsdk:magicediteffect:$eliminateVfxSdkVersion"
    implementation "com.oplus.effectengine:effectengine:$effectEngineVersion"
    implementation "com.oplus.aiunit:meowai:$aiUnitMeowaiVersion"
}
