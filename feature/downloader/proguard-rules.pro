# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# 核心类保留（精确范围）
-keep class com.oplus.note.downloader.** { *; }
# 网络相关保留
-keep class okhttp3.**, com.heytap.nearx.taphttp.** { *; }
-keepclasseswithmembers class * { @okhttp3.* <methods>; }
-keepclassmembers interface okhttp3.Callback { *; }

-keep class com.oplus.note.osdk.proxy.** { *; }
# 工具类保留
-keepclassmembers class com.oplus.note.downloader.util.DeviceUtil {
    public static java.lang.String getDeviceType();
}
-keep class com.oplus.cloud.status.Device { public static java.lang.String getGuid(); }
-keep class com.platform.usercenter.tools.algorithm.MD5Util { *; }