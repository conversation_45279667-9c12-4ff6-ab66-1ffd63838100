plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
}

//include generic compile configs
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.downloader'

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation project(':common:logger:logger-api')
    implementation project(":data:note-repository")
    implementation project(':data:skin-repository')
    implementation project(':data:paragraph-style-repository')
    implementation project(":common:baseres")
    implementation project(':common:lib_base')
    implementation "com.google.code.gson:gson:${gson}"
    implementation "com.heytap.nearx:taphttp:*******"
    implementation project(path: ':common:osdk-proxy')
    implementation "androidx.lifecycle:lifecycle-livedata-core-ktx:$lifecycle_version"
    implementation project(':library-sync:cloud-sync-support')
    implementation "com.google.code.gson:gson:${gson}"
    implementation "com.heytap.nearx:taphttp:*******"
    implementation "com.heytap.accountsdk:UCAccountSDK_Base_heytap:2.3.2.1"
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
}