/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ResponseObject
 * * Description: ResponseObject
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/25 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.protocol

import androidx.annotation.Keep

const val OK = 200

@Keep
data class ResponseObject<T>(
    val code: Int,
    val msg: String = "",
    val traceId: String,
    val data: T?
) {

    fun isOk(): Boolean {
        return code == OK
    }
}