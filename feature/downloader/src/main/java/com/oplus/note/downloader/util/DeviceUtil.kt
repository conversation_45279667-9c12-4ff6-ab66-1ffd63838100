/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: DeviceUtil.kt
 * * Description: DeviceUtil
 * * Version: 1.0
 * * Date : 2025/5/27
 * * Author: 80303972
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  80303972 2020/5/27 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.util

import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import android.view.WindowManager
import com.nearme.note.util.MyAppUtil
import com.oplus.note.osdk.proxy.OplusFeatureConfigManagerProxy

object DeviceUtil {
    const val SIZE_720 = "720P"
    const val SIZE_1080 = "1080P"
    const val SIZE_2K = "2K"
    const val SIZE_25K = "2.5K"
    const val SPLIT_TAG = "_"
    const val CONDITION_SIZE = 2
    const val PIXEL_0 = 0
    const val PIXEL_720 = 720
    const val PIXEL_721 = 721
    const val PIXEL_1080 = 1080
    const val PIXEL_1081 = 1081
    const val PIXEL_1440 = 1440
    const val PIXEL_1441 = 1441
    const val PIXEL_1600 = 1600

    @JvmStatic
    fun getResolutionSize(): String {
        val wm = MyAppUtil.getContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = wm.defaultDisplay
        val metrics = DisplayMetrics()
        display.getRealMetrics(metrics)
        val configuration = MyAppUtil.getContext().resources.configuration
        val pixel = if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            display.mode.physicalHeight
        } else {
            display.mode.physicalWidth
        }
        val resolutionSize = when (pixel) {
            in PIXEL_0..PIXEL_720 -> SIZE_720
            in PIXEL_721..PIXEL_1080 -> SIZE_1080
            in PIXEL_1081..PIXEL_1440 -> SIZE_2K
            in PIXEL_1441..PIXEL_1600 -> SIZE_25K
            else -> SIZE_1080
        }
        return resolutionSize
    }

    @JvmStatic
    fun getDeviceType(): String {
        return if (OplusFeatureConfigManagerProxy.isDevicePad) {
            Constants.SCREEN_TYPE_TABLET
        } else if (OplusFeatureConfigManagerProxy.isDeviceFold) {
            Constants.SCREEN_TYPE_FOLD
        } else {
            Constants.SCREEN_TYPE_PHONE
        }
    }

    @JvmStatic
    fun getCondition(
        deviceType: String = getDeviceType(),
        resolutionSize: String = getResolutionSize()
    ): String {
        return "${deviceType}_$resolutionSize"
    }

    @JvmStatic
    fun getAllResolutionSize(): ArrayList<String> {
        return ArrayList<String>().apply {
            add(SIZE_720)
            add(SIZE_1080)
            add(SIZE_2K)
        }
    }

    /**根据condition获取*/
    @JvmStatic
    fun getDeviceType(condition: String): String {
        var deviceType = Constants.SCREEN_TYPE_PHONE
        val list = condition.split(SPLIT_TAG)
        if (list.size == CONDITION_SIZE) {
            deviceType = list[0]
        }
        return deviceType
    }

    /**根据condition获取*/
    @JvmStatic
    fun getResolutionSize(condition: String): String {
        var resolutionSize = SIZE_1080
        val list = condition.split(SPLIT_TAG)
        if (list.size == CONDITION_SIZE) {
            resolutionSize = list[1]
        }
        return resolutionSize
    }

    /**用于获取当前支持的condition 折叠屏需要支持两套资源*/
    @JvmStatic
    fun getSupportCondition(
        deviceType: String = getDeviceType(),
        resolutionSize: String = getResolutionSize()
    ): ArrayList<String> {
        val list = ArrayList<String>()
        list.add("${deviceType}_$resolutionSize")
        if (deviceType == Constants.SCREEN_TYPE_FOLD) {
            list.add("${Constants.SCREEN_TYPE_PHONE}_$resolutionSize")
        }
        return list
    }

    /**用于获取当前使用的condition 折叠屏需要增加判断*/
    @JvmStatic
    fun getUsingDeviceType(isUnfolding: Boolean): String {
        return if (OplusFeatureConfigManagerProxy.isDevicePad) {
            Constants.SCREEN_TYPE_TABLET
        } else if (OplusFeatureConfigManagerProxy.isDeviceFold) {
            if (isUnfolding) {
                Constants.SCREEN_TYPE_FOLD
            } else {
                Constants.SCREEN_TYPE_PHONE
            }
        } else {
            Constants.SCREEN_TYPE_PHONE
        }
    }

    /**用于获取当前使用的condition 折叠屏需要增加判断*/
    @JvmStatic
    fun getUsingCondition(
        isUnfolding: Boolean = false
    ): String {
        val usingDeviceType = getUsingDeviceType(isUnfolding)
        val resolutionSize = getResolutionSize()
        return "${usingDeviceType}_$resolutionSize"
    }
}