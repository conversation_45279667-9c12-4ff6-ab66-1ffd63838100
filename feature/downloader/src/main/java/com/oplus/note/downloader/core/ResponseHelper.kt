/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: ResponseHelper.kt
 * * Description: ResponseHelper
 * * Version: 1.0
 * * Date : 2025/5/26
 * * Author: 80303972
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  80303972 2020/5/26 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.core

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.note.downloader.api.ParaStyleManager
import com.oplus.note.downloader.protocol.ResponseObject
import com.oplus.note.downloader.protocol.ResponseProgressBody
import com.oplus.note.downloader.protocol.SummaryListResp
import com.oplus.cloud.utils.MD5Utils
import com.oplus.note.downloader.util.DeviceUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.protocol.IHttpTransferListener
import com.oplus.note.repo.paragraph.api.ParaStyleContent
import com.oplus.note.repo.paragraph.bean.ParaStyleSummary
import com.oplus.note.repo.paragraph.core.ParaStyleParser
import com.oplus.note.repo.skin.SkinDataUtils
import com.oplus.note.repo.skin.SkinRepoFactory
import com.oplus.note.repo.skin.api.SkinContent
import com.oplus.note.repo.skin.bean.SkinSummary
import com.oplus.note.repo.skin.core.SkinParser
import com.oplus.note.utils.ZipFileUtils
import okhttp3.Response
import okio.BufferedSink
import okio.BufferedSource
import okio.buffer
import okio.sink
import java.io.File
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.StandardCopyOption

object ResponseHelper {
    private const val SKIN_TAG = "note-skin-v1"
    private const val PARA_STYLE_TAG = "note-paragraph-style-v1"
    const val FONT = "font"
    const val TAG = "ResponseHelper"

    fun dealDownloadListResponse(body: String) {
        kotlin.runCatching {
            val type = object : TypeToken<ResponseObject<SummaryListResp?>?>() {}.type
            val responseObj = Gson().fromJson<ResponseObject<SummaryListResp>>(body, type)
            AppLogger.CLOUD.d(TAG, "downSkinList，responseObj=${Gson().toJson(responseObj)}")
            if (responseObj.isOk()) {
                Downloader.saveBaseUrl(responseObj.data?.fileHost?.manual ?: "")
                val list = mutableListOf<Summary>()
                responseObj.data?.list?.forEach {
                    parseSummaryList(it)
                    list.addAll(it.children)
                }
            }
        }.onFailure { e ->
            AppLogger.CLOUD.i(TAG, "dealDownloadListResponse e = ${e.message}")
        }
    }

    fun dealDownloadSingleResponse(
        response: Response,
        md5: String,
        pair: Pair<String, String>,
        isSkin: Boolean,
        listenerWrapper: IHttpTransferListener<*>,
        filePath: String
    ) {
        val id = pair.first
        val condition = pair.second
        val outFile = File(filePath)
        val outDir = outFile.parent

        /** 检查有效文件是否已存在 */
        if (outFile.exists()) {
            val existingMd5 = MD5Utils.getMD5(outFile)
            if (md5 == existingMd5) {
                /** 有效文件已存在，直接处理（无需重复下载） */
                AppLogger.CLOUD.i(TAG, "Valid file already exists, skipping download")
                processDownloadedFile(id, condition, isSkin, listenerWrapper, outFile, outDir)
                return
            } else {
                /** MD5校验失败，删除无效文件 */
                outFile.delete()
            }
        }

        /** 若无有效文件存在，则继续下载 */
        val source: BufferedSource? =
            ResponseProgressBody(response.body!!, listenerWrapper).source()
        kotlin.runCatching {
            if (!outFile.parentFile.exists()) {
                outFile.parentFile.mkdirs()
            }

            AppLogger.CLOUD.i(TAG, "outDir=$outDir")
            val sink: BufferedSink = outFile.sink().buffer()
            source?.use { src ->
                sink.use { snk ->
                    src.readAll(snk)
                    snk.flush()
                }
            }

            AppLogger.CLOUD.i(TAG, "outFile=${outFile.path}")
            val newMd5 = MD5Utils.getMD5(outFile)
            if (md5 == newMd5) {
                processDownloadedFile(id, condition, isSkin, listenerWrapper, outFile, outDir)
            } else {
                AppLogger.CLOUD.e(TAG, "downParaStyle,md5 check fail $md5   $newMd5")
                listenerWrapper.onFailure(Exception("downParaStyle,md5 check fail"))
                outFile.delete() // Clean up invalid download
            }
        }.onFailure { e ->
            outFile.delete()
            AppLogger.CLOUD.e(TAG, "downParaStyle,onFail ${e.message}")
            listenerWrapper.onFailure(Exception("downParaStyle, onFail"))
        }
    }

    private fun processDownloadedFile(
        id: String,
        condition: String,
        isSkin: Boolean,
        listenerWrapper: IHttpTransferListener<*>,
        outFile: File,
        outDir: String
    ) {
        kotlin.runCatching {
            ZipFileUtils.unZipFile(outFile, outDir)
            val resolutionSize = DeviceUtil.getResolutionSize(condition)
            val deviceType = DeviceUtil.getDeviceType(condition)
            if (!isSkin) {
                copyFont(outDir, deviceType)
            }
            dealZipJson(id, resolutionSize, deviceType, outDir, isSkin, listenerWrapper)
        }.onFailure { exception ->
            AppLogger.CLOUD.e(TAG, "processDownloadedFile fail: ${exception.message}")
        }
    }

    private fun dealZipJson(
        id: String,
        resolutionSize: String,
        deviceType: String,
        outDir: String,
        isSkin: Boolean,
        listenerWrapper: IHttpTransferListener<*>,
    ) {
        DeviceUtil.getAllResolutionSize().forEach { item ->
            if (isSkin) {
                skinDealZipJson(outDir, item, resolutionSize, deviceType, listenerWrapper, id)
            } else {
                parseParaDealZipJson(outDir, item, resolutionSize, deviceType, listenerWrapper, id)
            }
        }
    }

    private fun skinDealZipJson(
        outDir: String,
        item: String,
        resolutionSize: String,
        deviceType: String,
        listenerWrapper: IHttpTransferListener<*>,
        id: String
    ) {
        val content =
            ZipFileUtils.readFileToString(outDir + File.separator + deviceType + File.separator + item + File.separator + SkinContent.JSON_FILE)
        val newCondition = "${deviceType}_$item"
        if (content.isNotEmpty()) {
            SkinRepoFactory.getSkinRepo().saveSkinJsonDetail(id, newCondition, content)
        }
        if (resolutionSize == item) {
            val data = SkinParser().parser(content)
            (listenerWrapper as Downloader.SkinWrapper).onSuccess(data)
        }
    }

    private fun parseParaDealZipJson(
        outDir: String,
        item: String,
        resolutionSize: String,
        deviceType: String,
        listenerWrapper: IHttpTransferListener<*>,
        id: String
    ) {
        val content =
            ZipFileUtils.readFileToString(outDir + File.separator + deviceType + File.separator + item + File.separator + ParaStyleContent.JSON_FILE)
        val newCondition = "${deviceType}_$item"
        if (content.isNotEmpty()) {
            ParaStyleManager.saveParaStyle(id, newCondition, content)
        }
        if (resolutionSize == item) {
            val data = ParaStyleParser.parser(content)
            (listenerWrapper as Downloader.ParaStyleWrapper).onSuccess(data)
        }
    }

    private fun copyFont(outDir: String, deviceType: String) {
        val sourceDirPath =
            outDir + File.separator + deviceType + File.separator + FONT + File.separator
        val destinationDirPath = ParaStyleManager.paraStylePath + FONT + File.separator
        AppLogger.CLOUD.d(TAG, "copyFont $sourceDirPath $destinationDirPath")
        /** 确保目标目录存在 获取源目录和目标目录的 Path 对象 */
        val sourceDir: Path = Paths.get(sourceDirPath)
        val destinationDir: Path = Paths.get(destinationDirPath)

        kotlin.runCatching {
            /**确保目标目录存在*/
            Files.createDirectories(destinationDir)

            /**遍历源目录所有文件（不包含子目录）*/
            Files.newDirectoryStream(sourceDir) { entry -> Files.isRegularFile(entry) }
                .use { stream ->
                    for (sourceFile in stream) {
                        // 构建目标文件的 Path 对象
                        val destinationFile = destinationDir.resolve(sourceFile.fileName)

                        // 使用 NIO 特性跳过已存在文件
                        if (!Files.exists(destinationFile)) {
                            Files.copy(
                                sourceFile,
                                destinationFile,
                                StandardCopyOption.COPY_ATTRIBUTES
                            )
                        }
                    }
                }
        }.onFailure { exception ->
            AppLogger.CLOUD.e(TAG, "copyFont fail: ${exception.message}")
        }
    }

    private fun parseSummaryList(item: SummaryListResp.ListItem) {

        when (item.aid) {
            SKIN_TAG -> parseSkin(item.children)

            PARA_STYLE_TAG -> parseParaStyle(item.children)
        }
    }

    private fun parseSkin(list: List<Summary>) {
        val skinList = mutableListOf<SkinSummary>()
        var deviceType = ""
        list.forEach { item ->
            DeviceUtil.getAllResolutionSize().forEach { size ->
                val summary = parseSkinSummary(item, item.screenType, size)
                skinList.add(summary)
                if (deviceType.isEmpty()) {
                    deviceType = item.screenType
                }
            }
        }
        if (skinList.isEmpty() || deviceType.isEmpty()) {
            AppLogger.CLOUD.i(TAG, "skinList deviceType isEmpty :$deviceType ")
            return
        }
        SkinRepoFactory.getSkinRepo().downloadAddSkinList(skinList, deviceType)
    }

    private fun parseSkinSummary(item: Summary, screenType: String, size: String): SkinSummary {
        return SkinSummary(
            aid = item.aid,
            id = SkinDataUtils.conversionAidSkinId(item.resourceName),
            md5 = item.fileInfo.md5,
            preview = item.preview.path,
            thumbnail = item.thumbnail.path,
            url = item.fileInfo.path,
            versionCode = item.versionCode.toInt(),
            condition = "${screenType}_$size",
            data1 = SkinContent.SKIN_AID_VERSION
        )
    }

    private fun parseParaStyle(list: List<Summary>) {
        val paraStyleList = mutableListOf<ParaStyleSummary>()
        var deviceType = ""
        list.forEach { item ->
            DeviceUtil.getAllResolutionSize().forEach { size ->
                val summary = ParaStyleSummary(
                    aid = item.aid,
                    id = item.resourceName,
                    md5 = item.fileInfo.md5,
                    preview = item.preview.path,
                    thumbnail = item.thumbnail.path,
                    thumbnailBlack = item.thumbnailBlack.path,
                    url = item.fileInfo.path,
                    versionCode = item.versionCode.toInt(),
                    section = item.section,
                    condition = "${item.screenType}_$size"
                )
                paraStyleList.add(summary)
                if (deviceType.isEmpty()) {
                    deviceType = item.screenType
                }
            }
        }
        if (paraStyleList.isEmpty() || deviceType.isEmpty()) {
            AppLogger.CLOUD.i(TAG, "paraStyleList isEmpty; deviceType:$deviceType ")
            return
        }
        ParaStyleManager.addParaStyleList(paraStyleList, deviceType)
    }
}