/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinListReq
 * * Description: SkinListReq
 * * Version: 1.0
 * * Date: 2020/5/25
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * *************/5/25 1.0 build this module
 ****************************************************************/

package com.oplus.note.downloader.protocol

import androidx.annotation.Keep

@Keep
data class SummaryListReq(

    var aids: List<Aid>? = null,
    var bizVersion: Long = -1,
    var mode: Int = 2,  //1正式环境、 2测试环境、
    var devId: String = "", // guid的md5值
    var pattern: String = "auto",
    var condition: Brand = Brand()
) {
    @Keep
    data class Aid(
        var aid: String = "",
        val filter: Filter = Filter(),
    )

    @Keep
    data class Filter(
        val key: List<String> = arrayListOf("screenType"),
        var value: String = "common"
    )

    @Keep
    data class Brand(var brand: String = "OPPO") //品牌
}