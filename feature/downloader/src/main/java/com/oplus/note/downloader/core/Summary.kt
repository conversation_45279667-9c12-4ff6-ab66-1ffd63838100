/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: Summary.kt
 * * Description: Summary
 * * Version: 1.0
 * * Date : 2025/5/26
 * * Author: 80303972
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  80303972 2020/5/26 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.core

import androidx.annotation.Keep

@Keep
data class Summary(
    val id: String = "",
    val aid: String = "",
    val versionCode: Long = 0L,
    val parent: String = "",
    val status: String = "",
    val publishedTime: Long = 0L,
    val resourceName: String = "",
    val screenType: String = "",
    val preview: FileInfo = FileInfo(),
    val thumbnail: FileInfo = FileInfo(),
    val thumbnailBlack: FileInfo = FileInfo(),
    val fileInfo: FileInfo = FileInfo(),
    val section: String = "",
) {
    @Keep
    data class FileInfo(
        val path: String = "",
        val md5: String = "",
        val size: Long = 0L,
    )
}