/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ProgressResponseBody
 * * Description: ProgressResponseBody
 * * Version: 1.0
 * * Date: 2020/5/21
 * * Author: 80242942
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80242942 2020/5/21 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.protocol

import com.oplus.note.logger.AppLogger
import com.oplus.note.protocol.IHttpTransferListener
import okhttp3.MediaType
import okhttp3.ResponseBody
import okio.*

class ResponseProgressBody(val mResponseBody: ResponseBody, val mListener: IHttpTransferListener<*>?) : ResponseBody() {

    companion object {
        const val COUNT_50 = 50
    }
    private var mBufferedSource: BufferedSource? = null

    override fun contentLength(): Long {
        return mResponseBody.contentLength()
    }

    override fun contentType(): MediaType? {
        return mResponseBody.contentType()
    }

    override fun source(): BufferedSource {
        if (mBufferedSource == null) {
            mBufferedSource = source(mResponseBody.source()).buffer()
        }
        return mBufferedSource!!
    }

    override fun close() {
        try {
            mBufferedSource?.close()
        } catch (e: Exception) {
            AppLogger.BASIC.e("ResponseProgressBody close", e.message)
        }
    }

    private fun source(source: Source): Source {
        return object : ForwardingSource(source) {
            var totalRead = 0L
            var lastRead = 0L
            val dial = mResponseBody.contentLength() / COUNT_50

            @Throws(IOException::class)
            override fun read(sink: Buffer, byteCount: Long): Long {
                // 获取已读字节数
                val byteRead: Long = super.read(sink, byteCount)
                // 增加当前读取的字节数，如果读取完成了bytesRead会返回-1
                totalRead += if (byteRead != -1L) byteRead else 0

                val increase = totalRead - lastRead
                if (increase > dial) {
                    lastRead = totalRead
                    // 回调，如果contentLength()不知道长度，会返回-1
                    mListener?.onProgress(totalRead, mResponseBody.contentLength())
                }

                return byteRead
            }
        }
    }
}