/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: HttpHelper.kt
 * * Description: HttpHelper
 * * Version: 1.0
 * * Date: 2025/5/16
 * * Author: 80303972
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80303972 2025/5/16 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.core

import android.content.Context
import com.google.gson.Gson
import com.oplus.note.downloader.protocol.SummaryListReq
import com.oplus.note.downloader.util.Constants
import com.oplus.note.downloader.util.DeviceUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import com.oplus.note.osdk.proxy.OplusSystemPropertiesProxy
import com.platform.usercenter.tools.algorithm.MD5Util
import okhttp3.Callback
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okio.Buffer
import java.nio.charset.Charset
import java.util.concurrent.TimeUnit

class HttpHelper private constructor() {

    companion object {
        const val TAG = "HttpHelper"
        private const val MEDIATYPE_JSON = "application/json; charset=utf-8"
        private const val ACTION_DOWNLOAD_SKIN_COMPLETE =
            "oplus.intent.action.DOWNLOAD_SKIN_COMPLETE"
        private const val CONTENT_TYPE_KEY = "Content-Type"
        private const val CONTENT_TYPE_VALUE = "application/json"
        private const val LANGUAGE_KEY = "language"
        private const val LANGUAGE_VALUE = "zh-CN"
        private const val OTA_VERSION_KEY = "otaVersion"
        private const val OS_VERSION_KEY = "osVersion"
        private const val ANDROID_VERSION_KEY = "androidVersion"
        private const val MODEL_KEY = "model"
        private const val INF_VERSION_KEY = "infVersion"
        private const val MODE_KEY = "mode"
        private const val MODEL_RELEASE = 1
        private const val MODEL_TEST = 2

        private const val AID_SKIN = "note-skin-v1"
        private const val AID_PARA_STYLE = "note-paragraph-style-v1"
        private const val INF_VERSION_VALUE = "2"
        private const val MODE_VALUE = "manual"
        private const val OS = "ColorOS"
        private const val ANDROID = "Android"
        private const val TIME_30 = 30L
        val instance: HttpHelper by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            HttpHelper()
        }
    }

    private var md5 = "" // 这个是设置guid 的md5值
    private var listReqHeaderMap: HashMap<String, String> = hashMapOf()
    private val mClient by lazy {
        OkHttpClient.Builder().addInterceptor(Interceptor { chain ->
            val request = chain.request()
            printRequest(request)
            chain.proceed(request)
        }).connectTimeout(TIME_30, TimeUnit.SECONDS)
            .readTimeout(TIME_30, TimeUnit.SECONDS)
            .writeTimeout(TIME_30, TimeUnit.SECONDS)
            .build()
    }

    private fun printRequest(request: Request) {
        var msg =
            "Request\nurl=${request.url}\nmethod=${request.method}\nheaders=${request.headers}body="
        val buffer = Buffer()
        request.body?.let {
            it.writeTo(buffer)
            msg += buffer.readString(Charset.forName("UTF-8"))
        }
    }

    fun downloadWithRequest(request: Request, callback: Callback) {
        mClient.newCall(request).enqueue(callback)
    }

    fun downloadWithUrl(url: String, callback: Callback) {
        try {
            val request = Request.Builder()
                .get()
                .url(url)
                .build()
            downloadWithRequest(request, callback)
        } catch (e: java.lang.Exception) {
            AppLogger.CLOUD.e(TAG, "downSkin e = $e")
            return
        }
    }

    fun downloadSkinAndParaStyleList(url: String, context: Context, callback: Callback) {
        kotlin.runCatching {
            val req = SummaryListReq()
            req.aids = getDownloadAids().toList()
            if (md5.isEmpty()) {
                md5 = MD5Util.md5Hex(com.oplus.cloud.status.Device.getGuid()) ?: ""
            }
            AppLogger.CLOUD.d(TAG, "downloadSkinAndParaStyleList md5 = $md5")
            if (md5.isEmpty()) {
                throw IllegalArgumentException("md5 isEmpty")
            }
            req.devId = md5

            val builder = Request.Builder()
            if (listReqHeaderMap.isEmpty()) {
                initListReqHeader()
            }
            for ((key, value) in listReqHeaderMap) {
                builder.addHeader(key, value)
            }
            builder.post(Gson().toJson(req).toRequestBody(MEDIATYPE_JSON.toMediaTypeOrNull()))
                .url(url)
            downloadWithRequest(builder.build(), callback)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "downSkinList error: $it")
        }
    }

    private fun initListReqHeader() {
        val osVersion = OplusBuildProxy.getOsVersion("")
        if (osVersion.startsWith("V", true)) {
            listReqHeaderMap[OS_VERSION_KEY] = osVersion.replace("V", OS, true)
        } else {
            listReqHeaderMap[OS_VERSION_KEY] = OS + osVersion
        }
        listReqHeaderMap[CONTENT_TYPE_KEY] = CONTENT_TYPE_VALUE
        listReqHeaderMap[LANGUAGE_KEY] = LANGUAGE_VALUE
        listReqHeaderMap[MODEL_KEY] = OplusSystemPropertiesProxy.model
        listReqHeaderMap[ANDROID_VERSION_KEY] =
            ANDROID + OplusSystemPropertiesProxy.androidVersion
        listReqHeaderMap[OTA_VERSION_KEY] = OplusSystemPropertiesProxy.otaVersion
        /**测试debug使用 ,正式灰度的时候需要注释掉*/
        //listReqHeaderMap["t-debug-mode"] = "1"
        listReqHeaderMap[INF_VERSION_KEY] = INF_VERSION_VALUE
    }

    fun getDownloadAids(): ArrayList<SummaryListReq.Aid> {
        val list = ArrayList<SummaryListReq.Aid>()

        val device = DeviceUtil.getDeviceType()
        when (device) {
            Constants.SCREEN_TYPE_TABLET -> {
                val paraStyleTableAid = SummaryListReq.Aid(aid = AID_PARA_STYLE)
                    .apply { filter.value = Constants.SCREEN_TYPE_TABLET }
                list.add(paraStyleTableAid)
            }

            Constants.SCREEN_TYPE_PHONE -> {
                val skinPhoneAid = SummaryListReq.Aid(aid = AID_SKIN)
                    .apply { filter.value = Constants.SCREEN_TYPE_PHONE }
                list.add(skinPhoneAid)
                val paraStylePhoneAid = SummaryListReq.Aid(aid = AID_PARA_STYLE)
                    .apply { filter.value = Constants.SCREEN_TYPE_PHONE }
                list.add(paraStylePhoneAid)
            }

            Constants.SCREEN_TYPE_FOLD -> {
                val skinPhoneAid = SummaryListReq.Aid(aid = AID_SKIN)
                    .apply { filter.value = Constants.SCREEN_TYPE_PHONE }
                list.add(skinPhoneAid)
                val skinFoldAid = SummaryListReq.Aid(aid = AID_SKIN)
                    .apply { filter.value = Constants.SCREEN_TYPE_FOLD }
                list.add(skinFoldAid)
                val paraStylePhoneAid = SummaryListReq.Aid(aid = AID_PARA_STYLE)
                    .apply { filter.value = Constants.SCREEN_TYPE_PHONE }
                list.add(paraStylePhoneAid)
                val paraStyleFoldAid = SummaryListReq.Aid(aid = AID_PARA_STYLE)
                    .apply { filter.value = Constants.SCREEN_TYPE_FOLD }
                list.add(paraStyleFoldAid)
            }
        }

        return list
    }
}