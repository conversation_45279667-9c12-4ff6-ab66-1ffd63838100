/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: Downloader.kt
 * * Description: Downloader
 * * Version: 1.0
 * * Date: 2025/5/25
 * * Author: 80303972
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80303972 2020/5/25 1.0 build this module
 ****************************************************************/
package com.oplus.note.downloader.core

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.nearme.note.util.AppExecutors
import com.oplus.note.repo.skin.bean.Skin
import com.oplus.note.repo.skin.bean.SkinSummary
import com.oplus.note.protocol.IHttpTransferListener
import com.nearme.note.util.FileUtil
import com.nearme.note.util.MyAppUtil
import com.oplus.cloud.utils.PrefUtils
import com.oplus.note.downloader.api.ParaStyleManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.paragraph.bean.ParaStyle
import com.oplus.note.repo.paragraph.bean.ParaStyleSummary
import com.oplus.note.repo.paragraph.core.ParaStyleParser
import com.oplus.note.repo.skin.SkinRepoFactory
import com.oplus.note.repo.skin.core.SkinParser
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import java.io.FileNotFoundException
import java.io.IOException
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.PriorityBlockingQueue

class Downloader private constructor(val mContext: Context) {

    companion object {
        const val TAG = "Downloader"
        const val PARA_STYLE_THREAD_TAG = "ParaStyleDownloader"
        const val SKIN_THREAD_TAG = "SkinDownloader"
        const val COUNT_13 = 13
        const val COUNT_6 = 6
        const val COUNT_100 = 100

        private const val ACTION_DOWNLOAD_SKIN_COMPLETE =
            "oplus.intent.action.DOWNLOAD_SKIN_COMPLETE"

        @SuppressLint("StaticFieldLeak")
        private var instance: Downloader? = null
        private var baseUrl: String? = null
        private const val BASE_URL: String = "base_url"
        private const val DOWNLOAD_LIST_PATH: String = "/enum/v1/child"

        private val skinDownloadExecutor by lazy {
            val comparator =
                Comparator<Pair<DownloadPriority, () -> Unit>> { p1, p2 ->
                    p2.first.compareTo(p1.first)  // 高优先级优先
                }
            val queue = PriorityBlockingQueue(COUNT_6, comparator)

            val executor = Executors.newSingleThreadExecutor { r ->
                Thread(r, SKIN_THREAD_TAG).apply { isDaemon = true }
            }.also { executor ->
                executor.execute {
                    while (true) {
                        kotlin.runCatching {
                            queue.take().second.invoke()
                        }.onFailure { e ->
                            AppLogger.BASIC.e(TAG, "Priority executor Exception: ${e.message}")
                        }
                    }
                }
            }
            PriorityExecutor(executor, queue)
        }

        private val paraDownloadExecutor by lazy {
            val comparator =
                Comparator<Pair<DownloadPriority, () -> Unit>> { p1, p2 ->
                    p2.first.compareTo(p1.first)  // 高优先级优先
                }
            val queue = PriorityBlockingQueue(COUNT_13, comparator)

            val executor = Executors.newSingleThreadExecutor { r ->
                Thread(r, PARA_STYLE_THREAD_TAG).apply { isDaemon = true }
            }.also { executor ->
                executor.execute {
                    while (true) {
                        try {
                            queue.take().second.invoke()
                        } catch (e: FileNotFoundException) {
                            // 处理文件未找到的异常
                            AppLogger.BASIC.e(TAG, "Priority FileNotFoundException: ${e.message}")
                        } catch (e: IOException) {
                            // 处理其他I/O相关的异常
                            AppLogger.BASIC.e(TAG, "Priority executor IOException: ${e.message}")
                        } catch (e: Exception) {
                            // 处理其他未预期的异常
                            AppLogger.BASIC.e(TAG, "Priority executor Exception: ${e.message}")
                        }
                    }
                }
            }
            PriorityExecutor(executor, queue)
        }

        fun getInstance(context: Context): Downloader {
            return instance ?: synchronized(this) {
                instance ?: Downloader(context.applicationContext).also {
                    instance = it
                }
            }
        }

        fun getBaseUrl(): String {
            return baseUrl ?: PrefUtils.getString(MyAppUtil.getContext(), BASE_URL, "")
        }

        fun saveBaseUrl(url: String) {
            baseUrl = url
            PrefUtils.putString(MyAppUtil.getContext(), BASE_URL, url)
        }
    }

    private val manualDownloadSet = mutableSetOf<String>()
    private val skinParser: SkinParser = SkinParser()
    private val skinDownloadingMap = ConcurrentHashMap<SkinSummary, SkinWrapper>()
    private val skinDownloadingItems = HashMap<SkinSummary, Int>()

    private val paraDownloadingMap = ConcurrentHashMap<ParaStyleSummary, ParaStyleWrapper>()
    //用于存储手动下载的任务（listener不为空）
    private val paraDownloadingItems = HashMap<ParaStyleSummary, Int>()

    fun getSkinDownloadingItems(): HashMap<SkinSummary, Int> {
        return skinDownloadingItems
    }

    fun getParaStyleDownloadingItems(): HashMap<ParaStyleSummary, Int> {
        return paraDownloadingItems
    }

    fun reattachSkinDownloading(skinSummary: SkinSummary, listener: IHttpTransferListener<Skin>?) {
        skinDownloadingMap[skinSummary]?.apply {
            updateReference(listener, skinSummary)
        }
    }

    fun reattachParaDownloading(
        paraStyleSummary: ParaStyleSummary,
        listener: IHttpTransferListener<ParaStyle>?
    ) {
        paraDownloadingMap[paraStyleSummary]?.apply {
            updateReference(listener, paraStyleSummary)
        }
    }

    fun getSkinDownloadingList(): Set<SkinSummary> {
        return skinDownloadingMap.keys.toSet()
    }

    fun getParaStyleDownloadingList(): Set<ParaStyleSummary> {
        return paraDownloadingMap.keys.toSet()
    }

    fun getManualDownloadSet(): MutableSet<String> {
        return manualDownloadSet
    }

    fun downSummaryList(url: String) {
        val requestUrl = url + DOWNLOAD_LIST_PATH
        kotlin.runCatching {
            HttpHelper.instance.downloadSkinAndParaStyleList(
                requestUrl,
                mContext,
                object : Callback {
                    override fun onResponse(call: Call, response: Response) {
                        val code = response.code
                        val body = response.body?.string()
                        AppLogger.BASIC.i(TAG, "downSkinList code = $code onResponse= $body")
                        if (response.isSuccessful && !body.isNullOrBlank()) {
                            ResponseHelper.dealDownloadListResponse(body)
                        }

                        AppExecutors.getInstance().executeCommandInDiskIO {
                            ParaStyleManager.downloadRemainingParaStyle()
                        }
                    }

                    override fun onFailure(call: Call, e: IOException) {
                        AppLogger.BASIC.i(TAG, "downSkinList onFailure= ${e.message}")
                    }
                })
        }.onFailure {
            AppLogger.BASIC.e(TAG, "downSkinList error: $it")
        }
    }

    fun downSkin(
        skinSummary: SkinSummary,
        filePath: String,
        listener: IHttpTransferListener<Skin>?,
        manualDownload: Boolean
    ) {
        AppLogger.BASIC.d(TAG, "downSkin skinSummary id :${skinSummary.id},  condition :${skinSummary.condition}")
        if (skinDownloadingMap.containsKey(skinSummary)) {
            listener?.let {
                skinDownloadingMap[skinSummary]?.updateReference(it, skinSummary)
            }
            return
        }

        if (manualDownload) {
            manualDownloadSet.add(skinSummary.id)
        }

        val priority = if (manualDownload) {
            DownloadPriority.HIGH
        } else {
            DownloadPriority.LOW
        }

        enqueueSkinDownload(priority) {
            val latch = java.util.concurrent.CountDownLatch(1)
            var callbackCompleted = false

            //下载的时候校验需不需要下载
            SkinRepoFactory.getSkinRepo()
                .getSkinSummary(skinSummary.id, skinSummary.condition)?.detail?.let {
                listener?.onSuccess(skinParser.parser(it))
                return@enqueueSkinDownload
            }

            val listenerWrapper = SkinWrapper(listener, skinSummary)
            val skinUrl = getBaseUrl() + skinSummary.url
            AppLogger.BASIC.d(TAG, "downSkin skinUrl=$skinUrl")
            //add skinSummary to skinDownloadingMap before request
            skinDownloadingMap[skinSummary] = listenerWrapper

            HttpHelper.instance.downloadWithUrl(skinUrl, object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    try {
                        AppLogger.BASIC.i(
                            TAG,
                            "downSkin response code = ${response.code},file=$filePath"
                        )
                        if (response.body != null) {
                            ResponseHelper.dealDownloadSingleResponse(
                                response,
                                skinSummary.md5,
                                Pair(skinSummary.id, skinSummary.condition),
                                true,
                                listenerWrapper,
                                filePath
                            )
                        } else {
                            listenerWrapper.onFailure(Exception("response body is null"))
                        }
                    } catch (e: Exception) {
                        AppLogger.BASIC.i(TAG, "downSkin e = ${e.message}")
                        listenerWrapper.onFailure(e)
                    } finally {
                        callbackCompleted = true
                        skinDownloadingMap.remove(skinSummary)
                        manualDownloadSet.remove(skinSummary.id)
                        FileUtil.deleteFile(filePath)
                        latch.countDown()
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    callbackCompleted = true
                    skinDownloadingMap.remove(skinSummary)
                    manualDownloadSet.remove(skinSummary.id)
                    FileUtil.deleteFile(filePath)
                    listener?.onFailure(e)
                    AppLogger.BASIC.i(TAG, "downSkin onFailure = ${e.message}")
                    latch.countDown()
                }
            })

            // 等待回调完成才继续下一个任务
            if (!callbackCompleted) {
                latch.await()
            }
        }
    }

    private fun enqueueSkinDownload(
        priority: DownloadPriority,
        task: () -> Unit
    ) {
        skinDownloadExecutor.queue.put(Pair(priority, task))
    }

    private fun enqueueDownload(
        summary: ParaStyleSummary,
        priority: DownloadPriority,
        task: () -> Unit
    ) {
        paraDownloadExecutor.queue.put(Pair(priority, task))
    }

    fun downParaStyle(
        paraStyleSummary: ParaStyleSummary,
        filePath: String,
        listener: IHttpTransferListener<ParaStyle>?,
        manualDownload: Boolean
    ) {
        if (paraDownloadingMap.containsKey(paraStyleSummary)) {
            listener?.let {
                paraDownloadingMap[paraStyleSummary]?.updateReference(it, paraStyleSummary)
            }
            return
        }

        if (manualDownload) {
            manualDownloadSet.add(paraStyleSummary.id)
        }
        val priority = if (manualDownload) {
            DownloadPriority.HIGH
        } else {
            DownloadPriority.LOW
        }

        enqueueDownload(paraStyleSummary, priority) {
            val latch = java.util.concurrent.CountDownLatch(1)
            var callbackCompleted = false

            //下载的时候校验需不需要下载
            ParaStyleManager.getParaStyleSummary(paraStyleSummary.id, paraStyleSummary.condition)?.detail?.let {
                ParaStyleParser.parser(it)
                listener?.onSuccess(ParaStyleParser.parser(it))
                return@enqueueDownload
            }

            val listenerWrapper = ParaStyleWrapper(listener, paraStyleSummary)
            val paraStyleUrl = getBaseUrl() + paraStyleSummary.url
            AppLogger.BASIC.d(TAG, "downParaStyle ${paraStyleSummary.id}  $priority")
            paraDownloadingMap[paraStyleSummary] = listenerWrapper

            HttpHelper.instance.downloadWithUrl(paraStyleUrl, object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    try {
                        AppLogger.BASIC.i(
                            TAG,
                            "downParaStyle response code = ${response.code},file=$filePath"
                        )
                        if (response.body != null) {
                            ResponseHelper.dealDownloadSingleResponse(
                                response,
                                paraStyleSummary.md5,
                                Pair(paraStyleSummary.id, paraStyleSummary.condition),
                                false,
                                listenerWrapper,
                                filePath
                            )
                        } else {
                            listenerWrapper.onFailure(Exception("response body is null"))
                        }
                    } catch (e: Exception) {
                        AppLogger.BASIC.i(TAG, "downParaStyle e = ${e.message}")
                        listenerWrapper.onFailure(e)
                    } finally {
                        callbackCompleted = true
                        paraDownloadingMap.remove(paraStyleSummary)
                        manualDownloadSet.remove(paraStyleSummary.id)
                        FileUtil.deleteFile(filePath)
                        latch.countDown()
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    callbackCompleted = true
                    paraDownloadingMap.remove(paraStyleSummary)
                    manualDownloadSet.remove(paraStyleSummary.id)
                    FileUtil.deleteFile(filePath)
                    listener?.onFailure(e)
                    AppLogger.BASIC.i(TAG, "downParaStyle onFailure = ${e.message}")
                    latch.countDown()
                }
            })

            // 等待回调完成才继续下一个任务
            if (!callbackCompleted) {
                latch.await()
            }
        }
    }

    inner class SkinWrapper(listener: IHttpTransferListener<Skin>?, skinSummary: SkinSummary) :
        IHttpTransferListener<Skin> {
        private var mRef = WeakReference(listener)
        private var mSkinSummary = skinSummary
        fun updateReference(listener: IHttpTransferListener<Skin>?, skinSummary: SkinSummary) {
            mRef = WeakReference(listener)
            mSkinSummary = skinSummary
        }

        override fun onProgress(currentLength: Long, totalLength: Long) {
            mRef.get()?.onProgress(currentLength, totalLength)
            skinDownloadingItems[mSkinSummary] = (currentLength * COUNT_100 / totalLength).toInt()
        }

        override fun onSuccess(response: Skin?) {
            mRef.get()?.onSuccess(response)
            skinDownloadingItems.remove(mSkinSummary)
            val intent = Intent(ACTION_DOWNLOAD_SKIN_COMPLETE)
            LocalBroadcastManager.getInstance(MyAppUtil.getContext()).sendBroadcast(intent)
        }

        override fun onFailure(exception: Exception?) {
            mRef.get()?.onFailure(exception)
            skinDownloadingItems.remove(mSkinSummary)
        }

        override fun onFailure(error: AssertionError?) {
            mRef.get()?.onFailure(error)
            skinDownloadingItems.remove(mSkinSummary)
        }
    }

    inner class ParaStyleWrapper(
        listener: IHttpTransferListener<ParaStyle>?,
        paraStyleSummary: ParaStyleSummary
    ) :
        IHttpTransferListener<ParaStyle> {
        private var mRef = WeakReference(listener)
        private var mParaStyleSummary = paraStyleSummary
        fun updateReference(
            listener: IHttpTransferListener<ParaStyle>?,
            paraStyleSummary: ParaStyleSummary
        ) {
            mRef = WeakReference(listener)
            mParaStyleSummary = paraStyleSummary
        }

        override fun onProgress(currentLength: Long, totalLength: Long) {
            mRef.get()?.let {
                it.onProgress(currentLength, totalLength)
                paraDownloadingItems[mParaStyleSummary] =
                    (currentLength * COUNT_100 / totalLength).toInt()
            }
        }

        override fun onSuccess(response: ParaStyle?) {
            mRef.get()?.onSuccess(response)
            paraDownloadingItems.remove(mParaStyleSummary)
        }

        override fun onFailure(exception: Exception?) {
            mRef.get()?.onFailure(exception)
            paraDownloadingItems.remove(mParaStyleSummary)
        }

        override fun onFailure(error: AssertionError?) {
            mRef.get()?.onFailure(error)
            paraDownloadingItems.remove(mParaStyleSummary)
        }
    }

    data class PriorityExecutor(
        val executor: ExecutorService,
        val queue: PriorityBlockingQueue<Pair<DownloadPriority, () -> Unit>>
    )
}

enum class DownloadPriority {
    LOW, NORMAL, HIGH,
}