/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : SkinListResp.kt
 * Description    : SkinListResp.kt
 * Version        : 1.0
 * Date           : 2025/4/10
 * Author         : He<PERSON><PERSON><PERSON>iang
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * HeBaoqiang     2025/4/10         1.0           create
 */

package com.oplus.note.downloader.protocol

import androidx.annotation.Keep
import com.oplus.note.downloader.core.Summary

@Keep
data class SummaryListResp(
    val bizId: String = "",
    val bizVersion: Int = 0,
    val fileHost: FileHost = FileHost(),
    val list: List<ListItem> = listOf()
) {
    @Keep
    data class FileHost(
        val auto: String = "",
        val manual: String = ""
    )

    @Keep
    data class ListItem(
        val aid: String = "",
        val children: List<Summary> = listOf()
    )
}