plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.skin.url'

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.startup:startup-runtime:$startup"
    implementation project(':common:logger:logger-api')
    implementation project(':feature:skin:skin-base')
}