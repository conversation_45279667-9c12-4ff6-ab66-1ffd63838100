/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - NoteLockScreenCardProvider.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/5/29
 ** Author: niexiaokang
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ****************************************************************/
package com.oplus.note.card.note

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri

class NoteLockScreenCardProvider : ContentProvider() {
    override fun onCreate(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun getType(uri: Uri): String {
        return ""
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }
}