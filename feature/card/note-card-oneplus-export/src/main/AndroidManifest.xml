<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.oplus.note.card.note.oneplusExport">

    <application>
        <provider
            android:name="com.oplus.note.card.note.NoteLockScreenCardProvider"
            android:authorities="${applicationId}.NoteLockScreenCardProvider"
            android:enabled="true"
            android:exported="true"
            android:initOrder="1"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="pantanal.card.action.RPKCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="pantanal.card.provider"
                android:resource="@xml/lock_screen_card" />
        </provider>
    </application>
</manifest>