plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

//include generic compile configs
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.smartcard'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation project(":feature:card:card-api")
    implementation project(":common:logger:logger-api")
    implementation "androidx.startup:startup-runtime:$startup"
}