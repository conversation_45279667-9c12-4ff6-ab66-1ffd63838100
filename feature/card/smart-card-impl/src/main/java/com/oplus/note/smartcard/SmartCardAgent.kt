/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: SmartCardAgetn.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/02
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.smartcard

import android.content.Context
import com.oplus.note.card.CardAgent
import com.oplus.note.card.CardType
import com.oplus.note.card.RefreshParameter
import com.oplus.note.logger.AppLogger

internal class SmartCardAgent : CardAgent {

    override suspend fun refresh(context: Context, parameter: RefreshParameter) {
        AppLogger.BASIC.d("SmartCardAgent", "refresh")
    }

    override fun getCardId(): String {
        return ""
    }

    override fun getCardSize(): String {
        return ""
    }

    override fun equalsCard(id: String): Boolean {
        return false
    }

    override fun getCardType(): CardType {
        return CardType.ASSISTANT
    }
}