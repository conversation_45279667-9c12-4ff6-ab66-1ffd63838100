/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: SmartCardInitializer.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/10/02
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.smartcard

import android.content.Context
import androidx.startup.Initializer
import com.oplus.note.card.CardAgentManager

class SmartCardInitializer : Initializer<Boolean> {

    override fun create(context: Context): Boolean {
        val agent = SmartCardAgent()
        CardAgentManager.registerCardAgent(agent)
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}