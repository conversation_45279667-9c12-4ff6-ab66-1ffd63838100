<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.oplus.note.smartcard">

    <application>

<!--        <provider-->
<!--            android:name="com.nearme.note.cardwidget.provider.NoteCardWidgetProvider"-->
<!--            android:authorities="com.oplus.note.cardwidget"-->
<!--            android:permission="com.oplus.permission.safe.ASSISTANT"-->
<!--            android:enabled="true"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.appcard.action.APPCARD_UPDATE" />-->
<!--            </intent-filter>-->
<!--            <meta-data-->
<!--                android:name="android.card.provider.array"-->
<!--                android:resource="@array/note_appcard_array" />-->

<!--            <meta-data-->
<!--                android:name="android.card.todo.middle.provider"-->
<!--                android:resource="@xml/todo_middle_appcard" />-->
<!--            <meta-data-->
<!--                android:name="android.card.todo.large.provider"-->
<!--                android:resource="@xml/todo_large_appcard" />-->
<!--            <meta-data-->
<!--                android:name="android.card.provider"-->
<!--                android:resource="@xml/note_appcard" />-->
<!--        </provider>-->

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="com.oplus.note.smartcard.SmartCardInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>
</manifest>