apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'


apply from: rootProject.projectDir.path + '/config.gradle'
apply from: rootProject.file('scripts/common.gradle')
repositories { flatDir { dir 'libs' } }

android {
    namespace 'com.oplus.note.scenecard'
    //just work on xml file
    resourcePrefix "scenecard_"

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}
apply from: rootProject.projectDir.path + "/coui_uikit.gradle"
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.fragment:fragment-ktx:$fragment_ktx"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "com.google.android.material:material:$material"

    implementation project(":common:logger:logger-api")
    // asr
    implementation project(':domain:speech:speech-api')
    domesticImplementation project(path: ':domain:speech:speech-breeno-service')
    exportImplementation project(path: ':domain:speech:speech-google-service')
    gdprImplementation project(path: ':domain:speech:speech-google-service')

    // semantic
    implementation project(path: ':domain:semantic-component:semantic-api')
    domesticImplementation project(path: ':domain:semantic-component:semantic-ssa')

    implementation project(':data:todo-repository')

    //use for vibrate
    implementation project(path: ':common:osdk-proxy')

    compileOnly "com.google.code.gson:gson:${gson}"

    implementation "androidx.constraintlayout:constraintlayout:${constraintlayout}"
    implementation "androidx.recyclerview:recyclerview:${recyclerview}"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation project(":common:logger:logger-api")
    implementation project(":common:lib_base")
    implementation "com.oplus.statistics:track:${track}"
    implementation "com.airbnb.android:lottie:${lottieview}"
    implementation project(":forcealertcomponent")
    implementation project(path: ":common:baseres")
    implementation project(path: ":common:permission")
}
