<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.oplus.note.scenecard">

    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <application>
        <activity
            android:name=".todo.SurpriseActivity"
            android:launchMode="singleTop"
            android:exported="false" />
        <activity
            android:name=".todo.TodoListActivity"
            android:excludeFromRecents="true"
            android:theme="@style/Splash"
            android:icon="@drawable/mini_launcher"
            android:label="@string/todo_app_name"
            android:taskAffinity="com.oplus.note.scenecard"
            android:exported="true"
            android:configChanges="uiMode">
            <intent-filter>
                <action android:name="oplus.intent.action.MINI_LAUNCHER_MAIN" />

                <category android:name="android.intent.category.SECONDARY_HOME" />
                <category android:name="oplus.intent.category.MINI_LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.WIDGET_ENTRY"/>
                <category android:name="android.intent.category.SECONDARY_HOME"/>
            </intent-filter>

            <meta-data
                android:name="com.oplus.secondaryhome.entry.widget.icon"
                android:resource="@drawable/icon_todo_small" />
            <meta-data
                android:name="com.oplus.secondaryhome.entry.widget.title"
                android:resource="@string/todo_app_name" />
        </activity>

        <activity
            android:name=".todo.TodoDetailActivity"
            android:exported="false"
            android:excludeFromRecents="true"
            android:theme="@style/PageTheme"
            android:configChanges="uiMode">
        </activity>
    </application>

</manifest>