/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TestAppbarBehavior.kt
** Description:
** Version: 1.0
** Date : 2023/6/21
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/6/21      1.0     create file
****************************************************************/
package com.google.android.material.appbar

import androidx.coordinatorlayout.widget.CoordinatorLayout

class NoteAppbarBehavior : AppBarLayout.Behavior() {

    fun calSetHeaderTopBottomOffset(parent: CoordinatorLayout?, header: AppBarLayout?, newOffset: Int) {
        setHeaderTopBottomOffset(parent, header, newOffset)
    }
}