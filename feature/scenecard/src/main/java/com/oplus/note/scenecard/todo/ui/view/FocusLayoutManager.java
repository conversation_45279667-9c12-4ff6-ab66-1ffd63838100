/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** OPLUS Coding Static Checking Skip
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/21      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.FloatRange;
import androidx.annotation.IntDef;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.coui.appcompat.animation.COUIInEaseInterpolator;
import com.coui.appcompat.animation.COUIMoveEaseInterpolator;
import com.oplus.note.scenecard.BuildConfig;
import com.oplus.note.scenecard.R;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by baixu on 2023/3/21 下午5:07
 *
 * <AUTHOR>
 */
public class FocusLayoutManager extends RecyclerView.LayoutManager {
    public static final String TAG = "FocusLayoutManager";
    /**
     * 堆叠方向在上
     */
    public static final int FOCUS_TOP = 3;

    /**
     * 最大可堆叠层级
     */
    int maxLayerCount;

    /**
     * 堆叠的方向。
     * 期望滚动方向为垂直时，传{@link #FOCUS_TOP}。
     */
    @FocusOrientation
    private int focusOrientation = FOCUS_TOP;
    /**
     * 堆叠view之间的偏移量
     */
    private float layerPadding;
    /**
     * 普通view之间的margin
     */
    private float normalViewGap;
    /**
     * 是否自动选中
     */
    private boolean isAutoSelect;
    /**
     * 变换监听接口。
     */
    private List<TrasitionListener> trasitionListeners;
    /**
     *
     */
    private OnFocusChangeListener onFocusChangeListener;
    /**
     * 水平方向累计偏移量
     */
    private long mHorizontalOffset;
    /**
     * 垂直方向累计偏移量
     */
    private long mVerticalOffset;
    /**
     * 屏幕可见的第一个View的Position
     */
    private int mFirstVisiPos;
    /**
     * 屏幕可见的最后一个View的Position
     */
    private int mLastVisiPos;
    /**
     * 一次完整的聚焦滑动所需要移动的距离。
     */
    private float onceCompleteScrollLength = -1;
    /**
     * 焦点view的position
     */
    private int focusdPosition = -1;
    /**
     * 自动选中动画
     */
    private ValueAnimator selectAnimator;
    private long autoSelectMinDuration;
    private long autoSelectMaxDuration;

    private static final int FIRST_OFFSET = 10;
    private static final int FIRST_OFFSET_SCROLL = -FIRST_OFFSET;

    private static final int SECOND_OFFSET = -27;

    private static final int SECOND_OFFSET_SCROLL = FIRST_OFFSET - SECOND_OFFSET;

    private static final int THIRD_OFFSET = -20;

    private static final int THIRD_OFFSET_SCROLL = SECOND_OFFSET - THIRD_OFFSET;

    private static final int ZERO_Z = 100;
    private static final int FIRST_Z = 200;

    private static final int SECOND_Z = 100;

    private static final int THIRD_Z = 0;

    private static final int FOURTH_Z = -100;

    //第4个的默认Z值，比第三个更低，保证看起来第4个在第3个下方
    private static final int DEFAULT_Z = -102;
    private int dimen_4 = 0;
    private int dimen_16 = 0;
    private int dimen_20 = 0;
    private int dimen_36 = 0;
    private int dimen_52 = 0;
    private int dimen_68 = 0;

    private static final long OPEN_ANIMATION_DURATION = 600L;

    private int mRecyclerWidth = 0;
    /**入场动画开始的位置（距离View顶部的偏移量）*/
    private int mOpenAnimationStartOffset = 0;
    private boolean mOpenAnimationCompleted = false;

    public interface AlphaChangeCallback {
        void onAlphaChange(float alpha);
    }
    private int dimen_86 = 0;
    private int dimen_134 = 0;
    private int dimen_154 = 0;
    private int dimen_174 = 0;
    private int dimen_178 = 0;

    @IntDef({FOCUS_TOP})
    @Retention(RetentionPolicy.SOURCE)
    public @interface FocusOrientation {
    }

    public FocusLayoutManager() {
        this(new Builder());
    }

    private FocusLayoutManager(Builder builder) {
        this.maxLayerCount = builder.maxLayerCount;
        this.layerPadding = builder.layerPadding;
        this.trasitionListeners = builder.trasitionListeners;
        this.normalViewGap = builder.normalViewGap;
        this.isAutoSelect = builder.isAutoSelect;
        this.onFocusChangeListener = builder.onFocusChangeListener;
        this.autoSelectMinDuration = builder.autoSelectMinDuration;
        this.autoSelectMaxDuration = builder.autoSelectMaxDuration;
        this.mOpenAnimationStartOffset = builder.mOpenAnimationStartOffset;
        this.mVerticalOffset = mOpenAnimationStartOffset;
    }


    @Override
    public RecyclerView.LayoutParams generateDefaultLayoutParams() {
        return new RecyclerView.LayoutParams(RecyclerView.LayoutParams.WRAP_CONTENT,
                RecyclerView.LayoutParams.WRAP_CONTENT);
    }

    @Override
    public void onLayoutChildren(RecyclerView.Recycler recycler, RecyclerView.State state) {
        if (state.getItemCount() == 0) {
            removeAndRecycleAllViews(recycler);
            return;
        }

        onceCompleteScrollLength = -1;
        //分离全部已有的view，放入临时缓存
        detachAndScrapAttachedViews(recycler);

        fill(recycler, state, 0);
    }


    @Override
    public boolean canScrollHorizontally() {
        return false;
    }

    @Override
    public boolean canScrollVertically() {
        return true;
    }

    @Override
    public int scrollVerticallyBy(int dy, RecyclerView.Recycler recycler,
                                  RecyclerView.State state) {
        //位移0、没有子View 当然不移动
        if (dy == 0 || getChildCount() == 0) {
            return 0;
        }

        //dy 往下是负，往上是正
        mVerticalOffset += dy;//累加实际滑动距离


        dy = fill(recycler, state, dy);

        return dy;
    }


    @Override
    public void onDetachedFromWindow(RecyclerView view, RecyclerView.Recycler recycler) {
        cancelAnimator();
        super.onDetachedFromWindow(view, recycler);
    }

    /**
     * @param recycler
     * @param state
     * @param delta
     */
    private int fill(RecyclerView.Recycler recycler, RecyclerView.State state, int delta) {
        int resultDelta = fillVerticalTop(recycler, state, delta);
        recycleChildren(recycler);
        return resultDelta;
    }

    /**
     * 垂直滚动、向上堆叠布局
     * 详细注释请参考{@link #fillVerticalTop(RecyclerView.Recycler, RecyclerView.State, int)}。
     *
     * @param recycler
     * @param state
     * @param dy       偏移量。手指从上向下滑动，dy < 0; 手指从下向上滑动，dy > 0;
     */
    private int fillVerticalTop(RecyclerView.Recycler recycler, RecyclerView.State state,
                                int dy) {

        //----------------1、边界检测-----------------
        if (dy < 0 && mOpenAnimationCompleted) {
            if (mVerticalOffset < 0) {
                mVerticalOffset = dy = 0;
            }
        }

        if (dy > 0) {
            if (mLastVisiPos - mFirstVisiPos <= maxLayerCount - 1) {
                mVerticalOffset -= dy;
                dy = 0;
            }
        }

        detachAndScrapAttachedViews(recycler);

        //----------------2、初始化布局数据-----------------

        float startY = getPaddingTop() - layerPadding;
        float lastBottom = -1.00f;
        int middleFirst = 0;

        View tempView = null;
        int tempPosition = -1;
        if (onceCompleteScrollLength == -1) {
            if (mFirstVisiPos >= getItemCount()) {
                mFirstVisiPos = Math.min(mFirstVisiPos, getItemCount() - 1);
                setFocusdPosition(0, false);
                return 0;
            }
            //因为mFirstVisiPos在下面可能会被改变，所以用tempPosition暂存一下。
            tempPosition = Math.max(0, mFirstVisiPos);
            tempView = recycler.getViewForPosition(tempPosition);
            measureChildWithScale(tempView, tempView.getWidth(), tempView.getHeight(), 0, 0);
            onceCompleteScrollLength = getDecoratedMeasurementVertical(tempView) + normalViewGap;
        }
        //当前"一次完整的聚焦滑动"所在的进度百分比.百分比增加方向为向着堆叠移动的方向（即如果为FOCUS_LEFT，从右向左移动fraction将从0%到100%）
        float fraction =
                (Math.abs(mVerticalOffset) % onceCompleteScrollLength) / (onceCompleteScrollLength);
        if (mVerticalOffset < 0 && fraction != 0f) {
            fraction = 1 - fraction;
        }

        //堆叠区域view偏移量。在一次完整的聚焦滑动期间，其总偏移量是一个layerPadding的距离
        float layerViewOffset = layerPadding * fraction;
        //普通区域view偏移量。在一次完整的聚焦滑动期间，其总位移量是一个onceCompleteScrollLength
        float normalViewOffset = onceCompleteScrollLength * fraction;
        boolean isLayerViewOffsetSetted = false;
        boolean isNormalViewOffsetSetted = false;

        //修正第一个可见的view：mFirstVisiPos。已经滑动了多少个完整的onceCompleteScrollLength就代表滑动了多少个item
        mFirstVisiPos = (int) Math.floor(mVerticalOffset / onceCompleteScrollLength);
        //向下取整
        //临时将mLastVisiPos赋值为getItemCount() - 1，放心，下面遍历时会判断view是否已溢出屏幕，并及时修正该值并结束布局
        mLastVisiPos = getItemCount() - 1;
        mLastVisiPos = Math.min(mLastVisiPos, mFirstVisiPos + 4);


        int newFocusedPosition = mFirstVisiPos + maxLayerCount - 1;
        if (newFocusedPosition != focusdPosition) {
            if (onFocusChangeListener != null) {
                onFocusChangeListener.onFocusChanged(newFocusedPosition, focusdPosition);
            }
            focusdPosition = newFocusedPosition;
        }


        //----------------3、开始布局-----------------

        for (int i = Math.max(mFirstVisiPos, 0); i <= mLastVisiPos; i++) {
            //属于堆叠区域
            if (i - mFirstVisiPos < maxLayerCount) {
                View item;

                if (i == tempPosition && tempView != null) {
                    //如果初始化数据时已经取了一个临时view，可别浪费了！
                    item = tempView;
                } else {
                    item = recycler.getViewForPosition(i);
                }
                addView(item);
                measureChildWithScale(item, item.getWidth(), item.getHeight(), i - mFirstVisiPos, fraction);

                startY += layerPadding;
                if (!isLayerViewOffsetSetted) {
                    startY -= layerViewOffset;
                    isLayerViewOffsetSetted = true;
                }

                if (trasitionListeners != null && !trasitionListeners.isEmpty()) {
                    for (TrasitionListener trasitionListener : trasitionListeners) {
                        trasitionListener.handleLayerView(this, item, i - mFirstVisiPos, i,
                                maxLayerCount, i, fraction, dy);
                    }
                }

                int l, t, r, b;
                l = getPaddingLeft();
                t = (int) ((int) startY);
                r = getPaddingLeft() + getDecoratedMeasurementHorizontal(item);
                middleFirst = r / 2;
                b = (int) (startY + getDecoratedMeasurementVertical(item));
                lastBottom = b;
                layoutDecoratedWithMargins(item, l, t, r, b);
                item.setTranslationZ(getTranslationZ(fraction, i - mFirstVisiPos));

            } else {//属于普通区域

                View item = recycler.getViewForPosition(i);
                addView(item);
                measureChildWithScale(item, item.getWidth(), item.getHeight(), i - mFirstVisiPos, fraction);
                if (lastBottom == -1.00f) {
                    if (mFirstVisiPos >= 0) {
                        startY += onceCompleteScrollLength;
                    } else {
                        startY += onceCompleteScrollLength * Math.abs(mFirstVisiPos);
                    }
                } else {
                    startY = lastBottom;
                }
                if (!isNormalViewOffsetSetted) {
                    startY += layerViewOffset;
                    startY -= normalViewOffset;
                    isNormalViewOffsetSetted = true;
                }


                if (trasitionListeners != null && !trasitionListeners.isEmpty()) {
                    for (TrasitionListener trasitionListener : trasitionListeners) {
                        if (i - mFirstVisiPos == maxLayerCount) {
                            trasitionListener.handleFocusingView(this, item, i - mFirstVisiPos, i, fraction, dy);
                        } else {
                            trasitionListener.handleNormalView(this, item, i - mFirstVisiPos, i, fraction, dy);
                        }
                    }
                }

                int l, t, r, b;
                int oriWidth = getDecoratedMeasurementHorizontal(item);
                if (middleFirst == 0) {
                    middleFirst = mRecyclerWidth / 2;
                }
                l = getPaddingLeft() + middleFirst - oriWidth / 2;
                t = (int) startY + getPaddingTop(fraction, i - mFirstVisiPos);
                r = getPaddingLeft() + oriWidth + middleFirst - oriWidth / 2;
                b = (int) (t + getDecoratedMeasurementVertical(item));
                lastBottom = b;
                layoutDecoratedWithMargins(item, l, t, r, b);
                item.setTranslationZ(getTranslationZ(fraction, i - mFirstVisiPos));
            }
        }

        return dy;
    }

    public void measureChildWithScale(@NonNull View child, int width, int height, int position, float fraction) {
        Context context = child.getContext();
        if (dimen_4 == 0) {
            dimen_4 = (int) (context.getResources().getDimension(R.dimen.dimen_8));
            dimen_16 = (int) (context.getResources().getDimension(R.dimen.dimen_16));
            dimen_20 = (int) (context.getResources().getDimension(R.dimen.dimen_20));
            dimen_36 = (int) (context.getResources().getDimension(R.dimen.dimen_36));
            dimen_52 = (int) (context.getResources().getDimension(R.dimen.dimen_52));
            dimen_68 = (int) (context.getResources().getDimension(R.dimen.dimen_68));
            dimen_86 = (int) (context.getResources().getDimension(R.dimen.dimen_86));
            dimen_134 = (int) (context.getResources().getDimension(R.dimen.dimen_134));
            dimen_154 = (int) (context.getResources().getDimension(R.dimen.dimen_154));
            dimen_174 = (int) (context.getResources().getDimension(R.dimen.dimen_174));
            dimen_178 = (int) (context.getResources().getDimension(R.dimen.dimen_178));
        }
        if (position == 0) {
            //154 182
            height = dimen_154;
            width = dimen_178;
        } else if (position == 1) {
            //68 182
            height = (int) (dimen_68 + fraction * dimen_86);
            width = dimen_178;
        } else if (position == 2) {
            //52 174
            height = (int) (dimen_52 + fraction * dimen_16);
            width = (int) (dimen_174 + dimen_4 * fraction);
        } else if (position == 3) {
            //36 154
            height = (int) (dimen_36 + fraction * dimen_16);
            width = (int) (dimen_154 + dimen_20 * fraction);
        } else {
            //36 154
            height = (int) (dimen_20 + fraction * dimen_16);
            width = (int) (dimen_134 + dimen_20 * fraction);
        }
        ViewGroup.LayoutParams layoutParams = child.getLayoutParams();
        layoutParams.height = height;
        layoutParams.width = width;
        child.setLayoutParams(layoutParams);
        measureChild(child, 0, 0);
    }

    /**
     * 0 1 之间变化 正常， 12之间变化还是 y = -83 * time +6
     * 根据滚动距离，计算首个item和第二个之间的间距,随滚动距离，padding减少
     *
     * @param fraction 滚动的比例
     * @param pos      从上到下数，第几个item
     */
    private float getTranslationZ(float fraction, int pos) {
        float transZ = DEFAULT_Z;
        if (pos == 0) {
            transZ = ZERO_Z;
        } else if (pos == 1) {
            transZ = SECOND_Z * fraction + SECOND_Z;
        } else if (pos == 2) {
            transZ = SECOND_Z * fraction + THIRD_Z;
        } else if (pos == 3) {
            transZ = SECOND_Z * fraction + FOURTH_Z;
        }
        return transZ;
    }

    /**
     * 0 1 之间变化 正常， 12之间变化还是 y = -83 * time +6
     * 根据滚动距离，计算首个item和第二个之间的间距,随滚动距离，padding减少
     *
     * @param fraction 滚动的比例
     * @param pos      从上到下数，第几个item
     */
    private int getPaddingTop(float fraction, int pos) {
        int paddingTop = 0;
        if (pos == 1) {
            paddingTop = (int) (FIRST_OFFSET_SCROLL * fraction + FIRST_OFFSET);
        } else if (pos == 2) {
            paddingTop = (int) (SECOND_OFFSET_SCROLL * fraction + SECOND_OFFSET);
        } else {
            paddingTop = (int) (THIRD_OFFSET_SCROLL * fraction + THIRD_OFFSET);
        }
        return paddingTop;
    }

    @Override
    public void onAdapterChanged(@Nullable RecyclerView.Adapter oldAdapter,
                                 @Nullable RecyclerView.Adapter newAdapter) {
        resetData();
        super.onAdapterChanged(oldAdapter, newAdapter);
    }

    @Override
    public void onMeasure(@NonNull RecyclerView.Recycler recycler,
                          @NonNull RecyclerView.State state, int widthSpec, int heightSpec) {
        int heightMode = View.MeasureSpec.getMode(heightSpec);
        if (heightMode == View.MeasureSpec.AT_MOST && focusOrientation == FOCUS_TOP) {
            if (BuildConfig.DEBUG) {
                throw new RuntimeException("FocusLayoutManager-onMeasure:当滚动方向为垂直时，recyclerView" +
                        "的高度请不要使用wrap_content");
            } else {
                Log.e(TAG, "FocusLayoutManager-onMeasure:当滚动方向为垂直时，recyclerView" +
                        "的高度请不要使用wrap_content");
            }
        }
        super.onMeasure(recycler, state, widthSpec, heightSpec);
        mRecyclerWidth = View.MeasureSpec.getSize(widthSpec);
    }

    @Override
    public boolean isAutoMeasureEnabled() {
        return true;
    }

    /**
     * 回收需回收的Item。
     */
    private void recycleChildren(RecyclerView.Recycler recycler) {
        List<RecyclerView.ViewHolder> scrapList = recycler.getScrapList();
        for (int i = 0; i < scrapList.size(); i++) {
            RecyclerView.ViewHolder holder = scrapList.get(i);
            removeAndRecycleView(holder.itemView, recycler);
        }
    }

    @Override
    public void onScrollStateChanged(int state) {
        super.onScrollStateChanged(state);
        switch (state) {
            case RecyclerView.SCROLL_STATE_DRAGGING:
                //当手指按下时，停止当前正在播放的动画
                cancelAnimator();
                break;
            case RecyclerView.SCROLL_STATE_IDLE:
                //当列表滚动停止后，判断一下自动选中是否打开
                if (isAutoSelect) {
                    //找到离目标落点最近的item索引
                    smoothScrollToPosition(findShouldSelectPosition());
                }
                break;
            default:
                break;
        }
    }

    /**
     * 返回应当选中的position
     *
     * @return
     */
    private int findShouldSelectPosition() {
        if (onceCompleteScrollLength == -1 || mFirstVisiPos == -1) {
            return -1;
        }
        int remainder = -1;
        if (focusOrientation == FOCUS_TOP) {
            remainder = (int) (Math.abs(mVerticalOffset) % onceCompleteScrollLength);
        }

        if (remainder >= onceCompleteScrollLength / 2.0f) { //超过一半，应当选中下一项
            if (mFirstVisiPos + 1 <= getItemCount() - 1) {
                return mFirstVisiPos + 1;
            }
        }

        return mFirstVisiPos;
    }

    /**
     * 返回移动到position所需的偏移量
     *
     * @param position
     * @return
     */
    private float getScrollToPositionOffset(int position) {
        float offset = position * onceCompleteScrollLength - Math.abs(mVerticalOffset);
        return offset;
    }


    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state,
                                       int position) {
        smoothScrollToPosition(position);
    }

    @Override
    public void scrollToPosition(int position) {
        mVerticalOffset += getScrollToPositionOffset(position);
        requestLayout();
    }

    /**
     * 平滑滚动到某个位置
     *
     * @param position 目标Item索引
     */
    public void smoothScrollToPosition(int position) {
        if (position > -1 && position < getItemCount()) {
            startValueAnimator(position);
        }
    }

    private void startValueAnimator(int position) {
        cancelAnimator();

        final float distance = getScrollToPositionOffset(position);
        long minDuration = autoSelectMinDuration;
        long maxDuration = autoSelectMaxDuration;
        long duration;
        float distanceFraction = (Math.abs(distance) / (onceCompleteScrollLength));
        if (distance <= onceCompleteScrollLength) {
            duration = (long) (minDuration + (maxDuration - minDuration) * distanceFraction);
        } else {
            duration = (long) (maxDuration * distanceFraction);
        }

        if (selectAnimator == null) {
            selectAnimator = ValueAnimator.ofFloat(0.0f, distance);
        }
        selectAnimator.setFloatValues(0.0f, distance);
        selectAnimator.setDuration(duration);
        selectAnimator.setInterpolator(new COUIMoveEaseInterpolator());
        final float startedOffset = mVerticalOffset;

        selectAnimator.addUpdateListener(animation -> {
            if (focusOrientation == FOCUS_TOP) {
                if (mVerticalOffset < 0) {
                    mVerticalOffset =
                            (long) Math.floor(startedOffset + (float) animation.getAnimatedValue());
                } else {
                    mVerticalOffset =
                            (long) Math.ceil(startedOffset + (float) animation.getAnimatedValue());
                }
                requestLayout();
            }

        });
        selectAnimator.start();

    }

    /**
     * 获取某个childView在水平方向所占的空间，将margin考虑进去
     *
     * @param view
     * @return
     */
    public int getDecoratedMeasurementHorizontal(View view) {
        final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                view.getLayoutParams();
        return getDecoratedMeasuredWidth(view) + params.leftMargin
                + params.rightMargin;
    }

    /**
     * 获取某个childView在竖直方向所占的空间,将margin考虑进去
     *
     * @param view
     * @return
     */
    public int getDecoratedMeasurementVertical(View view) {
        final RecyclerView.LayoutParams params = (RecyclerView.LayoutParams)
                view.getLayoutParams();
        return getDecoratedMeasuredHeight(view) + params.topMargin
                + params.bottomMargin;
    }

    public int getVerticalSpace() {
        return getHeight() - getPaddingTop() - getPaddingBottom();
    }

    public int getHorizontalSpace() {
        return getWidth() - getPaddingLeft() - getPaddingRight();
    }

    /**
     * 重置数据
     */
    public void resetData() {
        mFirstVisiPos = 0;
        mLastVisiPos = 0;
        onceCompleteScrollLength = -1;
        mHorizontalOffset = 0;
        mVerticalOffset = mOpenAnimationCompleted ? 0 : mOpenAnimationStartOffset;
        focusdPosition = -1;
        cancelAnimator();
    }

    public void cancelAnimator() {
        if (selectAnimator != null && (selectAnimator.isStarted() || selectAnimator.isRunning())) {
            selectAnimator.cancel();
        }
    }

    public boolean isScrollAnimationRunning() {
        if (selectAnimator != null) {
            return selectAnimator.isRunning();
        }
        return false;
    }

    public int getFocusdPosition() {
        return focusdPosition;
    }

    public void setFocusdPosition(int focusdPosition, boolean anim) {
        if (focusdPosition > -1 && focusdPosition < getItemCount() && focusdPosition >= maxLayerCount - 1) {
            if (anim) {
                smoothScrollToPosition(focusdPosition - (maxLayerCount - 1));
            } else {
                scrollToPosition(focusdPosition - (maxLayerCount - 1));
            }
        }
    }


    public boolean isAutoSelect() {
        return isAutoSelect;
    }

    public void setAutoSelect(boolean autoSelect) {
        isAutoSelect = autoSelect;
    }


    public int getMaxLayerCount() {
        return maxLayerCount;
    }

    public void setMaxLayerCount(int maxLayerCount) {
        this.maxLayerCount = maxLayerCount;
        resetData();
        requestLayout();
    }

    public int getFocusOrientation() {
        return focusOrientation;
    }

    public void setFocusOrientation(@FocusOrientation int focusOrientation) {
        this.focusOrientation = focusOrientation;
        resetData();
        requestLayout();
    }

    public float getLayerPadding() {
        return layerPadding;
    }

    public void setLayerPadding(float layerPadding) {
        if (layerPadding < 0) {
            layerPadding = 0;
        }
        this.layerPadding = layerPadding;
        resetData();
        requestLayout();
    }

    public float getNormalViewGap() {
        return normalViewGap;
    }

    public void setNormalViewGap(float normalViewGap) {
        this.normalViewGap = normalViewGap;
        resetData();
        requestLayout();
    }

    public List<TrasitionListener> getTrasitionListeners() {
        return trasitionListeners;
    }

    public void addTrasitionListener(TrasitionListener trasitionListener) {
        this.trasitionListeners.add(trasitionListener);
        requestLayout();
    }

    /**
     * @param trasitionListener if null,remove all
     * @return
     */
    public boolean removeTrasitionlistener(TrasitionListener trasitionListener) {
        if (trasitionListener != null) {
            return trasitionListeners.remove(trasitionListener);
        } else {
            trasitionListeners.clear();
            return true;
        }
    }


    public OnFocusChangeListener getOnFocusChangeListener() {
        return onFocusChangeListener;
    }

    public void setOnFocusChangeListener(OnFocusChangeListener onFocusChangeListener) {
        this.onFocusChangeListener = onFocusChangeListener;
    }


    public static class Builder {


        int maxLayerCount;
        @FocusOrientation
        private int focusOrientation;
        private float layerPadding;
        private float normalViewGap;
        private boolean isAutoSelect;
        private List<TrasitionListener> trasitionListeners;
        private OnFocusChangeListener onFocusChangeListener;
        private long autoSelectMinDuration;
        private long autoSelectMaxDuration;
        private TrasitionListener defaultTrasitionListener;
        private int mOpenAnimationStartOffset;


        public Builder() {
            maxLayerCount = 3;
            focusOrientation = FOCUS_TOP;
            layerPadding = 60;
            normalViewGap = 60;
            isAutoSelect = true;
            trasitionListeners = new ArrayList<>();
//            defaultTrasitionListener = new TrasitionListenerConvert(new SimpleTrasitionListener() {
//            });
//            trasitionListeners.add(defaultTrasitionListener);
            onFocusChangeListener = null;
            autoSelectMinDuration = 100;
            autoSelectMaxDuration = 300;
        }

        /**
         * 最大可堆叠层级
         */
        public Builder maxLayerCount(int maxLayerCount) {
            if (maxLayerCount <= 0) {
                throw new RuntimeException("maxLayerCount不能小于0");
            }
            this.maxLayerCount = maxLayerCount;
            return this;
        }

        /**
         * 堆叠view之间的偏移量
         *
         * @param layerPadding
         * @return
         */
        public Builder layerPadding(float layerPadding) {
            if (layerPadding < 0) {
                layerPadding = 0;
            }
            this.layerPadding = layerPadding;
            return this;
        }

        /**
         * 普通view之间的margin
         */
        public Builder normalViewGap(float normalViewGap) {
            this.normalViewGap = normalViewGap;
            return this;
        }

        /**
         * 是否自动选中
         */
        public Builder isAutoSelect(boolean isAutoSelect) {
            this.isAutoSelect = isAutoSelect;
            return this;
        }

        public Builder autoSelectDuration(@IntRange(from = 0) long minDuration, @IntRange(from =
                0) long maxDuration) {
            if (minDuration < 0 || maxDuration < 0 || maxDuration < minDuration) {
                throw new RuntimeException("autoSelectDuration入参不合法");
            }
            this.autoSelectMinDuration = minDuration;
            this.autoSelectMaxDuration = maxDuration;
            return this;
        }

        /**
         * 高级定制 添加滚动过程中view的变换监听接口
         *
         * @param trasitionListener
         * @return
         */
        public Builder addTrasitionListener(TrasitionListener trasitionListener) {
            if (trasitionListener != null) {
                this.trasitionListeners.add(trasitionListener);
            }
            return this;
        }


        public Builder setOnFocusChangeListener(OnFocusChangeListener onFocusChangeListener) {
            this.onFocusChangeListener = onFocusChangeListener;
            return this;
        }

        public Builder setOpenAnimationStartOffset(int offset) {
            this.mOpenAnimationStartOffset = offset;
            return this;
        }

        public FocusLayoutManager build() {
            return new FocusLayoutManager(this);
        }
    }


    /**
     * 滚动过程中view的变换监听接口。属于高级定制，暴露了很多关键布局数据。若定制要求不高，考虑使用{@link SimpleTrasitionListener}
     */
    public interface TrasitionListener {

        /**
         * 处理在堆叠里的view。
         *
         * @param focusLayoutManager
         * @param view               view对象。请仅在方法体范围内对view做操作，不要外部强引用它，view是要被回收复用的
         * @param viewLayer          当前层级，0表示底层，maxLayerCount-1表示顶层
         * @param maxLayerCount      最大层级
         * @param position           item所在的相对position
         * @param realPosition       item所在的position
         * @param fraction           "一次完整的聚焦滑动"所在的进度百分比.百分比增加方向为向着堆叠移动的方向（即如果为FOCUS_LEFT
         *                           ，从右向左移动fraction将从0%到100%）
         * @param offset             当次滑动偏移量
         */
        void handleLayerView(FocusLayoutManager focusLayoutManager, View view, int viewLayer,
                             int maxLayerCount, int position, int realPosition, float fraction, float offset);

        /**
         * 处理正聚焦的那个View（即正处在从普通位置滚向聚焦位置时的那个view,即堆叠顶层view）
         *
         * @param focusLayoutManager
         * @param view               view对象。请仅在方法体范围内对view做操作，不要外部强引用它，view是要被回收复用的
         * @param position           item所在的相对position
         * @param realPosition       item所在的position
         * @param fraction           "一次完整的聚焦滑动"所在的进度百分比.百分比增加方向为向着堆叠移动的方向（即如果为FOCUS_LEFT
         *                           ，从右向左移动fraction将从0%到100%）
         * @param offset             当次滑动偏移量
         */
        void handleFocusingView(FocusLayoutManager focusLayoutManager, View view, int position, int realPosition,
                                float fraction, float offset);

        /**
         * 处理不在堆叠里的普通view（正在聚焦的那个view除外）
         *
         * @param focusLayoutManager
         * @param view               view对象。请仅在方法体范围内对view做操作，不要外部强引用它，view是要被回收复用的
         * @param position           item所在的相对position
         * @param realPosition       item所在的position
         * @param fraction           "一次完整的聚焦滑动"所在的进度百分比.百分比增加方向为向着堆叠移动的方向（即如果为FOCUS_LEFT
         *                           ，从右向左移动fraction将从0%到100%）
         * @param offset             当次滑动偏移量
         */
        void handleNormalView(FocusLayoutManager focusLayoutManager, View view, int position, int realPosition,
                              float fraction, float offset);

    }

    /**
     * 简化版  滚动过程中view的变换监听接口。
     */
    public static abstract class SimpleTrasitionListener {

        /**
         * 返回堆叠view最大透明度
         *
         * @param maxLayerCount 最大层级
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getLayerViewMaxAlpha(int maxLayerCount) {
            return getFocusingViewMaxAlpha();
        }

        /**
         * 返回堆叠view最小透明度
         *
         * @param maxLayerCount 最大层级
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getLayerViewMinAlpha(int maxLayerCount) {
            return 0;
        }


        /**
         * 返回堆叠view最大缩放比例
         *
         * @param maxLayerCount 最大层级
         * @return
         */
        public float getLayerViewMaxScale(int maxLayerCount) {
            return getFocusingViewMaxScale();
        }

        /**
         * 返回堆叠view最小缩放比例
         *
         * @param maxLayerCount 最大层级
         * @return
         */
        public float getLayerViewMinScale(int maxLayerCount) {
            return 0.7f;
        }


        /**
         * 返回一个百分比值，相对于"一次完整的聚焦滑动"期间，在该百分比值内view就完成缩放、透明度的渐变变化。
         * 例：若返回值为1，说明在"一次完整的聚焦滑动"期间view将线性均匀完成缩放、透明度变化；
         * 例：若返回值为0.5，说明在"一次完整的聚焦滑动"的一半路程内（具体从什么时候开始变由实际逻辑自己决定），view将完成的缩放、透明度变化
         *
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getLayerChangeRangePercent() {
            return 0.35f;
        }

        /**
         * 返回聚焦view的最大透明度
         *
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getFocusingViewMaxAlpha() {
            return 1f;
        }

        /**
         * 返回聚焦view的最小透明度
         *
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getFocusingViewMinAlpha() {
            return getNormalViewAlpha();
        }

        /**
         * 返回聚焦view的最大缩放比例
         *
         * @return
         */
        public float getFocusingViewMaxScale() {
            return 1.0f;
        }

        /**
         * 返回聚焦view的最小缩放比例
         *
         * @return
         */
        public float getFocusingViewMinScale() {
            return getNormalViewScale();
        }

        /**
         * 返回值意义参考{@link #getLayerChangeRangePercent()}
         *
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getFocusingViewChangeRangePercent() {
            return 0.5f;
        }

        /**
         * 返回普通view的透明度
         *
         * @return
         */
        @FloatRange(from = 0.0f, to = 1.0f)
        public float getNormalViewAlpha() {
            return 1.0f;
        }

        /**
         * 返回普通view的缩放比例
         *
         * @return
         */
        public float getNormalViewScale() {
            return 1.0f;
        }

    }


    public interface OnFocusChangeListener {

        /**
         * @param focusdPosition
         * @param lastFocusedPosition 可能为-1
         */
        void onFocusChanged(int focusdPosition, int lastFocusedPosition);
    }

    public static float dp2px(Context context, float dp) {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp,
                context.getResources().getDisplayMetrics());
    }

    public float getNormalSpace() {
        return normalViewGap;
    }

    public void startOpenAnimation(AlphaChangeCallback callback) {
        ValueAnimator openAnimation = ValueAnimator.ofInt(mOpenAnimationStartOffset, 0);
        openAnimation.setDuration(OPEN_ANIMATION_DURATION);
        openAnimation.setInterpolator(new COUIInEaseInterpolator());
        openAnimation.addUpdateListener(animation -> {
            if (callback != null) {
                callback.onAlphaChange(animation.getAnimatedFraction());
            }
            mVerticalOffset =  (int) animation.getAnimatedValue();
            requestLayout();
        });
        openAnimation.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(@NonNull Animator animation) {

            }

            @Override
            public void onAnimationEnd(@NonNull Animator animation) {
                if (callback != null) {
                    callback.onAlphaChange(1f);
                }
                mOpenAnimationCompleted = true;
                mVerticalOffset = 0;
                requestLayout();
            }

            @Override
            public void onAnimationCancel(@NonNull Animator animation) {
                mOpenAnimationCompleted = true;
            }

            @Override
            public void onAnimationRepeat(@NonNull Animator animation) {

            }
        });
        openAnimation.start();
    }

}
