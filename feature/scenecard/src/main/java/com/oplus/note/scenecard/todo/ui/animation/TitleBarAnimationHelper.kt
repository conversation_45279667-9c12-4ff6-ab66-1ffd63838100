/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TitleBarAnimationHelper.kt
** Description:
** Version: 1.0
** Date : 2023/6/20
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/6/20      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.text.TextUtils
import android.util.LayoutDirection
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.NoteRecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.view.NoteAppbarLayout
import java.util.Locale
import kotlin.math.abs

/** http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&amp%3Bversion=V%2013&amp%3Bid=7837&version=V%2013&id=7833 */
class TitleBarAnimationHelper(val appbar: NoteAppbarLayout, val recyclerView: NoteRecyclerView) {

    companion object {
        private const val TAG = "TitleBarAnimationHelper"
        private const val NUMBER_04 = 0.4f
        private const val NUMBER_075 = 0.75f
        private const val NUMBER_5 = 5
        private const val NUMBER_7 = 7
        private const val NUMBER_12 = 12
    }

    /**滑动进度，从上往下（消失到完全出现）进度从 0 到 1*/
    private var fraction = 1f
    private var appbarChildView: ConstraintLayout = appbar.findViewById(R.id.title_bar)
    private var verticalOffset = 0f

    init {
        appbarChildView.viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                appbarChildView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL
                appbarChildView.pivotX = if (isRTL) appbarChildView.measuredWidth.toFloat() else 0f
                appbarChildView.pivotY = appbarChildView.measuredHeight.toFloat()
            }
        })
        (appbarChildView.layoutParams as AppBarLayout.LayoutParams).scrollEffect = ScaleAlphaChildScrollEffect()
    }

    /** scrollY 小于0时，表示顶部下拉触发的 overScroll */
    fun onRecyclerViewOverScroll(scrollX: Int, scrollY: Int) {
        if (scrollY > 0) {
            return
        }
        appbar.translationY = (abs(scrollY) / 2).toFloat()
    }

    private inner class ScaleAlphaChildScrollEffect : AppBarLayout.ChildScrollEffect() {
        override fun onOffsetChanged(appBarLayout: AppBarLayout, child: View, offset: Float) {
            calculateFraction(offset)
            val scale = calculateScale()
            val alpha = calculateAlpha()
            child.scaleX = scale
            child.scaleY = scale
            child.alpha = alpha
            verticalOffset = offset
        }
    }

    /** appBar 滚出屏幕后，删除所有待办显示空页面时，需要显示出 appbar */
    fun checkAndShowAppbar() {
        if (-verticalOffset >= appbar.totalScrollRange) {
            appbar.behavior?.calSetHeaderTopBottomOffset(
                appbar.parent as? CoordinatorLayout,
                appbar,
                appbar.totalScrollRange
            )
            appbarChildView.apply {
                scaleX = 1f
                scaleY = 1f
                alpha = 1f
            }
        }
    }

    private fun calculateFraction(offset: Float) {
        val totalHeight = appbar.totalScrollRange
        fraction = if (totalHeight == 0) {
            1f
        } else {
            (totalHeight - abs(offset)) / totalHeight
        }
    }

    /** fraction 从 0.4 到 1， scale 从0.75 到 1 */
    private fun calculateScale(): Float {
        val scale = if (fraction <= NUMBER_04) {
            NUMBER_075
        } else if (fraction <= 1f) {
            (NUMBER_5 * fraction + NUMBER_7) / NUMBER_12
        } else {
            1f
        }
        return scale
    }

    private fun calculateAlpha(): Float {
        return fraction.coerceAtLeast(0f).coerceAtMost(1f)
    }
}