/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/8/31
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/8/31      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.annotation.StringRes
import com.oplus.note.osdk.adapter.OplusIntentAdapter
import com.oplus.note.scenecard.todo.ui.fragment.TodoBaseFragment

object ContinueUtil {
    /**
     * 通过此参数避免多次接续，规避类似6187772的问题
     * */
    var continued = false
    /**
     * Use continuation to jump to MainActivity
     */
    @JvmStatic
    fun continueToMain(
        context: Context?,
        extra: String,
        @StringRes stringId: Int?,
        todoId: String? = null,
        forceReminder: Boolean = false
    ) {
        context ?: return
        val intent = Intent(SceneCardConstants.ACTION_ALL_NOTE)
        intent.putExtra(SceneCardConstants.ACTION_FROM, extra)
        if (stringId != null) {
            intent.putExtra(TodoBaseFragment.EXTRA_DESCRIPTION, context.getString(stringId))
        }
        if (todoId != null) {
            intent.putExtra(TodoBaseFragment.EXTRA_TODO_ID, todoId)
        }
        if (forceReminder) {
            intent.putExtra(SceneCardConstants.FORCE_REMINDER, true)
        }
        intent.`package` = context.packageName
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            OplusIntentAdapter.setOplusFlags(
                intent,
                TodoBaseFragment.OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED
                        or TodoBaseFragment.OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY
            )
        }
        continued = true
        context.startActivity(intent)
    }
}