/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.main

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Resources.getSystem
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.annotation.MainThread
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.NoteRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.slideview.COUISlideView
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.TodoItem
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.TodoCardMainFragmentBinding
import com.oplus.note.scenecard.todo.TodoListActivity
import com.oplus.note.scenecard.todo.speech.SpeechServiceManager
import com.oplus.note.scenecard.todo.ui.animation.TitleBarAnimationHelper
import com.oplus.note.scenecard.todo.ui.animation.TodoCustomItemAnimator
import com.oplus.note.scenecard.todo.ui.animation.TodoListAnimationManager
import com.oplus.note.scenecard.todo.ui.animation.rolltext.RollingTextView
import com.oplus.note.scenecard.todo.ui.controller.AsrStateController
import com.oplus.note.scenecard.todo.ui.controller.AsrStateController.Companion.FLAG_MICROPHONE
import com.oplus.note.scenecard.todo.ui.controller.AsrStateController.Companion.FLAG_NETWORK
import com.oplus.note.scenecard.todo.ui.controller.TodoFragmentsManager
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController
import com.oplus.note.scenecard.todo.ui.fragment.TodoBaseFragment
import com.oplus.note.scenecard.todo.ui.view.CircleButtonView
import com.oplus.note.scenecard.todo.ui.view.CreateButtonPanelView
import com.oplus.note.scenecard.todo.ui.view.NoteAppbarLayout
import com.oplus.note.scenecard.todo.ui.view.NoteSlideView
import com.oplus.note.scenecard.todo.ui.view.SurpriseView
import com.oplus.note.scenecard.utils.PointUtil
import com.oplus.note.scenecard.utils.ReflectionUtils
import com.oplus.note.scenecard.utils.VibrateUtils
import com.oplus.note.utils.toast
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date

@RequiresApi(Build.VERSION_CODES.R)
class TodoListFragment : TodoBaseFragment() {

    companion object {
        fun newInstance(): TodoListFragment = TodoListFragment()
        private const val ACTION_REFRESH_FLAMINGO_LIST = "com.oplus.note.intent.action.refresh_flamingo_list"
        const val TAG = "TodoListFragment"
        private const val FLOAT_TITLE = 1000
        private const val ANIMATION_DELAY = 300L
        private const val SHOW_MASK_VIEW_DELAY = 300L
        private const val NOTIFY_DELAY_WHEN_CREATE = 500L
        const val CREATE_ID = "create_id"
        const val NEW_ID = "new_id"
        const val DEFAULT_ALPHA = 1.0f
        const val DEFAULT_SCALE = 1.0f
        const val ALPHA_LIMIT = 0.9
        const val DEFAULT_PIVOTY = 0F
        const val LIMIT_SCALE = 0.9F
        const val LIMIT_ALPHA = 0.7F
        const val ONE = 1
        const val ZERO = 0
        const val TWO = 2
        private const val AUTO_LIMIT = 25F

        var hasCreatedTodo = false
    }

    lateinit var mAdapter: TodoListAdapter
    lateinit var mLayoutManager: LinearLayoutManager
    lateinit var mRecyclerView: NoteRecyclerView
    lateinit var titleBarAnimationHelper: TitleBarAnimationHelper
    private lateinit var mAsrButton: CircleButtonView
    private lateinit var mTodoCount: RollingTextView
    private lateinit var todoTitle: TextView
    private var binding: TodoCardMainFragmentBinding? = null
    private var mEmptyLayout: View? = null
    private var headMaskView: ImageView? = null
    private val mTodoListViewModel by viewModels<TodoListViewModel>({ requireActivity() })
    private var mLastClickedHolder: TodoListAdapter.SlideViewHolder? = null
    private var isFirstIn = true
    private var mAsrStateController: AsrStateController? = null
    private var mNetworkEnable = true
    private var mMicroPhoneEnable = true
    private var mAsrStateCallback: AsrStateController.AsrStateCallback? = null
    private lateinit var speechServiceManager: SpeechServiceManager
    private var mAsrEnable = false
    private var autoLimit = AUTO_LIMIT
    private var fastScroll = true
    private var overScroll = false
    private var isShowingSurpriseView = false
    //当显示空页面的时候，是否需要检查 显示激励页面/动画 的条件
    private var needShowAllDoneSurprise = false
    private var surpriseJob: Job? = null

    private val alarmReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            AppLogger.BASIC.d(TAG, "alarmReceiver onReceive")
            val stringExtra = intent?.getStringExtra("localId")
            mAdapter.notifyItemChangedByLocalId(stringExtra)
        }
    }

    private val mTimeChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            mTodoListViewModel.mCurrentDate.value = Date()
        }
    }

    override fun tag(): String {
        return TAG
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activity?.onBackPressedDispatcher?.addCallback(this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (binding?.surpriseView?.isVisible == true) {
                        binding?.surpriseView?.cancelAndGone()
                    } else {
                        activity?.finish()
                    }
                }
            })
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        context?.let {
            val filter = IntentFilter()
            filter.addAction(ACTION_REFRESH_FLAMINGO_LIST)
            LocalBroadcastManager.getInstance(it).registerReceiver(alarmReceiver, filter)
        }
        binding = TodoCardMainFragmentBinding.inflate(inflater, container, false)
        return binding?.root?.rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mEmptyLayout = view.findViewById(R.id.empty_layout)
        mAsrButton = view.findViewById<CircleButtonView?>(R.id.asr).also {
            val controller = AsrStateController(context)
            mNetworkEnable = controller.isNetAvailable(context)
            mMicroPhoneEnable = controller.isMicEnable()
            it.switchAsrEnable(mNetworkEnable && mMicroPhoneEnable)
        }
        headMaskView = view.findViewById(R.id.head_mask)
        initAsrButton()
        initRecyclerView(view)
        initTitleBar(view)
        initCreateButton(view)
        initAsrStateController()
        initReceiver()
        initObserves()
    }

    private fun initObserves() {
        mTodoListViewModel.mCurrentDate.observe(this) {
            AppLogger.BASIC.d(TAG, "date changed,check surprise! $it")
            val context = context ?: return@observe
            checkShowLastWeekSurprise()
            if (binding?.emptyLayoutSurprise?.isVisible == true) {
                lifecycleScope.launch(Dispatchers.IO) {
                    mTodoListViewModel.checkShowTodayAllDoneSurprise(context) { needSurprise: Boolean, _: Boolean ->
                        AppLogger.BASIC.d(TAG, "checkTodayAllDone...needSurprise=$needSurprise")
                        if (!needSurprise && binding?.emptyLayoutSurprise?.isVisible == true) {
                            showEmptyView()
                        }
                    }
                }
            }
        }
    }

    private fun initReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_TIME_CHANGED)
        filter.addAction(Intent.ACTION_DATE_CHANGED)
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED)
        activity?.registerReceiver(mTimeChangeReceiver, filter)
    }

    /**
     * 上周完成的激励动效
     */
    private fun checkShowLastWeekSurprise() {
        val context = context ?: return
        lifecycleScope.launch(Dispatchers.IO) {
            mTodoListViewModel.checkShowLastWeekSurprise(context) { type, count ->
                isShowingSurpriseView = true
                doShowSurprise(type, count)
            }
        }
    }

    @MainThread
    private fun doShowSurprise(type: String, count: Int) {
        AppLogger.BASIC.d(TAG, "doShowSurprise...type=$type,count=$count")
        surpriseJob = lifecycleScope.launch {
            delay(TodoCustomItemAnimator.REMOVE_DURATION)
            withContext(Dispatchers.Main) {
                (activity as? TodoListActivity)?.setPanelViewVisibility(false)
                binding?.surpriseView?.showAnimation(
                    type,
                    count,
                    object : SurpriseView
                    .AnimationCallback {
                        override fun onEnd() {
                            (activity as? TodoListActivity)?.setPanelViewVisibility(mAsrEnable)
                            isShowingSurpriseView = false
                        }
                    })
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        surpriseJob?.cancel()
        TodoListAnimationManager.release()
        mAsrStateController?.unRegister()
        mAsrStateCallback = null
        context?.let {
            LocalBroadcastManager.getInstance(it).unregisterReceiver(alarmReceiver)
        }
        activity?.unregisterReceiver(mTimeChangeReceiver)
    }

    override fun onResume() {
        super.onResume()
        TodoListAnimationManager.resetButtonStatusIfNeed()
        TodoListAnimationManager.playCompleteLottieIfNeed()
    }

    override fun onPause() {
        super.onPause()
        binding?.eavNoPermissionSurprise?.cancelAnimation()
    }

    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("StringFormatMatches")
    private fun initRecyclerView(view: View) {
        mRecyclerView = view.findViewById(R.id.recycler)
        kotlin.runCatching {
            mRecyclerView.setOnOverScrollListener(object : NoteRecyclerView.OverScrollListener {
                override fun onOverScroll(scrollX: Int, scrollY: Int) {
                    mAdapter.setItemScale()
                    titleBarAnimationHelper.onRecyclerViewOverScroll(scrollX, scrollY)
                }
            })
        }.onFailure {
            AppLogger.BASIC.d(TAG, "err: $it")
        }
        todoTitle = view.findViewById(R.id.item_count)
        todoTitle.fontVariationSettings = "'wght' $FLOAT_TITLE"
        mTodoCount = view.findViewById(R.id.todo_count)
        autoLimit = context?.resources?.getDimension(R.dimen.dimen_scroll_limit) ?: autoLimit
        mRecyclerView.apply {
            val ctx = activity ?: context
            mAdapter = TodoListAdapter(ctx, mRecyclerView)
            mLayoutManager = LinearLayoutManager(activity)
            layoutManager = mLayoutManager
            adapter = mAdapter
            setDispatchEventWhileScrolling(true)
            val animator = TodoCustomItemAnimator()
            animator.setItemAnimatorListener(object : TodoCustomItemAnimator.OnAnimatorListener {
                override fun onAnimatorEnd() {
                    mAdapter.apply {
                        mAdapter.mLastSlideViewWithStatusOn?.shrink()
                        setAutoFix(fastScroll = false, overScroll = false, true)
                    }
                }
            })
            itemAnimator = animator
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    mAdapter.setItemScale()
                }

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                        mAdapter.setAutoFix(fastScroll, overScroll, false)
                        if (fastScroll) {
                            fastScroll = false
                        }
                        mAdapter.setItemScale()
                        overScroll = false
                    } else {
                        kotlin.runCatching {
                            overScroll =
                                ReflectionUtils.getPrivateMethodValue(mRecyclerView, "isOverScrolling") as Boolean
                            AppLogger.BASIC.d(TAG, "overScroll $overScroll")
                        }
                    }
                }
            })
            onFlingListener = object : RecyclerView.OnFlingListener() {
                override fun onFling(velocityX: Int, velocityY: Int): Boolean {
                    fastScroll = true
                    return false
                }
            }
        }
        //recyclerview card有内容，且还需要做动画，加了post，初步绘制的时候，取到的高度是0，导致添加卡片之后，底部先绘制好，
        mRecyclerView.postDelayed({ mAdapter.addFooter = true }, ANIMATION_DELAY)

        activity?.apply {
            mAdapter.setButtonView((this as TodoListActivity).findViewById(R.id.create_panel))
        }

        mAdapter.setTodoItemHandListener(object : TodoItemHandListener {
            override fun onItemClick(
                holder: TodoListAdapter.SlideViewHolder,
                view: View,
                todoItem: TodoItem
            ) {
                activity?.apply {
                    PointUtil.setCardEventClickItem(this)
                }
                AppLogger.BASIC.d(TAG, "onClick TodoItem=${todoItem.localId}")
            }

            override fun done(todoItem: TodoItem, index: Int) {
                //最后一个被完成，不去查询数据库因为怕有延迟。另一个原因怕云同步同时操作
                needShowAllDoneSurprise = mAdapter.mTodoItems.size == 1 && mAdapter.mTodoItems[0].localId == todoItem.localId
                //prepare redo
                mTodoListViewModel.doneTodo(todoItem) {
                    AppLogger.BASIC.d(TAG, "done $it,needShow=$needShowAllDoneSurprise")
                    mRecyclerView.post {
                        checkShowMileStone()
                        mAdapter.createButtonPanelView?.animateIn()
                    }
                    context?.let { context ->
                        PointUtil.setCardEventDoneBySlide(context)
                    }
                }
            }

            override fun delete(todoItem: TodoItem, index: Int) {
                needShowAllDoneSurprise = mAdapter.mTodoItems.size == 1 && mAdapter.mTodoItems[0].localId == todoItem.localId
                mTodoListViewModel.deleteTodo(todoItem) {
                    mAdapter.createButtonPanelView?.animateIn()
                    AppLogger.BASIC.d(TAG, "delete $it,needShow=$needShowAllDoneSurprise")
                }
            }
        })
        mAdapter.setLayoutManager(mLayoutManager)
        mAdapter.slideChangeListener = object : TodoListAdapter.OnSlideStatusChangeListener {
            override fun onSlideStatusChange(view: View, status: Int) {
                val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()
                val firstItemView = mLayoutManager.findViewByPosition(firstVisibleItemPosition)
                if (view == firstItemView && (view as NoteSlideView).canDelete) {
                    if (status != COUISlideView.OnSlideListener.SLIDE_STATUS_OFF) {
                        headMaskView?.removeCallbacks(delayShowMaskRunnable)
                        headMaskView?.visibility = View.GONE
                    } else {
                        delayShowMask()
                    }
                } else {
                    delayShowMask()
                }
            }

            fun delayShowMask() {
                if (headMaskView?.visibility == View.GONE) {
                    headMaskView?.postDelayed(delayShowMaskRunnable, SHOW_MASK_VIEW_DELAY)
                }
            }
        }
        mTodoListViewModel.todoItems?.apply {
            observe(viewLifecycleOwner) {
                AppLogger.BASIC.d(TAG, "observe item change size ${it.size} hasCreatedTodo $hasCreatedTodo")
                if (!hasCreatedTodo) {
                    updateTodoList(it)
                } else {
                    if (it.isEmpty()) {
                        checkTodayAllDone()
                    }
                }
            }
        }
    }

    /**
     * 只检查里程碑动效
     */
    private fun checkShowMileStone() {
        lifecycleScope.launch(Dispatchers.IO) {
            AppLogger.BASIC.d(TAG, "checkShowMileStone in")
            //里程碑
            mTodoListViewModel.checkShowMileStone { type, count ->
                isShowingSurpriseView = true
                doShowSurprise(type, count)
            }
        }
    }

    private val delayShowMaskRunnable = {
        headMaskView?.visibility = View.VISIBLE
    }

    private fun updateTodoList(it: List<TodoItem>) {
        AppLogger.BASIC.d(TAG, "updateTodoList size ${it.size}")
        if (it.isEmpty()) {
            if (mAdapter.mTodoItems.isNotEmpty()) {
                mRecyclerView.postDelayed({
                    checkTodayAllDone()
                }, TodoCustomItemAnimator.REMOVE_DURATION)
            } else {
                checkTodayAllDone()
            }
        } else {
            showContentView()
        }
        context?.let {
            TodoResourceController.loadColorIndexToSp(it)
        }
        val list = mTodoListViewModel.updateTodoColorIndexList(it)
        val diffCallback = TodoItemDiffCallback(mAdapter.mTodoItems, list)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        mAdapter.setData(list)
        diffResult.dispatchUpdatesTo(mAdapter)
        //最后一个item不断完成，不会自动触发itemscale
        mAdapter.setItemScale()
        updateTodoTitleMaxWidth(it.size)
        mTodoCount.setText(it.size)
    }

    /**待办标题在多语言超长时的处理，要让数字显示出来，需要给标题设置 maxWidth */
    private fun updateTodoTitleMaxWidth(todoCount: Int) {
        mTodoCount.apply {
            val nowWidth = getTextMaxWidth(getText())
            val nextWidth = getTextMaxWidth(todoCount)
            val titleMaxWidth = resources.displayMetrics.widthPixels - Math.max(
                nowWidth,
                nextWidth
            ) - resources.getDimensionPixelSize(R.dimen.todo_title_total_margin)
            todoTitle.maxWidth = titleMaxWidth.toInt()
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun checkTodayAllDone() {
        AppLogger.BASIC.d(TAG, "checkTodayAllDone in...needShowAllDoneSurprise=$needShowAllDoneSurprise")
        val context = context
        if (context == null) {
            AppLogger.BASIC.d(TAG, "checkTodayAllDone context == null")
            showEmptyView()
            return
        }
        val fromCard = needShowAllDoneSurprise
        lifecycleScope.launch(Dispatchers.IO) {
            mTodoListViewModel.checkShowTodayAllDoneSurprise(context) { needSurprise: Boolean, needRun: Boolean ->
                AppLogger.BASIC.d(TAG, "checkTodayAllDone...need=$needSurprise,run=$needRun")
                if (needSurprise) {
                    showEmptyViewLottie(fromCard, needRun)
                } else {
                    showEmptyView()
                }
            }
        }
        needShowAllDoneSurprise = false
    }

    private fun initAsrStateController() {
        mAsrStateController = AsrStateController(context)
        mAsrStateCallback = object : AsrStateController.AsrStateCallback {
            override fun onStateChanged(@AsrStateController.AsrStateFlag flag: Int, enable: Boolean) {
                AppLogger.BASIC.d(TAG, "onStateChanged:$flag =>$enable")
                when (flag) {
                    FLAG_NETWORK -> mNetworkEnable = enable
                    FLAG_MICROPHONE -> mMicroPhoneEnable = enable
                    else -> {
                        // This branch should not be executed normally
                        AppLogger.BASIC.w(TAG, "receive$flag with $enable , error.")
                    }
                }
                mAsrButton.switchAsrEnable(mNetworkEnable && mMicroPhoneEnable)
            }
        }
        mAsrStateController?.register(mAsrStateCallback)
    }

    private fun showContentView() {
        AppLogger.BASIC.d(TAG, "showContentView...")
        mRecyclerView?.isVisible = true
        mEmptyLayout?.isVisible = false
        binding?.emptyLayoutSurprise?.isVisible = false
    }

    private fun showEmptyView() {
        AppLogger.BASIC.d(TAG, "showEmptyView...")
        if (mEmptyLayout?.isVisible == true) {
            AppLogger.BASIC.d(TAG, "showEmptyView already showing")
            return
        }
        mRecyclerView?.isVisible = false
        mEmptyLayout?.isVisible = true
        binding?.emptyLayoutSurprise?.isVisible = false
        mAdapter.createButtonPanelView?.animateIn()
        initEmptyViewColors()
        titleBarAnimationHelper.checkAndShowAppbar()
    }

    /**
     * @param fromCard 是否是在副屏的操作
     * @param needRun 是否需要执行激励动效
     */
    private fun showEmptyViewLottie(fromCard: Boolean, needRun: Boolean) {
        AppLogger.BASIC.d(TAG, "showEmptyViewLottie in...")
        if (binding?.emptyLayoutSurprise?.isVisible == true) {
            AppLogger.BASIC.d(TAG, "showEmptyViewLottie already showing")
            return
        }

        mRecyclerView.isVisible = false
        mEmptyLayout?.isVisible = false
        binding?.emptyLayoutSurprise?.isVisible = true
        mAdapter.createButtonPanelView?.animateIn()
        initEmptyViewColors()
        titleBarAnimationHelper.checkAndShowAppbar()
        if (fromCard && needRun && !isShowingSurpriseView) {
            binding?.ivAllDone?.isVisible = false
            binding?.eavNoPermissionSurprise?.isVisible = true
            binding?.eavNoPermissionSurprise?.playAnimation()
            binding?.eavNoPermissionSurprise?.addAnimatorListener(object : AnimatorListenerAdapter() {
                override fun onAnimationCancel(animation: Animator) {
                    super.onAnimationCancel(animation)
                    AppLogger.BASIC.d(TAG, "binding.eavNoPermissionSurprise cancel")
                    binding?.ivAllDone?.isVisible = true
                    binding?.eavNoPermissionSurprise?.isVisible = false
                }

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    binding?.ivAllDone?.isVisible = true
                    binding?.eavNoPermissionSurprise?.isVisible = false
                }
            })
        } else {
            binding?.ivAllDone?.isVisible = true
            binding?.eavNoPermissionSurprise?.isVisible = false
        }
    }

    private fun initEmptyViewColors() {
        //随机当前的index
        TodoResourceController.resetRandomIndex()
    }

    fun isAsrEnabled(): Boolean {
        if (mNetworkEnable.not()) {
            toast(R.string.network_unavailable)
            return false
        }
        if (mMicroPhoneEnable.not()) {
            toast(R.string.scene_microphone_occupied)
            return false
        }
        if (mAsrEnable.not()) {
            AppLogger.BASIC.d(TAG, "not support asr.")
            return false
        }
        return true
    }

    fun enterCreateWithAnimation(exportSpeechLanguage: String? = null): Boolean {
        if (!isAsrEnabled()) {
            return true
        }
        VibrateUtils.execVibrate(activity)
        TodoFragmentsManager.switchListToCreate(Bundle().apply {
            putString(ISpeechService.SPEECH_LANGUAGE, exportSpeechLanguage)
        })
        setFragmentResultListener(CREATE_ID) { _, bundle ->
            mRecyclerView.postDelayed({
                hasCreatedTodo = false
                mTodoListViewModel.todoItems?.value?.let {
                    updateTodoList(it)
                }
            }, NOTIFY_DELAY_WHEN_CREATE)
            kotlin.runCatching {
                val createId = bundle.getString(NEW_ID)
                mAdapter.highLightAnimationHelper.checkNeedScrollItem(createId, mRecyclerView, mAdapter, mLayoutManager)
            }
        }
        AppLogger.BASIC.d(TAG, "enter with animation")
        context?.let {
            PointUtil.setCardEventClickCreateBtn(it)
        }
        binding?.eavNoPermissionSurprise?.cancelAnimation()
        return true
    }

    private fun initCreateButton(view: View) {
        mAsrButton.setOnClickListener {
            enterCreateWithAnimation(null)
        }
        mAsrButton.setOnLongClickListener {
            enterCreateWithAnimation(null)
        }
        val asrButtonForAnimation = view.findViewById<CircleButtonView>(R.id.asr_for_animation)
        TodoListAnimationManager.setButton(asrButtonForAnimation, mAsrButton, mAsrEnable)
        TodoListAnimationManager.setLottieView(view.findViewById(R.id.lottie_view))
    }

    private fun initAsrButton() {
        speechServiceManager = SpeechServiceManager()
        mAsrEnable = if (SpeechServiceAgentFactory.isBreeno()) {
            SpeechServiceAgentFactory.isSupportBreenoOrAzure(mAsrButton.context)
        } else {
            SpeechServiceAgentFactory.isSupportGoogle(mAsrButton.context)
        }
        if (activity is TodoListActivity) {
            activity?.findViewById<CreateButtonPanelView>(R.id.create_panel)?.visibility = if (mAsrEnable) View.VISIBLE else View.GONE
        }
    }

    private fun initTitleBar(view: View) {
        val appbar = view.findViewById<NoteAppbarLayout>(R.id.appbar_layout)
        titleBarAnimationHelper = TitleBarAnimationHelper(appbar, mRecyclerView)
    }

    fun getFirstPosition(): Int {
        return mLayoutManager.findFirstVisibleItemPosition()
    }


    interface TodoItemHandListener {
        fun onItemClick(holder: TodoListAdapter.SlideViewHolder, view: View, todoItem: TodoItem)
        fun done(todoItem: TodoItem, index: Int)
        fun delete(todoItem: TodoItem, index: Int)
    }


    val Int.dp: Int get() = (this / getSystem().displayMetrics.density).toInt()
    val Int.px: Int get() = (this * getSystem().displayMetrics.density).toInt()
}