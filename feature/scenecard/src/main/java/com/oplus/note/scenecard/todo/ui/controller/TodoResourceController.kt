/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.controller

import android.content.Context
import androidx.annotation.ColorRes
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.utils.ToDoSharedPreferenceUtil
import com.oplus.note.repo.todo.TodoItem
import kotlin.random.Random

object TodoResourceController {
    private const val TAG = "TodoResourceController"

    private val mColorList = intArrayOf(
        R.color.todo_bg_color_1,
        R.color.todo_bg_color_2,
        R.color.todo_bg_color_3,
        R.color.todo_bg_color_4
    )

    private val mOverDueColorList = intArrayOf(
        R.color.todo_bg_color_overdue1,
        R.color.todo_bg_color_overdue2,
        R.color.todo_bg_color_overdue3,
        R.color.todo_bg_color_overdue4
    )

    private val mAlarmList = intArrayOf(
        R.drawable.alarm_icon_1,
        R.drawable.alarm_icon_2,
        R.drawable.alarm_icon_3,
        R.drawable.alarm_icon_4
    )


    private val mHomePageTextColorList = intArrayOf(
        R.color.todo_list_text_color_1,
        R.color.todo_list_text_color_2,
        R.color.todo_list_text_color_3,
        R.color.todo_list_text_color_5,
        R.color.todo_list_text_color_7,
        R.color.todo_list_text_color_8
    )


    @Volatile
    private var mCurrentIndex = -1

    init {
        AppLogger.BASIC.d(TAG, "init >>> mCurrentIndex=$mCurrentIndex")
    }

    @Synchronized
    fun saveColorIndexToSp(context: Context) {
        AppLogger.BASIC.d(TAG, "saveIndexToSp >>> mCurrentIndex=$mCurrentIndex")
        ToDoSharedPreferenceUtil.setColorIndexSP(context, mCurrentIndex)
    }

    @Synchronized
    fun loadColorIndexToSp(context: Context) {
        if (mCurrentIndex < 0) {
            mCurrentIndex = ToDoSharedPreferenceUtil.getColorIndexSP(context)
            if (mCurrentIndex < 0 || (mCurrentIndex >= mColorList.size)) {
                resetRandomIndex()
            }
            AppLogger.BASIC.d(TAG, "bindDataAndColor: mCurrentIndex=$mCurrentIndex")
        }
    }

    @Synchronized
    fun resetRandomIndex() {
        mCurrentIndex = Random.nextInt(0, mColorList.size)
        AppLogger.BASIC.d(TAG, "resetRandomIndex: mCurrentIndex=$mCurrentIndex")
    }

    @Synchronized
    @ColorRes
    fun getDetailPageTextColor(index: Int): Int {
        AppLogger.BASIC.d(TAG, "getDetailPageTextColor: mCurrentIndex=$mCurrentIndex")
        return mHomePageTextColorList[checkColorIndexLegal(index)]
    }

    fun getDataColor(item: TodoItem): Int {
        AppLogger.BASIC.d(TAG, "getDataColor : ${item.localId}, colorIndex=${item.colorIndex}")
        return if (item.colorIndex in mColorList.indices) {
            mColorList[item.colorIndex]
        } else {
            mColorList[getColorIndexAndMoveToNext()]
        }
    }

    fun getOverDueColor(item: TodoItem): Int {
        AppLogger.BASIC.d(TAG, "getDataColor : ${item.localId}, colorIndex=${item.colorIndex}")
        return if (item.colorIndex in mColorList.indices) {
            mOverDueColorList[item.colorIndex]
        } else {
            mOverDueColorList[getColorIndexAndMoveToNext()]
        }
    }

    fun getAlarmIcon(item: TodoItem): Int {
        AppLogger.BASIC.d(TAG, "getDataColor : ${item.localId}, colorIndex=${item.colorIndex}")
        return if (item.colorIndex in mColorList.indices) {
            mAlarmList[item.colorIndex]
        } else {
            mAlarmList[getColorIndexAndMoveToNext()]
        }
    }

    /**
     * 检查 index 是否是合法的
     * 1.如果index未初始化，则赋予当前的值
     * 2.如果后期颜色list数量减少，那么超出部分的index就会安装当前 mCurrentIndex 顺序往后排
     * @param index 旧的index
     * @return 修正后的index，可能变化，也可能本身就是正确的则不变化
     */
    @Synchronized
    fun checkColorIndexLegal(index: Int): Int {
        return if (index < 0) {
            return getColorIndexAndMoveToNext()
        } else if (index > mColorList.size - 1) {
            getColorIndexAndMoveToNext()
        } else {
            index
        }
    }

    fun getColorIndexMax(): Int {
        return mColorList.size - 1
    }

    /**
     * 返回当前的颜色index后index向后移动以为
     */
    @Synchronized
    private fun getColorIndexAndMoveToNext(): Int {
        if (mCurrentIndex < 0) {
            mCurrentIndex = 0
        }
        val index = mCurrentIndex
        moveToNextIndex()
        AppLogger.BASIC.d(TAG, "getColorIndexAndMoveToNext: index=$index,mCurrentIndex=$mCurrentIndex")
        return index
    }

    @Synchronized
    private fun moveToNextIndex() {
        if (mCurrentIndex == mColorList.size - 1) {
            mCurrentIndex = 0
        } else {
            mCurrentIndex++
        }
    }
}