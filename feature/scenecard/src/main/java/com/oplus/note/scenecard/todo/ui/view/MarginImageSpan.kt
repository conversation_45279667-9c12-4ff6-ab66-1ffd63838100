/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - MarginImageSpan.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2023/5/1      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.DynamicDrawableSpan
import android.text.style.ImageSpan
import java.lang.ref.WeakReference

class MarginImageSpan : ImageSpan {

    private var marginLeft = 0
    private var marginRight = 0

    constructor(
        context: Context,
        resId: Int,
        verticalAlignment: Int = DynamicDrawableSpan.ALIGN_BOTTOM,
        marginLeft: Int = 0,
        marginRight: Int = 0
    ) : super(context, resId, verticalAlignment) {
        this.marginLeft = marginLeft
        this.marginRight = marginRight
    }

    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        return marginLeft + super.getSize(paint, text, start, end, fm) + marginRight
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val b: Drawable = getCachedDrawable()
        canvas.save()
        var transY = bottom - b.bounds.bottom
        if (mVerticalAlignment == ALIGN_BASELINE) {
            transY -= paint.fontMetricsInt.descent
        } else if (mVerticalAlignment == ALIGN_CENTER) {
            transY = top + (bottom - top) / 2 - b.bounds.height() / 2
        }
        canvas.translate(x + marginLeft, transY.toFloat())
        b.draw(canvas)
        canvas.restore()
    }

    private var mDrawableRef: WeakReference<Drawable>? = null
    private fun getCachedDrawable(): Drawable {
        val wr: WeakReference<Drawable>? = mDrawableRef
        var d: Drawable? = null
        if (wr != null) {
            d = wr.get()
        }
        if (d == null) {
            d = drawable
            mDrawableRef = WeakReference(d)
        }
        return d!!
    }
}