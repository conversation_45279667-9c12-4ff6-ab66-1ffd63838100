/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AlphaAnim
 ** Description:
 **         v1.0:    AlphaAnim
 **
 ** Version: 1.0
 ** Date: 2023/02/22
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<EMAIL>       2023/2/22   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.anim

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.view.View
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.note.logger.AppLogger
import kotlin.math.abs

class AlphaAnim {

    var viewAnim: ValueAnimator? = null

    fun createAlphaAnim(
        show: Boolean,
        view: View?,
        duration: Long
    ): AlphaAnim {
        if (view == null) {
            return this
            AppLogger.BASIC.e(TAG, "createAlphaAnim:empty view")
        }
        val cur = if (show) 0f else 1f
        val dst = if (show) 1f else 0f
        val animator = ValueAnimator.ofFloat(cur, dst)
        animator.duration = abs(duration)
        animator.interpolator = COUIEaseInterpolator()
        animator.addUpdateListener { anim ->
            val value = anim.animatedValue as Float
            view?.alpha = value
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                if (show) {
                    view?.visibility = View.VISIBLE
                }
            }

            override fun onAnimationEnd(animation: Animator) {
                if (!show) {
                    view?.visibility = View.GONE
                }
            }
        })
        viewAnim = animator
        return this
    }

    fun buildListener(listener: Animator.AnimatorListener?): AlphaAnim {
        listener?.let {
            viewAnim?.addListener(it)
        }
        return this
    }

    fun startAnim() {
        viewAnim?.start()
    }

    fun cancelAnim() {
        viewAnim?.cancel()
    }


    companion object {
        private const val TAG = "AlphaAnim"
    }
}