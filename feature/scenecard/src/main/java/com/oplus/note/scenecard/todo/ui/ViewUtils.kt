/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui

import android.annotation.SuppressLint
import android.graphics.Rect
import android.text.SpannableString
import android.text.style.DynamicDrawableSpan
import android.view.MotionEvent
import android.view.View
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.TodoItem
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment
import com.oplus.note.scenecard.todo.ui.view.MarginImageSpan
import com.oplus.note.scenecard.utils.DateTimeUtil

@SuppressLint("NewApi")
object ViewUtils {
    private const val TAG = "ViewUtils"
    const val FLOAT_2 = 2F
    const val SCROLL_FRACTION = 0.2F
    const val SCROLL_START_FRACTION = 0.8F

    @JvmStatic
    fun ScrollView.canScroll(): Boolean {
        val child: View = this.getChildAt(0)
        val canScroll = if (child != null) {
            val childHeight = child.height
            this.height < childHeight + this.paddingTop + this.paddingBottom
        } else {
            false
        }
        AppLogger.BASIC.d(TAG, "ScrollView.canScroll() = $canScroll")
        return canScroll
    }

    /**
     * 设置待办时间的文字内容、文字颜色、提醒图标
     */
    @JvmStatic
    fun TextView.setAlarmDateTextColorDrawableRight(
        todoItem: TodoItem,
        @ColorRes normalColorId: Int,
        @DrawableRes alarmIcon: Int,
    ) {
        AppLogger.BASIC.d(TAG, "setAlarmDateTextColorDrawableRight in...")
        val forceAlarm = todoItem.getTodoExtra()?.forceReminder == true
        val textColor = context.getColor(normalColorId)

        var imageSpan: MarginImageSpan? = null
        if (forceAlarm) {
            val dp5 = context.resources.getDimensionPixelSize(R.dimen.alarm_icon_padding_text)
            imageSpan =
                MarginImageSpan(context, alarmIcon, DynamicDrawableSpan.ALIGN_BASELINE, dp5, dp5)
        }
        // 便签Mini待办列表时间格式化
        val timeDesc = DateTimeUtil.getTimeDesc(context, todoItem.alarmTime ?: 0)
        val repeatHintStr = TodoRepoFactory.get()?.getRepeatHintStr(todoItem.getTodoExtra())
        text = if (imageSpan != null) {
            //多1个空格用于替换显示闹钟图标
            val str = "$timeDesc$repeatHintStr "
            val spannableString = SpannableString(str)
            kotlin.runCatching {
                spannableString.setSpan(imageSpan, str.length - 1, str.length, SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE)
            }.onFailure {
                AppLogger.BASIC.e(TAG, "setAlarmDateTextColorDrawableRight error ${it.message}")
            }
            spannableString
        } else {
            "$timeDesc$repeatHintStr"
        }
        contentDescription = "$timeDesc$repeatHintStr"
        setTextColor(textColor)
    }

    @JvmStatic
    fun setButtonPressFeedback(view: View, type: Int) {
        val helper = COUIPressFeedbackHelper(view, type)
        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> helper.executeFeedbackAnimator(true)
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> helper.executeFeedbackAnimator(false)
            }
            false
        }
    }

    @JvmStatic
    fun calculateScale(visibleHeight: Int, totalHeight: Int): Float {
        val fraction = visibleHeight.toFloat() / totalHeight.toFloat()
        return getScale(fraction)
    }
    @JvmStatic
    fun getBottomRect(view: View): Rect {
        val rect = Rect()
        view.getLocalVisibleRect(rect)
        return rect
    }

    @JvmStatic
    fun calculateAlpha(visibleHeight: Int, totalHeight: Int): Float {
        val fraction = visibleHeight.toFloat() / totalHeight.toFloat()
        return getAlpha(fraction)
    }

    @JvmStatic
    private fun getScale(fraction: Float): Float {
        return if (fraction > TodoListFragment.DEFAULT_SCALE) {
            TodoListFragment.DEFAULT_SCALE
        } else if (SCROLL_START_FRACTION + SCROLL_FRACTION * fraction < TodoListFragment.LIMIT_SCALE) {
            TodoListFragment.LIMIT_SCALE
        } else {
            SCROLL_START_FRACTION + SCROLL_FRACTION * fraction
        }
    }
    @JvmStatic
    @VisibleForTesting
    fun getAlpha(fraction: Float): Float {
        return if (fraction > TodoListFragment.DEFAULT_ALPHA) {
            TodoListFragment.DEFAULT_ALPHA
        } else if (fraction < TodoListFragment.LIMIT_ALPHA) {
            TodoListFragment.LIMIT_ALPHA
        } else {
            fraction
        }
    }
}