/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TextColumn.kt
** Description:
** Version: 1.0
** Date : 2023/3/27
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/3/27      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation.rolltext

import android.animation.ValueAnimator
import android.graphics.Canvas
import android.graphics.Paint
import androidx.core.animation.doOnEnd
import com.oplus.note.scenecard.todo.ui.animation.rolltext.TextManager.Companion.EMPTY_NUMBER

class TextColumn(
    private val manager: TextManager,
    private val textPaint: Paint,
    private var changeCharList: List<Int>,
    increase: Boolean,
) {

    //当前 column 最大宽度(不显示空字符串时，都用最大宽度)
    private var maxCharWidth = 0f
    //当前 column 应该显示的宽度
    var nowColumnWidth = 0f
    private var firstIsEmpty = false
    private var lastIsEmpty = false

    private val targetChar
        get() = if (changeCharList.isEmpty()) EMPTY_NUMBER else changeCharList.last()

    private var edgeDelta = 0.0

    //当前数字 index
    private var currentIndex = 0
    private var currentChar: Int = EMPTY_NUMBER
    //滚动方向，-1 向上， 1向下
    private val direction: Int

    private var animator = ValueAnimator.ofFloat(1f)
    var animationUpdateListener: OnAnimationUpdateListener? = null

    interface OnAnimationUpdateListener {
        fun onUpdate()
    }

    init {
        direction = if (increase) -1 else 1
        initChangeCharList()
        animator.addUpdateListener {
            val nextProgress = getProgress(it.animatedFraction.toDouble())
            onAnimationUpdate(nextProgress.currentIndex, nextProgress.offsetPercentage, nextProgress.progress)
            animationUpdateListener?.onUpdate()
        }
        animator.doOnEnd {
            onAnimationEnd()
        }
    }

    /**
     * @param progress 动画的进度
     * */
    private fun getProgress(progress: Double): NextProgress {
        //相对于字符序列的进度
        val sizeProgress = (changeCharList.size - 1) * progress

        //通过进度获得当前字符
        val currentCharIndex = sizeProgress.toInt()

        //求底部偏移值
        val offset = sizeProgress - currentCharIndex
        // offsetPercentage 单个数字位移的偏移 （从 -1f ~ 1f ）
        val offsetPercentage = if (offset >= 0) offset else 0.0

        return NextProgress(currentCharIndex, offsetPercentage, progress)
    }

    fun startAnimation(duration: Long, delay: Long) {
        animator.apply {
            if (isRunning) {
                cancel()
            }
            this.duration = duration
            startDelay = delay
            start()
        }
    }

    fun measure() {
        //所有数字下，最大宽度都一样，直接传个 1 去计算宽度即可
        maxCharWidth = manager.maxCharWidth(1, textPaint)
        nowColumnWidth = if (firstIsEmpty) {
            0f
        } else {
            maxCharWidth
        }
    }

    private fun initChangeCharList() {
        //没有动画的情况
        if (changeCharList.size < 2) {
            currentChar = targetChar
        }
        firstIsEmpty = changeCharList.first() == EMPTY_NUMBER
        lastIsEmpty = changeCharList.last() == EMPTY_NUMBER
        //重新计算字符宽度
        measure()
    }

    private fun onAnimationUpdate(
        currentIndex: Int,
        offsetPercentage: Double,
        progress: Double   //整体动画进度
    ) {
        //当前字符
        this.currentIndex = currentIndex
        currentChar = changeCharList[currentIndex]

        //偏移量
        edgeDelta = offsetPercentage * manager.textHeight * direction

        //从空字符到显示数字，和从显示数字到显示空字符串时，宽度宽度变化
        if (currentChar == EMPTY_NUMBER && firstIsEmpty) {
            nowColumnWidth = maxCharWidth
        } else if (lastIsEmpty && currentIndex == changeCharList.lastIndex - 1) {
            nowColumnWidth = maxCharWidth
        } else if (currentChar == EMPTY_NUMBER) {
            nowColumnWidth = 0f
        } else {
            nowColumnWidth = maxCharWidth
        }
    }

    fun onAnimationEnd() {
        currentChar = targetChar
        edgeDelta = 0.0
    }

    fun draw(canvas: Canvas) {
        val cs = canvas.save()
        val originRect = canvas.clipBounds
        canvas.clipRect(0, originRect.top, nowColumnWidth.toInt(), originRect.bottom)

        fun drawText(idx: Int, verticalOffset: Float = 0f) {

            fun charAt(idx: Int): String {
                val number = changeCharList[idx]
                return if (number != EMPTY_NUMBER) {
                    String.format("%d", number)
                } else {
                    ""
                }
            }

            if (idx >= 0 && idx < changeCharList.size) {
                //水平位移，保证数字居中
                val horizontalOffset = (nowColumnWidth - manager.charWidth(changeCharList[idx], textPaint)) / 2
                canvas.drawText(charAt(idx), horizontalOffset, verticalOffset, textPaint)
            }
        }
        drawText(currentIndex + 1, edgeDelta.toFloat() - manager.textHeight * direction)
        drawText(currentIndex, edgeDelta.toFloat())
        drawText(currentIndex - 1, edgeDelta.toFloat() + manager.textHeight * direction)

        canvas.restoreToCount(cs)
    }
}

//数字的进度
data class NextProgress(
    val currentIndex: Int,   //当前应该显示 char list 中的哪个了
    val offsetPercentage: Double, //当前 char 显示的偏移比例 （0f~1f）
    val progress: Double   // 整体动画的进度
)