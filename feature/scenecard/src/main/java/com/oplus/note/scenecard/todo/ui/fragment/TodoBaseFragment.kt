/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.TodoCardTitlebarLayoutBinding

abstract class TodoBaseFragment : Fragment() {

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        AppLogger.BASIC.d(tag(), "onCreateView")
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppLogger.BASIC.d(tag(), "onViewCreated")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AppLogger.BASIC.d(tag(), "onCreate")
    }

    override fun onResume() {
        super.onResume()
        AppLogger.BASIC.d(tag(), "onResume")
    }

    override fun onPause() {
        super.onPause()
        AppLogger.BASIC.d(tag(), "onPause")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        AppLogger.BASIC.d(tag(), "onDestroyView")
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLogger.BASIC.d(tag(), "onDestroy")
    }

    protected fun initToolBar(binding: TodoCardTitlebarLayoutBinding, title: String, textVisible: Boolean = true, iconVisible: Boolean = true) {
        setToolBarTitleIcon(binding, title, textVisible, iconVisible)
        binding.back.setOnClickListener {
            onBackClick()
        }
    }

    fun setToolBarTitleIcon(binding: TodoCardTitlebarLayoutBinding, title: String, textVisible: Boolean = true, iconVisible: Boolean = true) {
        binding.title.text = title
        binding.title.isVisible = textVisible
        binding.back.isVisible = iconVisible
    }

    fun setToolBarIconVisible(view: View?, visible: Boolean) {
        view?.findViewById<View?>(R.id.back)?.isVisible = visible
    }

    /**
     * 获取tag
     */
    abstract fun tag(): String

    open fun onBackClick() {}

    companion object {
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED = 0x10000000
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY = 0x20000000
        const val EXTRA_DESCRIPTION = "oplus.intent.extra.DESCRIPTION"
        const val EXTRA_TODO_ID = "app_todo_card_local_id"
    }
}