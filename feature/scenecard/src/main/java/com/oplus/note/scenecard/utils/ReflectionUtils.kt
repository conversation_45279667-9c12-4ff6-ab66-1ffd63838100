/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import java.lang.reflect.Field

/**
 * <AUTHOR>
 * @Date 2023/3/14
 * @Description
 */
object ReflectionUtils {
    /**
     * 获取私有成员变量的值
     * @param instance
     * @param filedName
     * @return
     */
    @JvmStatic
    @Throws(NoSuchMethodException::class, IllegalAccessException::class)
    fun getPrivateMethodValue(instance: Any, methodName: String?,): Any? {
        val method = instance.javaClass.getDeclaredMethod(methodName)
        method.isAccessible = true
        return method.invoke(instance)
    }

    /**
     * 获取私有成员变量的值
     * @param instance
     * @param filedName
     * @return
     */
    @JvmStatic
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun getSuperPrivateField(instance: Any, filedName: String?): Any {
        val field: Field = instance.javaClass.superclass.getDeclaredField(filedName)
        field.isAccessible = true
        return field.get(instance)
    }

    /**
     * 设置私有成员的值
     * @param instance
     * @param fieldName
     * @param value
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    @JvmStatic
    @Throws(NoSuchFieldException::class, IllegalAccessException::class)
    fun setSuperPrivateField(instance: Any, fieldName: String?, value: Any?) {
        val field: Field = instance.javaClass.superclass.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(instance, value)
    }
}