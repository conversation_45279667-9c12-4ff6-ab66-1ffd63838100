/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - TodoCreateFragment
 ** Description:
 **         v1.0:   Create TodoCreateFragment file
 **
 ** Version: 1.0
 ** Date: 2023/02/10
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/10   1.0      Create this module
 ********************************************************************************/

package com.oplus.note.scenecard.todo.ui.fragment

import android.Manifest
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.content.DialogInterface
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.text.TextWatcher
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.annotation.StringRes
import androidx.annotation.VisibleForTesting
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.nearme.note.util.AndroidVersionUtils
import com.nearme.note.util.ConfigUtils
import com.oplus.anim.EffectiveAnimationView
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.ISpeechServiceCallback
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.permission.AlarmPermission
import com.oplus.note.permission.BlockedDialogMessage
import com.oplus.note.permission.NotificationPermission
import com.oplus.note.permission.OverlayPermission
import com.oplus.note.permission.Permission
import com.oplus.note.permission.PermissionFactory
import com.oplus.note.permission.PermissionManager
import com.oplus.note.permission.RequestPermissionCallback
import com.oplus.note.permission.ScreenOnPermission
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.TodoCardCreateFragmentBinding
import com.oplus.note.scenecard.todo.TodoListActivity
import com.oplus.note.scenecard.todo.speech.SpeechServiceManager
import com.oplus.note.scenecard.todo.ui.animation.TodoCreateAnimationHelper
import com.oplus.note.scenecard.todo.ui.controller.TodoFragmentsManager
import com.oplus.note.scenecard.todo.ui.main.CreateState
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment.Companion.CREATE_ID
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment.Companion.NEW_ID
import com.oplus.note.scenecard.todo.ui.main.TodoListViewModel
import com.oplus.note.scenecard.todo.ui.view.*
import com.oplus.note.scenecard.utils.ContinueUtil
import com.oplus.note.scenecard.utils.SceneCardConstants.APP_TODO_CARD_REQ_OVERLAY
import com.oplus.note.scenecard.utils.PointUtil
import com.oplus.note.scenecard.utils.SceneCardConstants.APP_TODO_CARD_REQ_NOTIFICATION
import com.oplus.note.scenecard.utils.VariationFontProvider
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.repo.todo.TodoItem
import com.oplus.note.scenecard.utils.SceneCardConstants
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@RequiresApi(Build.VERSION_CODES.R)
class TodoCreateFragment : TodoBaseFragment(), InputContentController.AlarmTimeCallback {

    var content: TextView? = null
    private var scrollView: TopFadingScrollView? = null
    private var wave: EffectiveAnimationView? = null
    private var inputContainer: SpeechInputContainer? = null
    private var inputController: InputContentController? = null
    private var speechServiceManager: SpeechServiceManager? = null
    private var animationHelper: TodoCreateAnimationHelper? = null
    private var confirmEnable = false
    private var isShowEndSoon = false
    private var isShowReachLimit = false
    private var finishFromUser = false
    private var heightWatcher: TextWatcher? = null
    private var startListenTime = 0L
    private var hasUploadAlarmTimePoint = false
    private var hasToast = false
    private var hasToastMaxSize = false
    private var totalContent = StringBuilder()
    private var tempContent = ""
    private var asrState = STATE_DEFAULT
    private var isPaused = false
    private var currentTodoId = ""
    private var todoItem: TodoItem? = null
    private var permissionManager: PermissionManager? = null
    /**
     * 需要进行接续授权
     * */
    private var continueGrantPermission: Boolean = false
    /**
     * 其他界面是否进行了明确拒绝
     * */
    private var denied: Boolean = false
    private var globalPermissionListener: PermissionManager.GlobalPermissionListener? = null

    /**
     * 点击了接续的bool值
     */
    private var breakUpByPermission = false
    private var viewModel: TodoListViewModel? = null

    //正在执行退出流程的过程中不允许文字更新
    private var isExiting = false
    private var isWaiting = false
    private var binding: TodoCardCreateFragmentBinding? = null
    private val handler = Handler(Looper.getMainLooper())
    private val stopWaitingCreateRunnable = Runnable {
        stopWaitingCreate()
    }
    private val delayCreateRunnable = Runnable {
        if (!isExiting) {
            AppLogger.BASIC.d(TAG, "delayCreateRunnable  createTodo")
            createTodo()
        }
    }
    /**
     * 闹钟提醒权限弹窗接续到弹窗的bool值
     * */
    var continueToList: Boolean = false

    override fun tag(): String {
        return TAG
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        permissionManager = PermissionManager(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        activity?.let {
            viewModel = ViewModelProvider(it)[TodoListViewModel::class.java]
        }
        binding = TodoCardCreateFragmentBinding.inflate(inflater, container, false)
        content = binding?.root?.findViewById(R.id.tv_input_content)
        wave = binding?.root?.findViewById(R.id.eav_input_title_wave)
        if (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL) {
            wave?.rotationY = RTL_ROTATION
        }
        inputContainer = binding?.root?.findViewById(R.id.fl_input_content)
        scrollView = binding?.root?.findViewById(R.id.sv_input_content)
        initTitle()
        return binding?.root
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        if (savedInstanceState != null) {
            exitCreate("recreate stop")
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        content?.movementMethod = ScrollingMovementMethod()
        kotlin.runCatching {
            content?.paint?.typeface =
                    content?.let { VariationFontProvider.getSansSerifVariationFont(it, INPUT_TEXT_WEIGHT) }
        }
        animationHelper = TodoCreateAnimationHelper(requireContext(), view as ViewGroup).apply {
            startOpenAnimation()
        }
        val lang = arguments?.getString(ISpeechService.SPEECH_LANGUAGE)
        inputController =
            content?.let {
                InputContentController(
                    requireContext(),
                    tv = it,
                    lifecycle.coroutineScope
                )
            }
        inputController?.apply {
            setAlarmTimeCallback(this@TodoCreateFragment)
            confirmStatusBlock = ::updateConfirmStatus
            speechLang = lang
        }
        content?.apply {
            heightWatcher = doOnTextChanged { text, _, _, _ ->
                scrollView?.post {
                    scrollView?.fullScroll(View.FOCUS_DOWN)
                }
                val size = text?.length ?: 0
                if (!hasToast && (size >= INPUT_MAX_SIZE_REMAIN)) {
                    hasToast = true
                    Toast.makeText(context, R.string.todo_toast_content_remain, Toast.LENGTH_SHORT).show()
                }
                if (!hasToastMaxSize && (size >= INPUT_MAX_SIZE)) {
                    hasToastMaxSize = true
                    Toast.makeText(context, R.string.todo_toast_content_full, Toast.LENGTH_SHORT).show()
                    (activity as? TodoListActivity)?.autoSaveWhenContentMaxSize()
                }
            }
        }

        checkPermission { initSpeechServiceManager(lang) }
        globalPermissionListener = object : PermissionManager.GlobalPermissionListener {
            override fun onDenied(failedList: List<Permission>) {
                denied = true
            }
        }
        globalPermissionListener?.let {
            PermissionManager.registerPermissionListener(it)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        AppLogger.BASIC.d(TAG, "onAttach")
        requireActivity().onBackPressedDispatcher.addCallback(this,
                object : OnBackPressedCallback(true) {
                    override fun handleOnBackPressed() {
                        isExiting = true
                        recycle()
                        exitCreate("OnBackPressedCallback")
                        if (isWaiting) {
                            PointUtil.setCardEventStopWait(context)
                        }
                    }
                })
    }

    override fun onDetach() {
        super.onDetach()
        AppLogger.BASIC.d(TAG, "onDetach")
    }

    override fun onResume() {
        super.onResume()
        isPaused = false
        AppLogger.BASIC.d(TAG, "onResume breakUpByPermission=$breakUpByPermission")
        checkPermissionWhenResume()
    }

    override fun onStart() {
        super.onStart()
        AppLogger.BASIC.d(TAG, "onStart")
        //在这里设置isVisible是因为在onResume的时候设置还是可以闪现页面
        if (breakUpByPermission || currentTodoId.isNotBlank()) {
            binding?.rlCreateRoot?.isVisible = false
        }
    }

    private fun getRemindPermissions(forceRemind: Boolean): List<Permission> {
        return mutableListOf<Permission>().apply {
            add(PermissionFactory.create(PermissionFactory.PermissionType.TYPE_NOTIFICATION))
            if (AndroidVersionUtils.isHigherAndroidT()) {
                add(PermissionFactory.create(PermissionFactory.PermissionType.TYPE_ALARM))
                add(PermissionFactory.create(PermissionFactory.PermissionType.TYPE_SCREEN_ON))
            }
            if (forceRemind) {
                add(PermissionFactory.create(PermissionFactory.PermissionType.TYPE_OVERLAY))
            }
        }
    }

    private fun checkPermissionWhenResume() {
        val context = context ?: return
        val forceRemind = todoItem?.getTodoExtra()?.forceReminder ?: false
        if (breakUpByPermission || continueGrantPermission && !continueToList) {
            binding?.rlCreateRoot?.isVisible = !breakUpByPermission
            if (denied) {
                AppLogger.BASIC.d(TAG, "checkCanSaveWhenResume PERMISSION_DENIED")
                //无权限则弹出提示
                if (forceRemind) {
                    Toast.makeText(context, com.oplus.note.baseres.R.string.permission_alarm_toast, Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(context, com.oplus.note.baseres.R.string.permission_notification_toast, Toast.LENGTH_SHORT).show()
                }
                denied = false
                toListFragmentWithAlarm()
            } else {
                binding?.rlCreateRoot?.isVisible = true
                AppLogger.BASIC.d(TAG, "recheck permission")
                binding?.rlCreateRoot?.post {
                    checkAlarmPermission(context, forceRemind)
                }
            }
        }
        breakUpByPermission = false
        continueGrantPermission = false
        continueToList = false
        //当前需要保存的id不为空，则说明在后台进行保存了
        if (currentTodoId.isNotBlank() && !isWaiting) {
            //如果是松手后等待的场景，则在后台继续执行解析逻辑，等待onResume再进行保存
            binding?.rlCreateRoot?.isVisible = false
            setTodoResult(currentTodoId, true)
        }
    }

    fun dismissBlockDialog() {
        AppLogger.BASIC.d(TAG, "dismissBlockDialog")
        permissionManager?.dismissDialogAndFinish()
    }

    /**
     * 松手后是否需要等待
     */
    fun needWaitAsr(): Boolean {
        AppLogger.BASIC.d(TAG, "needWaitAsr...$asrState")
        return asrState >= STATE_LISTENING
    }

    /**
     * 超过5s取消等待
     */
    private fun stopWaitingCreate() {
        AppLogger.BASIC.d(TAG, "stopWaitingCreate...")
        isExiting = true
        isWaiting = false
        if (confirmEnable) {
            createTodo()
        } else {
            exitCreate("stopWaitingCreate")
        }
    }

    /**
     * 长按语音按钮松手的回调
     * @param saveContent 松手的时候手指区域在新建区域还是在取消区域
     * @param isCancelAction 如果是ACTION_CANCEL，则需要toast提示语音输入中断，未保存待办
     */
    fun onLongPressUp(saveContent: Boolean, isCancelAction: Boolean) {
        AppLogger.BASIC.d(TAG, "onLongPressUp, saveContent=$saveContent,state=$asrState,isBreakUp=$isCancelAction")
        //1.取消新建直接结束页面
        if (!saveContent) {
            isExiting = true
            exitCreate("onLongPressUp cancel create")
            if (isCancelAction && confirmEnable) {
                viewModel?.createState = CreateState.CREATE_BREAK
            }
            return
        }
        if (asrState >= STATE_ERROR) {
            isExiting = true
            exitCreate("onLongPressUp ASR Error!")
            return
        }

        //说话时间不足1s，提示toast
        if ((System.currentTimeMillis() - startListenTime < DURATION_1S) && !confirmEnable) {
            AppLogger.BASIC.d(TAG, "onLongPressUp, duration invalidate return")
            exitCreate("duration < 1s")
            context?.let {
                Toast.makeText(it, R.string.todo_speak_short, Toast.LENGTH_SHORT).show()
            }
            return
        }

        cancelWaveAnimation()
        isWaiting = true
        handler.postDelayed(stopWaitingCreateRunnable, DURATION_5S)
        lifecycleScope.launch {
            if (asrState >= STATE_RESULT) {
                //已经有数据了，等待onFinish回调
                AppLogger.BASIC.d(TAG, "onLongPressUp,already start asr, waiting onFinish...")
                endInput()
            } else {
                //还没接收到数据，则等待1s，等待hasStartASR = true
                AppLogger.BASIC.d(TAG, "onLongPressUp,didn't start asr, waiting 1s...")

                delay(DURATION_1S)

                if (asrState >= STATE_RESULT) {
                    //等待onFinish回调
                    AppLogger.BASIC.d(TAG, "onLongPressUp,asr started after waiting 1s, waiting onFinish...")
                    endInput()
                } else {
                    //结束新建流程
                    AppLogger.BASIC.d(TAG, "onLongPressUp,asr didn't start after waiting 1s, cancel create")
                    isWaiting = false
                    isExiting = true
                    handler.removeCallbacks(stopWaitingCreateRunnable)
                    viewModel?.let {
                        if (it.createState == CreateState.DEFAULT_STATE) {
                            it.createState = CreateState.EMPTY_CONTENT
                        }
                    }
                    exitCreate("onLongPressUp empty text")
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun stopAndFinish() {
        (activity as? TodoListActivity)?.completeCreateTask()
        /**
         * Using popup may generate state loss exception
         */
        TodoFragmentsManager.popBackCreate()
    }

    /**
     * Export flavor need to apply for recording permission
     * Domestic automatically decide whether or not to pop up a user notice when bundling services
     */
    private fun checkPermission(block: () -> Unit) {
        context?.let { ctx ->
            if ((SpeechServiceAgentFactory.get()?.getType() == ISpeechService.SPEECH_AZURE)
                    || (SpeechServiceAgentFactory.get()?.getType() == ISpeechService.SPEECH_GOOGLE)
            ) {
                if (ContextCompat.checkSelfPermission(
                                ctx,
                                Manifest.permission.RECORD_AUDIO
                        ) != PackageManager.PERMISSION_GRANTED
                ) {
                    AppLogger.BASIC.d(TAG, "No recording permission")
                    //Export requires record permission
                    ContinueUtil.continueToMain(context, APP_TODO_CARD_REQ_AUDIO, R.string.todo_continue_micphone)
                } else {
                    AppLogger.BASIC.d(TAG, "have recording permission")
                    block.invoke()
                }
            } else {
                block.invoke()
            }
        }
    }

    /**
     * init wave animation
     */
    @VisibleForTesting
    fun initTitle() {
        wave?.setCacheComposition(false)
        wave?.progress = 0f
    }

    private fun initSpeechServiceManager(lang: String?) {
        speechServiceManager = SpeechServiceManager()
        speechServiceManager?.apply {
            <EMAIL>?.let {
                initService(it, <EMAIL>, lang)
                registerCallback(object : ISpeechServiceCallback {
                    override fun onStartService() {
                        AppLogger.BASIC.d(TAG, "onStartService")
                        asrState = STATE_START_SERVICE
                        inputController?.onStartService {
                            (activity as? TodoListActivity)?.setPanelViewLoading()
                        } ?: kotlin.run {
                            AppLogger.BASIC.e(TAG, "inputController is null")
                        }
                        isShowEndSoon = false
                        isShowReachLimit = false
                    }

                    override fun onStartServiceFailed(msg: String?) {
                        AppLogger.BASIC.d(TAG, "onStartServiceFailed")
                        activity?.runOnUiThread {
                            asrState = STATE_START_FAILED
                            inputController?.onStartServiceFailed()
                            (activity as? TodoListActivity)?.setPanelViewError()
                            viewModel?.createState = CreateState.NETWORK_ERROR
                        }
                    }

                    override fun onStartServiceSuccess() {
                        AppLogger.BASIC.d(TAG, "onStartServiceSuccess")
                        activity?.runOnUiThread {
                            asrState = STATE_START_SUCCESS
                            inputController?.onStartServiceSuccess()
                        }
                    }

                    override fun onStartListen() {
                        AppLogger.BASIC.d(TAG, "onStartListen")
                        //callback here by azure is sub-thread
                        activity?.runOnUiThread {
                            asrState = STATE_LISTENING
                            inputController?.onStartListen()
                            showWaveAnimation()
                            startListenTime = System.currentTimeMillis()
                            (activity as? TodoListActivity)?.setPanelViewListening()
                        }
                    }

                    override fun onError(msg: String?, isNetworkError: Boolean) {
                        AppLogger.BASIC.d(TAG, "onError:$msg")
                        activity?.runOnUiThread {
                            asrState = STATE_ERROR
                            handler.removeCallbacks(stopWaitingCreateRunnable)
                            recycle()
                            cancelWaveAnimation()
                            inputController?.onError()
                            if (isWaiting) {
                                isWaiting = false
                                viewModel?.createState = CreateState.NETWORK_ERROR_WHEN_WAITING
                                createTodo()
                            } else {
                                (activity as? TodoListActivity)?.setPanelViewError()
                                viewModel?.createState = CreateState.NETWORK_ERROR
                            }
                        }
                    }

                    override fun onResult(result: String?, isFinal: Boolean) {
                        AppLogger.DEBUG.d(TAG) { "onResult:$result isFinal:$isFinal" }
                        asrState = STATE_RESULT
                        if (result != null) {
                            tempContent = if (isFinal) {
                                totalContent.append(result)
                                ""
                            } else {
                                result
                            }
                        }
                        if (!isExiting) {
                            inputController?.onSpeechServiceResult(result, isFinal)
                        }
                    }

                    override fun onFinish() {
                        AppLogger.BASIC.d(TAG, "onFinish state=$asrState")
                        if (asrState == STATE_ERROR) {
                            return
                        }
                        activity?.runOnUiThread {
                            asrState = STATE_FINISH
                            handler.removeCallbacks(stopWaitingCreateRunnable)
                            cancelWaveAnimation()
                            inputController?.onFinishResult(totalContent.toString()) {
                                isWaiting = false
                                handler.postDelayed(delayCreateRunnable, DURATION_400)
                            }
                        }
                    }

                    override fun onStopService() {
                        AppLogger.BASIC.d(TAG, "onStopService")
                        activity?.runOnUiThread {
                            wave?.progress = 0f
                            wave?.cancelAnimation()
                        }
                    }

                    override fun onResultAmplitudes(amplitudes: Int) {
                    }
                })
                this.startService()
            }
        }
    }

    fun showWaveAnimation() {
        val animationView = wave ?: return
        animationView.progress = 0f
        animationView.playAnimation()
        val alphaIn = ObjectAnimator.ofFloat(animationView, "alpha", 1F)
        val alphaOut = ObjectAnimator.ofFloat(binding?.ivWaveDefault, "alpha", 0F)
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(alphaIn, alphaOut)
            start()
        }
    }

    fun cancelWaveAnimation() {
        val animationView = wave ?: return
        animationView.progress = 0f
        animationView.cancelAnimation()
        val alphaOut = ObjectAnimator.ofFloat(animationView, "alpha", 0F)
        val alphaIn = ObjectAnimator.ofFloat(binding?.ivWaveDefault, "alpha", 1F)
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(alphaIn, alphaOut)
            start()
        }
    }

    private fun createTodo() {
        val text = if (totalContent.isNotEmpty() || tempContent.isNotEmpty()) {
            totalContent.toString() + tempContent
        } else {
            ""
        }
        if (text.isBlank()) {
            exitCreate("empty content")
            return
        }
        val time = if (inputContainer?.hasTime == true) {
            inputContainer?.getAlarmTime()
        } else {
            null
        }
        createTodoInner(text, time)
    }

    private fun createTodoInner(text: String, time: Long?) {
        TodoListFragment.hasCreatedTodo = true
        val tempTodoItem = TodoItem().apply {
            content = text
            alarmTime = time
        }
        if (time == null) {
            createTodo(tempTodoItem, forceRemind = false) {}
        } else {
            showAlertMsgPanel(tempTodoItem)
        }
    }

    private fun createTodo(
        tempTodoItem: TodoItem,
        forceRemind: Boolean,
        endBlock: (context: Context) -> Unit
    ) {
        tempTodoItem.setForceRemind(forceRemind)
        val context = context ?: return
        TodoRepoFactory.get()?.createTodo(tempTodoItem) {
            AppLogger.BASIC.d(TAG, "createTodo:$it")
            if (tempTodoItem.alarmTime == null) {
                if (it == INSERT_FAILED) {
                    TodoListFragment.hasCreatedTodo = false
                    exitCreate("createTodoInner failed")
                } else {
                    addCreatePoint(tempTodoItem.content, null)
                    exitCreate("createTodoInner without alarm")
                    setTodoResult(it)
                }
            } else {
                addCreatePoint(tempTodoItem.content, tempTodoItem.alarmTime)
                tempTodoItem.localId = it
                todoItem = tempTodoItem
                endBlock.invoke(context)
            }
        }
    }

    fun continueGrantPermission(): Boolean {
        continueGrantPermission = true
        return isWaiting && !breakUpByPermission
    }

    fun isForceRemind(): Boolean {
        return todoItem?.getTodoExtra()?.forceReminder ?: false
    }

    private fun checkAlarmPermission(context: Context, forceRemindClick: Boolean) {
        val permissions = getRemindPermissions(forceRemindClick)
        AppLogger.BASIC.d(TAG, "checkAlarmPermission $forceRemindClick")
        //直接申请通知权限
        permissionManager?.requestPermissions(permissions, object : RequestPermissionCallback {
            override fun onSuccess(successList: List<Permission>) {
                AppLogger.BASIC.d(TAG, "${successList.size},${permissions.size}")
                //全部请求成功，才进入列表页
                AppLogger.BASIC.d(TAG, "granted success")
                toListFragmentWithAlarm()
            }

            //失败了直接返回
            override fun onFailed(successList: List<Permission>, failedList: List<Permission>) {
                AppLogger.BASIC.d(TAG, "successList $successList,failedList:$failedList")
                if (forceRemindClick) {
                    Toast.makeText(context, com.oplus.note.baseres.R.string.permission_alarm_toast, Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(context, com.oplus.note.baseres.R.string.permission_notification_toast, Toast.LENGTH_SHORT).show()
                }
                toListFragmentWithAlarm()
            }

            //受阻弹窗的positive listener
            override fun interceptBlockedPermissionProcess(unRequestList: List<Permission>): Boolean {
                AppLogger.BASIC.d(TAG, "unRequestList: ${unRequestList[0]}")
                if (unRequestList.firstOrNull() is OverlayPermission) {
                    AppLogger.BASIC.d(TAG, "still need continue if there are two permissions，continue overlay")
                    //overlay 接续
                    ContinueUtil.continueToMain(
                        context, APP_TODO_CARD_REQ_OVERLAY, com.oplus.note.baseres.R.string.permission_continue_des, todoItem?.localId,
                        forceRemindClick
                    )
                    breakUpByPermission = true
                }
                if (unRequestList.firstOrNull() is NotificationPermission ||
                    unRequestList.firstOrNull() is AlarmPermission ||
                    unRequestList.firstOrNull() is ScreenOnPermission
                ) {
                    AppLogger.BASIC.d(TAG, "still need continue if there are two permissions，continue overlay")
                    //overlay 接续
                    ContinueUtil.continueToMain(
                        context, APP_TODO_CARD_REQ_NOTIFICATION, com.oplus.note.baseres.R.string.permission_continue_des, todoItem?.localId,
                        forceRemindClick
                    )
                    breakUpByPermission = true
                }
                return true
            }
            //通知权限
            override fun getBlockedDialogMessage(permission: Permission): BlockedDialogMessage {
                AppLogger.BASIC.d(TAG, "permission $permission")
                if (permission is NotificationPermission) {
                    //通知权限
                    val message = if (ConfigUtils.isToDoDeprecated) {
                        com.oplus.note.baseres.R.string.dialog_open_permission_notification_content_above_OS16
                    } else {
                        com.oplus.note.baseres.R.string.dialog_open_permission_notification_content
                    }
                    return getBlockedDialogMessage(
                            com.oplus.note.baseres.R.string.notification_permission_dialog_title, message)
                } else if (permission is AlarmPermission) {
                    //开启“闹钟和提醒”权限
                    val message = if (ConfigUtils.isToDoDeprecated) {
                        com.oplus.note.baseres.R.string.schedule_alarm_permission_dialog_msg_above_OS16
                    } else {
                        com.oplus.note.baseres.R.string.schedule_alarm_permission_dialog_msg
                    }
                    return getBlockedDialogMessage(com.oplus.note.baseres.R.string.schedule_alam_permission_dialog_title, message)
                } else if (permission is ScreenOnPermission) {
                    //开启“开启屏幕”权限
                    val message = if (ConfigUtils.isToDoDeprecated) {
                        com.oplus.note.baseres.R.string.dialog_screen_on_content_above_OS16
                    } else {
                        com.oplus.note.baseres.R.string.dialog_screen_on_content
                    }
                    return getBlockedDialogMessage(com.oplus.note.baseres.R.string.dialog_screen_on_msg, message)
                } else {
                    //开启“悬浮窗”权限
                    return getBlockedDialogMessage(
                            com.oplus.note.baseres.R.string.permission_alert_window_dialog_title,
                            com.oplus.note.baseres.R.string.permission_alert_window_dialog_clock_content)
                }
            }
        }, false)
    }

    private fun getBlockedDialogMessage(@StringRes title: Int, @StringRes message: Int): BlockedDialogMessage {
        return BlockedDialogMessage(
                title,
                message,
                com.oplus.note.baseres.R.string.setting,
                R.string.scene_btn_cancel,
                com.support.dialog.R.style.COUIAlertDialog_Bottom_Tiny,
                Gravity.BOTTOM,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary),
                Color.WHITE,
                Color.BLACK,
                com.support.dialog.R.style.Animation_COUI_Dialog)
    }

    private fun showBottomMsgDialog(
        title: Int,
        msg: Int,
        positiveBtnRes: Int,
        negativeBtnRes: Int,
        positiveButtonClickListener: (context: Context) -> Unit,
        negativeButtonClickListener: (context: Context) -> Unit
    ) {
        AppLogger.BASIC.d(TAG, "showBottomMsgDialog get context$context")
        val mContext = context ?: return
        val dialog = COUIAlertDialogBuilder(mContext, com.support.dialog.R.style.COUIAlertDialog_Bottom_Tiny)
                .setPositiveButton(positiveBtnRes) { _, _ ->
                    positiveButtonClickListener.invoke(mContext)
                }
                .setNegativeButton(negativeBtnRes) { _, _ ->
                    negativeButtonClickListener.invoke(mContext)
                }
                .setWindowGravity(Gravity.BOTTOM)
                .setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog)
                .setTitle(title)
                .setMessage(msg)
                .show()
        //点击外部，视为取消
        dialog.setOnCancelListener {
            context.apply { negativeButtonClickListener.invoke(mContext) }
        }
        val buttonView = dialog?.getButton(DialogInterface.BUTTON_POSITIVE) as? COUIButton
        buttonView?.setDrawableColor(COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorPrimary))
        buttonView?.setTextColor(Color.WHITE)
        val buttonView1 = dialog?.getButton(DialogInterface.BUTTON_NEGATIVE) as? COUIButton
        buttonView1?.setTextColor(Color.BLACK)
    }

    private fun toListFragmentWithAlarm() {
        todoItem?.localId?.apply {
            exitCreate("exit create with alarm")
            if (isPaused) {
                currentTodoId = this
            } else {
                setTodoResult(this)
            }
        }
    }

    private fun showAlertMsgPanel(tempTodoItem: TodoItem) {
        val context = context ?: return
        isWaiting = true
        continueToList = true
        showBottomMsgDialog(
            com.oplus.note.baseres.R.string.open_alarm_title,
                com.oplus.note.baseres.R.string.open_alarm_des,
                com.oplus.note.baseres.R.string.open_button,
                com.oplus.note.baseres.R.string.close_button,
                {
                    continueToList = false
                    createTodo(tempTodoItem, forceRemind = true) {
                        checkAlarmPermission(context, true)
                    }
                },
                {
                    continueToList = false
                    createTodo(tempTodoItem, forceRemind = false) {
                        checkAlarmPermission(context, false)
                    }
                })
    }

    /**
     * 保存待办成功，设置result
     */
    private fun setTodoResult(id: String, exitNow: Boolean = false) {
        AppLogger.BASIC.d(TAG, "setTodoResult")
        kotlin.runCatching {
            setFragmentResult(CREATE_ID, bundleOf(NEW_ID to id))
        }.onFailure { err ->
            AppLogger.BASIC.d(TAG, "set Result err not $err")
        }
        if (exitNow) {
            exitCreateNow()
        } else {
            exitCreate("setTodoResult")
        }
    }

    private fun addCreatePoint(text: String, time: Long?) {
        val duration = System.currentTimeMillis() - startListenTime
        context?.let {
            PointUtil.setCardEventCreateComplete(
                    it,
                    duration,
                    text.length,
                    if (time == null) {
                        0
                    } else {
                        1
                    }
            )
        }
    }

    @VisibleForTesting
    fun exitCreate(tag: String) {
        AppLogger.BASIC.d(TAG, "exit create: $tag,breakUp=$breakUpByPermission")
        isWaiting = false
        animationHelper?.startCloseAnimation {
            finishFromUser = true
            stopAndFinish()
        } ?: kotlin.run {
            AppLogger.BASIC.e(TAG, "animationHelper is null")
        }
    }

    private fun exitCreateNow() {
        AppLogger.BASIC.d(TAG, "exitCreateNow,breakUp=$breakUpByPermission")
        finishFromUser = true
        stopAndFinish()
    }

    private fun updateConfirmStatus(enable: Boolean) {
        if (enable != confirmEnable) {
            confirmEnable = enable
        }
    }

    override fun onPause() {
        super.onPause()
        isPaused = true
        animationHelper?.cancelAnimation()
        AppLogger.BASIC.d(TAG, "onPause: isWaiting=$isWaiting,breakUpByPermission=$breakUpByPermission,finishFromUser=$finishFromUser")
        if (isWaiting) {
            //如果正在等待，中断场景还是需要保存流程,所以不特殊处理
            AppLogger.BASIC.d(TAG, "onPause: isWaiting")
            return
        }
        //非等待处理场景，需要停止服务stop service
        recycle()
        //如果是松手后息屏并且息屏的时候已经执行onFinish流程了，则需要等待保存，在onResume的时候setResult
        if (handler.hasCallbacks(delayCreateRunnable)) {
            AppLogger.BASIC.d(TAG, "onPause: hasCallbacks")
            return
        }
        if (breakUpByPermission) {
            //如果是权限接续，则保留当前界面，否则直接finish，不保存数据
        } else {
            if (finishFromUser.not()) {
                //去重,在动画结束也会调用stopAndFinish，这种情况不需要在这里处理
                stopAndFinish()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        recycle()
        handler.removeCallbacks(delayCreateRunnable)
        handler.removeCallbacks(stopWaitingCreateRunnable)
        context?.let { speechServiceManager?.release(it) }
        inputController?.semanticTool?.release()
        PermissionManager.unRegisterPermissionListener(globalPermissionListener ?: return)
    }

    override fun onAdd(time: Date?) {
        AppLogger.SPEECH.d(TAG, "onAdd")
        if (inputContainer?.hasTime == false) {
            //or there might by flash
            inputContainer?.showAlarmView(time)
        } else {
            inputContainer?.updateTime(time)
        }
        if (!hasUploadAlarmTimePoint) {
            context?.let {
                PointUtil.setCardEventNLPSuccess(it, PointUtil.TYPE_NLP_SHOW)
            }
        }
        hasUploadAlarmTimePoint = true
        PointUtil.setCardEventNLPSuccess(requireContext(), PointUtil.TYPE_NLP_SHOW_AND_SAVE)
    }

    override fun onRemove() {
        AppLogger.SPEECH.d(TAG, "onRemove")
        inputController?.onRemoveAlarm()
        inputContainer?.removeAlarmView()
        PointUtil.setCardEventNLPSuccess(requireContext(), PointUtil.TYPE_NLP_SHOW_AND_DELETE)
    }

    private fun endInput() {
        AppLogger.SPEECH.d(TAG, "endInput...")
        speechServiceManager?.stopService(requireContext())
    }

    private fun recycle() {
        speechServiceManager?.cancelService(requireContext())
        speechServiceManager?.unRegisterCallback()
        inputController?.cancel()
        content?.removeTextChangedListener(heightWatcher)
        recycleWave()
    }

    private fun recycleWave() {
        wave?.apply {
            removeAllAnimatorListeners()
            progress = 0f
            cancelAnimation()
            clearAnimation()
            wave = null
        }
    }

    companion object {
        const val TAG = "TodoCreateFragment"
        fun newInstance(): TodoCreateFragment = TodoCreateFragment()
        private const val MAX_SPEECH = 15 * 1000L
        private const val SWITCH_END_SOON = 2 * 1000L
        private const val SWITCH_RECORD_LIMIT = 1 * 1000L
        private const val UNAVAILABLE_LIMIT = 5 * 1000L
        private const val DURATION_180 = 180L
        private const val DURATION_400 = 400L
        private const val DURATION_500 = 500L
        private const val DURATION_1S = 1000L
        private const val DURATION_5S = 5000L
        private const val SECOND_INTERVAL = 1000L
        private const val RTL_ROTATION = 180f
        private const val TITLE_TEXT_WEIGHT = 700
        private const val INPUT_TEXT_WEIGHT = 1000
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED = 0x10000000
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY = 0x20000000
        const val APP_TODO_CARD_REQ_AUDIO = "app_todo_card_request_audio"
        const val APP_TODO_CARD_EDIT_AUDIO = "app_todo_card_edit_audio"
        const val APP_TODO_CARD_REQ_SCHEDULE_ALARM = "app_todo_card_req_schedule_alarm"
        const val APP_TODO_CARD_REQ_SCREEN_ON = "app_todo_card_req_screen_on"
        const val APP_TODO_CARD = "app_todo_card"
        const val APP_TODO_CARD_PRIVACY_POLICY = SceneCardConstants.APP_TODO_CARD_PRIVACY_POLICY
        const val APP_TODO_CARD_USER_AGREEMENT = SceneCardConstants.APP_TODO_CARD_USER_AGREEMENT

        //google
        const val APP_TODO_CARD_REQ_AUDIO_STATE = "app_todo_card_request_audio_state"
        const val INSERT_FAILED = "insert_failed"

        //待办上限是10000字
        const val INPUT_MAX_SIZE = 10000
        const val INPUT_MAX_SIZE_REMAIN = INPUT_MAX_SIZE - 30

        const val STATE_DEFAULT = 0
        const val STATE_START_SERVICE = 1
        const val STATE_START_FAILED = 2
        const val STATE_START_SUCCESS = 3
        const val STATE_LISTENING = 4
        const val STATE_RESULT = 5
        const val STATE_FINISH = 6
        const val STATE_STOP = 7
        const val STATE_ERROR = 8
    }
}