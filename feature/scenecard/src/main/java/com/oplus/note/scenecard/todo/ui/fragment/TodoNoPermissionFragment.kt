/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.fragment

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.TodoListActivity
import com.oplus.note.scenecard.todo.ui.controller.TodoFragmentsManager
import com.oplus.note.scenecard.utils.ContinueUtil
import com.oplus.note.semantic.api.SemanticFactory
import com.oplus.note.repo.todo.TodoRepoFactory
import java.util.*

@RequiresApi(Build.VERSION_CODES.R)
class TodoNoPermissionFragment : TodoBaseFragment() {
    companion object {
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED = 0x10000000
        const val OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY = 0x20000000
        const val TAG = "TodoNoPermissionFragment"
        fun newInstance(): TodoNoPermissionFragment = TodoNoPermissionFragment()
        const val APP_TODO_CARD_REQ_PRIVACY = "app_todo_card_privacy"
        private const val TYPE_PERSON_INFORMATION = 2
        private const val TYPE_USER_AGREEMENT = 3
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.fragment_no_permission, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        checkUserPrivacyPolicy()
    }

    private fun checkUserPrivacyPolicy() {
        val activity = activity ?: return
        TodoRepoFactory.get()?.checkUserPrivacyPolicy(
            activity,
            { agree ->
                AppLogger.BASIC.d(TAG, "checkUserPrivacyPolicy,click $agree")
                if (agree) {
                  doAfterGranted()
                }
            },
            { type ->
                AppLogger.BASIC.d(TAG, "checkUserPrivacyPolicy,interceptClickLink:$type")
                when (type) {
                    TYPE_PERSON_INFORMATION -> {
                        ContinueUtil.continueToMain(
                            activity,
                            TodoCreateFragment.APP_TODO_CARD_PRIVACY_POLICY,
                            com.oplus.note.baseres.R.string.information_protection
                        )
                    }

                    TYPE_USER_AGREEMENT -> {
                        ContinueUtil.continueToMain(
                            activity,
                            TodoCreateFragment.APP_TODO_CARD_USER_AGREEMENT,
                            com.oplus.note.baseres.R.string.look_complete_user_agreement
                        )
                    }
                }
                true
            }
        )
        AppLogger.BASIC.d(TAG, "initFragment checkUserPrivacyPolicy")
    }

    private fun doAfterGranted() {
        activity?.let {
            SemanticFactory.get()?.install(it)
        }
        TodoFragmentsManager.switchPermissionToList()
        (activity as? TodoListActivity)?.setPanelViewVisibility(true)
    }

    fun checkPrivacyPolicyAgreed() {
        val activity = activity ?: return
        val agreed = TodoRepoFactory.get()?.checkGrantedPrivacyPolicy(activity) == true
        AppLogger.BASIC.d(TAG, "checkPrivacyPolicyAgreed in......agreed=$agreed")
        if (agreed) {
            //已经有权限了，则切换到列表页
            TodoRepoFactory.get()?.dismissPrivacyPolicyDialog()
            doAfterGranted()
        } else {
            (activity as? TodoListActivity)?.setPanelViewVisibility(false)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onResume() {
        super.onResume()
        checkPrivacyPolicyAgreed()
    }

    override fun tag(): String {
        return TAG
    }
}