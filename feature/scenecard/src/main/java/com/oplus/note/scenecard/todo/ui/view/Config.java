package com.oplus.note.scenecard.todo.ui.view;

import androidx.annotation.FloatRange;
import androidx.annotation.IntRange;

/**
 * <AUTHOR>
 */
public class Config {
    @IntRange(from = 2)
    public int space = 60;
    public int maxStackCount = 3;
    public int initialStackCount = 0;
    @FloatRange(from = 0f, to = 2f)
    public float secondaryScale;
    @FloatRange(from = 0f, to = 1f)
    public float scaleRatio;
    public Align align;
}
