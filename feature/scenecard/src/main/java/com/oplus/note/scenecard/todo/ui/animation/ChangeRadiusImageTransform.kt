/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - ChangeRadiusImageTransform.kt
** Description:
** Version: 1.0
** Date : 2023/4/10
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/4/10      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.transition.Transition
import android.transition.TransitionValues
import android.util.Property
import android.view.ViewGroup
import androidx.cardview.widget.CardView
import com.oplus.note.scenecard.R

@SuppressLint("NewApi")
class ChangeRadiusImageTransform : Transition() {

    init {
        addTarget(CardView::class.java)
    }

    override fun captureStartValues(transitionValues: TransitionValues?) {
    }

    override fun captureEndValues(transitionValues: TransitionValues?) {
    }

    override fun createAnimator(
        sceneRoot: ViewGroup,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        val cardView = endValues?.view as CardView
        val dp26 = cardView.context.resources.getDimension(R.dimen.dimen_26)
        val dp34 = cardView.context.resources.getDimension(R.dimen.dimen_radius)
        val startRadius = if (TodoDetailAnimationHelper.mOpenTransition) dp34 else dp26
        val endRadius = if (TodoDetailAnimationHelper.mOpenTransition) dp26 else dp34
        return ObjectAnimator.ofFloat(cardView, RadiusProperty(), startRadius, endRadius)
    }

    private class RadiusProperty : Property<CardView, Float>(Float::class.java, "cardCornerRadius") {

        override fun get(obj: CardView?): Float {
            return 0f
        }

        override fun set(obj: CardView?, value: Float?) {
            obj?.radius = value ?: 0f
        }
    }
}