/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SceneCardConstants
 ** Description:
 **         v1.0:   Create SceneCardConstants file
 **
 ** Version: 1.0
 ** Date: 2023/08/22
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/10   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.utils

object SceneCardConstants {
    //privacy policy
    const val APP_TODO_CARD_PRIVACY = "app_todo_card_privacy"
    //request audio
    const val APP_TODO_CARD_REQ_AUDIO = "app_todo_card_request_audio"
    //request notification
    const val APP_TODO_CARD_REQ_NOTIFICATION = "app_todo_card_request_notification"
    //tds id
    const val APP_TODO_ID = "app_todo_id"
    //request overlay
    const val APP_TODO_CARD_REQ_OVERLAY = "app_todo_card_request_overlay"
    //用户须知详情接续
    const val APP_TODO_CARD_PRIVACY_POLICY = "app_todo_card_privacy_policy"
    //用户协议接续
    const val APP_TODO_CARD_USER_AGREEMENT = "app_todo_card_user_agreement"
    //action from
    const val ACTION_FROM = "action_from"
    //all note action
    const val ACTION_ALL_NOTE = "action.nearme.note.allnote"
    //force remind
    const val FORCE_REMINDER = "force_reminder"
    //need permission
    const val NEED_PERMISSION = "need_permission"
}