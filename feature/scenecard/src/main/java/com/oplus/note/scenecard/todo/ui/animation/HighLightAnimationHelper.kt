/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - HighLightAnimationHelper.kt
** Description:
** Version: 1.0
** Date : 2023/6/15
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/6/15      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.view.View
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.children
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment
import com.oplus.note.scenecard.todo.ui.view.NoteSlideView

/**
 * https://odocs.myoas.com/docs/dPkpKOyRRvIKPaqO?mobileMsgShowStyle=1&pcMsgShowStyle=1
 * 高亮相关动画参数定义
 * */
@SuppressLint("NewApi")
class HighLightAnimationHelper {

    companion object {
        private const val TAG = "HighLightAnimationHelper"
        private const val SMOOTH_DELAY = 600L
        private const val NOTIFY_ITEM_DELAY = 300L
        private const val HIGHLIGHT_ANIMATION_DURATION = 100L
        private const val STAY_HIGHLIGHT_DURATION = 250L
        private const val CANCEL_HIGHLIGHT_DURATION = 500L
        private const val ANIMATION_REPEAT_COUNT = 2
        private var animationExecuteCount = 0
        const val HIGHLIGHT_CHANGE = "highlight_change"
        private const val ALPHA_VALUE_1 = 1f
        private const val ALPHA_VALUE_0_8 = 0.8f
        private const val SCROLL_SPEED = 100f
    }

    private var isAnimationRunning = false
    private var highlightAlpha = ALPHA_VALUE_1

    /**新建的待办，检查如果没有显示在屏幕中（底部置灰状态的item也不算显示在屏幕中），需要滚到对应位置，然后执行高亮动画*/
    fun checkNeedScrollItem(
        createId: String?,
        recyclerView: COUIRecyclerView,
        adapter: TodoListAdapter,
        layoutManager: LinearLayoutManager
    ) {
        recyclerView.postDelayed({
            val find = adapter.mTodoItems.find {
                it.localId == createId
            }
            val index = adapter.mTodoItems.indexOf(find)
            val firstVisibleItem = layoutManager.findFirstVisibleItemPosition()
            val lastVisibleItem = layoutManager.findLastVisibleItemPosition()
            val lastItemView = layoutManager.findViewByPosition(lastVisibleItem)
            var lastItemScaled = false
            if (lastItemView is NoteSlideView) {
                lastItemScaled = lastItemView.children.first().alpha < TodoListFragment.DEFAULT_ALPHA
            }
            var needScroll = false
            if (lastItemScaled) {
                if (index !in firstVisibleItem until lastVisibleItem) {
                    needScroll = true
                }
            } else {
                if (index !in firstVisibleItem..lastVisibleItem) {
                    needScroll = true
                }
            }
            if (needScroll) {
                recyclerView.smoothScrollToPositionAndHighLight(index)
            }
            AppLogger.BASIC.d(
                TAG, "checkNeedScrollItem index $index firstVisibleItem $firstVisibleItem " +
                        "lastVisibleItem $lastVisibleItem lastItemScaled $lastItemScaled needScroll $needScroll"
            )
        }, SMOOTH_DELAY)
    }

    /**
     * 笔记列表定位滑动
     */
    fun needScrollItem(recyclerView: COUIRecyclerView, position: Int) {
        recyclerView.smoothScrollToPositionAndHighLight(position, false)
    }
    fun requestHighLight(highlightMask: View?, isTodoList: Boolean = true) {
        if (!isTodoList) highlightAlpha = ALPHA_VALUE_0_8 /* 笔记列表高亮颜色透明度变化80%-0 */
        highlightMask?.apply {
            alpha = 0f
            visibility = View.VISIBLE
            animationExecuteCount = 0
            highLightAlphaIn(highlightMask)
        }
    }

    fun cancelHighLight(highlightMask: View?) {
        highlightMask?.visibility = View.GONE
    }

    private fun highLightAlphaIn(highlightMask: View) {
        if (isAnimationRunning) {
            return
        }
        ObjectAnimator.ofFloat(highlightMask, View.ALPHA, 0f, 1f * highlightAlpha).apply {
            duration = HIGHLIGHT_ANIMATION_DURATION
            interpolator = COUIEaseInterpolator()
            doOnStart {
                isAnimationRunning = true
            }
            doOnEnd {
                highlightMask.postDelayed({
                    highLightAlphaOut(highlightMask)
                }, STAY_HIGHLIGHT_DURATION)
            }
            start()
        }
    }

    private fun highLightAlphaOut(highlightMask: View) {
        ObjectAnimator.ofFloat(highlightMask, View.ALPHA, 1f * highlightAlpha, 0f).apply {
            duration = CANCEL_HIGHLIGHT_DURATION
            interpolator = COUIEaseInterpolator()
            doOnEnd {
                isAnimationRunning = false
                if (++animationExecuteCount >= ANIMATION_REPEAT_COUNT) {
                    cancelHighLight(highlightMask)
                } else {
                    highLightAlphaIn(highlightMask)
                }
            }
            start()
        }
    }

    private fun RecyclerView.smoothScrollToPositionAndHighLight(position: Int, isTodoList: Boolean = true) {
        //新建的 item 显示到第2项
        val scrollPosition = (position - 1).coerceAtLeast(0)

        val linearSmoothScroller = object : LinearSmoothScroller(context) {

            // 调整滚动速度
            override fun calculateSpeedPerPixel(displayMetrics: android.util.DisplayMetrics): Float {
                // 这里可以根据需要调整滚动速度，值越大滚动越慢
                return SCROLL_SPEED / displayMetrics.densityDpi
            }

            override fun getHorizontalSnapPreference(): Int {
                return SNAP_TO_START
            }

            override fun getVerticalSnapPreference(): Int {
                return SNAP_TO_START
            }

            override fun onStop() {
                super.onStop()
                //滚不到准确的位置，再定位一次
                post {
                    (layoutManager as? LinearLayoutManager)?.scrollToPositionWithOffset(scrollPosition, 0)
                }
                AppLogger.BASIC.d(TAG, "scroller onStop")
                if (isTodoList) {
                    postDelayed({
                        //使用 payLoad 参数，只执行高亮 View 的动画，避免触发 item 的 change 动画
                        adapter?.notifyItemChanged(position, HIGHLIGHT_CHANGE)
                    }, NOTIFY_ITEM_DELAY)
                }
            }
        }
        linearSmoothScroller.targetPosition = scrollPosition
        layoutManager?.startSmoothScroll(linearSmoothScroller)
    }
}