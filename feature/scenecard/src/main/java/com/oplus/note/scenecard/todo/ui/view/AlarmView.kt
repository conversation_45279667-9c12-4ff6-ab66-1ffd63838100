/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AlarmView
 ** Description:
 **         v1.0:   Create AlarmView file
 **
 ** Version: 1.0
 ** Date: 2023/02/13
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/13   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.utils.DateTimeUtil
import java.util.Date

class AlarmView(context: Context?) : RelativeLayout(context) {

    init {
        init(context)
    }

    private var date: Date? = null
    var alarmTime: TextView? = null
    var btnRemove: ImageView? = null
    var removeBlock: (() -> Unit)? = null

    private fun init(context: Context?) {
        val inflater = LayoutInflater.from(context)
        inflater.inflate(R.layout.todo_card_create_alarm_layout, this, true)
        alarmTime = findViewById(R.id.tv_alert_time)
        btnRemove = findViewById(R.id.iv_remove_alert)
        btnRemove?.setOnClickListener { close() }
    }

    @VisibleForTesting
    fun updateTime(date: Date?) {
        alarmTime?.text = DateTimeUtil.getTimeDesc(context, date?.time ?: 0L)
        this.date = date
    }

    @VisibleForTesting
    fun close() {
        removeBlock?.invoke()
    }

    fun getTime(): Long {
        return date?.time ?: 0L
    }
}