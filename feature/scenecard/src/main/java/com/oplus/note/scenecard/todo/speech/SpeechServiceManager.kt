/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SpeechAssistantManager
 ** Description:
 **         v1.0:  call ISpeechService
 **
 ** Version: 1.0
 ** Date: 2023/02/14
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/14   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.speech

import android.content.Context
import androidx.lifecycle.LifecycleCoroutineScope
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.ISpeechServiceCallback
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.BuildConfig
import com.oplus.note.scenecard.R

class SpeechServiceManager {

    private var speechService: ISpeechService? = null
    private var speechCallback: ISpeechServiceCallback? = null
    private var context: Context? = null
    private var lifeScope: LifecycleCoroutineScope? = null
    private var lang: String? = null

    fun initService(
        context: Context,
        scope: LifecycleCoroutineScope,
        lang: String?,
    ) {
        this.speechService = if (BuildConfig.isExport) {
            SpeechServiceAgentFactory.get(ISpeechService.SPEECH_GOOGLE)
        } else {
            SpeechServiceAgentFactory.get(ISpeechService.SPEECH_BREENO)
        }
        this.context = context
        this.lifeScope = scope
        this.lang = lang
        AppLogger.BASIC.d(TAG, "service is:$speechService , type = ${speechService?.getType()}, lang=$lang")
    }

    fun registerCallback(callback: ISpeechServiceCallback) {
        this.speechCallback = callback
    }

    fun startService() {
        if (lifeScope == null) {
            return
        }
        if (speechCallback == null) {
            return
        }
        context?.let {
            speechService?.initService(it, lifeScope, speechCallback, lang, it.getString(R.string.select_speech_language_hint), null, null)
        }
    }

    fun unRegisterCallback() {
        this.speechCallback = null
    }

    fun stopService(context: Context) {
        speechService?.stop(context)
    }

    fun cancelService(context: Context) {
        speechService?.cancel(context)
    }

    fun release(context: Context) {
        speechService?.release(context, lifeScope)
        speechService = null
    }

    fun isEnable(context: Context): Boolean {
        speechService = SpeechServiceAgentFactory.get()
        return speechService?.isEnable(context) ?: false
    }

    companion object {
        private const val TAG = "SpeechServiceManager"
    }
}