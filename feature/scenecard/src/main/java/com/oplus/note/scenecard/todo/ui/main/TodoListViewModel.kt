/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.main

import android.content.Context
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController
import com.oplus.note.scenecard.todo.ui.view.SurpriseView
import com.oplus.note.scenecard.utils.DateUtil
import com.oplus.note.scenecard.utils.ToDoSharedPreferenceUtil
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.repo.todo.TodoItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date

class TodoListViewModel : ViewModel() {
    companion object {
        //新建面板显示的文本内容
        const val STATE_PREPARING = 1
        const val STATE_LISTENING = 2
        const val STATE_ERROR = 3
        const val LASTWEEK_COUNT = 10
        const val MILE_STONE_50 = 50
        const val MILE_STONE_100 = 100
        const val MILE_STONE_200 = 200
        const val MILE_STONE_500 = 500
        const val MILE_STONE_1000 = 1000
        const val TAG = "TodoListViewModel"
    }

    //执行进入新建页面动效完成之后，检查是否已经松手
    var isPressCanceled = false
    @CreateState
    var createState = CreateState.DEFAULT_STATE
    val mCurrentDate = MutableLiveData(Date())

    val todoItems = TodoRepoFactory.get()?.getUndoneTodoList(mCurrentDate)

    //里程碑数量列表必须是从小到大有序排列
    val mileStoneList = mutableListOf(MILE_STONE_50, MILE_STONE_100, MILE_STONE_200, MILE_STONE_500, MILE_STONE_1000)

    @WorkerThread
    suspend fun checkShowMileStone(block: (type: String, count: Int) -> Unit) {
        val doneCount = checkMileStoneCount()
        val needShow = mileStoneList.contains(doneCount)
        AppLogger.BASIC.d(TAG, "checkShowMileStone needShow=$needShow")
        if (needShow) {
            withContext(Dispatchers.Main) {
                block.invoke(SurpriseView.TYPE_MILE_STONE, doneCount)
            }
        }
    }

    /**
     * 检查是否展示上周完成的激励动效
     */
    @WorkerThread
    suspend fun checkShowLastWeekSurprise(context: Context, block: (type: String, count: Int) -> Unit) {
        if (DateUtil.isFirstEnterCard(context)) {
            val lastWeekDone = checkLastWeekDone()
            AppLogger.BASIC.d(TAG, "checkShowLastWeekSurprise lastWeekDone=$lastWeekDone")
            if (lastWeekDone >= LASTWEEK_COUNT) {
                withContext(Dispatchers.Main) {
                    block.invoke(SurpriseView.TYPE_ACHIEVEMENT, lastWeekDone)
                }
            }
        } else {
            AppLogger.BASIC.d(TAG, "checkShowLastWeekSurprise not first")
        }
    }

    /**
     * 检查是否展今天全部完成的激励动效
     */
    @WorkerThread
    suspend fun checkShowTodayAllDoneSurprise(
        context: Context,
        block: (allDone: Boolean, needAnimation: Boolean) -> Unit
    ) {
        val todayDoneList = getTodayDoneList(DateUtil.getToday(), DateUtil.getTomorrow())
        AppLogger.BASIC.d(TAG, "checkShowTodayAllDoneSurprise,list=${todayDoneList.size}")

        if (todayDoneList.isEmpty()) {
            //今天未完成待办，不需要显示激励空页面和动画
            ToDoSharedPreferenceUtil.setTodayDoneList(context, "")
            withContext(Dispatchers.Main) {
                block.invoke(false, false)
            }
            AppLogger.BASIC.d(TAG, "checkShowTodayAllDoneSurprise,today didn't done")
            return
        }
        val todayDoneListString = todayDoneList.joinToString(",") {
            "${it.localId}-${it.finishTime}"
        }
        val alreadyDoneStr = ToDoSharedPreferenceUtil.getTodayDoneListStr(context)
        if (alreadyDoneStr.isBlank()) {
            //今天有已完成的，本地之前没有保存过已完成的数据，则直接执行激励动画
            ToDoSharedPreferenceUtil.setTodayDoneList(context, todayDoneListString)
            withContext(Dispatchers.Main) {
                block.invoke(true, true)
            }
            AppLogger.BASIC.d(TAG, "checkShowTodayAllDoneSurprise,before didn't done")
            return
        }

        //本地和之前的都不为空，则比较id和完成时间是否相同
        val isIdSame = alreadyDoneStr.equals(todayDoneListString)
        AppLogger.BASIC.d(TAG, "checkShowTodayAllDoneSurprise,same=$isIdSame")
        if (!isIdSame) {
            ToDoSharedPreferenceUtil.setTodayDoneList(context, todayDoneListString)
        }
        withContext(Dispatchers.Main) {
            block.invoke(true, !isIdSame)
        }
    }

    fun doneTodo(todoItem: TodoItem, result: (count: Int) -> Unit) {
        TodoRepoFactory.get()?.doneTodo(todoItem, result)
    }

    fun deleteTodo(todoItem: TodoItem, result: (count: Int) -> Unit) {
        TodoRepoFactory.get()?.deleteTodo(todoItem, result)
    }

    fun createTodo(todoItem: TodoItem, result: (id: String) -> Unit) {
        TodoRepoFactory.get()?.createTodo(todoItem, result)
    }

    fun updateTodoColorIndexList(todoItem: List<TodoItem>): List<TodoItem> {
        if (todoItem.isEmpty()) {
            return todoItem
        }
        val filter = todoItem.filter {
            (it.colorIndex < 0) || (it.colorIndex > TodoResourceController.getColorIndexMax())
        }
        if (filter.isEmpty()) {
            return todoItem
        }
        filter.reversed().forEach {
            it.colorIndex = TodoResourceController.checkColorIndexLegal(it.colorIndex)
        }
        TodoRepoFactory.get()?.updateTodoColorIndexList(filter)
        return todoItem
    }

    @WorkerThread
    fun checkAllDone(date: Date): Boolean {
        return TodoRepoFactory.get()?.checkAllDone(date) ?: false
    }

    fun getTodayDoneList(date: Date, tomorrow: Date): List<TodoItem> {
        return TodoRepoFactory.get()?.getTodayDoneList(date, tomorrow) ?: mutableListOf()
    }

    @WorkerThread
    fun checkMileStoneCount(): Int {
        return TodoRepoFactory.get()?.checkMileStoneCount() ?: 0
    }

    @WorkerThread
    fun checkLastWeekDone(): Int {
        AppLogger.BASIC.d(TAG, "checkLastWeekDone in")
        return TodoRepoFactory.get()
            ?.checkLastWeekCount(DateUtil.getFirstDayOfLastWeek(), DateUtil.getLastDayOfWeek()) ?: 0
    }
}