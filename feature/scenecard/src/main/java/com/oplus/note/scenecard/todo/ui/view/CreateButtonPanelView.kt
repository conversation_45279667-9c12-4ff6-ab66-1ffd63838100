/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.animation.AnimatorSet
import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.content.res.Configuration
import android.graphics.drawable.GradientDrawable
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.annotation.ColorRes
import androidx.annotation.IntDef
import androidx.annotation.MainThread
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.animation.COUIOutEaseInterpolator
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.LayoutCreateBottonPannelBinding
import com.oplus.note.scenecard.todo.ui.main.TodoListViewModel
import com.oplus.note.scenecard.utils.VibrateUtils
import kotlin.math.abs
import kotlin.math.sqrt

@Retention(AnnotationRetention.SOURCE)
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE_PARAMETER)
@IntDef(
    CreateButtonPanelView.STATE_DEFAULT,
    CreateButtonPanelView.STATE_PRESSED,
    CreateButtonPanelView.STATE_WAITING
)
annotation class CreatePanelState

class CreateButtonPanelView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    ConstraintLayout(context, attrs, defStyleAttr), View.OnTouchListener {
    companion object {
        const val STATE_DEFAULT = 0
        const val STATE_PRESSED = 1
        const val STATE_WAITING = 2

        private const val TAG = "CreateButtonPanelView"
        private const val DURATION_100 = 100L
        private const val DURATION_180 = 180L
        private const val DURATION_350 = 350L
        private const val DURATION_450 = 450L

        private const val SCALE_0_5 = 0.5F
        private const val SCALE_1_2 = 1.2F

        //系统是500MS,LONG_PRESS_TIMEOUT
        private const val LONG_CLICK_DURATION = 500L

        //系统是125MS,PRESSED_STATE_DURATION
        private const val QUICK_CLICK_DURATION = 300L
        private const val MOVE_CANCEL_MAX_OFFSET = 100
        private const val FLOAT_0_5 = 0.5F
        const val ALPHA_HALF = 0.5f
        private const val DURATION_400 = 400L
        private const val SCALE_HALF = 0.5f
        private const val FG_ALPHA_ENABLE = 0.3f
    }

    private var cancelIcon: ImageView? = null
    private var ivCreateIcon: ImageView? = null
    private var ivCreateIconMask: ImageView? = null
    private var iconListView: View? = null

    //圆弧背景的y，相对于屏幕
    private var panelBgViewPositionY: Float = 0F

    private var isDown = false
    private var downTime = 0L
    private var isMove = false
    private var isQuickClick = false
    private var currentTouchInCreate = false
    private var isLongClick = false
    private var ignoreGesture = false
    private var startX = 0F
    private var startY = 0F
    @CreatePanelState
    private var currentState = STATE_DEFAULT
    private val handler = Handler(Looper.getMainLooper())
    private var callback: OnTouchEventChangedCallback? = null
    private val moveInterpolator = COUIMoveEaseInterpolator()
    private val easeInterpolator = COUIEaseInterpolator()

    //完成区域的动效
    private var doneAreaAnimatorSet: AnimatorSet? = null

    //取消区域的动效
    private var cancelAreaAnimatorSet: AnimatorSet? = null
    //进入新建页面，完成区域动效
    private var doneViewInAnimation: AnimatorSet? = null

    //三个helper用于白色蒙层、灰色按钮、麦克风按钮的按压动效
    private var helper1: COUIPressFeedbackHelper? = null
    private var helper2: COUIPressFeedbackHelper? = null
    private var helper3: COUIPressFeedbackHelper? = null
    private var mScaleInAnimation: AnimatorSet? = null
    private var mScaleOutAnimation: AnimatorSet? = null
    private var mAlphaInAnimation: AnimatorSet? = null
    private var mAlphaOutAnimation: AnimatorSet? = null
    private var binding: LayoutCreateBottonPannelBinding

    private val longPressRunnable = Runnable {
        if (isDown) {
            if (callback?.isAsrEnabled() == true) {
                isLongClick = true
                currentState = STATE_PRESSED
                callback?.onLongPressed()
                switchToPanelView()
                currentTouchInCreate = true
            }
            AppLogger.BASIC.d(TAG, "longPressRunnable onLongPressed")
        }
    }

    init {
        binding = LayoutCreateBottonPannelBinding.inflate(LayoutInflater.from(context), this, true)
        initView()
        initListener()
    }


    private fun initView() {
        iconListView = binding.root.findViewById(R.id.fl_create_icon_list)
        ivCreateIcon = binding.root.findViewById(R.id.iv_create_icon_list)
        ivCreateIconMask = binding.root.findViewById(R.id.iv_create_icon_mask)
        cancelIcon = binding.root.findViewById(R.id.iv_cancel_icon)
    }

    private fun initListener() {
        ivCreateIconMask?.apply {
            isClickable = true
            isFocusable = true
            isLongClickable = true
            setOnTouchListener(this@CreateButtonPanelView)
        }
    }

    /**
     * 圆形按钮按下动效，展开成圆弧
     */
    private fun switchToPanelView() {
        AppLogger.BASIC.d(TAG, "switchToCreateView...")
        startOtherViewAnimation(true)
        startDoneViewInAnimation()
    }

    /**
     * 圆弧面板松手后执行的动效，恢复成小圆
     * 可能恢复为等待中动效
     */
    private fun switchToCircleView(isWaiting: Boolean) {
        AppLogger.BASIC.d(TAG, "switchToListView...isWaiting = $isWaiting")
        //执行退出动效之前先取消正在进行的动画，因为文字动效有延迟，可能会造成回到列表后文字还显示
        cancelAreaAnimatorSet?.cancel()
        doneAreaAnimatorSet?.cancel()

        startOtherViewAnimation(false)
        val endColor = if (isWaiting) {
            R.color.create_panel_bg_color_wait
        } else {
            R.color.white
        }
        startDoneViewOutAnimation(startColor = R.color.create_panel_bg_color, endColorInt = endColor)
        //这里调用是为了恢复取消按钮到初始状态
        doCheckToCancelAreaAnimation(false)
    }

    /**
     * 列表到新建页面，执行按钮相关的转场动效
     */
    private fun startDoneViewInAnimation() {
        val maskView = ivCreateIconMask ?: return
        val voiceView = ivCreateIcon ?: return
        maskView.pivotX = maskView.height / 2F
        maskView.pivotY = 0F
        binding.ivCreateIconList.pivotX = binding.ivCreateIconList.width / 2F
        binding.ivCreateIconList.pivotY = 0F
        val width = maskView.width * maskView.scaleX
        val height = maskView.height * maskView.scaleY
        val dimen509 = resources.getDimension(R.dimen.dimen_509)
        val sx = dimen509 / width
        val sy = dimen509 / height
        AppLogger.BASIC.d(
            TAG, "startDoneViewInAnimation... sx=${maskView.scaleX},sy=${maskView.scaleY}," +
                    "mw=${maskView.measuredWidth},mh=${maskView.measuredHeight}" +
                    ",width=$width,height=$height,dimen509=$dimen509,sx=$sx,sy=$sy"
        )

        val scaleX = PropertyValuesHolder.ofFloat("scaleX", sx)
        val scaleY = PropertyValuesHolder.ofFloat("scaleY", sy)
        val alpha = PropertyValuesHolder.ofFloat("alpha", 0F)
        val animator = ObjectAnimator.ofPropertyValuesHolder(voiceView, scaleX, scaleY)
        val animator2 = ObjectAnimator.ofPropertyValuesHolder(maskView, scaleX, scaleY, alpha)

        //麦克风位移+缩放动效
        val dp13 = resources.getDimension(R.dimen.dimen_13_38)
        val sxMic = PropertyValuesHolder.ofFloat("scaleX", 1F)
        val syMic = PropertyValuesHolder.ofFloat("scaleY", 1F)
        val translationY = PropertyValuesHolder.ofFloat("translationY", dp13)
        val animator3 = ObjectAnimator.ofPropertyValuesHolder(binding.ivCreateIconDefault, sxMic, syMic, translationY)

        doneViewInAnimation = AnimatorSet().apply {
            duration = DURATION_450
            interpolator = moveInterpolator
            playTogether(animator, animator2, animator3)
            start()
        }
    }

    /**
     * 新建页面回到列表页，执行转场动效
     */
    private fun startDoneViewOutAnimation(@ColorRes startColor: Int, @ColorRes endColorInt: Int) {
        val maskView = ivCreateIconMask ?: return
        val voiceView = ivCreateIcon ?: return

        val width = maskView.width
        val height = maskView.height
        val dimen56 = resources.getDimension(R.dimen.dimen_56)
        val sx = dimen56 / width
        val sy = dimen56 / height
        AppLogger.BASIC.d(TAG, "startDoneViewOutAnimation... sx=$sx,sy=$sy")

        val scaleX = PropertyValuesHolder.ofFloat("scaleX", 1F)
        val scaleY = PropertyValuesHolder.ofFloat("scaleY", 1F)
        val alpha = PropertyValuesHolder.ofFloat("alpha", 1F)
        val animator = ObjectAnimator.ofPropertyValuesHolder(voiceView, scaleX, scaleY)
        val animator2 = ObjectAnimator.ofPropertyValuesHolder(maskView, scaleX, scaleY, alpha)

        val argbEvaluator = ArgbEvaluator()
        val startColor = context.getColor(startColor)
        val endColor = context.getColor(endColorInt)
        animator2.addUpdateListener {
            val value = (it.animatedValue as? Float) ?: 1F
            val fraction = 1F / value
            val evaluate = argbEvaluator.evaluate(
                fraction,
                startColor,
                endColor
            ) as Int
            (maskView.drawable as? GradientDrawable)?.setColor(evaluate)
        }

        //麦克风位移动效
        val translationY = PropertyValuesHolder.ofFloat("translationY", 0F)
        val animator3 = ObjectAnimator.ofPropertyValuesHolder(binding.ivCreateIconDefault, translationY)

        AnimatorSet().apply {
            duration = DURATION_450
            interpolator = moveInterpolator
            playTogether(animator, animator2, animator3)
            doOnStart {
                binding.ivCreateIconVoice.alpha = 0F
                binding.ivCreateIconVoiceWhite.alpha = 0F
            }
            start()
        }
    }

    /**
     * 执行转场动效过程中
     * 取消文本、取消按钮、新建文本的显示隐藏动效
     */
    private fun startOtherViewAnimation(needShow: Boolean) {
        AppLogger.BASIC.d(TAG, "startOtherViewAnimation...needShow=$needShow")

        val alpha = PropertyValuesHolder.ofFloat("alpha", if (needShow) 1F else 0F)
        val animator1 = ObjectAnimator.ofPropertyValuesHolder(binding.tvCreateText, alpha)
        val animator2 = ObjectAnimator.ofFloat(binding.tvCancelText, "alpha", 0F)
        val animator3 = ObjectAnimator.ofPropertyValuesHolder(binding.flCancelIcon, alpha)

        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = moveInterpolator
            if (needShow) {
                /*进入新建页面的时候不执行新建文本的alpha动效，因为在后续链接状态里面会更新alpha
                playTogether(animator1, animator3)
                */
                playTogether(animator3)
            } else {
                if (binding.tvCreateText.alpha != 0F) {
                    playTogether(animator1, animator2, animator3)
                } else {
                    playTogether(animator2, animator3)
                }
            }
            start()
        }
    }

    fun setTouchCallback(callback: OnTouchEventChangedCallback) {
        this.callback = callback
    }

    override fun onTouch(v: View?, event: MotionEvent?): Boolean {
        if (v == null || event == null) {
            return super.onTouchEvent(event)
        }
        if (currentState == STATE_WAITING) {
            AppLogger.BASIC.d(TAG, "onTouchEvent, return by STATE_WAITING")
            return super.onTouchEvent(event)
        }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                callback?.onActionDown()
                //按压动效
                startPressDownAnimation(true)
                isDown = true
                downTime = System.currentTimeMillis()

                startX = event.x
                startY = event.y

                handler.postDelayed(longPressRunnable, LONG_CLICK_DURATION)
                AppLogger.BASIC.d(TAG, "onTouch ACTION_DOWN startX=$startX,y=$startY")
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                AppLogger.BASIC.d(TAG, "onTouch ACTION=${event.action}")
                //取消长按事件
                handler.removeCallbacks(longPressRunnable)

                //没有移动超出范围，并且点击事件较短
                if (!checkMoveOutOfRange(event) && (System.currentTimeMillis() - downTime <= QUICK_CLICK_DURATION)) {
                    AppLogger.BASIC.d(TAG, "onTouch short click event...")
                    isQuickClick = true
                    callback?.onClick()
                }

                //执行松手动效,如果是中断场景(cancel事件)，则不保存待办
                if (!ignoreGesture) {
                    doActionUpAnimation(event.action == MotionEvent.ACTION_UP)
                }
                //重置参数
                resetArguments()
            }

            MotionEvent.ACTION_MOVE -> {
                AppLogger.DEBUG.d(TAG) { "onTouch ACTION_MOVE" }
                if (ignoreGesture) {
                    AppLogger.BASIC.d(TAG, "onTouch ACTION_MOVE, return by ignoreGesture")
                    return super.onTouchEvent(event)
                }
                isMove = true
                //如果在触发长按之前已经离开了触发区，则取消长按事件
                if (!isLongClick && checkMoveOutOfRange(event)) {
                    handler.removeCallbacks(longPressRunnable)
                }
                //持续检查移动范围，属于取消区新建域还是完成新建区域
                if (isLongClick) {
                    checkTouchInCancelOrDone(event)
                }
            }
            else -> {}
        }
        return true
    }

    /**
     * 松手的时候判断执行那个按钮的取消动画
     */
    private fun doActionUpAnimation(eventUp: Boolean) {
        AppLogger.BASIC.d(TAG, "doActionUpAnimation...")
        //显示麦克风图片
        binding.ivCreateIconVoice.cancelAnimation()
        binding.ivCreateIconVoiceWhite.cancelAnimation()
        if (isLongClick) {
            //如果是长按事件，则执行转场动效，不在这里处理
            AppLogger.BASIC.d(TAG, "onTouch long click up event...")
            VibrateUtils.execVibrate(context)

            //需要保存就等待1s
            val isWaiting = if ((currentTouchInCreate && eventUp) && (callback?.needWait() == true)) {
                startWaitingAnimation()
                true
            } else {
                binding.lvCreate.alpha = 0F
                binding.ivCreateIconDefault.alpha = 1F
                false
            }
            switchToCircleView(isWaiting)
            //如果是中断场景(cancel事件)，则不保存待办
            callback?.onLongPressedCancel(currentTouchInCreate && eventUp, !eventUp)
        } else {
            //如果是点击事件，则执行抬起动效
            startPressDownAnimation(false)
        }
    }

    private fun startWaitingAnimation() {
        AppLogger.BASIC.d(TAG, "startWaitingAnimation...")
        currentState = STATE_WAITING
        //加载框 alpha 出现
        ObjectAnimator.ofFloat(binding.lvCreate, "alpha", 1F).apply {
            addUpdateListener {
                duration = DURATION_180
                interpolator = easeInterpolator
            }
            start()
        }
    }

    fun stopWaitingAnimation() {
        AppLogger.BASIC.d(TAG, "stopWaitingAnimation...")
        //1.恢复到白色背景
        val drawable = (binding.ivCreateIconMask.drawable as? GradientDrawable) ?: return
        val argbEvaluator = ArgbEvaluator()
        val startColor = context.getColor(R.color.create_panel_bg_color_wait)
        val endColor = context.getColor(R.color.white)
        //2.麦克风alpha出现
        val micAnimator = ObjectAnimator.ofFloat(binding.ivCreateIconDefault, "alpha", 1F).apply {
            addUpdateListener {
                val fraction = (it.animatedValue as Float) / 1F
                val evaluate = argbEvaluator.evaluate(fraction, startColor, endColor) as Int
                drawable.setColor(evaluate)
            }
        }
        //3.加载框alpha消失
        val loadingAnimator = ObjectAnimator.ofFloat(binding.lvCreate, "alpha", 0F)
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = easeInterpolator
            doOnCancel {
                currentState = STATE_DEFAULT
            }
            doOnEnd {
                currentState = STATE_DEFAULT
            }
            playTogether(micAnimator, loadingAnimator)
            start()
        }
    }

    /**
     * 检查移动范围，属于取消区新建域还是完成新建区域
     */
    private fun checkTouchInCancelOrDone(event: MotionEvent): Boolean {
        val dividerY = calculateOffsetY()
        val currentY = event.rawY

        currentTouchInCreate = if (currentY >= dividerY) {
            if (!currentTouchInCreate) {
                AppLogger.BASIC.d(TAG, "checkTouchInCancelOrDone 首次移动到新建区域")
                checkToDoneArea()
            }
            true
        } else {
            if (currentTouchInCreate) {
                AppLogger.BASIC.d(TAG, "checkTouchInCancelOrDone 首次移动到取消区域")
                checkToCancelArea()
            }
            false
        }
        return currentTouchInCreate
    }

    /**
     * 滑动过程中移动到取消区域
     */
    private fun checkToCancelArea() {
        doCheckToCancelAreaAnimation(true)
        doCheckToDoneAreaAnimation(false)
    }

    /**
     * 滑动过程中移动到完成区域
     */
    private fun checkToDoneArea() {
        doCheckToDoneAreaAnimation(true)
        doCheckToCancelAreaAnimation(false)
    }

    /**
     * @return 获取新建/取消分割线的y左边，相对于屏幕
     */
    private fun calculateOffsetY(): Float {
        if (panelBgViewPositionY == 0F) {
            val position = IntArray(2)
            ivCreateIcon?.getLocationInWindow(position)
            panelBgViewPositionY = position[1].toFloat()
        }
        AppLogger.DEBUG.d(TAG) { "calculateDividerY, panelBgViewPositionY=$panelBgViewPositionY" }
        return panelBgViewPositionY
    }

    /**
     * 语音按钮按压动效
     */
    private fun startPressDownAnimation(isPressed: Boolean) {
        AppLogger.BASIC.d(TAG, "startPressDownAnimation in...isPressed=$isPressed")
        if (helper1 == null) {
            helper1 = COUIPressFeedbackHelper(ivCreateIconMask, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
        }
        if (helper2 == null) {
            helper2 = COUIPressFeedbackHelper(ivCreateIcon, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
        }
        if (helper3 == null) {
            helper3 = COUIPressFeedbackHelper(binding.ivCreateIconDefault, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
        }
        binding.ivCreateIconMask.pivotX = binding.ivCreateIconMask.width / 2F
        binding.ivCreateIconMask.pivotY = binding.ivCreateIconMask.height / 2F
        binding.ivCreateIconList.pivotX = binding.ivCreateIconList.width / 2F
        binding.ivCreateIconList.pivotY = binding.ivCreateIconList.height / 2F
        helper1?.executeFeedbackAnimator(isPressed)
        helper2?.executeFeedbackAnimator(isPressed)
        helper3?.executeFeedbackAnimator(isPressed)
    }

    /**
     * 移动到取消区域动效
     */
    private fun doCheckToCancelAreaAnimation(needShow: Boolean) {
        AppLogger.BASIC.d(TAG, "doCheckToCancelAreaAnimation in...needShow=$needShow")
        val animator1 = ObjectAnimator.ofFloat(binding.flCancelIconWhite, "alpha", if (needShow) 1F else 0F).apply {
            interpolator = easeInterpolator
            duration = DURATION_180
        }
        val animator2 = ObjectAnimator.ofFloat(binding.flCancelIconWhite, "scaleX", if (needShow) SCALE_1_2 else 1F).apply {
            duration = DURATION_350
            interpolator = moveInterpolator
        }
        val animator3 = ObjectAnimator.ofFloat(binding.flCancelIconWhite, "scaleY", if (needShow) SCALE_1_2 else 1F).apply {
            duration = DURATION_350
            interpolator = moveInterpolator
        }
        //松开取消的alpha
        val animator4 = ObjectAnimator.ofFloat(binding.tvCreateText, "alpha", if (needShow) 1F else 0F).apply {
            duration = DURATION_180
            interpolator = easeInterpolator
            if (needShow) {
                startDelay = DURATION_100
            }
        }
        cancelAreaAnimatorSet?.cancel()
        cancelAreaAnimatorSet = AnimatorSet().apply {
            playTogether(animator1, animator2, animator3, animator4)
            start()
        }
    }

    /**
     * 移动到新建区域动效
     */
    private fun doCheckToDoneAreaAnimation(needShow: Boolean) {
        AppLogger.BASIC.d(TAG, "doCheckToDoneAreaAnimation in,needShow=$needShow")
        //圆弧alpha
        val animator1 = ObjectAnimator.ofFloat(binding.ivCreateIconList, "alpha", if (needShow) 1F else SCALE_0_5).apply {
            interpolator = easeInterpolator
            duration = DURATION_180
        }
        //松开 完成新建alpha
        val animator2 = ObjectAnimator.ofFloat(binding.tvCreateText, "alpha", if (needShow) 1F else 0F).apply {
            duration = DURATION_180
            interpolator = easeInterpolator
            if (needShow) {
                startDelay = DURATION_100
            }
        }

        val animator3 = ObjectAnimator.ofFloat(binding.ivCreateIconVoiceWhite, "alpha", if (needShow) 0F else 1F).apply {
            interpolator = easeInterpolator
            duration = DURATION_180
        }
        if (needShow) {
            binding.ivCreateIconVoice.progress = binding.ivCreateIconVoiceWhite.progress
            if (binding.ivCreateIconVoiceWhite.isAnimating) {
                binding.ivCreateIconVoice.playAnimation()
                binding.ivCreateIconVoiceWhite.cancelAnimation()
            }
        } else {
            binding.ivCreateIconVoiceWhite.progress = binding.ivCreateIconVoice.progress
            if (binding.ivCreateIconVoice.isAnimating) {
                binding.ivCreateIconVoiceWhite.playAnimation()
                binding.ivCreateIconVoice.cancelAnimation()
            }
        }

        doneAreaAnimatorSet?.cancel()
        doneAreaAnimatorSet = AnimatorSet().apply {
            playTogether(animator1, animator2, animator3)
            start()
        }
    }

    /**
     * 如果按下过程中移动距离过远，则判断不属于点击事件，可能是fling之类的
     */
    private fun checkMoveOutOfRange(event: MotionEvent): Boolean {
        val currentX = event.x
        val currentY = event.y

        val offsetX = abs(currentX - startX)
        val offsetY = abs(currentY - startY)

        val offset = sqrt(((offsetX * offsetX + offsetY * offsetY).toDouble()))
        AppLogger.DEBUG.d(TAG) { "checkMoveOutOfRange offsetX=$offsetX,offsetY=$offsetY,offset=$offset" }

        if (offset > MOVE_CANCEL_MAX_OFFSET) {
            return true
        }
        return false
    }

    @MainThread
    fun setCreateTextView(isVisible: Boolean, state: Int) {
        AppLogger.BASIC.d(TAG, "setCreateTextView,visible=$isVisible,state=$state,Create=$currentTouchInCreate,isLongClick=$isLongClick")
        if (!isLongClick) {
            AppLogger.BASIC.d(TAG, "setCreateTextView,return by isLongClick false")
            return
        }
        if (isVisible && currentTouchInCreate) {
            //这里先取消动效，因为doneAreaAnimatorSet动效延迟100ms才执行，如果在此期间执行其他动效可能会出现问题
            doneAreaAnimatorSet?.cancel()
            ObjectAnimator.ofFloat(binding.tvCreateText, "alpha", 1F).apply {
                duration = DURATION_180
                interpolator = easeInterpolator
            }.start()
        } else {
            binding.tvCreateText.alpha = 0F
        }
        binding.tvCreateText.text = when (state) {
            TodoListViewModel.STATE_ERROR -> {
                binding.ivCreateIconDefault.alpha = 1F
                binding.ivCreateIconVoice.cancelAnimation()
                binding.ivCreateIconVoiceWhite.cancelAnimation()
                context.getString(R.string.todo_network_error_retry)
            }
            TodoListViewModel.STATE_LISTENING -> {
                doneViewInAnimation?.doOnEnd {
                    if (isLongClick) {
                        binding.ivCreateIconDefault.alpha = 0F
                        binding.ivCreateIconVoice.alpha = 1F

                        if (currentTouchInCreate) {
                            //在新建模式，并且在新建区域
                            binding.ivCreateIconVoiceWhite.alpha = 0F
                            binding.ivCreateIconVoice.playAnimation()
                        } else {
                            //在新建模式，在取消区域
                            binding.ivCreateIconVoiceWhite.alpha = 1F
                            binding.ivCreateIconVoiceWhite.playAnimation()
                        }
                    }
                }
                context.getString(R.string.todo_toast_done)
            }
            else -> ""
        }
    }

    /**
     * 异常场景需要执行松手动效，但是手指依然按住状态，需要屏蔽手势
     */
    fun pressUpManual() {
        AppLogger.BASIC.d(TAG, "pressUpManual...")
        if (isLongClick) {
            ignoreGesture = true
            doActionUpAnimation(true)
        }
    }

    private fun resetArguments() {
        isDown = false
        isMove = false
        isLongClick = false
        isQuickClick = false
        startX = 0F
        startY = 0F
        downTime = 0L
        currentTouchInCreate = false
        ignoreGesture = false
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        //重新计算分割线的位置
        calculateOffsetY()
    }

    /**
     * button消失和出现的动画： http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&version=V%2013&id=7590
     * */
    fun animateOut() {
        if (mScaleOutAnimation?.isRunning == true || binding.flCreateIconList.visibility == View.GONE) {
            return
        }
        AppLogger.BASIC.d(TAG, "animateOut")
        mScaleInAnimation?.cancel()
        mAlphaInAnimation?.cancel()
        mScaleOutAnimation = AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIOutEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(binding.flCreateIconList, "scaleX", 1f, SCALE_HALF),
                ObjectAnimator.ofFloat(binding.flCreateIconList, "scaleY", 1f, SCALE_HALF)
            )
            start()
        }
        mAlphaOutAnimation = AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(binding.flCreateIconList, "alpha", 1f, 0f)
            )
            doOnStart {
                binding.ivCreateIconMask.pivotX = binding.ivCreateIconMask.width / 2F
                binding.ivCreateIconMask.pivotY = binding.ivCreateIconMask.height / 2F
                binding.ivCreateIconList.pivotX = binding.ivCreateIconList.width / 2F
                binding.ivCreateIconList.pivotY = binding.ivCreateIconList.height / 2F
            }
            doOnEnd {
                visibility = GONE
            }
            start()
        }
    }

    fun animateIn() {
        if (mScaleInAnimation?.isRunning == true || binding.flCreateIconList.alpha == 1f && binding.flCreateIconList.visibility == View.VISIBLE) {
            return
        }
        AppLogger.BASIC.d(TAG, "animateIn")
        mScaleOutAnimation?.cancel()
        mAlphaOutAnimation?.cancel()
        mScaleInAnimation = AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIInEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(binding.flCreateIconList, "scaleX", SCALE_HALF, 1f),
                ObjectAnimator.ofFloat(binding.flCreateIconList, "scaleY", SCALE_HALF, 1f)
            )
            start()
        }
        mAlphaInAnimation = AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(binding.flCreateIconList, "alpha", 0f, 1f)
            )
            doOnStart {
                visibility = VISIBLE
                binding.ivCreateIconMask.pivotX = binding.ivCreateIconMask.width / 2F
                binding.ivCreateIconMask.pivotY = binding.ivCreateIconMask.height / 2F
                binding.ivCreateIconList.pivotX = binding.ivCreateIconList.width / 2F
                binding.ivCreateIconList.pivotY = binding.ivCreateIconList.height / 2F
            }
            start()
        }
    }

    fun clearAllAnimation() {
        mScaleInAnimation?.cancel()
        mAlphaInAnimation?.cancel()
        mScaleOutAnimation?.cancel()
        mAlphaOutAnimation?.cancel()
    }


    interface OnTouchEventChangedCallback {
        fun onActionDown()
        fun onClick()
        fun isAsrEnabled(): Boolean
        fun onLongPressed()
        fun onLongPressedCancel(saveContent: Boolean, isCancelAction: Boolean)
        fun needWait(): Boolean {
            return false
        }
    }
}