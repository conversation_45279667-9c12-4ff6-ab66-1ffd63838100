/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.fragment

import android.os.Build
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.TodoItem
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.TodoCardDetailFragmentBinding
import com.oplus.note.scenecard.todo.ui.ViewUtils.setAlarmDateTextColorDrawableRight
import com.oplus.note.scenecard.todo.ui.animation.TodoDetailAnimationHelper
import com.oplus.note.scenecard.todo.ui.animation.TodoListAnimationManager
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter
import com.oplus.note.scenecard.todo.ui.main.TodoListViewModel
import com.oplus.note.scenecard.utils.PointUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@RequiresApi(Build.VERSION_CODES.R)
class TodoDetailFragment : TodoBaseFragment(), View.OnClickListener {
    companion object {
        const val KEY_DATA = "data"
        const val KEY_TITLE_TYPE_IN_LIST = "title_type_in_list"
        private const val TAG = "TodoDetailFragment"

        fun newInstance(): TodoDetailFragment = TodoDetailFragment()
    }

    private val mViewModel by viewModels<TodoListViewModel>({ requireActivity() })
    private var mDataBinding: TodoCardDetailFragmentBinding? = null
    private var mData: TodoItem? = null
    private var mOriginalData: TodoItem? = null
    private var mAnimationHelper: TodoDetailAnimationHelper? = null
    var mClickDeleteOrComplete = false
    var fromRestore = false

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val dataBinding = DataBindingUtil.inflate<TodoCardDetailFragmentBinding>(
            inflater,
            R.layout.todo_card_detail_fragment, container, false
        )
        mDataBinding = dataBinding

        return dataBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mDataBinding?.tbDetail?.let { initToolBar(it, "") }
        initListener()
        initData()
        prepareSharedTransition(view, savedInstanceState != null)
    }

    private fun prepareSharedTransition(view: View, fromRestore: Boolean) {
        this.fromRestore = fromRestore
        mAnimationHelper = TodoDetailAnimationHelper(requireContext(), mDataBinding).apply {
            if (fromRestore) {
                setViewFinalState()
            } else {
                prepareSharedElementTransitionAnimation(activity)
            }
        }
    }

    override fun onStop() {
        super.onStop()
        if (TodoDetailAnimationHelper.openTransitionAnimationRunning) {
            activity?.finish()
            TodoListAnimationManager.markNeedRestButtonStatus()
        }
    }

    @Suppress("DEPRECATION")
    private fun initData() {
        (arguments?.getParcelable(KEY_DATA) as? TodoItem)?.apply {
            mData = this
            initContent(mData)
            mOriginalData = this
            lifecycleScope.launch(Dispatchers.IO) {
                AppLogger.BASIC.d(TAG, "initData observe...")
                val data = TodoRepoFactory.get()?.queryTodo(localId ?: "")
                withContext(Dispatchers.Main) {
                    if (data == null) {
                        directFinishActivity()
                    } else {
                        data.observe(viewLifecycleOwner) {
                            AppLogger.BASIC.d(TAG, "observe data change is null: ${it == null}")
                            if (null == it) {
                                directFinishActivity()
                                return@observe
                            }
                            mData = it
                            initContent(it)
                        }
                    }
                }
            }
        }
    }

    private fun initContent(todoItem: TodoItem?) {
        val typeInList = arguments?.getInt(KEY_TITLE_TYPE_IN_LIST, TodoListAdapter.VIEW_TYPE_HUGE)
        todoItem?.apply {
            mDataBinding?.tvDetailContent?.text = content
            mDataBinding?.contentForAnimation?.let {
                it.text = content
                TodoListAdapter.setContentTextParaDetail(typeInList, it, 0f)
            }
            setAlarmTimeVisible(this)
            initViewColors(colorIndex)
        }
    }

    private fun setAlarmTimeVisible(todoItem: TodoItem) {
        val color = TodoResourceController.getDetailPageTextColor(todoItem.colorIndex)
        val alarmDate = todoItem.alarmTime
        if ((alarmDate != null) && (alarmDate > 0L)) {
            mDataBinding?.clDetailDate?.isVisible = true
            mDataBinding?.tvDetailDate?.setAlarmDateTextColorDrawableRight(
                todoItem,
                color,
                R.color.color_alarm_time_expired_red,
            )
            //这里使用的默认颜色是列表的颜色，而不是详情页的颜色，为了返回列表时候颜色和列表的提示时间颜色统一
            mDataBinding?.timeForAnimation?.setAlarmDateTextColorDrawableRight(
                todoItem,
                R.color.color_alarm_time_normal_list,
                R.color.color_alarm_time_expired_red,
            )
            mDataBinding?.timeForAnimation?.apply {
                TodoListAdapter.setFontWight(this, TodoListAdapter.FONT_NORMAL)
                textSize = context.resources.getDimension(R.dimen.dimen_time_large)
            }
        } else {
            mDataBinding?.clDetailDate?.isVisible = false
            mDataBinding?.timeForAnimation?.isVisible = false
        }
    }

    private fun initViewColors(index: Int) {
    }

    private fun initListener() {
        mDataBinding?.btnCancel?.setOnClickListener(this)
        mDataBinding?.btnConfirm?.setOnClickListener(this)
        if (TodoListAnimationManager.asrButtonEnable) {
            mDataBinding?.btnConfirm?.setIconRes(R.drawable.voice)
        }
        mDataBinding?.tvDetailContent?.movementMethod = ScrollingMovementMethod.getInstance()
        activity?.onBackPressedDispatcher?.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finishWithAnimation()
            }
        })
    }

    override fun onClick(v: View) {
        mData?.let {
            when (v.id) {
                R.id.btn_cancel -> {
                    showConfirmDialog(it)
                }
                R.id.btn_confirm -> {
                    mViewModel.doneTodo(it) { count ->
                        if (count > 0) {
                            TodoListAnimationManager.needPlayCompleteAnimation = true
                            context?.let {
                                PointUtil.setCardEventDoneInDetail(it)
                            }
                            directFinishActivity()
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private fun showConfirmDialog(item: TodoItem) {
        activity?.let { context ->
            COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setNeutralButton(R.string.scene_btn_delete) { _, _ -> doDelete(item) }
                .setNegativeButton(R.string.scene_btn_cancel) { _, _ -> }
                .setWindowGravity(Gravity.BOTTOM)
                .setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog)
                .show()
        }
    }

    private fun doDelete(item: TodoItem) {
        mViewModel.deleteTodo(item) { count ->
            if (count > 0) {
                context?.let {
                    PointUtil.setCardEventDeleteInDetail(it)
                }
                directFinishActivity()
            }
        }
    }

    /**点击删除或完成按钮时关闭页面，不执行转场动画，执行alpha消失*/
    private fun directFinishActivity() {
        AppLogger.BASIC.d(TAG, "directFinishActivity")
        activity?.runOnUiThread {
            mAnimationHelper?.startDirectCloseAnimation {
                activity?.finish()
            }
        }
    }

    private fun finishWithAnimation() {
        if (checkItemChanged() || fromRestore) {
            directFinishActivity()
            return
        }
        AppLogger.BASIC.d(TAG, "finishWithAnimation")
        mAnimationHelper?.startCloseAnimation {
            activity?.finishAfterTransition()
        }
    }

    private fun checkItemChanged(): Boolean {
        var hasChanged = false
        mData?.let {
            hasChanged = mOriginalData?.contentSame(it) == false
        }
        AppLogger.BASIC.d(TAG, "checkItemChanged hasChanged=$hasChanged")
        return hasChanged
    }

    override fun onBackClick() {
        super.onBackClick()
        finishWithAnimation()
    }

    override fun tag(): String {
        return TAG
    }
}