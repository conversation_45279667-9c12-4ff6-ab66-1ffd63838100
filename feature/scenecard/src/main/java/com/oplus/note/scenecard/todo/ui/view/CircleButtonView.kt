/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CircleButtonView.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: yanglin<PERSON>@oppo.com
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.os.Build
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIOutEaseInterpolator
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import kotlin.math.min

@RequiresApi(Build.VERSION_CODES.Q)
class CircleButtonView : RelativeLayout {

    companion object {
        private const val TAG = "CircleButtonView"

        //按钮显示在哪，新建页、详情页、首页
        const val TAG_CREATE = "CREATE"
        const val TAG_DETAIL = "DETAIL"
        const val TAG_MAIN = "MAIN"

        const val DURATION_180 = 180L
        const val ALPHA_HALF = 0.5f
        private const val DURATION_400 = 400L
        private const val SCALE_HALF = 0.5f
        private const val FG_ALPHA_ENABLE = 0.3f
    }

    constructor(context: Context?) : super(context) {
        init(context, null)
    }

    constructor(ctx: Context?, attributeSet: AttributeSet?) : super(ctx, attributeSet) {
        init(ctx, attributeSet)
    }

    private lateinit var mImageView: ImageView
    private var mBackgroundColor: Int = context.getColor(R.color.circle_btn_background)
    private var mTargetColor: Int = context.getColor(R.color.circle_btn_background)
    private var mTag = TAG_CREATE
    private var mScaleInAnimation: AnimatorSet? = null
    private var mScaleOutAnimation: AnimatorSet? = null
    private var mAlphaInAnimation: AnimatorSet? = null
    private var mAlphaOutAnimation: AnimatorSet? = null

    private fun init(ctx: Context?, attributeSet: AttributeSet?) {
        val typedArray = context.obtainStyledAttributes(attributeSet, R.styleable.CircleButtonView)
        var iconRes: Int = -1
        try {
            mBackgroundColor = typedArray.getColor(
                R.styleable.CircleButtonView_bgColor,
                context.getColor(R.color.circle_btn_background)
            )
            mTargetColor = typedArray.getColor(
                R.styleable.CircleButtonView_targetColor,
                context.getColor(R.color.circle_btn_background)
            )
            typedArray.getString(R.styleable.CircleButtonView_tag)?.apply {
                mTag = this
            }
            iconRes =
                typedArray.getResourceId(R.styleable.CircleButtonView_iconRes, -1)
        } catch (e: java.lang.Exception) {
            AppLogger.BASIC.e(TAG, "init error ${e.message}")
        } finally {
            typedArray.recycle()
        }
        setButtonPressFeedback(this, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
        mImageView = ImageView(context).apply {
            if (iconRes != -1) {
                setImageResource(iconRes)
            }
            setBackgroundColor(Color.TRANSPARENT)
        }
        val lp = LayoutParams(
            context.resources.getDimensionPixelSize(R.dimen.circle_btn_icon_wh),
            context.resources.getDimensionPixelSize(R.dimen.circle_btn_icon_wh)
        )
        lp.addRule(CENTER_IN_PARENT)
        addView(mImageView, lp)
        setWillNotDraw(false)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            isForceDarkAllowed = false
        }
    }

    private val mCirclePaint: Paint by lazy {
        Paint().apply {
            color = mBackgroundColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }
    }

    private fun setButtonPressFeedback(view: View, type: Int) {
        val helper = COUIPressFeedbackHelper(view, type)
        view.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    helper.executeFeedbackAnimator(true)
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    helper.executeFeedbackAnimator(false)
                }
            }
            false
        }
    }

    fun getIconImageView(): ImageView {
        return mImageView
    }

    fun setIconRes(@DrawableRes resId: Int) {
        mImageView.setImageResource(resId)
    }

    fun setBgColor(color: Int) {
        mBackgroundColor = color
        mCirclePaint.color = mBackgroundColor
        postInvalidate()
    }

    fun getBgColor(): Int {
        return mBackgroundColor
    }

    fun getTargetColor(): Int {
        return mTargetColor
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas?.apply {
            drawCircle(width / 2f, height / 2f, min(width, height) / 2f, mCirclePaint)
        }
    }

    /**
     * button消失和出现的动画： http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&version=V%2013&id=7590
     * */
    fun animateOut() {
        mScaleInAnimation?.cancel()
        mAlphaInAnimation?.cancel()
        mScaleOutAnimation = AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIOutEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(this@CircleButtonView, "scaleX", 1f, SCALE_HALF),
                ObjectAnimator.ofFloat(this@CircleButtonView, "scaleY", 1f, SCALE_HALF)
            )
            start()
        }
        mAlphaOutAnimation = AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(this@CircleButtonView, "alpha", 1f, 0f)
            )
            doOnEnd {
                visibility = GONE
            }
            start()
        }
    }

    fun animateIn() {
        mScaleOutAnimation?.cancel()
        mAlphaOutAnimation?.cancel()
        mScaleInAnimation = AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIInEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(this@CircleButtonView, "scaleX", SCALE_HALF, 1f),
                ObjectAnimator.ofFloat(this@CircleButtonView, "scaleY", SCALE_HALF, 1f)
            )
            start()
        }
        mAlphaInAnimation = AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(this@CircleButtonView, "alpha", 0f, 1f)
            )
            doOnStart {
//                visibility = VISIBLE
                visibility = GONE
            }
            start()
        }
    }

    fun clearAllAnimation() {
        mScaleInAnimation?.cancel()
        mAlphaInAnimation?.cancel()
        mScaleOutAnimation?.cancel()
        mAlphaOutAnimation?.cancel()
    }

    fun getMtg(): String {
        return mTag
    }

    fun switchEnable(enable: Boolean) {
        val colorFrom = getBgColor()
        val colorTo = if (enable) {
            Color.WHITE
        } else {
            R.color.todo_create_btn_bg_disable_color
        }

        val bgAnimation = ValueAnimator.ofObject(ArgbEvaluator(), colorFrom, colorTo)
        bgAnimation.apply {
            interpolator = COUIEaseInterpolator()
            duration = DURATION_180
            addUpdateListener {
                val tempColor = it.animatedValue as? Int
                if (tempColor != null) {
                    setBgColor(tempColor)
                }
            }
        }

        val fgAlphaIn = ObjectAnimator.ofFloat(getIconImageView(), "alpha", 0f, 1f).apply {
            interpolator = COUIEaseInterpolator()
            duration = DURATION_180 / 2
        }

        val fgAlphaOut = ObjectAnimator.ofFloat(getIconImageView(), "alpha", 1f, 0f).apply {
            interpolator = COUIEaseInterpolator()
            duration = DURATION_180 / 2
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    if (enable) {
                        setIconRes(R.drawable.icon_todo_create_confirm_enable)
                    } else {
                        setIconRes(R.drawable.icon_todo_create_confirm_disable)
                    }
                    fgAlphaIn.start()
                }
            })
        }

        AnimatorSet().apply {
            playTogether(bgAnimation, fgAlphaOut)
            doOnStart {
                <EMAIL> = enable
            }
        }.start()
    }

    fun switchAsrEnable(enable: Boolean) {
        val from = if (enable) FG_ALPHA_ENABLE else 1f
        val to = if (enable) 1f else FG_ALPHA_ENABLE
        val fgAnimator = ObjectAnimator.ofFloat(getIconImageView(), "alpha", from, to).apply {
            interpolator = COUIEaseInterpolator()
            duration = DURATION_180
        }
        fgAnimator.start()
    }
}