/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/7/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/7/24      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import androidx.annotation.RawRes
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isVisible
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.LayoutSurpriseBinding
import com.oplus.note.scenecard.todo.ui.ViewUtils

class SurpriseView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "SurpriseView"
        const val TYPE_FINISH_ALL = "1"
        const val TYPE_MILE_STONE = "2"
        const val TYPE_ACHIEVEMENT = "3"
        private const val FLOAT_0_2 = 0.2F
        private const val FLOAT_0_3 = 0.3F
        private const val DURATION_300 = 300L
    }

    private var callback: AnimationCallback? = null
    private var binding: LayoutSurpriseBinding

    init {
        binding = LayoutSurpriseBinding.inflate(LayoutInflater.from(context), this, true)
        ViewUtils.setButtonPressFeedback(binding.flSurpriseBack, COUIPressFeedbackHelper.FILL_BUTTON_PRESS_FEEDBACK)
        binding.flSurpriseBack.setOnClickListener {
            cancelAndGone()
        }
    }

    fun showAnimation(type: String, count: Int, callback: AnimationCallback) {
        AppLogger.BASIC.d(TAG, "type=$type,count=$count")
        this.isVisible = true
        this.callback = callback
        when (type) {
            TYPE_FINISH_ALL -> playAllDoneAnimation()
            TYPE_MILE_STONE -> playMileStone(count)
            TYPE_ACHIEVEMENT -> playLastWeekDoneAnimation(count)
            else -> cancelAndGone()
        }
    }

    private fun playAllDoneAnimation() {
        AppLogger.BASIC.d(TAG, "playAllDoneAnimation in...")
        binding.tvSurpriseTitle.text = context.getString(R.string.scene_all_done_title)
        binding.tvSurpriseDesc.text = context.getString(R.string.scene_all_done)
        playAnimation(R.raw.alldone)
    }

    private fun playLastWeekDoneAnimation(count: Int) {
        AppLogger.BASIC.d(TAG, "playLastWeekDoneAnimation,count=$count")
        binding.tvSurpriseTitle.text = context.getString(R.string.scene_lastweek_done)
        binding.tvSurpriseDesc.text = resources.getQuantityString(R.plurals.scene_lastweek, count, count)
        playAnimation(R.raw.weeksurprise)
    }

    private fun playMileStone(count: Int) {
        AppLogger.BASIC.d(TAG, "playMileStone, count = $count")
        binding.tvSurpriseTitle.text = context.getString(R.string.scene_milestone_done)
        binding.tvSurpriseDesc.text = resources.getQuantityString(R.plurals.scene_milestone, count, count)
        playAnimation(R.raw.milestone)
    }

    private fun playAnimation(@RawRes resId: Int) {
        alphaAnimation(true)
        binding.lvSurprise.cancelAnimation()
        binding.lvSurprise.setAnimation(resId)
        binding.lvSurprise.playAnimation()
    }

    private fun alphaAnimation(needVisible: Boolean) {
        if (needVisible && <EMAIL> && <EMAIL> == 1F) {
            AppLogger.BASIC.d(TAG, "alphaAnimation, already visible")
            return
        }
        if (!needVisible && <EMAIL>() && <EMAIL> == 0F) {
            AppLogger.BASIC.d(TAG, "alphaAnimation,already gone")
            return
        }
        ObjectAnimator.ofFloat(this, "alpha", if (needVisible) 1F else 0F).apply {
            duration = DURATION_300
            interpolator = PathInterpolator(FLOAT_0_3, 0F, FLOAT_0_2, 1F)
            if (needVisible) {
                doOnStart {
                    <EMAIL> = true
                }
            }
            if (!needVisible) {
                doOnEnd {
                    <EMAIL> = false
                }
            }
            start()
        }
    }

    fun cancelAndGone() {
        AppLogger.BASIC.d(TAG, "cancelAndGone")
        binding.lvSurprise.cancelAnimation()
        alphaAnimation(false)
        callback?.onEnd()
        callback = null
    }

    interface AnimationCallback {
        fun onEnd()
    }
}