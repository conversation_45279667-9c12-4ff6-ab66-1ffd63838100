/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TestAppbarLayout.kt
** Description:
** Version: 1.0
** Date : 2023/6/21
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/6/21      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.util.AttributeSet
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.NoteAppbarBehavior

class NoteAppbarLayout(context: Context, attr: AttributeSet) : AppBarLayout(context, attr) {

    var behavior: NoteAppbarBehavior? = null

    override fun getBehavior(): CoordinatorLayout.Behavior<AppBarLayout> {
        behavior = NoteAppbarBehavior()
        return behavior!!
    }
}