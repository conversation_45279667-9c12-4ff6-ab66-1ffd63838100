/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * File: - $file$
 * Description:
 * Version: 1.0
 * Date : 2023/2/20
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author> <data>   <version>    <desc>
 * wangyinglei       2023/2/20      1.0     create file
 * v-yanjiepeng      2023/9/27      2.0     java to kotlin
 ************************************************************/
package com.oplus.note.scenecard.utils

import android.content.Context

object ToDoSharedPreferenceUtil {
    private const val TODO_SP_NAME = "todo_cache"
    private const val COLOR_INDEX = "color_index"
    private const val STONE_COUNT = "stone_count"
    private const val TODAY_DONE = "today_done"
    @JvmStatic
    fun getColorIndexSP(context: Context): Int {
        val sharedPreferences = context.getSharedPreferences(TODO_SP_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getInt(COLOR_INDEX, -1)
    }
    @JvmStatic
    fun setColorIndexSP(context: Context, value: Int) {
        val sharedPreferences = context.getSharedPreferences(TODO_SP_NAME, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putInt(COLOR_INDEX, value)
        editor.apply()
    }

    @JvmStatic
    fun getTodayDoneListStr(context: Context): String {
        val sharedPreferences = context.getSharedPreferences(TODO_SP_NAME, Context.MODE_PRIVATE)
        return sharedPreferences.getString(TODAY_DONE, "") ?: ""
    }
    @JvmStatic
    fun setTodayDoneList(context: Context, toJson: String) {
        val sharedPreferences = context.getSharedPreferences(TODO_SP_NAME, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putString(TODAY_DONE, toJson)
        editor.apply()
    }
}
