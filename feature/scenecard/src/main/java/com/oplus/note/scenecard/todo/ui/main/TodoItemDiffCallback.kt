/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - ItemDiffCallback.kt
** Description:
** Version: 1.0
** Date : 2023/4/4
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/4/4      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.main

import android.text.TextUtils
import androidx.recyclerview.widget.DiffUtil
import com.oplus.note.repo.todo.TodoItem

class TodoItemDiffCallback(private val oldList: List<TodoItem>, private val newList: List<TodoItem>) :
    DiffUtil.Callback() {

    override fun getOldListSize(): Int {
        return oldList.size
    }

    override fun getNewListSize(): Int {
        return newList.size
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        kotlin.runCatching {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            return TextUtils.equals(oldItem.localId, newItem.localId)
        }
        return false
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        kotlin.runCatching {
            val oldItem = oldList[oldItemPosition]
            val newItem = newList[newItemPosition]
            if (!TextUtils.equals(oldItem.content, newItem.content)) {
                return false
            }
            if (oldItem.alarmTime != newItem.alarmTime) {
                return false
            }
            if (oldItem.colorIndex != newItem.colorIndex) {
                return false
            }
            if (!TextUtils.equals(oldItem.toDoExtraStr, newItem.toDoExtraStr)) {
                return false
            }
        }.onFailure {
            return false
        }
        return true
    }
}