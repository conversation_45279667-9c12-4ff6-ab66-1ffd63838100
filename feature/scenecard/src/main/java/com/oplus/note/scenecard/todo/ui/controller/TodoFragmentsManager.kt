/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.controller

import android.os.Build
import android.os.Bundle
import android.os.Looper
import androidx.annotation.MainThread
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.fragment.TodoCreateFragment
import com.oplus.note.scenecard.todo.ui.fragment.TodoNoPermissionFragment
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RequiresApi(Build.VERSION_CODES.R)
object TodoFragmentsManager {
    const val TAG = "TodoFragmentsManager"
    const val BACK_FLAG = "back_flag"

    private var activity: AppCompatActivity? = null

    fun registerManager(activity: AppCompatActivity) {
        this.activity = activity
    }

    fun unRegisterManager() {
        this.activity = null
    }

    fun release() {
        unRegisterManager()
    }

    private fun runInMainThread(function: () -> Unit) {
        if (Looper.getMainLooper().isCurrentThread) {
            function.invoke()
        } else {
            activity?.lifecycleScope?.launch(Dispatchers.Main) {
                function.invoke()
            }
        }
    }

    fun popBackCreate() {
        runInMainThread {
            val fragmentManager = activity?.supportFragmentManager
            if (fragmentManager != null && fragmentManager.isStateSaved.not()) {
                val backStackEntry = fragmentManager.getBackStackEntryAt(fragmentManager.backStackEntryCount - 1)
                if (backStackEntry.name.equals(TodoCreateFragment.TAG)) {
                    fragmentManager.popBackStack(backStackEntry.name, FragmentManager.POP_BACK_STACK_INCLUSIVE)
                }
            }
        }
    }

    @Synchronized
    @MainThread
    fun backToLastPage() {
        runInMainThread {
            val count = activity?.supportFragmentManager?.backStackEntryCount ?: 0
            if (count > 0) {
                activity?.supportFragmentManager?.popBackStack()
            }
        }
    }

    @Synchronized
    @MainThread
    fun switchListToCreate(bundle: Bundle? = null) {
        runInMainThread {
            val fragment = TodoCreateFragment.newInstance()
            activity?.supportFragmentManager
                ?.beginTransaction()
                ?.addToBackStack(fragment.tag())
                ?.add(R.id.container, fragment.apply {
                    arguments = bundle
                }, fragment.tag())
                ?.commitAllowingStateLoss()
        }
    }

    @Synchronized
    @MainThread
    fun initListFragment() {
        runInMainThread {
            val fragment = TodoListFragment.newInstance()
            activity?.supportFragmentManager
                ?.beginTransaction()
                ?.addToBackStack(fragment.tag())
                ?.add(R.id.container, fragment, fragment.tag())
                ?.commitAllowingStateLoss()
        }
    }

    fun initPermissionFragment() {
        runInMainThread {
            val fragment = TodoNoPermissionFragment.newInstance()
            activity?.supportFragmentManager
                ?.beginTransaction()
                ?.addToBackStack(fragment.tag())
                ?.add(R.id.container, fragment, fragment.tag())
                ?.commitAllowingStateLoss()
        }
    }

    fun switchPermissionToList(bundle: Bundle? = null) {
        runInMainThread {
            val fragment = TodoListFragment.newInstance()
            activity?.supportFragmentManager
                ?.beginTransaction()
                ?.addToBackStack(fragment.tag())
                ?.replace(R.id.container, fragment.apply {
                    arguments = bundle
                }, fragment.tag())
                ?.commitAllowingStateLoss()
        }
    }
}