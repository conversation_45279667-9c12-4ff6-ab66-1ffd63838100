/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/4/30
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/4/30      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import android.graphics.Typeface
import android.util.SparseArray
import android.widget.TextView

object VariationFontProvider {
    private val sansSerifFontCache = SparseArray<Typeface>()

    fun getSansSerifVariationFont(tv: TextView, fontVariation: Int): Typeface {
        return getSansSerifVariationFont(sansSerifFontCache, tv, fontVariation)
    }

    private fun getSansSerifVariationFont(cache: SparseArray<Typeface>, tv: TextView, fontVariation: Int): Typeface {
        if (cache.get(fontVariation) != null) {
            return cache.get(fontVariation)
        } else {
            tv.let {
                it.paint.fontVariationSettings = "'wght' $fontVariation"
                return Typeface.create(it.paint.typeface, Typeface.NORMAL).also { typeface ->
                    cache.put(
                        fontVariation,
                        typeface
                    )
                }
            }
        }
    }
}