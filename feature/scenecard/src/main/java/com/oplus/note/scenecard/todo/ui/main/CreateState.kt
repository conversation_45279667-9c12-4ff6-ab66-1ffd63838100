/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/6/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/6/10      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.main

import androidx.annotation.IntDef

@Retention(AnnotationRetention.SOURCE)
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE_PARAMETER)
@IntDef(
    CreateState.DEFAULT_STATE,
    CreateState.EMPTY_CONTENT,
    CreateState.NETWORK_ERROR,
    CreateState.NO_PERMISSION,
    CreateState.CREATE_BREAK,
    CreateState.NETWORK_ERROR_WHEN_WAITING
)
annotation class CreateState {
    companion object {
        //新建待办异常提醒
        const val DEFAULT_STATE = 0
        const val EMPTY_CONTENT = 1
        const val NETWORK_ERROR = 2
        const val NO_PERMISSION = 3
        const val CREATE_BREAK = 4
        const val NETWORK_ERROR_WHEN_WAITING = 5
    }
}
