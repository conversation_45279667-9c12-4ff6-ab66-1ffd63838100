/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TodoDetailAnimationHelper.kt
** Description:
** Version: 1.0
** Date : 2023/3/6
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/3/6      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.content.Context
import android.os.Build
import android.transition.Fade
import android.transition.TransitionInflater
import android.transition.TransitionSet
import android.view.View
import androidx.core.animation.doOnEnd
import androidx.annotation.RequiresApi
import androidx.core.transition.doOnEnd
import androidx.core.transition.doOnStart
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.databinding.TodoCardDetailFragmentBinding
import java.util.*

/**
 * 相关动画列表 http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&version=V%2013&id=7590
 * 打开详情页的共享动画 https://cod-tools.myoas.com/AE-Timeline/?props=2023-02-21%2015%3A10%3A29.874_ae
 * */
@RequiresApi(Build.VERSION_CODES.R)
class TodoDetailAnimationHelper(val context: Context, val rootView: TodoCardDetailFragmentBinding?) {

    companion object {
        private const val TAG = "TodoDetailAnimationHelper"
        const val DURATION_400 = 400L
        private const val DURATION_180 = 180L
        private const val START_DELAY_73 = 73L
        private const val START_DELAY_100 = 100L
        private const val START_DELAY_147 = 147L
        var mOpenTransition = true
        var openTransitionAnimationRunning = false
    }

    /**
     * 重建场景导致动画异常，直接设置动画view为最终显示的状态，不执行动效
     */
    fun setViewFinalState() {
        rootView?.contentForAnimation?.alpha = 0F
        rootView?.timeForAnimation?.alpha = 0F
        rootView?.wholeBackground?.alpha = 1F

        rootView?.btnCancel?.visibility = View.VISIBLE
        rootView?.btnConfirm?.visibility = View.VISIBLE

        rootView?.btnCancel?.let {
            rootView.btnConfirm.let { it1 ->
                ButtonAnimationHelper(context,
                    it, it1
                ).setViewFinalState()
            }
        }
        TodoListAnimationManager.setViewFinalState()

        rootView?.tvDetailContent?.alpha = 1F
        rootView?.clDetailDate?.alpha = 1F
        rootView?.tbDetail?.root?.alpha = 1F
    }

    fun prepareSharedElementTransitionAnimation(activity: Activity?) {
        mOpenTransition = true
        (TransitionInflater.from(context).inflateTransition(android.R.transition.move) as TransitionSet).apply {
            duration = DURATION_400
            interpolator = COUIMoveEaseInterpolator()
            addTransition(ChangeRadiusImageTransform())
            doOnStart {
                if (mOpenTransition) {
                    openTransitionAnimationRunning = true
                    AppLogger.BASIC.d(TAG, "Transition doOnStart")
                    startOpenAnimation()
                }
            }
            doOnEnd {
                if (mOpenTransition) {
                    startTextAlphaInAnimation()
                }
                openTransitionAnimationRunning = false
                mOpenTransition = false
            }
            activity?.window?.sharedElementEnterTransition = this
            activity?.window?.sharedElementExitTransition = this
        }
        activity?.window?.enterTransition = Fade()
        activity?.window?.exitTransition = Fade()
    }

    /**中间文字 alpha 消失、背景 alpha 出现，底部 button 动画*/
    fun startOpenAnimation() {
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.contentForAnimation, "alpha", 1f, 0f),
                ObjectAnimator.ofFloat(rootView?.timeForAnimation, "alpha", 1f, 0f)
            )
            startDelay = START_DELAY_73
            start()
        }

        AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.wholeBackground, "alpha", 0f, 1f)
            )
            start()
        }

        rootView?.root?.postDelayed({
            rootView.btnCancel.visibility = View.VISIBLE
            rootView.btnConfirm.visibility = View.VISIBLE
            startButtonAnimation(true)
        }, START_DELAY_100)
    }

    /** 显示顶部文字和箭头等 */
    private fun startTextAlphaInAnimation() {
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.tvDetailContent, "alpha", 0f, 1f),
                ObjectAnimator.ofFloat(rootView?.clDetailDate, "alpha", 0f, 1f),
                ObjectAnimator.ofFloat(rootView?.tbDetail?.root, "alpha", 0f, 1f),
            )
            start()
        }
    }

    /**中间文字 alpha 显示，真正的文字和 back 键 alpha 隐藏*/
    fun startCloseAnimation(animationEndCallback: () -> Unit) {
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.contentForAnimation, "alpha", 0f, 1f),
                ObjectAnimator.ofFloat(rootView?.timeForAnimation, "alpha", 0f, 1f)
            )
            startDelay = START_DELAY_147
            start()
        }
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.tvDetailContent, "alpha", 1f, 0f),
                ObjectAnimator.ofFloat(rootView?.clDetailDate, "alpha", 1f, 0f),
                ObjectAnimator.ofFloat(rootView?.tbDetail?.root, "alpha", 1f, 0f),
            )
            doOnEnd {
                animationEndCallback.invoke()
            }
            start()
        }

        AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.wholeBackground, "alpha", 1f, 0f)
            )
            start()
        }
        startButtonAnimation(false)
    }

    /** 底部 button 位移和 alpha 动画*/
    private fun startButtonAnimation(open: Boolean) {
        rootView?.btnCancel?.let {
            rootView.btnConfirm.let { it1 ->
                ButtonAnimationHelper(context,
                    it, it1
                ).startAnimation(open)
            }
        }
        TodoListAnimationManager.startButtonAnimation(open)
    }

    /**
     * 点击删除或完成按钮时关闭页面，不执行转场动画，执行alpha消失
     * https://cod-tools.myoas.com/AE-Timeline/?props=2023-02-21%2017%3A09%3A25.192_ae
     * */
    fun startDirectCloseAnimation(endCallback: () -> Unit) {
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView?.root, "alpha", 1f, 0f)
            )
            doOnEnd {
                endCallback.invoke()
            }
            start()
        }
        rootView?.btnCancel?.let {
            rootView.btnConfirm.let { it1 ->
                ButtonAnimationHelper(context, it, it1).startAnimation(
                    open = false,
                    isDirectClose = true
                )
            }
        }
        TodoListAnimationManager.startDirectCloseAnimation()
    }
}