/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/3/8
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/3/8      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import android.content.Context
import com.oplus.note.scenecard.R
import com.oplus.note.utils.DateAndTimeUtils
import java.util.*

object DateTimeUtil {
    private const val DATE_YESTERDAY_0 = -24
    private const val DATE_TODAY_0 = 0
    private const val DATE_TOMORROW_0 = 24
    private const val DATE_TOMORROW_24 = 48

    @JvmStatic
    fun getTimeDesc(context: Context, dateTime: Long): String {
        if (dateTime <= 0) {
            //无日期的
            return ""
        }
        val before = getDateByType(DATE_YESTERDAY_0).time
        val yesterday = getDateByType(DATE_TODAY_0).time
        val today = getDateByType(DATE_TOMORROW_0).time
        val tomorrow = getDateByType(DATE_TOMORROW_24).time
        return if (dateTime < before) {
            //昨天之前
            DateAndTimeUtils.timeInMillis2DateAndTime(context, dateTime, true)
        } else if (dateTime < yesterday) {
            //昨天
            context.getString(R.string.scene_category_yesterday, DateAndTimeUtils.timeInMillis2Time(context, dateTime, true))
        } else if (dateTime < today) {
            //今天
            context.getString(R.string.scene_category_today, DateAndTimeUtils.timeInMillis2Time(context, dateTime, true))
        } else if (dateTime < tomorrow) {
            //明天
            context.getString(R.string.scene_category_tomorrow, DateAndTimeUtils.timeInMillis2Time(context, dateTime, true))
        } else {
            //之后
            DateAndTimeUtils.timeInMillis2DateAndTime(context, dateTime, true)
        }
    }

    @JvmStatic
    fun getDateByType(dateType: Int): Date {
        return Calendar.getInstance().apply {
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            set(Calendar.HOUR_OF_DAY, dateType)
        }.time
    }
}