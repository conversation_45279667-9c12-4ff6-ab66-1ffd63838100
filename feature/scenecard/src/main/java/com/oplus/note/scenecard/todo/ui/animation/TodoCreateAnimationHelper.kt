/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - TodoCreateAnimationHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2023/2/23      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.os.Build
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.note.logger.AppLogger

/**
 * 新建待办页打开的动画 https://cod-tools.myoas.com/AE-Timeline/?props=2023-02-21%2017%3A13%3A54.380_ae
 * */
@RequiresApi(Build.VERSION_CODES.R)
class TodoCreateAnimationHelper(val context: Context, val rootView: ViewGroup) {

    companion object {
        private const val DURATION_180 = 180L
        const val DURATION_400 = 400L
        private const val START_DELAY_67 = 67L
        private const val START_DELAY_220 = 220L
        private const val START_DELAY_287 = 287L
        private const val TAG = "TodoCreateAnimationHelper"
    }

    private var animatorSet: AnimatorSet? = null

    fun startOpenAnimation() {
        AppLogger.BASIC.d(TAG, "startOpenAnimation")
        /**1、背景 alpha 动画*/
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView, "alpha", 0f, 1f)
            )
            startDelay = 0
            start()
        }
    }

    fun startCloseAnimation(animationEndCallback: () -> Unit) {
        AppLogger.BASIC.d(TAG, "startCloseAnimation")
        /**1、背景 alpha 动画*/
        var isCancel = false
        animatorSet = AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(rootView, "alpha", 1f, 0f)
            )
            startDelay = START_DELAY_287
            doOnCancel {
                isCancel = true
            }
            doOnEnd {
                AppLogger.BASIC.d(TAG, "startCloseAnimation doOnEnd, isCancel=$isCancel")
                if (!isCancel) {
                    animationEndCallback.invoke()
                }
            }
            start()
        }
    }

    fun cancelAnimation() {
        AppLogger.BASIC.d(TAG, "cancelAnimation")
        animatorSet?.cancel()
    }
}