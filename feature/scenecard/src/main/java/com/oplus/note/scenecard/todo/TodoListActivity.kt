/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo

import android.annotation.SuppressLint
import android.app.ActivityOptions
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.annotation.IntegerRes
import androidx.annotation.RequiresApi
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.ContinueUtils
import com.nearme.note.util.FoldPhoneUtils
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.common.CommonFactory
import com.oplus.note.edgeToEdge.EdgeToEdgeActivity
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.databinding.ActivityTodoListBinding
import com.oplus.note.scenecard.todo.ui.controller.TodoFragmentsManager
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController
import com.oplus.note.scenecard.todo.ui.fragment.TodoCreateFragment
import com.oplus.note.scenecard.todo.ui.fragment.TodoCreateFragment.Companion.APP_TODO_CARD
import com.oplus.note.scenecard.todo.ui.fragment.TodoNoPermissionFragment
import com.oplus.note.scenecard.todo.ui.main.CreateState
import com.oplus.note.scenecard.todo.ui.main.TodoListFragment
import com.oplus.note.scenecard.todo.ui.main.TodoListViewModel
import com.oplus.note.scenecard.todo.ui.view.CreateButtonPanelView
import com.oplus.note.scenecard.utils.ContinueUtil
import com.oplus.note.scenecard.utils.PointUtil
import com.oplus.note.scenecard.utils.SceneCardConstants
import com.oplus.note.semantic.api.SemanticFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@RequiresApi(Build.VERSION_CODES.R)
class TodoListActivity : EdgeToEdgeActivity() {
    private val viewModel: TodoListViewModel by  viewModels<TodoListViewModel>()
    private var binding: ActivityTodoListBinding? = null
    private var clickToast: Toast? = null
    private var foldPhoneUtils: FoldPhoneUtils? = null
    private var onStoping = false
    private val changeListener = FragmentManager.OnBackStackChangedListener {
        supportFragmentManager.let {
            if (it.backStackEntryCount > 0) {
                val backStackEntry = it.getBackStackEntryAt(it.backStackEntryCount - 1)
                if (backStackEntry.name.equals(TodoListFragment.TAG)) {
                    AppLogger.BASIC.d(TAG, "onBackStackChanged========isTop")
                    binding?.root?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_AUTO
                    checkNeedShowToast()
                } else {
                    AppLogger.BASIC.d(TAG, "onBackStackChanged========isNotTop")
                    binding?.root?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO_HIDE_DESCENDANTS
                }
            }
        }
    }
    private var exportSpeechLanguage: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.i(TAG, "onCreate error: Todo is deprecated.")
            CommonFactory.get()?.openCalendarTodoList(this)
            finish()
            return
        }
        binding = ActivityTodoListBinding.inflate(LayoutInflater.from(this))
        setContentView(binding?.root)
        TodoFragmentsManager.registerManager(this)
        initFragment(savedInstanceState)
        initListener()
        foldPhoneUtils = ContinueUtils.registerListener(this, foldPhoneUtils, object : FoldPhoneUtils.ScreenListener {
            @SuppressLint("ResourceType")
            override fun onScreenChange(isSmallScreen: Boolean) {
                if (!isSmallScreen && !onStoping) {
                    var handled = false
                    val todoCreateFragment = supportFragmentManager.findFragmentByTag(TodoCreateFragment.TAG) as? TodoCreateFragment
                    todoCreateFragment?.apply {
                        if (continueGrantPermission() && !ContinueUtil.continued) {
                            handled = true
                            dismissBlockDialog()
                            if (continueToList) {
                                jumpToMain(APP_TODO_CARD, 0)
                            } else {
                                jumpToMain(APP_TODO_CARD, 0, true, isForceRemind())
                            }
                        }
                    }
                    if (!handled && !ContinueUtil.continued) {
                        val fragment = supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment
                        fragment?.apply {
                            handled = true
                            jumpToMain(APP_TODO_CARD, this.getFirstPosition())
                            PointUtil.setAutoSwitchScreen(this@TodoListActivity)
                        }
                    }
                    if (!handled && !ContinueUtil.continued) {
                        val permissionFragment = supportFragmentManager.findFragmentByTag(TodoNoPermissionFragment.TAG) as? TodoNoPermissionFragment
                        permissionFragment?.apply {
                            handled = true
                            jumpToMain(APP_TODO_CARD, 0)
                            PointUtil.setAutoSwitchScreen(this@TodoListActivity)
                        }
                    }
                }

                ContinueUtil.continued = false
                AppLogger.BASIC.d(TAG, "onScreenChange isSmallScreen: $isSmallScreen $onStoping")
                //用户需知界面展开接续到内屏的时候，把外屏finish，不然在内屏同意用户须知后回到外屏，用户须知界面会先出现再消失
                if (!isSmallScreen
                    && supportFragmentManager.backStackEntryCount == 1
                    && supportFragmentManager.getBackStackEntryAt(0).name == TodoNoPermissionFragment.TAG
                ) {
                    finish()
                }
            }
        })
    }

    /**
     * onPausing移动到onStop中，因为系统授权弹窗会导致页面进入onPause，阻止接续，onStop无此问题
     * */
    override fun onStop() {
        super.onStop()
        onStoping = true
    }

    override fun onStart() {
        super.onStart()
        onStoping = false
    }

    private fun initListener() {
        supportFragmentManager.addOnBackStackChangedListener(changeListener)

        binding?.createPanel?.setTouchCallback(object : CreateButtonPanelView.OnTouchEventChangedCallback {

            override fun onActionDown() {
                val fragment = supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment
                fragment?.mAdapter?.shrinkItem()
            }

            override fun onClick() {
                AppLogger.BASIC.d(TAG, "onClick")
                viewModel.isPressCanceled = false
                if (clickToast == null) {
                    clickToast =
                        Toast.makeText(this@TodoListActivity, R.string.todo_press_to_create, Toast.LENGTH_SHORT)
                }
                clickToast?.show()
            }

            override fun isAsrEnabled(): Boolean {
                val fragment = supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment
                val enabled = fragment?.isAsrEnabled() ?: false
                AppLogger.BASIC.d(TAG, "isAsrEnabled $enabled")
                return enabled
            }

            override fun onLongPressed() {
                AppLogger.BASIC.d(TAG, "onLongPressed")
                val fragment =
                    supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment ?: return
                fragment.enterCreateWithAnimation(exportSpeechLanguage)
            }

            override fun onLongPressedCancel(saveContent: Boolean, isCancelAction: Boolean) {
                AppLogger.BASIC.d(TAG, "onLongPressUp, saveContent=$saveContent,cancel=$isCancelAction")
                viewModel.isPressCanceled = true
                val count = supportFragmentManager.backStackEntryCount
                if (count > 0) {
                    val backStackEntry = supportFragmentManager.getBackStackEntryAt(count - 1)
                    if (backStackEntry.name.equals(TodoCreateFragment.TAG)) {
                        AppLogger.BASIC.d(TAG, "backStackEntry.name.equals(TodoCreateFragment.TAG)")
                        val fragment =
                            supportFragmentManager.findFragmentByTag(TodoCreateFragment.TAG) as? TodoCreateFragment
                                ?: return
                        fragment.onLongPressUp(saveContent, isCancelAction)
                    }
                }
            }

            override fun needWait(): Boolean {
                AppLogger.BASIC.d(TAG, "needWait...")
                val fragment = supportFragmentManager.findFragmentByTag(TodoCreateFragment.TAG) as? TodoCreateFragment
                    ?: return false
                return fragment.needWaitAsr()
            }
        })
    }

    override fun onResume() {
        super.onResume()
        AppLogger.BASIC.d(TAG, "onResume，state=${viewModel.createState}")
        if (viewModel.createState == CreateState.CREATE_BREAK) {
            Toast.makeText(this, R.string.todo_break_create, Toast.LENGTH_SHORT).show()
            viewModel.createState = CreateState.DEFAULT_STATE
        }
        val fragment =
                supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment
                        ?: return
        fragment.mAdapter.setAutoFix(fastScroll = false, overScroll = false, true)
        //Cache Speech Translation Language
        lifecycleScope.launch(Dispatchers.IO) {
            exportSpeechLanguage = SpeechServiceAgentFactory.getGoogleService()?.getLanguage(this@TodoListActivity)
            AppLogger.BASIC.d(TAG, "exportSpeechLanguage:$exportSpeechLanguage")
        }
    }

    private fun checkNeedShowToast() {
        AppLogger.BASIC.d(TAG, "checkNeedShowToast，state=${viewModel.createState}")

        when (viewModel.createState) {
            CreateState.EMPTY_CONTENT -> Toast.makeText(this, R.string.todo_toast_no_content, Toast.LENGTH_SHORT).show()
            CreateState.NETWORK_ERROR -> {
                if (SpeechServiceAgentFactory.get()?.getType() != ISpeechService.SPEECH_GOOGLE) {
                    Toast.makeText(this, R.string.todo_toast_network_error, Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, R.string.unable_to_connect_google_service, Toast.LENGTH_SHORT).show()
                }
            }

            CreateState.NETWORK_ERROR_WHEN_WAITING -> Toast.makeText(this, R.string.todo_net_error, Toast.LENGTH_SHORT).show()
            CreateState.NO_PERMISSION -> Toast.makeText(this, R.string.todo_toast_no_permission, Toast.LENGTH_SHORT).show()
        }
        if (viewModel.createState != CreateState.CREATE_BREAK) {
            viewModel.createState = CreateState.DEFAULT_STATE
        }
    }

    private fun initFragment(savedInstanceState: Bundle?) {
        AppLogger.BASIC.d(TAG, "initFragment,savedInstanceState = $savedInstanceState")
        val agreed = TodoRepoFactory.get()?.checkGrantedPrivacyPolicy(this) ?: false
        if (savedInstanceState == null) {
            if (agreed) {
                TodoFragmentsManager.initListFragment()
            } else {
                setPanelViewVisibility(false)
                TodoFragmentsManager.initPermissionFragment()
            }
        }
        if (agreed) {
            SemanticFactory.get()?.install(this)
        }
    }

    fun setPanelViewVisibility(visible: Boolean) {
        binding?.createPanel?.isVisible = visible
    }

    /**
     * 显示网络错误文本
     */
    fun setPanelViewError() {
        binding?.createPanel?.setCreateTextView(true, TodoListViewModel.STATE_ERROR)
    }

    /**
     * 准备中不显示文本
     */
    fun setPanelViewLoading() {
        binding?.createPanel?.setCreateTextView(false, TodoListViewModel.STATE_PREPARING)
    }

    /**
     * 倾听中显示松开完成新建
     */
    fun setPanelViewListening() {
        binding?.createPanel?.setCreateTextView(true, TodoListViewModel.STATE_LISTENING)
    }

    /**
     * 文字达到上限自动保存，执行松手动效
     */
    fun autoSaveWhenContentMaxSize() {
        binding?.createPanel?.pressUpManual()
    }

    /**
     * 新建流程完成后，将面板背景恢复为白色
     */
    fun completeCreateTask() {
        binding?.createPanel?.stopWaitingAnimation()
    }

    override fun onDestroy() {
        AppLogger.BASIC.d(TAG, "onDestroy")
        super.onDestroy()
        TodoRepoFactory.get()?.dismissPrivacyPolicyDialog()
        TodoFragmentsManager.unRegisterManager()
        TodoResourceController.saveColorIndexToSp(this)
        supportFragmentManager.removeOnBackStackChangedListener(changeListener)
        exportSpeechLanguage = null
        foldPhoneUtils?.unregister(this)
    }

    companion object {
        const val SNACK_BAR_DURATION = 2000
        private const val TAG = "TodoListActivity"
        const val POSITION = "position"
    }

    /**
     * Use continuation to jump to MainActivity
     */
    @RequiresApi(Build.VERSION_CODES.R)
    fun jumpToMain(extra: String, @IntegerRes position: Int, needPermission: Boolean = false, forceRemind: Boolean = false) {
        val intent = Intent("action.nearme.note.allnote").apply {
            putExtra("action_from", extra)
            putExtra(POSITION, position)
            putExtra(SceneCardConstants.NEED_PERMISSION, needPermission)
            putExtra(SceneCardConstants.FORCE_REMINDER, forceRemind)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_CLEAR_TASK
            `package` = packageName
        }
        val options = ActivityOptions.makeBasic()
        //launchDisplayId 0 为内屏，1为外屏
        options.launchDisplayId = 0
        AppLogger.BASIC.d(TAG, "jumpToMain")
        startActivity(intent, options.toBundle())
    }
}