/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - VibrateUtils
 ** Description:
 **         v1.0:   Vibrate Utils
 **
 ** Version: 1.0
 ** Date: 2023/02/21
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/21   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.utils

import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.os.VibrationEffect
import android.os.Vibrator
import androidx.annotation.VisibleForTesting
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusVibrateProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object VibrateUtils {

    private const val TAG = "VibrateUtils"
    private const val DURATION = 50L

    @JvmStatic
    fun execVibrate(context: Context?) {
        if (context == null) {
            AppLogger.BASIC.d(TAG, "execVibrate. no context")
            return
        }

        GlobalScope.launch(Dispatchers.Default) {
            startVibrate(context)
        }
    }

    @SuppressLint("WrongConstant")
    @JvmStatic
    @VisibleForTesting
    fun startVibrate(context: Context) {
        val success = OplusVibrateProxy.vibrateByLinearmotor(context)
        if (!success) {
            val vibrator = context.getSystemService(Service.VIBRATOR_SERVICE) as? Vibrator
            if (vibrator != null && vibrator.hasVibrator()) {
                AppLogger.BASIC.d(TAG, "vibrate.")
                vibrator.vibrate(
                    VibrationEffect.createOneShot(
                        DURATION,
                        VibrationEffect.DEFAULT_AMPLITUDE
                    )
                )
            } else {
                if (vibrator == null) {
                    AppLogger.BASIC.e(TAG, "fail to get vibrator service.")
                } else {
                    AppLogger.BASIC.e(TAG, "no vibrator.")
                }
            }
        }
    }
}