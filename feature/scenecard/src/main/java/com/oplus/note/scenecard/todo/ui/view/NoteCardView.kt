/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AlarmView
 ** Description:
 **         v1.0:   Create AlarmView file
 **
 ** Version: 1.0
 ** Date: 2023/02/13
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/2/13   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.graphics.Outline
import android.util.AttributeSet
import android.view.View
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import com.oplus.note.scenecard.R

class NoteCardView : FrameLayout {
    constructor(ctx: Context) : super(ctx) {
        init(ctx)
    }
    constructor(ctx: Context, attributeSet: AttributeSet?) : super(ctx, attributeSet) {
        init(ctx)
    }

    private fun init(context: Context) {
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline?) {
                view?.apply {
                    outline?.setRoundRect(
                        0,
                        0,
                        view.width,
                        view.height,
                        context.resources.getDimension(R.dimen.dimen_radius)
                    )
                }
            }
        }
        clipToOutline = true
    }
}