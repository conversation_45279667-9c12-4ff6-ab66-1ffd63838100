/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.main

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.os.Build
import android.text.TextUtils
import android.util.LayoutDirection
import android.view.*
import android.widget.CheckBox
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.ViewUtils
import androidx.core.view.children
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.slideview.COUISlideView
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.ViewUtils.calculateAlpha
import com.oplus.note.scenecard.todo.ui.ViewUtils.calculateScale
import com.oplus.note.scenecard.todo.ui.ViewUtils.setAlarmDateTextColorDrawableRight
import com.oplus.note.scenecard.todo.ui.animation.HighLightAnimationHelper
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController
import com.oplus.note.scenecard.todo.ui.view.CreateButtonPanelView
import com.oplus.note.scenecard.todo.ui.view.NoteCardView
import com.oplus.note.scenecard.todo.ui.view.NoteSlideView
import com.oplus.note.scenecard.utils.ReflectionUtils
import com.oplus.note.scenecard.utils.VariationFontProvider
import com.oplus.note.repo.todo.TodoItem
import java.util.Locale
import kotlin.math.abs

@RequiresApi(Build.VERSION_CODES.R)
class TodoListAdapter(var context: Context, var recyclerView: RecyclerView) :
    RecyclerView.Adapter<TodoListAdapter.SlideViewHolder>() {
    var mItemClickListener: TodoListFragment.TodoItemHandListener? = null
    var mTodoItems: List<TodoItem> = mutableListOf()
    private var mLastClickTime = 0L
    private var mLayoutManager: LinearLayoutManager? = null
    private var sizeLarge = context.resources.getDimension(R.dimen.todo_content_large)
    private var sizeMedium = context.resources.getDimension(R.dimen.todo_content_medium)
    private var sizeNormal = context.resources.getDimension(R.dimen.todo_content_normal)
    private var sizeSmall = context.resources.getDimension(R.dimen.todo_content_small)
    private var timeLarge = context.resources.getDimension(R.dimen.dimen_time_large)
    private var timeNormal = context.resources.getDimension(R.dimen.list_time_normal)
    private var dimen2 = context.resources.getDimension(R.dimen.dimen_2)
    private var clickedTripleData: Triple<SlideViewHolder, NoteSlideView, TodoItem>? = null
    var mLastSlideViewWithStatusOn: COUISlideView? = null
    private var isCancelTouch = false
    private var mIsMoved = false
    var createButtonPanelView: CreateButtonPanelView? = null
    var addFooter = false
    val highLightAnimationHelper = HighLightAnimationHelper()
    private var slideClose = false
    private var currentSlideView: COUISlideView? = null
    var slideChangeListener: OnSlideStatusChangeListener? = null
    var scroller: LinearSmoothScroller? = null

    companion object {
        private const val TAG = "TodoListAdapter"
        private const val DELAY = 500L
        private const val DELAY_ITEM = 50L
        private const val NUMBER02 = 0.2f
        private const val NUMBER05 = 0.5f
        private const val NUMBER06 = 0.6f
        private const val NUMBER25 = 2.5f
        private const val NUMBER15 = 1.5f
        const val VIEW_TYPE_UNDEFINE = 0
        const val VIEW_TYPE_HUGE = 1
        const val VIEW_TYPE_MIDIUM = 2
        const val VIEW_TYPE_NORMAL = 3
        const val VIEW_TYPE_SMALL = 4
        const val TIME_INTERVAL = 800L
        private const val MAX_LINES_4 = 4
        private const val FONT_HUGE = 1000
        const val FONT_NORMAL = 750
        private const val DURATION_400 = 400L
        const val SHADOW_RADIUS = 60F
        const val SCROLL_LIMIT = 25
        const val HOLDER_WIDTH = -300F
        const val DEFAULT_TRANS = 0F
        const val LIMIT_MARGIN = 21
        const val VIEW_TYPE_CARD = 1
        const val VIEW_TYPE_FOOTER = 2
        const val AUTO_LIMIT = 35F
        const val DURATION_300 = 300L
        const val DURATION_50 = 50L
        const val DURATION_100 = 100


        @JvmStatic
        fun setFontWight(textView: TextView, weight: Int) {
            kotlin.runCatching {
                textView.paint.typeface = VariationFontProvider.getSansSerifVariationFont(textView, weight)
            }
        }

        @Suppress("MaximumLineLength")
        @JvmStatic
        fun setContentTextParaDetail(type: Int?, textView: TextView?, fraction: Float) {
            textView?.apply {
                when (type) {
                    VIEW_TYPE_HUGE -> {
                        setFontWight(textView, FONT_HUGE)
                        textSize = resources.getDimension(R.dimen.todo_content_large)
                        maxLines = MAX_LINES_4
                        ellipsize = TextUtils.TruncateAt.END
                    }
                    VIEW_TYPE_MIDIUM -> {
                        setFontWight(textView, FONT_NORMAL)
                        textSize =
                            resources.getDimension(R.dimen.todo_content_medium) +
                                    fraction * (resources.getDimension(R.dimen.todo_content_large) - resources.getDimension(
                                R.dimen.todo_content_medium
                            ))
                        maxLines = 1
                        ellipsize = TextUtils.TruncateAt.END
                    }
                    VIEW_TYPE_NORMAL -> {
                        setFontWight(textView, FONT_NORMAL)
                        textSize =
                            resources.getDimension(R.dimen.todo_content_normal) +
                                    fraction * (resources.getDimension(R.dimen.todo_content_medium) - resources.getDimension(
                                R.dimen.todo_content_normal
                            ))
                        maxLines = 1
                        ellipsize = TextUtils.TruncateAt.END
                    }
                    else -> {
                        setFontWight(textView, FONT_NORMAL)
                        textSize =
                            resources.getDimension(R.dimen.todo_content_small) +
                                    fraction * (resources.getDimension(R.dimen.todo_content_normal) - resources.getDimension(
                                R.dimen.todo_content_small
                            ))
                        maxLines = 1
                        ellipsize = TextUtils.TruncateAt.END
                    }
                }
            }
        }
    }

    fun notifyItemChangedByLocalId(localId: String?) {
        if (localId.isNullOrBlank()) {
            AppLogger.BASIC.d(TAG, "notifyItemChangedByLocalId, id isNullOrBlank()")
            notifyDataSetChanged()
            return
        }
        val indexOfFirst = mTodoItems.indexOfFirst {
            it.localId == localId
        }
        AppLogger.BASIC.d(TAG, "notifyItemChangedByLocalId, index=$indexOfFirst")
        if (indexOfFirst >= 0) {
            notifyItemChanged(indexOfFirst)
        } else {
            notifyDataSetChanged()
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == mTodoItems.size) {
            VIEW_TYPE_FOOTER
        } else VIEW_TYPE_CARD
    }

    fun shrinkItem() {
        mLastSlideViewWithStatusOn?.shrink()
    }

    @SuppressLint("ClickableViewAccessibility", "RestrictedApi", "MaximumLineLength")
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SlideViewHolder {
        if (viewType == VIEW_TYPE_CARD) {
            val slideView = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_recycler_delete_group, parent, false) as NoteSlideView
            slideView.isForceDarkAllowed = false
            slideView.setMenuItemStyle(COUISlideView.MENU_ITEM_NORMAL_RECT_STYLE)
            slideView.setOnSlideListener { view, status ->
                slideChangeListener?.onSlideStatusChange(view, status)
                if (mLastSlideViewWithStatusOn != null && mLastSlideViewWithStatusOn !== view && !mLastSlideViewWithStatusOn!!.isSliding) {
                    mLastSlideViewWithStatusOn!!.shrink()
                    slideClose = true
                }
                if (status == COUISlideView.OnSlideListener.SLIDE_STATUS_ON) {
                    mLastSlideViewWithStatusOn = view as COUISlideView
                    currentSlideView = view
                }
                if (status == COUISlideView.OnSlideListener.SLIDE_STATUS_ON) {
                    createButtonPanelView?.animateOut()
                }
                if (status == COUISlideView.OnSlideListener.SLIDE_STATUS_OFF) {
                    if (slideClose) {
                        slideView.postDelayed({
                            if (slideView != currentSlideView) {
                                kotlin.runCatching {
                                    val slideStatus =
                                        ReflectionUtils.getSuperPrivateField(currentSlideView!!, "mCurrStatus") as Int
                                    AppLogger.BASIC.d(TAG, "status $slideStatus  $slideView")
                                    if (slideStatus == 0) {
                                        createButtonPanelView?.animateIn()
                                    }
                                }.onFailure {
                                    AppLogger.BASIC.d(TAG, "mCurrStatus ERROR: $it")
                                }
                            } else {
                                createButtonPanelView?.animateIn()
                            }
                        }, DURATION_300)
                    } else {
                        createButtonPanelView?.animateIn()
                    }
                    slideClose = false
                }
            }
            val myViewHolder = CardViewHolder(itemView = slideView)

            slideView.setOnSmoothScrollListener {
                if (recyclerView.isPressed) {
                    recyclerView.apply {
                        isPressed = false
                        invalidate()
                    }
                }
            }
            slideView.setOnTouchListener { view, event ->
                if (event.pointerCount > 1) {
                    // 屏蔽双指点击事件
                    return@setOnTouchListener true
                }
                var isSlideOn = false
                if (mLastSlideViewWithStatusOn != null && mLastSlideViewWithStatusOn?.slideViewScrollX != 0) {
                    isSlideOn = true
                }
                when (event.actionMasked) {
                    MotionEvent.ACTION_DOWN -> {
                        mIsMoved = false
                        isCancelTouch = if (isSlideOn) {
                            val inMenu: Boolean = if (ViewUtils.isLayoutRtl(recyclerView)) {
                                event.x < mLastSlideViewWithStatusOn!!.holderWidth && event.x < mLastSlideViewWithStatusOn!!.holderWidth
                            } else {
                                event.x > mLastSlideViewWithStatusOn!!.width - mLastSlideViewWithStatusOn!!.holderWidth
                            }
                            if (abs(mLastSlideViewWithStatusOn!!.slideViewScrollX) >= mLastSlideViewWithStatusOn!!.holderWidth &&
                                !inMenu &&
                                !mLastSlideViewWithStatusOn!!.isSliding
                            ) {
                                mLastSlideViewWithStatusOn?.shrink()
                            }
                            view === mLastSlideViewWithStatusOn && !inMenu
                        } else {
                            false
                        }
                        isCancelTouch
                    }
                    MotionEvent.ACTION_MOVE -> {
//                        AppLogger.BASIC.d("bx","my scroll: ${ReflectionUtils.getSuperPrivateField(slideView,"mIsBeingDragged")}")

                        if (slideView.slideViewScrollX != 0) {
                            mIsMoved = true
                        }
                        isCancelTouch
                    }
                    MotionEvent.ACTION_UP -> {
                        if (slideView.slideViewScrollX == 0 && !mIsMoved) {
                            val pos = myViewHolder.adapterPosition
                            if (pos != -1) {
                                mItemClickListener!!.onItemClick(myViewHolder, myViewHolder.itemView, mTodoItems[pos])
                            }
                            mIsMoved = true
                        }
                        isCancelTouch
                    }
                    else -> {
                        mIsMoved = false
                        isCancelTouch
                    }
                }
            }
            slideView.apply {
                setDeleteItemIcon(null)
                setItemBackgroundColor(Color.TRANSPARENT)
                setDeleteEnable(true)
                enableFastDelete(false)
                setRoundRectMenuLeftMargin(0)
                setRoundRectMenuRightMargin(0)
            }
            return myViewHolder
        } else {
            val footer = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_footer, parent, false)
            footer.setOnTouchListener { v, event ->
                if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                    mLastSlideViewWithStatusOn?.shrink()
                }
                false
            }
            return FooterViewHolder(
                itemView = footer
            )
        }
    }


    override fun getItemCount(): Int {
        return mTodoItems.size + if (addFooter) 1 else 0
    }

    fun setData(todos: List<TodoItem>) {
        mTodoItems = todos
    }

    override fun onBindViewHolder(holder: SlideViewHolder, position: Int) {
        AppLogger.BASIC.d("adapter", "onBindView $position")
        holder.bind(position)
    }

    override fun onBindViewHolder(
        holder: SlideViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        AppLogger.BASIC.d("adapter", "onBindView payloads $position")
        holder.bind(position, payloads)
    }

    fun setTodoItemHandListener(listener: TodoListFragment.TodoItemHandListener) {
        mItemClickListener = listener
    }

    open inner class SlideViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        fun bind(position: Int, payloads: MutableList<Any>? = null) {
            if (payloads?.isNotEmpty() == true) {
                rebindTextWithPayload(position, payloads)
            } else {
                rebindText(position)
            }
        }

        open fun rebindTextWithPayload(position: Int, payloads: MutableList<Any>) {
        }

        @SuppressLint("UseCompatLoadingForDrawables")
        open fun rebindText(position: Int) {
        }
    }

    inner class FooterViewHolder(itemView: View) : SlideViewHolder(itemView) {
        override fun rebindText(position: Int) {
            super.rebindText(position)
            AppLogger.BASIC.d(TAG, "bind footer")
        }
    }

    inner class CardViewHolder(itemView: View) : SlideViewHolder(itemView) {
        var title: TextView?
        var rootView: View?
        var bgColor: View?
        var time: TextView?
        var card: NoteCardView?
        var checkBox: CheckBox?
        var overDue: TextView?
        var highlightMask: View?
        var isRtl: Boolean = false

        init {
            isRtl = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL
            val content =
                LayoutInflater.from(itemView.context).inflate(R.layout.item_todo_layout, null)
            title = content.findViewById(R.id.content)
            rootView = content.findViewById(R.id.rootView)
            time = content.findViewById(R.id.time)
            bgColor = content.findViewById(R.id.bg_color)
            card = content.findViewById(R.id.card)
            checkBox = content.findViewById(R.id.done)
            overDue = content.findViewById(R.id.overdue)
            highlightMask = content.findViewById(R.id.highlight_mask)
            (itemView as NoteSlideView).apply {
                contentView = content
                mCardView = card
                mBgColor = bgColor
                mTime = time
                mTitle = title
                mOverDue = overDue
                mDone = checkBox
            }
        }

        override fun rebindTextWithPayload(position: Int, payloads: MutableList<Any>) {
            super.rebindTextWithPayload(position, payloads)
            payloads.forEach {
                if (it == HighLightAnimationHelper.HIGHLIGHT_CHANGE) {
                    highLightAnimationHelper.requestHighLight(highlightMask)
                }
            }
        }

        override fun rebindText(position: Int) {
            super.rebindText(position)
            val todoItem = mTodoItems[position]
            itemView.post {
                highLightAnimationHelper.cancelHighLight(highlightMask)
                (itemView as NoteSlideView).apply {
                    if (!isRtl && translationX < HOLDER_WIDTH || isRtl && translationX > -HOLDER_WIDTH) {
                        translationX = DEFAULT_TRANS
                        shrink()
                    }
                    if (!isRtl && contentView.translationX < HOLDER_WIDTH ||
                            isRtl && contentView.translationX > -HOLDER_WIDTH) {
                        contentView.translationX = DEFAULT_TRANS
                        shrink()
                    }
                    mPosition = position
                    //此处添加点击事件
                    mBgColor?.setBackgroundResource(TodoResourceController.getDataColor(todoItem))
                    mTime?.apply {
                        setTime(this, mOverDue, todoItem)
                        alpha = 1f
                    }
                    mTitle?.apply {
                        text = todoItem.content
                        alpha = 1f
                        setContentTextPara(this)
                    }
                    setOnDeleteItemClickListener {
                        mItemClickListener?.delete(todoItem, position)
                    }
                    checkBox?.isChecked = false
                    checkBox?.setOnCheckedChangeListener { _, isChecked ->
                        if (isChecked) {
                            mItemClickListener?.done(
                                todoItem,
                                position
                            )
                        }
                    }
                }
            }
        }
    }

    private fun setTime(textView: TextView?, overDue: TextView?, todo: TodoItem) {
        if (todo.getTodoExtra()?.forceReminder == null || todo.alarmTime == 0L) {
            textView?.visibility = View.GONE
            overDue?.visibility = View.GONE
        } else {
            val color = TodoResourceController.getOverDueColor(todo)
            val alarmIcon = TodoResourceController.getAlarmIcon(todo)
            textView?.apply {
                visibility = View.VISIBLE
                setAlarmDateTextColorDrawableRight(
                    todo,
                    color,
                    alarmIcon
                )
            }
            overDue?.visibility = if (todo.isAlarmExpired()) View.VISIBLE else View.GONE
        }
    }


    /**
     * 外屏字体大小目前的规律如下：ui图给18px，仅在xml中使用，填写18dp，UI正常，18px，字体缩小
     * 如果在代码中设置字体大小，如果值为18px，UI正常，18dp，字体放大，切随系统大小变化而变化
     * **/
    @Suppress("MaximumLineLength")
    fun setContentTextPara(textView: TextView?) {
        textView?.apply {
            setFontWight(textView, FONT_HUGE)
        }
    }

    fun setLayoutManager(layoutManager: LinearLayoutManager) {
        mLayoutManager = layoutManager
    }

    private fun setDeleteEnable(slideView: NoteSlideView, enable: Boolean) {
        if (enable) {
            slideView.setItemBackgroundColor(Color.TRANSPARENT)
            slideView.setDeleteEnable(true)
            slideView.setDeleteItemIcon(null)
            slideView.isEnabled = true
            slideView.mDone?.isEnabled = true
        } else {
            slideView.setDeleteEnable(false)
            slideView.setDeleteItemIcon(null)
            slideView.isEnabled = false
            slideView.mDone?.isEnabled = false
        }
    }


    fun setItemScale() {
        val firstVisibleItemPosition =
            (recyclerView.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
        val lastVisibleItemPosition =
            (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
        for (index in lastVisibleItemPosition downTo firstVisibleItemPosition) {
            val view: View? = recyclerView.layoutManager!!.findViewByPosition(index)
            if (view !is NoteSlideView) {
                continue
            }
            kotlin.runCatching {
                view.post {
                    val rect = Rect()
                    view.post {
                        //双重post保证计算的rect是正确的
                        view.getGlobalVisibleRect(rect)
                        val visibleHeight = rect.height()
                        val totalHeight = view.measuredHeight
                        val px = view.measuredWidth / TodoListFragment.TWO
                        val scale = calculateScale(visibleHeight, totalHeight)
                        val mAlpha = calculateAlpha(visibleHeight, totalHeight)
                        if (index == lastVisibleItemPosition || index == lastVisibleItemPosition - 1 && index != 0) {
                            view.children.first().apply {
                                pivotY = TodoListFragment.DEFAULT_PIVOTY
                                pivotX = px.toFloat()
                                scaleX = scale
                                scaleY = scale
                                alpha = mAlpha
                                if (totalHeight - visibleHeight < LIMIT_MARGIN) {
                                    setDeleteEnable(view, true)
                                } else {
                                    setDeleteEnable(view, false)
                                }
                                if (index == lastVisibleItemPosition && lastVisibleItemPosition == itemCount - 1) {
                                    setDeleteEnable(view, false)
                                    return@post
                                }
                            }
                        } else {
                            view.children.first().apply {
                                pivotY = TodoListFragment.DEFAULT_PIVOTY
                                pivotX = px.toFloat()
                                scaleX = TodoListFragment.DEFAULT_SCALE
                                scaleY = TodoListFragment.DEFAULT_SCALE
                                alpha = TodoListFragment.DEFAULT_ALPHA
                                setDeleteEnable(view, true)
                            }
                            if (index == firstVisibleItemPosition) {
                                AppLogger.BASIC.d(TAG, "limit: ${totalHeight - visibleHeight}")
                                if (totalHeight - visibleHeight < LIMIT_MARGIN) {
                                    setDeleteEnable(view, true)
                                } else {
                                    setDeleteEnable(view, false)
                                }
                            } else {
                                setDeleteEnable(view, true)
                            }
                        }
                    }
                }
            }
        }
    }

    fun setAutoFix(fastScroll: Boolean, overScroll: Boolean, autoMove: Boolean) {
        if (fastScroll || autoMove) {
            AppLogger.BASIC.d(TodoListFragment.TAG, "fast scroll")
            val firstVisibleItemPosition =
                (recyclerView.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
            val view: View? = recyclerView.layoutManager!!.findViewByPosition(firstVisibleItemPosition)
            view?.post {
                val rect = com.oplus.note.scenecard.todo.ui.ViewUtils.getBottomRect(view)
                val visibleHeight = rect.bottom - rect.top
                val totalHeight = view.measuredHeight
                //第一个item overscroll的时候，回弹异常
                if (firstVisibleItemPosition == 0  && overScroll) {
                    AppLogger.BASIC.d(TodoListFragment.TAG, "fast scroll pos 0")
                    smoothScrollToPosition(0, context)
                }
                if (visibleHeight == totalHeight) {
                    return@post
                }
                //向下滑动
                else if (visibleHeight <= totalHeight / 2) {
                    //不到一半，向上滚动，但是最后一个item，是滚不了的，因此需要向下滚动
                    if (recyclerView.canScrollVertically(visibleHeight)) {
                        smoothScrollToPosition(firstVisibleItemPosition + 1, context)
                    } else {
                        smoothScrollToPosition(firstVisibleItemPosition, context)
                    }
                    AppLogger.BASIC.d(TodoListFragment.TAG, "scroll up")
                }
                //向上滑动
                else if (totalHeight - visibleHeight <= totalHeight / 2) {
                    AppLogger.BASIC.d(TodoListFragment.TAG, "scroll down")
                    smoothScrollToPosition(firstVisibleItemPosition, context)
                }
            }
        } else {
            AppLogger.BASIC.d(TodoListFragment.TAG, "slow scroll")
            val firstVisibleItemPosition =
                (recyclerView.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
            val view: View? = recyclerView.layoutManager!!.findViewByPosition(firstVisibleItemPosition)
            view?.post {
                val rect = com.oplus.note.scenecard.todo.ui.ViewUtils.getBottomRect(view)
                val visibleHeight = rect.bottom - rect.top
                val totalHeight = view.measuredHeight
                if (firstVisibleItemPosition == 0 && overScroll) {
                    AppLogger.BASIC.d(TodoListFragment.TAG, "slow scroll pos 0")
                    smoothScrollToPosition(0, context)
                }
                if (visibleHeight == totalHeight) {
                    return@post
                }

                //向下滑动
                else if (visibleHeight <= AUTO_LIMIT) {
                    if (recyclerView.canScrollVertically(visibleHeight)) {
                        AppLogger.BASIC.d(TodoListFragment.TAG, "slow scroll up first")
                        smoothScrollToPosition(firstVisibleItemPosition + 1, context)
                    } else {
                        AppLogger.BASIC.d(TodoListFragment.TAG, "slow scroll up first unable,scroll down")
                        smoothScrollToPosition(firstVisibleItemPosition, context)
                    }
                }
                //向上滑动
                else if (totalHeight - visibleHeight <= AUTO_LIMIT) {
                    AppLogger.BASIC.d(TodoListFragment.TAG, "slow scroll down first")
                    smoothScrollToPosition(firstVisibleItemPosition, context)
                }
            }
        }
        //快速滑动，切触发了overscroll，可能导致第一个item最后计算错误
        if (overScroll || autoMove) {
            setItemScale()
            AppLogger.BASIC.d(TAG, "auto fix setScale")
        }
    }

    private fun smoothScrollToPosition(position: Int, context: Context) {
        if (scroller == null) {
            scroller = object : LinearSmoothScroller(context) {
                override fun getVerticalSnapPreference(): Int {
                    return SNAP_TO_START
                }

                override fun calculateTimeForScrolling(dx: Int): Int {
                    return DURATION_100
                }
            }
        }
        scroller?.apply {
            targetPosition = position
            mLayoutManager?.startSmoothScroll(this)
        }
    }

    fun setButtonView(view: CreateButtonPanelView) {
        createButtonPanelView = view
    }

    private fun postDelayShrink(slideView: COUISlideView) {
        slideView.postDelayed({
            kotlin.runCatching {
                val isDragged =
                    ReflectionUtils.getSuperPrivateField(slideView, "mIsBeingDragged") as Boolean
                if (!isDragged) {
                    slideView.shrink()
                }
            }.onFailure {
                AppLogger.BASIC.d("bx", "mIsbeingDragged: $it")
            }
        }, DURATION_50)
    }

    interface OnSlideStatusChangeListener {
        fun onSlideStatusChange(view: View, status: Int)
    }
}