/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - InputContainer
 ** Description:
 **         v1.0:   Create InputContainer file
 **
 ** Version: 1.0
 ** Date: 2023/03/01
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/3/1   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.anim.AlphaAnim
import com.oplus.note.scenecard.utils.DateTimeUtil
import java.util.*

class SpeechInputContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    private var alarmView: View? = null
    var hasTime = false
        private set
    private val alphaAnim = AlphaAnim()
    private var alarmTime: TextView? = null
    private var date: Date? = null


    fun showAlarmView(time: Date?) {
        AppLogger.SPEECH.d(TAG, "showAlarmView,${alarmView == null}")
        alphaAnim.cancelAnim()
        if (alarmView == null) {
            alarmView = findViewById(R.id.ll_alert_time)
        }
        alarmView?.isVisible = true
        if (alarmTime == null) {
            alarmTime = findViewById(R.id.tv_alert_time)
        }
        alarmTime?.isVisible = true
        hasTime = true
        updateTime(time)
        alphaAnim.createAlphaAnim(true, alarmView, ANIM_DURATION).startAnim()
    }

    fun removeAlarmView() {
        AppLogger.SPEECH.d(TAG, "removeAlarmView,${alarmView == null}")
        alphaAnim.cancelAnim()
        alarmView?.let {
            alphaAnim.createAlphaAnim(false, it, ANIM_DURATION)
                .buildListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        it.isVisible = false
                    }
                })
                .startAnim()
        }
        hasTime = false
    }

    fun updateTime(time: Date?) {
        AppLogger.SPEECH.d(TAG, "updateTime,${alarmView == null}")
        if (hasTime) {
            this.date = time
            alarmTime?.text = DateTimeUtil.getTimeDesc(context, time?.time ?: 0L)
        }
    }

    fun getAlarmTime(): Long = date?.time ?: 0L

    companion object {
        private const val TAG = "SpeechInputContainer"
        const val ANIM_DURATION = 180L
    }
}