/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - InputContentController
 ** Description:
 **         v1.0:   control text display
 **
 ** Version: 1.0
 ** Date: 2023/02/21
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/21   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.content.Context
import android.text.Editable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.BackgroundColorSpan
import android.text.style.CharacterStyle
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import androidx.annotation.StringDef
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleCoroutineScope
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.semantic.HightRange
import com.oplus.note.semantic.SemanticTime
import com.oplus.note.semantic.api.ISemanticTool
import com.oplus.note.semantic.api.SemanticFactory
import com.oplus.note.utils.SingleRunner
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.Locale

class InputContentController(
    val context: Context,
    val tv: TextView,
    private val lifecycleCoroutineScope: LifecycleCoroutineScope
) {

    var isWaiting = false
    //Speech language
    var speechLang: String? = null
    var semanticTool: ISemanticTool? = SemanticFactory.get()
    var confirmStatusBlock: ((Boolean) -> Unit)? = null

    private var waitJob: Job? = null
    private var dotStr = ""
    private var speechTextStart = -1
    private var speechTextEnd = -1
    private var alarmTime: Date? = null
    private val semanticBgColor = context.getColor(R.color.semantic_color_background)
    private val semanticFgColor = context.getColor(R.color.semantic_color_foreground)
    private var alarmTimeCallback: AlarmTimeCallback? = null
    private val normalTextColor =
        context.getColor(R.color.color_input_text_color_normal)
    private val uncertainSpanColor =
        context.getColor(R.color.color_input_text_color_uncertain)
    private var startServiceTime = -1L
    private var singleRunner: SingleRunner? = null
    private var singleRunnerNLP: SingleRunner? = null
    private var startServiceSuccess = false
    private var serviceError = false
    private var currentSemantic: SemanticTime? = null
    private val currentNLPContent = mutableListOf<String>()

    //Manually delete the alarm view, then nlp will no longer be triggered
    private var hasRemoveAlarm: Boolean = false

    //Is there already voice input
    private var hasSpeech = false

    init {
        semanticTool?.apply {
            val result = initModel(context)
            AppLogger.BASIC.d(TAG, "init Model:$result")
        }
        singleRunner = SingleRunner()
        singleRunnerNLP = SingleRunner()
        hasSpeech = false
    }

    fun onStartService(block: () -> Unit) {
        /*
         *If the connection time is longer than 1 second, loading is displayed
         */
        startServiceTime = System.currentTimeMillis()
        AppLogger.BASIC.d(TAG, "onStartService:$startServiceTime")
        updateLayoutDirection(speechLang, tv)
        lifecycleCoroutineScope.launch(Dispatchers.Main) {
            flow {
                delay(CONNECT_DELAY)
                emit(0)
            }.flowOn(Dispatchers.IO).collect {
                if (startServiceSuccess.not() && serviceError.not()) {
                    //show connecting;
                    showWaiting(STATUS_CONNECT, true)
                    block.invoke()
                }
            }
        }
    }

    fun onStartServiceSuccess() {
        //start service success, then start listening
    }

    fun onStartServiceFailed() {
        // show prompt
        startServiceSuccess = false
        showError()
    }

    fun onStartListen() {
        // show prompt
        if (isWaiting) {
            showWaiting(STATUS_CONNECT, false)
        }
        showWaiting(STATUS_LISTEN, true)
        startServiceSuccess = true
    }

    fun onError() {
        serviceError = true
        showError()
    }

    @VisibleForTesting
    fun showError() {
        if (isWaiting) {
            showWaiting(STATUS_CONNECT, false)
            updateLayoutDirection(speechLang, tv)
        }
        lifecycleCoroutineScope.launch(Dispatchers.Main) {
            if (!hasSpeech) {
                tv.text = SpannableStringBuilder(context.getString(R.string.todo_toast_network_error)).apply {
                    setSpan(
                        ForegroundColorSpan(uncertainSpanColor),
                        0,
                        this.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
//            Toast.makeText(context, R.string.scene_network_error, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * "倾听中..." / "连接中..."
     * @param waiting true = start show,false = stop show
     */
    @VisibleForTesting
    fun showWaiting(@AwaitStatus type: String, waiting: Boolean) {
        if (waiting) {
            isWaiting = true
            awaitingStatus(type)
            waitJob = lifecycleCoroutineScope.launch(Dispatchers.IO) {
                while (isWaiting) {
                    delay(DOT_DURATION)
                    awaitingStatus(type)
                }
            }
        } else {
            waitJob?.cancel()
            lifecycleCoroutineScope.launch(Dispatchers.Main) {
                tv.text = SpannableStringBuilder("").apply {
                    setSpan(
                        ForegroundColorSpan(normalTextColor),
                        0,
                        this.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }
            isWaiting = false
        }
    }

    private fun awaitingStatus(@AwaitStatus status: String) = lifecycleCoroutineScope.launch(Dispatchers.Main) {
        val wait = if (status == STATUS_LISTEN) {
            context.getString(R.string.scene_listening)
        } else {
            context.getString(R.string.todo_toast_preparing)
        }
        tv.text = SpannableStringBuilder(wait + dotStr).apply {
            setSpan(
                ForegroundColorSpan(uncertainSpanColor),
                0,
                length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        if (dotStr.isEmpty()) {
            dotStr = "."
        } else if (dotStr.length == DOT_1) {
            dotStr = ".."
        } else if (dotStr.length == DOT_2) {
            dotStr = "..."
        } else if (dotStr.length == DOT_3) {
            dotStr = ""
        }
    }

    fun onSpeechServiceResult(result: String?, isFinalResult: Boolean) {
        if (isWaiting) {
            showWaiting(STATUS_LISTEN, false)
            updateLayoutDirection(speechLang, tv)
        }
        AppLogger.DEBUG.d(TAG) { "onSpeechServiceResult => $result ,isFinal:$isFinalResult" }
        val speechText = tv.text
        if (isFinalResult && result?.length == 1 && TextUtils.equals(result, "\n")
            && speechText.isInvalidEnd()
        ) {
            AppLogger.DEBUG.d(TAG) { "onSpeechServiceResult => empty" }
            return
        }

        if (!TextUtils.isEmpty(result)) {
            lifecycleCoroutineScope.launch(Dispatchers.Main) {
                result?.let {
                    insertSpeechText(it, isFinalResult)
                    setConfirmEnable()
                }
            }
        }
    }

    fun onFinishResult(text: String, block: () -> Unit) {
        if (text.isBlank()) {
            return
        }
        lifecycleCoroutineScope.launch {
            singleRunnerNLP?.afterPrevious {
                AppLogger.BASIC.d(TAG, "onFinishResult")
                processNLP()
                withContext(Dispatchers.Main) {
                    block.invoke()
                }
            }
        }
    }

    fun onStopService(confirmEnable: Boolean) {
        if (confirmEnable) {
            tv.text = SpannableStringBuilder.valueOf(tv.text).apply {
                this.setSpan(
                    ForegroundColorSpan(normalTextColor),
                    0,
                    this.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                val result = semanticTool?.process(context, tv.text.toString())
                if (result != null && result.isDateValid()) {
                    //设置文本高亮
                    for (value in result.getHighList()) {
                        this.setSpan(
                            BackgroundColorSpan(semanticBgColor),
                            value.start,
                            value.end,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        this.setSpan(
                            ForegroundColorSpan(semanticFgColor),
                            value.start,
                            value.end,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                }
            }
        }
    }

    private suspend fun insertSpeechText(result: String, isFinalResult: Boolean) {
        if (!isFinalResult) {
            kotlin.runCatching {
                if (speechTextStart < 0) {
                    speechTextStart = tv.length()
                    speechTextEnd = speechTextStart + result.length
                    appendText(getSpannableString(result))
                } else {
                    replaceAndAppend(result)
                }
            }.onSuccess {
                AppLogger.DEBUG.d(TAG) { "insertSpeechText temp,$speechTextStart ==> $speechTextEnd" }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "insertSpeechText temp error,${it.message}")
            }
        } else {
            kotlin.runCatching {
                if ((speechTextStart == -1) || (speechTextEnd == -1)) {
                    appendText(getSpannableString(result))
                } else {
                    replaceAndAppend(result)
                }
            }.onSuccess {
                AppLogger.DEBUG.d(TAG) { "insertSpeechText final,$speechTextStart ==> $speechTextEnd" }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "insertSpeechText final error,${it.message}")
            }
            speechTextStart = -1
            speechTextEnd = -1
        }
    }

    private suspend fun processNLP() {
        AppLogger.DEBUG.d(TAG) { "processNLP in..." }
        if (isWaiting || startServiceSuccess.not()) {
            return
        }
        val originSpan: Editable = tv.editableText ?: return
        val text = originSpan.toString()
        semanticTool?.let {
            val result = it.process(context, text)
            withContext(Dispatchers.Main) {
                autoSetAlarmTime(result, originSpan)
            }
        }
    }

    @Synchronized
    private fun compareNLPContentSame(editable: Editable, highList: MutableList<HightRange>): Boolean {
        val nlpContent = currentNLPContent
        if (nlpContent.isEmpty() || editable.isEmpty() || highList.isEmpty()) {
            return false
        }
        if (highList.size != nlpContent.size) {
            return false
        }
        var isSame = true
        run breaking@{
            highList.forEachIndexed { index, highRange ->
                if ((editable.length < highRange.start) || (editable.length < highRange.end)) {
                    isSame = false
                    return@breaking
                } else {
                    if (currentNLPContent[index] != editable.substring(highRange.start, highRange.end)) {
                        isSame = false
                        return@breaking
                    }
                }
            }
        }
        return isSame
    }

    private fun autoSetAlarmTime(
        semantic: SemanticTime?,
        editable: Editable
    ) {
        AppLogger.DEBUG.d(TAG) { "autoSetAlarmTime in" }
        //新的为null，则直接清除时间
        if (semantic == null || !semantic.isDateValid() || semantic.getHighList().isEmpty()) {
            AppLogger.DEBUG.d(TAG) { "autoSetAlarmTime semantic=null" }
            clearNLPSpan(editable)
            currentSemantic = null
            return
        }
        //旧的为null，则直接设置新的时间
        if (currentSemantic == null) {
            AppLogger.DEBUG.d(TAG) { "autoSetAlarmTime currentSemantic=null" }
            setNLPSpan(semantic, editable)
            currentSemantic = semantic
            return
        }
        //新旧都不为null则比较内容是否相等
        if (semantic.isSemanticTimeEqual(currentSemantic) && compareNLPContentSame(editable, semantic.getHighList())) {
            AppLogger.DEBUG.d(TAG) { "autoSetAlarmTime equals" }
            return
        } else {
            clearNLPSpan(editable)
            setNLPSpan(semantic, editable)
            currentSemantic = semantic
        }
    }

    @Synchronized
    private fun clearNLPSpan(editable: Editable) {
        AppLogger.BASIC.d(TAG, "clearNLPSpan ...")
        //清除NLP高亮span
        val spans = editable.getSpans(
            0, editable.length,
            CharacterStyle::class.java
        )
        for (span in spans) {
            if (span is BackgroundColorSpan && span.backgroundColor == semanticBgColor) {
                editable.removeSpan(span)
            }
            if (span is ForegroundColorSpan && span.foregroundColor == semanticFgColor) {
                editable.removeSpan(span)
            }
        }
        tv.text = editable
        currentNLPContent.clear()
        alarmTimeCallback?.onRemove()
    }

    @Synchronized
    private fun setNLPSpan(semantic: SemanticTime, editable: Editable) {
        alarmTime = semantic.translate2Date()
        AppLogger.BASIC.d(TAG, "setNLPSpan alarmTime=$alarmTime")

        currentNLPContent.clear()
        //设置文本高亮
        run breaking@{
            for (value in semantic.getHighList()) {
                if (value.start > editable.length || value.end > editable.length) {
                    return@breaking
                }
                editable.setSpan(
                    BackgroundColorSpan(semanticBgColor),
                    value.start,
                    value.end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                editable.setSpan(
                    ForegroundColorSpan(semanticFgColor),
                    value.start,
                    value.end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                currentNLPContent.add(editable.substring(value.start, value.end))
            }
        }
        tv.text = editable
        alarmTimeCallback?.onAdd(alarmTime)
    }

    @VisibleForTesting
    fun getSpannableString(
        text: String,
        isFinalResult: Boolean = true
    ): SpannableStringBuilder {

        AppLogger.DEBUG.d(TAG) { "getSpannableString:text:$text tv:${tv.text} final:$isFinalResult" }
        if (isFinalResult) {
            return SpannableStringBuilder(text).apply {
                setSpan(
                    ForegroundColorSpan(normalTextColor),
                    0,
                    this.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } else {
            return SpannableStringBuilder(text).apply {
                setSpan(
                    ForegroundColorSpan(normalTextColor),
                    0,
                    this.length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }
    }

    private fun setConfirmEnable() {
        hasSpeech = true
        confirmStatusBlock?.invoke(true)
    }

    private fun CharSequence?.isInvalidEnd(): Boolean {
        return (this == null || this.endsWith("\n"))
    }

    fun cancel() {
        if (isWaiting) {
            showWaiting(STATUS_LISTEN, false)
        }
        alarmTimeCallback = null
    }

    fun setAlarmTimeCallback(alarmTimeCallback: AlarmTimeCallback) {
        this.alarmTimeCallback = alarmTimeCallback
    }

    fun onRemoveAlarm() {
        hasRemoveAlarm = true
        //清除NLP高亮span
        val origin = SpannableStringBuilder.valueOf(tv.text)
        val spans = origin.getSpans(
            0, origin.length,
            CharacterStyle::class.java
        )
        for (span in spans) {
            if (span is BackgroundColorSpan && span.backgroundColor == semanticBgColor) {
                origin.removeSpan(span)
            }
            if (span is ForegroundColorSpan && span.foregroundColor == semanticFgColor) {
                origin.removeSpan(span)
            }
        }
        tv.text = origin
    }

    interface AlarmTimeCallback {
        fun onAdd(time: Date?)
        fun onRemove()
    }


    /**
     *  Add text to the TextView word by word
     *  When multiple [appendText] are called at the same time, they will be queued for execution
     */
    private suspend fun appendText(words: SpannableStringBuilder) {
        AppLogger.DEBUG.d(TAG) { "appendText:$words" }
        singleRunner?.afterPrevious {
            appendTextInner(words)
        }
    }

    private suspend fun replaceAndAppend(result: String) {
        AppLogger.DEBUG.d(TAG) { "replaceAndAppend $result ,start:$speechTextStart end:$speechTextEnd" }
        singleRunner?.afterPrevious {
            replaceAndAppendInner(result)
        }
    }

    private suspend fun replaceAndAppendInner(result: String) {
        val originSb = StringBuilder(tv.text.toString())
        val newSb = StringBuilder(tv.text.toString())
        AppLogger.DEBUG.d(TAG) { "replaceAndAppendInner in======" }
        newSb.replace(speechTextStart, speechTextEnd, result)
        //新的长度大于当前长度
        if (speechTextEnd <= newSb.length) {
            //重复部分内容相同，则不需要替换，也不需要重新设置span,直接新增后面的内容
            if (originSb.substring(speechTextStart, speechTextEnd) != newSb.substring(speechTextStart, speechTextEnd)) {
                //前面一段内容不相等，直接replace整体string
                val builder = SpannableStringBuilder(tv.text)
                builder.replace(speechTextStart, speechTextEnd, getSpannableString(newSb.substring(speechTextStart, speechTextEnd)))
                tv.text = builder
                currentNLPContent.clear()
            }
            val addPart = newSb.substring(speechTextEnd)
            if (addPart.isNotEmpty()) {
                appendTextInner(getSpannableString(addPart))
            } else {
                AppLogger.DEBUG.d(TAG) { "replaceAndAppendInner,addPart empty" }
                singleRunnerNLP?.afterPrevious {
                    processNLP()
                }
            }
        } else {
            //新的长度小于当前长度,先删除多余的文字
            val builder = SpannableStringBuilder(tv.text)
            builder.delete(newSb.length, builder.length)
            if (newSb.substring(speechTextStart, newSb.length) != originSb.subSequence(speechTextStart, newSb.length)) {
                //文字变短了，并且重复部分的内容也变了，则替换
                builder.replace(speechTextStart, newSb.length, getSpannableString(newSb.substring(speechTextStart)))
                currentNLPContent.clear()
            }
            tv.text = builder
            singleRunnerNLP?.afterPrevious {
                processNLP()
            }
        }
        speechTextEnd = speechTextStart + result.length
        AppLogger.DEBUG.d(TAG) { "replaceAndAppendInner end======" }
    }

    private suspend fun appendTextInner(words: SpannableStringBuilder) {
        AppLogger.DEBUG.d(TAG) { "appendTextInner,words=$words" }
        val spans = words.getSpans(0, words.length, ForegroundColorSpan::class.java)
        flow {
            var append: SpannableStringBuilder
            for (i in words.indices) {
                append = SpannableStringBuilder(words[i].toString()).apply {
                    setSpan(
                        ForegroundColorSpan(spans[0].foregroundColor),
                        0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                delay(APPEND_DELAY)
                emit(append)
            }
        }.flowOn(Dispatchers.IO).onCompletion {
            AppLogger.DEBUG.d(TAG) { "appendTextInner,onCompletion" }
        }.collect {
            val builder = SpannableStringBuilder.valueOf(tv.text)
            builder.append(it)
            AppLogger.DEBUG.d(TAG) { "appendTextInner:$builder" }
            tv.text = builder
            singleRunnerNLP?.afterPrevious {
                processNLP()
            }
        }
    }


    @VisibleForTesting
    fun updateLayoutDirection(lang: String?, content: TextView?) {
        /**
         * On export, the text direction of the [content] should follow the language of the speech engine.
         */
        if (SpeechServiceAgentFactory.get()?.getType() == ISpeechService.SPEECH_GOOGLE) {
            lang?.let {
                val direction = getLayoutDirectionFromLang(it)
                content?.layoutDirection = direction
                if (direction == View.LAYOUT_DIRECTION_RTL) {
                    content?.textDirection = View.TEXT_DIRECTION_RTL
                } else {
                    content?.textDirection = View.TEXT_DIRECTION_LOCALE
                }
            }
        }
    }

    @Retention(AnnotationRetention.SOURCE)
    @StringDef(
        STATUS_CONNECT,
        STATUS_LISTEN,
    )
    annotation class AwaitStatus

    companion object {
        private const val TAG = "InputContentController"
        private const val DOT_DURATION = 500L
        private const val DOT_3 = 3
        private const val DOT_2 = 2
        private const val DOT_1 = 1
        private const val APPEND_DELAY = 10L
        private const val CONNECT_DELAY = 1000L
        private const val STATUS_CONNECT = "connect"
        private const val STATUS_LISTEN = "listen"

        /**
         * Get text directions in a language
         * @param lang  language,like "en-US","zh-CN"
         * @return the layout direction. This may be one of: View.LAYOUT_DIRECTION_LTR or View.LAYOUT_DIRECTION_RTL.
         */
        @JvmStatic
        fun getLayoutDirectionFromLang(lang: String): Int {
            val locale = Locale.forLanguageTag(lang)
            return TextUtils.getLayoutDirectionFromLocale(locale)
        }
    }
}