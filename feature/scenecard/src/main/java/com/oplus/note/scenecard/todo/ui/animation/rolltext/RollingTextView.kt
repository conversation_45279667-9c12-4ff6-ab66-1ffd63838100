/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - RollingTextView.kt
** Description: 数字滚动切换的 view
** Version: 1.0
** Date : 2023/3/27
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/3/27      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation.rolltext

import android.content.Context
import android.content.res.Resources
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.ViewUtils.FLOAT_2

/**   http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&version=V%2014&id=7565   */
class RollingTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "RollingTextView"
    }

    private val textPaint = Paint()
    private val textManager = TextManager(textPaint)

    private var textStyle = Typeface.NORMAL

    private var typeface: Typeface?
        set(value) {
            textPaint.typeface = when (textStyle) {
                Typeface.BOLD_ITALIC -> Typeface.create(value, Typeface.BOLD_ITALIC)
                Typeface.BOLD -> Typeface.create(value, Typeface.BOLD)
                Typeface.ITALIC -> Typeface.create(value, Typeface.ITALIC)
                else -> value
            }
            onTextPaintMeasurementChanged()
        }
        get() = textPaint.typeface

    private var textColor: Int = Color.BLACK
        set(color) {
            if (field != color) {
                field = color
                textPaint.color = color
                invalidate()
            }
        }

    init {
        var text = ""
        var textSize = context.resources.getDimension(R.dimen.todo_count_text_size)

        fun applyTypedArray(arr: TypedArray) {
            text = arr.getString(R.styleable.RollingTextView_android_text) ?: ""
            textColor = arr.getColor(R.styleable.RollingTextView_android_textColor, textColor)
            textSize = arr.getDimension(R.styleable.RollingTextView_android_textSize, textSize)
            textStyle = arr.getInt(R.styleable.RollingTextView_android_textStyle, textStyle)
        }

        val arr = context.obtainStyledAttributes(attrs, R.styleable.RollingTextView,
            defStyleAttr, defStyleRes)

        applyTypedArray(arr)
        textPaint.style = Paint.Style.FILL_AND_STROKE
        textPaint.strokeWidth = FLOAT_2
        textPaint.isAntiAlias = true
        if (textStyle != 0) {
            typeface = textPaint.typeface
        }

        setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
        var initNumber = 0
        kotlin.runCatching {
            initNumber = text.toInt()
        }
        setText(initNumber, false)

        arr.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas.save()
        canvas.translate(0f, textManager.textBaseline)
        textManager.draw(canvas)
        canvas.restore()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val desiredWidth = resolveSize(computeDesiredWidth(), widthMeasureSpec)
        val desiredHeight = resolveSize(computeDesiredHeight(), heightMeasureSpec)
        setMeasuredDimension(desiredWidth, desiredHeight)
        minimumWidth = desiredWidth
    }

    private fun checkForReLayout(): Boolean {
        requestLayout()
        return true
    }

    private fun computeDesiredWidth(): Int {
        return textManager.currentTextWidth.toInt() + paddingLeft + paddingRight
    }

    private fun computeDesiredHeight(): Int {
        return textManager.textHeight.toInt() + paddingTop + paddingBottom
    }

    private fun onTextPaintMeasurementChanged() {
        textManager.updateFontMatrics()
        checkForReLayout()
        invalidate()
    }

    /**
     * @param number 设置数字（大于等于0）
     * @param animate 是否需要滚动效果
     */
    fun setText(number: Int, animate: Boolean =  true) {
        if (getText() == number) {
            //数字相同，无需再修改
            return
        }
        val targetNumber = number.coerceAtLeast(0)
        if (animate) {
            textManager.setText(targetNumber)
            textManager.setAnimationUpdateListener(object : TextColumn.OnAnimationUpdateListener {
                override fun onUpdate() {
                    checkForReLayout()
                    invalidate()
                }
            })
            textManager.startAnimation()
        } else {
            textManager.setText(targetNumber)
            textManager.onAnimationEnd()
            checkForReLayout()
            invalidate()
        }
        contentDescription = String.format("%d", getText())
    }

    fun setTextSize(unit: Int, size: Float) {
        val r: Resources = context?.resources ?: Resources.getSystem()
        textPaint.textSize = TypedValue.applyDimension(unit, size, r.displayMetrics)
        onTextPaintMeasurementChanged()
    }

    override fun getBaseline(): Int {
        val fontMetrics = textPaint.fontMetrics
        return (textManager.textHeight / 2 + ((fontMetrics.descent - fontMetrics.ascent) / 2 - fontMetrics.descent)).toInt()
    }

    fun getText(): Int {
        return textManager.currentNumber
    }

    fun getTextMaxWidth(text: Int): Float {
        return textManager.maxCharWidth(1, textPaint) * text.toString().length + paddingStart + paddingEnd
    }
}