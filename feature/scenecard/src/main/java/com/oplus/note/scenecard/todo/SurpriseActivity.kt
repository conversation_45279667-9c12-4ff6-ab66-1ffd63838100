/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.annotation.RequiresApi
import com.nearme.note.util.IntentParamsUtil
import com.oplus.note.edgeToEdge.EdgeToEdgeActivity
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R

@RequiresApi(Build.VERSION_CODES.R)
class SurpriseActivity : EdgeToEdgeActivity() {
    private var type: String? = null
    private var count: Int = -1
    private lateinit var alphaMovieView: ImageView
    private var newType: String? = null

    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.fragment_surprise)
        alphaMovieView = findViewById(R.id.amv_media)
        initIntent()

        findViewById<View>(R.id.back).setOnClickListener {
            finish()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        newType = IntentParamsUtil.getStringExtra(intent, ARG_TYPE, "")
    }

    private fun initIntent() {
        type = IntentParamsUtil.getStringExtra(intent, ARG_TYPE, "")
        count = IntentParamsUtil.getIntExtra(intent, ARG_COUNT, -1)
        when (type) {
            TYPE_FINISH_ALL -> alphaMovieView.setImageResource(R.drawable.all_done)
            TYPE_ACHIEVEMENT -> {
                finishAlarm()
//                alphaMovieView.setImageResource(R.drawable.last_week_done)
            }
            TYPE_MILE_STONE -> {
                finishAlarm()
//                alphaMovieView.setImageResource(R.drawable.mile_stone_done)
                AppLogger.BASIC.d("bx", "$count")
            }
        }
    }

    override fun onPause() {
        super.onPause()
    }

    override fun finish() {
        if (type == TYPE_ACHIEVEMENT && newType == TYPE_FINISH_ALL) {
            alphaMovieView.setImageResource(R.drawable.all_done)
        } else {
            super.finish()
        }
    }

    private fun finishAlarm() {
        alphaMovieView.postDelayed({ finish() }, FINISH_DELAY)
    }

    companion object {
        private const val ARG_TYPE = "type"
        private const val ARG_COUNT = "count"
        const val TYPE_FINISH_ALL = "1"
        const val TYPE_MILE_STONE = "2"
        const val TYPE_ACHIEVEMENT = "3"
        private const val FINISH_DELAY = 2000L
        fun enterSurprise(context: Activity, type: String = TYPE_FINISH_ALL, count: Int = -1) {
            Intent(context, SurpriseActivity::class.java).apply {
                putExtra(ARG_TYPE, type)
                putExtra(ARG_COUNT, count)
                val options = ActivityOptions.makeBasic()
                options.launchDisplayId = context.display?.displayId!!
                context.startActivity(this)
            }
        }
    }
}