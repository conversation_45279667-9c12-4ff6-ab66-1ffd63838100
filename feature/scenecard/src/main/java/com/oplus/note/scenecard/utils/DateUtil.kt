/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/3/10      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.nearme.note.util.PreferencesUtils
import java.util.Calendar
import java.util.Date
import java.util.Locale

object DateUtil {
    const val NUM_DAY = 24

    @JvmStatic
    @VisibleForTesting
    fun getNextWeekStart(): Long {
        val first = if (Locale.getDefault() == Locale.CHINA) {
            Calendar.MONDAY
        } else {
            Calendar.getInstance().firstDayOfWeek
        }
        val firstDay = Calendar.getInstance()
        firstDay.add(Calendar.WEEK_OF_MONTH, 1)
        firstDay.set(Calendar.DAY_OF_WEEK, first)
        firstDay.set(Calendar.HOUR_OF_DAY, 0)
        firstDay.set(Calendar.MINUTE, 0)
        firstDay.set(Calendar.SECOND, 0)
        return firstDay.timeInMillis
    }

    @JvmStatic
    fun getFirstDayOfLastWeek(): Date {
        val firstDay = Calendar.getInstance()
        val first = if (Locale.getDefault() == Locale.CHINA) {
            Calendar.MONDAY
        } else {
            Calendar.getInstance().firstDayOfWeek
        }
        firstDay.add(Calendar.WEEK_OF_MONTH, -1)
        firstDay.set(Calendar.DAY_OF_WEEK, first)
        firstDay.set(Calendar.HOUR_OF_DAY, 0)
        firstDay.set(Calendar.MINUTE, 0)
        firstDay.set(Calendar.SECOND, 0)
        return firstDay.time
    }

    @JvmStatic
    fun getLastDayOfWeek(): Date {
        val firstDay = Calendar.getInstance()
        val first = if (Locale.getDefault() == Locale.CHINA) {
            Calendar.MONDAY
        } else {
            Calendar.getInstance().firstDayOfWeek
        }
        firstDay.set(Calendar.DAY_OF_WEEK, first)
        firstDay.add(Calendar.DATE, -1)
        firstDay.set(Calendar.HOUR_OF_DAY, 0)
        firstDay.set(Calendar.MINUTE, 0)
        firstDay.set(Calendar.SECOND, 0)
        return firstDay.time
    }

    @JvmStatic
    fun isFirstEnterCard(context: Context): Boolean {
        val nextWeekStart = PreferencesUtils.getNextWeekStart(context)
        if (nextWeekStart == -1L) {
            PreferencesUtils.setNextWeekStart(context, getNextWeekStart())
            return true
        } else {
            if (System.currentTimeMillis() > nextWeekStart) {
                PreferencesUtils.setNextWeekStart(context, getNextWeekStart())
                return true
            }
        }
        return false
    }
    @JvmStatic
    fun getToday(): Date {
        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, 0)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        return today.time
    }

    @JvmStatic
    fun getTomorrow(): Date {
        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, NUM_DAY)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        return today.time
    }
}
