/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - TextManager.kt
** Description:
** Version: 1.0
** Date : 2023/3/27
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/3/27      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation.rolltext

import android.graphics.Canvas
import android.graphics.Paint
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.isArabNumber
import kotlin.collections.ArrayList
import kotlin.math.abs
import kotlin.math.pow

class TextManager(private val textPaint: Paint) {

    companion object {
        const val TAG = "TextManager"
        //并非真正要绘制10，占个位，当数字是 EMPTY_NUMBER 时表示绘制空字符串
        const val EMPTY_NUMBER = 10
        private const val TOTAL_DURATION = 500L
        private const val ANIMATION_DELAY = 50L
        private const val NUMBER_9 = 9
        private const val NUMBER_10 = 10
    }

    //单个字符最大宽度
    private var maxCharWidth = 0f
    //保存字符和对应宽度的 map
    private val charWidthMap = hashMapOf<Int, Float>()

    private val textColumns = mutableListOf<TextColumn>()

    var currentNumber: Int = 0

    var textHeight: Float = 0f

    var textBaseline = 0f

    init {
        updateFontMatrics()
    }

    /**获取字符最大宽度，阿拉伯数字下，使用数字 8 的宽度，其他情况下获取 0~9 中最宽的字符的宽度*/
    fun maxCharWidth(c: Int, textPaint: Paint): Float {
        return if (c == EMPTY_NUMBER) {
            0f
        } else {
            if (maxCharWidth > 0f) {
                maxCharWidth
            } else {
                getNumberMaxWith(textPaint).also { maxCharWidth = it }
            }
        }
    }

    /**获取字符的宽度*/
    fun charWidth(c: Int, textPaint: Paint): Float {
        return if (c == EMPTY_NUMBER) {
            0f
        } else {
            charWidthMap[c] ?: textPaint.measureText(String.format("%d", c)).also { charWidthMap[c] = it }
        }
    }

    private fun getNumberMaxWith(textPaint: Paint): Float {
        if (isArabNumber()) {
            return textPaint.measureText("8")
        }
        var width = 0f
        for (i in 0..NUMBER_9) {
            width = Math.max(width, textPaint.measureText(String.format("%d", i)))
        }
        return width
    }

    fun updateFontMatrics() {
        charWidthMap.clear()
        maxCharWidth = 0f
        with(textPaint.fontMetrics) {
            textHeight = bottom - top
            textBaseline = -top
        }
        textColumns.forEach { it.measure() }
    }

    fun setAnimationUpdateListener(listener: TextColumn.OnAnimationUpdateListener?) {
       textColumns.forEach {
           it.animationUpdateListener = listener
       }
    }

    fun startAnimation() {
        textColumns.forEachIndexed { index, textColumn ->
            val delay = (textColumns.size - 1 - index) * ANIMATION_DELAY
            val duration = TOTAL_DURATION - delay
            textColumn.startAnimation(duration, delay)
        }
    }

    fun onAnimationEnd() {
        textColumns.forEach { it.onAnimationEnd() }
    }

    fun draw(canvas: Canvas) {
        textColumns.forEach {
            it.draw(canvas)
            canvas.translate(it.nowColumnWidth, 0f)
        }
    }

    val currentTextWidth: Float
        get() {
            val textWidth = textColumns
                .map { it.nowColumnWidth }
                .fold(0f) { total, next -> total + next }
            return textWidth
        }

    /**小于等于0时，不显示*/
    fun setText(targetNumber: Int) {
        val columns = mutableListOf<TextColumn>()
        val sourceNumber = currentNumber.toString()

        val maxLen = Math.max(sourceNumber.length, targetNumber.toString().length)
        AppLogger.BASIC.d(TAG, "setText sourceNumber $sourceNumber targetNumber $targetNumber")

        for (idx in 0 until maxLen) {
            //获取当前 index 下，需要变化的数字列表
            val list = getShowNumberList(currentNumber, targetNumber, idx, maxLen)
            AppLogger.BASIC.d(TAG, "setText idx $idx list $list")
            columns.add(TextColumn(this, textPaint, list, targetNumber >= currentNumber))
        }
        textColumns.clear()
        textColumns.addAll(columns)

        currentNumber = targetNumber
    }

    /**获取需要显示的 char 的列表
     * @param sourceNumber 当前显示的数字
     * @param targetNumber 需要变化到的数字
     * @param index 数字的索引 */
    private fun getShowNumberList(
        sourceNumber: Int,
        targetNumber: Int,
        index: Int,
        maxLength: Int
    ): List<Int> {
        //是否需要多转一圈
        val needOneMoreRound = getOneMoreRound(sourceNumber, targetNumber, index, maxLength)
        //数字增加 还是减少
        val increase = targetNumber - sourceNumber > 0
        //当前后数字位数不一致时，对 index 进行修正
        val diffLength = abs(sourceNumber.toString().length - targetNumber.toString().length)
        val sourceIndex = if (increase) index - diffLength else index
        val targetIndex = if (!increase) index - diffLength else index
        //从哪个数字开始变化
        val from = getNumberAt(sourceNumber, sourceIndex)
        //变化结束后显示哪个数字
        val to = getNumberAt(targetNumber, targetIndex)
        val list = ArrayList<Int>()

        list.add(from)
        var temp = from
        while (temp != to) {
            temp = if (increase) {
                increaseOne(temp)
            } else {
                decreaseOne(temp)
            }
            list.add(temp)
        }

        if (needOneMoreRound) {
            addOneRound(increase, to, list)
        }
        /**检查新增位数时，首个数字不显示0；减少位数时，末尾数字不显示0*/
        if (diffLength > 0) {
            for (i in 0 until diffLength) {
                if (increase && index == i && list[0] == 0) {
                    list[0] = EMPTY_NUMBER
                } else if (!increase && index == i && list.last() == 0) {
                    list[list.lastIndex] = EMPTY_NUMBER
                }
            }
        }
        //数字减小到 0 下，不显示
        if (targetNumber == 0 && targetIndex == 0) {
            list[list.lastIndex] = EMPTY_NUMBER
        }
        //数字从0开始新增，不显示0
        if (sourceNumber == 0 && sourceIndex == 0) {
            list[0] = EMPTY_NUMBER
        }
        return list
    }

    private fun increaseOne(i: Int): Int {
        var result = i + 1
        if (result >= NUMBER_10) {
            result = 0
        }
        return result
    }

    private fun decreaseOne(i: Int): Int {
        var result = i - 1
        if (result < 0) {
            result = NUMBER_9
        }
        return result
    }

    /**
     * 当前位数是否要多转一圈
     * 变化前后的差值在某位的大小大于 10 ，则需要多转一圈
     * 比如 11 变成 275 ，差值是 264
     * 在个位的 变化为264 / 1 = 264，大于 10 需要多转一圈
     * 在百位的 变化为264 / 10 = 26，大于 10 需要多转一圈
     * 在个位的 变化为264 / 100 = 2，小于 10 不需要多转一圈
     * */
    private fun getOneMoreRound(sourceNumber: Int, targetNumber: Int, index: Int, maxLength: Int): Boolean {
        val diffCount = abs(targetNumber - sourceNumber)
        val indexFromRight = maxLength - 1 - index  // 0 1 2 变成 2 1 0
        val n = diffCount / NUMBER_10.toDouble().pow(indexFromRight.toDouble())
        return n >= NUMBER_10
    }

    /**给 list 再添加一圈数字 */
    private fun addOneRound(increase: Boolean, to: Int, list: ArrayList<Int>) {
        for (i in 1..NUMBER_10) {
            var number = 0
            if (increase) {
                number = to + i
                if (number >= NUMBER_10) {
                    number -= NUMBER_10
                }
            } else {
                number = to - i
                if (number < 0) {
                    number += NUMBER_10
                }
            }
            list.add(number)
        }
    }

    /**获取指定位数上的数字*/
    private fun getNumberAt(number: Int, index: Int): Int {
        kotlin.runCatching {
            return number.toString().substring(index, index + 1).toInt()
        }.onFailure {
            AppLogger.BASIC.w(TAG, "getNumberAt error ${it.message}")
        }
        return 0
    }
}