/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - TodoCreateAnimationHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2023/2/23      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.*
import android.content.Context
import android.os.Build
import android.text.TextUtils
import android.util.LayoutDirection
import android.view.animation.LinearInterpolator
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import androidx.core.animation.doOnEnd
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.view.CircleButtonView
import java.util.Locale

/**
 * button animation https://cod-tools.myoas.com/AE-Timeline/?props=2023-02-24%2018%3A09%3A15.628_ae
 * */
@RequiresApi(Build.VERSION_CODES.Q)
class ButtonAnimationHelper(val context: Context, val leftBtn: CircleButtonView, val rightBtn: CircleButtonView) {
    companion object {
        private const val DURATION_180 = 180L
        private const val DURATION_100 = 100L
        private const val DURATION_400 = 400L
        private const val START_DELAY_220 = 220L
        private const val DELAY_116 = 116L

        //直接关闭详情页时，动画时长的系数 180f / 400f
        private const val DIRECT_CLOSE_COEFFICIENT = 0.45f
    }

    /**
     * @param open true 执行按钮展开动画，false 执行按钮合并动画
     * @param isDirectClose true时执行直接关闭页面的动画，不执行共享元素转场动画，时间较短，会在所有时间上乘以一个系数
     * @param endCallBack  合并动画执行完成的回调
     * */
    fun startAnimation(
        open: Boolean,
        isDirectClose: Boolean = false,
        endCallBack: (() -> Unit)? = null
    ) {
        if (open) startOpenAnimation() else startCloseAnimation(isDirectClose, endCallBack)
    }

    @Suppress("LongMethod")
    @VisibleForTesting
    fun startCloseAnimation(
        isDirectClose: Boolean = false,
        endCallBack: (() -> Unit)? = null
    ) {
        val isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL
        val translationX = if (isRTL) {
            -(context.resources.displayMetrics.widthPixels / 4f)
        } else {
            context.resources.displayMetrics.widthPixels / 4f
        }
        //button 左右位移
        AnimatorSet().apply {
            duration = getDirectCloseAnimTime(isDirectClose, DURATION_400)
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    leftBtn, "translationX", -translationX, 0f
                ), ObjectAnimator.ofFloat(
                    rightBtn, "translationX", translationX, 0f
                )
            )
            doOnEnd {
                endCallBack?.invoke()
            }
            start()
        }

        //取消和完成按钮的 alpha 隐藏
        AnimatorSet().apply {
            duration = getDirectCloseAnimTime(isDirectClose, DURATION_180)
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    leftBtn.getIconImageView(), "alpha", 1f, 0f
                ), ObjectAnimator.ofFloat(
                    rightBtn.getIconImageView(), "alpha", 1f, 0f
                )
            )
            doOnEnd {
                rightBtn.setIconRes(R.drawable.voice)
            }
            start()
        }

        //完成按钮 voice图标的 alpha 显示
        AnimatorSet().apply {
            duration = getDirectCloseAnimTime(isDirectClose, DURATION_180)
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    rightBtn.getIconImageView(), "alpha", 0f, 1f
                )
            )
            startDelay = getDirectCloseAnimTime(isDirectClose, DURATION_180)
            start()
        }

        //color change
        val targetColor = context.getColor(R.color.circle_btn_background)
        colorAnimation(rightBtn, leftBtn, targetColor, targetColor, isDirectClose)

        if (leftBtn.getMtg() == CircleButtonView.TAG_DETAIL) {
            //详情页返回时执行 button 的隐藏动画
            AnimatorSet().apply {
                duration = getDirectCloseAnimTime(isDirectClose, DURATION_400)
                interpolator = COUIMoveEaseInterpolator()
                playTogether(
                    ObjectAnimator.ofFloat(leftBtn, "alpha", 1f, 0f),
                    ObjectAnimator.ofFloat(rightBtn, "alpha", 1f, 0f)
                )
                start()
            }
        } else if (leftBtn.getMtg() == CircleButtonView.TAG_MAIN) {
            //详情页返回时，首页的 left button 隐藏动画，避免按钮合并瞬间颜色跳变
            AnimatorSet().apply {
                duration = getDirectCloseAnimTime(isDirectClose, DURATION_180)
                interpolator = LinearInterpolator()
                playTogether(
                    ObjectAnimator.ofFloat(leftBtn, "alpha", 1f, CircleButtonView.ALPHA_HALF)
                )
                startDelay = getDirectCloseAnimTime(isDirectClose, START_DELAY_220)
                start()
            }
        }
    }

    /**
     * 重建场景导致动画异常，直接设置动画view为最终显示的状态，不执行动效
     */
    fun setViewFinalState() {
        val isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL
        val translationX = if (isRTL) {
            -(context.resources.displayMetrics.widthPixels / 4f)
        } else {
            context.resources.displayMetrics.widthPixels / 4f
        }
        leftBtn.translationX = -translationX
        rightBtn.translationX = translationX

        leftBtn.getIconImageView().alpha = 1F
        rightBtn.getIconImageView().alpha = 1F

        leftBtn.setIconRes(R.drawable.cancel)
        rightBtn.setIconRes(R.drawable.confirm)
        leftBtn.setBgColor(context.getColor(R.color.detail_cancel_color))
        rightBtn.setBgColor(context.getColor(R.color.detail_confirm_color))
    }

    /** 底部 button 位移和 alpha 动画*/
    @VisibleForTesting
    fun startOpenAnimation() {
        val isRTL = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL
        val translationX = if (isRTL) {
            -(context.resources.displayMetrics.widthPixels / 4f)
        } else {
            context.resources.displayMetrics.widthPixels / 4f
        }
        //button 左右位移
        AnimatorSet().apply {
            duration = DURATION_400
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    leftBtn, "translationX", 0f, -translationX
                ), ObjectAnimator.ofFloat(
                    rightBtn, "translationX", 0f, translationX
                )
            )
            start()
        }
        //完成按钮 录音图标的 alpha 隐藏
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    rightBtn.getIconImageView(), "alpha", 1f, 0f
                )
            )
            start()
        }
        //取消和完成按钮的 alpha 显示
        AnimatorSet().apply {
            duration = DURATION_180
            interpolator = COUIEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(
                    leftBtn.getIconImageView(), "alpha", 0f, 1f
                ), ObjectAnimator.ofFloat(
                    rightBtn.getIconImageView(), "alpha", 0f, 1f
                )
            )
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                    if (leftBtn.getMtg() == CircleButtonView.TAG_CREATE) {
                        leftBtn.setIconRes(R.drawable.icon_todo_create_cancel)
                        rightBtn.setIconRes(R.drawable.icon_todo_create_confirm_disable)
                    } else if (leftBtn.getMtg() == CircleButtonView.TAG_DETAIL) {
                        leftBtn.setIconRes(R.drawable.cancel)
                        rightBtn.setIconRes(R.drawable.confirm)
                    }
                }

                override fun onAnimationEnd(animation: Animator) {
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
            leftBtn.postDelayed({ start() }, START_DELAY_220)
        }
        //color change
        colorAnimation(rightBtn, leftBtn, rightBtn.getTargetColor(), leftBtn.getTargetColor())
    }

    @VisibleForTesting
    fun colorAnimation(
        rightBtn: CircleButtonView,
        leftBtn: CircleButtonView,
        rightTargetColor: Int,
        leftTargetColor: Int,
        isDirectClose: Boolean = false,
    ) {
        AnimatorSet().apply {
            duration = getDirectCloseAnimTime(isDirectClose, DURATION_100)
            val animatorConfirm = ValueAnimator.ofObject(
                ArgbEvaluator(),
                rightBtn.getBgColor(),
                rightTargetColor
            )
            animatorConfirm.addUpdateListener {
                rightBtn.setBgColor(it.animatedValue as Int)
            }

            val animatorCancel = ValueAnimator.ofObject(
                ArgbEvaluator(),
                leftBtn.getBgColor(),
                leftTargetColor
            )
            animatorCancel.addUpdateListener {
                leftBtn.setBgColor(it.animatedValue as Int)
            }
            playTogether(
                animatorConfirm,
                animatorCancel
            )
            startDelay = getDirectCloseAnimTime(isDirectClose, DELAY_116)
            start()
        }
    }

    @VisibleForTesting
    fun getDirectCloseAnimTime(isDirectClose: Boolean = false, time: Long): Long {
        return if (isDirectClose) (time * DIRECT_CLOSE_COEFFICIENT).toLong() else time
    }
}