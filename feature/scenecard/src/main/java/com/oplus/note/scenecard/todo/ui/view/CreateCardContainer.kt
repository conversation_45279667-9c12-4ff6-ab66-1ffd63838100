/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - CreateCardContainer
 ** Description:
 **         v1.0:   Create CreateCardContainer file
 **
 ** Version: 1.0
 ** Date: 2023/04/10
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/4/10   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.RectF
import android.os.Build
import android.util.AttributeSet
import android.view.View
import android.view.WindowManager
import androidx.annotation.RequiresApi
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R

@RequiresApi(Build.VERSION_CODES.R)
class CreateCardContainer : View {

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet) {
        init(context)
    }

    constructor(context: Context, attributeSet: AttributeSet?, i: Int) : super(context, attributeSet, i) {
        init(context)
    }

    val radius = context.resources.getDimensionPixelOffset(R.dimen.input_container_bg_radius)
    val paint = Paint()
    private val maxRect = RectF()
    private val scaledRect = RectF()
    private val drawRect = RectF()

    private fun init(context: Context) {
        val displayRect = (context.getSystemService(
            Context.WINDOW_SERVICE
        ) as WindowManager).currentWindowMetrics.bounds
        AppLogger.BASIC.d(TAG, "scaleX:$scaleX,scaleY:$scaleY,displayRect:$displayRect")
        val bottomMargin = context.resources.getDimensionPixelOffset(R.dimen.create_button_container_height)
        maxRect.apply {
            top = displayRect.top.toFloat()
            left = displayRect.left.toFloat()
            right = displayRect.right.toFloat()
            bottom = displayRect.bottom.toFloat() - bottomMargin
        }
        scaledRect.set(maxRect)
        val matrix = Matrix()
        matrix.setScale(START_SCALE_X, START_SCALE_Y, maxRect.centerX(), maxRect.centerY())
        matrix.mapRect(scaledRect)
        paint.apply {
            style = Paint.Style.FILL
            color = context.getColor(R.color.scenecard_create_card_background)
        }
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        canvas?.drawRoundRect(drawRect, radius.toFloat(), radius.toFloat(), paint)
    }

    fun closeAnimation(): ValueAnimator? {
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.interpolator = COUIEaseInterpolator()
        animator.duration = DURATION_400
        animator.addUpdateListener {
            val progress = it.animatedValue as? Float
            progress?.let {
                drawRect.mapRectToRectByProgress(maxRect, scaledRect, progress)
                postInvalidate()
            }
        }
        animator.start()
        return animator
    }

    fun openAnimation(): ValueAnimator? {
        val animator = ValueAnimator.ofFloat(0f, 1f)
        animator.interpolator = COUIEaseInterpolator()
        animator.duration = DURATION_400
        animator.addUpdateListener {
            val progress = it.animatedValue as? Float
            progress?.let {
                drawRect.mapRectToRectByProgress(scaledRect, maxRect, progress)
                postInvalidate()
            }
        }
        return animator
    }

    private fun RectF.mapRectToRectByProgress(
        src: RectF,
        dst: RectF,
        progress: Float
    ) {
        val left = (dst.left - src.left) * progress + src.left
        val top = (dst.top - src.top) * progress + src.top
        val right = (dst.right - src.right) * progress + src.right
        val bottom = (dst.bottom - src.bottom) * progress + src.bottom
        this.set(left, top, right, bottom)
    }

    companion object {
        const val TAG = "CreateCardContainer"
        private const val DURATION_400 = 400L
        private const val START_DELAY_67 = 67L
        private const val START_SCALE_X = 0.8834f   // 546f / 618f
        private const val START_SCALE_Y = 0.5539f   //462f / 834f
    }
}