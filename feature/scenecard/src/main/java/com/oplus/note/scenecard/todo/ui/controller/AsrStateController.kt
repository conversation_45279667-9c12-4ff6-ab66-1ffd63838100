/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AsrStateController
 ** Description:
 **         v1.0:   Monitor network and microphone status, change ASR status
 **
 ** Version: 1.0
 ** Date: 2023/04/20
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/4/20   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.scenecard.todo.ui.controller

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioManager
import android.net.ConnectivityManager
import android.net.Network
import androidx.annotation.IntDef
import com.nearme.note.util.AndroidVersionUtils
import com.nearme.note.util.NetworkUtils
import com.oplus.note.logger.AppLogger
import java.util.concurrent.Executor

@SuppressLint("NewApi")
class AsrStateController(context: Context?) {

    private var mExecutor: Executor? = null
    private var mNetworkCallback: ConnectivityManager.NetworkCallback? = null
    private var mConnectivityManager: ConnectivityManager? = null
    private var mAsrStateCallback: AsrStateCallback? = null
    private var mAudioManager: AudioManager? = null
    private var mModeChangedListener: AudioManager.OnModeChangedListener? = null

    init {
        mConnectivityManager =
            context?.applicationContext?.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
        mNetworkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                AppLogger.BASIC.d(TAG, "network Available")
                mAsrStateCallback?.onStateChanged(FLAG_NETWORK, true)
            }

            override fun onUnavailable() {
                super.onUnavailable()
                AppLogger.BASIC.d(TAG, "network Unavailable")
                mAsrStateCallback?.onStateChanged(FLAG_NETWORK, false)
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                AppLogger.BASIC.d(TAG, "network Lost")
                mAsrStateCallback?.onStateChanged(FLAG_NETWORK, false)
            }
        }
        if (AndroidVersionUtils.isHigherAndroidS()) {
            mAudioManager = context?.getSystemService(Context.AUDIO_SERVICE) as? AudioManager
            mExecutor = context?.mainExecutor
            mModeChangedListener = AudioManager.OnModeChangedListener { mode ->
                AppLogger.BASIC.d(TAG, "AudioManager mode change:$mode")
                if (mode == AudioManager.MODE_IN_CALL || mode == AudioManager.MODE_IN_COMMUNICATION) {
                    //call or voice call
                    AppLogger.BASIC.d(TAG, "current mode is call")
                    mAsrStateCallback?.onStateChanged(FLAG_MICROPHONE, false)
                } else {
                    mAsrStateCallback?.onStateChanged(FLAG_MICROPHONE, true)
                }
            }
        } else {
            AppLogger.BASIC.w(TAG, "Android version lower than AndroidS ,can`t add audio manager changed listener")
        }
    }

    fun register(callback: AsrStateCallback?) {
        mAsrStateCallback = callback
        mNetworkCallback?.let {
            mConnectivityManager?.registerDefaultNetworkCallback(it)
            AppLogger.BASIC.d(TAG, "registerDefaultNetworkCallback")
        }
        if (AndroidVersionUtils.isHigherAndroidS()) {
            if ((mExecutor != null) && (mModeChangedListener != null)) {
                mAudioManager?.addOnModeChangedListener(mExecutor!!, mModeChangedListener!!)
            } else {
                AppLogger.BASIC.e(TAG, "params are null.")
            }
        }
    }

    fun unRegister() {
        mNetworkCallback?.let { mConnectivityManager?.unregisterNetworkCallback(it) }
        AppLogger.BASIC.d(TAG, "unregisterNetworkCallback")
        mAsrStateCallback = null
        if (AndroidVersionUtils.isHigherAndroidS()) {
            mModeChangedListener?.let { mAudioManager?.removeOnModeChangedListener(it) }
            mModeChangedListener = null
        }
    }

    fun isMicEnable(): Boolean =
        ((mAudioManager?.mode != AudioManager.MODE_IN_COMMUNICATION) && (mAudioManager?.mode != AudioManager.MODE_IN_CALL))

    fun isNetAvailable(context: Context?): Boolean {
        return if (context != null) {
            (NetworkUtils.isWifiConnected(context) && NetworkUtils.isWifiOnline(context))
                    || (NetworkUtils.isMobileDataConnected(context))
        } else {
            false
        }
    }



    interface AsrStateCallback {
        fun onStateChanged(@AsrStateFlag flag: Int, enable: Boolean)
    }

    @Retention(AnnotationRetention.SOURCE)
    @IntDef(
        FLAG_MICROPHONE,
        FLAG_NETWORK
    )
    annotation class AsrStateFlag

    companion object {
        private const val TAG = "AsrStateController"
        const val FLAG_NETWORK = 2
        const val FLAG_MICROPHONE = 1
    }
}