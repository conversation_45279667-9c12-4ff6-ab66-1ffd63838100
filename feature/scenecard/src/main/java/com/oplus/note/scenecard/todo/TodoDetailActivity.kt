/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/2/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  baixu       2023/2/20      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.todo

import android.app.Instrumentation
import android.os.Build
import android.os.Bundle
import androidx.annotation.RequiresApi
import com.nearme.note.util.ConfigUtils
import com.oplus.note.edgeToEdge.EdgeToEdgeActivity
import com.oplus.note.logger.AppLogger
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.fragment.TodoDetailFragment

@RequiresApi(Build.VERSION_CODES.R)
class TodoDetailActivity : EdgeToEdgeActivity() {
    companion object {
        private const val TAG = "TodoDetailActivity"
    }

    private var mFragment: TodoDetailFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.i(TAG, "onCreate error: Todo is deprecated.")
            finish()
            return
        }
        setContentView(R.layout.activity_todo_detail)
        if (savedInstanceState == null) {
            mFragment = TodoDetailFragment.newInstance()
            mFragment!!.arguments = intent.extras
            supportFragmentManager.beginTransaction()
                .replace(R.id.container, mFragment!!)
                .commitNow()
        }
    }

    override fun finish() {
        super.finish()
        if (mFragment?.mClickDeleteOrComplete == true) {
            overridePendingTransition(-1, R.anim.detail_exit_fade_out)
        }
    }

    /** https://blog.csdn.net/u013077428/article/details/126484571 解决页面 onStop 后返回的转场动画失效的问题*/
    override fun onStop() {
        if (!isFinishing) {
            Instrumentation().callActivityOnSaveInstanceState(this, Bundle())
        }
        super.onStop()
    }
}