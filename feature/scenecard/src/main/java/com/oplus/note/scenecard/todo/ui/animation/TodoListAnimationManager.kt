/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - ListAnimationManager.kt
** Description:
** Version: 1.0
** Date : 2023/3/14
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2023/3/14      1.0     create file
****************************************************************/
package com.oplus.note.scenecard.todo.ui.animation

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Build
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.RelativeLayout
import androidx.annotation.RequiresApi
import androidx.core.animation.doOnEnd
import androidx.core.view.ViewCompat
import com.oplus.anim.EffectiveAnimationView
import com.oplus.note.scenecard.R
import com.oplus.note.scenecard.todo.ui.main.TodoListAdapter
import com.oplus.note.scenecard.todo.ui.view.CircleButtonView
@RequiresApi(Build.VERSION_CODES.R)
object TodoListAnimationManager {

    private var mLeftBtn: CircleButtonView? = null
    private var mRightBtn: CircleButtonView? = null
    private var needResetButtonStatus = false
    private var completeLottieView: EffectiveAnimationView? = null
    var asrButtonEnable = true
    var needPlayCompleteAnimation = false

    fun setButton(leftBtn: CircleButtonView?, rightBtn: CircleButtonView, asrButtonEnable: Boolean) {
        mLeftBtn = leftBtn
        mRightBtn = rightBtn
        this.asrButtonEnable = asrButtonEnable
    }

    fun setLottieView(view: EffectiveAnimationView?) {
        completeLottieView = view
        completeLottieView?.apply {
            setAnimation(R.raw.complete_todo_animation)
            addAnimatorListener(object : AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    visibility = View.GONE
                }

                override fun onAnimationCancel(animation: Animator) {
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
        }
    }

    fun release() {
        mLeftBtn = null
        mRightBtn = null
        completeLottieView = null
    }

    /**
     * 重建场景导致动画异常，直接设置动画view为最终显示的状态，不执行动效
     */
    fun setViewFinalState() {
        if (mLeftBtn == null || mRightBtn == null || !asrButtonEnable) {
            return
        }
        mLeftBtn?.visibility = View.VISIBLE
        ButtonAnimationHelper(mLeftBtn!!.context, mLeftBtn!!, mRightBtn!!).setViewFinalState()
    }

    @SuppressLint("ObjectAnimatorBinding")
    fun startButtonAnimation(open: Boolean) {
        if (mLeftBtn == null || mRightBtn == null || !asrButtonEnable) {
            return
        }
        mRightBtn?.clearAllAnimation()
        if (open) {
            mLeftBtn?.visibility = View.VISIBLE
            ButtonAnimationHelper(mLeftBtn!!.context, mLeftBtn!!, mRightBtn!!).startAnimation(open = true)
        } else {
            ButtonAnimationHelper(mLeftBtn!!.context, mLeftBtn!!, mRightBtn!!).startAnimation(open = false) {
               AnimatorSet().apply {
                   duration = CircleButtonView.DURATION_180
                   interpolator = LinearInterpolator()

                   @SuppressLint("ALL")
                   val objectAnimator = ObjectAnimator.ofFloat(mLeftBtn, "alpha", CircleButtonView.ALPHA_HALF, 0f)
                   playTogether(objectAnimator)
                    doOnEnd {
                        mLeftBtn?.visibility = RelativeLayout.GONE
                    }
                    start()
                }
            }
        }
    }

    /**点击删除或者完成时直接关闭详情页时，列表的2个按钮执行合并动画*/
    fun startDirectCloseAnimation() {
        if (mLeftBtn == null || mRightBtn == null || !asrButtonEnable) {
            return
        }
        mRightBtn?.clearAllAnimation()
        ButtonAnimationHelper(mLeftBtn!!.context, mLeftBtn!!, mRightBtn!!).startAnimation(
            open = false,
            isDirectClose = true
        ) {
            mLeftBtn?.visibility = View.GONE
        }
    }

    fun markNeedRestButtonStatus() {
        mLeftBtn?.postDelayed({
            needResetButtonStatus = true
        }, TodoDetailAnimationHelper.DURATION_400)
    }

    /**button直接变成初始状态，不执行动画*/
    fun resetButtonStatusIfNeed() {
        if (needResetButtonStatus) {
            needResetButtonStatus = false
            mLeftBtn?.apply {
                translationX = 0f
                setBgColor(context.getColor(R.color.circle_btn_background))
                setIconRes(R.drawable.voice)
                getIconImageView().alpha = 1f
                visibility = View.GONE
            }

            mRightBtn?.apply {
                translationX = 0f
                setBgColor(context.getColor(R.color.circle_btn_background))
                setIconRes(R.drawable.voice)
                getIconImageView().alpha = 1f
            }
        }
    }

    fun playCompleteLottieIfNeed() {
        if (needPlayCompleteAnimation) {
            needPlayCompleteAnimation = false
            completeLottieView?.visibility = View.VISIBLE
            completeLottieView?.playAnimation()
        }
    }
}