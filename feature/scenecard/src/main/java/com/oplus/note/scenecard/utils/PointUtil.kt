/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/4/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wangyinglei       2023/4/18      1.0     create file
 ****************************************************************/
package com.oplus.note.scenecard.utils

import android.content.Context
import com.oplus.statistics.OplusTrack

object PointUtil {
    //火烈鸟副屏卡片group id
    private const val GROUP_FLAMINGO_CARD = "2001035"
    private const val EVENT_COMPLETE_IN_DETAIL = "event_complete_in_detail"
    private const val EVENT_DELETE_IN_DETAIL = "event_delete_in_detail"
    private const val EVENT_OPEN_DETAIL = "event_open_detail"
    private const val EVENT_CANCEL_ALARM = "event_cancel_alarm"
    private const val EVENT_CANCEL_SAVE = "event_cancel_save"
    private const val EVENT_SLIDE_COMPLETE_TODO = "event_slide_complete_todo"
    private const val EVENT_CREATE_TODO_BY_CARD = "event_create_todo"
    private const val KEY_CREATE_DURATION = "id_todo_duration"
    private const val KEY_CREATE_CONTENT_LENGTH = "id_todo_content_length"
    private const val KEY_CREATE_ALARM = "id_todo_alarm"
    private const val EVENT_NLP_SUCCESS = "event_nlp_success"
    private const val KEY_NLP_SUCCESS_TYPE = "key_nlp_success_type"
    const val TYPE_NLP_SHOW = 0
    const val TYPE_NLP_SHOW_AND_SAVE = 1
    const val TYPE_NLP_SHOW_AND_DELETE = 2
    private const val EVENT_CLICK_ASR_BTN = "event_click_asr_btn"
    private const val EVENT_STOP_WAIT_ASR = "event_stop_wait_asr"
    private const val EVENT_AUTO_SWITCH_SCREEN = "event_auto_switch_screen"
    private const val EVENT_CLICK_TODO_ITEM = "event_click_todo_item"


    @JvmStatic
    fun setCardEventClickCreateBtn(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_CLICK_ASR_BTN, null)
    }

    @JvmStatic
    fun setCardEventNLPSuccess(context: Context, type: Int) {
        val map: HashMap<String, String> = HashMap()
        map[KEY_NLP_SUCCESS_TYPE] = type.toString()
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_NLP_SUCCESS, map)
    }

    @JvmStatic
    fun setCardEventStopWait(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_STOP_WAIT_ASR, null)
    }

    /**
     * 用户使用副屏待办卡片完成待办添加的次数和人数
     * @param duration 语音录入的时长
     * @param textLength 新建待办的字数（包括标点符号）
     * @param addAlarmTime 是否添加 提醒时间 （添加为1 未添加为0）
     */
    @JvmStatic
    fun setCardEventCreateComplete(context: Context, duration: Long, textLength: Int, addAlarmTime: Int) {
        val map: MutableMap<String, String> = HashMap()
        map[KEY_CREATE_DURATION] = duration.toString()
        map[KEY_CREATE_CONTENT_LENGTH] = textLength.toString()
        map[KEY_CREATE_ALARM] = addAlarmTime.toString()
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_CREATE_TODO_BY_CARD, map)
    }

    @JvmStatic
    fun setCardEventDoneBySlide(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_SLIDE_COMPLETE_TODO, null)
    }

    @JvmStatic
    fun setCardEventCancelSave(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_CANCEL_SAVE, null)
    }

    @JvmStatic
    fun setCardEventCancelAlarm(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_CANCEL_ALARM, null)
    }

    @JvmStatic
    fun setCardEventOpenDetail(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_OPEN_DETAIL, null)
    }

    @JvmStatic
    fun setCardEventDeleteInDetail(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_DELETE_IN_DETAIL, null)
    }

    @JvmStatic
    fun setCardEventDoneInDetail(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_COMPLETE_IN_DETAIL, null)
    }

    @JvmStatic
    fun setAutoSwitchScreen(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_AUTO_SWITCH_SCREEN, null)
    }

    @JvmStatic
    fun setCardEventClickItem(context: Context) {
        OplusTrack.onCommon(context, GROUP_FLAMINGO_CARD, EVENT_CLICK_TODO_ITEM, null)
    }
}