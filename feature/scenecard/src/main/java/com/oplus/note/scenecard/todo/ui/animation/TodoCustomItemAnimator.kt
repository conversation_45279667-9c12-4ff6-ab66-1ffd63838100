/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CustomItemAnimator.java
 ** Description:
 ** Version: 1.0
 ** Date : 2023/6/13
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2023/6/13      1.0     create file
 ****************************************************************/

package com.oplus.note.scenecard.todo.ui.animation

import android.animation.Animator
import android.animation.TimeInterpolator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.oplus.note.logger.AppLogger
import java.util.ArrayList

/**
 * This implementation of [RecyclerView.ItemAnimator] provides basic
 * animations on remove, add, and move events that happen to the items in
 * a RecyclerView. RecyclerView uses a DefaultItemAnimator by default.
 *
 * @see RecyclerView.setItemAnimator
 */
class TodoCustomItemAnimator : SimpleItemAnimator() {
    private val mMoveInterpolator: TimeInterpolator = COUIMoveEaseInterpolator()
    private val mRemoveScaleInterpolator: TimeInterpolator = COUIMoveEaseInterpolator()
    private val mRemoveAlphaInterpolator: TimeInterpolator = COUIEaseInterpolator()
    private val mAddAlphaInterpolator: TimeInterpolator = COUIEaseInterpolator()
    private val mAddScaleInterpolator: TimeInterpolator = COUIMoveEaseInterpolator()
    private val mPendingRemovals = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingAdditions = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingMoves = ArrayList<MoveInfo>()
    private val mPendingChanges = ArrayList<ChangeInfo>()
    private val mAdditionsList = ArrayList<ArrayList<RecyclerView.ViewHolder>>()
    private val mMovesList = ArrayList<ArrayList<MoveInfo>>()
    private val mChangesList = ArrayList<ArrayList<ChangeInfo>>()
    private val mAddAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mMoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mRemoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mChangeAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private var mListener: OnAnimatorListener? = null
    private var mStartRunAnimations = false

    init {
        removeDuration = REMOVE_DURATION
        moveDuration = MOVE_DURATION
        addDuration = ADD_DURATION
    }

    interface OnAnimatorListener {
        fun onAnimatorEnd()
    }

    fun setItemAnimatorListener(animatorListener: OnAnimatorListener?) {
        mListener = animatorListener
    }

    @VisibleForTesting
    internal data class MoveInfo(
        var holder: RecyclerView.ViewHolder,
        var fromX: Int,
        var fromY: Int,
        var toX: Int,
        var toY: Int
    )

    @VisibleForTesting
    data class ChangeInfo @VisibleForTesting constructor(
        var oldHolder: RecyclerView.ViewHolder?,
        var newHolder: RecyclerView.ViewHolder?
    ) {
        var fromX = 0
        var fromY = 0
        var toX = 0
        var toY = 0

        constructor(
            oldHolder: RecyclerView.ViewHolder?,
            newHolder: RecyclerView.ViewHolder?,
            fromX: Int,
            fromY: Int,
            toX: Int,
            toY: Int
        ) : this(oldHolder, newHolder) {
            this.fromX = fromX
            this.fromY = fromY
            this.toX = toX
            this.toY = toY
        }

        override fun toString(): String {
            return ("ChangeInfo{"
                    + "oldHolder=" + oldHolder
                    + ", newHolder=" + newHolder
                    + ", fromX=" + fromX
                    + ", fromY=" + fromY
                    + ", toX=" + toX
                    + ", toY=" + toY
                    + '}')
        }
    }

    override fun runPendingAnimations() {
        val removalsPending = !mPendingRemovals.isEmpty()
        val movesPending = !mPendingMoves.isEmpty()
        val changesPending = !mPendingChanges.isEmpty()
        val additionsPending = !mPendingAdditions.isEmpty()
        if (!removalsPending && !movesPending && !additionsPending && !changesPending) {
            return
        }
        mStartRunAnimations = true
        if (removalsPending) {
            for (holder in mPendingRemovals) {
                animateRemoveImpl(holder)
            }
            mPendingRemovals.clear()
        }
        if (movesPending) {
            val moves = ArrayList(mPendingMoves)
            mMovesList.add(moves)
            mPendingMoves.clear()
            for (moveInfo in moves) {
                animateMoveImpl(
                    moveInfo.holder, moveInfo.fromX, moveInfo.fromY,
                    moveInfo.toX, moveInfo.toY
                )
            }
            moves.clear()
            mMovesList.remove(moves)
        }
        if (changesPending) {
            val changes = ArrayList(mPendingChanges)
            mChangesList.add(changes)
            mPendingChanges.clear()
            val changer = Runnable {
                for (change in changes) {
                    animateChangeImpl(change)
                }
                changes.clear()
                mChangesList.remove(changes)
            }
            val totalChangeDelay = if (removalsPending) removeDuration else 0
            if (totalChangeDelay > 0) {
                val holder = changes[0].oldHolder
                ViewCompat.postOnAnimationDelayed(holder!!.itemView, changer, totalChangeDelay)
            } else {
                changer.run()
            }
        }
        if (additionsPending) {
            val additions = ArrayList(mPendingAdditions)
            mAdditionsList.add(additions)
            mPendingAdditions.clear()
            for (holder in additions) {
                animateAddImpl(holder)
            }
            additions.clear()
            mAdditionsList.remove(additions)
        }
    }

    override fun animateRemove(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder, null)
        mPendingRemovals.add(holder)
        return true
    }

    @VisibleForTesting
    fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val alpha = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        val animation = view.animate()
        mRemoveAnimations.add(holder)
        // 1f ~ 0.9f
        animation.scaleX(SCALE_09).scaleY(SCALE_09)
        animation.interpolator = mRemoveScaleInterpolator
        animation.setDuration(removeDuration).setListener(
            object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    view.translationZ = -1f
                    dispatchRemoveStarting(holder)
                }

                override fun onAnimationCancel(animation: Animator) {
                    AppLogger.BASIC.d(TAG, "onAnimationCancel:")
                    alpha.cancel()
                }

                override fun onAnimationEnd(animator: Animator) {
                    animation.setListener(null)
                    //放在这里因为removeDuration时间比alpha更长
                    view.alpha = 1f
                    view.scaleY = 1f
                    view.scaleX = 1f
                    view.translationZ = 0f
                    dispatchRemoveFinished(holder)
                    mRemoveAnimations.remove(holder)
                    dispatchFinishedWhenDone()
                }
            }).start()
        alpha.duration = ADD_REMOVE_ALPHA_DURATION
        alpha.interpolator = mRemoveAlphaInterpolator
        alpha.start()
    }

    override fun animateAdd(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder, null)
        holder.itemView.alpha = 0f
        holder.itemView.scaleX = SCALE_09
        holder.itemView.scaleY = SCALE_09
        mPendingAdditions.add(holder)
        return true
    }

    private fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        animation.interpolator = mAddScaleInterpolator
        mAddAnimations.add(holder)
        animation.scaleX(1f).scaleY(1f).setDuration(addDuration)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchAddStarting(holder)
                }

                override fun onAnimationCancel(animator: Animator) {
                    view.alpha = 1f
                    view.scaleX = 1f
                    view.scaleY = 1f
                }

                override fun onAnimationEnd(animator: Animator) {
                    animation.setListener(null)
                    dispatchAddFinished(holder)
                    mAddAnimations.remove(holder)
                    dispatchFinishedWhenDone()
                }
            }).start()
        val alpha = ObjectAnimator.ofFloat(view, View.ALPHA, 0f, 1f)
        alpha.duration = ADD_REMOVE_ALPHA_DURATION
        alpha.interpolator = mAddAlphaInterpolator
        alpha.startDelay = ADD_ALPHA_DELAY
        alpha.start()
    }

    override fun animateMove(
        holder: RecyclerView.ViewHolder,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        val view = holder.itemView
        val tempFromX = (fromX + holder.itemView.translationX).toInt()
        val tempFromY = (fromY + holder.itemView.translationY).toInt()
        resetAnimation(holder, mMoveInterpolator)
        val deltaX = toX - tempFromX
        val deltaY = toY - tempFromY
        if (deltaX == 0 && deltaY == 0) {
            dispatchMoveFinished(holder)
            return false
        }
        if (deltaX != 0) {
            view.translationX = -deltaX.toFloat()
        }
        if (deltaY != 0) {
            view.translationY = -deltaY.toFloat()
        }
        mPendingMoves.add(MoveInfo(holder, tempFromX, tempFromY, toX, toY))
        return true
    }

    private fun animateMoveImpl(holder: RecyclerView.ViewHolder, fromX: Int, fromY: Int, toX: Int, toY: Int) {
        val view = holder.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX != 0) {
            view.animate().translationX(0f)
        }
        if (deltaY != 0) {
            view.animate().translationY(0f)
        }
        val animation = view.animate()
        animation.interpolator = mMoveInterpolator
        mMoveAnimations.add(holder)
        animation.setDuration(moveDuration).setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                dispatchMoveStarting(holder)
            }

            override fun onAnimationCancel(animator: Animator) {
                if (deltaX != 0) {
                    view.translationX = 0f
                }
                if (deltaY != 0) {
                    view.translationY = 0f
                }
            }

            override fun onAnimationEnd(animator: Animator) {
                view.alpha = 1f
                animation.setListener(null)
                dispatchMoveFinished(holder)
                mMoveAnimations.remove(holder)
                dispatchFinishedWhenDone()
            }
        }).start()
    }

    override fun animateChange(
        oldHolder: RecyclerView.ViewHolder,
        newHolder: RecyclerView.ViewHolder,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        if (oldHolder === newHolder) {
            return animateMove(oldHolder, fromX, fromY, toX, toY)
        }
        val prevAlpha = oldHolder.itemView.alpha
        resetAnimation(oldHolder, null)
        oldHolder.itemView.alpha = prevAlpha
        if (newHolder != null) {
            // carry over translation values
            resetAnimation(newHolder, null)
            newHolder.itemView.alpha = 0f
        }
        mPendingChanges.add(ChangeInfo(oldHolder, newHolder, fromX, fromY, toX, toY))
        return true
    }

    fun animateChangeImpl(changeInfo: ChangeInfo) {
        val holder = changeInfo.oldHolder
        val view = holder?.itemView
        val newHolder = changeInfo.newHolder
        val newView = newHolder?.itemView
        if (view != null) {
            val oldViewAnim = view.animate().setDuration(
                changeDuration
            )
            mChangeAnimations.add(changeInfo.oldHolder)
            oldViewAnim.alpha(0f).setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchChangeStarting(changeInfo.oldHolder, true)
                }

                override fun onAnimationEnd(animator: Animator) {
                    oldViewAnim.setListener(null)
                    view.alpha = 1f
                    dispatchChangeFinished(changeInfo.oldHolder, true)
                    mChangeAnimations.remove(changeInfo.oldHolder)
                    dispatchFinishedWhenDone()
                }
            }).start()
        }
        if (newView != null) {
            val newViewAnimation = newView.animate()
            mChangeAnimations.add(changeInfo.newHolder)
            newViewAnimation.translationX(0f).translationY(0f).setDuration(changeDuration)
                .alpha(1f).setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationStart(animator: Animator) {
                        dispatchChangeStarting(changeInfo.newHolder, false)
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        newViewAnimation.setListener(null)
                        newView.alpha = 1f
                        dispatchChangeFinished(changeInfo.newHolder, false)
                        mChangeAnimations.remove(changeInfo.newHolder)
                        dispatchFinishedWhenDone()
                    }
                }).start()
        }
    }

    @VisibleForTesting
    fun endChangeAnimation(infoList: MutableList<ChangeInfo>, item: RecyclerView.ViewHolder?) {
        for (i in infoList.indices.reversed()) {
            val changeInfo = infoList[i]
            if (endChangeAnimationIfNecessary(changeInfo, item)
                && changeInfo.oldHolder == null && changeInfo.newHolder == null
            ) {
                infoList.remove(changeInfo)
            }
        }
    }

    @VisibleForTesting
    fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo) {
        if (changeInfo.oldHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.oldHolder)
        }
        if (changeInfo.newHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.newHolder)
        }
    }

    @VisibleForTesting
    fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo, item: RecyclerView.ViewHolder?): Boolean {
        var oldItem = false
        if (changeInfo.newHolder === item) {
            changeInfo.newHolder = null
        } else if (changeInfo.oldHolder === item) {
            changeInfo.oldHolder = null
            oldItem = true
        } else {
            return false
        }
        item!!.itemView.alpha = 1f
        item.itemView.translationX = 0f
        item.itemView.translationY = 0f
        dispatchChangeFinished(item, oldItem)
        return true
    }

    override fun endAnimation(item: RecyclerView.ViewHolder) {
        val view = item.itemView
        view.animate().cancel()
        endMoveAnimation(item, view)
        endChangeAnimation(mPendingChanges, item)
        endRemoveAnimation(item, view)
        endAddAnimation(item, view)
        for (i in mChangesList.indices.reversed()) {
            val changes = mChangesList[i]
            endChangeAnimation(changes, item)
            if (changes.isEmpty()) {
                mChangesList.removeAt(i)
            }
        }
        for (i in mMovesList.indices.reversed()) {
            val moves = mMovesList[i]
            for (j in moves.indices.reversed()) {
                val moveInfo = moves[j]
                if (moveInfo.holder === item) {
                    view.translationY = 0f
                    view.translationX = 0f
                    dispatchMoveFinished(item)
                    moves.removeAt(j)
                    if (moves.isEmpty()) {
                        mMovesList.removeAt(i)
                    }
                    break
                }
            }
        }
        for (i in mAdditionsList.indices.reversed()) {
            val additions = mAdditionsList[i]
            if (additions.remove(item)) {
                view.alpha = 1f
                view.scaleX = 1f
                view.scaleY = 1f
                dispatchAddFinished(item)
                if (additions.isEmpty()) {
                    mAdditionsList.removeAt(i)
                }
            }
        }
        check(!(mRemoveAnimations.remove(item) && DEBUG)) { "after animation is cancelled, item should not be in mRemoveAnimations list" }
        check(!(mAddAnimations.remove(item) && DEBUG)) { "after animation is cancelled, item should not be in mAddAnimations list" }
        check(!(mChangeAnimations.remove(item) && DEBUG)) { "after animation is cancelled, item should not be in mChangeAnimations list" }
        check(!(mMoveAnimations.remove(item) && DEBUG)) { "after animation is cancelled, item should not be in mMoveAnimations list" }
        dispatchFinishedWhenDone()
    }

    private fun endMoveAnimation(item: RecyclerView.ViewHolder, view: View) {
        for (i in mPendingMoves.indices.reversed()) {
            val moveInfo = mPendingMoves[i]
            if (moveInfo.holder === item) {
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(item)
                mPendingMoves.removeAt(i)
            }
        }
    }

    private fun endRemoveAnimation(item: RecyclerView.ViewHolder, view: View) {
        if (mPendingRemovals.remove(item)) {
            view.alpha = 1f
            view.scaleX = 1f
            view.scaleY = 1f
            dispatchRemoveFinished(item)
        }
    }

    private fun endAddAnimation(item: RecyclerView.ViewHolder, view: View) {
        if (mPendingAdditions.remove(item)) {
            view.alpha = 1f
            view.scaleX = 1f
            view.scaleY = 1f
            dispatchAddFinished(item)
        }
    }

    private fun resetAnimation(holder: RecyclerView.ViewHolder, interpolator: TimeInterpolator?) {
        holder.itemView.animate().interpolator =
            interpolator ?: DEFAULT_INTERPOLATOR
        endAnimation(holder)
    }

    override fun isRunning(): Boolean {
        var isRunning = (!mPendingAdditions.isEmpty()
                || !mPendingChanges.isEmpty()
                || !mPendingMoves.isEmpty()
                || !mPendingRemovals.isEmpty())
        isRunning = (isRunning || !mMoveAnimations.isEmpty()
                || !mRemoveAnimations.isEmpty()
                || !mAddAnimations.isEmpty()
                || !mChangeAnimations.isEmpty())
        isRunning = (isRunning || !mMovesList.isEmpty()
                || !mAdditionsList.isEmpty()
                || !mChangesList.isEmpty())
        return isRunning
    }

    /**
     * Check the state of currently pending and running animations. If there are none
     * pending/running, call [.dispatchAnimationsFinished] to notify any
     * listeners.
     */
    fun dispatchFinishedWhenDone() {
        if (!isRunning) {
            dispatchAnimationsFinished()
            if (mStartRunAnimations) {
                mStartRunAnimations = false
                if (mListener != null) {
                    mListener!!.onAnimatorEnd()
                }
            }
        }
    }

    override fun endAnimations() {
        var count = mPendingMoves.size
        for (i in count - 1 downTo 0) {
            val item = mPendingMoves[i]
            val view = item.holder.itemView
            view.translationY = 0f
            view.translationX = 0f
            dispatchMoveFinished(item.holder)
            mPendingMoves.removeAt(i)
        }
        count = mPendingRemovals.size
        for (i in count - 1 downTo 0) {
            val item = mPendingRemovals[i]
            dispatchRemoveFinished(item)
            mPendingRemovals.removeAt(i)
        }
        count = mPendingAdditions.size
        for (i in count - 1 downTo 0) {
            val item = mPendingAdditions[i]
            item.itemView.alpha = 1f
            item.itemView.scaleX = 1f
            item.itemView.scaleY = 1f
            dispatchAddFinished(item)
            mPendingAdditions.removeAt(i)
        }
        count = mPendingChanges.size
        for (i in count - 1 downTo 0) {
            endChangeAnimationIfNecessary(mPendingChanges[i])
        }
        mPendingChanges.clear()
        if (!isRunning) {
            return
        }
        endAnimations2()
    }

    /**方法太长，拆成2个，过代码检测 */
    private fun endAnimations2() {
        var count = 0
        var listCount = mMovesList.size
        for (i in listCount - 1 downTo 0) {
            val moves = mMovesList[i]
            count = moves.size
            for (j in count - 1 downTo 0) {
                val moveInfo = moves[j]
                val item = moveInfo.holder
                val view = item.itemView
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(moveInfo.holder)
                moves.removeAt(j)
                if (moves.isEmpty()) {
                    mMovesList.remove(moves)
                }
            }
        }
        listCount = mAdditionsList.size
        for (i in listCount - 1 downTo 0) {
            val additions = mAdditionsList[i]
            count = additions.size
            for (j in count - 1 downTo 0) {
                val item = additions[j]
                val view = item.itemView
                view.alpha = 1f
                dispatchAddFinished(item)
                additions.removeAt(j)
                if (additions.isEmpty()) {
                    mAdditionsList.remove(additions)
                }
            }
        }
        listCount = mChangesList.size
        for (i in listCount - 1 downTo 0) {
            val changes = mChangesList[i]
            count = changes.size
            for (j in count - 1 downTo 0) {
                endChangeAnimationIfNecessary(changes[j])
                if (changes.isEmpty()) {
                    mChangesList.remove(changes)
                }
            }
        }
        cancelAll(mRemoveAnimations)
        cancelAll(mMoveAnimations)
        cancelAll(mAddAnimations)
        cancelAll(mChangeAnimations)
        dispatchAnimationsFinished()
    }

    fun cancelAll(viewHolders: List<RecyclerView.ViewHolder?>) {
        for (i in viewHolders.indices.reversed()) {
            viewHolders[i]!!.itemView.animate().cancel()
        }
    }

    /**
     * {@inheritDoc}
     *
     *
     * If the payload list is not empty, DefaultItemAnimator returns `true`.
     * When this is the case:
     *
     *  * If you override [.animateChange], both
     * ViewHolder arguments will be the same instance.
     *
     *  *
     * If you are not overriding [.animateChange],
     * then DefaultItemAnimator will call [.animateMove] and
     * run a move animation instead.
     *
     *
     */
    override fun canReuseUpdatedViewHolder(
        viewHolder: RecyclerView.ViewHolder,
        payloads: List<Any>
    ): Boolean {
        return !payloads.isEmpty() || super.canReuseUpdatedViewHolder(viewHolder, payloads)
    }

    companion object {
        const val REMOVE_DURATION: Long = 500
        private const val TAG = "TodoCustomItemAnimator"
        private const val DEBUG = false
        private val DEFAULT_INTERPOLATOR = ValueAnimator().interpolator
        private const val MOVE_DURATION: Long = 500
        private const val ADD_DURATION: Long = 500
        private const val ADD_ALPHA_DELAY: Long = 100
        private const val ADD_REMOVE_ALPHA_DURATION: Long = 180
        private const val SCALE_09 = 0.9f
    }
}