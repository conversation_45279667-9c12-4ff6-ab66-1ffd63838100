/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * File: - $file$
 * Description:
 * Version: 1.0
 * Date : 2023/2/20
 * Author: <EMAIL>
 *
 * OPLUS Coding Static Checking Skip
 * ---------------------Revision History: ---------------------
 * <author> <data>   <version>    <desc>
 * baixu       2023/2/21      1.0     create file
</desc></version></data></author> */
package androidx.recyclerview.widget

import android.content.Context
import android.util.AttributeSet
/**
 * <AUTHOR>
 */
class NoteRecyclerView : COUIRecyclerView {
    private var mListener: OverScrollListener? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle)

    override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
        super.onOverScrolled(scrollX, scrollY, clampedX, clampedY)
        mListener?.onOverScroll(scrollX, scrollY)
    }

    interface OverScrollListener {
        /**
         * overScroll listener
         *
         * @return void
         */
        fun onOverScroll(scrollX: Int, scrollY: Int)
    }

    fun setOnOverScrollListener(listener: OverScrollListener?) {
        mListener = listener
    }
}