/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - PrivacyPolicyNoteFactory.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/8/30
 * * Author: NieXiaokang
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 **************************************************************************/
package com.oplus.note.privacypolicy.api

object PrivacyPolicyNoteFactory {
    private var provider: PrivacyPolicyNoteProvider? = null

    @JvmStatic
    fun registerProvider(provider: PrivacyPolicyNoteProvider) {
        this.provider = provider
    }

    @JvmStatic
    fun getProvider(): PrivacyPolicyNoteProvider? {
        return provider
    }
}