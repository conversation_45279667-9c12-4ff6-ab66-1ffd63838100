/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - PrivacyPolicyNoteHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/8/30
 * * Author: NieXiaokang
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 **************************************************************************/
package com.oplus.note.privacypolicy.api

import android.app.Activity
import android.app.Dialog
import android.content.Context

interface PrivacyPolicyNoteHelper {
    fun getPrivacyPolicyList(
        context: Context?,
        brand: String,
        isExist: Boolean,
        isNewAiVersion: Boolean
    ): MutableList<String>

    fun getPrivacyPolicyMap(context: Context?, brand: String): MutableMap<String, String>
    fun getPersonalInforList(context: Context?, isNewAiVersion: Boolean): MutableList<String>
    fun getUserAgreementList(context: Context?, brand: String): MutableList<String>
    fun getUserAgreementMap(context: Context, brand: String): MutableMap<String, String>
    fun dismissPrivacyPolicyDialog()
    fun getPrivacyPolicyDialog(): Dialog?
    fun showDialog(activity: Activity?, params: PrivacyPolicyParams)
    fun startPrivacyPage(context: Context?, type: PrivacyPolicyConstants.ActivityType)
}