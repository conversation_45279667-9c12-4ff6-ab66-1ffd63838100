plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.privacypolicyapi'

    resourcePrefix "privacypolicy_"

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation project(":common:logger:logger-api")
}