package com.oplus.note.notebook.internal

import android.content.Context
import com.google.common.truth.Truth.assertThat
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import org.junit.Test
import org.mockito.Mockito.anyInt
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`

class NotebookItemConverterTest {

    @Test
    fun testContextIsNull() {
        // 初始化
        val context: Context? = null
        val vm = mock(NotebookVM::class.java)
        val curFolder = mock(Folder::class.java)

        // 调用函数
        val transformer = NotebookItemConverter.createEncryptChosenTransformer(context, vm, curFolder)

        // 验证结果
        val result = transformer(emptyList())
        assertThat(result).isEmpty()
    }

    @Test
    fun testEmptyFolders() {
        // 初始化
        val context = mock(Context::class.java)
        val vm = mock(NotebookVM::class.java)
        val curFolder = mock(Folder::class.java)
        `when`(vm.findDefaultEncryptFolder()).thenReturn(mock(Folder::class.java))

        // 调用函数
        val transformer = NotebookItemConverter.createEncryptChosenTransformer(context, vm, curFolder)
        val result = transformer(emptyList())

        // 验证结果
        assertThat(result).isNotEmpty()
        assertThat(result.size).isEqualTo(1)
    }

    @Test
    fun testDefaultEncryptFolderIsNull() {
        // 初始化
        val context = mock(Context::class.java)
        val vm = mock(NotebookVM::class.java)
        val curFolder = mock(Folder::class.java)
        `when`(vm.findDefaultEncryptFolder()).thenReturn(mock(Folder::class.java))

        // 创建一个自定义文件夹
        val customFolder = Folder()
        customFolder.guid = "test_guid"
        customFolder.encrypted = Folder.FOLDER_ENCRYPTED

        // 调用函数
        val transformer = NotebookItemConverter.createEncryptChosenTransformer(context, vm, curFolder)
        val result = transformer(listOf(customFolder))

        // 验证结果
        assertThat(result.size).isEqualTo(2)
    }

    @Test
    fun testFilterLogic() {
        // 初始化
        val context = mock(Context::class.java)
        val vm = mock(NotebookVM::class.java)
        val curFolder = mock(Folder::class.java)
        `when`(vm.findDefaultEncryptFolder()).thenReturn(mock(Folder::class.java))
        `when`(context.applicationContext).thenReturn(context)
        `when`(context.getString(anyInt())).thenReturn("context")

        // 创建不同的文件夹类型
        val allNotesFolder = FolderFactory.regenerateAllNotesFolder(context)
        val summaryFolder = FolderFactory.generateCallSummaryFolder(context, "aa", false)
        val encryptedFolder = FolderFactory.regenerateDefaultEncryptFolder(context)

        // 调用函数
        val transformer = NotebookItemConverter.createEncryptChosenTransformer(context, vm, curFolder)
        val result = transformer(listOf(allNotesFolder, summaryFolder, encryptedFolder))

        // 验证结果
        assertThat(result).isNotEmpty()
        assertThat(result.size).isEqualTo(1) // 只包含默认加密文件夹
    }

    @Test
    fun testNoFoldersMatchCondition() {
        // 初始化
        val context = mock(Context::class.java)
        val vm = mock(NotebookVM::class.java)
        val curFolder = mock(Folder::class.java)
        `when`(vm.findDefaultEncryptFolder()).thenReturn(mock(Folder::class.java))

        // 创建一个文件夹，不满足任何条件
        val folder = Folder()
        folder.guid = "test_guid"
        folder.encrypted = Folder.FOLDER_ENCRYPTED

        // 调用函数
        val transformer = NotebookItemConverter.createEncryptChosenTransformer(context, vm, curFolder)
        val result = transformer(listOf(folder))

        // 验证结果
        assertThat(result.size).isEqualTo(2)
    }
}
