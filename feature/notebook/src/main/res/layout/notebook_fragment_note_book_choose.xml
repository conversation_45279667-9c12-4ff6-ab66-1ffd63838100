<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundElevatedWithCard"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/notebook_choose_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.0"
        android:clipToPadding="false"
        android:paddingBottom="32dp"
        android:paddingStart="@dimen/note_common_list_horizontal_padding"
        android:paddingTop="@dimen/coui_list_to_ex_top_padding"
        android:paddingEnd="@dimen/note_common_list_horizontal_padding" />

    <View
        android:id="@+id/notebook_choose_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_divider_height"
        android:alpha="1"
        android:background="?attr/couiColorDivider" />

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/notebook_choose_save"
        style="@style/Widget.COUI.Button.Large.ButtonNew"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/coui_horizontal_single_btn_margin"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        android:enabled="false"
        android:maxWidth="@dimen/coui_single_larger_btn_width"
        android:text="@string/save" />
</LinearLayout>