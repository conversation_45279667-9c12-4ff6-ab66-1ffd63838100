<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard">

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:paddingTop="10dp"
        app:elevation="0dp">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            android:layout_width="match_parent"
            app:layout_scrollFlags="noScroll"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/notebook_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="top|center_horizontal"
        android:clipToPadding="false"
        android:divider="@null"
        android:forceDarkAllowed="false"
        android:paddingStart="16dp"
        android:paddingTop="@dimen/coui_list_to_ex_top_padding"
        android:paddingEnd="16dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <FrameLayout
        android:id="@+id/notebook_navi_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:visibility="gone"
        android:background="?attr/couiColorBar">

        <com.coui.appcompat.bottomnavigation.COUINavigationView
            android:id="@+id/notebook_navi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:couiItemLayoutType="verticalType"
            app:couiNaviIconTint="@null"
            app:couiNaviMenu="@menu/notebook_menu_main_navi"
            app:couiNaviTextColor="@color/notebook_color_item_name"
            app:couiToolNavigationViewBg="?attr/couiColorBar"
            app:navigationType="tool" />
    </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
