<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/preference"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:minHeight="@dimen/support_preference_min_height"
    android:orientation="horizontal"
    android:paddingStart="@dimen/coui_list_item_left_padding"
    android:paddingEnd="@dimen/coui_list_item_right_padding"
    app:couiCardListHorizontalMargin="0dp">

    <ImageView
        android:id="@+id/notebook_item_drag"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:contentDescription="@null"
        android:importantForAccessibility="no"
        android:paddingEnd="4dp"
        android:src="@drawable/notebook_ic_drag" />

    <ImageView
        android:id="@+id/notebook_item_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/notebook_item_name"
        style="@style/couiTextHeadlineXS"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_weight="1.0"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/notebook_color_item_name"
        android:textDirection="locale"
        tools:text="Notebook Name Notebook Name Notebook Name Notebook Name" />

    <LinearLayout
        android:id="@+id/notebook_item_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/notebook_item_count"
            style="@style/couiTextBodyM"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/notebook_color_item_count"
            tools:text="1" />

        <ImageView
            android:id="@+id/notebook_item_encrypt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:contentDescription="@null"
            android:src="@drawable/notebook_btn_encrypt" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="8dp"
            android:paddingEnd="0dp">

            <CheckBox
                style="?attr/couiShapeCheckBoxStyle"
                android:id="@+id/notebook_item_select"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:clickable="false"
                android:focusable="false" />

            <ImageView
                android:id="@+id/notebook_item_next"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:contentDescription="@null"
                android:src="@drawable/notebook_btn_next" />
        </FrameLayout>
    </LinearLayout>

</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>
