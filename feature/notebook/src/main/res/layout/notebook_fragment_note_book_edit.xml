<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.grid.COUIPercentWidthLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="12dp"
    android:paddingBottom="24dp"
    app:gridNumber="@integer/grid_guide_column_preference"
    app:paddingSize="small"
    app:paddingType="cardListType"
    app:percentMode="paddingMode"
    tools:background="?attr/couiColorBackgroundElevatedWithCard">

    <com.oplus.note.notebook.internal.WithIconCardSingleInputView
        android:id="@+id/notebook_edit_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:couiHint="@string/note_edit_notebook_name_tips" />

    <com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="16dp">

        <TextView
            style="@style/couiTextDescription"
            android:textColor="?attr/couiColorSecondNeutral"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="@string/notebook_select_color" />

        <com.coui.appcompat.grid.COUIGridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            app:childHeight="32dp"
            app:childWidth="32dp"
            app:maxHorizontalGap="13.33dp"
            app:minHorizontalGap="10.5dp"
            app:specificType="specificSizeMode">

            <View
                android:contentDescription="@string/color_yellow"
                android:id="@+id/notebook_edit_cover_yellow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_yellow" />

            <View
                android:contentDescription="@string/color_orange"
                android:id="@+id/notebook_edit_cover_orange"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_orange" />

            <View
                android:contentDescription="@string/color_red"
                android:id="@+id/notebook_edit_cover_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_red" />

            <View
                android:contentDescription="@string/color_green"
                android:id="@+id/notebook_edit_cover_green"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_green" />

            <View
                android:contentDescription="@string/color_blue"
                android:id="@+id/notebook_edit_cover_azure"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_azure" />

            <View
                android:contentDescription="@string/color_gray"
                android:id="@+id/notebook_edit_cover_grey"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_grey" />

            <View
                android:contentDescription="@string/color_brown"
                android:id="@+id/notebook_edit_cover_brown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/notebook_edit_cover"
                android:backgroundTint="@color/notebook_cover_color_brown" />
        </com.coui.appcompat.grid.COUIGridLayout>

    </com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>

</com.coui.appcompat.grid.COUIPercentWidthLinearLayout>