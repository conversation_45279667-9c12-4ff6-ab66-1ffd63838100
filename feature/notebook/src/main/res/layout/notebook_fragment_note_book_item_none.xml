<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/preference"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginBottom="16dp"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="@dimen/coui_list_item_left_padding"
    android:paddingEnd="@dimen/coui_list_item_right_padding"
    app:couiCardListHorizontalMargin="0dp">

    <TextView
        style="@style/couiTextHeadlineXS"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/notebook_none"
        android:textColor="?attr/couiColorLabelSecondary" />

</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>
