<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/preference"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/coui_list_item_left_padding"
    android:paddingTop="8dp"
    android:paddingEnd="4dp"
    android:paddingBottom="8dp">

    <TextView
        style="@style/couiTextButtonM"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:text="@string/notebook_my"
        android:textDirection="locale" />

    <TextView
        android:id="@+id/notebook_item_new"
        style="@style/couiTextButtonM"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:text="@string/create_new"
        android:textColor="@color/coui_text_btn_text_color" />

</LinearLayout>
