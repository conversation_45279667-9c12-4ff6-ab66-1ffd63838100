/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: EncryptDecryptHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/22
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
@file:Suppress("FuncSingleCommentRule")

package com.oplus.note.notebook.internal.util

import android.content.Context
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCaller
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.nearme.note.db.DeleteFolderCacheHolder
import com.nearme.note.encrypt.EncryptedActivityResultProcessor
import com.nearme.note.util.AlertDialogHelper
import com.nearme.note.util.setCallSummaryEncryptedStatus
import com.oplus.note.baseres.R
import com.oplus.note.notebook.internal.NotebookEditViewModel
import com.oplus.note.notebook.internal.NotebookItem
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.statistic.StatisticsNotebook
import com.oplus.note.utils.VibrateHelper
import com.oplus.note.utils.getLocaleString
import com.oplus.note.utils.toast

internal class NotebookItemManipulator<T>(private val host: T) where T : LifecycleOwner, T : ActivityResultCaller {

    companion object {
        private const val MODE_ENCRYPT = 0
        private const val MODE_DELETE_FOLDER = 1
        private const val MODE_SWITCH_FOLDER = 2
        private const val MODE_UNKNOWN = -1
    }

    private val _context: Context?
        get() = when (host) {
            is Fragment -> host.context
            is ComponentActivity -> host
            else -> null
        }

    private val processor = EncryptedActivityResultProcessor(host).apply {
        setEncryptCallback { success ->
            if (success) {
                when (processorMode) {
                    MODE_SWITCH_FOLDER -> selectInternal(processingItems ?: emptyList())
                    MODE_ENCRYPT -> encryptOrDecryptInternal(processingItems ?: emptyList(), recentDeleteEncryptCallback)
                    MODE_DELETE_FOLDER -> deleteInternal(processingItems ?: emptyList())
                }
            } else {
                processorCallback?.invoke(false)
            }
            resetProcessingMode()
        }
    }
    private var processingItems: List<NotebookItem>? = null
    private var processorMode = MODE_UNKNOWN
    private var processorCallback: ((Boolean) -> Unit)? = null
    private var recentDeleteEncryptCallback: ((Boolean) -> Unit)? = null

    private val viewModel by when (host) {
        is Fragment -> host.viewModels<NotebookEditViewModel>({ host.requireActivity() })
        is ComponentActivity -> host.viewModels<NotebookEditViewModel>()
        else -> lazy { null }
    }

    private fun updateProcessingMode(mode: Int, items: List<NotebookItem>) {
        processingItems = items
        processorMode = mode
    }

    private fun resetProcessingMode() {
        processingItems = null
        processorMode = MODE_UNKNOWN
    }

    fun encryptOrDecrypt(
        selected: NotebookItem?,
        items: List<NotebookItem>,
        uiCallback: ((Boolean) -> Unit)? = null,
        recentDeleteEncryptCallback: (Boolean) -> Unit
    ) {
        if (selected == null || items.isEmpty()) {
            return
        }
        processorCallback = uiCallback

        if (!selected.encrypted) {
            updateProcessingMode(MODE_ENCRYPT, items)
            this.recentDeleteEncryptCallback = recentDeleteEncryptCallback
            processor.startEncrypt()
            return
        }

        encryptOrDecryptInternal(items, recentDeleteEncryptCallback)
    }

    private fun encryptOrDecryptInternal(items: List<NotebookItem>, recentDeleteEncryptCallback: ((Boolean) -> Unit)?) {
        val folders = mutableListOf<Folder>()
        val shouldEncrypt = items.find { !it.encrypted } != null
        items.forEach {
            it.encrypted = shouldEncrypt
            it.folder?.encrypted = if (it.encrypted) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
            if (it.folder?.state != Folder.FOLDER_STATE_NEW) {
                it.folder?.state = Folder.FOLDER_STATE_MODIFIED
            }

            if (it.folder != null) {
                folders.add(it.folder)
            }
        }
        viewModel?.updateFoldersAndNotesForEncryptOrDecrypt(folders, shouldEncrypt, recentDeleteEncryptCallback)

        val csf = folders.find { FolderFactory.isCallSummaryFolder(it) }
        setCallSummaryEncryptedStatus(_context, csf?.guid, csf?.encrypted)

        val resources = _context?.resources
        if (folders.size > 1) {
            _context?.toast(resources?.getString(if (shouldEncrypt) R.string.already_set_to_private else R.string.already_set_to_public))
        } else if (folders.size == 1) {
            val name = folders.getOrNull(0)?.name ?: ""
            val msg = resources?.getLocaleString(
                if (shouldEncrypt) R.string.notebook_set_to_encrypted else R.string.notebook_set_to_unencrypted,
                name
            )
            _context?.toast(msg)
        }
        processorCallback?.invoke(true)
    }

    fun delete(
        selected: NotebookItem?,
        items: List<NotebookItem>,
        isAllChecked: Boolean,
        isCloudEnable: () -> Boolean,
        uiCallback: ((Boolean) -> Unit)?
    ) {
        val context = _context
        if (context == null || selected == null || items.isEmpty()) {
            return
        }
        processorCallback = uiCallback

        val title = if (isAllChecked) {
            context.getString(R.string.title_delete_notebook_all)
        } else if (items.size > 1) {
            context.resources.getQuantityString(R.plurals.title_delete_notebook_multi, items.size, items.size)
        } else {
            context.getString(R.string.title_delete_note_book)
        }
        val message = if (isCloudEnable.invoke()) {
            if (isAllChecked) {
                context.getString(R.string.notebook_delete_cloud_all)
            } else if (items.size > 1) {
                context.resources.getQuantityString(R.plurals.notebook_delete_local_multi_cloud, items.size, items.size)
            } else {
                context.getString(R.string.notebook_delete_cloud)
            }
        } else {
            if (isAllChecked) {
                context.getString(R.string.notebook_delete_local_all)
            } else if (items.size > 1) {
                context.resources.getQuantityString(R.plurals.notebook_delete_local_multi_local, items.size, items.size)
            } else {
                context.getString(R.string.notebook_delete_local)
            }
        }

        val dialog = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setTitle(title)
            .setMessage(message)
            .setNeutralButton(R.string.delete_button) { _, _ ->
                val hasEncrypted = items.find { it.encrypted } != null
                if (!selected.encrypted && hasEncrypted) {
                    updateProcessingMode(MODE_DELETE_FOLDER, items)
                    processor.startEncrypt()
                    return@setNeutralButton
                }

                deleteInternal(items)
            }
            .setNegativeButton(R.string.cancel) { _, _ -> }
            .setWindowGravity(AlertDialogHelper.getBottomAlertDialogWindowGravity(context))
            .setWindowAnimStyle(AlertDialogHelper.getBottomAlertDialogWindowAnimStyle(context))
            .create()

        dialog.show()
    }

    private fun deleteInternal(items: List<NotebookItem>) {
        StatisticsNotebook.setEventDeleteFolder(_context, 2)
        val folders = mutableListOf<Folder>()
        items.forEach {
            if (it.folder != null) {
                folders.add(it.folder)
            }
        }

        DeleteFolderCacheHolder.updateDeletedFoldersNew(_context, folders)
        viewModel?.deleteFolders(folders)
        VibrateHelper.startDeleteVibrateForJava(_context, null)

        processorCallback?.invoke(true)
    }

    /**
     * @param selected 当前选中笔记本
     * @param target 新选中笔记本
     * @param skipCheck 是否跳过私密密码验证，加密笔记本间切换不需要验证。
     */
    fun select(selected: NotebookItem?, target: List<NotebookItem>, skipCheck: Boolean = false, uiCallback: ((Boolean) -> Unit)? = null) {
        if (target.isEmpty()) {
            return
        }
        processorCallback = uiCallback

        val hasEncrypted = target.find { it.encrypted } != null
        val isSelectedEncrypted = selected?.encrypted ?: false
        if (!isSelectedEncrypted && hasEncrypted && !skipCheck) {
            updateProcessingMode(MODE_SWITCH_FOLDER, target)
            processor.startEncrypt()
            return
        }

        selectInternal(target)
    }

    private fun selectInternal(target: List<NotebookItem>) {
        target.forEach { it.selected = true }
        processorCallback?.invoke(true)
    }
}