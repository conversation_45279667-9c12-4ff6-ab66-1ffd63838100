/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/05/30
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.activity.OnBackPressedCallback
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.forEach
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.lifecycle.map
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.note.edgeToEdge.CommonBaseFragment
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.R
import com.oplus.note.notebook.internal.util.LocalEditingFolderChannel
import com.oplus.note.notebook.internal.util.NotebookItemManipulator
import com.oplus.note.notebook.internal.util.SimpleItemTouchHelperCallBack
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.statistic.StatisticsNotebook

internal class NotebookFragment : CommonBaseFragment() {

    companion object {

        private const val TAG = "NotebookFragment"

        fun show(containerId: Int, fm: FragmentManager, stackName: String?) {
            fm.commit {
                setCustomAnimations(
                    R.anim.notebook_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit,
                    com.support.appcompat.R.anim.coui_close_slide_enter,
                    R.anim.notebook_close_slide_exit
                )
                add(containerId, newInstance(), "NotebookFragment")
                if (!stackName.isNullOrBlank()) {
                    addToBackStack(stackName)
                    setReorderingAllowed(true)
                }
            }
        }

        fun newInstance() = NotebookFragment()
    }

    private var adapter: NotebookRecyclerAdapter? = null
    private var onBackPressedCallback: OnBackPressedCallback? = null
    private var toolbar: COUIToolbar? = null
    private var notebookRv: RecyclerView? = null
    private var naviContainer: View? = null
    private var navi: COUINavigationView? = null

    private val nim = NotebookItemManipulator(this)
    private val notebookVM by viewModels<NotebookVM>({ requireActivity() })
    private val notebookEditVM by viewModels<NotebookEditViewModel>()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = inflater.inflate(R.layout.notebook_fragment_note_book, container, false)
        initWindowInsets(view)
        initTopBar(view)
        initRecyclerView(view)
        initNavigation(view)
        return view
    }

    private fun initWindowInsets(root: View) {
        val stableRVPaddingBottom =
            resources.getDimensionPixelSize(com.support.listview.R.dimen.coui_list_to_ex_bottom_padding) +
                resources.getDimensionPixelSize(com.support.bottomnavigation.R.dimen.coui_tool_navigation_item_height)

        EdgeToEdgeManager.observeOnApplyWindowInsets(root) { _, insets ->
            val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.statusBars())
            root.updatePadding(top = stableStatusBarInsets.top)

            val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
            naviContainer?.updatePadding(bottom = bottom)
            (notebookRv?.layoutParams as? MarginLayoutParams)?.bottomMargin = bottom
            notebookRv?.updatePadding(bottom = stableRVPaddingBottom)
        }
    }

    private fun initTopBar(root: View) {
        toolbar = root.findViewById(R.id.toolbar)
        toolbar?.setNavigationIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
        toolbar?.setNavigationOnClickListener {
            onBackPressed()
        }
        toolbar?.setTitle(com.oplus.note.baseres.R.string.note_notebook)

        toolbar?.inflateMenu(R.menu.notebook_menu_main)
        toolbar?.setOnMenuItemClickListener { menu ->
            when (menu.itemId) {
                R.id.notebook_menu_check -> toggleCheckableState()
                R.id.notebook_menu_cancel -> onBackPressed()
            }
            return@setOnMenuItemClickListener true
        }
    }

    private fun triggerCancel() {
        parentFragmentManager.popBackStack()
    }

    private fun initRecyclerView(root: View) {
        notebookRv = root.findViewById(R.id.notebook_list)
        notebookRv?.layoutManager = COUILinearLayoutManager(context)
        adapter = NotebookRecyclerAdapter(root.context,
            onSelectedChangedListener = { target, adapterSelectedCallback ->
                triggerSelectNotebook(target, adapterSelectedCallback)
            },
            onCheckedChangedListener = {
                refreshNavigationItemViewState()
                refreshToolbarState()
            },
            onItemMoveListener = {
                refreshNavigationItemViewState(isDrag = true, it)
            },
            onCreateNewFolderClickListener = {
                StatisticsNotebook.setEventNewNotebook(context)
                NotebookEditFragment.show(childFragmentManager, null, 0)
            },
            onDragResultListener = { folder ->
                notebookEditVM.updateByDrag(folder.mapNotNull { it.folder })
            },
            onItemLongClickListener = {
                toggleCheckableState()
            })
        notebookRv?.adapter = adapter
        notebookRv?.addItemDecoration(COUIRecyclerView.COUIRecyclerViewItemDecoration(context))

        val callback: ItemTouchHelper.Callback = SimpleItemTouchHelperCallBack(adapter)
        val helper = ItemTouchHelper(callback)
        helper.attachToRecyclerView(notebookRv)
        adapter?.setItemTouchHelper(helper)
    }

    private fun refreshNavigationItemViewState(isDrag: Boolean = false, isUnDisable: Boolean = false) {
        val isCheckableState = isRecyclerViewInCheckableState()

        naviContainer?.visibility = if (isCheckableState) View.VISIBLE else View.GONE

        val checkedValues = adapter?.getCurrentValues()?.filter { it.checked } ?: emptyList()

        val editable = checkedValues.size == 1 &&
            checkedValues.find {
                FolderFactory.isSummaryFolder(it.folder) ||
                    FolderFactory.isCollectionFolder(it.folder) ||
                    FolderFactory.isQuickNoteFolder(it.folder)  ||
                    FolderFactory.isRecentDeleteFolder(it.folder)
            } == null
        val encryptable = checkedValues.isNotEmpty()
        val deletable = checkedValues.isNotEmpty() && checkedValues.find {
            FolderFactory.isRecentDeleteFolder(it.folder)
        } == null
        navi?.menu?.forEach { menuItem ->
            when (menuItem.itemId) {
                R.id.notebook_navi_edit -> {
                    if (isDrag) {
                        menuItem.isEnabled = editable && isUnDisable
                    } else {
                        menuItem.isEnabled = editable
                    }
                }
                R.id.notebook_navi_encrypt -> {
                    if (isDrag) {
                        menuItem.isEnabled = encryptable && isUnDisable
                    } else {
                        menuItem.isEnabled = encryptable
                    }

                    val shouldShowAsEncrypt = checkedValues.any { !it.encrypted }
                    if (shouldShowAsEncrypt || !encryptable) {
                        menuItem.setTitle(com.oplus.note.baseres.R.string.set_encrypted_to_folder)
                        menuItem.setIcon(com.oplus.note.baseres.R.drawable.note_ic_encrypt)
                    } else {
                        menuItem.setTitle(com.oplus.note.baseres.R.string.set_unencrypted_to_folder)
                        menuItem.setIcon(com.oplus.note.baseres.R.drawable.note_ic_decrypt)
                    }
                }

                R.id.notebook_navi_delete -> {
                    if (isDrag) {
                        menuItem.isEnabled = deletable && isUnDisable
                    } else {
                        menuItem.isEnabled = deletable
                    }
                }
            }
        }
    }

    private fun initNavigation(root: View) {
        naviContainer = root.findViewById(R.id.notebook_navi_container)
        navi = root.findViewById(R.id.notebook_navi)
        navi?.setOnItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.notebook_navi_edit -> triggerEditNotebook()
                R.id.notebook_navi_encrypt -> triggerEncryptNotebook()
                R.id.notebook_navi_delete -> triggerDeleteNotebook()
            }
            return@setOnItemSelectedListener true
        }
    }

    private fun triggerSelectNotebook(target: NotebookItem?, adapterSelectedCallback: () -> Unit) {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val targets = target?.let { listOf(it) } ?: emptyList()
        if (targets.isEmpty()) {
            triggerCancel()
            return
        }
        nim.select(selected, targets) { success ->
            if (success) {
                selected?.selected = false
                adapterSelectedCallback.invoke()

                notebookVM.updateCurrentFolder(target?.folder)
                triggerCancel()
            }
        }
    }

    private fun triggerDeleteNotebook() {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val checkedValues = adapter?.getCurrentValues()?.filter { it.checked } ?: emptyList()
        val isAllChecked = notebookVM.getNotebooks().filter { !FolderFactory.isEmbedFolder(it) }.size == checkedValues.size
        nim.delete(selected, checkedValues, isAllChecked, notebookVM.isCloudEnable) {
            toggleCheckableState()
            if (checkedValues.contains(selected)) {
                val defaultCur = notebookVM.findAllNoteFolder()
                notebookVM.updateCurrentFolder(defaultCur)
            }
        }
    }

    private fun triggerEncryptNotebook() {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val checkedValues = adapter?.getCurrentValues()?.filter { it.checked } ?: emptyList()
        nim.encryptOrDecrypt(selected, checkedValues, { success ->
            if (success) {
                toggleCheckableState()
            }
        }) { isEncrypt ->
            AppLogger.BASIC.d(TAG, "setOnRecentDelEncryptCallBack")
            val encryptedRecentFolder = notebookVM.findRecentDeleteFolder()
            encryptedRecentFolder.encrypted = if (isEncrypt) FolderInfo.FOLDER_ENCRYPTED else FolderInfo.FOLDER_UNENCRYPTED
            val isRecentDeleteFolder = FolderFactory.isRecentDeleteFolder(adapter?.getCurrentValues()?.find { it.selected }?.folder?.guid)
            if (isRecentDeleteFolder) notebookVM.updateRecentDeleteFolderCurrentFolder(encryptedRecentFolder)
        }
    }

    private fun triggerEditNotebook() {
        val checked = adapter?.getCurrentValues()?.find { it.checked }
        NotebookEditFragment.show(childFragmentManager, checked?.folder, 0)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        registerOnBackPressedCallback()

        initObservers()
    }

    private fun initObservers() {
        AppLogger.BASIC.d(TAG, "initObservers")
        notebookVM.foldersAndRichNoteCounts.map(NotebookItemConverter.createTransformer(context, notebookVM))
            .observe(viewLifecycleOwner) { items ->
                // 编辑模式下拖拽排序会直接更新数据库，如果这里响应变化，会导致列表顺序显示错误
                if (!isRecyclerViewInCheckableState()) {
                    AppLogger.BASIC.d(TAG, "initObservers refresh notebook items")
                    adapter?.refresh(items)
                }
            }
    }

    private fun registerOnBackPressedCallback() {
        onBackPressedCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPressed()
            }
        }.apply {
            addBackInvokedCallback(this)
        }
    }

    private fun onBackPressed() {
        if (isRecyclerViewInCheckableState()) {
            toggleCheckableState()
        } else {
            triggerCancel()
        }
    }

    private fun isRecyclerViewInCheckableState(): Boolean {
        return adapter?.isCheckableState() ?: false
    }

    private fun toggleCheckableState() {
        // firstly,update list adapter check state.
        adapter?.toggleCheckableState()

        // navi
        refreshNavigationItemViewState()
        // toolbar
        refreshToolbarState()
    }

    private fun refreshToolbarState() {
        val isCheckableState = isRecyclerViewInCheckableState()

        toolbar?.isTitleCenterStyle = isCheckableState
        toolbar?.clearMenu()
        toolbar?.inflateMenu(if (isCheckableState) R.menu.notebook_menu_main_edit else R.menu.notebook_menu_main)

        if (isCheckableState) {
            toolbar?.setNavigationIcon(null)

            val allCount =
                adapter?.getCurrentValues()?.count { it.folder != null && !FolderFactory.isUnEncryptFolder(it.folder) } ?: 0
            val checkCount = adapter?.getCurrentValues()?.count { it.checked } ?: 0
            when (checkCount) {
                0 -> toolbar?.setTitle(com.oplus.note.baseres.R.string.memo_select_note)
                allCount -> toolbar?.setTitle(com.oplus.note.baseres.R.string.memo_note_select_all)
                else -> toolbar?.title = getString(com.oplus.note.baseres.R.string.memo_note_select_num, "$checkCount")
            }
        } else {
            toolbar?.setNavigationIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)

            toolbar?.setTitle(com.oplus.note.baseres.R.string.note_notebook)
        }
    }

    private val editingFolderReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val editingFolder = LocalEditingFolderChannel.getEditingFolder(intent)
            if(editingFolder != null && isRecyclerViewInCheckableState()) {
                adapter?.update(editingFolder)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LocalEditingFolderChannel.register(context, editingFolderReceiver)
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalEditingFolderChannel.unregister(context, editingFolderReceiver)
    }

    override fun onResume() {
        super.onResume()
        onBackPressedCallback?.isEnabled = true
    }

    override fun onPause() {
        super.onPause()
        onBackPressedCallback?.isEnabled = false
    }
}