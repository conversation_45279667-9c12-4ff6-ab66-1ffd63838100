/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/05/30
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook

import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCaller
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import com.oplus.note.notebook.internal.NotebookVM

object NotebookAgentFactory {

    /**
     * 创建生命周期与 [owner] 绑定的 [NotebookAgent] 对象。
     */
    fun create(owner: ComponentActivity): Lazy<NotebookAgent> {
        return owner.viewModels<NotebookVM>()
    }

    /**
     * 创建生命周期与 [owner] 所在Activity绑定的 [NotebookAgent] 对象。
     */
    fun create(owner: Fragment): Lazy<NotebookAgent> {
        return owner.viewModels<NotebookVM>({ owner.requireActivity() })
    }

    fun <T> createEncryptAgent(host: T): NotebookEncryptAgent<T> where T : LifecycleOwner, T : ActivityResultCaller {
        return NotebookEncryptAgent(host)
    }
}