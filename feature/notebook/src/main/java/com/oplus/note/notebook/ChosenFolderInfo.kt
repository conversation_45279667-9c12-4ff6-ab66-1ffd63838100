/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: ChosenFolderInfo.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/09/09
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook

import com.oplus.note.repo.note.entity.Folder

data class ChosenFolderInfo(
    val preFolder: Folder?,
    val newFolder: Folder,
    val isEncrypt: <PERSON>ole<PERSON>
)