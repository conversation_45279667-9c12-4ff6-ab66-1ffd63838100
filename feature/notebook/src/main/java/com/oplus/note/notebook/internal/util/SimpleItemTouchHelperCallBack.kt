/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: SimpleItemTouchHelperCallback.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/07/16
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal.util

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.oplus.note.notebook.internal.NotebookRecyclerAdapter
import kotlin.math.abs

internal class SimpleItemTouchHelperCallBack(private val mAdapter: NotebookRecyclerAdapter?) : ItemTouchHelper.Callback() {

    private var mSelectedViewHolder: NotebookDraggableViewHolder? = null

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {

    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        val dragFlag = ItemTouchHelper.DOWN or ItemTouchHelper.UP
        return makeMovementFlags(dragFlag, 0)
    }

    override fun onMove(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder): Boolean {
        if (viewHolder.itemViewType != target.itemViewType) {
            return false
        }
        if(viewHolder !is NotebookDraggableViewHolder || target !is NotebookDraggableViewHolder || !target.isDraggable()) {
            return false
        }
        mSelectedViewHolder?.itemView?.performHapticFeedback(
            COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE,
            0
        )
        mAdapter?.onItemMove(viewHolder, target)
        return true
    }

    override fun isLongPressDragEnabled(): Boolean {
        return false
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
            if (viewHolder is NotebookDraggableViewHolder) {
                // Let the view holder know that this item is being moved or dragged
                mSelectedViewHolder = viewHolder
                mSelectedViewHolder?.onItemSelected()
            }
        }
        if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
            mSelectedViewHolder?.onItemDrop()
        }
    }

    override fun chooseDropTarget(
        selected: RecyclerView.ViewHolder,
        dropTargets: List<RecyclerView.ViewHolder>,
        curX: Int,
        curY: Int
    ): RecyclerView.ViewHolder? {
        val right = curX + selected.itemView.width
        val bottom = curY + selected.itemView.height
        var winner: RecyclerView.ViewHolder? = null
        var winnerScore = -1
        val dx = curX - selected.itemView.left
        val dy = curY - selected.itemView.top
        val targetsSize = dropTargets.size
        for (i in 0 until targetsSize) {
            val target = dropTargets[i]
            if (dx > 0) {
                val diff = target.itemView.right - right
                if (diff < 0 && target.itemView.right > selected.itemView.right) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dx < 0) {
                val diff = target.itemView.left - curX
                if (diff > 0 && target.itemView.left < selected.itemView.left) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dy < 0) {
                val diff = target.itemView.top + target.itemView.height / 2 - curY
                if (diff > 0 && target.itemView.top + target.itemView.height / 2 < selected.itemView.top) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
            if (dy > 0) {
                val diff = target.itemView.bottom - target.itemView.height / 2 - bottom
                if (diff < 0 && target.itemView.bottom - target.itemView.height / 2 > selected.itemView.bottom) {
                    val score = abs(diff)
                    if (score > winnerScore) {
                        winnerScore = score
                        winner = target
                    }
                }
            }
        }
        return winner
    }
}