/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NotebookEditViewModel.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/18
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.oplus.cloud.status.Device
import com.oplus.note.repo.note.NoteRepoFactory
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID

internal class NotebookEditViewModel(application: Application) : AndroidViewModel(application) {

    private val folderRepo = NoteRepoFactory.getFolderRepo()

    suspend fun hasSameNameFolder(curFolder: Folder?, name: String): Boolean = withContext(Dispatchers.IO) {
        val folders = folderRepo?.findNotDeletedFolderByName(name)
        val hasSameNameFolderInDb = if (curFolder != null) {
            !folders.isNullOrEmpty() && folders.find { it.guid == curFolder.guid } == null
        } else {
            !folders.isNullOrEmpty()
        }
        if (hasSameNameFolderInDb) {
            return@withContext true
        }

        val isEmbedFolderName = FolderFactory.isEmbedFolderName(getApplication(), name)
        return@withContext isEmbedFolderName
    }

    fun updateByDrag(folders: List<Folder>) {
        if (folders.isNotEmpty()) {
            viewModelScope.launch {
                folders.forEach {
                    if (it.state != Folder.FOLDER_STATE_NEW) {
                        it.state = Folder.FOLDER_STATE_MODIFIED
                    }
                }
                folderRepo?.update(folders)
            }
        }
    }

    /**
     * 笔记本弹窗界面新建或修改笔记本
     *
     * @return 修改后的笔记本
     */
    suspend fun insertOrUpdateFromEdit(folder: Folder?, name: String, cover: String): Folder = withContext(Dispatchers.IO) {
        if (folder == null) {
            val real = FolderFactory.createEmptyFolder(Device.getDeviceIMEI(getApplication()))
            real.name = name
            real.guid = UUID.randomUUID().toString()
            real.extra?.setPureCover(cover)
            folderRepo?.insert(real)
            return@withContext real
        } else {
            folder.state = Folder.FOLDER_STATE_MODIFIED
            folder.name = name
            folder.extra?.setPureCover(cover)
            folder.modifyTime = Date(System.currentTimeMillis())
            folderRepo?.insert(folder)
            return@withContext folder
        }
    }

    fun updateFoldersAndNotesForEncryptOrDecrypt(
        folders: List<Folder>,
        shouldEncrypt: Boolean,
        recentDeleteEncryptCallback: ((Boolean) -> Unit)?
    ) {
        if (folders.isEmpty()) {
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            folderRepo?.updateFoldersAndNotesForEncryptOrDecrypt(folders, shouldEncrypt, recentDeleteEncryptCallback)
        }
    }

    fun deleteFolders(folders: MutableList<Folder>) {
        viewModelScope.launch {
            folderRepo?.deleteFolders(folders)
        }
    }
}