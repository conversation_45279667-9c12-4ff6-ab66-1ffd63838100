/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NotebookEditFragment.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/09
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.graphics.drawable.LayerDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.nearme.note.util.NoteColorTextUtils
import com.nearme.note.util.ResourceUtils
import com.oplus.note.notebook.R
import com.oplus.note.notebook.internal.util.LocalEditingFolderChannel
import com.oplus.note.osdk.proxy.OplusInputMethodManagerProxy
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.statistic.StatisticsNotebook
import com.oplus.note.utils.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

internal class NotebookEditFragment : COUIPanelFragment() {

    companion object {
        private const val MAX_FOLDER_NAME_LENGTH: Int = 50

        private const val ARGUMENTS_EXTRA_FOLDER = "arguments_extra_folder"
        private const val ARGUMENTS_EXTRA_FROM = "arguments_extra_from"

        /**
         * @param from 触发方来源，用于埋点[StatisticsNotebook.setEventCreateFolder]
         */
        fun show(fm: FragmentManager, folder: Folder? = null, from: Int) {
            val ef = COUIBottomSheetDialogFragment()
            ef.setMainPanelFragment(newInstance(folder, from))
            ef.show(fm, NotebookEditFragment::class.java.name)
            OplusInputMethodManagerProxy.setShowSoftInputEnabled(true)
        }

        private fun newInstance(folder: Folder?, from: Int): NotebookEditFragment {
            val fragment = NotebookEditFragment()
            val args = Bundle()
            args.putParcelable(ARGUMENTS_EXTRA_FOLDER, folder)
            args.putInt(ARGUMENTS_EXTRA_FROM, from)
            fragment.arguments = args
            return fragment
        }
    }

    private var folder: Folder? = null
    private var from: Int = 0
    private var saveItem: MenuItem? = null
    private var input: WithIconCardSingleInputView? = null

    private val notebookEditVM by viewModels<NotebookEditViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        folder = arguments?.getParcelable(ARGUMENTS_EXTRA_FOLDER)
        from = arguments?.getInt(ARGUMENTS_EXTRA_FROM, 0) ?: 0
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        val bgColor =
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard)
        panelView?.setBackgroundColor(bgColor)
        LayoutInflater.from(context).inflate(R.layout.notebook_fragment_note_book_edit, contentView as? ViewGroup, true)

        initToolbar()
        initName(panelView)
        initGridCovers(panelView)
    }

    private fun initGridCovers(root: View?) {
        if (root == null) {
            return
        }

        covers.addAll(
            listOf(
                Cover(NoteBookData.IMG_COVER_PURE_YELLOW, false, root.findViewById(R.id.notebook_edit_cover_yellow)),
                Cover(NoteBookData.IMG_COVER_PURE_ORANGE, false, root.findViewById(R.id.notebook_edit_cover_orange)),
                Cover(NoteBookData.IMG_COVER_PURE_RED, false, root.findViewById(R.id.notebook_edit_cover_red)),
                Cover(NoteBookData.IMG_COVER_PURE_GREEN, false, root.findViewById(R.id.notebook_edit_cover_green)),
                Cover(NoteBookData.IMG_COVER_PURE_AZURE, false, root.findViewById(R.id.notebook_edit_cover_azure)),
                Cover(NoteBookData.IMG_COVER_PURE_GREY, false, root.findViewById(R.id.notebook_edit_cover_grey)),
                Cover(NoteBookData.IMG_COVER_PURE_BROWN, false, root.findViewById(R.id.notebook_edit_cover_brown))
            )
        )

        covers.forEach { cover ->
            cover.view?.setOnClickListener { _ -> updateSelectCover(cover) }
        }

        val curCoverId = folder?.extra?.getPureCover() ?: NoteBookData.getDefaultPureCover()
        updateSelectCover(covers.find { it.id == curCoverId })
    }

    private data class Cover(
        val id: String, var select: Boolean, val view: View?
    )

    private val covers = mutableListOf<Cover>()

    private fun updateSelectCover(selectCover: Cover?) {
        covers.forEach { cover ->
            cover.select = cover.id == selectCover?.id

            val drawable = cover.view?.background as? LayerDrawable
            drawable?.findDrawableByLayerId(R.id.notebook_edit_cover_fg)?.alpha = if (cover.select) 255 else 0
        }

        val id = ResourceUtils.getResIdByResName(context, selectCover?.id)
        input?.setImgRes(id)
    }

    private fun initName(root: View?) {
        if (root == null) {
            return
        }
        input = root.findViewById(R.id.notebook_edit_name)
        input?.editText?.addTextChangedListener(object : TextWatcher {
            private var bt: String? = null
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                bt = p0?.toString() ?: ""
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}

            override fun afterTextChanged(p0: Editable?) {
                if (p0 == null) {
                    return
                }

                if (NoteColorTextUtils.containsIllegalCharFileName(p0)) {
                    p0.replace(0, p0.length, bt)
                    toast(getString(com.oplus.note.baseres.R.string.toast_file_name_illegal_input_char) + "\\ / : * ? \" < > |")
                } else if (NoteColorTextUtils.lengthLimit(p0, MAX_FOLDER_NAME_LENGTH)) {
                    if (bt.isNullOrEmpty()) {
                        p0.replace(0, p0.length, p0.substring(0, MAX_FOLDER_NAME_LENGTH))
                    } else {
                        p0.replace(0, p0.length, bt)
                    }

                    toast(com.oplus.note.baseres.R.string.note_reach_folder_name_lenth_limit)
                }

                saveItem?.isEnabled = !TextUtils.isEmpty(p0)
            }
        })
        input?.editText?.setText(folder?.name)
        if (FolderFactory.isPaintFolder(folder)) {
            input?.editText?.isEnabled = false
        }
    }

    private fun initToolbar() {
        toolbar.apply {
            visibility = View.VISIBLE
            title = if (isCreateMode()) {
                context.getString(com.oplus.note.baseres.R.string.note_new_notebook)
            } else {
                context.getString(com.oplus.note.baseres.R.string.note_edit_notebook_name)
            }
            isTitleCenterStyle = true
            inflateMenu(R.menu.notebook_menu_edit)

            saveItem = menu.findItem(R.id.notebook_menu_edit_save)
            saveItem?.setOnMenuItemClickListener {
                triggerNotebookSave()
                true
            }

            menu.findItem(R.id.notebook_menu_edit_cancel).setOnMenuItemClickListener {
                triggerNotebookCancel()
                true
            }
        }
    }

    private fun triggerNotebookCancel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private var saveJob: Job? = null

    private fun triggerNotebookSave() {
        if (saveJob == null || saveJob?.isCompleted == true) {
            saveJob = lifecycleScope.launch {
                val name = input?.editText?.text?.toString()?.trim() ?: ""
                val cover = covers.find { it.select }?.id ?: NoteBookData.getDefaultPureCover()

                if (TextUtils.isEmpty(name)) {
                    withContext(Dispatchers.Main) { toast(com.oplus.note.baseres.R.string.toast_file_name_empty) }
                    return@launch
                }

                val haveSameNameFolder: Boolean = notebookEditVM.hasSameNameFolder(folder, name)
                if (haveSameNameFolder) {
                    withContext(Dispatchers.Main) { toast(com.oplus.note.baseres.R.string.note_folder_name_exists_new) }
                    return@launch
                }

                val isCreate = folder == null
                if (isCreate) {
                    StatisticsNotebook.setEventCreateFolder(context, from)
                } else {
                    StatisticsNotebook.setEventRenameFolder(context)
                }
                val result = notebookEditVM.insertOrUpdateFromEdit(folder, name, cover)
                LocalEditingFolderChannel.notifyEditingFolderChanged(context, result)

                triggerNotebookCancel()
            }
        }
    }

    private fun isCreateMode(): Boolean {
        return folder == null
    }
}