/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: FolderCacher.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/04/18
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal.util

import android.content.Context
import com.oplus.note.repo.note.FolderRepo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.utils.SharedPreferencesUtil
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 处理当前选中文件夹信息缓存业务
 */
class FolderCacheManager(private val repo: FolderRepo?, private val dispatcher: CoroutineDispatcher = Dispatchers.IO) {

    companion object {
        private const val TAG = "FolderCacheManager"
    }

    /**
     * find selected folder cache
     */
    suspend fun find(context: Context): Folder {
        val ctx = context.applicationContext

        return withContext(dispatcher) {
            val defaultFolder = FolderFactory.regenerateAllNotesFolder(context)

            val name = SharedPreferencesUtil.getInstance().getString(ctx, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.LAST_FOLDER_NAME_KEY, defaultFolder.name)
            val guid = SharedPreferencesUtil.getInstance().getString(ctx, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.LAST_FOLDER_GUID_KEY, defaultFolder.guid)

            // the cache info may invalid, get refresh info from DB.
            val result = repo?.findByGuid(guid)?.takeIf { !it.isEncrypted }

            if (FolderFactory.isAllNotesFolder(result)) {
                return@withContext FolderFactory.regenerateAllNotesFolder(context)
            } else if (FolderFactory.isUncategorizedFolder(result)) {
                return@withContext FolderFactory.regenerateUncategorizedFolder(context, null)
            } else {
                return@withContext result ?: defaultFolder
            }
        }
    }

    /**
     * update selected folder cache
     */
    suspend fun update(context: Context, name: String, guid: String): Boolean {
        val ctx = context.applicationContext

        return withContext(dispatcher) {
            SharedPreferencesUtil.getInstance().putString(ctx, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.LAST_FOLDER_NAME_KEY, name)
            SharedPreferencesUtil.getInstance().putString(ctx, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.LAST_FOLDER_GUID_KEY, guid)
            return@withContext true
        }
    }
}