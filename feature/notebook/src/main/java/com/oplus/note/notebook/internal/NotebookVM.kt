/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NoteBookViewMode.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/03
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.app.Application
import androidx.annotation.IdRes
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.ChosenFolderInfo
import com.oplus.note.notebook.NotebookAgent
import com.oplus.note.notebook.internal.util.FolderCacheManager
import com.oplus.note.repo.note.NoteRepoFactory
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.RichNoteCount
import com.oplus.note.statistic.StatisticsNotebook
import kotlinx.coroutines.launch

internal class NotebookVM(application: Application) : AndroidViewModel(application), NotebookAgent {

    companion object {
        private const val TAG = "NotebookVM"
    }

    private val folderRepo = NoteRepoFactory.getFolderRepo()
    private val richNoteRepo = NoteRepoFactory.getNoteRepo()

    private val foldersInDb = folderRepo?.getViewableFoldersLiveData()
    private val foldersInView: LiveData<List<Folder>>? = foldersInDb?.map { folders ->
        val result = mutableListOf<Folder>()

        result.add(FolderFactory.regenerateAllNotesFolder(getApplication()))
        result.addAll(folders.filter { folder -> !FolderFactory.isEmbedFolder(folder) })
        result.add(FolderFactory.regenerateUncategorizedFolder(getApplication(), folders.find { FolderFactory.isUncategorizedFolder(it) }))
        result.add(FolderFactory.regenerateDefaultEncryptFolder(getApplication(), folders.find { FolderFactory.isDefaultEncryptedFolder(it) }))
        result.add(FolderFactory.regenerateRecentDeleteFolder(getApplication()))

        result
    }
    private val richNoteCountsInDb = richNoteRepo?.getRichNoteCountLiveData()
    val foldersAndRichNoteCounts = MediatorLiveData<List<Folder>>().apply {
        if (foldersInView != null) {
            addSource(foldersInView) { f ->
                val result = mergeFolderAndRichNote(f, richNoteCountsInDb?.value)
                if (result != null) value = result
            }
        }
        if (richNoteCountsInDb != null) {
            addSource(richNoteCountsInDb) { r ->
                val result = mergeFolderAndRichNote(foldersInView?.value, r)
                if (result != null) value = result
            }
        }
    }

    private fun mergeFolderAndRichNote(folders: List<Folder>?, counts: List<RichNoteCount>?): List<Folder>? {
        if (folders == null || counts == null) {
            return null
        }
        return folders
    }

    val currentFolder: MutableLiveData<Folder> = MutableLiveData()

    override var isCloudEnable: () -> Boolean = { false }

    private val folderCacheManager = FolderCacheManager(folderRepo)

    init {
        initCacheFolder()
    }

    private fun initCacheFolder() {
        viewModelScope.launch {
            val cacheFolder = folderCacheManager.find(getApplication())
            if (currentFolder.value == null) {
                currentFolder.value = cacheFolder
            }
        }
    }

    private fun verifySourceIsAvailable() {
        if (foldersAndRichNoteCounts.value.isNullOrEmpty()) {
            AppLogger.BASIC.e(TAG, "verifySourceIsAvailable failed, folders is not available.")
        }
    }

    override fun findRichNoteCountInFolder(folder: Folder?): Int {
        verifySourceIsAvailable()

        if (folder == null) {
            return 0
        }
        if (FolderFactory.isAllNotesFolder(folder)) {
            // 过滤加密笔记本和最近删除后，累加各笔记本中的笔记数量
            return richNoteCountsInDb?.value?.filter { rnc ->
                !(foldersInDb?.value?.find { rnc.folderGuid == it.guid }?.isEncrypted ?: false) &&
                    !FolderFactory.isRecentDeleteFolder(rnc.folderGuid)
            }?.sumOf { it.noteCount } ?: 0
        } else if (FolderFactory.isUncategorizedFolder(folder)) {
            // 未分类需要包括无效笔记本
            return richNoteCountsInDb?.value?.filter { rnc ->
                (foldersInDb?.value?.find { rnc.folderGuid == it.guid } == null &&
                    !FolderFactory.isRecentDeleteFolder(rnc.folderGuid)) ||
                    rnc.folderGuid == folder.guid
            }?.sumOf { it.noteCount } ?: 0
        }
        return richNoteCountsInDb?.value?.find { it.folderGuid == folder.guid }?.noteCount ?: 0
    }

    override fun findRecentFolderRichNoteCountInFolder(): Int {
        return findRichNoteCountInFolder(findRecentDeleteFolder())
    }

    override fun findAllNoteFolder(): Folder {
        verifySourceIsAvailable()
        val inMemory = foldersAndRichNoteCounts.value?.find { FolderFactory.isAllNotesFolder(it) }
        return FolderFactory.regenerateAllNotesFolder(getApplication(), inMemory)
    }

    /**
     * 查询内置未分类（默认笔记本），依赖[observeNotebooks]监听所得数据，会跟当前语言修正笔记本名称
     */
    internal fun findUncategorizedFolder(): Folder {
        verifySourceIsAvailable()
        val inMemory = foldersAndRichNoteCounts.value?.find { FolderFactory.isUncategorizedFolder(it) }
        return FolderFactory.regenerateUncategorizedFolder(getApplication(), inMemory)
    }

    override fun findDefaultEncryptFolder(): Folder {
        verifySourceIsAvailable()
        val inMemory = foldersAndRichNoteCounts.value?.find { FolderFactory.isDefaultEncryptedFolder(it) }
        return FolderFactory.regenerateDefaultEncryptFolder(getApplication(), inMemory)
    }

    /**
     * 查询内置最近删除，依赖[observeNotebooks]监听所得数据，会跟当前语言修正笔记本名称
     */
    internal fun findRecentDeleteFolder(): Folder {
        verifySourceIsAvailable()
        val inMemory = foldersAndRichNoteCounts.value?.find { FolderFactory.isRecentDeleteFolder(it) }
        return FolderFactory.regenerateRecentDeleteFolder(getApplication(), inMemory)
    }

    @Suppress("NullSafeMutableLiveData")
    override fun updateCurrentFolder(folder: Folder?) {
        val curFolder = currentFolder.value
        if (folder != null && folder.guid != curFolder?.guid) {
            currentFolder.value = folder

            if (!folder.isEncrypted && !FolderFactory.isRecentDeleteFolder(folder)) {
                viewModelScope.launch { folderCacheManager.update(getApplication(), folder.name, folder.guid) }
            }
        }
    }

    @Suppress("NullSafeMutableLiveData")
    override fun updateRecentDeleteFolderCurrentFolder(folder: Folder?) {
            currentFolder.postValue(folder)
    }

    override fun showNotebookList(@IdRes containerId: Int, fm: FragmentManager, stackName: String?) {
        StatisticsNotebook.setEventOpenNotebook(getApplication())
        NotebookFragment.show(containerId, fm, stackName)
    }

    private var chooseNotebookCallbacks = hashMapOf<String, (ChosenFolderInfo) -> Unit>()

    internal fun getChooseNotebookCallback(tag: String): ((ChosenFolderInfo) -> Unit)? {
       return chooseNotebookCallbacks[tag]
    }

    override fun registerChooseNotebookListener(tag: String, cb: (ChosenFolderInfo) -> Unit) {
        kotlin.runCatching {
            chooseNotebookCallbacks.put(tag, cb)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "registerChooseNotebookListener error. ${it.message}")
        }
    }

    override fun unregisterChooseNotebookListener(tag: String, cb: (ChosenFolderInfo) -> Unit) {
        kotlin.runCatching {
            if (chooseNotebookCallbacks[tag] == cb) {
                chooseNotebookCallbacks.remove(tag)
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "unregisterChooseNotebookListener error. ${it.message}")
        }
    }

    override fun chooseNotebook(fm: FragmentManager, select: Folder?, encrypt: Boolean, isFromDetail: Boolean, chooseTag: String) {
        if (fm.isDestroyed || fm.isStateSaved) return
        runCatching {
            NotebookChooseFragment.show(fm, select, encrypt, isFromDetail, chooseTag)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "chooseNotebook error. ${it.message}")
        }
    }

    override fun observeCurrentNotebook(owner: LifecycleOwner, observer: Observer<Folder>) {
        currentFolder.observe(owner, observer)
    }

    override fun getCurrentNotebook(): Folder? {
        // 从列表重新获取最新对象
        return getNotebooks().find { currentFolder.value?.guid == it.guid }
    }

    override fun getNotebooks(): List<Folder> {
        return foldersAndRichNoteCounts.value ?: emptyList()
    }

    override fun getNotebookOrDefault(folderId: String?): Folder {
        val find = foldersAndRichNoteCounts.value?.find { it.guid == folderId }
        if (find != null) {
            return find
        }
        return findUncategorizedFolder()
    }

    override fun observeNotebooks(owner: LifecycleOwner, observer: Observer<List<Folder>>) {
        val embedFolderNameTransformer: (List<Folder>) -> List<Folder> = { folders ->
            val result = mutableListOf<Folder>()

            result.add(FolderFactory.regenerateAllNotesFolder(getApplication(), folders.find { FolderFactory.isAllNotesFolder(it) }))
            result.addAll(folders.filter { folder -> !FolderFactory.isEmbedFolder(folder) })
            folders.forEach { folder ->
                FolderFactory.updateFolderName(getApplication(), folder)
            }
            result.add(FolderFactory.regenerateUncategorizedFolder(getApplication(), folders.find { FolderFactory.isUncategorizedFolder(it) }))
            result.add(FolderFactory.regenerateDefaultEncryptFolder(getApplication(), folders.find { FolderFactory.isDefaultEncryptedFolder(it) }))
            result.add(FolderFactory.regenerateRecentDeleteFolder(getApplication(), folders.find { FolderFactory.isRecentDeleteFolder(it) }))

            result
        }

        foldersAndRichNoteCounts.distinctUntilChanged().map(embedFolderNameTransformer).observe(owner, observer)
    }

    override fun shouldShowChooseEncryptedFolderPanel(): Boolean {
        val result = foldersInDb?.value?.filter {
            it.isEncrypted && !FolderFactory.isSummaryFolder(it) && !FolderFactory.isDefaultEncryptedFolder(it)
        } ?: return false
        return result.isNotEmpty()
    }
}

