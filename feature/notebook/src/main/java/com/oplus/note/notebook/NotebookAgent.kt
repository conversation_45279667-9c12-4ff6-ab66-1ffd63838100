/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NoteBookAgent.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/05/30
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook

import androidx.annotation.IdRes
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory

/**
 * 生命周期同ViewModel
 */
interface NotebookAgent {

    var isCloudEnable: () -> Boolean

    /**
     * 打开所有笔记本列表界面，若想监听当前笔记本变化，可使用[observeCurrentNotebook]
     *
     * @param stackName see [androidx.fragment.app.FragmentTransaction.addToBackStack]
     */
    fun showNotebookList(@IdRes containerId: Int, fm: FragmentManager, stackName: String?)

    /**
     * 打开选择笔记本面板，若想监听选择结果，可使用[registerChooseNotebookListener]
     *
     * @param select 当前选中笔记本
     * @param encrypt 是否选择加密笔记本
     * @param isFromDetail 是否由详情页触发
     * @param chooseTag 选择结果监听器tag，用于匹配通过[registerChooseNotebookListener]方法注册的具体监听器对象
     */
    fun chooseNotebook(fm: FragmentManager, select: Folder?, encrypt: Boolean, isFromDetail: Boolean, chooseTag: String)

    /**
     * 监听当前选中笔记本变化。
     *
     * @see [showNotebookList]
     */
    fun observeCurrentNotebook(owner: LifecycleOwner, observer: Observer<Folder>)

    /**
     * 获取首页列表页的当前笔记本
     */
    fun getCurrentNotebook(): Folder?

    /**
     * 更新首页列表页的当前笔记本
     */
    fun updateCurrentFolder(folder: Folder?)

    /**
     * 更新当前笔记本为 “最近删除”
     */
    fun updateRecentDeleteFolderCurrentFolder(folder: Folder?)

    /**
     * 获取当前笔记列表，数据同[observeNotebooks]中监听返回数据
     */
    fun getNotebooks(): List<Folder>

    /**
     * 获取笔记本，依赖[observeNotebooks]中监听返回数据。
     *
     * @param folderId null时会返回[FolderFactory.FOLDER_GUID_UNCATEGORIZED]笔记本
     */
    fun getNotebookOrDefault(folderId: String?): Folder

    /**
     * 注册笔记本选择监听
     *
     * @see [chooseNotebook]
     */
    fun registerChooseNotebookListener(tag: String, cb: (ChosenFolderInfo) -> Unit)

    /**
     * 取消注册笔记本选择监听。
     *
     * @see [chooseNotebook]
     */
    fun unregisterChooseNotebookListener(tag: String, cb: (ChosenFolderInfo) -> Unit)

    /**
     * 监听所有笔记本变化
     */
    fun observeNotebooks(owner: LifecycleOwner, observer: Observer<List<Folder>>)

    /**
     * 选择加密笔记本时，是否需要显示笔记本列表弹窗
     */
    fun shouldShowChooseEncryptedFolderPanel(): Boolean

    /**
     * 查询对应笔记本下笔记数量，默认返回0。
     * 依赖[observeNotebooks]监听所得数据
     */
    fun findRichNoteCountInFolder(folder: Folder?): Int

    /**
     * 查询最近删除对应笔记本下笔记数量，默认返回0。
     * 依赖[observeNotebooks]监听所得数据
     */
    fun findRecentFolderRichNoteCountInFolder(): Int

    /**
     * 查询全部笔记，依赖[observeNotebooks]监听所得数据，会跟当前语言修正笔记本名称
     */
    fun findAllNoteFolder(): Folder

    /**
     * 查询内置私密笔记本，依赖[observeNotebooks]监听所得数据，会跟当前语言修正笔记本名称
     */
    fun findDefaultEncryptFolder(): Folder
}