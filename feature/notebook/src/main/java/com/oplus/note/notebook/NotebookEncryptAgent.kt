/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - EncryptedHelper.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023/10/27
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                        2023/10/27         1.0    Create this module
 **********************************************************************************/
package com.oplus.note.notebook

import android.app.Activity
import android.os.Build
import android.view.WindowManager.LayoutParams.FLAG_SECURE
import androidx.activity.result.ActivityResultCaller
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.cardlist.COUICardListHelper
import com.nearme.note.encrypt.EncryptedActivityResultProcessor
import com.oplus.note.notebook.internal.NotebookItem
import com.oplus.note.notebook.internal.util.NotebookItemManipulator
import com.oplus.note.repo.note.entity.Folder

class NotebookEncryptAgent<T>(host: T) where T : LifecycleOwner, T : ActivityResultCaller {

    companion object {
        private const val TAG = "NotebookEncryptAgent"
    }

    private val nim = NotebookItemManipulator(host)
    private val processor = EncryptedActivityResultProcessor(host)

    fun encryptOrDecrypt(folder: Folder, callback: ((Boolean) -> Unit)? = null, recentDeleteEncryptCallback: (Boolean) -> Unit) {
        val select = NotebookItem(
            folder = folder,
            noteCount = 0,
            encrypted = folder.isEncrypted,
            cardType = COUICardListHelper.NONE,
            selected = true,
            checked = false,
            enableInChecked = false,
            viewType = NotebookItem.VIEW_TYPE_NORMAL
        )
        val items = listOf(
            NotebookItem(
                folder = folder,
                noteCount = 0,
                encrypted = folder.isEncrypted,
                cardType = COUICardListHelper.NONE,
                selected = false,
                checked = false,
                enableInChecked = false,
                viewType = NotebookItem.VIEW_TYPE_NORMAL
            )
        )

        nim.encryptOrDecrypt(select, items, callback, recentDeleteEncryptCallback)
    }

    /**
     * 校验隐私密码
     */
    fun check(callback: (Boolean) -> Unit) {
        processor.setEncryptCallback {
            callback.invoke(it)
        }
        processor.startEncrypt()
    }
}

fun Activity?.setRecentScreenshotEnabled(enable: Boolean) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        this?.setRecentsScreenshotEnabled(enable)
    } else {
        if (enable) {
            this?.window?.clearFlags(FLAG_SECURE)
        } else {
            this?.window?.addFlags(FLAG_SECURE)
        }
    }
}