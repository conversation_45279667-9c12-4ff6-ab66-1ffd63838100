/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: LocalEditingFolderChannel.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/08/09
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal.util

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oplus.note.repo.note.entity.Folder

/**
 * 笔记本编辑页更新笔记本，通知笔记本列表页更新
 */
internal object LocalEditingFolderChannel {

    private const val ACTION = "com.oplus.note.notebook.ACTION_EDITING_FOLDER_UPDATE"
    private const val EXTRA_FOLDER = "EXTRA_FOLDER"

    fun notifyEditingFolderChanged(context: Context?, folder: Folder) {
        if (context != null) {
            LocalBroadcastManager.getInstance(context).sendBroadcast(Intent(ACTION).apply {
                putExtra(EXTRA_FOLDER, folder)
            })
        }
    }

    fun register(context: Context?, receiver: BroadcastReceiver) {
        if (context != null) {
            LocalBroadcastManager.getInstance(context).registerReceiver(receiver, IntentFilter(ACTION))
        }
    }

    fun unregister(context: Context?, receiver: BroadcastReceiver) {
        if (context != null) {
            LocalBroadcastManager.getInstance(context).unregisterReceiver(receiver)
        }
    }

    fun getEditingFolder(intent: Intent?): Folder? {
        return intent?.getParcelableExtra(EXTRA_FOLDER)
    }
}