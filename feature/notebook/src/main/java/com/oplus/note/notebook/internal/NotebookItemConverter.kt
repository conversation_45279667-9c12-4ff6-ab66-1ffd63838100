/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NotebookItemCoverter.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/17
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.content.Context
import com.coui.appcompat.cardlist.COUICardListHelper
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory

internal object NotebookItemConverter {

    fun createEncryptChosenTransformer(context: Context?, vm: NotebookVM, curFolder: Folder?): (List<Folder>) -> List<NotebookItem> {
        if (context == null) {
            return { emptyList() }
        }

        return { folders ->
            val result = mutableListOf<NotebookItem>()

            result.add(convertInternal(vm.findDefaultEncryptFolder(), COUICardListHelper.FULL, vm, curFolder))
            fillCustomFolders(folders, result, false, vm = vm, curFolder = curFolder, filter = {
                FolderFactory.isEmbedFolder(it) || FolderFactory.isSummaryFolder(it) || FolderFactory.isCollectionFolder(it) || !it.isEncrypted
            })

            result
        }
    }

    fun createChosenTransformer(context: Context?, vm: NotebookVM, curFolder: Folder?): (List<Folder>) -> List<NotebookItem> {
        if (context == null) {
            return { emptyList() }
        }

        return { folders ->
            val result = mutableListOf<NotebookItem>()

            result.add(convertInternal(vm.findUncategorizedFolder(), COUICardListHelper.FULL, vm, curFolder))
            result.add(createTitleGroupNotebookItem())

            fillCustomFolders(folders, result, vm = vm, curFolder = curFolder, filter = {
                FolderFactory.isEmbedFolder(it) || FolderFactory.isSummaryFolder(it) || FolderFactory.isCollectionFolder(it) || it.isEncrypted
                        || FolderFactory.isPaintFolder(it)
            })

            result
        }
    }

    fun createTransformer(context: Context?, vm: NotebookVM): (List<Folder>) -> List<NotebookItem> {
        if (context == null) {
            return { emptyList() }
        }

        return { folders ->
            val result = mutableListOf<NotebookItem>()

            result.add(convertInternal(vm.findAllNoteFolder(), COUICardListHelper.FULL, vm))
            result.add(createTitleGroupNotebookItem())

            fillCustomFolders(folders, result, needNone = false, vm = vm, filter = { FolderFactory.isEmbedFolder(it) })

            result.add(convertInternal(vm.findUncategorizedFolder(), COUICardListHelper.HEAD, vm))
            result.add(convertInternal(vm.findDefaultEncryptFolder(), COUICardListHelper.MIDDLE, vm))
            result.add(convertInternal(vm.findRecentDeleteFolder(), COUICardListHelper.TAIL, vm))

            result
        }
    }

    private fun createTitleGroupNotebookItem(): NotebookItem {
        return NotebookItem(
            folder = null,
            noteCount = 0,
            encrypted = false,
            cardType = COUICardListHelper.NONE,
            selected = false,
            checked = false,
            enableInChecked = false,
            viewType = NotebookItem.VIEW_TYPE_GROUP_TITLE
        )
    }

    private fun createNoneNotebookItem(): NotebookItem {
        return NotebookItem(
            folder = null,
            noteCount = 0,
            encrypted = false,
            cardType = COUICardListHelper.FULL,
            selected = false,
            checked = false,
            enableInChecked = false,
            viewType = NotebookItem.VIEW_TYPE_NONE
        )
    }

    private fun fillCustomFolders(
        folders: List<Folder>,
        result: MutableList<NotebookItem>,
        needNone: Boolean = true,
        vm: NotebookVM? = null,
        curFolder: Folder? = null,
        filter: (Folder) -> Boolean
    ) {
        var hasCustomFolders = false
        val first = folders.firstOrNull { !filter.invoke(it) }
        val last = folders.lastOrNull { !filter.invoke(it) }
        if (first != null && first == last) {
            hasCustomFolders = true
            result.add(convertInternal(first, COUICardListHelper.FULL, vm, curFolder))
        } else {
            folders.forEach { folder ->
                if (filter.invoke(folder)) {
                    return@forEach
                }
                hasCustomFolders = true
                val cardType = when (folder) {
                    first -> COUICardListHelper.HEAD
                    last -> COUICardListHelper.TAIL
                    else -> COUICardListHelper.MIDDLE
                }
                result.add(convertInternal(folder, cardType, vm, curFolder))
            }
        }
        if (!hasCustomFolders && needNone) {
            result.add(createNoneNotebookItem())
        }
    }

    private fun convertInternal(folder: Folder, cardType: Int, vm: NotebookVM? = null, curFolder: Folder? = null): NotebookItem {
        val realCurFolder: Folder? = curFolder ?: vm?.currentFolder?.value
        val count = vm?.findRichNoteCountInFolder(folder) ?: 0
        val isSelected = folder.guid == realCurFolder?.guid

        return NotebookItem(
            folder = folder,
            noteCount = count,
            encrypted = folder.isEncrypted,
            cardType = cardType,
            selected = isSelected,
            checked = false,
            enableInChecked = !FolderFactory.isUnEncryptFolder(folder),
            viewType = NotebookItem.VIEW_TYPE_NORMAL
        )
    }
}