/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/05/30
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.view.updateMargins
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.state.COUIStateEffectDrawable
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.nearme.note.util.ResourceUtils
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.R
import com.oplus.note.notebook.internal.util.NotebookDraggableViewHolder
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import java.util.Collections
import kotlin.math.max
import kotlin.math.min

internal class NotebookRecyclerAdapter(
    context: Context,
    private val onSelectedChangedListener: ((NotebookItem?, () -> Unit) -> Unit)? = null,
    private val onCheckedChangedListener: (() -> Unit)? = null,
    private val onItemMoveListener: ((Boolean) -> Unit)? = null,
    private val onCreateNewFolderClickListener: View.OnClickListener? = null,
    private val onDragResultListener: ((List<NotebookItem>) -> Unit)? = null,
    private val onItemLongClickListener: (() -> Unit)? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TAG = "NotebookRecyclerAdapter"
    }

    private val groupGap: Int = context.resources.getDimensionPixelSize(com.oplus.note.baseres.R.dimen.dp_16)
    private var preSelectedPosition: Int = RecyclerView.NO_POSITION
    private var _isCheckableState = false

    private var itemTouchHelper: ItemTouchHelper? = null

    private val values = mutableListOf<NotebookItem>()

    fun refresh(items: List<NotebookItem>) {
        values.clear()
        values.addAll(items)
        preSelectedPosition = items.indexOfFirst { it.selected }

        notifyDataSetChanged()
    }

    fun update(folder: Folder) {
        val find = values.find { it.folder?.guid == folder.guid }
        find?.folder?.name = folder.name
        find?.folder?.extra?.setPureCover(folder.extra.getPureCover())

        notifyDataSetChanged()
    }

    fun getCurrentValues(): List<NotebookItem> {
        return values
    }

    fun setItemTouchHelper(itemTouchHelper: ItemTouchHelper) {
        this.itemTouchHelper = itemTouchHelper
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            NotebookItem.VIEW_TYPE_NORMAL -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.notebook_fragment_note_book_item_normal, parent, false)
                return NotebookViewHolder(view)
            }

            NotebookItem.VIEW_TYPE_GROUP_TITLE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.notebook_fragment_note_book_item_group_title, parent, false)
                return NotebookGroupTitleViewHolder(view)
            }

            NotebookItem.VIEW_TYPE_NONE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.notebook_fragment_note_book_item_none, parent, false)
                return NotebookNoneViewHolder(view)
            }

            else -> throw IllegalArgumentException("Unsupported view type. $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is NotebookViewHolder) {
            holder.bind(values[position])
        } else if (holder is NotebookGroupTitleViewHolder) {
            holder.bind()
        }

        if (holder is NotebookDraggableViewHolder) {
            holder.onBindViewHolder(itemTouchHelper)
        }
    }

    override fun getItemCount(): Int = values.size

    override fun getItemViewType(position: Int): Int {
        return values[position].viewType
    }

    fun toggleCheckableState() {
        _isCheckableState = !_isCheckableState

        if (_isCheckableState) {
            values.forEach { it.checked = false }
        }

        notifyDataSetChanged()
    }

    fun isCheckableState(): Boolean {
        return _isCheckableState
    }

    private fun updateSelectedDataAndRefreshUI(curPosition: Int) {
        val prePosition = preSelectedPosition

        if (curPosition == prePosition) {
            onSelectedChangedListener?.invoke(null) { }
            return
        }

        onSelectedChangedListener?.invoke(values.getOrNull(curPosition)) {
            notifyItemChanged(curPosition)
            notifyItemChanged(prePosition)

            preSelectedPosition = curPosition
        }
    }

    private fun updateCheckedDataAndRefreshUI(curPosition: Int) {
        val data = values.getOrNull(curPosition) ?: return
        if (FolderFactory.isUnEncryptFolder(data.folder)) {
            return
        }

        data.checked = !data.checked

        onCheckedChangedListener?.invoke()

        notifyItemChanged(curPosition)
    }

    fun onItemMove(source: RecyclerView.ViewHolder, target: RecyclerView.ViewHolder) {
        val sourcePosition = source.bindingAdapterPosition
        val targetPosition = target.bindingAdapterPosition
        movePosition = targetPosition

        val sourceItem = values[sourcePosition]
        val targetItem = values[targetPosition]
        val tempSortTime = sourceItem.folder?.extra?.getSortTime()
        val tempCardType = sourceItem.cardType
        sourceItem.folder?.extra?.setSortTime(targetItem.folder?.extra?.getSortTime())
        sourceItem.cardType = targetItem.cardType
        targetItem.folder?.extra?.setSortTime(tempSortTime)
        targetItem.cardType = tempCardType
        onBindViewHolder(source, sourcePosition)
        onBindViewHolder(target, targetPosition)
        Collections.swap(values, sourcePosition, targetPosition)
        notifyItemMoved(sourcePosition, targetPosition)
    }

    private var dragPosition:Int? = null
    private var movePosition:Int? = null
    private var dropPosition:Int? = null

    private fun notifyNotebookDragStart(dragPosition: Int) {
        this.dragPosition = dragPosition
        this.movePosition = dragPosition
    }

    private fun notifyNotebookDragEnd(dropPosition: Int) {
        this.dropPosition = dropPosition

        val drag = this.dragPosition
        val drop = this.dropPosition
        if (drag != null && drop != null && drag != drop) {
            val items = mutableListOf<NotebookItem>()
            (min(drag, drop)..max(drag, drop)).forEach {
                if (it in 0 until values.size) {
                    items.add(values[it])
                }
            }
            onDragResultListener?.invoke(items)
        }

        this.dragPosition = null
        this.movePosition = null
        this.dropPosition = null
    }

    /**
     * 如果是选择笔记本弹窗样式时，不存在多选状态，所以onCheckedChangedListener应该为空
     */
    private fun isChooseMode() : Boolean {
        return onCheckedChangedListener == null
    }

    inner class NotebookViewHolder(itemView: View) : NotebookDraggableViewHolder(itemView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {

        private val ivNotebookIcon: ImageView = itemView.findViewById(R.id.notebook_item_icon)
        private val tvNotebookName: TextView = itemView.findViewById(R.id.notebook_item_name)
        private val tvNotebookSize: TextView = itemView.findViewById(R.id.notebook_item_count)
        private val cbNotebookCheck: CheckBox = itemView.findViewById(R.id.notebook_item_select)
        private val ivNotebookEncrypt: ImageView = itemView.findViewById(R.id.notebook_item_encrypt)
        private val ivNotebookNext: ImageView = itemView.findViewById(R.id.notebook_item_next)
        private val flEnd: View = itemView.findViewById(R.id.notebook_item_end)

        private val ivNotebookDragView = itemView.findViewById<ImageView>(R.id.notebook_item_drag)

        init {
            itemView.setOnClickListener {
                if (isCheckableState()) {
                    updateCheckedDataAndRefreshUI(bindingAdapterPosition)
                } else {
                    updateSelectedDataAndRefreshUI(bindingAdapterPosition)
                }
            }
            setItemLongClickListener()
        }

        override fun onItemSelected() {
            super.onItemSelected()
            onItemMoveListener?.invoke(false)
            notifyNotebookDragStart(bindingAdapterPosition)
        }

        override fun onItemDrop() {
            super.onItemDrop()
            onItemMoveListener?.invoke(true)
            notifyNotebookDragEnd(bindingAdapterPosition)
        }

        override fun getDragView(): View? {
            return ivNotebookDragView
        }

        override fun isDraggable(): Boolean {
            val item = values.getOrNull(bindingAdapterPosition)?.folder
            if (FolderFactory.isEmbedFolder(item)) {
                return false
            }
            return super.isDraggable()
        }

        fun bind(data: NotebookItem) {
            refreshBackground(data)

            refreshNotebookIcon(data)
            refreshNotebookName(data)
            refreshNotebookCount(data)
            refreshNotebookEncrypt(data)
            refreshNotebookCheck(data)
            refreshNotebookSelect(data)
            refreshDragView(data)
            refreshNotebookNext()
        }

        private fun setItemLongClickListener() {
            if (onItemLongClickListener != null) {
                itemView.setOnLongClickListener {
                    if (!isCheckableState()) {
                        AppLogger.BASIC.d(TAG, "enter checkable state: $this")
                        // 提前获取position信息，避免列表更新中位置信息获取不对
                        val position = bindingAdapterPosition
                        // 禁止当前长按item复用，避免列表刷新时复用至其它位置，显示异常按压效果
                        setIsRecyclable(false)
                        onItemLongClickListener.invoke()
                        updateCheckedDataAndRefreshUI(position)
                    } else {
                        if (getDragView()?.visibility == View.VISIBLE) {
                            itemTouchHelper?.startDrag(this)
                        }
                    }
                    return@setOnLongClickListener true
                }
            }
        }

        private fun refreshBackground(data: NotebookItem) {
            val root = itemView
            val cardType = data.cardType
            COUICardListHelper.setItemCardBackground(root, cardType)
            if (cardType == COUICardListHelper.TAIL || cardType == COUICardListHelper.FULL) {
                val params = root.layoutParams as? RecyclerView.LayoutParams
                params?.updateMargins(bottom = groupGap)
            } else {
                val params = root.layoutParams as? RecyclerView.LayoutParams
                params?.updateMargins(bottom = 0)
            }

            root.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshNotebookIcon(data: NotebookItem) {
            if (FolderFactory.isAllNotesFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.notebook_cover_all_notes)
            } else if (FolderFactory.isUncategorizedFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.notebook_cover_uncategorized)
            } else if (FolderFactory.isDefaultEncryptedFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.notebook_cover_default_encrypted)
            } else if (FolderFactory.isRecentDeleteFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.notebook_cover_recent_delete)
            } else if (FolderFactory.isQuickNoteFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.img_cover_yellow)
            } else if (FolderFactory.isCollectionFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.img_cover_orange)
            } else if (FolderFactory.isSummaryFolder(data.folder)) {
                ivNotebookIcon.setImageResource(R.drawable.notebook_cover_summary)
            } else {
                val id = ResourceUtils.getResIdByResName(
                    itemView.context,
                    data.folder?.extra?.getRealCover() ?: NoteBookData.getDefaultPureCover()
                )
                ivNotebookIcon.setImageResource(if (id > 0) id else R.drawable.img_cover_yellow)
            }
            ivNotebookIcon.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshNotebookName(data: NotebookItem) {
            tvNotebookName.text = data.folder?.name
            tvNotebookName.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        @SuppressLint("SetTextI18n")
        private fun refreshNotebookCount(data: NotebookItem) {
            tvNotebookSize.text = "${data.noteCount}"
            tvNotebookSize.visibility = if (!data.encrypted) View.VISIBLE else View.GONE
            tvNotebookSize.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshNotebookEncrypt(data: NotebookItem) {
            ivNotebookEncrypt.visibility = if (data.encrypted) View.VISIBLE else View.GONE
            ivNotebookEncrypt.isEnabled = if (isCheckableState()) data.enableInChecked else true
        }

        private fun refreshNotebookCheck(data: NotebookItem) {
            cbNotebookCheck.visibility = if (isCheckableState() && data.enableInChecked) View.VISIBLE else View.GONE
            cbNotebookCheck.isChecked = data.checked
        }

        private fun refreshNotebookSelect(data: NotebookItem) {
            val root = itemView
            if (root is COUICardListSelectedItemLayout) {
                val background: Drawable = root.background
                if (background is COUIStateEffectDrawable) {
                    if (!isCheckableState() && data.selected) {
                        root.setIsSelected(true)
                    } else {
                        root.setIsSelected(false)
                    }
                }
            }
        }

        private fun refreshDragView(data: NotebookItem) {
            ivNotebookDragView.isVisible = isCheckableState() && !FolderFactory.isEmbedFolder(data.folder)
        }

        private fun refreshNotebookNext() {
            ivNotebookNext.visibility = if (isCheckableState() || isChooseMode()) View.GONE else View.VISIBLE
        }

        override fun drawDivider(): Boolean {
            if (bindingAdapterPosition == movePosition || bindingAdapterPosition == ((movePosition ?: 0) - 1)) {
                return false
            }

            if (itemView is COUICardListSelectedItemLayout) {
                val cardType = values.getOrNull(bindingAdapterPosition)?.cardType
                return cardType == COUICardListHelper.HEAD || cardType == COUICardListHelper.MIDDLE
            }
            return false
        }

        override fun getDividerStartAlignView(): View {
            return tvNotebookName
        }

        override fun getDividerEndAlignView(): View {
            return flEnd
        }
    }

    inner class NotebookGroupTitleViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {

        private val btnNew = itemView.findViewById<TextView>(R.id.notebook_item_new)

        init {
            COUITextViewCompatUtil.setPressRippleDrawable(btnNew)
            btnNew.setOnClickListener(onCreateNewFolderClickListener)
        }

        fun bind() {
            btnNew.visibility = if (isCheckableState()) View.INVISIBLE else View.VISIBLE
        }

        override fun drawDivider(): Boolean {
            return false
        }
    }

    inner class NotebookNoneViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {

        override fun drawDivider(): Boolean {
            return false
        }
    }
}