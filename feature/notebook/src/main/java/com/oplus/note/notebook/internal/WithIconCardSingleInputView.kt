/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: WithIconCardSingleInputView.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/03
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import com.coui.appcompat.edittext.COUICardSingleInputView
import com.coui.component.responsiveui.unit.dp

internal class WithIconCardSingleInputView : COUICardSingleInputView {

    private var imageView: ImageView? = null

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        val headContainer = findViewById<LinearLayout>(com.support.input.R.id.header_container)
        headContainer.visibility = VISIBLE
        imageView = ImageView(context)
        imageView?.scaleType = ImageView.ScaleType.FIT_XY
        val size = context?.let { 24.dp.toPixel(it).toInt() } ?: LayoutParams.WRAP_CONTENT
        headContainer.addView(imageView, LayoutParams(size, size))
    }

    fun setImgRes(imgRes: Int) {
        imageView?.setImageResource(imgRes)
    }
}
