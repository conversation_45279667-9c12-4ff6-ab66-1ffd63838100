/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/05/30
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import com.coui.appcompat.cardlist.COUICardListHelper
import com.oplus.note.notebook.internal.NotebookItem.Companion.VIEW_TYPE_GROUP_TITLE
import com.oplus.note.notebook.internal.NotebookItem.Companion.VIEW_TYPE_NONE
import com.oplus.note.notebook.internal.NotebookItem.Companion.VIEW_TYPE_NORMAL
import com.oplus.note.repo.note.entity.Folder

/**
 * @param cardType defined in [COUICardListHelper]
 * @param viewType one of [VIEW_TYPE_NORMAL], [VIEW_TYPE_GROUP_TITLE], [VIEW_TYPE_NONE]
 */
internal data class NotebookItem(
    val folder: Folder?,
    val noteCount: Int, // 笔记本中的笔记数量
    var encrypted: Boolean, // 笔记本是否加密
    var cardType: Int, // 分组卡片背景类型
    var selected: Boolean, // 是否选中，item背景
    var checked: Boolean, // 多选状态下是否选中，checkbox状态
    var enableInChecked: Boolean, // 多选状态是否可操作
    var viewType: Int = VIEW_TYPE_NORMAL, // 列表项类型
) {
    companion object {
        const val VIEW_TYPE_NORMAL = 0
        const val VIEW_TYPE_GROUP_TITLE = 1
        const val VIEW_TYPE_NONE = 2
    }
}