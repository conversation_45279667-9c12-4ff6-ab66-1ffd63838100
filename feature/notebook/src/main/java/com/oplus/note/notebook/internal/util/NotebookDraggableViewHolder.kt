/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NotebookDraggableViewHoler.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/07/17
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal.util

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import androidx.core.view.animation.PathInterpolatorCompat
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.oplus.note.notebook.R

abstract class NotebookDraggableViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    companion object {
        private const val MAX_ELEVATION = 16f
    }

    private var mSelectedAnimator: AnimatorSet? = null
    private var mDropAnimator: AnimatorSet? = null
    private val mScale: Float

    init {
        val outValue = TypedValue()
        itemView.resources.getValue(R.dimen.my_view_item_scale, outValue, true)
        mScale = outValue.float
        initAnimators()
    }

    private fun initAnimators() {
        initSelectAnimator()
        initDropAnimator()
    }

    private fun initSelectAnimator() {
        val elevationAnimation = ObjectAnimator.ofFloat(
            itemView,
            "elevation",
            0f,
            MAX_ELEVATION
        ).apply {
            duration = 300
            interpolator = PathInterpolatorCompat.create(0.4f, 0f, 0.2f, 1f)
        }

        val scaleXAnimator = ObjectAnimator.ofFloat(
            itemView,
            "scaleX",
            1f,
            mScale
        ).apply {
            duration = 300
            interpolator = PathInterpolatorCompat.create(0.15f, 0f, 0f, 1f)
        }

        val scaleYAnimator = ObjectAnimator.ofFloat(
            itemView,
            "scaleY",
            1f,
            mScale
        ).apply {
            duration = 300
            interpolator = PathInterpolatorCompat.create(0.15f, 0f, 0f, 1f)
        }

        mSelectedAnimator = AnimatorSet()
        mSelectedAnimator?.play(scaleXAnimator)?.with(scaleYAnimator)?.with(elevationAnimation)
    }

    private fun initDropAnimator() {
        val scaleXAnimator = ObjectAnimator.ofFloat(itemView, "scaleX", mScale, 1f)
        val scaleYAnimator = ObjectAnimator.ofFloat(itemView, "scaleY", mScale, 1f)
        val elevationAnimation = ObjectAnimator.ofFloat(itemView, "elevation", MAX_ELEVATION, 0f)
        mDropAnimator = AnimatorSet().apply {
            duration = 400
            interpolator = PathInterpolatorCompat.create(0.15f, 0f, 0f, 1f)
            play(scaleXAnimator)?.with(scaleYAnimator)?.with(elevationAnimation)
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    itemView.performHapticFeedback(
                        COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE,
                        0
                    )
                }
            })
        }
    }

    open fun onItemSelected() {
        if (mDropAnimator != null && mDropAnimator?.isRunning!!) {
            mDropAnimator?.end()
        }
        mSelectedAnimator?.start()
    }

    open fun onItemDrop() {
        if (mSelectedAnimator != null && mSelectedAnimator?.isRunning!!) {
            mSelectedAnimator?.end()
        }
        mDropAnimator?.start()
    }

    /**
     * 获取把手控件
     */
    abstract fun getDragView(): View?

    /**
     * 默认支持拖拽操作
     */
    open fun isDraggable(): Boolean = true

    fun onBindViewHolder(itemTouchHelper: ItemTouchHelper?) {
        if (isDraggable()) {
            getDragView()?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> itemTouchHelper?.startDrag(this)
                    else -> {
                    }
                }
                true
            }
        } else {
            getDragView()?.setOnTouchListener(null)
        }
    }
}