/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: NotebookSelectFragment.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/06/12
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.notebook.internal

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.map
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.oplus.note.notebook.ChosenFolderInfo
import com.oplus.note.notebook.R
import com.oplus.note.notebook.internal.util.NotebookItemManipulator
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.statistic.StatisticsNotebook

internal class NotebookChooseFragment : COUIPanelFragment() {

    companion object {

        private const val ARGUMENTS_ENCRYPT = "args_encrypt_key"
        private const val ARGUMENTS_SELECT = "args_select_key"
        private const val ARGUMENTS_IS_FROM_DETAIL = "args_is_from_detail"
        private const val ARGUMENTS_CHOOSE_TAG = "args_choose_tag"

        fun show(fm: FragmentManager, select: Folder?, encrypt: Boolean, isFromDetail: Boolean, chooseTag: String): COUIBottomSheetDialogFragment {
            val sdf = COUIBottomSheetDialogFragment()
            sdf.setMainPanelFragment(newInstance(select, encrypt, isFromDetail, chooseTag))
            sdf.setIsShowInMaxHeight(true)
            sdf.show(fm, NotebookChooseFragment::class.java.name)
            return sdf
        }

        private fun newInstance(select: Folder?, encrypt: Boolean, isFromDetail: Boolean, chooseTag: String): NotebookChooseFragment {
            val fragment = NotebookChooseFragment()
            val args = Bundle()
            args.putBoolean(ARGUMENTS_ENCRYPT, encrypt)
            args.putBoolean(ARGUMENTS_IS_FROM_DETAIL, isFromDetail)
            args.putString(ARGUMENTS_CHOOSE_TAG, chooseTag)
            if (select != null) {
                args.putParcelable(ARGUMENTS_SELECT, select)
            }
            fragment.arguments = args
            return fragment
        }
    }

    private var adapter: NotebookRecyclerAdapter? = null
    private var singleButtonWrap: SingleButtonWrap? = null

    private val nim = NotebookItemManipulator(this)
    private val notebookVM by viewModels<NotebookVM>({ requireActivity() })

    private var chooseTag: String = ""
    private var isFromDetail: Boolean = false
    private var encrypt: Boolean = false // 选择面板是否为加密模式，加密模式下仅显示加密笔记本
    private var select: Folder? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        chooseTag = arguments?.getString(ARGUMENTS_CHOOSE_TAG, "") ?: ""
        isFromDetail = arguments?.getBoolean(ARGUMENTS_IS_FROM_DETAIL, false) ?: false
    }

    override fun initView(panelView: View?) {
        super.initView(panelView)
        val bgColor =
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard)
        panelView?.setBackgroundColor(bgColor)
        LayoutInflater.from(context)
            .inflate(R.layout.notebook_fragment_note_book_choose, contentView as? ViewGroup, true)

        initToolbar()
        initRecyclerViewAndDividerLine(panelView)
        initSingleButton(panelView)
    }

    private fun initSingleButton(root: View?) {
        if (root == null) {
            return
        }
        val saveBtn = root.findViewById<COUIButton>(R.id.notebook_choose_save)
        saveBtn.setOnClickListener {
            val item = adapter?.getCurrentValues()?.find { it.selected }
            updateChosenNotebook(item?.folder)
            triggerCancel()
        }
        singleButtonWrap = SingleButtonWrap(saveBtn, SingleButtonWrap.Type.Large)
    }

    private fun initToolbar() {
        toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(com.oplus.note.baseres.R.string.note_encrypt_to_folder)
            isTitleCenterStyle = true
            inflateMenu(R.menu.notebook_menu_edit)
            menu.findItem(R.id.notebook_menu_edit_save).apply {
                title = ""
                isEnabled = false
                icon = null
            }
            menu.findItem(R.id.notebook_menu_edit_cancel).setOnMenuItemClickListener {
                triggerCancel()
                true
            }
        }
    }

    private fun updateChosenNotebook(newFolder: Folder?) {
        if (newFolder != null && newFolder.guid != select?.guid) {
            val info = ChosenFolderInfo(select, newFolder, encrypt)
            notebookVM.getChooseNotebookCallback(chooseTag)?.invoke(info)
        }
    }

    private fun triggerCancel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun initRecyclerViewAndDividerLine(root: View?) {
        if (root == null) {
            return
        }
        val divider: View = root.findViewById(R.id.notebook_choose_divider)
        val ncl: RecyclerView = root.findViewById(R.id.notebook_choose_list)
        ncl.layoutManager = object : LinearLayoutManager(context) {
            override fun onLayoutCompleted(state: RecyclerView.State?) {
                super.onLayoutCompleted(state)
                // check if the divider should be visible
                val last = findLastCompletelyVisibleItemPosition()
                val first = findFirstCompletelyVisibleItemPosition()
                val size = ncl.adapter?.itemCount ?: -1
                divider.alpha = if (last < size - 1 || first > 0) 1f else 0f
            }
        }
        adapter = NotebookRecyclerAdapter(
            root.context,
            onSelectedChangedListener = { target, adapterSelectedCallback ->
                triggerSelectNotebook(target, adapterSelectedCallback)
            },
            onCheckedChangedListener = null,
            onCreateNewFolderClickListener = {
                StatisticsNotebook.setEventNewNotebook(context)
                NotebookEditFragment.show(childFragmentManager, null, if (isFromDetail) 1 else 2)
            }
        )
        ncl.adapter = adapter
        ncl.addItemDecoration(COUIRecyclerView.COUIRecyclerViewItemDecoration(context))
    }

    private fun triggerSelectNotebook(target: NotebookItem?, adapterSelectedCallback: () -> Unit) {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val targets = target?.let { listOf(it) } ?: emptyList()
        if (targets.isEmpty()) {
            return
        }
        nim.select(selected, targets, encrypt) { success ->
            if (success) {
                selected?.selected = false
                adapterSelectedCallback.invoke()
            }
        }

        singleButtonWrap?.processView?.isEnabled = true
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initObservers()
    }

    @SuppressLint("NewApi")
    private fun initObservers() {
        encrypt = arguments?.getBoolean(ARGUMENTS_ENCRYPT, false) ?: false
        select = arguments?.getParcelable(ARGUMENTS_SELECT, Folder::class.java)

        val transformer = if (encrypt) {
            NotebookItemConverter.createEncryptChosenTransformer(context, notebookVM, select)
        } else {
            NotebookItemConverter.createChosenTransformer(context, notebookVM, select)
        }

        notebookVM.foldersAndRichNoteCounts.map(transformer).observe(viewLifecycleOwner) { items ->
            adapter?.refresh(items)
        }
    }


    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        singleButtonWrap?.onConfigurationChanged(newConfig)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        singleButtonWrap?.release()
    }
}