/****************************************************************
 * * Copyright (C), 2019-2027, OPlus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteSummaryUtils.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/11/1
 * * Author: niexiaokang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.util

import android.content.Context
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.utils.NoteStatusProviderUtil

fun isCollectionNotebook(folder: FolderInfo?): Boolean {
    return isCollectionNotebook(folder?.guid)
}
fun isCollectionNotebook(folderId: String?): Boolean {
    val id = folderId ?: ""
    return id == FolderInfo.FOLDER_GUID_COLLECTION
}
/**
 * 判断当前笔记本是否是智能摘要（通话摘要、语音摘要、文章摘要）
 */
fun isSummaryNotebook(folder: FolderInfo?): Boolean {
    return isSummaryNotebook(folder?.guid)
}

fun isSummaryNotebook(folderId: String?): Boolean {
    val id = folderId ?: ""
    return id == FolderInfo.FOLDER_GUID_CALL_SUMMARY
            || id == FolderInfo.FOLDER_GUID_AUDIO_SUMMARY
            || id == FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY
}

fun setCallSummaryEncryptedStatus(context: Context?, folderId: String?, encrypted: Int?) {
    val isEncrypt = encrypted == FolderInfo.FOLDER_ENCRYPTED
    if (context == null) {
        encryptLog(isEncrypt, "setCallSummaryEncryptedStatus failed context is null")
        return
    }
    if (folderId == FolderInfo.FOLDER_GUID_CALL_SUMMARY) {
        val success = NoteStatusProviderUtil.setStatus(context, NoteStatusProviderUtil.FLAG_CALL_SUMMARY_FOLDER_ENCRYPT_STATUS, isEncrypt)
        encryptLog(isEncrypt, "setCallSummaryEncryptedStatus success:$success")
    }
}

fun encryptLog(isEncrypt: Boolean, msg: String) {
    val start = if (isEncrypt) "51010101" else "51020101"
    AppLogger.THIRDLOG.d("NotebookEncrypt", "$start,$msg")
}