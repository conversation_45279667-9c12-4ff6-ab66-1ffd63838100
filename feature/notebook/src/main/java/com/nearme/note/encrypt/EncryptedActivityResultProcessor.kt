/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - EncryptedActivityResultProcessor.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023/10/19
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023/10/19         1.0    Create this module
 **********************************************************************************/
package com.nearme.note.encrypt

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResultCaller
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.nearme.note.util.privacyPasswordIntent
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class EncryptedActivityResultProcessor<T>(private val host: T)
    where T : LifecycleOwner, T : ActivityResultCaller {

    private var launcher: ActivityResultLauncher<Intent>? = null
    private var callback: ((Boolean) -> Unit)? = null

    init {
        host.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onCreate(owner: LifecycleOwner) {
                super.onCreate(owner)
                register()
            }

            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                unregister()
            }
        })
    }

    private fun register() {
        launcher = host.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            callback?.invoke(it.resultCode == Activity.RESULT_OK)
        }
    }

    private fun unregister() {
        launcher?.unregister()
        callback = null
    }

    fun startEncrypt() {
        kotlin.runCatching {
            val context = when (host) {
                is Fragment -> host.context
                is Activity -> host
                else -> null
            }
            if (context == null) {
                return
            }
            host.lifecycleScope.launch(Dispatchers.Default) {
                kotlin.runCatching {
                    launcher?.launch(privacyPasswordIntent(context))
                }.onFailure {
                    AppLogger.BASIC.e("EncryptedActivityResultProcessor", "startEncrypt error.", it)
                }
            }
        }.onFailure {
            AppLogger.BASIC.e("EncryptedActivityResultProcessor", "startEncrypt error.", it)
        }
    }

    fun setEncryptCallback(callback: (Boolean) -> Unit) {
        this.callback = callback
    }
}