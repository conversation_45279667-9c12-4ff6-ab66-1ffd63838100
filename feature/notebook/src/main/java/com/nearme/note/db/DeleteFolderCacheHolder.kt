/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - DeleteFolderCacheHolder.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023/10/30
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023/10/30         1.0    Create this module
 **********************************************************************************/
package com.nearme.note.db

import android.content.Context
import android.content.SharedPreferences
import com.oplus.note.notebook.guide.ENCRYPTED
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderInfo

/***
 * 已删除笔记本缓存格式为(folderGuid, isEncrypted)，folderGuid: 笔记本固定id，isEncrypted: 是否加密
 * 多个笔记本格式: (folderGuid1, true);(folderGuid2, false);(folderGuid3, true)
 */
object DeleteFolderCacheHolder {

    private const val DELETED_FOLDERS = "deleted_folders"
    private const val SPLIT_CHAR = ";"

    private const val PAIR_LEFT_BRACKET = "("
    private const val PAIR_RIGHT_BRACKET = ")"
    private const val PAIR_SPLIT_CHAR = ","
    private const val DEFAULT_VALUE = ""

    private var sp: SharedPreferences? = null

    private val deletedFoldersInfo: List<Pair<String, Boolean>>
        get() = getAllDeletedFoldersInfo()
    private val deletedFolderGuidsCache: String
        get() = sp?.getString(DELETED_FOLDERS, DEFAULT_VALUE) ?: DEFAULT_VALUE

    private fun createSpIfNeeded(context: Context?) {
        if (sp == null) {
            return
        }
        sp = context?.getSharedPreferences(DELETED_FOLDERS, Context.MODE_PRIVATE)
    }

    private fun getSp(context: Context?): SharedPreferences? {
        createSpIfNeeded(context)
        return sp
    }

    @JvmStatic
    fun updateDeletedFolders(context: Context?, folders: List<FolderInfo>) {
        val list = arrayListOf<Pair<String, Boolean>>()
        folders.forEach {
            list.add(it.guid to (it.encrypted == ENCRYPTED))
        }
        addDeletedFoldersInfo(context, list)
    }

    fun updateDeletedFoldersNew(context: Context?, folders: List<Folder>) {
        val list = arrayListOf<Pair<String, Boolean>>()
        folders.forEach {
            list.add(it.guid to (it.encrypted == ENCRYPTED))
        }
        addDeletedFoldersInfo(context, list)
    }

    fun isDeletedEncryptedFolder(guid: String): Boolean {
        return deletedFoldersInfo.any {
            it.first == guid && it.second
        }
    }

    private fun addDeletedFoldersInfo(context: Context?, info: List<Pair<String, Boolean>>) {
        val sb = StringBuilder(deletedFolderGuidsCache)
        info.forEach {
            sb.append(if (sb.isEmpty()) "" else SPLIT_CHAR).append(it)
        }
        getSp(context)?.putStringAsync(DELETED_FOLDERS, sb.toString())
    }

    private fun SharedPreferences.putStringAsync(key: String, value: String) {
        edit().putString(key, value).apply()
    }

    private fun getAllDeletedFoldersInfo(): List<Pair<String, Boolean>> {
        return arrayListOf<Pair<String, Boolean>>().also { list ->
            deletedFolderGuidsCache.split(SPLIT_CHAR).forEach {
                list.add(it.toPair())
            }
        }
    }

    @JvmStatic
    fun removeFolderCache(context: Context?, folderInfo: FolderInfo?) {
        if (folderInfo == null) return
        // 只处理通话摘要，文章摘要和速记三个特殊的笔记本，新建时清除SP加密缓存
        if (folderInfo.guid == FolderInfo.FOLDER_GUID_CALL_SUMMARY
            || folderInfo.guid == FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY
            || folderInfo.guid == FolderInfo.FOLDER_GUID_COLLECTION
            || folderInfo.guid == FolderInfo.FOLDER_GUID_AUDIO_SUMMARY
            || folderInfo.guid == FolderInfo.FOLDER_GUID_QUICK) {
            val list = deletedFoldersInfo.filter { it.first != folderInfo.guid }
            updateDeletedFoldersInfo(context, list)
        }
    }

    @JvmStatic
    fun removeFolderCacheRecovery(context: Context?, folder: Folder?) {
        if (folder == null) return
        if (folder.encrypted == FolderInfo.FOLDER_ENCRYPTED) {
            return
        }
        if (!deletedFoldersInfo.map { it.first }.contains(folder.guid)) {
            return
        }
        val list = deletedFoldersInfo.filter { it.first != folder.guid }
        updateDeletedFoldersInfo(context, list)
    }

    private fun updateDeletedFoldersInfo(context: Context?, info: List<Pair<String, Boolean>>) {
        getSp(context)?.putStringAsync(DELETED_FOLDERS, info.joinToString(SPLIT_CHAR))
    }

    /**
     * transform cache string (first, second) to Pair(first, second)
     */
    private fun String.toPair(): Pair<String, Boolean> {
        val list = this.removeSurrounding(PAIR_LEFT_BRACKET, PAIR_RIGHT_BRACKET).split(PAIR_SPLIT_CHAR)
        return Pair(list.firstOrNull() ?: "", list.lastOrNull()?.trim().toBoolean())
    }
}