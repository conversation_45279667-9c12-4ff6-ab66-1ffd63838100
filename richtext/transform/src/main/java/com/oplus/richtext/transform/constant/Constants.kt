package com.oplus.richtext.transform.constant

@Suppress("Unused")
object Constants {
    object Common {
        const val STRING_BLANK = ' '
        const val CHAR_DASHED_LINE = '-'
        const val CHAR_UNDER_LINE = '_'
    }

    internal enum class AlignGravity(val aliasName: String) {
        START("start"),
        CENTER("center"),
        END("end")
    }

    internal object HtmlTags {
        const val HTML = "html"
        const val BODY = "body"
        const val HEAD = "head"
        const val TITLE = "title"
        const val META = "meta"

        const val S = "s"
        const val B = "b"
        const val I = "i"
        const val P = "p"
        const val SPAN = "span"
        const val BR = "br"
        const val U = "u"
        const val A = "a"
        const val IMG = "img"
        const val OL = "ol"
        const val UL = "ul"
        const val LI = "li"
        const val DIV = "div"
        const val STYLE = "style"
        const val CLASS = "class"
        const val ID = "id"
        const val INPUT = "input"
        const val LABEL = "label"
        const val TYPE = "type"
        const val H1 = "h1"
        const val H2 = "h2"
        const val H3 = "h3"
        const val TABLE = "table"
        const val TBODY = "tbody"
        const val TD = "td"
        const val TR = "tr"
        const val HR = "hr"

        const val BACKGROUND_COLOR = "background-color"
        const val MEDIA_ATTACHMENT = "media-attachment"
        const val TEXT_ALIGN = "text-align"
        const val FONT_SIZE = "font-size"
        const val SUMMARY = "summary"
        const val QUOTE = "quote"
        const val COLOR = "color"
        const val QL_INDENT = "ql-indent-"
        const val HEADING_STYLE = "heading-style-"
        const val CONTENT_STYLE = "content-style-"
        const val QUOTE_STYLE = "quote-style-"
    }

    object HtmlAttributes {
        private const val HIGH_LIGHT_RGBA_PREFIX = "rgba"
        private const val HIGH_LIGHT_RGBA = "(255,240,73,0.50)"
        const val TEXT_SIZE_DEFAULT = "1.0"

        const val FONT_SIZE_PREFIX = "font-size:"
        const val FONT_SIZE_SUFFIX = "em"

        //"background-color:rgba(255,240,73,0.50)"
        const val STYLE_HIGH_LIGHT = "${HtmlTags.BACKGROUND_COLOR}:$HIGH_LIGHT_RGBA_PREFIX$HIGH_LIGHT_RGBA"

        //"background-color:rgba"
        const val STYLE_HIGH_LIGHT_PREFIX = "${HtmlTags.BACKGROUND_COLOR}:$HIGH_LIGHT_RGBA_PREFIX"

        //“color:”
        const val STYLE_COLOR_PREFIX = "${HtmlTags.COLOR}:var(--"
        const val STYLE_COLOR_ENDFIX = "Color)"
        const val CLASS_COLOR_PREFIX = "color_"
        const val DEFAULT_COLOR = "default"
        const val STYLE_COLOR_PREFIX_4HTML = "${HtmlTags.COLOR}:"

        const val TEXT_ALIGN_START = "text-align:start;"
        const val TEXT_ALIGN_CENTER = "text-align:center;"
        const val TEXT_ALIGN_END = "text-align:end;"

        const val CHECKBOX = "checkbox"
        const val CHECKBOX_GROUP = "checkbox-group"
        const val CHECKBOX_TEXT = "checkbox-text"

        const val HREF = "href"

        const val ATTR_SPLIT = ";"
    }

    internal object HtmlHeadTag {
        const val KEY_HTML_TYPE = "html_type"
        const val KEY_SELECT_META_HTML_TYPE = "meta[name=$KEY_HTML_TYPE]"

        const val KEY_HTML_VERSION = "html_version"
        const val KEY_SELECT_META_HTML_VERSION = "meta[name=$KEY_HTML_VERSION]"

        const val KEY_META_CONTENT = "content"
        const val KEY_META_NAME = "name"
        const val VALUE_HTML_VERSION = 1
    }

    internal object SimilarHtmlTag {
        const val TAG_ALIGN = "align"
        const val TAG_BOLD = "text-weight-bold"
        const val TAG_ITALIC = "text-italic"
        const val TAG_UNDERLINE = "text-decoration-underline"
        const val TAG_HIGHLIGHT = "text-highlight-active"
        const val TAG_TEXT_LINE_THROUGH = "text-line-through"

        const val TAG_TEXT_SIZE_PREFIX = "text-size-"

        const val TAG_ALIGN_START = "align-start"
        const val TAG_ALIGN_CENTER = "align-center"
        const val TAG_ALIGN_END = "align-end"

        const val TAG_CHECKED = "checked"
        const val TAG_UNCHECKED = "unchecked"
        const val TAG_TEXT_COLOR_PREFIX = "color_"
    }

    internal object ClassValues {
        //分隔线样式
        const val HR_STYLE_PREFIX = "hr-style-"
        const val HR_STYLE_SOLID = "solid"
        const val HR_STYLE_DASHED = "dashed"
        const val HR_STYLE_DOUBLE = "double"
        const val HR_STYLE_FREEHAND = "freehand"
    }
}