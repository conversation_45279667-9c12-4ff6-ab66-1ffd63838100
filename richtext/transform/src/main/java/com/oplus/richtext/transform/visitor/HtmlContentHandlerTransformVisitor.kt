/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - HtmlContentHandlerTransformVisitor.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/5/31
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>     <version>    <desc>
 **  ********      2022/8/16      1.0     create file
 ****************************************************************/

package com.oplus.richtext.transform.visitor

import org.jsoup.Jsoup
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode
import org.jsoup.select.NodeTraversor
import org.jsoup.select.NodeVisitor
import org.xml.sax.ContentHandler
import org.xml.sax.helpers.AttributesImpl

internal class HtmlContentHandlerTransformVisitor : NodeVisitor {
    private var _contentHandler: ContentHandler? = null
    private var _data = ""

    fun setContentHandler(contentHandler: ContentHandler) {
        _contentHandler = contentHandler
    }

    fun setDataSource(data: String) {
        _data = data
    }

    fun parse() {
        NodeTraversor.traverse(this, Jsoup.parse(_data))
    }

    override fun head(node: Node, depth: Int) {
        if (node !is TextNode) {
            val nodeName = node.nodeName()
            val attributesImpl = AttributesImpl()
            // jsoup的属性转换成Sax属性
            val attrs = node.attributes()
            attrs.forEach {
                attributesImpl.addAttribute("", it.key, it.key, "string", it.value)
            }
            _contentHandler?.startElement("", nodeName, "", attributesImpl)
        }
    }

    override fun tail(node: Node, depth: Int) {
        if (node !is TextNode) {
            _contentHandler?.endElement("", node.nodeName(), "")
        } else {
            val charArray = node.wholeText.toCharArray()
            _contentHandler?.characters(charArray, 0, charArray.size)
        }
    }
}