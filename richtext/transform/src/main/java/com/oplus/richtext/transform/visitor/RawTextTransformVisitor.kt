/****************************************************************
 ** Copyright (C), 2010-2023, Oplus Mobile Comm Corp., Ltd.
 ** File:  - NodeHeadInfo.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/12/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  lvhuachuan   2022/12/14     1.0            add file
 ****************************************************************/

package com.oplus.richtext.transform.visitor

import com.oplus.richtext.transform.trans.RawTagTransformManager
import org.jsoup.nodes.Element

internal class RawTextTransformVisitor : BaseTransformVisitor() {
    companion object {
        private const val TAG = "RawTextTransformVisitor"
    }

    override fun tag(): String {
        return TAG
    }

    override fun handleTransform(node: Element, targetIndex: Int) {
        RawTagTransformManager.transformToHtmlText(node, targetText, targetIndex)
    }
}