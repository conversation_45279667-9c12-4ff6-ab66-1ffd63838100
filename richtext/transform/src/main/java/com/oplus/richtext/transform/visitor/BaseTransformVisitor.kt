package com.oplus.richtext.transform.visitor

import com.oplus.note.logger.AppLogger
import org.jsoup.nodes.Element
import org.jsoup.nodes.Entities
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode
import org.jsoup.select.NodeVisitor
import java.util.Stack

internal abstract class BaseTransformVisitor : NodeVisitor {

    private val headInfoStack: Stack<Pair<Element, Int>> = Stack()
    protected val targetText = StringBuilder()

    /**
     * 获取子类的Log TAG
     */
    abstract fun tag(): String

    /**
     * 子类执行具体的转换逻辑
     */
    abstract fun handleTransform(node: Element, targetIndex: Int)

    override fun head(node: Node, depth: Int) {

        when (node) {
            is TextNode -> handleHeadTextNode()
            is Element -> handleHeadElement(node)
            else -> AppLogger.BASIC.d(tag(), "Un handle html head.")
        }
    }

    override fun tail(node: Node, depth: Int) {

        when (node) {
            is TextNode -> handleTailTextNode(node)
            is Element -> handleTailElement(node)
            else -> AppLogger.BASIC.d(tag(), "Un handle html tail.")
        }
    }

    fun result(): String {
        return targetText.toString()
    }

    private fun handleTailElement(node: Element) {

        var hIndex: Int? = null

        val nodeInfo = headInfoStack.peek()
        if (nodeInfo.first.tagName() == node.tagName()) {
            headInfoStack.pop()

            hIndex = nodeInfo.second
        }

        val index = fixInsertIndex(hIndex, targetText)
        handleTransform(node, index)
    }

    private fun handleHeadTextNode() {
    }

    private fun handleHeadElement(node: Element) {

        val htmlIndex = targetText.length
        headInfoStack.push(Pair(node, htmlIndex))
    }

    private fun handleTailTextNode(node: TextNode) {
        targetText.append(Entities.escape(node.wholeText))
    }

    private fun fixInsertIndex(index: Int?, targetText: StringBuilder): Int {
        return if (index == null || index > targetText.length) {
            AppLogger.BASIC.w(tag(), "Fix index from $index to ${targetText.length}.")
            targetText.length
        } else {
            index
        }
    }
}