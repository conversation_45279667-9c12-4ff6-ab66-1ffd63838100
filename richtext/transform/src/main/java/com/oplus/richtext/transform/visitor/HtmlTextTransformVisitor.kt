/****************************************************************
 ** Copyright (C), 2010-2023, Oplus Mobile Comm Corp., Ltd.
 ** File:  - NodeHeadInfo.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/12/14
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  lvhuachuan   2022/12/14     1.0            add file
 ****************************************************************/

package com.oplus.richtext.transform.visitor

import com.oplus.richtext.transform.trans.HtmlTagTransformManager
import org.jsoup.nodes.Element

internal class HtmlTextTransformVisitor : BaseTransformVisitor() {
    companion object {
        private const val TAG = "HtmlTextTransformVisitor"
    }

    override fun tag(): String {
        return TAG
    }

    override fun handleTransform(node: Element, targetIndex: Int) {
        HtmlTagTransformManager.transformToRawText(node, targetText, targetIndex)
    }
}