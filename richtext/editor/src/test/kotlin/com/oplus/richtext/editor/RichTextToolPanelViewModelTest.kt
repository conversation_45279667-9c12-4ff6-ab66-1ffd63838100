/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: RichTextToolPanelViewModelTest.kt
 * * Description: RichTextToolPanelViewModelTest
 * * Version: 1.0
 * * Date: 2025/04/21
 * * Author: hufeng
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
import android.content.Context
import android.view.View
import androidx.test.platform.app.InstrumentationRegistry
import com.oplus.richtext.editor.RichTextToolPanelViewModel
import com.oplus.richtext.editor.utils.RichTextToolsConstant
import com.oplus.richtext.editor.view.RichTextToolItemClickListener
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [30])
class RichTextToolPanelViewModelTest {

    private lateinit var viewModel: RichTextToolPanelViewModel
    private lateinit var mockContext: Context
    private lateinit var mockItemClickListener: RichTextToolItemClickListener

    @Before
    fun setUp() {
        mockContext = InstrumentationRegistry.getInstrumentation().targetContext
        mockItemClickListener = mock()
        viewModel = RichTextToolPanelViewModel().apply {
            itemClickListener = mockItemClickListener
        }
    }

    @Test
    fun testIsAnyListActive() {
        viewModel.isBulletListActivated.value = true
        assert(viewModel.isAnyListActive())

        viewModel.isBulletListActivated.value = false
        viewModel.isOrderedListActivated.value = true
        assert(viewModel.isAnyListActive())

        viewModel.isOrderedListActivated.value = false
        viewModel.isTodoListActivated.value = true
        assert(viewModel.isAnyListActive())

        viewModel.isTodoListActivated.value = false
        assert(!viewModel.isAnyListActive())
    }

    @Test
    fun testOnBlockQuoteItemClick() {
        val view = mock<View>()
        viewModel.onBlockQuoteItemClick(view)
        verify(mockItemClickListener).setBlockQuote(true)
    }

    @Test
    fun testOnHorizontalRuleClick() {
        val view = mock<View>()
        viewModel.onHorizontalRuleClick(view)
        verify(mockItemClickListener).addHorizontalRule()
    }

    @Test
    fun testOnBoldItemClick() {
        val view = mock<View>()
        viewModel.onBoldItemClick(view)
        verify(mockItemClickListener).setBold(true)
        assert(viewModel.isBoldActivated.value == true)
    }

    @Test
    fun testOnItalicItemClick() {
        val view = mock<View>()
        viewModel.onItalicItemClick(view)
        verify(mockItemClickListener).setItalic(true)
        assert(viewModel.isItalicActivated.value == true)
    }

    @Test
    fun testOnStrikethroughItemClick() {
        val view = mock<View>()
        viewModel.onStrikethroughItemClick(view)
        verify(mockItemClickListener).setStrikethrough(true)
        assert(viewModel.isStrikethroughActivated.value == true)
    }

    @Test
    fun testOnBulletListItemClick() {
        val view = mock<View>()
        viewModel.onBulletListItemClick(view)
        verify(mockItemClickListener).setBulletList(true)
    }

    @Test
    fun testOnOrderedListItemClick() {
        val view = mock<View>()
        viewModel.onOrderedListItemClick(view)
        verify(mockItemClickListener).setOrderedList(true)
    }

    @Test
    fun testOnTaskListItemClick() {
        val view = mock<View>()
        viewModel.onTaskListItemClick(view)
        verify(mockItemClickListener).setTaskList(true)
    }

    @Test
    fun testOnIncreaseIndentItemClick() {
        val view = mock<View>()
        viewModel.onIncreaseIndentItemClick(view)
        verify(mockItemClickListener).increaseIndent()
    }

    @Test
    fun testOnDecreaseIndentItemClick() {
        val view = mock<View>()
        viewModel.onDecreaseIndentItemClick(view)
        verify(mockItemClickListener).decreaseIndent()
    }

    @Test
    fun testOnAlignItemClick() {
        val view = mock<View>()
        val align = RichTextToolsConstant.ALIGNMENT_CENTER
        viewModel.onAlignItemClick(view, align)
        verify(mockItemClickListener).setTextAlign(align)
        assert(viewModel.activatedTextAlign.value != null)
    }

    @Test
    fun testSetSelectedPickerColor() {
        val picker = RichTextToolPanelViewModel.TextColorPicker("Red", 0xFF0000, RichTextToolPanelViewModel.PICK_COLOR_RED)
        viewModel.setSelectedPickerColor(mockContext, picker)
        assert(viewModel.activatedTextColorPicker.value == picker)
        verify(mockItemClickListener).setTextColorType(RichTextToolPanelViewModel.PICK_COLOR_RED)
    }

    @Test
    fun testOnTextHighlightItemClick() {
        val picker = RichTextToolPanelViewModel.TextColorPicker("Yellow", 0xFFFF00, RichTextToolPanelViewModel.BG_PICK_COLOR_YELLOW)
        viewModel.onTextHighlightItemClick(mockContext, picker)
        assert(viewModel.activatedBackGroundColorPicker.value == picker)
        verify(mockItemClickListener).setTextHighlight(RichTextToolPanelViewModel.BG_PICK_COLOR_YELLOW)
    }

    @Test
    fun testOnUnderlineItemClick() {
        val picker = RichTextToolPanelViewModel.TextColorPicker("Red", 0xFF0000, RichTextToolPanelViewModel.LINE_PICK_COLOR_RED)
        viewModel.onUnderlineItemClick(mockContext, picker)
        assert(viewModel.activatedUnderLineColorPicker.value == picker)
        verify(mockItemClickListener).setUnderline(RichTextToolPanelViewModel.UNDER_LINE, RichTextToolPanelViewModel.LINE_PICK_COLOR_RED)
    }

    @Test
    fun testOnWavylineItemClick() {
        val picker = RichTextToolPanelViewModel.TextColorPicker("Red", 0xFF0000, RichTextToolPanelViewModel.LINE_PICK_COLOR_RED)
        viewModel.onWavylineItemClick(mockContext, picker)
        assert(viewModel.activatedWaveLineColorPicker.value == picker)
        verify(mockItemClickListener).setUnderline(RichTextToolPanelViewModel.WAVE_LINE, RichTextToolPanelViewModel.LINE_PICK_COLOR_RED)
    }

    @Test
    fun testSetSelectedPickerSize() {
        val picker = RichTextToolPanelViewModel.TextSizePicKer(20)
        viewModel.setSelectedPickerSize(picker)
        assert(viewModel.activatedTextSizePicker.value == picker)
        verify(mockItemClickListener).setTextSize(20)
    }

    @Test
    fun testSetFocusEditor() {
        viewModel.setFocusEditor(mockContext, 0)
        assert(viewModel.focusEditor.value == 0)
    }
}