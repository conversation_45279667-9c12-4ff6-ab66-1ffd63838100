apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.richtext.core'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    testOptions {
        unitTests.includeAndroidResources = true

        unitTests.all {
            systemProperty 'robolectric.dependency.repo.url', prop_oppoMavenUrl
            systemProperty 'robolectric.dependency.repo.id', 'local'
        }
    }
}
apply from: rootProject.projectDir.path + "/coui_uikit.gradle"
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "androidx.core:core-ktx:${core_ktx}"
    
    implementation "com.oplus.statistics:track:${track}"

    api "org.jsoup:jsoup:${jsoup}"
    api "com.google.code.gson:gson:${gson}"
    implementation project(path: ':common:logger:logger-api')
    compileOnly project(":richtext:transform")
}
