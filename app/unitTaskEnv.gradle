import com.sun.management.OperatingSystemMXBean
import java.lang.management.ManagementFactory

static long getSysMemMBSize() {
    def mem = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean()
    def memByteSize = mem.totalPhysicalMemorySize
    return memByteSize / 1024 / 1024
}

static int getUnitTestForksNum(int maxParallelForksProp) {
    if (maxParallelForksProp > 0) {
        return maxParallelForksProp
    }
    final int memLimit = 4 + Math.max(0, Math.round(getSysMemMBSize() / 1000f - 16))
    final int cpuLimit = Runtime.runtime.availableProcessors().intdiv(6) ?: 1
    return Math.min(memLimit, cpuLimit)
}

static String getJvmMaxHeapMBConfig(String maxHeapSizeProp, int maxForkNum) {
    if (!maxHeapSizeProp.isEmpty()) {
        return maxHeapSizeProp
    }
    final long minHeapLimit = 4096L
    final long totalMemSize = getSysMemMBSize()
    final long maxHeapLimit = Math.max(minHeapLimit, (long) (totalMemSize / 12))
    final long autoHeapLimit = (totalMemSize / 12) - (256 * (maxForkNum - 8))
    final long useMaxHeapSize = Math.min(maxHeapLimit, Math.max(minHeapLimit, autoHeapLimit))
    return "${useMaxHeapSize}m"
}

ext {
     env_maxParallelForks = getUnitTestForksNum(0)
     env_maxHeapSize = getJvmMaxHeapMBConfig("", env_maxParallelForks)
}
println("Unit test runtime, maxHeapSize=$env_maxHeapSize, maxParallelForks=$env_maxParallelForks")
