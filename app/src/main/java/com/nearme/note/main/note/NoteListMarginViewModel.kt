package com.nearme.note.main.note

import android.app.Application
import android.content.Context
import androidx.lifecycle.*
import com.nearme.note.util.postValueSafe
import com.oplus.note.R

class NoteListMarginViewModel(private val application: Application) : AndroidViewModel(application) {
    var mNotePlaceHolderViewHeight = MutableLiveData(0)

    fun refreshPlaceHolderViewHeight(ctx: Context?) {
        val resources = (ctx ?: application).resources
        val height = resources.getDimensionPixelOffset(R.dimen.toolbar_height) +
                resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_height) +
                resources.getDimensionPixelOffset(R.dimen.note_subtitle_height) +
                resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_margin_bottom) +
                resources.getDimensionPixelOffset(R.dimen.note_list_padding_top) +
                resources.getDimensionPixelOffset(R.dimen.toolbar_list_label_height)
        mNotePlaceHolderViewHeight.postValueSafe(height)
    }
}