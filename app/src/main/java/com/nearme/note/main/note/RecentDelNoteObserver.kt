/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: RecentDelNoteObserver.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/12/05
 * * Author: ********
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import android.database.ContentObserver
import android.os.Handler
import androidx.annotation.MainThread
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withStarted
import com.oplus.note.utils.NoteStatusProviderUtil
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class RecentDelNoteObserver(handler: Handler?, fragment: NoteListFragment) : ContentObserver(handler) {

    companion object {
        private const val TAG = "RecentDelNoteObserver"
    }

    private val weakRef = WeakReference(fragment)
    private var stateLiveData = MutableLiveData<Boolean?>() // 记录是否有修改过最近删除加密状态

    init {
        fragment.lifecycleScope.launch {
            fragment.lifecycle.withStarted {
                runCatching {
                    stateLiveData.observe(fragment.viewLifecycleOwner) {
                        tryNotifyStateChanged()
                    }
                }
            }
        }
    }

    override fun onChange(selfChange: Boolean) {
        super.onChange(selfChange)

        val fragment = weakRef.get() ?: return
        fragment.lifecycleScope.launch {
            val ctx = fragment.context ?: return@launch
            val isRecentDeleteFolderEncrypt = NoteStatusProviderUtil.isRecentDeleteFolderEncrypt(ctx)
            stateLiveData.value = isRecentDeleteFolderEncrypt
        }
    }

    @MainThread
    private fun tryNotifyStateChanged() {
        val isEncrypt = stateLiveData.value ?: return
        val isFromInternal = NoteStatusProviderUtil.changeRecentDeleteFolderEncryptState // 判断本次更新是否来自内部修改
        weakRef.get()?.updateRecentDelNoteStatus(isEncrypt, isFromInternal)

        NoteStatusProviderUtil.changeRecentDeleteFolderEncryptState = false
        stateLiveData.value = null
    }
}