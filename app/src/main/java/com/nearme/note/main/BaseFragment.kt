/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: BaseFragment.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2021/06/15
 * * Author: PengFei.Ma
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * PengFei.Ma   2021/06/15      1.0    build this module
 ****************************************************************/
package com.nearme.note.main

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.oplus.note.edgeToEdge.CommonBaseFragment
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import java.lang.ref.WeakReference

open class BaseFragment : CommonBaseFragment() {
    companion object {
        private const val TAG = "BaseFragment"

        private fun getStatusBarClickReceiver(fragment: BaseFragment): BroadcastReceiver {
            val weakReference = WeakReference(fragment)
            return object : BroadcastReceiver() {
                override fun onReceive(paramContext: Context, paramIntent: Intent) {
                    weakReference.get()?.backToTop()
                }
            }
        }
    }

    private val statusBarClickReceiver: BroadcastReceiver by lazy {
        getStatusBarClickReceiver(this)
    }

    override fun onResume() {
        super.onResume()
        registerStatusBarReceiver()
    }

    override fun onPause() {
        super.onPause()
        unregisterStatusBarReceiver()
    }

    private fun registerStatusBarReceiver() {
        try {
            val intentFilter = IntentFilter()
            if (OplusBuildProxy.isAboveOS113()) {
                intentFilter.addAction("com.oplus.clicktop")
            } else {
                intentFilter.addAction("com.color.clicktop")
            }
            ContextCompat.registerReceiver(requireContext(), statusBarClickReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "registerStatubarReceiver error e = " + e.message)
        }
    }

    private fun unregisterStatusBarReceiver() {
        try {
            activity?.unregisterReceiver(statusBarClickReceiver)
        } catch (ignore: Exception) {
        }
    }

    open fun backToTop() {
        //subclass implementation
    }
}