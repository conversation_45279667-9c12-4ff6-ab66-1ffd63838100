package com.nearme.note.main

import android.content.Context
import android.provider.Settings
import com.nearme.note.MyApplication.Companion.appContext

@Deprecated("use [ResponsiveUiHelper]")
object UIConfigMonitor {
    private const val TAG = "UIConfigMonitor"
    private const val SYSTEM_FOLDING_MODE_KEYS = "oplus_system_folding_mode"
    private const val SYSTEM_FOLDING_MODE_OPEN = 1
    private const val SYSTEM_FOLDING_MODE_CLOSE = 0

    /***
     * 手机真实的展开状态
     */
    @JvmStatic
    @Deprecated("use [ResponsiveUiHelper.isUnfoldState]")
    fun isFoldingModeOpen(context: Context?): Boolean {
        return Settings.Global.getInt(context?.contentResolver,
                SYSTEM_FOLDING_MODE_KEYS, SYSTEM_FOLDING_MODE_CLOSE) == SYSTEM_FOLDING_MODE_OPEN
    }

    /***
     * 手机真实的展开状态
     */
    @JvmStatic
    @Deprecated("use [ResponsiveUiHelper.isUnfoldState]")
    fun isFoldingModeOpen(): Boolean {
        return Settings.Global.getInt(appContext.contentResolver,
            SYSTEM_FOLDING_MODE_KEYS, SYSTEM_FOLDING_MODE_CLOSE) == SYSTEM_FOLDING_MODE_OPEN
    }
}