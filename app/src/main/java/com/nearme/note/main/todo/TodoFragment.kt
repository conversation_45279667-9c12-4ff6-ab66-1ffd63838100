/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: TodoFragment.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.todo

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.text.format.DateFormat
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.animation.LinearInterpolator
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.util.Pair
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.nearme.note.DialogFactory
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.list.TodoAdapter
import com.nearme.note.activity.list.TodoItemAnimator
import com.nearme.note.activity.list.TodoItemTouchHelperCallBack
import com.nearme.note.activity.list.entity.ToDoItem
import com.nearme.note.activity.list.entity.ToDoItem.countFinishTimeNonZero
import com.nearme.note.activity.list.entity.ToDoItem.countFinishTimeZero
import com.nearme.note.activity.richedit.CheckPermissionHelper
import com.nearme.note.activity.richedit.CheckPermissionHelper.RequestResultCallback
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.appwidget.todowidget.TodoSettingViewModel
import com.nearme.note.common.Constants
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.logic.AccountManager
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.NoteSyncProcessProxy
import com.nearme.note.main.ActivitySharedViewModel
import com.nearme.note.main.BaseFragment
import com.nearme.note.main.MainActivity
import com.nearme.note.main.MainFragment
import com.nearme.note.main.NoteSubTitleViewHelper
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.setting.SettingsActivity
import com.nearme.note.util.AlarmController
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.DeleteSoundUtils
import com.nearme.note.util.DeviceInfoUtils
import com.nearme.note.util.DialogUtils
import com.nearme.note.util.EnvirStateUtils
import com.nearme.note.util.FlexibleWindowUtils
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.MultiClickFilter
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.ScreenUtil
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.util.ToDoAlarmController
import com.oplus.note.utils.isPackageDisabled
import com.nearme.note.view.ColorEditTextWrapper
import com.nearme.note.view.DialogUseMode
import com.nearme.note.view.TodoModalDialog
import com.nearme.note.view.helper.IntentUtils
import com.nearme.note.view.helper.NavigationAnimatorHelper
import com.nearme.note.view.helper.UiHelper
import com.nearme.note.view.refresh.BounceCallBack
import com.nearme.note.view.refresh.BounceHandler
import com.nearme.note.view.refresh.BounceLayout
import com.nearme.note.view.refresh.DefaultHeader
import com.nearme.note.view.refresh.EventForwardingHelper
import com.nearme.note.view.scalebehavior.PrimaryTitleBehavior
import com.nearme.note.viewmodel.ToDoViewModel
import com.nearme.note.viewmodel.TodoSharedViewModel
import com.oplus.cloud.utils.PrefUtils
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.view.CloudKitInfoController
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.cloudkit.view.InfoNotifyControllerWrapper
import com.oplus.cloudkit.view.SyncGuideManagerWrapper
import com.oplus.forcealertcomponent.StatisticsUtil
import com.oplus.note.R
import com.oplus.note.databinding.TodoListLayoutBinding
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.OsConfigurations
import com.oplus.note.os.ResponsiveUiHelper
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.permission.PermissionManager
import com.oplus.note.permission.PermissionsBlockedUtils
import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.scenecard.todo.TodoListActivity
import com.oplus.note.scenecard.utils.SceneCardConstants
import com.oplus.note.scenecard.utils.SceneCardConstants.FORCE_REMINDER
import com.oplus.note.statistic.StatisticsNoteCard
import com.oplus.note.utils.SysDragManager
import com.oplus.note.view.EmptyContentView
import com.oplus.richtext.editor.view.focus.FocusInfo
import com.oplus.todo.search.TodoSearchManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import kotlin.math.abs

class TodoFragment : BaseFragment() {
    companion object {
        private const val TAG = "TodoFragment"
        const val ACTION_UPDATE_ITEM_EXPIRED = "action_update_item_expired"
        private const val ALPHA_DURATION = 160L
        private const val DELAY_TIME = 100L
        private const val ITEM_BACKGROUND_CHANGE_DURATION = 400
        private const val ITEM_BACKGROUND_STAY_DURATION = 550
        private const val DELAY_REMOVE_ONGLOBAL_LAYOUT = 300L
        private const val PULLINGDOWN_THRESHOLD = 50F
        private const val DURATION_SNACK_BAR = 2000
        const val FRAGMENT_ALPHA_TIME = 500L
        private const val HEADEAR_COUNT = 2
    }

    private val todoMarginViewModel by viewModels<TodoListMarginViewModel>({ requireActivity() })
    private val todoListViewModel by viewModels<TodoSharedViewModel>({ requireActivity() })
    private val todoViewModel by viewModels<ToDoViewModel>({ requireActivity() })
    private val sharedViewModel by viewModels<ActivitySharedViewModel>({ requireActivity() })
    private var linearLayoutManager: COUILinearLayoutManager? = null

    private val adapter by lazy {
        TodoAdapter(viewLifecycleOwner, todoListViewModel)
    }
    private val mSubTitleViewHelper by lazy { NoteSubTitleViewHelper() }

    private val editMenuStub = lazy { binding?.root?.findViewById<ViewStub>(R.id.todo_edit_menu_stub) }
    private var binding: TodoListLayoutBinding? = null
    private var behavior: PrimaryTitleBehavior? = null
    private var infoNotifyController: InfoNotifyControllerWrapper? = null
    private var guideManager: SyncGuideManagerWrapper? = null
    private var loadDataFinished = false
    private var supportTitleMarginStart = 0
    private var toolNavigationView: COUINavigationView? = null
    private var navigationAnimatorHelper: NavigationAnimatorHelper? = null
    private var isAnimating = false
    private var selectItemSize = 0
    /**云同步开关状态，为 null 时表示还没有获取成功*/
    private var syncEnable: Boolean? = null
    private var todoModalDialog: TodoModalDialog? = null
    private var emptyContentPage: EmptyContentView? = null
    private var noteSyncProcess: NoteSyncProcessProxy? = null
    private var noteListHelper: NoteListHelper? = null
    private var noteListHelperCallBack: NoteListHelper.CallBack? = null
    private var dialogClickListener: DialogFactory.DialogOnClickListener? = null
    private var dialogFactory: DialogFactory? = null
    private var preHourFormat = false
    private var isNotificationInit = true
    private var isSelectionModeFirstInit = true
    private var downY = 0F
    private var moveY = 0F
    private var mCallBack: BounceCallBack? = null
    private var isShowTips = false
    private var mPlaceHolderViewHeight = 0  // 记录列表头部空控件 的占位高度

    private var mIsFirstLoadTodoList = true
    private var mEmptyContentPageHelper: ImageHelper? = null
    private var localReceiver: LocalReceiver? = null
    private var requestNotificationPermission: ActivityResultLauncher<String>? = null
    private val alarmPermissionHelper = CheckPermissionHelper()
    private var permissionManager: PermissionManager? = null
    private var memuClickId = -1
    private var isFirstOnResume = true
    private var itemTouchHelper: ItemTouchHelper? = null
    private var gestureDetector: GestureDetector? = null
    private var nowShowSyncTip: Boolean = false
    private var lastUnfoldState: Boolean? = null
    private var lastSmallScreen: Boolean? = null
    private var screenWidth: Int = 0
    private var tempTodoList: List<ToDoItem>? = null
    private var sysDragManager: SysDragManager? = null

    private val timeChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            todoListViewModel.mCurrentDate.setValue(Date())
            adapter.notifyDataSetChanged()
            AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO)
            AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity !is MainActivity && ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.i(TAG, "onCreate error: Todo is deprecated.")
            activity?.finish()
            return
        }
        screenWidth = ScreenUtil.getScreenWidth()
        permissionManager = PermissionManager(this)
        supportTitleMarginStart = resources.getDimensionPixelOffset(R.dimen.toolbar_support_title_margin_start)
        initNoteListHelper()
        initReceiver()
        preHourFormat = DateFormat.is24HourFormat(context)
        val isFromLockScreenCardNotification = IntentParamsUtil.getBooleanExtra(activity?.intent, AlarmController.DATA_FROM_LOCKSCREEN_CARD_NOTIFICATION, false)
        AppLogger.BASIC.d(TAG, "onCreate isFromLockScreenCardNotification: $isFromLockScreenCardNotification")
        if (isFromLockScreenCardNotification == true) {
            activity?.intent?.removeExtra(AlarmController.DATA_FROM_NOTIFICATION)
            activity?.intent?.removeExtra(AlarmController.DATA_FROM_LOCKSCREEN_CARD_NOTIFICATION)
            activity?.intent?.removeExtra(ToDoAlarmController.DATA_UUID)
            StatisticsUtil.setEventFromLockscreenCardNotification(context)
        }

        lifecycleScope.launch {
            delay(DELAY_TIME)
            if (todoListViewModel.showTodoEditDialog && todoListViewModel.editingToDo.value != null) {
                showTodoModalDialog()
            }
        }
        localReceiver = LocalReceiver()
        val intentFilter = IntentFilter(Constants.ACTION_NOTIFICATION_GRANT)
        LocalBroadcastManager.getInstance(requireContext())
            .registerReceiver(localReceiver!!, intentFilter)
        //request notification permission
        requestNotificationPermission = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                AppLogger.BASIC.d(TAG, "request notification permission,grant:$isGranted ")
            } else {
                AppLogger.BASIC.d(TAG, "request notification permission,grant:$isGranted ")
                showNotificationPermissionDialog()
            }
        }
        setStatistics()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = TodoListLayoutBinding.inflate(inflater, container, false).apply {
            lifecycleOwner = viewLifecycleOwner
            viewModel = todoMarginViewModel
        }

        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (savedInstanceState != null && sharedViewModel.todoSelectionMode.value == true) {
            todoListViewModel.isDeleteDialogRebuild = savedInstanceState.getBoolean(Constants.DIALOG_REBUILD_TAG, false)
        }
        activity?.let {
            lastUnfoldState = ResponsiveUiHelper.isUnfoldState(it)
            lastSmallScreen = ResponsiveUiHelper.isSmallScreen(it)
        }
        initiateWindowInsets()
        initiateToolbar()
        initRecyclerView(savedInstanceState)
        initRefreshView()
        initBehavior()
        initEmptyPage()

        initObservers(savedInstanceState != null)
        val alphaAnimator = ObjectAnimator.ofFloat(binding?.root, "alpha", 0f, 1f)
        alphaAnimator.duration = FRAGMENT_ALPHA_TIME
        alphaAnimator.start()
        handleJumpScroll()
        showTodoListIfNeed(true)
        view.doOnLayoutChangeListener()
        sysDragManager = context?.let { SysDragManager(it, lifecycle) }
    }

    /**
     * 检查是加载父子级分组view还是小屏列表view
     */
    private fun showTodoListIfNeed(fromCreate: Boolean = false) {
        activity?.let {
            val unfoldState = ResponsiveUiHelper.isUnfoldState(it)
            val smallScreen = ResponsiveUiHelper.isSmallScreen(it)
            AppLogger.BASIC.d(
                TAG, "showTodoListIfNeed foldStateChange:${lastUnfoldState != unfoldState}," +
                        "screenChanged=${lastSmallScreen != smallScreen}," +
                        "adapter.mIsTwoPanel=${adapter.mIsTwoPanel}," +
                        "smallScreen=$smallScreen"
            )
            val twoPane = !smallScreen
            if (lastUnfoldState != unfoldState || lastSmallScreen != smallScreen || adapter.mIsTwoPanel != twoPane) {
                if (smallScreen) {
                    //小窗口，非父子级
                    AppLogger.BASIC.d(TAG, "showTodoListIfNeed small screen")
                    sharedViewModel.twoPane = false
                } else {
                    //中大窗口，父子级
                    AppLogger.BASIC.d(TAG, "showTodoListIfNeed large screen")
                    sharedViewModel.twoPane = true
                    if (fromCreate) {
                        //首次初始化页面需要默认加载未完成
                        val fragment = activity?.supportFragmentManager?.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment
                        fragment?.loadTodos(TodoListFragment.TODO_ALL)
                    }
                    doOnSelectionModeChanged(false)
                    adapter.exitSelectionMode()
                    setSubtitleViewVisibility()
                }
                if (sharedViewModel.todoSelectionMode.isInitialized) {
                    if (sharedViewModel.todoSelectionMode.value == true) {
                        //恢复工具类
                        (parentFragmentManager.findFragmentByTag(MainFragment.TAG) as? MainFragment)?.doBottomMenuAnimation(false, false, true)
                    }
                    sharedViewModel.todoSelectionMode.value = false
                    todoListViewModel.selectAll(false)
                    todoListViewModel.mPendingDeleteSelectedToDos.value = false
                }
                if (todoListViewModel.toDoItems.isInitialized) {
                    todoListViewModel.toDoItems.value?.let { list ->
                        AppLogger.BASIC.d(TAG, "showTodoListIfNeed setToDoItems")
                        todoListViewModel.setToDoItems(list)
                    }
                }
                lastUnfoldState = unfoldState
                lastSmallScreen = smallScreen
            }
        }
    }

    private fun View.doOnLayoutChangeListener() {
        val action = fun() {
            var onLayoutChangeListener =
                getTag(R.id.parent) as? View.OnLayoutChangeListener
            if (onLayoutChangeListener != null) {
                return
            } else {
                onLayoutChangeListener =
                    View.OnLayoutChangeListener { _, left, _, right, _, oldLeft, _, oldRight, _ ->
                        if (right - left != oldRight - oldLeft) {
                            showTodoListIfNeed()
                        }
                    }
            }
            setTag(R.id.parent, onLayoutChangeListener)
            addOnLayoutChangeListener(onLayoutChangeListener)
        }
        if (ViewCompat.isAttachedToWindow(this)) {
            action()
        }
        addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(view: View) {
                action()
            }

            override fun onViewDetachedFromWindow(view: View) {
                removeOnAttachStateChangeListener(this)
                val onLayoutChangeListener =
                    getTag(R.id.parent) as? View.OnLayoutChangeListener
                setTag(R.id.parent, null)
                removeOnLayoutChangeListener(onLayoutChangeListener)
            }
        })
    }

    private fun handleJumpScroll() {
        activity?.apply {
            val position = IntentParamsUtil.getIntExtra(this.intent, TodoListActivity.POSITION, -1)
            AppLogger.BASIC.d(TAG, "initRecyclerView position: $position")
            if (position > 0) {
                binding?.todoList?.post {
                    scrollToPosition(position + HEADEAR_COUNT)
                    behavior?.initBehavior(binding?.parent ?: return@post,
                            binding?.appBar ?: return@post,
                            binding?.todoList ?: return@post)
                    behavior?.onListScroll()
                    AppLogger.BASIC.d(TAG, "handleJumpScroll position $position")
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        resetHourFormat()
        noteListHelper?.onResume()
        lifecycleScope.launch {
            delay(DELAY_TIME)
            val fromEditTodoAction = IntentUtils.fromEditTodoAction(activity?.intent)
            val isFromAppCard = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.MAIN_ACTION_FROM, "")
            todoListViewModel.mFromSpeechAssist = false
            AppLogger.BASIC.d(TAG, "onResume: isFromAppCard: $isFromAppCard, fromEditTodoAction=$fromEditTodoAction")
            if (fromEditTodoAction) {
                if (ConfigUtils.isToDoDeprecated) {
                    AppLogger.BASIC.i(TAG, "onResume error: Todo is deprecated.")
                    activity?.finish()
                    return@launch
                }
                handleIntentFromEditTodoAction(activity)
            } else {
                handleIntentFromAppCard(activity)
            }

            /**
             * 1.Show edit-dialog or create-dialog.
             * 2.Go to secondary screen open tdo-card,choose the same tdo item(if is create-dialog,choose any),
             *     go to detail fragment,done or delete it.
             * 3.Open device,go to main notes app,check the dialog will dismiss.If the dialog is create-tdo type,it will not dismiss.
             *
             */
            if (!todoListViewModel.isCreationMode
                && (isFromAppCard != PrefUtils.APP_TODO_CARD_EDIT)
                && (isFromAppCard != PrefUtils.APP_TODO_CARD_NEW)
                && (todoModalDialog?.isShowing == true)
            ) {
                todoListViewModel.editingToDo?.value?.let {
                    lifecycleScope.launch(Dispatchers.IO) {
                        val item = todoViewModel.getByLocalId(it.localId.toString())
                        if ((item == null) || (item.isDelete != it.isDelete) || (item.finishTime != it.finishTime)) {
                            withContext(Dispatchers.Main) {
                                todoModalDialog?.dismiss()
                            }
                        }
                    }
                }
            }
        }
        if (!isFirstOnResume) {
            refreshNoteListTips()
        }
        isFirstOnResume = false
    }

    private fun handleIntentFromEditTodoAction(activity: Activity?) {
        activity?.apply {
            val actionHandled = IntentParamsUtil.getBooleanExtra(this.intent, PrefUtils.ACTION_HANDLED, false)
            val actionFrom = IntentParamsUtil.getStringExtra(this.intent, PrefUtils.MAIN_ACTION_FROM, "")
            val editTodoAction = IntentUtils.fromEditTodoAction(this.intent)
            AppLogger.BASIC.d(TAG, "handleIntentFromEditTodoAction: editTodoAction=$editTodoAction, actionHandled=$actionHandled")
            if (actionHandled) {
                return@apply
            }
            if (editTodoAction) {
                val fromSpeechAssist = IntentUtils.fromSpeechAssist(actionFrom)
                val actionLocalId = IntentParamsUtil.getStringExtra(this.intent, PrefUtils.ACTION_PARAM_LOCAL_ID, "")
                AppLogger.BASIC.d(TAG, "handleIntentFromEditTodoAction: fromSpeechAssist: $fromSpeechAssist, actionLocalId=$actionLocalId")
                val content = IntentParamsUtil.getStringExtra(this.intent, PrefUtils.ACTION_PARAM_CONTENT)
                val alarmTime = IntentParamsUtil.getLongExtra(this.intent, PrefUtils.ACTION_PARAM_ALARM_TIME, 0L)
                todoListViewModel.mFromSpeechAssist = fromSpeechAssist
                if (actionLocalId.isNotEmpty()) {
                    // Edit
                    lifecycleScope.launch(Dispatchers.IO) {
                        actionLocalId?.let { localId ->
                            val toDo = todoViewModel.getByLocalId(localId) ?: return@let
                            withContext(Dispatchers.Main) {
                                todoListViewModel.setToDoForEditing(toDo)
                                showTodoModalDialog()
                            }
                        }
                    }
                } else {
                    // New
                    fabMainActionSelected(content, alarmTime)
                }
                this.intent.putExtra(PrefUtils.ACTION_HANDLED, true)
            }
        }
    }

    private fun handleIntentFromAppCard(activity: Activity?) {
        val isFromAppCard = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.MAIN_ACTION_FROM, "")
        AppLogger.BASIC.d(TAG, "handleIntentFromAppCard: isFromAppCard: $isFromAppCard")
        if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD_NEW) && PrivacyPolicyHelper.isAgreeUserNotice(context)) {
            fabMainActionSelected()
            removeToDoCardExtra()
        } else if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD) || isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_PRIVACY)) {
            if (IntentParamsUtil.getBooleanExtra(activity?.intent, SceneCardConstants.NEED_PERMISSION, false)) {
                val remind = IntentParamsUtil.getBooleanExtra(activity?.intent, FORCE_REMINDER, false)
                alarmPermissionHelper.checkAlarmPermissions(
                    permissionManager,
                    activity,
                    0L,
                    remind,
                    ignore = false)
            }
            removeToDoCardExtra()
        } else if (isFromAppCard.equals(PrefUtils.APP_TODO_MIDDLE_CARD_OTHER)) {
            val toDoLocalId = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.APP_TODO_CARD_LOCAL_ID, "")
            var position = -1
            val toDoItemList: List<ToDoItem>? = todoListViewModel.toDoItems.value
            toDoItemList?.apply {
                for (i in toDoItemList.indices) {
                    if (toDoLocalId == toDoItemList[i].toDo.localId.toString()) {
                        position = i
                        break
                    }
                }
                if (position > -1) {
                    position += 1
                    scrollToPosition(position)
                } else {
                    AppLogger.BASIC.d(TAG, "current position $position can not be found")
                }
            }
            removeToDoCardExtra()
        } else if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD_EDIT)) {
            lifecycleScope.launch(Dispatchers.IO) {
                val toDoLocalId = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.APP_TODO_CARD_LOCAL_ID, "")
                toDoLocalId?.let { localId ->
                    val toDo = todoViewModel.getByLocalId(localId) ?: return@let
                    withContext(Dispatchers.Main) {
                        todoListViewModel.setToDoForEditing(toDo)
                        showTodoModalDialog()
                        removeToDoCardExtra()
                    }
                }
            }
        } else if (isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_AUDIO) ||
            isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_NOTIFICATION) ||
            isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_OVERLAY)) {
            val forceRemind = IntentParamsUtil.getBooleanExtra(activity?.intent, FORCE_REMINDER, false)
            continueCheckPermission(isFromAppCard == SceneCardConstants.APP_TODO_CARD_REQ_AUDIO, forceRemind)
            removeToDoCardExtra()
        }
    }

    private fun continueCheckPermission(isExport: Boolean, forceRemind: Boolean) {
        AppLogger.BASIC.d(TAG, "continueCheckPermission checkPermission$isExport,$forceRemind")
        if (isExport) {
            alarmPermissionHelper.checkMicrophonePermissions(permissionManager, activity, ignoreBlocked = false)
        } else {
            alarmPermissionHelper.checkAlarmPermissions(
                    permissionManager,
                    activity,
                    0L,
                    forceRemind,
                    ignore = true)
        }
    }

    override fun onPause() {
        super.onPause()
        todoModalDialog?.apply {
            val editText = this.findViewById<ColorEditTextWrapper>(R.id.edit_text)
            if (editText != null) {
                AppLogger.BASIC.d(TAG, "hasSelection: ${editText.hasSelection()}")
                if (this.isShowing && editText.hasSelection()) {
                    FocusInfo.hideSoftInput(this.context, this.contentView)
                }
            }
        }
    }

    fun refreshResumeCloud() {
        if (!isAdded) {
            return
        }
        try {
            if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext()) || !DeviceInfoUtils.isAppInstalled(requireContext(), MbaUtils.PACKAGER_CLOUD)) {
                AppLogger.BASIC.d(TAG, "todo resume mba disable")
                todoListViewModel.mSyncEnable.value = false
            } else {
                AppLogger.BASIC.d(TAG, "todo resume mba enable")
            }
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "refreshResumeCloud catch message: ${e.message}", e)
        }

        noteSyncProcess?.checkSyncSwitchStateTask()
        guideManager?.queryCloudKitSyncCloudState(context)
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        super.onMultiWindowModeChanged(isInMultiWindowMode)
        if (todoListViewModel.toDoItems.value != null) {
            resetMainEmptyPageAndSyncTips(todoListViewModel.toDoItems.value)
        }

        binding?.mainTitle?.postDelayed({
            behavior?.updateToolbar()
        }, DELAY_TIME)
    }

    override fun backToTop() {
        binding?.todoList?.stopScroll()
        binding?.todoList?.smoothScrollToPosition(0)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (sharedViewModel.todoSelectionMode.value == true) {
            sharedViewModel.todoSelectionMode.value = false
        }
        todoListViewModel.selectAll(false)
        todoListViewModel.mPendingDeleteSelectedToDos.value = false
    }

    override fun onDestroy() {
        super.onDestroy()
        permissionManager = null
        todoModalDialog?.onDestroy()
        todoModalDialog?.setDialogListener(null)
        todoModalDialog = null
        sharedViewModel.notificationUUID.value = ""
        guideManager?.release()
        appContext.unregisterReceiver(timeChangeReceiver)
        noteSyncProcess?.release()
        noteListHelper?.onBack()
        noteListHelper?.onDestroy()
        if (dialogFactory != null) {
            dialogFactory!!.onDestory()
            dialogFactory = null
        }
        if (localReceiver != null) {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(localReceiver!!)
        }
        infoNotifyController?.onDestroy()
        binding?.toolbar?.setOnMenuItemClickListener(null)
        binding?.toolbar?.hideOverflowMenu()
        todoListViewModel.removeOuterToDoAdapterCallback(callback)
    }

    private fun resetHourFormat() {
        val currentHourFormat = DateFormat.is24HourFormat(context)
        if (preHourFormat != currentHourFormat) {
            preHourFormat = currentHourFormat
            adapter.notifyDataSetChanged()
        }
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.root) { v, insets ->
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.statusBars())
            v.updatePadding(left = systemBarInsets.left, top = stableStatusBarInsets.top, right = systemBarInsets.right)
            initToolNavigationMenu()
            val layoutParams = toolNavigationView?.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParams?.bottomMargin = systemBarInsets.bottom
            toolNavigationView?.layoutParams = layoutParams
            binding?.refresh?.updatePadding(bottom = systemBarInsets.bottom)
            behavior?.setSystemBarInsetsTop(systemBarInsets.top)
            binding?.root?.visibility = View.VISIBLE
        }
    }

    private fun initiateToolbar() {
        binding?.toolbar?.inflateMenu(R.menu.menu_todo_list)
        binding?.toolbar?.setPopupWindowOnDismissListener {
            if (memuClickId == R.id.edit_todo) {
                if (sharedViewModel.storagePermissionDenied.value == true) {
                    sharedViewModel.checkPermission.value = true
                    return@setPopupWindowOnDismissListener
                }

                todoListViewModel.isDeletingOrHiding = false
                todoListViewModel.setSelectionMode(true, false)
                StatisticsUtils.setEventTodoMoreEdit(context ?: appContext)
            }
            memuClickId = -1
        }
        binding?.toolbar?.setOnMenuItemClickListener { menu ->
            memuClickId = menu.itemId
            when (menu.itemId) {
                R.id.select_all -> {
                    val isSelectAll = getString(R.string.select_all).contentEquals(menu.title!!)
                    todoListViewModel.selectAll(isSelectAll)
                }
                R.id.cancel -> {
                    todoListViewModel.isDeletingOrHiding = false
                    todoListViewModel.setSelectionMode(false, false)
                    StatisticsUtils.setEventTodoEditCancel()
                }

                R.id.jump_setting -> {
                    handleJumpSetting()
                }
                R.id.toggle_finished_todo -> {
                    val isHide: Boolean = todoListViewModel.mHideFinishedTodo.value ?: false
                    todoListViewModel.isDeletingOrHiding = true
                    if (isHide) {
                        menu.setTitle(R.string.hide_finished_todo)
                        TodoSettingViewModel.changeHideFinishedTodoApp(false)
                        todoListViewModel.mHideFinishedTodo.setValue(false)
                        StatisticsUtils.setEventTodoMoreHideFinish(context)
                    } else {
                        menu.setTitle(R.string.show_finished_todo)
                        TodoSettingViewModel.changeHideFinishedTodoApp(true)
                        todoListViewModel.mHideFinishedTodo.setValue(true)
                        StatisticsUtils.setEventTodoMoreShowFinish(context)
                    }
                }
            }
            return@setOnMenuItemClickListener true
        }
    }

    private fun handleJumpSetting() {
        activity?.let {
            if (EnvirStateUtils.getComponentState(it, SettingsActivity::class.java)) {
                FlexibleWindowUtils.startFlexibleSettingsActivity(
                    OplusFlexibleWindowManagerProxy.getFlexibleActivityPositionRight(),
                    it
                )
                // click setting buried point
                StatisticsUtils.setEventSettingOpenCount(it)
            }
        }
    }

    private fun initRecyclerView(savedInstanceState: Bundle?) {
        // 1.set recyclerview layout manager
        linearLayoutManager = object : COUILinearLayoutManager(context) {
            override fun canScrollVertically(): Boolean {
                return (binding?.refresh?.isRefreshing() == false) && super.canScrollVertically() && !isShowTips
            }

            override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
                runCatching {
                    super.onLayoutChildren(recycler, state)
                }.onFailure {
                    AppLogger.BASIC.d(TAG, "onLayoutChildren error.", it)
                }
            }
        }
        binding?.todoList?.layoutManager = linearLayoutManager

        // 2.set recyclerview item animator
        val itemAnimator = TodoItemAnimator()
        binding?.todoList?.itemAnimator = itemAnimator
        itemAnimator.setItemAnimatorListener {
            if (todoListViewModel.isDeletingOrHiding) {
                val toDoItems: List<ToDoItem>? = todoListViewModel.toDoItems.value
                val hasToDos = toDoItems != null && toDoItems.size > ToDoItem.TODO_HEADER_COUNT
                if (behavior?.getScaleEnable() == true || !hasToDos) {
                    behavior?.updateToolbar()
                }
            }
        }

        // 3.init header view
        infoNotifyController = InfoNotifyControllerWrapper.Builder()
            .setRecyclerView(binding?.todoList)
            .setFragment(this)
            .build()
        adapter.setHeadTipsLayout(infoNotifyController!!.getHeadTipsLayout())
        val placeHeight = resources.getDimensionPixelOffset(R.dimen.toolbar_height) +
                resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_height) +
                resources.getDimensionPixelOffset(R.dimen.note_subtitle_height) +
                resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_margin_bottom)
        adapter.setPlaceHolderViewHeight(placeHeight)
        mPlaceHolderViewHeight = placeHeight
        // 4.set recyclerview adapter
        binding?.todoList?.adapter = adapter
        adapter.setmRecyclerView(binding?.todoList)
        itemTouchHelper = ItemTouchHelper(TodoItemTouchHelperCallBack(adapter))
        itemTouchHelper?.attachToRecyclerView(binding?.todoList)
        if (savedInstanceState != null) {
            binding?.todoList?.viewTreeObserver?.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    binding?.todoList?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                    binding?.todoList?.scrollToPosition(0)
                }
            })
        }
        addOnItemTouchListener()
    }

    private fun addOnItemTouchListener() {
        activity?.let {
            gestureDetector =
                GestureDetector(it, object : GestureDetector.SimpleOnGestureListener() {
                    override fun onLongPress(e: MotionEvent) {
                        super.onLongPress(e)
                        binding?.todoList?.let { list ->
                            val childView = list.findChildViewUnder(e.x, e.y)
                            if (childView != null) {
                                val position =
                                    list.getChildLayoutPosition(childView)
                                val viewHolder =
                                    list.findViewHolderForAdapterPosition(position) as? TodoAdapter.TodoViewHolder

                                viewHolder?.apply {
                                    mItemViewModel?.let { viewModel ->
                                        mBinding?.backgroundItem?.let { backgroundItem ->
                                            viewModel.onItemLongClick(this, backgroundItem, sysDragManager)
                                        }
                                    }
                                }
                                AppLogger.BASIC.d(
                                    TAG,
                                    "onLongPress viewHolder is null ${viewHolder == null} position $position"
                                )
                            }
                            AppLogger.BASIC.d(
                                TAG,
                                "onLongPress childView is null ${childView == null}"
                            )
                        }
                    }

                    override fun onSingleTapUp(e: MotionEvent): Boolean {
                        return super.onSingleTapUp(e)
                    }
                })
        }
        binding?.todoList?.addOnItemTouchListener(object :
            RecyclerView.SimpleOnItemTouchListener() {
            override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                return if (adapter.isDragging || binding?.todoList?.itemAnimator?.isRunning == true) {
                    true
                } else {
                    gestureDetector?.onTouchEvent(e) ?: super.onInterceptTouchEvent(rv, e)
                }
            }
        })
    }

    private fun initRefreshView() {
        binding?.refresh?.apply {
            setRefreshEnable(false)
            setBounceHandler(BounceHandler(), binding?.todoList)
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(
                        downX: Float,
                        downY: Float,
                        moveX: Float,
                        moveY: Float
                ): Boolean {
                    return downY < moveY
                }
            })
            setHeaderView(DefaultHeader(context), binding?.headerRoot)
            mCallBack = setBounceCallBack(object : BounceCallBack {
                override fun startLoadingMore() {
                    //do nothing
                }

                override fun startRefresh() {
                }

                override fun refreshCompleted() {
                }

                override fun touchEventCallBack(ev: MotionEvent) {
                    handleTouchEventCallBack(ev)
                }
            })
            val headerViewHeight = resources.getDimensionPixelSize(R.dimen.default_height)
            val headerTopPadding =
                resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_top_padding)
            val headerBottomPadding =
                resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_bottom_padding)
            val listMaxTop =
                resources.getDimensionPixelOffset(R.dimen.pull_refresh_down_fragment_max_drag_distance) + headerBottomPadding
            /* In different layouts, the loading animation (LottieView) is hidden (hidden by other views) by setting
               the topMargin of the root layout, and is 24dp above the listView or recyclerView, This value is up to
               the business side according to the needs. */
            (binding?.headerRoot?.layoutParams as? CoordinatorLayout.LayoutParams?)?.topMargin = -headerViewHeight - headerBottomPadding
            /* Drag threshold. When the drag distance is greater than or equal to this value, it means that it will enter
               the loading state after letting go. The reason for this calculation is to make the loading animation (LottieView)
               24dp from the top (the View that covered it before), and also 24dp from the listView or recyclerView below
               (including the topPadding of the listView), so the specific value is up to the business party according to the
               needs up to you. */
            mDragDistanceThreshold =
                headerViewHeight + headerBottomPadding + headerTopPadding + headerBottomPadding
            /* The maximum distance that bounceLayout can be dragged down. */
            mMaxDragDistance = listMaxTop
        }
        val callback = if (!ConfigUtils.isUseCloudKit) {
                null
            } else {
                object : CloudKitSyncGuidManager.OnSyncFinishCallback {
                    override fun onSyncing() {
                        nowShowSyncTip = true
                        setSubtitleViewVisibility()
                    }

                    override fun onSyncFinish(syncStatus: CloudKitSyncStatus) {
                        nowShowSyncTip = true
                        todoListViewModel.mCompleteRefreshWithTipsAndDelay.value =
                            Pair(null, BounceLayout.MSG_REFRESH_DELAY)
                        TodoSearchManager.notifyDataChange()
                    }

                    override fun onSyncFinishSubtitleChange() {
                        nowShowSyncTip = false
                        setSubtitleViewVisibility()
                    }
                }
            }

        guideManager = SyncGuideManagerWrapper(
            this,
            infoNotifyController,
            todoViewModel = todoViewModel,
            onSyncFinishCallback = callback
        )
        if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
            todoListViewModel.mSyncEnable.value = false
        }
        noteSyncProcess = guideManager?.firstQueryNoteSyncCloudStateCompact(activity, this,
            object : NoteSyncProcess.CloudSyncStateCallback {
                override fun refreshViewState(syncState: Int) {
                    AppLogger.BASIC.d(TAG, "todo refreshViewState canSyncToCloud = $syncState")
                    val canSyncToCloud = if (ConfigUtils.isUseCloudKit)
                        syncState > CloudKitSdkManager.CLOSE_CODE
                    else
                        syncState > NoteSyncProcess.NOTE_SETTING_CLOUD_SYNC_CLOSE
                    context?.apply {
                        if (isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())
                            || !DeviceInfoUtils.isAppInstalled(activity, MbaUtils.PACKAGER_CLOUD)
                        ) {
                            todoListViewModel.mSyncEnable.value = false
                        } else {
                            todoListViewModel.mSyncEnable.value = canSyncToCloud
                        }
                    }
                }

                override fun refreshModuleState(isSupport: Boolean) {
                    AppLogger.BASIC.i(TAG, "refreshModuleState $isSupport")
                }
            })
    }

    private fun handleTouchEventCallBack(ev: MotionEvent) {
        when (ev.actionMasked) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_POINTER_DOWN -> {
                downY = ev.y
            }
            MotionEvent.ACTION_MOVE -> {
                moveY = ev.y
                if (abs(moveY - downY) > PULLINGDOWN_THRESHOLD && sharedViewModel.isPullingDown.value != true
                    && !sharedViewModel.isDragSortMode()) {
                    AppLogger.BASIC.d(TAG, "set isPullingDown true")
                    sharedViewModel.isPullingDown.value = true
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_POINTER_UP -> {
                sharedViewModel.isPullingDown.value = false
            }
        }
    }

    private fun initBehavior() {
        val params = binding?.appBar?.layoutParams as? CoordinatorLayout.LayoutParams
        behavior = params?.behavior as? PrimaryTitleBehavior
    }

    private fun initObservers(isRebuild: Boolean) {
        activity?.apply {
            todoMarginViewModel.notifyInMultiWindowBottomOrZoomWindow(requireActivity().isInMultiWindowMode)
        }
        initSortModeObserver()
        initToDoItemObserver(isRebuild)
        initZipDataObserver()
        observeGetFinished()
        todoViewModel.sumToDoDistribution { result ->
            AppLogger.BASIC.d(TAG, "sumToDoDistribution: result=$result")
            StatisticsUtils.setSumToDoDistribution(activity, result)
        }

        observesumToDoContentLength()

        observeTodoSelectionMode()
        observeTodoDragMode()

        observePendingDeleteSelected()

        setOuterToDoAdapterCallback()

        observeDeleteSelected()

        todoListViewModel.mCompleteRefreshWithTipsAndDelay.observe(viewLifecycleOwner, { tipsAndDelay: Pair<String?, Int?> ->
            val delay: Long = tipsAndDelay.second?.toLong() ?: 0L
            binding?.refresh?.postDelayed({
                binding?.refresh?.setRefreshCompleted()
            }, delay)
        })

        observeSyncEnable()

        observeStoragePermission()

        observeCanSaveTodo()
    }

    private fun areListsEqualInFinishTime(
        originToDoList: List<ToDoItem?>?,
        newOriginTodoList: List<ToDoItem?>?
    ): Boolean {
        var zeroCountOrigin = 0
        var nonZeroCountOrigin = 0
        var zeroCountNew = 0
        var nonZeroCountNew = 0
        if (originToDoList == null || newOriginTodoList == null) {
            AppLogger.BASIC.d(
                TAG,
                "areListsEqualInFinishTime null so return false $originToDoList $newOriginTodoList"
            )
            return false
        }
        zeroCountOrigin = countFinishTimeZero(originToDoList)
        nonZeroCountOrigin = countFinishTimeNonZero(originToDoList)
        zeroCountNew = countFinishTimeZero(newOriginTodoList)
        nonZeroCountNew = countFinishTimeNonZero(newOriginTodoList)
        // 打印 zeroCount 和 nonZeroCount 的值
        AppLogger.BASIC.d(
            TAG,
            "areListsEqualInFinishTime zeroCountOrigin: $zeroCountOrigin, nonZeroCountOrigin: $nonZeroCountOrigin"
        )
        AppLogger.BASIC.d(
            TAG,
            "areListsEqualInFinishTime zeroCountNew: $zeroCountNew, nonZeroCountNew: $nonZeroCountNew"
        )
        return zeroCountOrigin == zeroCountNew && nonZeroCountOrigin == nonZeroCountNew
    }

    private fun initZipDataObserver() {
        todoListViewModel.attachActivitySharedViewModel(sharedViewModel)
        todoListViewModel.attachToDoViewModel(todoViewModel)
        todoListViewModel.zippedDataNew(
            todoViewModel.getDataExpired(todoListViewModel.mCurrentDate),
            todoViewModel.getToday(todoListViewModel.mCurrentDate),
            todoViewModel.getNoAlarmTimeDate(todoListViewModel.mCurrentDate),
            todoViewModel.getTomorrow(todoListViewModel.mCurrentDate),
            todoViewModel.getAfterTomorrow(todoListViewModel.mCurrentDate),
            todoViewModel.getFinished(todoListViewModel.mCurrentDate),
            todoListViewModel.mHideFinishedTodo
        ).observe(viewLifecycleOwner) { toDoItems: MutableList<ToDoItem> ->
            if (todoListViewModel.isSortAction()) {
                return@observe
            }
            AppLogger.BASIC.d(TAG, "initObservers zippedDataNew")
            toDoItems.add(0, ToDoItem.getPlaceHolderItem())
            //Add a HeyTap cloud head by default
            toDoItems.add(1, ToDoItem.getHeyTapHeaderItem())
            todoListViewModel.setToDoItems(toDoItems)
            updateTitle()
        }
    }

    private fun initToDoItemObserver(isRebuild: Boolean) {
        var firstRebuild = isRebuild
        todoListViewModel.toDoItems.observe(viewLifecycleOwner) { toDoItems: List<ToDoItem> ->
            val twoPane = sharedViewModel.twoPane
            if ((adapter.mIsTwoPanel == twoPane) && (todoListViewModel.isSortAction || !adapter.hasSubmit)) {
                AppLogger.BASIC.d(TAG, "not refresh data by ${todoListViewModel.isSortAction},${adapter.hasSubmit}")
                return@observe
            }
            val hideDoneTodo: Boolean = todoListViewModel.mHideFinishedTodo.value ?: false
            val areListsEqualInFinishTime = areListsEqualInFinishTime(tempTodoList, toDoItems)
            tempTodoList = null
            //areListsEqualInFinishTime表示已完成待办和未完成待办数量一致
            AppLogger.BASIC.d(TAG, "initObservers toDoItems twoPane=$twoPane, hideDoneTodo=$hideDoneTodo,rebuild=$firstRebuild")
            val setDataFlag = ((!(isEditMode() && twoPane) && !areListsEqualInFinishTime) || firstRebuild)
            AppLogger.BASIC.d(
                TAG,
                "setDataFlag =$setDataFlag, areListsEqualInFinishTime=$areListsEqualInFinishTime"
            )
            if (setDataFlag) {
                //待办父子级编辑模式不更新列表
                adapter.setData(toDoItems, twoPane, sharedViewModel.currentTodoType, true)
            }
            loadDataFinished = true
            correctNavigationViewMenuState(toDoItems)
            if (mIsFirstLoadTodoList) {
                mIsFirstLoadTodoList = false
                val hasToDos = toDoItems.size > ToDoItem.TODO_HEADER_COUNT
                resetMainEmptyPage(hasToDos)
            } else {
                resetMainEmptyPageAndSyncTips(toDoItems)
            }
            initNotificationAnimator()

            var selectedSize = 0
            for (toDo in toDoItems) {
                if (toDo.isSelected && toDo.itemType == ToDoItem.ITEM_TYPE_TODO) {
                    selectedSize++
                }
            }
            selectItemSize = selectedSize
            if (!twoPane) {
                handleIsAnimating(selectedSize)
            }
            firstRebuild = false
        }
    }

    private fun initSortModeObserver() {
        todoListViewModel.mDragSortMode.observe(viewLifecycleOwner) {
            AppLogger.BASIC.d(TAG, "initObservers mDragSortMode: $it,twoPanel=${sharedViewModel.twoPane}")
            behavior?.setIsSortDrag(it)
            if (sharedViewModel.twoPane) {
                AppLogger.BASIC.d(
                    TAG,
                    "todoListViewModel.toDoItems.value ${todoListViewModel.toDoItems.value}"
                )
                tempTodoList = todoListViewModel.toDoItems.value
            }
            if (!sharedViewModel.twoPane) {
                if (it) {
                    adapter.enterDragMode()
                } else {
                    adapter.exitDragMode()
                    behavior?.updateToolbar()
                }
            }
        }
    }

    private fun observeCanSaveTodo() {
        todoListViewModel.canSave.observe(viewLifecycleOwner) {
            todoModalDialog?.updateCanSave(it)
        }
    }

    private fun handleIsAnimating(selectedSize: Int) {
        if (!isAnimating) {
            correctToolbarMenu()
            val isSelectionMode = isEditMode() && !sharedViewModel.twoPane
            correctTitleInfo(selectedSize, isSelectionMode)
        }
    }
    private fun observeGetFinished() {
        todoViewModel.getFinished(todoListViewModel.mCurrentDate).observe(viewLifecycleOwner, { toDos: List<ToDo> ->
            AppLogger.BASIC.d(TAG, "getFinished: " + toDos.size)
            val items = ArrayList<ToDoItem>()
            for (toDo in toDos) {
                items.add(ToDoItem(toDo, false, ToDoItem.ITEM_TYPE_TODO))
            }
            todoListViewModel.setFinishedToDoItems(items)
            correctToolbarMenu()
        })
    }

    private fun observesumToDoContentLength() {
        todoViewModel.sumToDoContentLengthDistribution { result: List<Int?>? ->
            result?.forEach {
                it?.apply {
                    StatisticsUtils.setEventToDoTextCount(activity, this)
                }
            }
        }
    }

    private fun observeTodoSelectionMode() {
        sharedViewModel.todoSelectionMode.observe(viewLifecycleOwner) { isSelectionMode: Boolean ->
            AppLogger.BASIC.d(TAG, "observeTodoSelectionMode isSelectionMode=$isSelectionMode")
            if (isSelectionMode) {
                isSelectionModeFirstInit = false
            }

            if (isSelectionModeFirstInit) {
                isSelectionModeFirstInit = false
                return@observe
            }

            todoListViewModel.mSelectionMode.value = isSelectionMode

            if (sharedViewModel.twoPane) {
                AppLogger.BASIC.d(TAG, "observeTodoSelectionMode two panel return")
                return@observe
            }
            doOnSelectionModeChanged(isSelectionMode)
        }
    }

    private fun doOnSelectionModeChanged(isSelectionMode: Boolean) {
        initToolNavigationMenu()
        updateNavigationViewMenuWithAnim(isSelectionMode)
        updateBehavior(isSelectionMode)
        toolbarAnimation()
        titleAnimation()

        if (isSelectionMode) {
            adapter.enterSelectionMode()
            binding?.todoList?.setFadingEdgeLength(resources.getDimensionPixelOffset(R.dimen.color_navigation_list_fading_edge_length))
        } else {
            adapter.exitSelectionMode()
            binding?.todoList?.setFadingEdgeLength(0)
        }

        infoNotifyController?.setSyncGuideViewState(!isSelectionMode, syncEnable == true)
    }

    private fun observeTodoDragMode() {
        sharedViewModel.dragSortMode.observe(viewLifecycleOwner) { isDragSortMode ->
            if (isDragSortMode == false && !sharedViewModel.twoPane) {
                StatisticsUtils.setEventTodoSort()
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun observePendingDeleteSelected() {
        todoListViewModel.mPendingDeleteSelectedToDos.observe(viewLifecycleOwner, { isPendingDeleteSelectedNotes: Boolean ->
            AppLogger.BASIC.d(TAG, "observePendingDeleteSelected $isPendingDeleteSelectedNotes")
            if (isPendingDeleteSelectedNotes) {
                val bundle = Bundle()
                bundle.putInt(Constants.SELECT_COUNT, todoListViewModel.selectedTodoCount)
                bundle.putBoolean(Constants.IS_SELECT_ALL, todoListViewModel.isAllToDosSelected)
                noteListHelperCallBack?.showTips(DialogFactory.TYPE_DIALOG_TODO_DELETE, bundle)
            } else {
                DialogUtils.safeDismissDialog(dialogFactory?.lastDialog)
            }
        })
    }

    private val callback = object : TodoAdapter.Callback {
        override fun onItemClick(item: ToDoItem) {
            if (isEditMode() || sharedViewModel.twoPane) {
                return
            }
            todoListViewModel.setToDoForEditing(item.toDo)
            showTodoModalDialog()
        }

        override fun onItemLongClick(
            item: ToDoItem,
            viewHolder: RecyclerView.ViewHolder,
            view: View,
            runnable: Runnable?,
            sysDragManager: SysDragManager
        ) {
            if (sharedViewModel.twoPane) {
                return
            }
            viewHolder.let {
                itemTouchHelper?.startDrag(it)
                adapter.isDragging = true
            }
        }

        override fun onItemChecked(item: ToDoItem, checked: Boolean) {
            AppLogger.BASIC.i(TAG, "onItemChecked")
        }

        override fun onItemDragStateChanged(isDrag: Boolean) {
            if (sharedViewModel.twoPane) {
                return
            }
            linearLayoutManager?.let {
                adapter.notifyDragStateChanged(isDrag, it.findFirstVisibleItemPosition(), it.findLastVisibleItemPosition())
            }
        }

        override fun onDrag(
            item: ToDoItem?,
            view: View?,
            runnable: Runnable?,
            vibrate: Boolean,
            sysDragManager: SysDragManager
        ) {
            AppLogger.BASIC.i(TAG, "onDrag")
        }

        override fun hasSelectionMode(): Boolean {
            return isEditMode() && !sharedViewModel.twoPane
        }

        override fun onItemMove(sourceTodo: ToDoItem?, targetTodo: ToDoItem?) {
            //not use
        }

        override fun onItemClear(source: RecyclerView.ViewHolder?) {
            //not use
        }

        override fun onItemMoveStart(source: RecyclerView.ViewHolder?) {
            //not use
        }

        override fun onItemMoveEnd(source: RecyclerView.ViewHolder?) {
            updateTitle()
            correctToolbarMenu()
        }

        override fun onGroupItemClick(isUndo: Boolean?) {
            AppLogger.BASIC.d(TAG, "onGroupItemClick $isUndo")
        }
    }

    private fun setOuterToDoAdapterCallback() {
        todoListViewModel.setOuterToDoAdapterCallback(callback)
    }

    private fun observeDeleteSelected() {
        todoListViewModel.mDeleteSelectedToDos.observe(viewLifecycleOwner) { toDos: List<ToDo> ->
            todoListViewModel.isDeletingOrHiding = true
            todoViewModel.deleteAll(toDos) {
                AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO)
                CloudSyncTrigger.sendDataChangedBroadcast(appContext)
                WidgetUtils.sendTodoDataChangedBroadcast(appContext)
                TodoSearchManager.notifyDataChange(isFullQuery = false)
            }

            statisticForDeleteTodo(toDos)
        }
    }

    private fun statisticForDeleteTodo(todos: List<ToDo>) {
        // 数据操作埋点
        todos.forEach { DataStatisticsHelper.todoUserOps(TAG, "01020103", it) }

        // 普通埋点
        var completeNumber = 0
        var unCompleteNumber = 0
        for (toDo in todos) {
            if (toDo.isComplete) {
                completeNumber++
            } else {
                unCompleteNumber++
            }
        }
        StatisticsUtils.setEventDeleteToDo(context, completeNumber, unCompleteNumber, todoListViewModel.isAllToDosSelected)
    }

    private fun observeSyncEnable() {
        todoListViewModel.mSyncEnable.observe(viewLifecycleOwner) { enable: Boolean ->
            guideManager?.queryNoteSyncCloudStateCompact(context,
                object : NoteSyncProcess.CloudSyncStateCallback {
                override fun refreshViewState(settingState: Int) {
                    AppLogger.NOTE.d(TAG, "todo queryNoteSyncCloudState state = $settingState")
                    if (guideManager?.getSyncSwitchState() != settingState || enable != syncEnable) {
                        guideManager?.updateSyncSwitchState(settingState)
                        syncEnable = enable
                    }
                    resetMainEmptyPageAndSyncTips(todoListViewModel.toDoItems.value)
                }

                override fun refreshModuleState(isSupport: Boolean) {
                    AppLogger.BASIC.i(TAG, "refreshModuleState $isSupport")
                }
            })
        }
    }

    private fun observeStoragePermission() {
        sharedViewModel.storagePermissionDenied.observe(viewLifecycleOwner, { denied: Boolean ->
            if (denied) {
                binding?.refresh?.visibility = View.INVISIBLE
            } else {
                binding?.refresh?.visibility = View.VISIBLE
            }
            val toDoItems: List<ToDoItem>? = todoListViewModel.toDoItems.value
            val hasToDos = toDoItems != null && toDoItems.size > ToDoItem.TODO_HEADER_COUNT
            resetMainEmptyPage(hasToDos)
        })
    }

    private fun initToolNavigationMenu() {
        if (!isAdded) {
            return
        }
        if (toolNavigationView == null) {
            toolNavigationView = editMenuStub.value?.inflate() as? COUINavigationView
            toolNavigationView?.setOnNavigationItemSelectedListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.todo_delete -> {
                        todoListViewModel.pendingDeleteSelectedToDos()
                    }
                }
                true
            }
            navigationAnimatorHelper = NavigationAnimatorHelper(requireContext())
            navigationAnimatorHelper?.initToolNavigationAnimator(toolNavigationView!!)
        }
    }

    private fun initNoteListHelper() {
        noteListHelperCallBack = object : NoteListHelper.CallBack {
            override fun exitRefreshing(tips: String?, delay: Int) {
                todoListViewModel.mCompleteRefreshWithTipsAndDelay.value = Pair(tips, delay)
            }

            override fun showTips(type: Int, arg: Bundle?) {
                showTipsDialog(type, arg)
            }

            override fun downloadSkin() {
                AppLogger.BASIC.i(TAG, "downloadSkin")
            }

            override fun setIsEncryptOrDecrypt(isEncryptOrDecrypt: Boolean) {
                AppLogger.BASIC.i(TAG, "setIsEncryptOrDecrypt")
            }

            override fun updateAdapterModeForListAndMenu(isGrid: Boolean) {
                AppLogger.BASIC.i(TAG, "updateAdapterModeForListAndMenu")
            }

            override fun turnToAllNoteFolder() {
                AppLogger.BASIC.i(TAG, "turnToAllNoteFolder")
            }
        }
        noteListHelper = NoteListHelper(noteListHelperCallBack)
        noteListHelper?.initData(activity, false)
    }

    private fun initDialogFactory() {
        AppLogger.BASIC.d(TAG, "initDialogFactory")
        dialogClickListener = object : DialogFactory.DialogOnClickListener {
            override fun onDialogClickButton(type: Int, index: Int) {
                AppLogger.BASIC.i(TAG, "onDialogClickButton index:$index")
            }

            override fun onDialogClickPositive(type: Int) {
                when (type) {
                    DialogFactory.TYPE_DIALOG_TODO_DELETE -> {
                        DeleteSoundUtils.playDeleteSound()
                        todoListViewModel.deleteSelectedToDos()
                    }

                    DialogFactory.DIALOG_TYPE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                        val isInMultiWindowMode = activity?.isInMultiWindowMode ?: false
                        UiHelper.turnToManageAppOverlayPage(activity, isInMultiWindowMode)
                        //儿童空间模式下，无法跳转到开启悬浮窗权限的界面
                        if (UiHelper.isChildrenMode() || UiHelper.isFocusMode()) {
                            onDialogClickNegative(type)
                        }
                    }
                    DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                        mCallBack?.startRefresh()
                    }
                    DialogFactory.TYPE_NOTIFICATION_APPLY -> toNotificationSetting(activity)
                    DialogFactory.TYPE_SCREEN_ON_APPLY -> {
                        CommonPermissionUtils.toScreenOnSetting(activity,
                                CommonPermissionUtils.GUIDE_PERMISSIONS_SCREEN_ON_CODE)
                    }
                    DialogFactory.TYPE_SCREEN_ON_APPLY_BY_FORCE -> {
                        CommonPermissionUtils.toScreenOnSetting(activity,
                                CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_SCREEN_CODE)
                    }
                    DialogFactory.TYPE_SCHEDULE_ALARM_APPLY -> {
                        CommonPermissionUtils.toScheduleAlarmSetting(activity,
                                CommonPermissionUtils.GUIDE_PERMISSIONS_ALARM_CODE)
                    }
                }
            }

            override fun onDialogClickNegative(type: Int) {
                when (type) {
                    DialogFactory.DIALOG_TYPE_ALERT_WINDOW_PERMISSION_REQUEST,
                    DialogFactory.TYPE_SCREEN_ON_APPLY_BY_FORCE -> {
                        todoModalDialog?.onDialogClickNegative();
                    }
                    DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                        binding?.refresh?.setRefreshCompleted()
                    }
                }

            }

            override fun onDialogDismiss(type: Int) {
                if (type == DialogFactory.TYPE_DIALOG_TODO_DELETE) {
                    todoListViewModel.mPendingDeleteSelectedToDos.setValue(false)
                }
                if (CheckNextAlarmUtils.isSpecialPermission(type)) {
                    todoListViewModel.toDoItems.value?.let {
                        resetMainEmptyPageAndSyncTips(it)
                    }
                }
            }
        }
        dialogFactory = DialogFactory(activity, dialogClickListener)
    }

    private fun showTipsDialog(type: Int, bundle: Bundle?): Dialog? {
        AppLogger.BASIC.d(TAG, "showTipsDialog")
        if (dialogFactory == null) {
            initDialogFactory()
        }
        return dialogFactory?.showDialog(type, bundle)
    }

    private fun updateTitle() {
        val todoCount: Int = todoListViewModel.todoCount
        val subtitle = resources.getQuantityString(com.oplus.note.baseres.R.plurals.n_todo, todoCount, todoCount)
        val visibility: Int

        if (isEditMode() && !sharedViewModel.twoPane) {
            visibility = View.INVISIBLE
            correctTitleInfo(selectItemSize, true)
        } else {
            binding?.toolbar?.title = ""
            binding?.mainTitle?.setText(R.string.todo_app_name)
            visibility = if (todoCount > 0) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
        }
        if (!ConfigUtils.isUseCloudKit) {
            binding?.subTitle?.text = subtitle
            binding?.subTitle?.visibility = visibility
            binding?.subTitleView?.root?.visibility = View.GONE
        } else {
            binding?.subTitle?.visibility = View.GONE
            guideManager?.updateSubTitle(subtitle, visibility)
        }
        setSubtitleViewVisibility()
    }
    /**
     * 更新副标题 高度，及 mPlaceHolderView 高度
     */
    private fun setSubtitleViewVisibility(mWaitTime: Long = 0L) {
        val subTitleView = if (!ConfigUtils.isUseCloudKit) {
            binding?.subTitle as TextView
        } else {
            binding?.subTitleView?.root
        }
        val hasToDos = todoListViewModel.todoCount > 0
        mSubTitleViewHelper.updateSubtitleViewHeight(
            subTitleView = subTitleView,
            adapter = adapter,
            behavior = behavior,
            refreshEnable = false,
            hasList = hasToDos,
            mPlaceHolderViewHeight = mPlaceHolderViewHeight,
            isCloudSyncing = nowShowSyncTip
        )
    }

    private fun isEditMode(): Boolean {
        return sharedViewModel.todoSelectionMode.value == true
    }

    private fun updateNavigationViewMenuWithAnim(isSelectionMode: Boolean) {
        if (isSelectionMode) {
            toolNavigationView?.inflateMenu(R.menu.navi_menu_todo)
            navigationAnimatorHelper?.showToolNavigation {
                if (todoListViewModel.isDeleteDialogRebuild) {
                    todoListViewModel.pendingDeleteSelectedToDos()
                    todoListViewModel.isDeleteDialogRebuild = false
                }
            }
        } else {
            navigationAnimatorHelper?.dismissToolNavigation()
        }
    }

    private fun updateBehavior(isEditMode: Boolean) {
        behavior?.setIsEditMode(isEditMode)
        var marginStart = 0
        if (isEditMode) {
            marginStart = supportTitleMarginStart
        }
        binding?.toolbar?.titleMarginStart = marginStart
    }

    private fun toolbarAnimation() {
        val toolbarOutAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 1f, 0f)
        val toolbarInAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet()
        toolbarAnimatorSet.duration = ALPHA_DURATION
        toolbarAnimatorSet.interpolator = LinearInterpolator()
        toolbarAnimatorSet.play(toolbarOutAnimation).before(toolbarInAnimation)
        toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                if (!isAdded) {
                    return
                }

                isAnimating = false
                val isSelectionMode = isEditMode() && !sharedViewModel.twoPane
                if (isSelectionMode) {
                    correctTitleInfo(selectItemSize, true)
                }
                updateTitle()
                correctToolbarMenu()
                behavior?.apply {
                    val hasTodos = todoListViewModel.todoCount != 0
                    setScaleEnable(hasTodos)
                    val isRunning = binding?.todoList?.itemAnimator?.isRunning
                    AppLogger.BASIC.d(TAG, "toolbarAnimationEnd, todoList itemAnimator isRunning=$isRunning")

                    if (isRunning == true) {
                        updateTitleMargin()
                    } else {
                        updateToolbar()
                    }
                }
            }
        })
        isAnimating = true
        behavior?.setScaleEnable(false)
        toolbarAnimatorSet.start()
    }

    /**
     * Alpha animation of primary title when switch between normal mode and edit mode
     */
    private fun titleAnimation() {
        val alphaOutAnimation = ObjectAnimator.ofFloat(binding?.titleContainer, "alpha", 1f, 0f)
        val alphaInAnimation = ObjectAnimator.ofFloat(binding?.titleContainer, "alpha", 0f, 1f)
        val animationSet = AnimatorSet()
        animationSet.interpolator = LinearInterpolator()
        animationSet.duration = ALPHA_DURATION
        animationSet.play(alphaOutAnimation).before(alphaInAnimation)
        animationSet.start()
    }

    /**
     * 取消按钮、标题、全选按钮文本变换时 需要更新标题位置
     */
    private fun updateUpdateToolbarWhenTextChange() {
        view?.post {
            behavior?.updateToolbar()
        }
    }

    private fun correctTitleInfo(selectedSize: Int, isSelectionMode: Boolean) {
        if (isSelectionMode) {
            if (selectedSize == 0) {
                binding?.mainTitle?.let { mainTitle ->
                    mainTitle.text = getString(com.oplus.note.baseres.R.string.memo_select_note)
                    updateUpdateToolbarWhenTextChange()
                }
            } else {
                val isAllToDoSelected: Boolean = todoListViewModel.isAllToDosSelected
                binding?.mainTitle?.let { mainTitle ->
                    if (isAllToDoSelected) {
                        mainTitle.text = getString(com.oplus.note.baseres.R.string.memo_note_select_all)
                        updateUpdateToolbarWhenTextChange()
                    } else {
                        mainTitle.text = getString(com.oplus.note.baseres.R.string.memo_note_select_num, selectedSize.toString())
                        updateUpdateToolbarWhenTextChange()
                    }
                }
            }
        }
    }

    private fun correctToolbarMenu() {
        AppLogger.BASIC.d(TAG, "correctToolbarMenu isEditMode=${isEditMode()}")
        val menu: Menu? = binding?.toolbar?.menu
        if ((menu == null) || (menu.size() == 0)) {
            return
        }
        val cancel = menu.findItem(R.id.cancel)
        val select = menu.findItem(R.id.select_all)
        val edit = menu.findItem(R.id.edit_todo)
        val toggleFinishedTodo = menu.findItem(R.id.toggle_finished_todo)
        val setting = menu.findItem(R.id.jump_setting)
        val emptyItem = menu.findItem(R.id.empty)
        if (cancel == null || select == null || edit == null
            || toggleFinishedTodo == null || setting == null
        ) {
            return
        }

        val isSelectionMode = isEditMode() && !sharedViewModel.twoPane
        var value: List<ToDoItem>? = todoListViewModel.toDoItems.value
        if (value == null) {
            value = emptyList()
        }

        var hasFinishedTodos = false
        val hasToDos = value.size > ToDoItem.TODO_HEADER_COUNT
        val isAllToDoSelected: Boolean = todoListViewModel.isAllToDosSelected

        if (!hasToDos) {
            hasFinishedTodos = todoListViewModel.hasFinishedTodos()
        }
        if (!PrimaryTitleBehavior.SHOW_MENU_TEXT_BTN) {
            cancel.setIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
        }
        cancel.isVisible = isSelectionMode
        select.isVisible = isSelectionMode
        edit.isVisible = !isSelectionMode && hasToDos && !sharedViewModel.twoPane
        emptyItem.isVisible = false
        toggleFinishedTodo.isVisible = !isSelectionMode && (hasToDos || hasFinishedTodos)

        val isHide: Boolean = todoListViewModel.mHideFinishedTodo.value ?: false
        if (isHide) {
            toggleFinishedTodo.setTitle(R.string.show_finished_todo)
        } else {
            toggleFinishedTodo.setTitle(R.string.hide_finished_todo)
        }

        setting.isVisible = !isSelectionMode
        if (!PrimaryTitleBehavior.SHOW_MENU_TEXT_BTN) {
            select.setIcon(if (isAllToDoSelected) {
                com.support.appcompat.R.drawable.coui_btn_check_on_normal
            } else {
                com.support.appcompat.R.drawable.coui_btn_check_off_normal
            })
        }
        select.setTitle(if (isAllToDoSelected) R.string.deselect_all else R.string.select_all)
        behavior?.setScaleEnable(hasToDos)
    }

    private fun correctNavigationViewMenuState(items: List<ToDoItem>) {
        var hasSelected = false
        for (item in items) {
            if (item.isSelected) {
                hasSelected = true
                break
            }
        }
        updateTodoDeleteMenuState(hasSelected)
    }

    private fun updateTodoDeleteMenuState(hasSelected: Boolean) {
        if (toolNavigationView == null) {
            return
        }
        val item: MenuItem? = toolNavigationView!!.menu.findItem(R.id.todo_delete)
        item?.isEnabled = hasSelected
    }

    fun fabMainActionSelected(content: String? = null, alarmTime: Long = 0) {
        if (!isAdded) {
            return
        }

        if (MultiClickFilter.isEffectiveClick()) {
            todoListViewModel.createToDoForEditing(content, alarmTime)
            showTodoModalDialog()
        }
    }

    private fun initReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_TIME_CHANGED)
        filter.addAction(Intent.ACTION_DATE_CHANGED)
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED)
        filter.addAction(ACTION_UPDATE_ITEM_EXPIRED)
        ContextCompat.registerReceiver(appContext, timeChangeReceiver, filter, ContextCompat.RECEIVER_EXPORTED)
    }

    private fun initEmptyPage() {
        val stub: ViewStub? = binding?.emptyContentStub?.viewStub
        AppLogger.BASIC.d(TAG, "initEmptyPage stub=${stub == null}")
        if (stub != null) {
            emptyContentPage = stub.inflate() as EmptyContentView
            mEmptyContentPageHelper = ImageHelper(requireActivity())
            AppLogger.BASIC.d(TAG, "initEmptyPage:")

            emptyContentPage?.init(mEmptyContentPageHelper, TAG)
            emptyContentPage?.setPageClickListener(object : EmptyContentView.EmptyPageClickListener {
                override fun onSwitch() {
                    if (context != null && isPackageDisabled(
                            MbaUtils.PACKAGER_CLOUD,
                            requireContext()
                        )
                    ) {
                        MbaUtils.showMbaCloudDialog(requireContext())
                    } else {
                        NoteSyncProcess.startCloudSettingActivity(context)
                    }
                }
            })
        }
    }

    private fun resetMainEmptyPageAndSyncTips(toDoItems: List<ToDoItem>?) {
        val hasToDos = toDoItems != null && toDoItems.size > ToDoItem.TODO_HEADER_COUNT
        resetMainEmptyPage(hasToDos)
        if (guideManager == null || infoNotifyController == null || context == null) {
            return
        }
        refreshSyncTips(hasToDos)
    }

    fun refreshSyncTips(hasTodo: Boolean? = null) {
        val hasToDos = if (hasTodo == null) {
            val size = todoListViewModel.toDoItems.value?.size ?: 0
            size > ToDoItem.TODO_HEADER_COUNT
        } else {
            hasTodo
        }
        guideManager?.getCloudOperation(context, syncEnable, lifecycleScope) { cloudOperationShow ->
            if (cloudOperationShow) {
                AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips cloudOperationShow")
                return@getCloudOperation
            }
            showCloudSyncTipCard(hasToDos) { cloudSyncTipCardShow ->
                if (cloudSyncTipCardShow) {
                    AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips cloudSyncTipCardShow")
                    return@showCloudSyncTipCard
                }
                context?.let {
                    if (!CheckNextAlarmUtils.getNotificationsEnabled(it)) {
                        guideManager?.showNotifyGuideView(it as Activity, hasToDos, syncEnable)
                    } else if (!CommonPermissionUtils.getScheduleAlarmEnabled(it)) {
                        guideManager?.showAlarmGuideView(it as Activity, hasToDos, syncEnable)
                    } else if (!CommonPermissionUtils.getScreenOnEnabled(it)) {
                        guideManager?.showScreenOnGuideView(it as Activity, hasToDos, syncEnable)
                    } else if (!CommonPermissionUtils.getOverlayEnabled(it)) {
                        guideManager?.showOverlayGuideView(it as Activity, hasToDos, syncEnable)
                    } else {
                        guideManager?.hideSyncGuideView()
                    }
                }
            }
        }
    }

    private fun showCloudSyncTipCard(hasToDos: Boolean, showCallBack: ((show: Boolean) -> Unit)? = null) {
        AppLogger.BASIC.d(TAG, "showCloudSyncTipCard")
        lifecycleScope.launch(Dispatchers.Main) {
            val isLogin = withContext(Dispatchers.IO) {
                AccountManager.isLogin(activity)
            }
            //已登录，但云同步服务不可用
            val cloudDisable = isLogin && !CloudKitGlobalStateManager.cloudEnable()
            if (OsConfigurations.isMultiSystem || (context != null && !DeviceInfoUtils.isAppInstalled(context, MbaUtils.PACKAGER_CLOUD))
                || cloudDisable
            ) {
                guideManager?.hideSyncGuideView()
                showCallBack?.invoke(false)
            } else {
                if (syncEnable != false) {
                    showCallBack?.invoke(false)
                } else {
                    guideManager?.showCloudSyncTipView(hasToDos, showCallBack)
                }
            }
            infoNotifyController?.setSyncGuideViewState(!isEditMode(), syncEnable == true)
        }
    }

    fun resetMainEmptyPage() {
        if (!isAdded) {
            return
        }
        if (todoListViewModel.toDoItems.value != null) {
            val toDoItems = todoListViewModel.toDoItems.value
            val hasToDos = toDoItems != null && toDoItems.size > ToDoItem.TODO_HEADER_COUNT
            resetMainEmptyPage(hasToDos)
        }
    }

    fun refreshNoteListTips() {
        resetMainEmptyPageAndSyncTips(todoListViewModel.toDoItems.value)
    }

    private fun resetMainEmptyPage(hasToDos: Boolean) {
        if (binding == null) {
            return
        }

        val isInMultiWindowMode = activity?.isInMultiWindowMode ?: false
        //分屏的时候,如果折叠屏展开且在竖屏情况下,需要显示空页面
        var isFoldingModeOpen = false
        if (isInMultiWindowMode) {
            isFoldingModeOpen = UIConfigMonitor.isFoldingModeOpen(context)
        }

        val isPrivacyDenied = (context == null) || !PrivacyPolicyHelper.isAgreeUserNotice(context)
        if (isPrivacyDenied && (!isInMultiWindowMode || isFoldingModeOpen)) {
            return
        }
        if (sharedViewModel.twoPane) {
            emptyContentPage?.display(EmptyContentView.State.STATE_HIDE, false)
            AppLogger.BASIC.d(TAG, "resetMainEmptyPage return by two panel")
            return
        }
        val isNeedHide = (isInMultiWindowMode && !isFoldingModeOpen && !UiHelper.isDevicePad())
        if (!loadDataFinished || hasToDos || isNeedHide) {
            emptyContentPage?.display(EmptyContentView.State.STATE_HIDE, false)
        } else if (OsConfigurations.isMultiSystem || !DeviceInfoUtils.isAppInstalled(
                context,
                MbaUtils.PACKAGER_CLOUD
            )
        ) {
            emptyContentPage?.display(EmptyContentView.State.NO_TODO_CONTENT, false)
        } else {
            if (syncEnable == true) {
                emptyContentPage?.display(EmptyContentView.State.PULL_RECOVERY_TODO, false)
            } else if (ConfigUtils.isUseCloudKit) {
                if (OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)) {
                    emptyContentPage?.display(EmptyContentView.State.STATE_HIDE, false)
                } else {
                    emptyContentPage?.display(EmptyContentView.State.NO_TODO_CONTENT, false)
                }
            } else {
                emptyContentPage?.display(EmptyContentView.State.SYNC_SWITCH, false)
            }
        }
    }

    private fun showTodoModalDialog() {
        if (!isAdded || activity?.isFinishing == true) {
            return
        }
        if (todoModalDialog != null && todoModalDialog?.isShowing == true) {
            todoModalDialog?.setToolbarTitle()
            return
        }
        todoListViewModel.showTodoEditDialog = true
        viewLifecycleOwner.lifecycleScope.launch {
            val isInWindowFloatingMode: Boolean
            val switchColor: Int
            withContext(Dispatchers.IO) {
                isInWindowFloatingMode = OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)
                switchColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            }
            todoModalDialog = TodoModalDialog(requireActivity(), R.style.ToDoDialogStyle, DialogUseMode.NOTE_INSIDE, switchColor).apply {
                hasInWindowFloatingMode = isInWindowFloatingMode
            }
            todoModalDialog?.apply {
                mViewModel = todoListViewModel
                mLifecycleOwner = this@TodoFragment
                setCheckPermissionCallback(object : TodoModalDialog.CheckPermissionCallback {
                    override fun showDialog(type: Int, bundle: Bundle?): Dialog? {
                        return showTipsDialog(type, bundle)
                    }

                    override fun checkPermission(forceReminder: Boolean, function: () -> Unit) {
                        //隐藏输入法
                        FocusInfo.hideSoftInput(activity, <EMAIL>?.decorView)
                        alarmPermissionHelper.checkAlarmPermissions(
                            permissionManager,
                            activity,
                            0L,
                            forceReminder,
                            object : RequestResultCallback {
                                override fun onEnd(granted: Boolean) {
                                    function.invoke()

                                    if (!granted) {
                                        //未授予权限，显示引导tips
                                        CloudKitInfoController.resetPermissionGuideState()
                                        resetMainEmptyPageAndSyncTips(todoListViewModel.toDoItems.value)
                                    }
                                    AppLogger.BASIC.d(TAG, "RequestResultCallback onEnd granted=$granted")
                                }
                            })
                    }
                })
                setOnDismissListener {
                    clearListenerAndDestroy()
                    todoModalDialog = null
                }
                setDialogListener { type ->
                    if (CheckNextAlarmUtils.isSpecialPermission(type)) {
                        todoListViewModel.toDoItems.value?.let {
                            resetMainEmptyPageAndSyncTips(it)
                        }
                    }
                }
                setSaveListener {
                    if (sharedViewModel.twoPane) {
                        (activity?.supportFragmentManager?.findFragmentByTag(TodoListFragment.TAG) as? TodoListFragment)?.scrollToPosition(0)
                    } else {
                        scrollToPosition(0)
                    }
                }
                if (!isShowing) {
                    show()
                }
            }
        }
    }

    private fun initNotificationAnimator() {
        AppLogger.BASIC.d(TAG, "initNotificationAnimator isNotificationInit=$isNotificationInit")
        if (isNotificationInit) {
            sharedViewModel.notificationUUID.observe(viewLifecycleOwner) { uuid: String? ->
                if (TextUtils.isEmpty(uuid)) {
                    AppLogger.BASIC.d(TAG, "notificationUUID observe, uuid is empty")
                    return@observe
                }
                var position = -1
                val toDoItemList: List<ToDoItem>? = todoListViewModel.toDoItems.value
                toDoItemList?.apply {
                    for (i in toDoItemList.indices) {
                        if (uuid == toDoItemList[i].toDo.localId.toString()) {
                            position = i
                            break
                        }
                    }

                    if (position > -1) {
                        scrollToPosition(position)
                    } else {
                        AppLogger.BASIC.d(TAG, "current position $position can not be found")
                    }
                }
                AppLogger.BASIC.d(
                    TAG,
                    "notificationUUID observe uuid=$uuid, toDoItemList.size=${toDoItemList?.size}, position=$position"
                )
            }
            isNotificationInit = false
        }
    }

    private fun scrollToPosition(position: Int) {
        binding?.todoList?.scrollToPosition(position)
        binding?.todoList?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                (binding?.todoList?.layoutManager as? COUILinearLayoutManager)
                    ?.scrollToPositionWithOffset(position, binding?.appBar?.height ?: 0)
                binding?.todoList?.postDelayed({
                    binding?.todoList?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                }, DELAY_REMOVE_ONGLOBAL_LAYOUT)
            }
        })
    }

    fun unSelectedAllTodos() {
        todoListViewModel.selectAll(false)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (todoModalDialog?.isShowing == false) {
            AppLogger.BASIC.d(TAG, " todo dialog is not show,nothing todo ")
            return
        }
        AppLogger.BASIC.d(TAG, "[F] requestCode $requestCode")
        when (requestCode) {
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_NOTIFY_CODE -> {
                if (CheckNextAlarmUtils.permisionNotifyAlarmScreenOnGranded(requireContext())) {
                    todoModalDialog?.onRemindAreaClick()
                }
            }

            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_NOTIFY_CODE -> {
                if (CheckNextAlarmUtils.permisionNotifyAlarmScreenOnGranded(requireContext())) {
                    if (WidgetUtils.isPrivacyDenied(requireContext())) {
                        Toast.makeText(context, R.string.save_todo_failed, Toast.LENGTH_SHORT).show()
                        return
                    }
                    todoModalDialog?.saveTodo()
                    todoModalDialog?.dismiss()
                }
            }

            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_SCREEN_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_ALERT_CODE -> {
                todoModalDialog?.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    private fun showNotificationPermissionDialog() {
        showTipsDialog(DialogFactory.TYPE_NOTIFICATION_APPLY, null)
    }

    /**
     * 跳转应用详情页面，可手动设置通知权限
     */
    fun toNotificationSetting(activity: Activity?) {
        activity?.apply {
            if (PermissionsBlockedUtils.getCanNavigateToAppPermissions(this)  && !com.oplus.note.BuildConfig.isExport) {
                val intent = Intent(PermissionsBlockedUtils.ACTION_SAFE_CENTER_PERMISSION)
                val bundle = Bundle()
                val arrayList = ArrayList<String>()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    arrayList.add(Manifest.permission.POST_NOTIFICATIONS)
                }
                bundle.putStringArrayList(PermissionsBlockedUtils.PERMISSION_LIST, arrayList)
                bundle.putString(
                    PermissionsBlockedUtils.PACKAGE_NAME_STRING,
                    activity.applicationContext.packageName
                )
                intent.putExtras(bundle)
                try {
                    activity.startActivity(intent)
                } catch (e: SecurityException) {
                    AppLogger.BASIC.e(
                        TAG,
                        "${PermissionsBlockedUtils.ACTION_SAFE_CENTER_PERMISSION} permission error  ${e.message}"
                    )
                    PermissionsBlockedUtils.setCanNavigateToAppPermissions(false)
                    defaultToNotificationSetting(this)
                }
            } else {
                defaultToNotificationSetting(this)
            }
        } ?: kotlin.run {
            AppLogger.BASIC.e(TAG, "activity is null")
        }
    }

    private fun defaultToNotificationSetting(activity: Activity) {
        val intent = Intent()
        val packageName: String = activity.applicationContext.packageName
        val extraPackageName = "android.intent.extra.PACKAGE_NAME"
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.parse("package:$packageName")
        intent.putExtra(extraPackageName, packageName)
        kotlin.runCatching {
            this.startActivity(intent)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "package not found:${it.message}}")
        }
    }

    inner class LocalReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null || !isAdded) {
                return
            }
            if (intent.action == Constants.ACTION_NOTIFICATION_GRANT) {
                resetMainEmptyPageAndSyncTips(todoListViewModel.toDoItems.value)
            }
        }
    }

    fun removeToDoCardExtra() {
        activity?.intent?.removeExtra(PrefUtils.MAIN_ACTION_FROM)
        activity?.intent?.removeExtra(PrefUtils.APP_TODO_CARD_LOCAL_ID)
        activity?.intent?.removeExtra(FORCE_REMINDER)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        dialogFactory?.let {
            if ((it.lastDialog?.isShowing == true) && (it.dialogType == DialogFactory.TYPE_DIALOG_TODO_DELETE)) {
                outState.putBoolean(Constants.DIALOG_REBUILD_TAG, true)
                todoListViewModel.mPendingDeleteSelectedToDos.value = false
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AppLogger.BASIC.d(TAG, "onConfigurationChanged")
        emptyContentPage?.update(mEmptyContentPageHelper, false)
        showTodoListIfNeed()
        val shouldShow = !sharedViewModel.twoPane || (sharedViewModel.currentTodoType != TodoListFragment.TODO_DONE)
        if (sharedViewModel.currentTabIndex.value == MainActivity.TODO_INDEX) {
            //选中的是待办才从这里更新
            sharedViewModel.fabAnimatorHelper?.changeFloatButtonState(shouldShow)
        }
    }

    private val updateSubTitleViewTask = Runnable {
        setSubtitleViewVisibility()
    }

    private fun setStatistics() {
        val context = activity ?: return
        val isFromAppCard = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.MAIN_ACTION_FROM, "")
        when (isFromAppCard) {
            PrefUtils.APP_TODO_CARD_NEW -> StatisticsNoteCard.setEventAddTodo(context, context.intent)
            PrefUtils.APP_TODO_CARD_EDIT,
            PrefUtils.APP_TODO_CARD -> StatisticsNoteCard.setEventOpenTodoList(context, context.intent)
        }
    }
}