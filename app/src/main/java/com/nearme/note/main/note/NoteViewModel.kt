/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteViewModel.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/29
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class NoteViewModel(
    private val impl: SelectedRichNoteViewModel = SelectedRichNoteViewModelDefaultImpl()
) : ViewModel(), SelectedRichNoteViewModel by impl {
    val noteDataChanged = MutableLiveData<Pair<RichNoteWithAttachments, Folder>>()
    val sortRuleChanged = MutableLiveData<Int>()
    var noteCount = MutableLiveData<Int>()
    var notifyDetailSaveData = MutableLiveData<Int>()
    var mLastSearchList: MutableList<String> = arrayListOf()
    var searchAttachmentId: String = ""
    var forceUpdate = false
    var searchPair: Pair<String, Boolean>? = null
}