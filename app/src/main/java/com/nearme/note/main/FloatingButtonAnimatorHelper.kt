/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - FloatingButtonAnimatorHelper
 ** Description:
 **         v1.0:  init float button position
 **
 ** Version: 1.0
 ** Date: 2023/04/21
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/09/15  Adapted to OS14 design
 ********************************************************************************/
package com.nearme.note.main

import android.animation.Animator
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.PathInterpolator
import androidx.annotation.VisibleForTesting
import androidx.core.animation.doOnEnd
import androidx.core.view.isVisible
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.TwoPaneCalculateWidthUtils
import com.nearme.note.util.updateMarginLayoutParams
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.ResponsiveUiHelper

class FloatingButtonAnimatorHelper {
    companion object {
        private const val TAG = "FloatingButtonAnimatorHelper"
        private const val DEFAULT_MASK_DISAPPEAR_DURATION = 270L
        private const val DEFAULT_MASK_APPEAR_DURATION = 400L
        private val DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR = PathInterpolator(0.3f, 0f, 1f, 1f)
    }

    var floatButtonShouldShow = false

    @VisibleForTesting
    var scaleAnimation: ValueAnimator? = null

    @VisibleForTesting
    var floatingButton: COUIFloatingButton? = null

    @VisibleForTesting
    var viewStubFabMaskView: ViewStub? = null
    var fabMaskView: View? = null
    var systemBarsRight = 0
    //系统底部状态栏高度
    var systemBarsBottom = 0

    fun initViews(fab: COUIFloatingButton?, fabMask: ViewStub?) {
        floatingButton = fab
        viewStubFabMaskView = fabMask
    }

    fun changeFloatButtonState(shouldShow: Boolean) {
        // It's better to make a animation here
        if (floatButtonShouldShow == shouldShow) {
            return
        }

        floatButtonShouldShow = shouldShow
        AppLogger.BASIC.d(TAG, "changeFloatButtonState shouldShow=$shouldShow")
        if (scaleAnimation?.isRunning == true) {
            //如果同时切换tab页的缩放动效也在运行，则不执行下面动效，在scaleAnimation?.doOnEnd里面直接设置visibility。因为doOnEnd里面执行了放大动效，确保floatingButton能显示出来
            scaleAnimation?.doOnEnd {
                AppLogger.BASIC.d(TAG, "changeFloatButtonState doOnEnd")
                floatingButton?.isVisible = shouldShow
            }
            AppLogger.BASIC.d(TAG, "changeFloatButtonState return scaleAnimation running")
            return
        }
        if (shouldShow) {
            floatingButton?.animationFloatingButtonEnlarge()
            floatingButton?.mainFloatingButton?.animate()
                ?.setListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {
                        floatingButton?.visibility = View.VISIBLE
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        AppLogger.BASIC.d(TAG, "onAnimationEnd true")
                        floatingButton?.visibility = View.VISIBLE
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        AppLogger.BASIC.d(TAG, "onAnimationCancel true")
                        floatingButton?.visibility = View.VISIBLE
                    }

                    override fun onAnimationRepeat(animation: Animator) {
                    }
                })
        } else {
            val listener: Animator.AnimatorListener = object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {}
                override fun onAnimationEnd(animation: Animator) {
                    AppLogger.BASIC.d(TAG, "onAnimationEnd false")
                    floatingButton?.visibility = View.GONE
                }

                override fun onAnimationCancel(animation: Animator) {
                    AppLogger.BASIC.d(TAG, "onAnimationCancel false")
                    floatingButton?.visibility = View.GONE
                }

                override fun onAnimationRepeat(animation: Animator) {}
            }
            val animator: ValueAnimator? = floatingButton?.animationFloatingButtonShrink(listener)
            animator?.start()
        }
    }

    fun startFabChangePageAnimation(activity: Activity, currentTabIndex: Int, twoPane: Boolean, inZoomWindow: Boolean) {
        AppLogger.BASIC.d(TAG, "startFabChangePageAnimation")
        if (scaleAnimation?.isRunning == true) {
            scaleAnimation?.cancel()
        }

        val listener: Animator.AnimatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {
                AppLogger.BASIC.d(TAG, "startFabChangePageAnimation onAnimationStart")
            }

            override fun onAnimationEnd(animation: Animator) {
                AppLogger.BASIC.d(TAG, "startFabChangePageAnimation onAnimationEnd")
                updateFabMarginEnd(activity, twoPane, currentTabIndex, inZoomWindow, true)
                updateFabMarginBottom(activity, twoPane, currentTabIndex, isZoomWindow = inZoomWindow)
                createFabScaleAnimation(true, null).start()
            }

            override fun onAnimationCancel(animation: Animator) {
                AppLogger.BASIC.d(TAG, "startFabChangePageAnimation onAnimationCancel")
            }

            override fun onAnimationRepeat(animation: Animator) {}
        }

        scaleAnimation = createFabScaleAnimation(false, listener)
        scaleAnimation?.start()
    }

    private fun createFabScaleAnimation(isShow: Boolean, listener: Animator.AnimatorListener?): ValueAnimator {
        val scaleInterpolator = PathInterpolator(0.3f, 0.0f, 0.1f, 1.0f)
        val alphaProperty = PropertyValuesHolder.ofFloat("alpha", if (isShow) 0.0f else 1.0f, if (isShow) 1.0f else 0.0f)
        val scaleXProperty = PropertyValuesHolder.ofFloat("scaleX", if (isShow) 0.6f else 1.0f, if (isShow) 1.0f else 0.6f)
        val scaleYProperty = PropertyValuesHolder.ofFloat("scaleY", if (isShow) 0.6f else 1.0f, if (isShow) 1.0f else 0.6f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(alphaProperty, scaleXProperty, scaleYProperty)
        anim.duration = 350L
        anim.interpolator = scaleInterpolator
        anim.addUpdateListener { animation ->
            floatingButton?.mainFloatingButton?.apply {
                alpha = animation.getAnimatedValue("alpha") as Float
                scaleX = animation.getAnimatedValue("scaleX") as Float
                scaleY = animation.getAnimatedValue("scaleY") as Float
            }
        }

        if (listener != null) {
            anim.addListener(listener)
        }

        return anim
    }

    /**
     * 蒙层消失动效
     *
     * @param normal 动效时长
     */
    @Suppress("MagicNumber")
    fun maskAnimationDisappear(normal: Boolean = false) {
        fabMaskView?.apply {
            visibility = View.VISIBLE
            alpha = 1f

            animate().apply {
                if (normal) {
                    duration = 350
                    interpolator = COUIMoveEaseInterpolator()
                } else {
                    duration = DEFAULT_MASK_DISAPPEAR_DURATION
                    interpolator = DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR
                }
                alpha(0f)
            }.withEndAction {
                visibility = View.GONE
            }.start()
        }
    }

    private fun inflateViewStub() {
        if (fabMaskView == null) {
            runCatching {
                val view = viewStubFabMaskView?.inflate()
                fabMaskView = view?.findViewById(R.id.mask_view)
            }
        }
    }

    /**
     * 蒙层显示动效
     *
     * @param normal 动效时长
     */
    @Suppress("MagicNumber")
    fun maskAnimationAppear(normal: Boolean = false) {
        inflateViewStub()
        fabMaskView?.apply {
            alpha = 0f
            visibility = View.VISIBLE

            animate().apply {
                if (normal) {
                    duration = 350
                    interpolator = COUIMoveEaseInterpolator()
                } else {
                    duration = DEFAULT_MASK_APPEAR_DURATION
                    interpolator = DEFAULT_MASK_ANIMATION_PATH_INTERPOLATOR
                }
                alpha(1f)
            }.start()
        }
    }

    fun updateFabMarginEnd(
        activity: Activity,
        isTwoPanel: Boolean,
        currentTabIndex: Int,
        isZoomWindow: Boolean = false,
        invalidate: Boolean = false
    ) {
        var marginEnd = if (currentTabIndex == MainActivity.NOTE_INDEX) {
            if (isTwoPanel && !isZoomWindow) {
                getFabMarginEndInternal(activity) + TwoPaneCalculateWidthUtils.getTwoPaneDetailSizeSafe(
                    activity
                )
            } else {
                getFabMarginEndInternal(activity)
            }
        } else {
            getFabMarginEndInternal(activity)
        }

        marginEnd += systemBarsRight

        if (((floatingButton?.layoutParams as? ViewGroup.MarginLayoutParams)?.marginEnd != marginEnd) && invalidate) {
            floatingButton?.layoutParams =
                (floatingButton?.layoutParams as? ViewGroup.MarginLayoutParams)?.apply {
                    this.marginEnd = marginEnd
                }
        }
        AppLogger.BASIC.d(
            TAG, "twoPane:$isTwoPanel,marginEnd:$marginEnd, invalidate:$invalidate"
        )
    }

    /**
     * 更新新建按钮底部间距，待办父子级模式下，新建按钮底部间距跟笔记列表不一样，没有导航栏的高度
     * @param systemBarHeight 手势栏的高度
     */
    fun updateFabMarginBottom(
        activity: Activity,
        isTwoPanel: Boolean,
        currentTabIndex: Int,
        systemBarHeight: Int = systemBarsBottom,
        isZoomWindow: Boolean = false
    ) {
        AppLogger.BASIC.d(TAG,
            "updateFabMarginBottom isTwoPanel=$isTwoPanel,tabIndex=$currentTabIndex,systemBarHeight=$systemBarHeight,isZoomWindow=$isZoomWindow"
        )
        val marginBottom = if (ConfigUtils.isToDoDeprecated) {
            systemBarHeight
        } else {
            if (isTwoPanel) {
                if (currentTabIndex == MainActivity.TODO_INDEX && !isZoomWindow) {
                    systemBarHeight
                } else {
                    systemBarHeight + activity.resources.getDimensionPixelSize(com.support.bottomnavigation.R.dimen.coui_tool_navigation_item_height)
                }
            } else {
                systemBarHeight + activity.resources.getDimensionPixelSize(com.support.bottomnavigation.R.dimen.coui_tool_navigation_item_height)
            }
        }

        floatingButton?.updateMarginLayoutParams {
            bottomMargin = marginBottom + activity.resources.getDimensionPixelOffset(R.dimen.color_floating_button_margin_bottom)
        }
    }


    @VisibleForTesting
    fun getFabMarginEndInternal(activity: Activity): Int {
        return when {
            ResponsiveUiHelper.isSmallScreen(activity) -> activity.resources.getDimensionPixelSize(R.dimen.dp_20)
            ResponsiveUiHelper.isMediumScreen(activity) -> activity.resources.getDimensionPixelOffset(R.dimen.dp_24)
            ResponsiveUiHelper.isBigScreen(activity) -> activity.resources.getDimensionPixelOffset(R.dimen.dp_40)
            else -> activity.resources.getDimensionPixelSize(R.dimen.dp_20)
        }
    }
}