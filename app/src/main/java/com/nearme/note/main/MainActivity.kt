/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: MainActivity.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/24
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main

import android.Manifest
import android.animation.AnimatorSet
import android.animation.ObjectAnimator.ofFloat
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.content.res.ColorStateList
import android.content.res.Configuration
import android.net.ConnectivityManager
import android.os.Bundle
import android.os.Looper
import android.text.format.DateUtils
import android.util.SparseArray
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewTreeObserver
import android.view.Window
import android.widget.EditText
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.floatingactionbutton.COUIFloatingButton
import com.coui.appcompat.theme.COUIThemeOverlay
import com.nearme.note.BaseActivity
import com.nearme.note.DialogFactory
import com.nearme.note.DialogFactory.DIALOG_TYPE_RED_NOTE_MIGRATE_FAIL_TIPS
import com.nearme.note.MyApplication
import com.nearme.note.activity.notebook.NoteBookViewModel
import com.nearme.note.activity.richedit.MigrateDialogHelper
import com.nearme.note.activity.richedit.RichNoteSaveTransitionHelper
import com.nearme.note.activity.richedit.RichNoteTransformHelper
import com.nearme.note.activity.richedit.TransparentActivity
import com.nearme.note.activity.richedit.webview.WVQuickNoteViewEditFragment
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.appwidget.notewidget.NoteWidgetProvider
import com.nearme.note.appwidget.todowidget.ToDoWidgetProvider
import com.nearme.note.appwidget.todowidget.TodoSettingViewModel.Companion.getHideFinishedTodoValuePlugin
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.common.Constants
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NoteExternalCallPresenter.Companion.sendDataChangeNotify
import com.nearme.note.main.note.NoteDetailFragment
import com.nearme.note.main.todo.TodoFragment
import com.nearme.note.main.todo.TodoListFragment
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.RichNoteTransformer.batchTransForm
import com.nearme.note.skin.NetworkStateReceiver
import com.nearme.note.upgrade.MigrateOldPackageManager
import com.nearme.note.util.AlarmController
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.AndroidVersionUtils
import com.nearme.note.util.AppExecutors
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.CheckNextAlarmUtils.PERMISSION_REQUEST_CODE
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DialogUtils
import com.nearme.note.util.Injector
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.NextAlarmCallBack
import com.nearme.note.util.NotePermissionUtil
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.ResourceUtils
import com.nearme.note.util.SearchBroadcastReceiver
import com.nearme.note.util.SearchBroadcastUtils
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.util.TBLSdkUtils
import com.nearme.note.util.ToDoAlarmController
import com.nearme.note.util.TwoPaneCalculateWidthUtils
import com.nearme.note.util.isNotRemind
import com.nearme.note.util.startRotatingAnimation
import com.nearme.note.view.ColorContainerTransformSharedElementCallback
import com.nearme.note.view.NoteFloatingButton
import com.nearme.note.view.helper.ContainerTransformConfigurationHelper
import com.nearme.note.view.helper.IntentUtils
import com.nearme.note.view.helper.UiHelper
import com.oneplus.helper.OnePlusMoveDataHelper
import com.oneplus.helper.OnePlusMoveDataHelper.MoveStateListener
import com.oplus.cloud.agent.note.NoteSyncAgent
import com.oplus.cloud.agent.note.NoteSyncAgent.OnDeleteFinishListener
import com.oplus.cloud.utils.PrefUtils
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.cloudkit.view.CloudKitInfoController
import com.oplus.migrate.MigrateStatusCallback
import com.oplus.migrate.utils.MigrateResult
import com.oplus.note.R
import com.oplus.note.databinding.LayoutMainEndContainerBinding
import com.oplus.note.databinding.MainActivity2Binding
import com.oplus.note.edgeToEdge.EdgeToEdgeActivity
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.ResponsiveUiHelper
import com.oplus.note.os.ResponsiveUiHelper.isBigScreen
import com.oplus.note.os.ResponsiveUiHelper.isMediumScreen
import com.oplus.note.os.ResponsiveUiHelper.isSmallScreen
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.osdk.proxy.OplusFontManagerProxy
import com.oplus.note.privacypolicy.api.PrivacyPolicyNoteCallback
import com.oplus.note.push.PushAgentFactory
import com.oplus.note.push.PushRegisterCallback
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.scenecard.utils.SceneCardConstants
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.semantic.api.SemanticFactory
import com.oplus.note.statistic.StatisticsNoteCard
import com.oplus.note.utils.FocusViewHelper
import com.oplus.notes.webview.cache.api.IWebViewProxyCache
import com.oplus.notes.webview.container.web.WVJBWebView
import com.oplus.richtext.core.utils.RichUiHelper
import com.oplus.todo.search.TodoSearchManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@SuppressLint("ScreencaptureDetector")
class MainActivity : EdgeToEdgeActivity() {

    companion object {
        private const val TAG = "MainActivity"
        var configurationHelper: ContainerTransformConfigurationHelper? = null
        const val NOTE_INDEX = 0
        const val TODO_INDEX = 1

        const val EXTRA_FOLDER = "extra_folder"

        private const val CARDINAL_NUMBER_ONE_HUNDRED = 100
        private const val DURATION_300 = 300L
        private const val DELAY_TIME = 50L
        private const val ACTION_FINISH_MAIN_ACTIVITY: String = "com.oplus.note.action.FINISH_MAIN_ACTIVITY"
        private const val KEY_FINISH_MAIN_ACTIVITY_HASH: String = "com.oplus.note.FINISH_MAIN_ACTIVITY_HASH"
        private const val FLIP_FONT = "flip_font"
        private const val MAIN_INDEX = 2
        private const val DETAIL_INDEX = 3
        private const val DETAIL_TODOLIST_INDEX = 4
        const val STACK_NAME = TAG
        const val ANIMATION_TIME = 350L
        fun createIntent(context: Context, folder: Folder?): Intent {
            val intent = Intent(context, MainActivity::class.java)
            if (folder != null) {
                intent.putExtra(EXTRA_FOLDER, folder)
            }
            return intent
        }
    }

    private val SP_KEY_PRIVACY_IS_AGREE = "privacy_policy_is_agree"
    private val sharedViewModel by viewModels<ActivitySharedViewModel>()
    private val noteBookViewModel by viewModels<NoteBookViewModel>()
    private var privacyPolicyHelper: PrivacyPolicyHelper? = null
    private var dialogFactory: DialogFactory? = null
    private val mMigrateDialogHelper = MigrateDialogHelper()
    private val transformHelper = RichNoteTransformHelper()
    private var isMigrated = false
    private var isMovingOnsplusData = false
    private var initTabPosition = 0
    private var deleteFinishListener: OnDeleteFinishListener? = null
    private var initRotatingDialog = false
    private var rotatingDialog: AlertDialog? = null
    private var networkStateReceiver: NetworkStateReceiver? = null
    private val fragmentList = SparseArray<Fragment>()
    private var isAnimationPause = false
    private var systemBarsRight = 0
    private var mSaveInstanceBundle: Bundle? = null
    private var mLastMode = WVQuickNoteViewEditFragment.SMALL_WINDOW
    private var mLastScreenIsSmall = false
    private var viewInit = false
    private var mBackAnimObserver: LiveData<Boolean>? = null
    private var switchFragmentAnimatorSet: AnimatorSet? = null
    private val localBroadcastManager = LocalBroadcastManager.getInstance(MyApplication.appContext)
    private val finishMainActivityBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            AppLogger.BASIC.d(TAG, "finishMainActivityBroadcastReceiver action = ${intent?.action}")
            if (context == null || intent == null) return
            val hashCode = intent.getIntExtra(KEY_FINISH_MAIN_ACTIVITY_HASH, -1)
            if (<EMAIL>() != hashCode && !isFinishing && !isDestroyed && !isInMultiWindowMode) {
                finish()
            }
        }
    }
    private val isFlipFont by lazy {
        runCatching {
            OplusFontManagerProxy.isFlipFontUsed()
        }.getOrDefault(false)
    }
    var binding: MainActivity2Binding? = null
    var bindingEnd: LayoutMainEndContainerBinding? = null
    private val moveInterpolator = COUIMoveEaseInterpolator()
    private var mUnFoldState: Boolean = false
    private var mIsFromAppCard: Boolean = false
    private val focusListener = ViewTreeObserver.OnGlobalFocusChangeListener { _, newFocus ->
        if (newFocus is WVJBWebView || newFocus is EditText) {
            FocusViewHelper.addFocusViewHashCode(newFocus.hashCode())
        }
    }
    private var searchBroadcastReceiver: SearchBroadcastReceiver? = null

    @SuppressLint("WrongConstant")
    override fun onCreate(savedInstanceState: Bundle?) {
        AppLogger.BASIC.d(TAG, "onCreate MainActivity")
        ResponsiveUiHelper.setOrientation(this)
        window.requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS)
        setExitSharedElementCallback(ColorContainerTransformSharedElementCallback())
        window.sharedElementsUseOverlay = false
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(this, R.layout.main_activity_2)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        lifecycleScope.launch(Dispatchers.IO) {
            RichUiHelper.linkColor = COUIContextUtil.getAttrColor(this@MainActivity, com.coui.appcompat.R.attr.couiColorPrimaryText)
            val isFirstTransform = transformHelper.isFirstTransform()
            if (isFirstTransform) {
                withContext(Dispatchers.Main) {
                    binding?.mainContainer?.visibility = View.GONE
                }
            }
            WidgetUtils.updateWidgetPreview(this@MainActivity)
            checkAlarmWhenOnCreate()
        }
        initiateWindowInsets()
        configurationHelper = ContainerTransformConfigurationHelper()
        UiHelper.initStatics(this)
        sharedViewModel.twoPane = resources.getBoolean(R.bool.is_two_panel)
        updateWebCacheSize()
        sharedViewModel.inZoomWindowState = OplusFlexibleWindowManagerProxy.isInFreeFormMode(this)

        if (savedInstanceState != null) {
            val lastFlipFont = savedInstanceState.getBoolean(FLIP_FONT, false)
            if (lastFlipFont) {
                val flipFont = runCatching {
                    OplusFontManagerProxy.isFlipFontUsed()
                }.getOrDefault(false)
                if (!flipFont) {
                    AppLogger.BASIC.d(TAG, "onCreate: switch to system font from third font")
                    ResourceUtils.isSwitchToSystemFontFromThird = true
                }
            }
        }

        val callback = object : MigrateStatusCallback {
            override fun start() {
                AppLogger.BASIC.i(TAG, "MigrateStatusCallback start")
            }

            override fun publishProgress(complete: Int, total: Int) {
                AppLogger.BASIC.i(TAG, "publishProgress complete:$complete total:$total")
            }

            override fun end(status: Int) {
                onEnd(status)
            }
        }

        MigrateOldPackageManager.INSTANCE.addOnMigrateFinishedListenerWithDialog(mMigrateDialogHelper, {
            if (isFinishing || isDestroyed) {
                AppLogger.BASIC.d(TAG, "---isDestroyed---")
                return@addOnMigrateFinishedListenerWithDialog
            }

            mMigrateDialogHelper.dismissDialog()
            isMigrated = true
            val intent = intent
            handleInitTabPosition(intent)
            val isNoteFragment = initTabPosition == NOTE_INDEX
            StatisticsUtils.setDefaultEnterPager(this, isNoteFragment)

            checkPrivacyPolicy()

            lifecycleScope.launch(Dispatchers.IO) {
                // upload note statistic data to server in AllNoteActivity.onCreate()
                try {
                    <EMAIL> {
                        StatisticsUtils.setEventTodoWidgetCurrentOpaque(this,
                            (WidgetUtils.loadTodoWidgetBackgroundAlpha(this) * CARDINAL_NUMBER_ONE_HUNDRED).toInt())
                        val hide = getHideFinishedTodoValuePlugin(false)
                        StatisticsUtils.setEventTodoWidgetHideOrShowCompleteTodo(this,
                            if (hide) StatisticsUtils.TYPE_TODO_WIDGET_HIDE_COMPLETE_TODO else StatisticsUtils.TYPE_TODO_WIDGET_SHOW_COMPLETE_TODO)
                        StatisticsUtils.setEventNoteNum(this)
                        StatisticsUtils.setEventNoteWidgetCount(this)
                        StatisticsUtils.setEventNumMemoTopped(this, RichNoteRepository.findToppedNoteCount())
                        StatisticsUtils.calculateNoteNum()
                        StatisticsUtils.setEventFolderNumType(this)
                        StatisticsUtils.setEventNotebookCover()
                        StatisticsUtils.setEventTodoNum(this)

                        if (!SyncSwitchStateRepository.isCloudClose(this)) {
                            StatisticsUtils.setEventSettingCloudSync(
                                this,
                                StatisticsUtils.TYPE_CLOUD_SYNC_OPENED
                            )
                        }
                        val result = WidgetUtils.hasAlreadyAddedWidget(this, ComponentName(this, ToDoWidgetProvider::class.java))
                        StatisticsUtils.setEventTodoWidgetStatus(this,
                            if (result) StatisticsUtils.TYPE_ADDED_STATUS else StatisticsUtils.TYPE_UNADDED_STATUS)
                    }
                } catch (e: java.lang.Exception) {
                    AppLogger.BASIC.d(TAG, "onCreate: inDiskIO: " + e.message)
                }
            }

            lifecycleScope.launch(Dispatchers.Main) {
                delay(DateUtils.SECOND_IN_MILLIS + DateUtils.SECOND_IN_MILLIS)
                if (AppDatabase.getShouldShowFailedDialog()) {
                    showUpgradeFailedDialog()
                    AppDatabase.setShouldShowFailedDialogFlag(false)
                }
            }
        }, callback, this)

        if (networkStateReceiver == null) {
            networkStateReceiver = NetworkStateReceiver()
        }
        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        ContextCompat.registerReceiver(this, networkStateReceiver, filter, ContextCompat.RECEIVER_EXPORTED)

        //仅内销会成功注册 push
        PushAgentFactory.get()?.register(this, this, object : PushRegisterCallback {
            override fun onRegisterSuccess(id: String?) {
                CloudKitSdkManager.registerPush(id)
            }
        })
        StatisticsUtils.setEventOpenNote()
        // 作为back队列首位，在队列中位置固定，不随生命周期变化
        // 通过activity获取的lifecycle，注册的LifecycleObserver生命周期回调会晚于fragment: activity -> fragment -> activity.lifecycle
        setBackInvokedCallback({ backPressed() })
        if (savedInstanceState == null) {
            sendFinishMainActivityBroadcast()
        }
        registerFinishMainActivityReceiver()
        RichNoteSaveTransitionHelper.reset()
        StatisticsNoteCard.setEventOpenNotebook(this, intent)
        initDmp()
    }

    /**
     * 查询是否有未来提醒
     */
    private suspend fun checkAlarmWhenOnCreate() {
        CheckNextAlarmUtils.checkNextAlarm(object : NextAlarmCallBack {
            override fun haveNextAlarm(haveAlarm: Boolean) {
                AppLogger.BASIC.e(TAG, "haveAlarm:$haveAlarm")
                /**
                 * 有未来提醒且应用未被授予通知权限
                 */
                if (haveAlarm && !CheckNextAlarmUtils.getNotificationsEnabled(this@MainActivity)) {
                    lifecycleScope.launch(Dispatchers.Main) {
                        /**
                         * 未勾选［不再提醒］
                         */
                        val fromCardRequestNotification = IntentParamsUtil.getStringExtra(intent, PrefUtils.MAIN_ACTION_FROM, "")
                            .equals(SceneCardConstants.APP_TODO_CARD_REQ_NOTIFICATION)
                        val needPermission = IntentParamsUtil.getBooleanExtra(intent, SceneCardConstants.NEED_PERMISSION, false)
                        AppLogger.BASIC.e(TAG, "checkNextAlarm requestNotification:$fromCardRequestNotification,needPermission=$needPermission")
                        if (!isNotRemind(Manifest.permission.POST_NOTIFICATIONS)
                            && !CloudKitInfoController.isNotifyIgnore
                            && !fromCardRequestNotification
                            && !needPermission
                        ) {
                            CheckNextAlarmUtils.initNotificationPermission(this@MainActivity)
                        }
                    }
                }
            }
        })
    }

    private fun onEnd(status: Int) {
        if (MigrateResult.isMigrateEndFail(status)) {
            if (dialogFactory == null) {
                dialogFactory = DialogFactory(this@MainActivity, null)
            }
            dialogFactory?.showDialog(DIALOG_TYPE_RED_NOTE_MIGRATE_FAIL_TIPS, null)
        }
    }

    private fun calIsFromAppCard(isFromAppCard: String): Boolean {
        return when (isFromAppCard) {
            PrefUtils.APP_TODO_CARD, PrefUtils.APP_TODO_CARD_EDIT -> true
            else -> false
        }
    }

    private fun handleInitTabPosition(intent: Intent) {
        val isFromNotification = IntentParamsUtil.getBooleanExtra(intent, AlarmController.DATA_FROM_NOTIFICATION, false)
        val uuid = IntentParamsUtil.getStringExtra(intent, ToDoAlarmController.DATA_UUID)
        val editTodo = IntentUtils.fromEditTodoAction(intent)
        var hasGetTabPosition = false
        if (editTodo) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. fromEditTodoAction: true.")
                return
            }
            initTabPosition = TODO_INDEX
            val fragment = fragmentList[MAIN_INDEX]
            if (fragment is MainFragment) {
                fragment.updateView()
            }
            return
        }
        if (isFromNotification) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromNotification: true.")
                return
            }
            intent.removeExtra(AlarmController.DATA_FROM_NOTIFICATION)
            intent.removeExtra(ToDoAlarmController.DATA_UUID)
            StatisticsUtils.setEventOpenNotification(this)
            AppLogger.BASIC.d(TAG, "handleInitTabPosition isFromNotification = $isFromNotification, uuid = $uuid")
            hasGetTabPosition = true
            initTabPosition = TODO_INDEX
            sharedViewModel.notificationUUID.value = uuid
        }

        val isFromTodoWidget = IntentParamsUtil.getBooleanExtra(intent, ToDoWidgetProvider.KEY_IS_FROM_TODO_WIDGET, false)
        if (isFromTodoWidget) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromTodoWidget: true.")
                return
            }
            intent.removeExtra(ToDoWidgetProvider.KEY_IS_FROM_TODO_WIDGET)
            StatisticsUtils.setEventOpenAppWidget(this)
            hasGetTabPosition = true
            initTabPosition = TODO_INDEX
        }

        val isFromAppCard = IntentParamsUtil.getStringExtra(intent, PrefUtils.MAIN_ACTION_FROM, "")
        mIsFromAppCard = calIsFromAppCard(isFromAppCard) && sharedViewModel.twoPane

        AppLogger.BASIC.i(TAG, "handleInitTabPosition isFromAppCard: $isFromAppCard,isFromTodoWidget=$isFromTodoWidget")
        if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD)) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromAppCard: APP_TODO_CARD")
            } else {
                StatisticsUtils.setEventTodoCardOperation(this, StatisticsUtils.TYPE_CARD_BLANK)
            }
        } else if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD_EDIT)) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromAppCard: APP_TODO_CARD_EDIT")
            } else {
                StatisticsUtils.setEventTodoCardOperation(this, StatisticsUtils.TYPE_CARD_ITEM)
            }
        } else if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD_NEW)) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromAppCard: APP_TODO_CARD_NEW")
            } else {
                StatisticsUtils.setEventTodoCardOperation(this, StatisticsUtils.TYPE_CARD_NEW_BUTTON)
            }
        }
        if (isFromAppCard.equals(PrefUtils.APP_TODO_CARD) || isFromAppCard.equals(PrefUtils.APP_TODO_MIDDLE_CARD_OTHER)
            || isFromAppCard.equals(PrefUtils.APP_TODO_CARD_EDIT) || isFromAppCard.equals(PrefUtils.APP_TODO_CARD_NEW)
            || isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_PRIVACY) || isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_AUDIO)
            || isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_NOTIFICATION)
            || isFromAppCard.equals(SceneCardConstants.APP_TODO_CARD_REQ_OVERLAY)
        ) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. isFromAppCard: $isFromAppCard")
                return
            }
            initTabPosition = TODO_INDEX
            return
        }

        if (!hasGetTabPosition) {
            initTabPosition = PrefUtils.getInt(this, PrefUtils.KEY_HOME_PAGER_INDEX, NOTE_INDEX)
            if (initTabPosition == TODO_INDEX) {
                if (ConfigUtils.isToDoDeprecated) {
                    AppLogger.BASIC.i(TAG, "handleInitTabPosition error: Todo is deprecated. initTabPosition = TODO_INDEX")
                    initTabPosition = NOTE_INDEX
                } else if (isFromAppCard.equals("app_note_card")) {
                    intent.removeExtra(PrefUtils.MAIN_ACTION_FROM)
                    initTabPosition = NOTE_INDEX
                }
            }
        } else {
            if (isFromAppCard.equals("app_note_card")) {
                intent.removeExtra(PrefUtils.MAIN_ACTION_FROM)
                initTabPosition = NOTE_INDEX
            }
        }

        val isSummary = IntentParamsUtil.getBooleanExtra(intent, TransparentActivity.IS_SUMMARY, false)
        if (isSummary) {
            initTabPosition = NOTE_INDEX
        }
        AppLogger.BASIC.i(TAG, "handleInitTabPosition initTabPosition: $initTabPosition")
    }

    override fun onRestart() {
        super.onRestart()
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
            if (this is MainFragment && this.isAdded) {
                this.onRestart()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        AppLogger.BASIC.d(TAG, "onNewIntent")
        if (intent != null) {
            setIntent(intent)
            handleInitTabPosition(intent)
            if (sharedViewModel.currentTabIndex.value != initTabPosition) {
                sharedViewModel.currentTabIndex.value = initTabPosition
                supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
                    if (this is MainFragment && this.isAdded) {
                        this.getViewPager()?.setCurrentItem(initTabPosition, false)
                    }
                }
            }
            AppLogger.BASIC.d(TAG, "onNewIntent: $initTabPosition")
        }
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.root) { _, insets ->
            val navigationBarInsets = insets.getInsets(WindowInsetsCompat.Type.navigationBars())
            sharedViewModel.navigationWindowInsetBottom = navigationBarInsets.bottom
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            systemBarsRight = systemBarInsets.right
            sharedViewModel.systemBarInsetBottom = systemBarInsets.bottom
            AppLogger.BASIC.d(TAG, "systemBarInsets.bottom: ${systemBarInsets.bottom}")
            if (sharedViewModel.twoPane && fragmentList[MAIN_INDEX] != null) {
                (fragmentList[MAIN_INDEX] as? MainFragment)?.apply {
                    if (isAdded) {
                        updateDetailParentFragmentWidth()
                    }
                }
            }
            sharedViewModel.fabAnimatorHelper?.apply {
                sharedViewModel.currentTabIndex.value?.let { curTabIndex ->
                    systemBarsRight = getSuitableSystemBarsRight(curTabIndex)
                    updateFabMarginEnd(
                        this@MainActivity,
                        sharedViewModel.twoPane,
                        curTabIndex,
                        sharedViewModel.inZoomWindowState,
                        true
                    )
                } ?: run {
                    AppLogger.BASIC.e(TAG, "currentTabIndex is null")
                }
            }
        }
    }



    private fun initFragments() {
        for (fragment in supportFragmentManager.fragments) {
            if (fragment is MainFragment) {
                fragmentList.append(MAIN_INDEX, fragment)
            } else if (fragment is NoteDetailFragment) {
                fragmentList.append(DETAIL_INDEX, fragment)
            } else if (fragment is TodoListFragment) {
                fragmentList.append(DETAIL_TODOLIST_INDEX, fragment)
            }
        }
        AppLogger.BASIC.d(TAG, "initFragments fragments=${supportFragmentManager.fragments.size}")

        if (fragmentList[MAIN_INDEX] == null) {
            fragmentList.append(MAIN_INDEX, MainFragment.newInstance(initTabPosition))
        }

        if (fragmentList[DETAIL_INDEX] == null) {
            fragmentList.append(DETAIL_INDEX, NoteDetailFragment.newInstance())
        }

        if (fragmentList[DETAIL_TODOLIST_INDEX] == null) {
            fragmentList.append(DETAIL_TODOLIST_INDEX, TodoListFragment.newInstance())
        }
    }

    fun updateFabMarginEnd() {
        sharedViewModel.currentTabIndex.value?.let {
            sharedViewModel.fabAnimatorHelper?.updateFabMarginEnd(
                this@MainActivity,
                sharedViewModel.twoPane,
                it,
                sharedViewModel.inZoomWindowState, true
            )
        }
    }

    fun updateFabMarginBottom() {
        AppLogger.BASIC.d(TAG, "updateFabMarginBottom index=${sharedViewModel.currentTabIndex.value}")
        sharedViewModel.fabAnimatorHelper?.updateFabMarginBottom(
            this@MainActivity,
            sharedViewModel.twoPane,
            sharedViewModel.currentTabIndex.value ?: NOTE_INDEX,
            sharedViewModel.systemBarInsetBottom,
            sharedViewModel.inZoomWindowState,
        )
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun initFloatingButton() {
        val currentTabIndex = sharedViewModel.currentTabIndex.value ?: NOTE_INDEX
        AppLogger.BASIC.d(TAG, "initFloatingButton currentTabIndex: $currentTabIndex")
        sharedViewModel.fabAnimatorHelper = FloatingButtonAnimatorHelper().apply {
            initViews(binding?.fab, binding?.fabMask?.viewStub)
            systemBarsRight = if (sharedViewModel.twoPane) 0 else <EMAIL>
            updateFabMarginEnd(this@MainActivity, sharedViewModel.twoPane, currentTabIndex, sharedViewModel.inZoomWindowState)
        }

        binding?.fabMask?.setOnInflateListener { _, inflated ->
            inflated.setOnClickListener {
                if (supportFragmentManager.backStackEntryCount > 0) {
                    onBackPressed()
                }
            }
        }

        binding?.fab?.apply {
            val couiColorPrimary = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            mainFloatingButtonBackgroundColor = ColorStateList.valueOf(couiColorPrimary)
            mainFloatingButton.setBackgroundColor(couiColorPrimary)
            mainFloatingButton.contentDescription = if (currentTabIndex == NOTE_INDEX) {
                getString(R.string.memo_new_note)
            } else {
                getString(R.string.todo_create)
            }
        }

        var isStylusTouch = false
        binding?.fab?.apply {
            setOnDispatchTouchEventListener(object : NoteFloatingButton.OnDispatchTouchEventListener {
                override fun dispatchTouchEvent(ev: MotionEvent) {
                    for (i in 0 until ev.pointerCount) {
                        isStylusTouch = ev.getToolType(i) == MotionEvent.TOOL_TYPE_STYLUS
                    }
                }
            })
        }
        binding?.fab?.setOnChangeListener(object : COUIFloatingButton.OnChangeListener {
            override fun onMainActionSelected(): Boolean {
                AppLogger.BASIC.d(TAG, "onMainActionSelected")
                if (sharedViewModel.storagePermissionDenied.value == true) {
                    sharedViewModel.checkPermission.value = true
                    return false
                }

                if (isFinishing) {
                    return false
                }
                if (sharedViewModel.viewPagerScrollStateIdle.value != true) {
                    return false
                }
                supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
                    if (this is MainFragment && this.isAdded) {
                        if (this.getViewPager()?.currentItem == MainFragment.NOTE_INDEX) {
                            this.getNoteListFragment()
                                ?.fabMainActionSelected(isStylusTouch) { success ->
                                    if (success) {
                                        isAnimationPause = true
                                        binding?.fab?.animate()?.cancel()
                                        sharedViewModel.fabAnimatorHelper?.maskAnimationAppear()
                                    }
                                }
                        } else {
                            this.getTodoFragment()?.fabMainActionSelected()
                        }
                    }
                }

                return false
            }

            override fun onToggleChanged(isOpen: Boolean) {
                AppLogger.BASIC.i(TAG, "onToggleChanged $isOpen")
            }
        })
    }

    private fun initiateObservers() {
        sharedViewModel.isNotebookListShow.observe(this) { show ->
            if (binding?.notebookContainerStub?.isInflated != true) {
                binding?.notebookContainerStub?.viewStub?.inflate()
            }

            if (show) {
                sharedViewModel.fabAnimatorHelper?.maskAnimationAppear(true)
            } else {
                sharedViewModel.fabAnimatorHelper?.maskAnimationDisappear(true)
            }
        }
        sharedViewModel.floatButtonChange().observe(this) { shouldShow: Boolean ->
            updateFabVisibility(shouldShow)
        }
        window?.decorView?.viewTreeObserver?.addOnGlobalFocusChangeListener(focusListener)

        sharedViewModel.isKitPanelAlive.observe(this) { isKitAlive ->
            binding?.fab?.isEnabled = !isKitAlive
        }
    }

    private fun updateFabVisibility(visible: Boolean) {
        sharedViewModel.fabAnimatorHelper?.apply {
            changeFloatButtonState(visible)
        }
    }

    override fun onResume() {
        super.onResume()
        // 兜底，防止写作助手崩溃后，按钮状态未重置
        binding?.fab?.isEnabled = true
        val sp: SharedPreferences = this.getSharedPreferences(PrivacyPolicyHelper.SP_NAME, MODE_PRIVATE)
        val isAgreePrivacy = sp.getBoolean(SP_KEY_PRIVACY_IS_AGREE, false)
        val showStatementInfo = IntentParamsUtil.getStringExtra(intent, PrefUtils.MAIN_ACTION_FROM, "")
        AppLogger.BASIC.d(TAG, "onResume  isAgreePrivacy=$isAgreePrivacy,showStatementInfo=$showStatementInfo ," +
                "tabIndex=${(supportFragmentManager.findFragmentByTag(MainFragment.TAG) as? MainFragment)?.getViewPager()?.currentItem}")
        if (PrivacyPolicyHelper.isPrivacyStatementSupport(this) && privacyPolicyHelper != null && !isAgreePrivacy
                && privacyPolicyHelper?.checkPrivacyPolicy(showStatementInfo) == true
        ) {
            val editor = sp.edit()
            editor.putBoolean(SP_KEY_PRIVACY_IS_AGREE, true).apply()
        }
        checkDeleteInBackground()

        if (isAnimationPause) {
            sharedViewModel.fabAnimatorHelper?.maskAnimationDisappear()
        }

        isAnimationPause = false

        networkStateReceiver?.apply {
            isResume = true
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        AppLogger.BASIC.d(TAG, "onSaveInstanceState")
        outState.putBoolean(FLIP_FONT, isFlipFont)
        mSaveInstanceBundle = outState
        super.onSaveInstanceState(outState)
    }

    override fun onPause() {
        super.onPause()
        // 兜底，防止写作助手崩溃后，按钮状态未重置
        binding?.fab?.isEnabled = false
        networkStateReceiver?.apply {
            isResume = false
        }
        saveViewPagerIndex()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        AppLogger.BASIC.d(TAG, " [A] onActivityResult $requestCode")
        if (sharedViewModel.currentTabIndex.value == NOTE_INDEX) {
            //dispatch onActivityResult to NoteFragment
            dispatchNoteActivityResult(requestCode, resultCode, data)
        }

        if (sharedViewModel.currentTabIndex.value == TODO_INDEX) {
            dispatchTodoActivityResult(requestCode, resultCode, data)
        }
        if (CommonPermissionUtils.TIPS_PERMISSIONS_ALARM_CODE == requestCode) {
            if (!CheckNextAlarmUtils.getNotificationsEnabled(this)) {
                return
            }
            lifecycleScope.launch {
                CheckNextAlarmUtils.checkNextAlarm(object : NextAlarmCallBack {
                    override fun haveNextAlarm(haveAlarm: Boolean) {
                        if (CommonPermissionUtils.getScheduleAlarmEnabled(this@MainActivity)) {
                            AlarmUtils.resetAllSystemAlarms()
                        }
                    }
                })
            }
        }
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        super.onMultiWindowModeChanged(isInMultiWindowMode)
        AppLogger.BASIC.d(TAG, "onMultiWindowModeChanged isInMultiWindowMode=$isInMultiWindowMode,twoPane=${sharedViewModel.twoPane}")

        if (!sharedViewModel.twoPane) {
            return
        }

        val isGrid = sharedViewModel.isGridMode()
        if (isInMultiWindowMode || OplusFlexibleWindowManagerProxy.isInFreeFormMode(this)) {
            bindingEnd?.mainEndContainer?.visibility = View.GONE
            binding?.dividerLine?.visibility = View.INVISIBLE
            binding?.dividerGuideLine?.setGuidelineEnd(0)
            val noteDetailFragment = supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG)
            if (noteDetailFragment is NoteDetailFragment) {
                if(noteDetailFragment.isCoverPaint){
                    noteDetailFragment.getNoteViewCoverPaintEditFragment()?.dismissRecoverDialog()
                    noteDetailFragment.getNoteViewCoverPaintEditFragment()?.mUiHelper?.dismissTextMenu()
                }else{
                    noteDetailFragment.getNoteViewEditFragment()?.dismissRecoverDialog()
                    noteDetailFragment.getNoteViewEditFragment()?.mUiHelper?.dismissTextMenu()
                }
            }
        } else {
            bindingEnd?.mainEndContainer?.visibility = View.VISIBLE
            binding?.dividerLine?.visibility = View.VISIBLE
            binding?.dividerGuideLine?.setGuidelineEnd(resources.getDimensionPixelOffset(R.dimen.note_twopane_detail_size))
            supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
                if (this is MainFragment && this.isAdded) {
                    getNoteListFragment()?.let { noteListFragment ->
                        if (!noteListFragment.isAdded || noteListFragment.context == null) {
                            return
                        }
                        val twoPane = context?.resources?.getBoolean(R.bool.is_two_panel) ?: false
                        noteListFragment.twoPane = twoPane
                        if (isGrid) {
                            noteListFragment.adapter.notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    override fun initBackAnimObserverState() {
        // activity级可以影响返回键功能的变量，需要注册观察
        super.initBackAnimObserverState()
        addBackAnimObserver(sharedViewModel.noteSelectionMode)
        addBackAnimObserver(sharedViewModel.todoSelectionMode)
        addBackAnimObserver(sharedViewModel.isSearch)
    }

    private fun backPressed() {
        // 1.selection mode out
        if (sharedViewModel.noteSelectionMode.value == true) {
            sharedViewModel.noteSelectionMode.value = false
            return
        }

        if (sharedViewModel.todoSelectionMode.value == true) {
            sharedViewModel.todoSelectionMode.value = false
            supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
                if (this is MainFragment && this.isAdded) {
                    this.getTodoFragment()?.unSelectedAllTodos()
                }
                return
            }
        }

        // 2.search out
        if (sharedViewModel.isSearch.value == true) {
            sharedViewModel.isSearch.value = false
            if (sharedViewModel.twoPane && onBackToTwoPane()) {
                return
            }
            return
        }

        if (sharedViewModel.twoPane && onBackToTwoPane()) {
            return
        }
        finish()
    }

    private fun onBackToTwoPane(): Boolean {
        supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG)?.apply {
            if (this is NoteDetailFragment && this.isAdded) {
                val handled = if (this.isCoverPaint) this.getNoteViewCoverPaintEditFragment()?.onBackPressed() else this.getNoteViewEditFragment()
                    ?.onBackPressed()
                if (handled == true) {
                    return handled
                }
            }
        }

        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLogger.BASIC.d(TAG, "onDestroy")
        sharedViewModel.noteRecycledBlock = null
        networkStateReceiver?.apply {
            isResume = false
            unregisterReceiver(this)
        }
        networkStateReceiver = null
        configurationHelper = null
        if (dialogFactory != null) {
            dialogFactory!!.onDestory()
            dialogFactory = null
        }
        if (privacyPolicyHelper != null) {
            privacyPolicyHelper?.onDestroy()
        }

        transformHelper.destroy()
        removeTblObserve()
        unregisterFinishMainActivityReceiver()
        SearchBroadcastUtils.unregister(this, searchBroadcastReceiver)

        NoteSearchManager.recycle()
    }

    /**
     * isScreen 判断是窗口切换（大小）， 平板重建时需要关掉之前的弹框
     */
    private fun checkPrivacyPolicy(isScreen: Boolean = false) {
        AppLogger.BASIC.d(TAG, "checkPrivacyPolicy...")
        if (PrivacyPolicyHelper.isPrivacyStatementSupport(this)) {
            if (ConfigUtils.isExport) {
                syncInitialProcess()
                PrivacyPolicyHelper.setUserAgreeStatusExport(this)
            } else if (privacyPolicyHelper == null) {
                privacyPolicyHelper = PrivacyPolicyHelper(this, object : PrivacyPolicyNoteCallback {
                    override fun doAfterPermitted(agreePrivacyPolicyClickButton: Boolean) {
                        AppLogger.BASIC.d(TAG, "checkPrivacyPolicy $agreePrivacyPolicyClickButton")
                        if (agreePrivacyPolicyClickButton) {
                            syncInitialProcess()
                        }
                    }
                })
            }
            if (isScreen) {
                privacyPolicyHelper?.dismissDialog()
            }
            val isFromAppCard = IntentParamsUtil.getStringExtra(intent, PrefUtils.MAIN_ACTION_FROM, "")
            privacyPolicyHelper?.checkPrivacyPolicy(isFromAppCard)
        } else {
            PrivacyPolicyHelper.agreeAll(this)
            syncInitialProcess()
        }
    }

    private fun showPageView() {
        AppLogger.BASIC.d(TAG, "showPageView")
        binding?.mainContainer?.visibility = View.VISIBLE
        initiateObservers()
    }

    private fun syncInitialProcess() {
        AppLogger.BASIC.d(TAG, "syncInitialProcess viewInit = $viewInit")
        if (!viewInit) {
            initFragments()
            showDetailIfNeed()
            initFloatingButton()
            observeTbl()
            viewInit = true
        }
        lifecycleScope.launch(Dispatchers.IO) {
            // sdk install 方法有 contentResolver.insert 远端调用，这里放到子线程
            SemanticFactory.get()?.install(applicationContext)
            NoteListHelper.checkSauUpdate(this@MainActivity)
            sendDataChangeNotify(this@MainActivity)
        }
        initRequestPermission()
    }

    private fun initRequestPermission() {
        AppLogger.BASIC.d(TAG, "initRequestPermission")
        val results = ArrayList<String>()
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED
                && !AndroidVersionUtils.isHigherAndroidQ()) {
            results.add(Manifest.permission.READ_PHONE_STATE)
        }
        hidePermissionRequestPage()
        lifecycleScope.launch(Dispatchers.Default) {
            WidgetUtils.requestLayoutWidget(this@MainActivity, ToDoWidgetProvider::class.java)
            WidgetUtils.requestLayoutWidget(this@MainActivity, NoteWidgetProvider::class.java)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        AppLogger.BASIC.d(TAG, "onRequestPermissionsResult")
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode != PERMISSION_REQUEST_CODE) {
            return
        }
        NotePermissionUtil.sendBroadcast(true)
        hidePermissionRequestPage()
        WidgetUtils.requestLayoutWidget(this, NoteWidgetProvider::class.java)
        WidgetUtils.requestLayoutWidget(this, ToDoWidgetProvider::class.java)
        NoteCardWidgetProvider.instance.postUIToCard()

        for (permission in permissions) {
            if (Manifest.permission.POST_NOTIFICATIONS == permission) {
                val intent = Intent(Constants.ACTION_NOTIFICATION_GRANT)
                LocalBroadcastManager.getInstance(MyApplication.appContext).sendBroadcast(intent)
            }
        }
    }

    override fun onTopResumedActivityChanged(isTopResumedActivity: Boolean) {
        super.onTopResumedActivityChanged(isTopResumedActivity)
    }

    private fun hidePermissionRequestPage() {
        transformHelper.showTransformGuide(this, mMigrateDialogHelper) {
            showPageView()
            moveOnePlusData()
        }

        sharedViewModel.moveBackUpNoteData()
        AlarmUtils.resetAllSystemAlarms()
        sharedViewModel.storagePermissionDenied.value = false
        sharedViewModel.checkPermission.value = false
        sharedViewModel.openPermissionPage.value = false
    }

    private fun moveOnePlusData() {
        if (isMovingOnsplusData) {
            return
        }

        isMovingOnsplusData = true
        AppLogger.BASIC.d(TAG, "start move onePlus data")
        GlobalScope.launch {
            OnePlusMoveDataHelper.moveData(object : MoveStateListener {
                override fun onNoData() {
                    AppLogger.BASIC.i(TAG, "onNoData")
                }

                override fun onStart() {
                    if (mMigrateDialogHelper.showDialog(this@MainActivity)) {
                        AppLogger.BASIC.d(TAG, "---show move data dialog---")
                    }
                }

                override fun onSuccess() {
                    mMigrateDialogHelper.dismissDialog()
                    AlarmUtils.resetAllSystemAlarms()
                    AppLogger.BASIC.d(TAG, "---dismiss move data dialog---")
                    AppExecutors.getInstance().executeCommandInDiskIO { batchTransForm() }
                }

                override fun onFailed(e: Exception) {
                    mMigrateDialogHelper.dismissDialog()
                }
            })
            AppLogger.BASIC.d(TAG, "end move onePlus data")
        }
    }

    private fun showUpgradeFailedDialog() {
        val dialog = AlertDialog.Builder(this)
                .setTitle(R.string.db_upgrade_failed)
                .setMessage(R.string.db_failed_prompt)
                .setPositiveButton(R.string.db_failed_ok) { _, _ -> }
                .create()
        dialog.show()
    }

    private fun checkDeleteInBackground() {
        if (NoteSyncAgent.isDeleting && !initRotatingDialog) {
            createLoadingDialog()
            deleteFinishListener = object : OnDeleteFinishListener {
                override fun onDeleteFinish() {
                    if (rotatingDialog != null) {
                        AppLogger.BASIC.d(TAG, "mRotatingDialog dismiss")
                        DialogUtils.safeDismissDialog(rotatingDialog)
                        initRotatingDialog = false
                    }
                }
            }
            NoteSyncAgent.setDeleteFinishListener(deleteFinishListener)
            if (rotatingDialog != null && !rotatingDialog!!.isShowing) {
                rotatingDialog?.show()
            }
            initRotatingDialog = true
        }
    }

    private fun createLoadingDialog() {
        rotatingDialog = COUIAlertDialogBuilder(this, com.support.dialog.R.style.COUIAlertDialog_Rotating).apply {
            setCancelable(false)
        }.create()
                .apply {
                    startRotatingAnimation(getString(R.string.deleting))
                }
    }

    private fun saveViewPagerIndex() {
        val currentIndex = getCurrentPageIndex()
        AppLogger.BASIC.d(TAG, "saveViewPagerIndex: $currentIndex")
        PrefUtils.putInt(this, PrefUtils.KEY_HOME_PAGER_INDEX, currentIndex)
    }

    private fun getCurrentPageIndex(): Int {
        return sharedViewModel.currentTabIndex.value ?: NOTE_INDEX
    }

    fun isEncryptedNote(): Boolean {
        return noteBookViewModel.currentFolderEncrypted
    }

    fun turnToAllNoteFolder() {
        sharedViewModel.turnToAllNoteFolder.value = true
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        AppLogger.BASIC.d(TAG, "onConfigurationChanged")
        super.onConfigurationChanged(newConfig)
        mIsFromAppCard = false
        ResponsiveUiHelper.setOrientation(this)
        updateWebCacheSize()
        val inZoomWindow = OplusFlexibleWindowManagerProxy.isInFreeFormMode(this)
        if (inZoomWindow != sharedViewModel.inZoomWindowState) {
            sharedViewModel.inZoomWindowState = inZoomWindow

            val fragment = fragmentList[TODO_INDEX] as? TodoFragment
            if (fragment?.isAdded == true) {
                fragment.resetMainEmptyPage()
            }
        }

        val mCurrentMode = BaseActivity.getMode(newConfig.toString())
        val isSmall = isSmallScreen(this) || mCurrentMode == WVQuickNoteViewEditFragment.SMALL_WINDOW
        val isSmallToFull =
            (mLastMode == WVQuickNoteViewEditFragment.SMALL_WINDOW && mCurrentMode == WVQuickNoteViewEditFragment.FULL_SCREEN)
                    || (mLastScreenIsSmall && (isMediumScreen(this) || isBigScreen(this)))

        val isZoomWindowState = OplusFlexibleWindowManagerProxy.isInFreeFormMode(this)
        if (isZoomWindowState) {
            AppLogger.BASIC.d(TAG, "check   to Small")
            checkPrivacyPolicy(true)
        }
        if (isSmallToFull) {
            AppLogger.BASIC.d(TAG, "check  Small to fullscreen")
            checkPrivacyPolicy(true)
        }

        val newUnfoldState: Boolean = ResponsiveUiHelper.isUnfoldState(this)
        var foldStateChanged = false
        if (mUnFoldState != newUnfoldState) {
            mUnFoldState = newUnfoldState
            AppLogger.BASIC.d(TAG, "fold state change")
            foldStateChanged = true
        }
        mLastMode = mCurrentMode
        mLastScreenIsSmall = isSmall
        showDetailIfNeed(foldStateChanged)
    }

    fun getSuitableSystemBarsRight(tabIndex: Int): Int {
        return if (sharedViewModel.twoPane && tabIndex == NOTE_INDEX) 0 else systemBarsRight
    }

    fun refreshNoteListTips() {
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
            if (this is MainFragment && this.isAdded) {
                this.refreshNoteListTips()
            }
        }
    }

    override fun dispatchKeyEvent(keyEvent: KeyEvent): Boolean {
        var isConsume = false
        val detailFragment =
            (supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG) as? NoteDetailFragment)
        if (detailFragment?.isAdded == true && sharedViewModel.isSearch.value == false) {
            // 先检查详情页面是否消费事件，详情页面没有消费就走列表页的快捷键功能
            isConsume = if (detailFragment.isCoverPaint) {
                detailFragment.getNoteViewCoverPaintEditFragment()?.dispatchKeyEvent(keyEvent) == true
            } else {
                detailFragment.getNoteViewEditFragment()?.dispatchKeyEvent(keyEvent) == true
            }
            if (!isConsume) {
                val mainFragment =
                    (supportFragmentManager.findFragmentByTag(MainFragment.TAG) as? MainFragment)
                if (mainFragment?.isAdded == true) {
                    isConsume =
                        mainFragment.getNoteListFragment()?.dispatchKeyEvent(keyEvent) == true
                }
            }
        }
        return isConsume || super.dispatchKeyEvent(keyEvent)
    }

    private val tblCoreReadyObserve = Observer<Boolean> { value ->
        if (value) {
            if (IWebViewProxyCache.ENABLE_CACHE) {
                val twoPanel = resources.getBoolean(R.bool.is_two_panel)
                Looper.myQueue().addIdleHandler {
                    val cacheSize = if (twoPanel) 2 else 1
                    AppLogger.BASIC.i(TAG, "queueIdle, cacheSize:$cacheSize")
                    Injector.injectFactory<IWebViewProxyCache>()
                        ?.createWebViewProxyCache(this@MainActivity, R.style.AppTheme_DayNight_WebView, cacheSize) ?: false
                }
            }
            TBLSdkUtils.instance.checkAndDownloadNewestTBLApk(this.applicationContext)
            removeTblObserve()
        }
    }

    private fun observeTbl() {
        TBLSdkUtils.coreReadyLiveData.observeForever(tblCoreReadyObserve)
    }

    private fun removeTblObserve() {
        TBLSdkUtils.coreReadyLiveData.removeObserver(tblCoreReadyObserve)
        window?.decorView?.viewTreeObserver?.removeOnGlobalFocusChangeListener(focusListener)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG)?.apply {
            if (this is NoteDetailFragment && this.isAdded) {
                if(this.isCoverPaint)
                 this.getNoteViewCoverPaintEditFragment()?.dispatchTouchEvent(ev) else this.getNoteViewEditFragment()?.dispatchTouchEvent(ev)
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun sendFinishMainActivityBroadcast() {
        runCatching {
            localBroadcastManager.sendBroadcast(Intent(ACTION_FINISH_MAIN_ACTIVITY).apply {
                putExtra(KEY_FINISH_MAIN_ACTIVITY_HASH, <EMAIL>())
            })
        }.onFailure {
            AppLogger.BASIC.d(TAG, "sendFinishMainActivityBroadcast error ${it.message}")
        }
    }

    private fun registerFinishMainActivityReceiver() {
        runCatching {
            localBroadcastManager.registerReceiver(finishMainActivityBroadcastReceiver, IntentFilter(ACTION_FINISH_MAIN_ACTIVITY))
        }.onFailure {
            AppLogger.BASIC.d(TAG, "registerFinishMainActivityReceiver error ${it.message}")
        }
    }

    private fun unregisterFinishMainActivityReceiver() {
        runCatching {
            localBroadcastManager.unregisterReceiver(finishMainActivityBroadcastReceiver)
        }.onFailure {
            AppLogger.BASIC.d(TAG, "unregisterFinishMainActivityReceiver error ${it.message}")
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        runCatching {
            super.onRestoreInstanceState(savedInstanceState)
        }.onFailure {
            AppLogger.BASIC.d(TAG, "onRestoreInstanceState")
        }
    }

    private fun updateWebCacheSize() {
        if (IWebViewProxyCache.ENABLE_CACHE) {
            val cacheSize = if (sharedViewModel.twoPane) {
                IWebViewProxyCache.CACHE_SIZE_IN_TWO_PANEL
            } else {
                1
            }
            Injector.injectFactory<IWebViewProxyCache>()?.updateCacheSize(cacheSize)
        }
    }

    /**
     * Determine whether to display detail pages
     */
    private fun showDetailIfNeed(foldStateChanged: Boolean = false) {
        if (isSmallScreen(this)) {
            sharedViewModel.twoPane = false
            ensureDetailInflated(false, foldStateChanged)
        } else {
            sharedViewModel.twoPane = true
            ensureDetailInflated(true, foldStateChanged)
        }
        bindFragments()
        updateFabMarginEnd()
        updateFabMarginBottom()
    }

    /**
     * Check if subpage should be loaded
     * @param twoPane Whether it is two pane mode
     */
    private fun ensureDetailInflated(twoPane: Boolean, needHide: Boolean) {
        if (bindingEnd == null) {
            val inflatedView = binding?.vsEndContainer?.viewStub?.inflate()
            bindingEnd = inflatedView?.let { LayoutMainEndContainerBinding.bind(it) }
        }
        if (twoPane) {
            bindingEnd?.mainEndContainer?.visibility = View.VISIBLE
            binding?.dividerGuideLine?.setGuidelineEnd(TwoPaneCalculateWidthUtils.getTwoPaneDetailSizeSafe(this))
            binding?.dividerLine?.visibility = View.VISIBLE
        } else {
            bindingEnd?.mainEndContainer?.visibility = View.GONE
            binding?.dividerGuideLine?.setGuidelineEnd(0)
            binding?.dividerLine?.visibility = View.GONE
        }
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
            val isGrid = sharedViewModel.isGridMode()
            if (this is MainFragment && this.isAdded) {
                getNoteListFragment()?.apply {
                    if (this.isAdded) {
                        val twoPanel = <EMAIL>(R.bool.is_two_panel)
                        this.twoPane = twoPanel
                        if (isGrid) {
                            this.adapter.notifyDataSetChanged()
                        }
                    }
                }
            }
        }
    }

    private fun bindFragments() {
        val mainFragment = supportFragmentManager.findFragmentByTag(MainFragment.TAG) ?: (fragmentList[MAIN_INDEX] as? MainFragment)
        val noteDetailFragment =
            supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG) ?: (fragmentList[DETAIL_INDEX] as? NoteDetailFragment)
        val todoListFragment = supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) ?:
            (fragmentList[DETAIL_TODOLIST_INDEX] as? TodoListFragment)
        supportFragmentManager.commit(allowStateLoss = true) {
            if (mainFragment != null && mainFragment is MainFragment) {
                if (!mainFragment.isAdded) {
                    AppLogger.BASIC.d(TAG, "bindFragments commit mainFragment")
                    replace(R.id.start_container, mainFragment, MainFragment.TAG)
                }
                mainFragment.onViewPagerSelectChange = ::onViewPagerSelectChange
            }
            if (sharedViewModel.twoPane) {
                if (noteDetailFragment != null && !noteDetailFragment.isAdded) {
                    replace(R.id.main_end_container_note, noteDetailFragment, NoteDetailFragment.TAG)
                }
                if (todoListFragment != null && !todoListFragment.isAdded) {
                    replace(R.id.main_end_container_todo, todoListFragment, TodoListFragment.TAG)
                }
            }
        }
        AppLogger.BASIC.d(TAG, "bindFragments initTabPosition=$initTabPosition")
        if (sharedViewModel.twoPane) {
            onViewPagerSelectChange(initTabPosition)
        }
    }

    fun setStartContainerVisible() {
        if (isNeedDoSpecialFlag()) {
            mIsFromAppCard = false
            binding?.startContainer?.postDelayed({
                binding?.startContainer?.alpha = 1f
                doSwitchAnimator(bindingEnd?.mainEndContainerTodo, bindingEnd?.mainEndContainerNote)
            }, DELAY_TIME)
        }
    }
    /**
     * Viewpager select callback
     */
    private fun onViewPagerSelectChange(tabIndex: Int) {
        AppLogger.BASIC.d(TAG, "onViewPagerSelectChange $tabIndex")
        when (tabIndex) {
            NOTE_INDEX -> {
                mIsFromAppCard = false
                doSwitchAnimator(bindingEnd?.mainEndContainerNote, bindingEnd?.mainEndContainerTodo)
                val noteDetailFragment =
                    (supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG) ?: (fragmentList[DETAIL_INDEX])) as? NoteDetailFragment
                noteDetailFragment?.onFragmentSelected()
            }

            TODO_INDEX -> {
                if (isNeedDoSpecialFlag()) {
                    binding?.startContainer?.alpha = 0f
                } else {
                    doSwitchAnimator(
                        bindingEnd?.mainEndContainerTodo,
                        bindingEnd?.mainEndContainerNote
                    )
                }
                val todoListFragment =
                    (supportFragmentManager.findFragmentByTag(TodoListFragment.TAG) ?: (fragmentList[DETAIL_TODOLIST_INDEX])) as? TodoListFragment
                todoListFragment?.onFragmentSelected()
            }
        }
        initTabPosition = tabIndex
    }

    private fun isNeedDoSpecialFlag(): Boolean {
        return mIsFromAppCard && sharedViewModel.twoPane
    }
    /**
     * 待办/笔记切换动效
     */
    private fun doSwitchAnimator(inView: View?, outView: View?) {
        AppLogger.BASIC.d(TAG, "doSwitchAnimator")
        if (inView == null || outView == null) {
            AppLogger.BASIC.d(TAG, "doSwitchAnimator return null")
            return
        }
        if (switchFragmentAnimatorSet?.isRunning == true) {
            AppLogger.BASIC.d(TAG, "doSwitchAnimator cancel running")
            switchFragmentAnimatorSet?.cancel()
        }
        val inAnimator = ofFloat(inView, "alpha", 1F)
        val outAnimator = ofFloat(outView, "alpha", 0F)
        switchFragmentAnimatorSet = AnimatorSet().apply {
            duration = DURATION_300
            doOnStart {
                inView.isVisible = true
            }
            doOnEnd {
                outView.isVisible = false
            }
            playTogether(inAnimator, outAnimator)
            start()
        }
    }

    /**
     * Dispatch activityResult to [MainFragment]
     */
    private fun dispatchNoteActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        dispatchActivityResultToNoteList(requestCode, resultCode, data)
        if (sharedViewModel.twoPane) {
            when (requestCode) {
                CommonPermissionUtils.GUIDE_PERMISSIONS_NOTIFY_CODE,
                CommonPermissionUtils.GUIDE_PERMISSIONS_ALARM_CODE,
                CommonPermissionUtils.GUIDE_PERMISSIONS_SCREEN_ON_CODE -> dispatchActivityResultToNoteDetail(requestCode, resultCode, data)
            }
        }
    }

    private fun dispatchTodoActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_NOTIFY_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_NOTIFY_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_SCREEN_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_ALERT_CODE -> {
                dispatchActivityResultToTodoFragment(requestCode, resultCode, data)
            }
        }
    }


    private fun dispatchActivityResultToNoteList(requestCode: Int, resultCode: Int, data: Intent?) {
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
            if (this is MainFragment && this.isAdded) {
                getNoteListFragment()?.apply {
                    if (this.isAdded.not()) {
                        return
                    }
                    this.onActivityResult(requestCode, resultCode, data)
                }
            }
        }
    }

    private fun dispatchActivityResultToTodoFragment(requestCode: Int, resultCode: Int, data: Intent?) {
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.apply {
            if (this is MainFragment && this.isAdded) {
                getTodoFragment()?.apply {
                    if (this.isAdded.not()) {
                        return
                    }
                    this.onActivityResult(requestCode, resultCode, data)
                }
            }
        }
    }

    private fun dispatchActivityResultToNoteDetail(requestCode: Int, resultCode: Int, data: Intent?) {
        (supportFragmentManager.findFragmentByTag(NoteDetailFragment.TAG) as? NoteDetailFragment)?.apply {
            if (this.isAdded) {
                this.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    /**
     * Update detail parent fragment width
     */
    private fun updateDetailParentFragmentWidth() {
        val lineParams = binding?.dividerGuideLine?.layoutParams as? ConstraintLayout.LayoutParams
        val oldWidth = lineParams?.guideEnd ?: 0
        val newWidth = TwoPaneCalculateWidthUtils.getTwoPaneDetailSizeSafe(this)

        if (oldWidth != newWidth) {
            binding?.dividerGuideLine?.setGuidelineEnd(newWidth)
            AppLogger.BASIC.d(TAG, "updateDetailParentFragmentWidth:$newWidth")
        }
    }

    fun refreshCloudSyncTips() {
        supportFragmentManager.findFragmentByTag(MainFragment.TAG)?.let {
            if (it is MainFragment && it.isAdded) {
                it.refreshCloudSyncTips()
            }
        }
    }

    private fun initDmp() {
        searchBroadcastReceiver = SearchBroadcastUtils.register(this@MainActivity)
        lifecycleScope.launch(Dispatchers.IO) {
            val isAgreeDmp = PrivacyPolicyHelper.isAgreeDmpSearch()
            NoteSearchManager.init(isAgreeDmp)
            TodoSearchManager.init(isAgreeDmp)
        }
    }
}