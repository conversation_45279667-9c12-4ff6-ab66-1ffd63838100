/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:
 * * Description:
 * * Version: 1.0
 * * Date : 2024/11/18
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import android.text.TextUtils
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.oplus.note.data.SingleLiveEventData
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

/**
 * 父子级下，笔记列表中当前选中笔记信息
 */
data class SelectedRichNoteInfo(
    val note: RichNoteWithAttachments,
    val notebook: Folder,
    val highlightSearch: List<String> // 搜索结果中高亮信息
)

interface SelectedRichNoteViewModel {

    /**
     * 监听选中笔记变化，单次只能绑定一个observe，并且相同的value只会响应一次observe
     */
    fun observeSelectedRichNote(owner: LifecycleOwner, observer: Observer<SelectedRichNoteInfo?>)

    /**
     * 是否为当前选中笔记
     */
    fun isSelectedRichNote(localId: String?): Boolean

    /**
     * 是否为当前选中笔记的笔记本
     */
    fun isSelectedRichNoteOfNotebook(folderId: String?): Boolean

    fun getSelectedRichNote(): RichNote?

    /**
     * 是否为上一个选中笔记
     */
    fun isPreSelectedRichNote(localId: String?): Boolean

    fun hasSelectedRichNote(): Boolean

    /**
     * 更新选中笔记信息
     *
     * @param notify 是否通知监听方
     */
    fun updateSelectedRichNote(info: SelectedRichNoteInfo, notify: Boolean = true)

    fun resetSelectedRichNote()
}

class SelectedRichNoteViewModelDefaultImpl : SelectedRichNoteViewModel {

    private var preSelectedRichNote: SelectedRichNoteInfo? = null
    private var selectedRichNoteInfo: SelectedRichNoteInfo? = null
    private val selectedRichNoteInfoLiveData = SingleLiveEventData<SelectedRichNoteInfo?>()

    /**
     * selectedRichNoteInfoLiveData 单次只能绑定一个observe，并且相同的value只会响应一次observe
     */
    override fun observeSelectedRichNote(owner: LifecycleOwner, observer: Observer<SelectedRichNoteInfo?>) {
        AppLogger.BASIC.d("NoteListFragment", "observeSelectedRichNote")
        selectedRichNoteInfoLiveData.observe(owner, observer)
    }

    override fun isSelectedRichNote(localId: String?): Boolean {
        val selectedLocalId = selectedRichNoteInfo?.note?.richNote?.localId
        if (localId == null || TextUtils.isEmpty(selectedLocalId)) {
            return false
        }
        return selectedLocalId == localId
    }

    override fun isSelectedRichNoteOfNotebook(folderId: String?): Boolean {
        return folderId != null && selectedRichNoteInfo?.notebook?.guid == folderId
    }

    override fun getSelectedRichNote(): RichNote? {
        return selectedRichNoteInfo?.note?.richNote
    }

    override fun isPreSelectedRichNote(localId: String?): Boolean {
        val preSelectedLocalId = preSelectedRichNote?.note?.richNote?.localId
        if (localId == null || TextUtils.isEmpty(preSelectedLocalId)) {
            return false
        }
        return preSelectedLocalId == localId
    }

    override fun hasSelectedRichNote(): Boolean {
        return selectedRichNoteInfo != null && !TextUtils.isEmpty(selectedRichNoteInfo?.note?.richNote?.localId)
    }

    override fun updateSelectedRichNote(info: SelectedRichNoteInfo, notify: Boolean) {
        AppLogger.BASIC.d("NoteListFragment", "info:${info.note.richNote.localId},notify:$notify")
        preSelectedRichNote = selectedRichNoteInfo
        selectedRichNoteInfo = info
        if (notify) {
            notifySelectedRichNoteInfoChanged()
        }
    }

    /**
     * selectedRichNoteInfoLiveData 单次只能绑定一个observe，并且相同的value只会响应一次observe
     */
    private fun notifySelectedRichNoteInfoChanged() {
        selectedRichNoteInfoLiveData.value = selectedRichNoteInfo
    }

    override fun resetSelectedRichNote() {
        preSelectedRichNote = null
        selectedRichNoteInfo = null
        selectedRichNoteInfoLiveData.value = null
    }
}

