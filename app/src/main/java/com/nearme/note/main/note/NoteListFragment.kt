/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteListFragment.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.text.format.DateFormat
import android.util.ArrayMap
import android.view.DragEvent
import android.view.Gravity
import android.view.HapticFeedbackConstants
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.WindowManager
import android.view.animation.LinearInterpolator
import android.widget.AbsListView
import android.widget.AdapterView
import android.widget.TextView
import androidx.annotation.MainThread
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.SearchView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.animation.doOnEnd
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager.OnBackStackChangedListener
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.panel.COUINavigationBarUtil
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.component.responsiveui.window.WindowWidthSizeClass
import com.nearme.note.DialogFactory
import com.nearme.note.DialogFactory.DIALOG_TYPE_AIKIT_BACK_OR_FULL
import com.nearme.note.DialogFactory.DIALOG_TYPE_STOP_AI_REWRITE_BY_CHANGENOTE
import com.nearme.note.MyApplication
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.edit.SaveImageAndShare
import com.nearme.note.activity.list.ItemClickHelper
import com.nearme.note.activity.list.NoteBookLabelAdapter
import com.nearme.note.activity.list.NoteItemAnimator
import com.nearme.note.activity.list.NoteListUpdateCallback
import com.nearme.note.activity.list.NoteModeSwitcher
import com.nearme.note.activity.list.NoteStaggeredGridLayoutManager
import com.nearme.note.activity.list.NoteViewHolder
import com.nearme.note.activity.list.QueryChangedListener
import com.nearme.note.activity.list.ToppedUtil
import com.nearme.note.activity.list.entity.NoteItem
import com.nearme.note.activity.notebook.NoteBookViewModel
import com.nearme.note.activity.richedit.NoteViewRichEditActivity
import com.nearme.note.activity.richedit.RichNoteSaveTransitionHelper
import com.nearme.note.activity.richedit.SplitScreenDataSyncManager
import com.nearme.note.activity.richedit.TransparentActivity
import com.nearme.note.activity.richedit.aigc.NoteViewEditAigcTextHelper
import com.nearme.note.activity.richedit.coverpaint.AdaptationCoverPaintUtil
import com.nearme.note.activity.richedit.thirdlog.NoteBinder
import com.nearme.note.activity.richedit.thirdlog.ThirdLogDetailActivity
import com.nearme.note.activity.richedit.thirdlog.ThirdLogDetailFragment
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.activity.richlist.NoteAdapterInterface
import com.nearme.note.activity.richlist.NoteSearchAdapterInterface
import com.nearme.note.activity.richlist.OnSelectionChangeListener
import com.nearme.note.activity.richlist.RichNoteDiffCallBack
import com.nearme.note.activity.richlist.RichNoteItem
import com.nearme.note.activity.richlist.RichNoteListAdapter
import com.nearme.note.activity.richlist.RichNoteListAdapter.Companion.CHECKBOX_POSITION
import com.nearme.note.activity.richlist.RichNoteSearchAdapter
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.common.Constants
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.control.list.SearchMenuHelper
import com.nearme.note.db.NotesProvider
import com.nearme.note.drag.DragResultCallback
import com.nearme.note.drag.NoteListDragHelper
import com.nearme.note.logic.AccountManager
import com.nearme.note.logic.MenuExecutor
import com.nearme.note.logic.MenuExecutor.ExecutorProgressListener
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.NoteSyncProcess.CloudSyncStateCallback
import com.nearme.note.logic.NoteSyncProcessProxy
import com.nearme.note.main.ActivitySharedViewModel
import com.nearme.note.main.BaseFragment
import com.nearme.note.main.MainActivity
import com.nearme.note.main.NoteSubTitleViewHelper
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.paint.PaintActivity
import com.nearme.note.paint.coverdoodle.CoverScaleRatio
import com.nearme.note.setting.SettingsActivity
import com.nearme.note.thirdlog.AIGCCollectManager
import com.nearme.note.thirdlog.ThirdLogNoteManager
import com.nearme.note.util.AccessibilityUtils
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.ClickUtils
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DataStatisticsHelper
import com.nearme.note.util.DensityHelper
import com.nearme.note.util.DeviceInfoUtils
import com.nearme.note.util.EnvirStateUtils
import com.nearme.note.util.FlexibleWindowUtils
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.Injector
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.LrcUtils
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.MultiClickFilter
import com.nearme.note.util.NavigateUtils
import com.nearme.note.util.NoteSearchManagerWrapper
import com.nearme.note.util.PRIVACY_PASSWORD_LIST_MOVE_CODE
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.SortRule
import com.nearme.note.util.SortRule.SORT_RULE_BY_CREATE_TIME
import com.nearme.note.util.SortRule.SORT_RULE_BY_UPDATE_TIME
import com.nearme.note.util.SortRule.readSortRule
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.util.TodoOfflineGuiderHelper
import com.nearme.note.util.WaterMark
import com.nearme.note.util.inputMethodManager
import com.nearme.note.util.isCollectionNotebook
import com.nearme.note.util.isSummaryNotebook
import com.nearme.note.util.postValueSafe
import com.nearme.note.util.refreshCard
import com.nearme.note.util.refreshCardAll
import com.nearme.note.view.StaggeredGridLayoutAnimationRecyclerView
import com.nearme.note.view.helper.MenuMultiSelectHelper
import com.nearme.note.view.helper.NavigationAnimatorHelper
import com.nearme.note.view.helper.UiHelper
import com.nearme.note.view.refresh.BounceCallBack
import com.nearme.note.view.refresh.BounceHandler
import com.nearme.note.view.refresh.DefaultHeader
import com.nearme.note.view.refresh.EventForwardingHelper
import com.nearme.note.view.scalebehavior.PrimaryTitleBehavior
import com.oplus.cloud.utils.PrefUtils
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.util.isCloudSyncEnableFun
import com.oplus.cloudkit.view.CloudKitInfoController
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.cloudkit.view.HeadTipsBaseController
import com.oplus.cloudkit.view.InfoNotifyControllerWrapper
import com.oplus.cloudkit.view.SyncGuideManagerWrapper
import com.oplus.note.NoteFileProvider
import com.oplus.note.R
import com.oplus.note.aigc.model.AIGCState
import com.oplus.note.common.SysDragConstants
import com.oplus.note.common.utils.AppFeatureHelper
import com.oplus.note.databinding.NoteListFragmentBinding
import com.oplus.note.edgeToEdge.DeDuplicateInsetsCallback
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.export.ExportAgentFactory
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.ChosenFolderInfo
import com.oplus.note.notebook.NotebookAgentFactory
import com.oplus.note.notebook.guide.ENCRYPTED
import com.oplus.note.notebook.setRecentScreenshotEnabled
import com.oplus.note.os.OsConfigurations
import com.oplus.note.os.ResponsiveUiHelper
import com.oplus.note.osdk.proxy.AnimationCallbackProxy
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.osdk.proxy.OplusInputMethodManagerProxy
import com.oplus.note.permission.PermissionManager.Companion.DIALOG_MESSAGE
import com.oplus.note.permission.PermissionManager.Companion.SHOW_ALARM_TIPS_ACTION
import com.oplus.note.questionnaire.api.question.QuestionFactory
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.util.NoteFeatureUtil
import com.oplus.note.scenecard.todo.ui.animation.HighLightAnimationHelper
import com.oplus.note.scenecard.utils.VibrateUtils
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.statistic.StatisticsNoteHome
import com.oplus.note.utils.NoteStatusProviderUtil
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.note.utils.SysDragManager
import com.oplus.note.utils.isPackageDisabled
import com.oplus.note.utils.toast
import com.oplus.note.view.EmptyContentView
import com.oplus.note.view.EmptyContentViewLazyLoader
import com.oplus.note.view.PressAnimView
import com.oplus.note.view.bubbletips.BubbleTipManager
import com.oplus.notes.webview.cache.api.IWebViewProxyCache
import com.oplus.notes.webview.cache.api.WebViewForceDestroyListener
import com.oplus.notes.webview.container.web.KeyboardManagerImpl
import com.oplus.richtext.core.utils.RichUiHelper
import com.oplus.richtext.editor.view.focus.FocusInfo
import com.oplus.todo.search.TodoSearchManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.Arrays
import java.util.Objects
import kotlin.math.abs
import kotlin.math.max

@Suppress("LargeClass")
class NoteListFragment : BaseFragment() {
    companion object {
        private const val TAG = "NoteListFragment"
        const val DELETE_ANIMATION_TRANSITION = 1000
        private const val ALPHA_DURATION = 160L
        private const val DELAY_TIME = 100L
        private const val WECHAT_PACKAGE_NAME = "com.tencent.mm"
        private const val DELAY_200 = 200L
        private const val NOTE_ANIMATION_DELAY = 1000L // 系统那边建议用1秒timeout兜底，因为系统动画都是按1秒来兜底异常

        private var menuClickId = -1
        fun newInstance(): NoteListFragment {
            return NoteListFragment()
        }
        private const val SUB_CREATE_TIME_POS = 0
        private const val SUB_UPDATE_TIME_POS = 1
        private const val SUB_GROUP_PEOPLE_POS = 2
        private const val SUB_NOT_GROUP_POS = 3

        private const val FADE_AND_SHOW_TIME: Long = 200
        private var isHideFloatingButton = false //是无缝隐藏


        fun prepareLayoutManager(fragment: NoteListFragment): NoteStaggeredGridLayoutManager {
            val weakReference = WeakReference(fragment)
            return object : NoteStaggeredGridLayoutManager(RichNoteListAdapter.LIST_SPAN_COUNT, VERTICAL) {
                override fun canScrollVertically(): Boolean {
                    return super.canScrollVertically() && canScrollVertically(weakReference)
                }
            }
        }

        fun prepareTextChangeListener(fragment: NoteListFragment): SearchView.OnQueryTextListener {
            val weakReference = WeakReference(fragment)
            return object : SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(s: String): Boolean {
                    return false
                }

                override fun onQueryTextChange(newText: String): Boolean {
                    return weakReference.get()?.onQueryTextChange(newText) ?: false
                }
            }
        }

        fun prepareStatusChangedListener(fragment: NoteListFragment): ThirdLogNoteManager.StatusChangeListener {
            val weakReference = WeakReference(fragment)
            return object : ThirdLogNoteManager.StatusChangeListener {
                override fun onStatusChanged(noteId: String) {
                    weakReference.get()?.onStatusChanged(noteId)
                }

                override fun onStopCreateSummary(noteId: String): Boolean {
                    return super.onStopCreateSummary(noteId)
                }
            }
        }

        fun canScrollVertically(fragment: WeakReference<NoteListFragment>): Boolean {
            return fragment.get()?.canScrollVertically() ?: false
        }
    }

    var twoPane: Boolean = false

    val searchMenuHelper by lazy {
        SearchMenuHelper(this)
    }

    val noteListViewModel by viewModels<NoteListViewModel>({ requireActivity() })
    private val noteViewModel by viewModels<NoteViewModel>({ requireActivity() })
    private val noteMarginViewModel by viewModels<NoteListMarginViewModel>({ requireActivity() })
    private val sharedViewModel by viewModels<ActivitySharedViewModel>({ requireActivity() })
    private val noteBookViewModel by activityViewModels<NoteBookViewModel>()
    val notebookAgent by NotebookAgentFactory.create(this)
    private val notebookEncryptAgent = NotebookAgentFactory.createEncryptAgent(this)

    var binding: NoteListFragmentBinding? = null
    private val editMenuStub = lazy { binding?.noteEditMenuStub?.viewStub }
    private val editMenuStubSecondary = lazy { binding?.noteEditMenuStubSecondary?.viewStub }
    private var lastSearchList: MutableList<String>? = null
    /** 父子集下，笔记添加涂鸦保存时，父级直接切换到手写笔记本 */
    private var isChangingPaintFolder = false
    private var weakObserver: WeakObserver? = null
    private var isDeleteOperation = false
    private var recentDelNoteObserver: RecentDelNoteObserver? = null
    private var isFromSearchClick: Boolean = false
    var searchLongClickNoteId: String? = null
    // 当前是否是onResume状态
    private var isOnResume = false
    // 最后一次点击的列表项view
    private var clickPressView: PressAnimView? = null
    private val keyboardManager by lazy { KeyboardManagerImpl() }
    // 当前等待刷新到界面的列表数据
    private var pendingRichNoteItemList: List<RichNoteItem>? = null
    private val seamlessAnimationCallback by lazy {
        object : AnimationCallbackProxy() {
            private var currentProgress = -1F

            override fun animationProgress(progress: Float) {
                currentProgress = progress
            }

            override fun onAnimationStart(isOpen: Boolean) {
                AppLogger.BASIC.d(TAG, "onSeamlessAnimationStart: isOpen=$isOpen")
                if (isOpen) {
                    lifecycleScope.launch(Main) {
                        (context as? MainActivity)?.binding?.fab?.hide()
                        isHideFloatingButton = true
                    }
                }
            }

            override fun onAnimationEnd(isOpen: Boolean) {
                // 是否是页面开启动画执行已结束
                val isOpenAnimEnd = (!isOpen && currentProgress == 0F) || (isOpen && currentProgress == 1F)
                // 是否是页面返回关闭动画执行已结束
                val isCloseAnimEnd = (!isOpen && currentProgress == 1F) || (isOpen && currentProgress == 0F)
                AppLogger.BASIC.d(TAG, "onSeamlessAnimationEnd: isOpenAnimEnd=$isOpenAnimEnd, isCloseAnimEnd=$isCloseAnimEnd," +
                        " isOpen=$isOpen, progress=$currentProgress")
                currentProgress = -1F
                if (isCloseAnimEnd) {
                    clickPressView?.setViewSeamlessAnimationCallback(null)
                    view?.removeCallbacks(updateNoteListCallback)
                    clickPressView = null
                }
                lifecycleScope.launch(Main) {
                    if (isCloseAnimEnd) {
                        AppLogger.BASIC.d(TAG, "SeamlessAnimationEnd, data=${pendingRichNoteItemList != null}")
                        if (pendingRichNoteItemList != null) {
                            updateNoteList(pendingRichNoteItemList!!, "animationEnd")
                            pendingRichNoteItemList = null
                        }
                        (context as? MainActivity)?.binding?.fab?.show()
                        isHideFloatingButton = false
                    }
                }
            }
        }
    }
    private val updateNoteListCallback by lazy {
        object : Runnable {
            override fun run() {
                AppLogger.BASIC.d(TAG, "updateNoteListCallback, data=${pendingRichNoteItemList != null}")
                if (pendingRichNoteItemList != null) {
                    updateNoteList(pendingRichNoteItemList!!, "updateNoteListCallback")
                    pendingRichNoteItemList = null
                }
                clickPressView?.setViewSeamlessAnimationCallback(null)
                clickPressView = null
            }
        }
    }

    val adapter by lazy {
        RichNoteListAdapter(requireContext(), notebookAgent.getCurrentNotebook(), noteListViewModel.selectionManager).apply {
            setOnItemClick(object : RichNoteListAdapter.ItemCallback {
                override fun setOnItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                    if (MultiClickFilter.isEffectiveShortClick()) {
                        clickPressView?.setViewSeamlessAnimationCallback(null)
                        clickPressView = null
                        val pressView = view as? PressAnimView
                        val isTwoPane = resources.getBoolean(R.bool.is_two_panel)
                        if (!isTwoPane && !isEditMode() && (pressView?.isSeamlessAnimationSupport() == true)) {
                            clickPressView = pressView.apply {
                                // 在这里添加高度设置调用
                                val screenHeight = resources.displayMetrics.heightPixels
                                setSeamlessCoverHeights(
                                    screenH = screenHeight,
                                    topH = binding?.appBar?.height ?: 0,
                                    bottomH = sharedViewModel.bottomMenuParentHeight
                                )
                                setViewSeamlessAnimationCallback(seamlessAnimationCallback)
                            }
                        }
                        listItemClick(adapter, view, position)
                    }
                }

                @RequiresApi(Build.VERSION_CODES.TIRAMISU)
                override fun setOnItemLongClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                    if (MultiClickFilter.isEffectiveShortClick()) {
                        listItemLongClick(adapter, view, position)
                    }
                }
            })
        }
    }

    val searchAdapter by lazy {
        RichNoteSearchAdapter(requireContext(), noteListViewModel.currentFolder.value).apply {
            setOnItemClick(object : RichNoteSearchAdapter.ItemCallback {
                override fun setOnItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                    if (adapter !is NoteSearchAdapterInterface<*>) {
                        return
                    }
                    val targetAdapter = adapter as NoteSearchAdapterInterface<*>
                    if (!targetAdapter.isHeaderView(position)) {
                        val guid = targetAdapter.getClickItemGuid(position)
                        if (MultiClickFilter.isEffectiveClick(view) && !TextUtils.isEmpty(guid)) {
                            //搜索状态弹出输入法，点击item需要删除输入法弹框
                            FocusInfo.hideSoftInput(context, searchView?.searchView)
                            val attachmentId =
                                getItem(position)?.attachment?.attachmentId ?: getItem(position)?.webNote?.item_id ?: ""
                            checkAIGCState {
                                openNoteInSearchMode(guid, getClickItemFlag(position), attachmentId)
                            }
                        }
                    }
                }
                override fun setOnLongItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                    if (adapter !is RichNoteSearchAdapter) {
                        AppLogger.BASIC.d(TAG, "search list setOnLongItemClick, adapter mismatch")
                        return
                    }
                    searchView?.clearFocus()
                    //搜索过来标记点击
                    noteListViewModel.isSearchButtonClicked = true
                    searchMenuHelper.showPopUpMenu(context, view, position)
                }
            })
        }
    }
    private val mSubTitleViewHelper by lazy { NoteSubTitleViewHelper() }

    private var noteModeSwitcher: NoteModeSwitcher? = null
    private var layoutManager: StaggeredGridLayoutManager? = null
    private var noteListCountPre = -1
    private var changeSort = false
    private var loadDataFinished = false
    private var supportTitleMarginStart = 0
    private var behavior: PrimaryTitleBehavior? = null
    private var initiateSearchView = false
    private var searchItem: MenuItem? = null
    private var searchView: COUISearchViewAnimate? = null
    private val searchViewAnimatorHelper by lazy {
        SearchViewAnimatorHelper()
    }
    private var onlyMaskAnim = false
    private var hasPlayAnimation = false
    private var infoNotifyController: InfoNotifyControllerWrapper? = null
    private var guideManager: SyncGuideManagerWrapper? = null
    private var emptyContentViewLazyLoader: EmptyContentViewLazyLoader? = null
    private var notePlaceHolderView: View? = null
    private var mPlaceHolderViewHeight = 0  // 记录列表头部空控件 的占位高度
    private var searchPlaceHolderView: View? = null
    private var isQueryTextCleared = false
    private var isRestoreFlag = false
    private var noteSyncProcess: NoteSyncProcessProxy? = null
    private var preHourFormat = false
    private var isCurrentFolderFirstInit = true
    private var isSelectionModeFirstInit = true
    private var mInZoomWindowState = false
    private var mOS16ToolNavigationBg: View? = null
    private var mToolNavigationView: COUINavigationView? = null
    private var mToolNavigationViewSecondary: COUINavigationView? = null
    private var mNavigationAnimatorHelper: NavigationAnimatorHelper? = null
    private var mIsAnimating = false
    private var mSelectItemSize = 0
    /**云同步开关状态，为 null 时表示还没有获取成功*/
    private var mSyncEnable: Boolean? = null
    private var mNoteListHelper: NoteListHelper? = null
    private var mIsEncryptOrDecrypt = false
    private var mDialogClickListener: DialogFactory.DialogOnClickListener? = null
    private var mDialogFactory: DialogFactory? = null
    private var localReceiver: LocalReceiver? = null
    private var guidHashMap: ArrayMap<String, Int>? = null
    private var downY = 0F
    private var moveY = 0F
    private var mCallBack: BounceCallBack? = null
    private var isGridMode = false
    private var isSwitchGrideModing = false
    private var needRefreshList = false
    private var mDeleteDialog: Dialog? = null
    private var mToolbarOverflowPopupWindow: COUIPopupListWindow? = null
    private var mSubCheckedPositionByTime = SUB_UPDATE_TIME_POS
    private var mSubCheckedPositionByGroup = SUB_NOT_GROUP_POS

    private var mIsFirstLoadNoteList = true

    private var mSearchPageHelper: ImageHelper? = null
    private var mEmptyContentPageHelper: ImageHelper? = null
    private var thirdLogListener: ThirdLogNoteManager.StatusChangeListener? = null
    private var localId: String? = null
    private var isSummary = false
    private var isSpecifiedFolder = false
    private var tempNotes: MutableList<RichNoteItem>? = null
    private var isSortAnimateRunning = false
    private var isFirstOnResume = true
    private var isSyncing = false
    private var nowShowSyncTip: Boolean = false

    private var initFolderGuid: String? = null
    private var initFolderName: String? = null
    private var initFolder: Folder? = null

    private val highLightAnimationHelper by lazy {
        HighLightAnimationHelper()
    }
    private var mAISpaceDragView: View? = null

    private var aigcInterruptAction: (() -> Unit?)? = null

    private val webViewForceDestroyListener = object : WebViewForceDestroyListener {
        override fun onWebViewDestroyed() {
            AppLogger.BASIC.d(TAG, "onWebViewDestroyed")
            resetCheckedInfo()
        }
    }
    private val viewStubCreateByAi = ViewStubCreateByAi()
    private var hasBeenSetData = false
    private var isAiAskQueryChange = false
    private var isInLeftFadingAnimation = false
    private var isInRightFadingAnimation = false
    private var isIgnoreDiffVersion = false

    private var sysDragManager: SysDragManager? = null
    private var leftFadeAlphaAnimator: ObjectAnimator? = null
    private var rightFadeAlphaAnimator: ObjectAnimator? = null
    val aigcTextHelper by lazy { NoteViewEditAigcTextHelper() }
    private var needShowKit = true
    private var windowInsetsBottomValue = 0
    private var todoGuiderHelper: TodoOfflineGuiderHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mInZoomWindowState = sharedViewModel.inZoomWindowState
        supportTitleMarginStart = resources.getDimensionPixelOffset(R.dimen.toolbar_support_title_margin_start)
        initNoteListHelper()
        preHourFormat = DateFormat.is24HourFormat(context)
        guidHashMap = ArrayMap()
        localReceiver = LocalReceiver()
        val intentFilter = IntentFilter(Constants.ACTION_SAVE_NOTE_COMPLETE)
        intentFilter.addAction(Constants.ACTION_SAVE_NOTE_FINISHED)
        intentFilter.addAction(Constants.ACTION_SAVE_PICTURE_COMPLETE)
        intentFilter.addAction(Constants.ACTION_DOWNLOAD_SKIN_COMPLETE)
        intentFilter.addAction(Constants.ACTION_NOTIFICATION_GRANT)
        intentFilter.addAction(Constants.ACTION_REFRESH_DATA_ON_AIGC_DELETE)
        intentFilter.addAction(SHOW_ALARM_TIPS_ACTION)
        LocalBroadcastManager.getInstance(requireContext())
                .registerReceiver(localReceiver!!, intentFilter)

        //注册监听，得到回调后更新状态值
        registerRecentDelNoteObserver(activity)
        registerChooseNotebookListener()
        initIsIgnoreDiffVersion()
        sysDragManager = context?.let { SysDragManager(it, lifecycle) }
        todoGuiderHelper = TodoOfflineGuiderHelper(this)
    }

    private fun initIsIgnoreDiffVersion() {
        isIgnoreDiffVersion = SharedPreferencesUtil.getInstance().getBoolean(
            appContext, SharedPreferencesUtil.DIFF_VERSION_TIPS,
            SharedPreferencesUtil.IS_DIFF_VERSION_IGNORE, false
        )
        NoteFeatureUtil.setIsIgnoreDiffVersion(isIgnoreDiffVersion)
    }

    private fun registerRecentDelNoteObserver(context: Context?) {
        AppLogger.BASIC.d(TAG, "registerRecentDelNoteObserver")
        recentDelNoteObserver = RecentDelNoteObserver(
            Handler(Looper.getMainLooper()),
            this
        )
        context?.contentResolver?.registerContentObserver(
            NoteStatusProviderUtil.recentDelNoteUri,
            true,
            recentDelNoteObserver!!
        )
    }

    private fun unRegisterRecentDelNoteObserver(context: Context?) {
        try {
            recentDelNoteObserver?.let {
                context?.contentResolver?.unregisterContentObserver(it)
                recentDelNoteObserver = null
            }
        } catch (ignore: Exception) {
            AppLogger.BASIC.e(TAG, "unRegisterRecentDelNoteObserver error", ignore)
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        activity?.intent?.apply {
            /**
             * 目前泛在卡片、速览卡片跳转传参只能传string
             */
            IntentParamsUtil.getStringExtra(this, NotesProvider.IS_SPECIFIED_FOLDER)?.let {
                    isSpecifiedFolder = it == "isTrue"
            }
            isSummary = IntentParamsUtil.getBooleanExtra(this, TransparentActivity.IS_SUMMARY, false)
            val note = IntentParamsUtil.getParcelableExtra<RichNoteWithAttachments>(this, WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE)
            if (note != null) {
                localId = note.richNote.localId
                this.putParcelableArrayListExtra(WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE, null)
                this.putExtra(TransparentActivity.IS_SUMMARY, false)
            }
        }

        tryParseInitFolderFromInput()
    }

    /**
     * 解析初始化笔记本信息
     */
    private fun tryParseInitFolderFromInput() {
        val intentFromActivity = activity?.intent ?: return

        val folder = IntentParamsUtil.getParcelableExtra<Folder>(intentFromActivity, MainActivity.EXTRA_FOLDER)
        if (folder != null) {
            initFolder = folder
            intentFromActivity.removeExtra(MainActivity.EXTRA_FOLDER)
            return
        }

        val folderGuid = IntentParamsUtil.getStringExtra(intentFromActivity, NotesProvider.COL_NOTE_FOLDER_GUID)
        if (TextUtils.isEmpty(initFolderGuid)) {
            initFolderGuid = folderGuid
            intentFromActivity.removeExtra(NotesProvider.COL_NOTE_FOLDER_GUID)
            return
        }
    }

    /**
     * 若存在初始化笔记本信息，需要切换至该笔记本显示
     */
    private fun switchToInitFolderIfNeeded() {
        if (initFolder != null) {
            AppLogger.BASIC.d(TAG, "switchToInitFolderIfNeeded by initFolder")
            switchFolder(initFolder!!)
            initFolder = null
            return
        }

        if (initFolderGuid != null) {
            AppLogger.BASIC.d(TAG, "switchToInitFolderIfNeeded by initFolderGuid")
            val folder = notebookAgent.getNotebooks().find { it.guid == initFolderGuid }
            if (folder != null) {
                switchFolder(folder)
            }
            initFolderGuid = null
            return
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = NoteListFragmentBinding.inflate(inflater, container, false)
        binding?.lifecycleOwner = this
        binding?.viewModel = noteMarginViewModel
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initiateWindowInsets()

        if (savedInstanceState != null) {
            noteListViewModel.isCreateDialog =
                savedInstanceState.getBoolean(Constants.DIALOG_REBUILD_TAG, false)
        }
        mSearchPageHelper = ImageHelper(requireActivity())
        mEmptyContentPageHelper = ImageHelper(requireActivity())
        initiateToolbar()
        initNoteBookLabel()
        initiateAISpaceDragView()
        initiateNoteItemListView(savedInstanceState)
        initBehavior()

        binding?.root?.post {
            initiateSearchView()
            initiateSearchViewAdapter()
        }

        initRefreshView()
        initiateEmptyPage()
        initiateObservers()
        initSearchListObserver()
        initRefreshAndPermissionObserver()
        initCallBack()
        NoteSearchManager.setLrcFunction { LrcUtils.getSpeechLogInfoList(it) }

        preWindowWidthSize = activity?.let { ResponsiveUiHelper.getWindowSizeClass(it) }
    }

    private fun getLeftFadeAlpha(): Float {
        val layoutManager = binding?.notebookLabelContainer?.listLabel?.layoutManager as? LinearLayoutManager
        val firstVisibleItemPosition = layoutManager?.findFirstCompletelyVisibleItemPosition() ?: -1
        return if (firstVisibleItemPosition <= 0) 0f else 1f
    }

    private fun leftFadeOfFloat() {
        val fadeView = if (RichUiHelper.isRTL()) binding?.notebookLabelContainer?.rightFade else binding?.notebookLabelContainer?.leftFade
        if (fadeView == null) {
            return
        }
        val alpha = getLeftFadeAlpha()
        if (!isInLeftFadingAnimation && alpha != fadeView.alpha) {
            isInLeftFadingAnimation = true
            leftFadeAlphaAnimator?.cancel()
            leftFadeAlphaAnimator = ObjectAnimator.ofFloat(fadeView, "alpha", fadeView.alpha, alpha).apply {
                setDuration(FADE_AND_SHOW_TIME)
                doOnEnd {
                    fadeView.alpha = getLeftFadeAlpha()
                    isInLeftFadingAnimation = false
                }
                start()
            }
        }
    }

    private fun getRightFadeAlpha(): Float {
        val layoutManager = binding?.notebookLabelContainer?.listLabel?.layoutManager as? LinearLayoutManager
        val lastVisibleItemPosition = layoutManager?.findLastCompletelyVisibleItemPosition() ?: -1
        val lastPosition = (layoutManager?.itemCount ?: 0) - 1
        return if (lastVisibleItemPosition == lastPosition) 0f else 1f
    }

    private fun rightFadeOfFloat() {
        val fadeView = if (RichUiHelper.isRTL()) binding?.notebookLabelContainer?.leftFade else binding?.notebookLabelContainer?.rightFade
        if (fadeView == null) {
            return
        }
        val alpha = getRightFadeAlpha()
        if (!isInRightFadingAnimation && alpha != fadeView.alpha) {
            isInRightFadingAnimation = true
            rightFadeAlphaAnimator?.cancel()
            rightFadeAlphaAnimator = ObjectAnimator.ofFloat(fadeView, "alpha", fadeView.alpha, alpha).apply {
                setDuration(FADE_AND_SHOW_TIME)
                doOnEnd {
                    fadeView.alpha = getRightFadeAlpha()
                    isInRightFadingAnimation = false
                }
                start()
            }
        }
    }

    private fun rightFadeVisibility() {
        val fadeView = if (RichUiHelper.isRTL()) binding?.notebookLabelContainer?.leftFade else binding?.notebookLabelContainer?.rightFade
        fadeView?.visibility = if (isSpecialDeviceType()) View.VISIBLE else View.GONE
    }

    private fun isSpecialDeviceType(): Boolean {
        return CoverScaleRatio.getDeviceType() == CoverScaleRatio.IS_PAD ||
                ResponsiveUiHelper.isUnfoldState(requireActivity())
    }
    private val labelAdapter by lazy {
        NoteBookLabelAdapter(
            currentFolder = noteListViewModel.currentFolder.value?.toFolder(),
            onEntranceClickListener = {
                tryShowNotebookList()
            },
            itemClick = { _, folder ->
                if (binding?.noteList?.scrollState != RecyclerView.SCROLL_STATE_IDLE || ClickUtils.isFastDoubleClick(ClickUtils.DURATION_300)) {
                    return@NoteBookLabelAdapter
                }

                if (noteListViewModel.currentFolder.value?.guid == folder.guid) {
                    return@NoteBookLabelAdapter
                }

                checkAIGCState {
                    switchFolder(folder)
                }
            })
    }

    private fun switchFolder(target: Folder) {
        AppLogger.BASIC.d(TAG, "Folder changed, guid:" + target.guid)
        if (isEditMode()) {
            exitEditMode(false)
        }

        if (FolderFactory.isRecentDeleteFolder(target)) {
            val status = if (NoteStatusProviderUtil.getStatus(
                    requireContext(),
                    NoteStatusProviderUtil.FLAG_RECENT_DEL_FOLDER_ENCRYPT_STATUS
                )
            ) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
            target.encrypted = status
        }
        if (target.isEncrypted && notebookAgent.getCurrentNotebook()?.isEncrypted != true) {
            notebookEncryptAgent.check { result ->
                if (result) {
                    noteListViewModel.isChangeFolderFromChoiceFolder = true
                    notebookAgent.updateCurrentFolder(target)
                }
            }
        } else {
            noteListViewModel.isChangeFolderFromChoiceFolder = true
            noteListViewModel.defaultSelectedFromChoiceFolder = true
            noteListViewModel.changeNoteListByInitOrChangeFolder = true
            notebookAgent.updateCurrentFolder(target)
        }
    }

    private fun getAllNoteCounts(): Int {
        return notebookAgent.findRichNoteCountInFolder(notebookAgent.findAllNoteFolder())
    }

    private fun hasNotesInCurrentFolder(): Boolean {
        return notebookAgent.findRichNoteCountInFolder(notebookAgent.getCurrentNotebook()) > 0
    }

    private fun isCurrentRecentDeleteNotebook(): Boolean {
        return FolderFactory.isRecentDeleteFolder(notebookAgent.getCurrentNotebook())
    }

    /**
     * mToolNavigationView有一个显示动画。
     * 要等他完全显示出来才去执行重建的逻辑，
     * 不然锚点不准
     */
    private fun checkShowDialog() {
        if (noteListViewModel.isCreateDialog) {
            if (mDialogFactory == null) {
                initDialogFactory()
            }
            mDialogFactory?.rebuildAlertDialog(
                true,
                noteListViewModel.dialogType,
                noteListViewModel.dialogExtra,
                false
            )
            noteListViewModel.isCreateDialog = false
        }
    }

    private fun onStatusChanged(noteId: String) {
        val isInsertingEnd = ThirdLogNoteManager.getInstance().isNoteInserted(noteId)
        if (isInsertingEnd) {
            AppLogger.BASIC.d(TAG, "$noteId is inserting end")
            val noteItems = adapter.getNoteItems()
            for (noteItem in noteItems) {
                val isSame = noteItem.data?.richNote?.localId == noteId
                if (isSame) {
                    lifecycleScope.launch(Main) {
                        kotlin.runCatching {
                            AppLogger.BASIC.d(TAG, "$noteId is inserting end notifyDataSetChanged")
                            adapter.notifyDataSetChanged()
                        }.onFailure {
                            AppLogger.BASIC.d(TAG, it.toString())
                        }
                    }
                    return
                }
            }
        }
    }

    private fun initCallBack() {
        thirdLogListener = prepareStatusChangedListener(this).also {
            ThirdLogNoteManager.getInstance().registerStatuesChangeListener(it)
        }
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.parent) { v, insets ->
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            windowInsetsBottomValue = systemBarInsets.bottom
            val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.statusBars())
            v.updatePadding(left = systemBarInsets.left, right = if (twoPane) 0 else systemBarInsets.right, top = stableStatusBarInsets.top)
            initToolNavigationMenu()
            val layoutParams = mToolNavigationView?.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParams?.bottomMargin = systemBarInsets.bottom
            mToolNavigationView?.layoutParams = layoutParams
            val layoutParamsToolNav = mToolNavigationViewSecondary?.layoutParams as? ViewGroup.MarginLayoutParams
            layoutParamsToolNav?.bottomMargin = systemBarInsets.bottom
            mToolNavigationViewSecondary?.layoutParams = layoutParamsToolNav
            if (ConfigUtils.isToDoDeprecated && NavigateUtils.isFullScreenNavigationGesture(context)) {
                binding?.refresh?.updatePadding(bottom = 0)
            } else {
                binding?.refresh?.updatePadding(bottom = systemBarInsets.bottom)
            }
            v.postDelayed({
                mToolNavigationView?.updatePadding(left = systemBarInsets.left, right = 0)
                mToolNavigationViewSecondary?.updatePadding(left = systemBarInsets.left, right = 0)
            }, DELAY_TIME)
            behavior?.setSystemBarInsetsTop(systemBarInsets.top)
            binding?.parent?.visibility = View.VISIBLE
        }
    }

    override fun onResume() {
        isOnResume = true
        super.onResume()
        mNoteListHelper?.onResume()
        resetHourFormat()
        if (sharedViewModel.isRecentDeleteFolder.value == true) {
            if (noteListViewModel.richNoteItemList.value?.isEmpty() == true) { //最近删除为空
                mToolNavigationView?.visibility = View.GONE
                mToolNavigationViewSecondary?.visibility = View.GONE
            } else { //最近删除不为空
                mToolNavigationView?.visibility = if (sharedViewModel.isSearch.value == true) View.GONE else View.VISIBLE
            }
            setOS16BottomMenuBgSameToNav()
        }
        AppLogger.BASIC.d(TAG, "onResume")
        if (!isFirstOnResume) {
            val hasNotes = adapter.getNoteItemCount() != 0
            NoteFeatureUtil.setIsFirstInApp(isFirstOnResume)
            resetMainEmptyPageAndSyncTips(hasNotes)
        }
        NoteFeatureUtil.setIsFirstInApp(isFirstOnResume)
        isFirstOnResume = false
        ensureFabIsVisibleWithDelay()
    }

    override fun onPause() {
        isOnResume = false
        super.onPause()
    }

    private fun refreshResumeCloud() {
        if (!isAdded) {
            return
        }

        if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
            AppLogger.BASIC.d(TAG, "notelist resume mba disable")
            noteListViewModel.syncEnable.value = false
        } else {
            AppLogger.BASIC.d(TAG, "notelist resume mba enable")
        }
        noteSyncProcess?.checkSyncSwitchStateTask()
        guideManager?.queryCloudKitSyncCloudState(context)
    }

    private fun resetHourFormat() {
        val currentHourFormat = DateFormat.is24HourFormat(context)
        if (preHourFormat != currentHourFormat) {
            preHourFormat = currentHourFormat

            if (sharedViewModel.isSearch.value == true) {
                searchAdapter.notifyDataSetChangedDelegate()
            } else {
                adapter.notifyDataSetChangedDelegate()
            }
        }
    }
    /**兜底无缝不会回调延迟一秒后恢复回来**/
    private fun ensureFabIsVisibleWithDelay() {
        if (!isHideFloatingButton) return
        lifecycleScope.launch {
            delay(NOTE_ANIMATION_DELAY)
            val context = context as? MainActivity ?: return@launch
            val fab = context.binding?.fab ?: return@launch
            fab.show()
            isHideFloatingButton = false
        }
    }
    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        AppLogger.BASIC.d(TAG, "onViewStateRestored")
        sharedViewModel.isSearch.value = false
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            NoteListHelper.REQUEST_CODE_PASSWORD -> {
                if (null != mNoteListHelper) {
                    if (resultCode == Activity.RESULT_OK) {
                        val noteData: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                        if (noteData?.first != null) {
                            val selectedNotes: Set<String> = HashSet(noteData.first)
                            mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_CONFIRM,
                                    noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), false)
                        }
                    } else {
                        noteListViewModel.isDeletingOrRecovering = false
                        AppLogger.BASIC.e(TAG, "delete all note but verify password failed!")
                    }
                }
            }
            PRIVACY_PASSWORD_LIST_MOVE_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    mIsEncryptOrDecrypt = true
                    val noteData: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotesData
                    if (noteData?.first != null) {
                        val selectedNotes: Set<String> = HashSet(noteData.first)
                        opsStatistic("01010101", selectedNotes)
                        mNoteListHelper?.setEncryptNoteData(noteListViewModel.currentFolder.value, selectedNotes)
                    }
                    mNoteListHelper?.startMoveExecutor(isAllNoteSelected())
                }
            }
        }
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        AppLogger.BASIC.d(TAG, "folder onMultiWindowModeChanged")
        binding?.mainTitle?.postDelayed({
            behavior?.updateToolbar()
        }, DELAY_TIME)

        resetMainEmptyPage()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AppLogger.BASIC.d(TAG, "onConfigurationChanged")
        checkIfWindowSizeChanged()
        rightFadeVisibility()
        val tempFlag = OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)
        if (tempFlag != mInZoomWindowState) {
            mInZoomWindowState = tempFlag
            if (sharedViewModel.isSearch.value != true) {
                behavior?.updateToolbar()
            }
            resetMainEmptyPage()
        }
        mDialogFactory?.onConfigurationChanged(newConfig)
        mEmptyContentPageHelper?.let { emptyContentViewLazyLoader?.update(it, twoPane) }
        binding?.mainTitle?.postDelayed({
            behavior?.updateToolbar()
        }, DELAY_TIME)
    }

    private var preWindowWidthSize: WindowWidthSizeClass? = null

    private fun checkIfWindowSizeChanged() {
        activity?.let {
            val newWindowWidthSizeClass = ResponsiveUiHelper.getWindowSizeClass(it)
            if (preWindowWidthSize != newWindowWidthSizeClass) {
                onWindowSizeClassChanged(newWindowWidthSizeClass)
                preWindowWidthSize = newWindowWidthSizeClass
            }
        } ?: AppLogger.BASIC.d(TAG, "checkIfWindowSizeChanged activity is null")
    }

    /**
     * 大中小屏变化时调用
     */
    private fun onWindowSizeClassChanged(newWindowSizeClass: WindowWidthSizeClass) {
        AppLogger.BASIC.d(TAG, "onWindowSizeChanged: $preWindowWidthSize -- $newWindowSizeClass")
        if (preWindowWidthSize == WindowWidthSizeClass.Compact || newWindowSizeClass == WindowWidthSizeClass.Compact) {
            reOpenNoteIfNeed(true)
        }
    }

    override fun backToTop() {
        if (sharedViewModel.isSearch.value == true) {
            searchViewAnimatorHelper?.resultList?.smoothScrollToPosition(0)
        } else {
            binding?.noteList?.stopScroll()
            binding?.noteList?.smoothScrollToPosition(0)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        todoGuiderHelper?.onDestroy()
        guideManager?.release()
        binding?.noteList?.layoutManager = null
        mCallBack = null
        leftFadeAlphaAnimator?.cancel()
        leftFadeAlphaAnimator = null
        rightFadeAlphaAnimator?.cancel()
        rightFadeAlphaAnimator = null
        ThirdLogNoteManager.getInstance().unRegisterStatuesChangeListener(thirdLogListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        view?.removeCallbacks(updateNoteListCallback)
        if (mDialogFactory != null) {
            mDialogFactory!!.onDestory()
            mDialogFactory = null
        }

        if (IWebViewProxyCache.ENABLE_CACHE) {
            Injector.injectFactory<IWebViewProxyCache>()?.removeWebViewForceDestroyListener(webViewForceDestroyListener)
        }

        mNoteListHelper?.onBack()
        mNoteListHelper?.onDestroy()
        noteSyncProcess?.release()
        if (localReceiver != null) {
            LocalBroadcastManager.getInstance(requireContext()).unregisterReceiver(localReceiver!!)
        }
        infoNotifyController?.onDestroy()
        searchView?.searchView?.setOnQueryTextListener(null)
        binding?.toolbar?.setOnMenuItemClickListener(null)
        binding?.toolbar?.hideOverflowMenu()
        unregisterChooseNotebookListener()
        noteListViewModel.changeToFolderModel = null
        weakObserver?.let {
            ChangeToPaintFolderModel.noteChangeToPaintFolder.removeObserver(it)
        }
        /*
         *反注册监听，防止内存泄漏
         */
        unRegisterRecentDelNoteObserver(activity)
    }

    private fun initiateToolbar() {
        binding?.toolbar?.inflateMenu(R.menu.menu_note_list)
        binding?.toolbar?.setPopupWindowOnDismissListener {
            if (menuClickId == R.id.edit_note) {
                activity?.let {
                    enterEditMode()
                    StatisticsUtils.setEventNoteEditModel()
                }
            }
            menuClickId = -1
        }
        updateToolbarMenuBySortRule(readSortRule())
        initiateSort()
        binding?.toolbar?.menuView?.apply {
            setOverflowMenuListener { popup ->
                mToolbarOverflowPopupWindow = popup
                val mainList = popup.itemList
                for (item in mainList) {
                    when (item.id) {
                        R.id.group_sort_by -> {
                            val selectPopupListItem1 = item.subMenuItemList[mSubCheckedPositionByTime]
                            val selectPopupListItem2 = item.subMenuItemList[mSubCheckedPositionByGroup]
                            selectPopupListItem1.isChecked = true
                            selectPopupListItem1.stateIcon =
                                    ContextCompat.getDrawable(context, com.support.poplist.R.drawable.coui_menu_ic_checkbox_selected)
                            selectPopupListItem2.isChecked = true
                            selectPopupListItem2.stateIcon =
                                    ContextCompat.getDrawable(context, com.support.poplist.R.drawable.coui_menu_ic_checkbox_selected)
                        }
                        R.id.sort_create_time -> {
                            if (item.isChecked) {
                                item.stateIcon = ContextCompat.getDrawable(context, com.support.poplist.R.drawable.coui_menu_ic_checkbox_selected)
                            }
                        }
                        R.id.sort_update_time -> {
                            if (item.isChecked) {
                                item.stateIcon = ContextCompat.getDrawable(context, com.support.poplist.R.drawable.coui_menu_ic_checkbox_selected)
                            }
                        }
                    }
                }
            }
        }
        binding?.toolbar?.setOnMenuItemClickListener {
            if ((adapter.inSelectionMode()) &&
                (it.itemId == R.id.select_all || (it.itemId == R.id.cancel && (!it.title.isNullOrEmpty())))) {
                onMenuItemClickListener(it) //进入退出选中模式，不弹出挽留弹窗。
            } else {
                checkAIGCState("OnMenuItemClickListener", null, false) {
                    onMenuItemClickListener(it)
                }
            }
            true
        }
    }

    private fun initiateSort() {
        val isSortUpdateTime = readSortRule() == SORT_RULE_BY_UPDATE_TIME
        mSubCheckedPositionByTime = if (isSortUpdateTime) SUB_UPDATE_TIME_POS else SUB_CREATE_TIME_POS
        mSubCheckedPositionByGroup = if (SortRule.isGroupByPeople()) SUB_GROUP_PEOPLE_POS else SUB_NOT_GROUP_POS
    }

    private fun refreshCheckBox(title: CharSequence) {
        val isSelectAll = getString(R.string.select_all).contentEquals(title)
        if (isSelectAll) {
            adapter.selectAll()
            refreshCheckBox(MenuMultiSelectHelper.MenuMode.SELECT_ALL)
        } else {
            adapter.deSelectAll()
            refreshCheckBox(MenuMultiSelectHelper.MenuMode.DE_SELECT_AL)
        }
    }
    private fun onMenuItemClickListener(menu: MenuItem) {
        menuClickId = menu.itemId
        val act = activity ?: return
        StatisticsUtils.setEventNoteMore()
        CloudKitSyncGuidManager.editFolderGuids = listOf(noteListViewModel.currentFolder.value?.guid.toString())
        when (menu.itemId) {
            R.id.select_all -> refreshCheckBox(menu.title ?: "")
            R.id.encrypt -> {
                encryptOrDecryptCurrentNotebook()
                if (noteBookViewModel.currentFolderEncrypted) {
                    StatisticsUtils.setEventCallSummaryListUnPrivate(context)
                } else {
                    StatisticsUtils.setEventCallSummaryListMakePrivate(context)
                }
            }

            R.id.cancel -> {
                if (!menu.title.isNullOrEmpty()) {
                    exitEditMode()
                    StatisticsUtils.setEventNoteEditCancel()
                }
            }

            R.id.mode_note -> changeMode(act, menu)
            R.id.jump_setting -> startSettingsActivity(act)
            R.id.sort_create_time, R.id.sort_create_time_1 -> switchAdapterSortRule(SORT_RULE_BY_CREATE_TIME)
            R.id.sort_update_time, R.id.sort_update_time_1 -> switchAdapterSortRule(SORT_RULE_BY_UPDATE_TIME)
            R.id.group_by_people -> saveGroupByPeople(true)
            R.id.group_not_by_people -> saveGroupByPeople(false)
            R.id.note_searchView -> onSearchViewClick()
            R.id.group_sort_by -> onGroupSortByClick()
        }
    }

    private fun onGroupSortByClick() {
        mToolbarOverflowPopupWindow?.apply {
            setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                for (item in itemList) {
                    if (item.id == R.id.group_sort_by) {
                        item.subMenuItemList[position].isChecked = true
                        when (item.subMenuItemList[position].groupId) {
                            R.id.group_3 -> mSubCheckedPositionByTime = position
                            R.id.group_4 -> mSubCheckedPositionByGroup = position
                        }
                        val subItem = binding?.toolbar?.menu?.findItem(R.id.group_sort_by)?.subMenu?.findItem(item.subMenuItemList[position].id)
                        if (subItem != null) onMenuItemClickListener(subItem)
                        dismiss()
                        break
                    }
                }
            })
        }
    }

    private fun enterEditMode() {
        if (sharedViewModel.isSearch.value == true) {
            AppLogger.BASIC.d(TAG, "onMenuItemClick, in search mode")
            return
        }
        sharedViewModel.noteSelectionModeChangeWithAnim = true
        behavior?.setIsEditMode(true)
        sharedViewModel.noteSelectionMode.value = true
    }

    private fun exitEditMode(withAnim: Boolean = true) {
        sharedViewModel.noteSelectionModeChangeWithAnim = withAnim
        behavior?.setIsEditMode(false)
        sharedViewModel.noteSelectionMode.value = false
    }

    private fun startSettingsActivity(act: Activity) {
        if (EnvirStateUtils.getComponentState(act, SettingsActivity::class.java)) {
            FlexibleWindowUtils.startFlexibleSettingsActivity(OplusFlexibleWindowManagerProxy.getFlexibleActivityPositionLeft(), act)
            StatisticsUtils.setEventSettingOpenCount(act)
        }
    }
    private fun saveGroupByPeople(flag: Boolean) {
        if (noteListViewModel.isGroupByPeople.value != flag) {
            SortRule.saveGroupByPeople(flag)
            noteListViewModel.isGroupByPeople.value = flag
            StatisticsUtils.setEventGroupPeopleType(context, flag)
        }
    }

    private fun changeFoldersOfDeleteEncrypt(newFolders: List<Folder>) {
        AppLogger.BASIC.d(TAG, "changeFoldersOfDeleteEncrypt")
        val currentFolder = noteListViewModel.currentFolder.value?.toFolder()
        val folders = newFolders.filter {
            updateCurrentFolderIfNeed(currentFolder, it)
            if (FolderFactory.isRecentDeleteFolder(it)) {
                context?.let { mContext ->
                    it.encrypted = if (NoteStatusProviderUtil.getStatus(mContext, NoteStatusProviderUtil.FLAG_RECENT_DEL_FOLDER_ENCRYPT_STATUS)) {
                        Folder.FOLDER_ENCRYPTED
                    } else {
                        Folder.FOLDER_UNENCRYPTED
                    }
                }
                AppLogger.BASIC.d(TAG, "isRecentDeleteFolder")
            }
            !it.isEncrypted
        }

        //云同步提示相关初始化提前至笔记本列表数据返回
        initInfoNotifyControllerWrapper()
        initSyncGuideManagerWrapper()
        folders?.let {
            labelAdapter.refresh(it.toMutableList())
            refreshIsAllSyncSwitchClosedState(folders)
        }
        adapter.setFolderList(newFolders)

        labelAdapter.scrollToCheckedPosition()
        NoteCardWidgetProvider.instance.postUIToCard(false)
        switchToInitFolderIfNeeded()
    }

    private fun updateCurrentFolderIfNeed(currentFolder: Folder?, folder: Folder) {
        currentFolder?.let {
            if (it.guid == folder.guid) {
                val isNameChange = it.name != folder.name
                val isEncryptedChange = it.encrypted != folder.encrypted
                AppLogger.BASIC.d(TAG, "updateCurrentFolder isNameChange:$isNameChange,isEncryptedChange:$isEncryptedChange")
                if (isNameChange || isEncryptedChange) {
                    noteListViewModel.currentFolder.postValue(FolderInfo(folder))
                }
            }
        }
    }

    private fun encryptOrDecryptCurrentNotebook(isOnResume: Boolean = false) {
        // 当在加密笔记本中，取消加密不需要校验密码
        val folder = notebookAgent.getCurrentNotebook()
        if (folder == null) {
            AppLogger.BASIC.e(TAG, "encryptOrDecryptCurrentNotebook error: current folder is null.")
            return
        }
        notebookEncryptAgent.encryptOrDecrypt(folder, { encrypted ->
            if (!encrypted && isOnResume) {
                val allNoteFolder =
                    notebookAgent.getNotebooks().find { it.guid == FolderFactory.FOLDER_GUID_ALL_NOTES }
                notebookAgent.updateCurrentFolder(allNoteFolder)
            }
        }, { isRecentDeleteEncrypt ->
            AppLogger.BASIC.d(TAG, "callBack encrypted recentdelete: $isRecentDeleteEncrypt")
            val currentNotebook = notebookAgent.getCurrentNotebook()
            currentNotebook?.encrypted =
                if (isRecentDeleteEncrypt) FolderInfo.FOLDER_ENCRYPTED else FolderInfo.FOLDER_UNENCRYPTED
            notebookAgent.updateRecentDeleteFolderCurrentFolder(currentNotebook)
        })
    }

    private fun changeMode(context: Context, menu: MenuItem) {
        val isGrid = sharedViewModel.isGridMode()
        val switchToGrid = !isGrid
        if (switchToGrid) {
            SharedPreferencesUtil.getInstance().putInt(context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.HOME_PAGE_MODE_KEY, SharedPreferencesUtil.HOME_PAGE_MODE_GRID_MODE)
        } else {
            SharedPreferencesUtil.getInstance().putInt(context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.HOME_PAGE_MODE_KEY, SharedPreferencesUtil.HOME_PAGE_MODE_LIST_MODE)
        }
        sharedViewModel.noteMode.value = switchToGrid
        menu.setTitle(if (switchToGrid) R.string.note_list_mode else R.string.note_grid_mode)

        if (isSyncing) {
            AppLogger.BASIC.d(TAG, "syncing and no need to trigger sync operation")
        } else {
            CloudSyncTrigger.sendDataChangedBroadcast(context)
        }
        StatisticsUtils.setEventSwitchNote(context, if (switchToGrid) StatisticsUtils.TYPE_SWITCH_NOTE_LIST else StatisticsUtils.TYPE_SWITCH_NOTE_GRID)
    }

    private fun canScrollVertically(): Boolean {
        return (binding?.refresh?.isRefreshing() == false)
    }

    private fun initiateAISpaceDragView() {
        lifecycleScope.launch(Dispatchers.IO) {
            val isSupportAISpaceFeature = context?.let { AppFeatureHelper.isSupportAISpaceFeature(it) }
            withContext(Main) {
                if (isSupportAISpaceFeature == true) {
                    mAISpaceDragView = binding?.gridItemNoteRealme?.viewStub?.inflate()
                } else {
                    binding?.gridItemNoteRealme?.viewStub?.visibility = View.GONE
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun listItemDrag(adapter: RecyclerView.Adapter<*>?, view: View, position: Int, vibrate: Boolean) {
        if ((adapter !is NoteAdapterInterface<*>) || (binding?.refresh?.isRefreshing() == true)) {
            return
        }
        if (adapter.inSelectionMode()) {
            AppLogger.BASIC.w(TAG, "SelectionManager is in SelectionMode")
            if (!adapter.isHeaderView(position)) {
                val viewHolder =
                    binding?.noteList?.findViewHolderForLayoutPosition(position)
                if (viewHolder is NoteViewHolder) {
                    val guid = adapter.getClickItemGuid(position)
                    if (TextUtils.isEmpty(guid)) {
                        AppLogger.BASIC.d(TAG, "listItemDrag guid is empty")
                        return
                    }
                    val isSelected =
                        noteListViewModel.selectedNotes.value?.first?.contains(guid)
                            ?: false
                    if (!isSelected) {
                        adapter.onItemClick(position, viewHolder)
                        notifySelectionChange()
                    }
                    view.post {
                        if (noteListViewModel.currentFolder.value?.guid != FolderInfo.FOLDER_GUID_RECENT_DELETE) {
                            lifecycleScope.launch(Main) {
                                tryStartDrag(view, vibrate, viewHolder)
                            }
                        } else {
                            AppLogger.BASIC.d(TAG, "drag in recent delete folder.")
                        }
                    }
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun listItemLongClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
        if ((adapter !is NoteAdapterInterface<*>) || (binding?.refresh?.isRefreshing() == true)) {
            return
        }
        if (adapter.getItemViewType(position) == NoteItem.TYPE_NOTE) {
            if (adapter.inSelectionMode()) {
                AppLogger.BASIC.w(TAG, "SelectionManager is in SelectionMode")
                //如果是在选择模式，则直接触发拖拽
                listItemDrag(adapter, view, position, false)
                return
            }
            if (position > -1) {
                enterEditMode()
                binding?.noteList?.post { listItemClick(adapter, view, position) }
            }
        }
    }

    private fun listItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
        StatisticsUtils.setEventNoteListClick()
        if (adapter !is RichNoteListAdapter) {
            return
        }
        if (!adapter.isHeaderView(position)) {
            val viewHolder = binding?.noteList?.findViewHolderForLayoutPosition(position)
            if (viewHolder is NoteViewHolder) {
                val guid = adapter.getClickItemGuid(position)
                if (TextUtils.isEmpty(guid)) {
                    AppLogger.BASIC.d(TAG, "listItemClick guid is empty")
                    return
                }
                if (adapter.inSelectionMode()) {
                    adapter.onItemClick(position, viewHolder)
                    notifySelectionChange()
                } else {
                    kotlin.runCatching {
                        AppLogger.BASIC.d(
                            TAG,
                            "currentNoteAigc ${sharedViewModel.currentNoteAigcState}"
                        )
                        checkAIGCState("noteList onClick", guid, true) {
                            onNoteItemClick(view, viewHolder, guid)
                        }
                    }.onFailure {
                        AppLogger.BASIC.d(TAG, "callKit METHOD_HIDE_WINDOW fail  ${it.message}")
                    }
                }
            } else {
                AppLogger.BASIC.e(TAG, "Invalid holder, position is$position")
            }
        }
    }

    private fun initiateNoteItemListView(savedInstanceState: Bundle?) {
        isGridMode = sharedViewModel.isGridMode()

        noteModeSwitcher = NoteModeSwitcher(binding?.noteList)
        updateRecyclerViewPadding()
        updateToolbarMenuByMode(isGridMode)

        // 1.set recyclerview layout manager
        layoutManager = prepareLayoutManager(this)
        val spanCount = if (isGridMode) RichNoteListAdapter.getGridSpanCount() else RichNoteListAdapter.LIST_SPAN_COUNT
        layoutManager?.spanCount = spanCount
        binding?.noteList?.layoutManager = layoutManager

        // 2.set recyclerview item animator
        val itemAnimator = NoteItemAnimator()
        binding?.noteList?.itemAnimator = itemAnimator
        itemAnimator.setAnimatorListener {
            // 在摘要界面时，置顶触发了列表更新，需要在动效执行完成后，刷新一下footer，以确保ai提示语的显示正常
            if (adapter.isSummary) {
                adapter.updateFooterView()
            }
        }
        // 3.init header view
        noteMarginViewModel.refreshPlaceHolderViewHeight(activity)
        val height = noteMarginViewModel.mNotePlaceHolderViewHeight.value ?: 0
        notePlaceHolderView = View(context)
        notePlaceHolderView?.apply {
            layoutParams = AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height)
            visibility = View.INVISIBLE
        }
        mPlaceHolderViewHeight = height
        adapter.initPlaceHolderView(notePlaceHolderView!!)
        adapter.mQuestionViewHelper = QuestionFactory.getProvider()?.getQuestionViewHelper(lifecycle)
        // 4.set recyclerview adapter
        adapter.setAdapterMode(if (isGridMode) RichNoteListAdapter.ADAPTER_MODE_GRID else RichNoteListAdapter.ADAPTER_MODE_LIST)
        adapter.setSelectionChangeListener(object : OnSelectionChangeListener {
            override fun onSelectionChange(count: Int) {
                notifySelectionChange()
            }
        })
        if (twoPane && (savedInstanceState != null) && (noteViewModel.hasSelectedRichNote())) {
            val selectedGuid = noteViewModel.getSelectedRichNote()?.localId
            if (selectedGuid != null) {
                adapter.setCheckedGuid(selectedGuid)
                searchAdapter.setCheckedGuid(selectedGuid)
            }
        }
        adapter.setSortRule(readSortRule())
        binding?.noteList?.adapter = adapter
        adapter.setOnDataLoadedListener { noteViewHolder, _ ->
            AppLogger.BASIC.d(TAG, "setOnDataLoadedListener mIsShowHeader=${adapter.mIsShowHeader}")
            if (adapter.mIsShowHeader == HeadTipsBaseController.CLOUD_TIP_HEADER_STATUS_UNDEFINED) {
                todoGuiderHelper?.tryShowNoteGuideTips()
            }
        }
        adapter.setInfoBoardAnimEndListener {
            AppLogger.BASIC.d(TAG, "setInfoBoardAnimEndListener")
            todoGuiderHelper?.tryShowNoteGuideTips()
        }
        noteListViewModel.defaultSelectedFromChoiceFolder = true
        noteListViewModel.changeNoteListByInitOrChangeFolder = true
        // 5.set recyclerview item click listener
        binding?.noteList?.addOnItemTouchListener(object : ItemClickHelper(
            shouldHandleItemTouch = { position -> !adapter.isHeaderView(position) },
            shouldHandleHapticFeedback = false
        ) {
            @RequiresApi(Build.VERSION_CODES.TIRAMISU)
            override fun onDragPress(
                adp: RecyclerView.Adapter<*>?,
                view: View,
                position: Int,
                vibrate: Boolean
            ) {
                super.onDragPress(adp, view, position, vibrate)
                listItemDrag(adp, view, position, vibrate)
            }
        })
    }


    private fun onNoteItemClick(view: View, viewHolder: NoteViewHolder, guid: String) {
        if (MultiClickFilter.isEffectiveClick(view)) {
            val isEncryptedNote = viewHolder.folderGuid?.let {
                noteBookViewModel.isEncryptedNote(it)
            } ?: false
            val isEncryptedFolder = notebookAgent.getCurrentNotebook()?.isEncrypted == true
            AppLogger.BASIC.d(TAG, "isEncryptedNote: $isEncryptedNote, isEncryptedFolder: $isEncryptedFolder")
            if (isEncryptedNote && !isEncryptedFolder) {
                notebookEncryptAgent.check { success ->
                    if (success) {
                        openEncryptedNote(guid)
                    }
                }
            } else {
                val currentNote = adapter.getRichNoteWithAttachmentsByLocalId(guid)
                val isCoverPaint = currentNote?.let { AdaptationCoverPaintUtil.isCoverPaintNotCharCount(it) } ?: false
                if(!isCoverPaint && twoPane){
                    val detail = activity?.supportFragmentManager?.findFragmentByTag(NoteDetailFragment.TAG)
                    if (detail != null && detail is NoteDetailFragment) {
                        detail.removeCoverFragment()
                    }
                }
                val animationEffects = view.getTag(view.id) as? Bundle
                openNote(guid, animationEffects = animationEffects)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    @MainThread
    suspend fun tryStartDrag(view: View, vibrate: Boolean, viewHolder: NoteViewHolder) {
        noteListViewModel.selectedNotes.value?.first?.let { localIds ->
            val map = localIds.map {
                adapter.getRichNoteWithAttachmentsByLocalId(it)
            }
            activity?.let { ctx ->
                val isAvailable = ExportAgentFactory.getDocAgent()?.getDetectDataState(ctx)
                // 6.set drag listener
                view.setOnDragListener { v, event ->
                    // 拖入接收方且松手
                    if (event.action == DragEvent.ACTION_DRAG_EXITED) {
                        if (isAvailable == false) {
                            view.cancelDragAndDrop()
                            v.setOnDragListener(null)
                            revokePermission(ctx, map)
                            sysDragManager?.unregisterSysDragReceiver()
                            return@setOnDragListener true
                        }
                    } else if (event.action == DragEvent.ACTION_DRAG_ENDED) {
                        val first =
                            layoutManager?.findFirstVisibleItemPositions(null)?.minOrNull() ?: 0
                        val last =
                            layoutManager?.findLastVisibleItemPositions(null)?.maxOrNull() ?: 0
                        var result = StatisticsUtils.DRAG_RESULT_SUCCESS
                        if (!event.result) {
                            result = StatisticsUtils.DRAG_RESULT_NOT_RESUME
                            toast(R.string.drag_not_consume)
                        }
                        adapter.notifyDragStateChanged(false, first, last)
                        v.setOnDragListener(null)

                        StatisticsUtils.setEventNoteDragOut(map.size, result)
                        AppLogger.BASIC.d(TAG, "onDrag: clear")
                        revokePermission(ctx, map)
                        sysDragManager?.unregisterSysDragReceiver()
                    } else if (event.action == DragEvent.ACTION_DRAG_ENTERED) {
                        // 拖回自身时发广播更新角标
                        sendSysDragBroadcast(v)
                    }
                    true
                }

                NoteListDragHelper.startDragNoteItem(ctx,
                    map,
                    mAISpaceDragView,
                    viewHolder,
                    view,
                    sysDragManager!!,
                    object : DragResultCallback {
                        override fun onDragResult(success: Boolean) {
                            AppLogger.BASIC.d(TAG, "onDragResult: $success")
                            if (success) {
                                val first =
                                    layoutManager?.findFirstVisibleItemPositions(null)?.minOrNull()
                                        ?: 0
                                val last =
                                    layoutManager?.findLastVisibleItemPositions(null)?.maxOrNull()
                                        ?: 0
                                adapter.notifyDragStateChanged(true, first, last)
                                if (vibrate) {
                                    view.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                                }
                            } else {
                                view.setOnDragListener(null)
                            }
                        }
                    })
            }
        }
    }

    /**
     * 发广播更新角标
     * @param view 拖拽的view
     */
    private fun sendSysDragBroadcast(view: View) {
        Intent(SysDragConstants.SYS_DRAG_BROADCAST).let {
            it.putExtra(
                SysDragConstants.SYS_DRAG_BADGE_STATUS,
                SysDragConstants.DRAG_BADGE_FORBID
            )
            it.setFlags(Intent.FLAG_RECEIVER_FOREGROUND)
            view.context.sendBroadcast(it)
        }
    }

    @SuppressLint("WrongConstant")
    private fun revokePermission(context: Context, notes: List<RichNoteWithAttachments?>) {
        if (notes.isNotEmpty()) {
            val getParentUri =
                notes[0]?.let {
                    NoteFileProvider.getDragAndDropFileUri(context.applicationContext, it, true)
                }
            if (getParentUri != null) {
                kotlin.runCatching {
                    context.revokeUriPermission(
                        WECHAT_PACKAGE_NAME,
                        getParentUri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
                    )
                }.onFailure {
                    AppLogger.BASIC.e(TAG, "revokeUriPermission err $it")
                }
            }
        }
    }

    fun fabMainActionSelected(isStylusTouch: Boolean, block: (Boolean) -> Unit) {
        /**
         * 待办切到笔记时，添加按钮还没有那么快消失，能快速的点击到，因此该处添加兼容处理
         */
        val isSpeechOrArticle = isSummaryNotebook(noteListViewModel.currentFolder.value)
        val isCollection = isCollectionNotebook(noteListViewModel.currentFolder.value)
        if (!isAdded || noteListViewModel.currentFolder.value == null || isSpeechOrArticle || isCollection) {

            block.invoke(false)
            return
        }
        if (activity == null) {
            block.invoke(false)
            return
        }

        AppLogger.BASIC.d(TAG, "fabMainActionSelected: $isStylusTouch")
        checkAIGCState("fabMainActionSelected", null, false) {
            createNewNote(isStylusTouch)
            block.invoke(true)
        }
    }

    private fun createPaintNote() {
        noteListViewModel.currentFolder.value?.apply {
            PaintActivity.startInternalQuickPaint(activity, guid)
        }
    }

    private fun createNewNote(isPencilTouch: Boolean) {
        // 全部笔记是虚拟归类，新建时如果当前笔记本为全部笔记，需要切换至默认笔记本
        var target = notebookAgent.getNotebookOrDefault(notebookAgent.getCurrentNotebook()?.guid)
        if (FolderFactory.isAllNotesFolder(target)) {
            target = notebookAgent.getNotebookOrDefault(null)
        }

        mNoteListHelper!!.createNewNote(activity, target, isPencilTouch)
    }

    private fun initBehavior() {
        val params = binding?.appBar?.layoutParams as? CoordinatorLayout.LayoutParams
        behavior = params?.behavior as? PrimaryTitleBehavior
        behavior?.needMargin = true
        behavior?.setShouldOnListScroll {
            hasBeenSetData
        }
        behavior?.setIsNoteFragment(true)
        binding?.noteList?.post {
            behavior?.initBehavior(
                binding?.parent ?: return@post,
                binding?.appBar ?: return@post,
                binding?.noteList ?: return@post
            )
        }
        behavior?.setLabelHeight(resources.getDimensionPixelOffset(R.dimen.toolbar_list_label_height))
        behavior?.listDragStatusListener = { isShow ->
            AppLogger.BASIC.d(TAG, "listDragStatusListener status isShow $isShow")
        }
    }

    private fun initRefreshView() {
        binding?.refresh?.apply {
            setRefreshEnable(false)
            setBounceHandler(BounceHandler(), binding?.noteList)
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(
                        downX: Float,
                        downY: Float,
                        moveX: Float,
                        moveY: Float
                ): Boolean {
                    return downY < moveY
                }
            })
            setHeaderView(DefaultHeader(context), binding?.headerRoot)
            mCallBack = setBounceCallBack(object : BounceCallBack {
                override fun startLoadingMore() {
                    //do nothing
                }

                override fun startRefresh() {
                    // save detail data first,use -1 for distinct with menu id.
                    noteViewModel.notifyDetailSaveData.value = -1
                }

                override fun refreshCompleted() {
                }

                override fun touchEventCallBack(ev: MotionEvent) {
                    val syncDisable = getFolderSyncState() == 0 || mSyncEnable == false
                    if (checkRefresh() && syncDisable) {
                        backToTop()
                        AppLogger.BASIC.d(TAG, "touchEventCallBack -> backToTop")
                        return
                    }
                    when (ev.actionMasked) {
                        MotionEvent.ACTION_DOWN, MotionEvent.ACTION_POINTER_DOWN -> {
                            downY = ev.y
                        }
                        MotionEvent.ACTION_MOVE -> {
                            moveY = ev.y
                            if (abs(moveY - downY) > 50F) {
                                if (sharedViewModel.isPullingDown.value != true) {
                                    AppLogger.BASIC.d(TAG, "set isPullingDown true")
                                    sharedViewModel.isPullingDown.value = true
                                }
                            }
                        }
                        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL, MotionEvent.ACTION_POINTER_UP -> {
                            sharedViewModel.isPullingDown.value = false
                            if (checkRefresh()) {
                                backToTop()
                            }
                        }
                    }
                }
            })
            val headerViewHeight = resources.getDimensionPixelSize(R.dimen.default_height)
            val headerTopPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_top_padding)
            val headerBottomPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_bottom_padding)
            val listPadding = resources.getDimensionPixelOffset(R.dimen.note_list_padding_top)
            val listMaxTop =
                    resources.getDimensionPixelOffset(R.dimen.pull_refresh_down_fragment_max_drag_distance) + headerBottomPadding
            /* In different layouts, the loading animation (LottieView) is hidden (hidden by other views) by setting
               the topMargin of the root layout, and is 24dp above the listView or recyclerView, This value is up to
               the business side according to the needs. */
            val headerTopMargin = headerViewHeight + headerTopPadding + listPadding
            (binding?.headerRoot?.layoutParams as? CoordinatorLayout.LayoutParams?)?.topMargin = -headerTopMargin - headerBottomPadding
            /* Drag threshold. When the drag distance is greater than or equal to this value, it means that it will enter
               the loading state after letting go. The reason for this calculation is to make the loading animation (LottieView)
               24dp from the top (the View that covered it before), and also 24dp from the listView or recyclerView below
               (including the topPadding of the listView), so the specific value is up to the business party according to the
               needs up to you. */
            mDragDistanceThreshold =
                    headerViewHeight + headerBottomPadding + headerTopPadding + headerBottomPadding
            /* The maximum distance that bounceLayout can be dragged down. */
            mMaxDragDistance = listMaxTop
        }
    }

    private fun initInfoNotifyControllerWrapper() {
        if (infoNotifyController != null) return
        infoNotifyController = InfoNotifyControllerWrapper.Builder()
            .setRecyclerView(binding?.noteList)
            .setFragment(this)
            .build()
        infoNotifyController?.getHeadTipsLayout()?.let {
            adapter.initHeaderViews(it)
        }
    }

    private fun initSyncGuideManagerWrapper() {
        if (guideManager != null) return
        val callback = if (!ConfigUtils.isUseCloudKit) {
            null
        } else {
            object : CloudKitSyncGuidManager.OnSyncFinishCallback {
                override fun onSyncing() {
                    nowShowSyncTip = true
                    setSubtitleViewVisibility()
                }

                override fun onSyncFinish(syncStatus: CloudKitSyncStatus) {
                    nowShowSyncTip = true
                    setSubtitleViewVisibility()
                    refreshCardAll(context)
                }

                override fun onSyncFinishSubtitleChange() {
                    nowShowSyncTip = false
                    setSubtitleViewVisibility()
                }
            }
        }
        if (callback != null) {
            guideManager = SyncGuideManagerWrapper(this, infoNotifyController, true, null, callback)
            updateTitle()
        }
        if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
            noteListViewModel.syncEnable.value = false
        }
        noteSyncProcess = guideManager?.firstQueryNoteSyncCloudStateCompact(activity, this,
            object : CloudSyncStateCallback {
                override fun refreshViewState(syncState: Int) {
                    AppLogger.BASIC.d(TAG, "refreshViewState canSyncToCloud = $syncState")
                    val canSyncToCloud = if (ConfigUtils.isUseCloudKit)
                        syncState > CloudKitSdkManager.CLOSE_CODE
                    else
                        syncState > NoteSyncProcess.NOTE_SETTING_CLOUD_SYNC_CLOSE
                    kotlin.runCatching {
                        lifecycleScope.launch(Dispatchers.Default) {
                            val context = activity ?: return@launch
                            if (isPackageDisabled(MbaUtils.PACKAGER_CLOUD, context)
                                || !DeviceInfoUtils.isAppInstalled(context, MbaUtils.PACKAGER_CLOUD)
                            ) {
                                noteListViewModel.syncEnable.postValueSafe(false)
                            } else {
                                noteListViewModel.syncEnable.postValueSafe(canSyncToCloud)
                            }
                        }
                    }.onFailure {
                        AppLogger.BASIC.e(TAG, "refreshViewState error ${it.message}")
                    }
                }

                override fun refreshModuleState(isSupport: Boolean) {}
        })
    }

    private fun initiateSearchView() {
        val ctx = context ?: return
        if (initiateSearchView) {
            return
        }

        if (binding?.toolbar == null) {
            return
        }

        val menu: Menu? = binding?.toolbar?.menu
        if (menu == null || menu.size() == 0) {
            return
        }

        searchItem = menu.findItem(R.id.note_searchView)
        if (searchItem == null) {
            return
        }

        correctSearchViewState()
        searchView = COUISearchViewAnimate(ctx)
        searchItem?.actionView = searchView
        initiateSearchView = true
        initToolBarSearchView(searchItem)
        searchView?.setAtBehindToolBar(binding?.toolbar, Gravity.TOP, searchItem)
        searchView?.addOnCancelButtonClickListener(COUISearchViewAnimate.OnCancelButtonClickListener {
            if (searchViewAnimatorHelper?.isAnimationRunning() == true) {
                return@OnCancelButtonClickListener true
            }

            binding?.noteList?.visibility = View.VISIBLE
            sharedViewModel.isSearch.value = false
            true
        })

        searchViewAnimatorHelper?.setOnClickListener(View.OnClickListener {
            if (searchViewAnimatorHelper?.isAnimationRunning() == true) {
                return@OnClickListener
            }
            sharedViewModel.isSearch.value = false
        })

        searchView?.searchView?.setOnQueryTextListener(prepareTextChangeListener(this))
    }

    private fun onQueryTextChange(newText: String): Boolean {
        if (!TextUtils.isEmpty(newText) && !isQueryTextCleared) {
            if (noteListViewModel.isSupportAiAsk == true) {
                searchViewAnimatorHelper?.backgroundMask?.visibility = View.VISIBLE
                searchViewAnimatorHelper?.backgroundMask?.alpha = 0f
                binding?.noteList?.visibility = View.INVISIBLE
                if (isAiAskQueryChange) {
                    isAiAskQueryChange = false
                } else {
                    searchAdapter.setCurrentAskQuery(newText)
                }
            } else {
                searchViewAnimatorHelper?.backgroundMask?.visibility = View.GONE
                binding?.noteList?.visibility = View.INVISIBLE
            }
            noteListViewModel.searchText.setValue(newText.lowercase())
            StatisticsNoteHome.setEventSearchContent(context, newText)
        } else {
            if (isRestoreFlag) {
                searchViewAnimatorHelper?.backgroundMask?.visibility = View.VISIBLE
                binding?.noteList?.visibility = View.VISIBLE
                searchViewAnimatorHelper?.backgroundMask?.alpha = 1f
                searchViewAnimatorHelper?.resultList?.visibility = View.GONE
                searchViewAnimatorHelper?.emptyContainer?.visibility = View.GONE
            }
        }
        return true
    }

    private fun initiateSearchViewAdapter() {
        val context = activity ?: return
        searchPlaceHolderView = View(context).apply {
            layoutParams = AbsListView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0)
            visibility = View.INVISIBLE
        }

        searchAdapter.addPlaceHolderView(searchPlaceHolderView!!)
        searchViewAnimatorHelper?.apply {
            adapter = searchAdapter
            layoutManager = LinearLayoutManager(context)
        }
        searchAdapter.initAiAsk(object : QueryChangedListener {
            override fun onQueryChanged(query: String) {
                isAiAskQueryChange = true
                searchView?.searchView?.searchAutoComplete?.run {
                    setText(query)
                    setSelection(query.length)
                }
            }
        })
        binding?.noteList?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it, object : DeDuplicateInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    val imeHeight = insets.getInsets(WindowInsetsCompat.Type.ime()).bottom
                    updateSearchListMargin(imeHeight)
                    imeVisible = insets.isVisible(WindowInsetsCompat.Type.ime())
                }
            })
        }
    }

    /**
     * 更新搜索列表的margin
     */
    private fun updateSearchListMargin(imeHeight: Int) {
        val params = searchViewAnimatorHelper?.resultList?.layoutParams as? ViewGroup.MarginLayoutParams
        if (params == null) {
            AppLogger.BASIC.d(TAG, "updateSearchListMargin layoutParams is null")
        } else {
            val bottom = imeHeight - COUINavigationBarUtil.getNavigationBarHeight(context)
            params.bottomMargin = max(bottom, 0)
            searchViewAnimatorHelper?.resultList?.layoutParams = params
        }
    }

    private fun initiateEmptyPage() {
        val stub: ViewStub? = binding?.emptyContentStub?.viewStub
        if (stub != null) {
            emptyContentViewLazyLoader = EmptyContentViewLazyLoader(stub, object : EmptyContentView.EmptyPageClickListener {
                override fun onSwitch() {
                    if (context != null && isPackageDisabled(MbaUtils.PACKAGER_CLOUD, requireContext())) {
                        MbaUtils.showMbaCloudDialog(requireContext())
                    } else {
                        NoteSyncProcess.startCloudSettingActivity(context)
                    }
                }
            })
        }
        mEmptyContentPageHelper?.let { emptyContentViewLazyLoader?.init(it) }
    }

    private fun initiateEmptyPageIfNeeded(hasNotes: Boolean) {
        val isPrivacyDenied = (context == null) || !PrivacyPolicyHelper.isAgreeUserNotice(context)
        if (!hasNotes || isPrivacyDenied) {
            emptyContentViewLazyLoader?.initialize()
        }
    }

    private val searchViewFocusChangeListener: View.OnFocusChangeListener =
        View.OnFocusChangeListener { v, hasFocus ->
            AppLogger.BASIC.d(
                TAG,
                "searchViewFocusChangeListener hasFocus:$hasFocus AigcState:${sharedViewModel.currentNoteAigcState}"
            )
            if (hasFocus) {
                AppLogger.BASIC.d(TAG, "searchViewFocusChangeListener OnFocusChangeListener")
                if (sharedViewModel.currentNoteAigcState == AIGCState.STATE_IDLE) {
                    activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
                    searchView?.postDelayed(showSearchSoftInputRunnable, DELAY_200)
                } else if (sharedViewModel.twoPane) {
                    if (isFromSearchClick) {
                        isFromSearchClick = false
                        AppLogger.BASIC.d(TAG, "isFromSearchClick return")
                        return@OnFocusChangeListener
                    }
                    activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
                    checkAIGCState("shouldCheckAIGCState change", null, false) {
                        Unit
                    }
                }
            }
        }

    private fun tryShowSoftwareInSearchMode() {
        searchView?.searchView?.searchAutoComplete?.onFocusChangeListener = searchViewFocusChangeListener
        searchView?.searchView?.searchAutoComplete?.setOnClickListener {
            if (sharedViewModel.twoPane) {
                if (sharedViewModel.currentNoteAigcState != AIGCState.STATE_IDLE) {
                    AppLogger.BASIC.d(TAG, "tryShowSoftwareInSearchMode not STATE_IDLE")
                    activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
                    checkAIGCState("shouldCheckAIGCState change", null, false) {
                    }
                } else {
                    AppLogger.BASIC.d(TAG, "tryShowSoftwareInSearchMode is STATE_IDLE")
                    activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
                }
            }
        }
        activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
    }

    private fun onSearchViewClick() {
        if (PrivacyPolicyHelper.isAgreeDmpSearch() || PrivacyPolicyHelper.isDmpSearchDialogShow(context)) {
            isSearchViewClick()
        } else {
            PrivacyPolicyHelper.showDMPEnhancementDialog(context) { isAgree ->
                StatisticsNoteHome.setEventDMPIsAgreeOrDis(context, isAgree.toString())
                PrivacyPolicyHelper.setDmpSearchAgreeStatus(context, isAgree)
                PrivacyPolicyHelper.setDmpSearchDialogShowStatus(context, true)
                NoteSearchManager.init(isAgree)
                TodoSearchManager.init(isAgree)
                isSearchViewClick()
            }
        }
    }

    private fun isSearchViewClick() {
        isFromSearchClick = true
        OplusInputMethodManagerProxy.setShowSoftInputEnabled(true)
        setSearchHint()
        searchViewAnimatorHelper?.setSearchView(searchView)
        searchViewAnimatorHelper?.setImageHelper(mSearchPageHelper)
        val viewStub = binding?.viewStubSearchResult?.viewStub
        searchViewAnimatorHelper?.initViewsAndAnimators(binding?.root, viewStub, notePlaceHolderView, behavior, layoutManager)
        tryShowSoftwareInSearchMode()
        sharedViewModel.isNeedUpdateSearchRes.observe(viewLifecycleOwner) { isNeedUpdateSearchRes ->
            if (isNeedUpdateSearchRes && twoPane && sharedViewModel.isSearch.value == true) {
                noteListViewModel.searchText.postValueSafe(noteListViewModel.searchText.value)
            }
        }
        NoteSearchManagerWrapper.notifyDataChange()
        TodoSearchManager.notifyDataChange()
        if (searchViewAnimatorHelper?.isAnimationRunning() == true) {
            return
        }

        if (behavior?.isAutotScrolling() == true || isSwitchGrideModing || changeSort) {
            AppLogger.BASIC.d(TAG, "--RecyclerView is auto scrolling, return--isSwitchGrideModing=$isSwitchGrideModing" +
                    "isAutotScrolling:${behavior?.isAutotScrolling()},changeSort:$changeSort")
            return
        }

        sharedViewModel.isSearch.value = true
        onlyMaskAnim = behavior?.hasPrimaryTitle() != true
        AppLogger.BASIC.d(TAG, "onlyMaskAnim=$onlyMaskAnim,hasPrimaryTitle=${behavior?.hasPrimaryTitle()}")
        binding?.notebookLabelContainer?.labelContainer?.visibility = View.GONE
        searchViewAnimatorHelper?.animateSearchIn(onlyMaskAnim)
        viewStubCreateByAi.createByAiLl?.visibility = View.GONE
        StatisticsUtils.setEventSearchNote(context)
        searchView?.postDelayed(showSearchSoftInputRunnable, DELAY_200)
    }

    private fun reOpenNoteIfNeed(force: Boolean = false) {
        twoPane = resources.getBoolean(R.bool.is_two_panel)
        if (!noteViewModel.hasSelectedRichNote() && force && twoPane) {
            // 切换到中大屏时如果无信息信息，执行默认选中第一条逻辑
            binding?.root?.postDelayed({ trySelectFirstRichNote() }, DELAY_TIME)
        } else {
            val selected = noteViewModel.getSelectedRichNote()?.localId ?: return
            AppLogger.BASIC.d(TAG, "reOpenNoteIfNeed twoPane = $twoPane , isSpeechRecording = ${sharedViewModel.isSpeechRecording}")
            if (twoPane && !sharedViewModel.isSpeechRecording) {
                noteViewModel.forceUpdate = force
                openNote(selected, force = force)
            } else {
                refreshSelectedUiState(true)
                refreshSelectedUiStateInSearchMode(true)
            }
        }
    }

    private fun hasWVNoteViewEditFragment(): Boolean {
        val result = kotlin.runCatching {
            val detail = activity?.supportFragmentManager?.findFragmentByTag(NoteDetailFragment.TAG)
            val target = detail?.childFragmentManager?.findFragmentByTag(WVNoteViewEditFragment.TAG)
            target != null
        }.getOrDefault(false)
        AppLogger.BASIC.d(TAG, "hasWVNoteViewEditFragment: $result")
        return result
    }

    private fun openNoteInSearchMode(
        guid: String,
        toThirdLogDetail: Boolean = false,
        attachmentId: String = "",
        force: Boolean = false
    ) {
        // 仅用于搜索页打开笔记
        val note = noteListViewModel.noteItemSearchList.value?.find {
            it.data?.richNote?.localId == guid
        }?.data
        if (note == null) {
            AppLogger.BASIC.d(TAG, "no matched result")
        } else {
            openNote(note, toThirdLogDetail, attachmentId, force)
        }
    }

    private fun openNote(
        guid: String,
        toThirdLogDetail: Boolean = false,
        attachmentId: String = "",
        force: Boolean = false,
        animationEffects: Bundle? = null
    ) {
        // 从缓存里找要打开的笔记，只能打开当前页的笔记
        var note = noteListViewModel.richNoteItemList.value?.find { it.data?.richNote?.localId == guid }?.data
        // 当前笔记本可能不包含，从搜索列表中查询看是否包含
        if (note == null) {
            note = noteListViewModel.searchNoteList?.value?.find { it.richNote.localId == guid }
        }
        if (note == null) {
            AppLogger.BASIC.d(TAG, "no matched result")
            return
        }
        val localIds = PrefUtils.getStringSet(context, PrefUtils.TODO_NOTE_ID_LIST_KEY)
        if (localIds != null && localIds.contains(guid)) {
            StatisticsUtils.setTodoListNoteView(context)
        }
        openNote(note, toThirdLogDetail, attachmentId, force, animationEffects = animationEffects)
    }

    private fun openNote(
        note: RichNoteWithAttachments,
        toThirdLogDetail: Boolean = false,
        attachmentId: String = "",
        force: Boolean = false,
        animationEffects: Bundle? = null
    ) {
        // 1. open note
        AppLogger.BASIC.d(TAG, "openNote:${note.richNote.localId} , twoPane:$twoPane, flag:$toThirdLogDetail")
        var searchList = ArrayList(noteListViewModel.searchList)
        if (sharedViewModel.isSearch.value == false) {
            searchList = arrayListOf()
        }
        val folder = notebookAgent.getNotebookOrDefault(note.richNote.folderGuid)
        val isDifferentLocalId = !noteViewModel.isSelectedRichNote(note.richNote.localId)
        val isDifferentSearchList = !Objects.equals(searchList, lastSearchList)

        if (twoPane && !toThirdLogDetail) {
            if (isDifferentLocalId || isDifferentSearchList) {
                AppLogger.BASIC.d(TAG, "localId: ${note.richNote.localId}")
                noteViewModel.searchAttachmentId = attachmentId
                updateSelectedRichNote(SelectedRichNoteInfo(note, folder, searchList), true)
            } else if (force) {
                updateSelectedRichNote(SelectedRichNoteInfo(note, folder, searchList), true)
            }
        } else {
            if (!toThirdLogDetail) {
                updateSelectedRichNote(SelectedRichNoteInfo(note, folder, searchList), false)
            }
            lifecycleScope.launch(Dispatchers.Default) {
                kotlin.runCatching {
                    startToActivity(note, folder.guid, folder.name, searchList, toThirdLogDetail, attachmentId, animationEffects = animationEffects)
                }.onFailure {
                    AppLogger.BASIC.e(TAG, "startToActivity in thread failed ${it.message}")
                    withContext(Main) {
                        startToActivity(
                            note, folder.guid, folder.name, searchList, toThirdLogDetail,
                            attachmentId, animationEffects = animationEffects
                        )
                    }
                }
            }
        }

        lastSearchList = searchList
    }

    private fun updateSelectedRichNote(info: SelectedRichNoteInfo, notify: Boolean) {
        // 选中笔记更新监听方仅resume后才处理笔记更新逻辑，所以延迟到resume后再调用updateSelectedRichNote方法
        val detail = activity?.supportFragmentManager?.findFragmentByTag(NoteDetailFragment.TAG)
        AppLogger.BASIC.d(
            TAG,
            "${activity == null},${activity?.supportFragmentManager == null},${detail == null},${detail?.lifecycleScope == null}"
        )
        detail?.lifecycleScope?.launch {
            AppLogger.BASIC.d(TAG, "launch")
            detail.viewLifecycleOwner.withResumed {
                AppLogger.BASIC.d(TAG, "withResumed")
                noteViewModel.updateSelectedRichNote(info, notify)

                // 2. open note: refresh selected item state if need
                refreshSelectedUiState()
                refreshSelectedUiStateInSearchMode()
            }
        }
    }

    private fun startToActivity(
        note: RichNoteWithAttachments,
        folderGuid: String,
        folderName: String,
        searchList: ArrayList<String>,
        toThirdLogDetail: Boolean = false,
        searchAttachmentId: String? = "",
        animationEffects: Bundle? = null
    ) {
        AppLogger.BASIC.d(TAG, "startToActivity searchList: $searchList, richNote.flag: $toThirdLogDetail")
        activity ?: return
        if (toThirdLogDetail) {
            startActivity(
                Intent(context, ThirdLogDetailActivity::class.java).apply {
                    val options = Bundle()
                    options.putBinder(ThirdLogDetailActivity.NOTE_INFO, NoteBinder(note))
                    putExtras(options)
                    putExtra(ThirdLogDetailActivity.SEARCH_ATTACHMENT_ID, searchAttachmentId)
                    val type = note.getAttachment(searchAttachmentId ?: "")?.type ?: -1
                    putExtra(ThirdLogDetailActivity.SEARCH_ATTACHMENT_TYPE, type)
                    val speechType = when (type) {
                        Attachment.TYPE_IDENTIFY_VOICE_LRC -> Attachment.TYPE_IDENTIFY_VOICE
                        Attachment.TYPE_SPEECH_LRC -> Attachment.TYPE_SPEECH_AUDIO
                        else -> type
                    }
                    putExtra(ThirdLogDetailActivity.SPEECH_ATTACHMENT_TYPE, speechType)
                    putExtra(ThirdLogDetailActivity.FOLDER_GUID, folderGuid)
                    putExtra(SaveImageAndShare.CONTENT_SPEECH_TYPE, note.speechLogInfo?.speechType ?: 0)
                    putExtra(ThirdLogDetailActivity.NOTE_GUID, note.richNote.localId)
                    putStringArrayListExtra(ThirdLogDetailFragment.EXTRA_SEARCH_TEXT, searchList)
                }
            )
        } else {
            startActivity(NoteViewRichEditActivity.createIntent(requireContext(), note, folderGuid, folderName).apply {
                putExtra(NoteViewRichEditActivity.EXTRA_SEARCH_TEXT, searchList)
                putExtra(NoteViewRichEditActivity.EXTRA_SEARCH_ATTACHMENT_ID, searchAttachmentId)
            }, animationEffects)
        }
    }

    private fun openEncryptedNote(guid: String) {
        if (guid.isNotEmpty()) {
            openNote(guid)
        }
    }

    private fun initiateObservers() {
        notebookAgent.observeNotebooks(viewLifecycleOwner) { folders ->
            AppLogger.BASIC.d(TAG, "initiateObservers: observeNotebooks")
            changeFoldersOfDeleteEncrypt(folders)
        }

        notebookAgent.observeCurrentNotebook(viewLifecycleOwner) { folder ->
            AppLogger.BASIC.d(TAG, "initiateObservers: observeCurrentNotebook")
            refreshSelectedItem(false)
            noteListViewModel.currentFolder.value = FolderInfo(folder)
            updateIsGroupByPeople(folder)
        }

        sharedViewModel.currentTabIndex.observe(viewLifecycleOwner) {
            AppLogger.BASIC.d(TAG, "initiateObservers: currentTabIndex $it")
            binding?.noteList?.scrollTouchEventFlag = StaggeredGridLayoutAnimationRecyclerView.SCROLL_TOUCHEVENT_DEFAULT
        }

        noteBookViewModel.folders.observe(viewLifecycleOwner) { folders ->
            searchAdapter.setFolderList(folders)
            AppLogger.BASIC.d(TAG, "folder  observe noteListViewModel.folders")
        }

        noteListViewModel.currentFolder.observe(viewLifecycleOwner) { newFolder: FolderInfo ->
            AppLogger.BASIC.d(TAG, "folder  observe noteListViewModel.currentFolder")
            if (isSummary || isSpecifiedFolder) {
                newFolder.guid = initFolderGuid
                newFolder.name = initFolderName
                isSummary = false
                isSpecifiedFolder = false
            }
            if (!isCurrentFolderFirstInit) {
                updateTitle()
                // correct toolbar menu
                correctToolbarMenu()
            }
            if (TextUtils.equals(newFolder.name, context?.resources?.getString(R.string.memo_all_notes))) {
                newFolder.guid = FolderInfo.FOLDER_GUID_ALL
                newFolder.extra.setSync(com.oplus.cloudkit.util.Constants.FOLDER_SYNC_ON)
            }
            binding?.noteList?.layoutManager?.scrollToPosition(0)
            labelAdapter.setCurrentFolder(newFolder.toFolder())
            labelAdapter.scrollToCheckedPosition()
            CloudKitSyncGuidManager.editFolderGuids = listOf(newFolder.guid)
            isCurrentFolderFirstInit = false
            val currentFolder: Folder? = adapter.getCurrentFolder()
            val isRecentDeleteFolder = FolderFactory.isRecentDeleteFolder(newFolder.guid)
            val isFolderGuidChanged = currentFolder == null || newFolder.guid != currentFolder.guid
            val isFolderNameChanged = currentFolder == null || newFolder.name != currentFolder.name
            val isFolderSyncChanged = if (FolderFactory.isAllNotesFolder(newFolder.guid)) {
                false
            } else {
                currentFolder == null || newFolder.extra?.getSyncState() != currentFolder.extra?.getSyncState()
            }
            val isSummaryNotebook = FolderFactory.isSummaryFolder(newFolder.guid)
            val isCollectionNotebook = FolderFactory.isCollectionFolder(newFolder.guid)
            adapter.isSummary = isSummaryNotebook
            if (adapter.isSummary) {
                viewStubCreateByAi.inflate(binding?.viewStubCreateByAi?.viewStub)
                adapter.setFixedFooterView(viewStubCreateByAi.createByAiLl)
            }
            adapter.setFooterCount(if (isSummaryNotebook) 1 else 0)
            if (!isSummaryNotebook) {
                viewStubCreateByAi.createByAiLl?.visibility = View.GONE
            }
            sharedViewModel.isSummaryFolder.value = isSummaryNotebook
            sharedViewModel.isCollectionFolder.value = isCollectionNotebook
            sharedViewModel.isPaintFolder.value = FolderFactory.isPaintFolder(newFolder.toFolder())
            noteListViewModel.labelTypeList = null
            if (isFolderGuidChanged || isFolderNameChanged || isFolderSyncChanged) {
                // 1.reload note list by given folder.
                AppLogger.BASIC.i(TAG, "folder has changed.")
                adapter.setCurrentFolder(newFolder.toFolder())
                if (!isChangingPaintFolder) {
                    resetCheckedInfo()
                }
                initiateSort()
            }

            // correct navigationview menu
            if (isRecentDeleteFolder) {
                adapter.setCurrentFolder(newFolder.toFolder())
                initToolNavigationMenu()
                mToolNavigationView!!.inflateMenu(R.menu.menu_note_all_delete)
                mToolNavigationViewSecondary!!.inflateMenu(R.menu.menu_note_delete_list_edit)
                // 显示时， 需区分最近删除界面 是否为可编辑状态
                AppLogger.BASIC.i(TAG, "NavigationAnimatorHelper currentFolder.observe: " + isEditMode())
                mNavigationAnimatorHelper?.showToolNavigationWithoutAnim(isEditMode())
                sharedViewModel.isRecentDeleteFolder.value = true
                val deleteAll = mToolNavigationView?.findViewById<View>(R.id.delete_all)
                deleteAll?.post {
                    checkShowDialog()
                }
            } else {
                sharedViewModel.isRecentDeleteFolder.value = false
                if (!isEditMode()) {
                    mNavigationAnimatorHelper?.hideToolNavigationWithoutAnim()
                }
            }
            setOS16BottomMenuBgSameToNav()

            if (noteListViewModel.isChangeFolderFromChoiceFolder) {
                layoutManager?.scrollToPositionWithOffset(0, 0)
                behavior?.expandPrimaryTitle()
                noteListViewModel.isChangeFolderFromChoiceFolder = false
            }

            noteBookViewModel.updateCurrentFolderAndObserver(viewLifecycleOwner, newFolder) {
                updateEncryptedState()
            }
        }


        noteListViewModel.sortRule.observe(viewLifecycleOwner) { sortRule: Int? ->
            noteViewModel.sortRuleChanged.value = sortRule
            AppLogger.BASIC.d(TAG, "sortRule.observe( sortRule : $sortRule )")
            updateToolbarMenuBySortRule(sortRule)
            lifecycleScope.launch(Dispatchers.IO) {
                WidgetUtils.sendNoteDataChangedBroadcast(MyApplication.appContext)
            }
            refreshCard(context, null, noteListViewModel.currentFolder.value?.guid, false)
        }
        noteListViewModel.isGroupByPeople.observe(viewLifecycleOwner) {
            adapter.setIsGroupByPeople(it)
            searchAdapter.setIsGroupByPeople(it)
            updateToolbarMenuGroupByPeople(it, noteListViewModel.currentFolder.value?.guid)
        }
        noteListViewModel.richNoteItemList.observe(viewLifecycleOwner) { noteItems ->
            if (noteItems != null) {
                view?.removeCallbacks(updateNoteListCallback)
                // 这儿注意有可能数据回调先回来，动画回调后回来，所以回到onResume后就先等待动画回调，但也给了一个兜底防止动画一直没回调
                if ((clickPressView != null) && isOnResume) {
                    AppLogger.BASIC.d(TAG, "richNoteItemListObserve, pending refresh data")
                    pendingRichNoteItemList = noteItems
                    // 兜底逻辑：防止动画流程异常时页面数据没刷新
                    view?.postDelayed(
                        updateNoteListCallback,
                        NOTE_ANIMATION_DELAY
                    ) // 具体延时多少秒要根据动画时长来
                } else {
                    pendingRichNoteItemList = null
                    updateNoteList(noteItems, "liveDataObserve")
                }
            }
        }

        noteListViewModel.selectedNotes.observe(viewLifecycleOwner) { data: Pair<Set<String>?, Boolean?> ->
            if (data.first != null && data.second != null) { // 1.correct title info
                mSelectItemSize = data.first!!.size
                if (!mIsAnimating && !noteListViewModel.isDeletingOrRecovering) {
                    correctTitleInfo(mSelectItemSize, isEditMode())
                }

                // 2.correct navigationview menu state
                correctNavigationViewMenuState(data.first!!.size, data.second!!)
            }
        }

        noteListViewModel.isAllNoteSelected.observe(viewLifecycleOwner) { isAllNoteSelected: Boolean? ->
            correctToolbarSelect()
            correctDialogShow()
        }

        noteMarginViewModel.mNotePlaceHolderViewHeight.observe(viewLifecycleOwner) { height: Int ->
            adapter.updatePlaceHolderViewHeight(height)
            searchViewAnimatorHelper?.setNotePlaceHolderViewHeight(height)
            mPlaceHolderViewHeight = height
        }

        sharedViewModel.noteSelectionMode.observe(viewLifecycleOwner) { isSelectionMode ->
            if (isSelectionMode) {
                isSelectionModeFirstInit = false
            }

            if (isSelectionModeFirstInit) {
                isSelectionModeFirstInit = false
                return@observe
            }

            labelAdapter.canClick(!isSelectionMode)
            initToolNavigationMenu()

            updateNavigationViewMenuWithAnim(isSelectionMode)
            updateBehavior(isSelectionMode)
            toolbarAnimation(isSelectionMode)
            infoNotifyController?.setSyncGuideViewState(!isSelectionMode, mSyncEnable == true)

            if (isSelectionMode) {
                binding?.toolbar?.isTitleCenterStyle = true
                adapter.enterSelectionMode()
                refreshCheckBox(MenuMultiSelectHelper.MenuMode.ENTER)
                val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
                if (data?.first != null) {
                    notifySelectionChange()
                }
                binding?.noteList?.setFadingEdgeLength(resources.getDimensionPixelOffset(R.dimen.color_navigation_list_fading_edge_length))
            } else {
                adapter.exitSelectionMode()
                refreshCheckBox(MenuMultiSelectHelper.MenuMode.LEAVE)
                binding?.noteList?.setFadingEdgeLength(0)
                correctSearchViewState()
            }
        }

        if (sharedViewModel.turnToAllNoteFolder.value == true) {
            sharedViewModel.turnToAllNoteFolder.value = false
        }

        sharedViewModel.turnToAllNoteFolder.observe(viewLifecycleOwner) { value ->
            if (value == true) {
                isCurrentFolderFirstInit = false
                if (twoPane && (sharedViewModel.isRecentDeleteFolder.value == true)) {
                    resetCheckedInfo()
                    adapter.notifyDataSetChanged()
                } else {
                    switchToAllNoteFolder()
                }
            }
        }

        noteViewModel.notifyDetailSaveData.value = 0
        sharedViewModel.viewPagerScrollStateIdle.observe(viewLifecycleOwner) { isIdle ->
            if (isIdle) {
                val hasNotes = hasNotesInCurrentFolder()
                val isSearchMode = sharedViewModel.isSearch.value ?: false
                behavior?.setScaleEnable(hasNotes && !isSearchMode)
                if (sharedViewModel.currentTabIndex.value == 0 && behavior?.getScaleEnable() == true && behavior?.hasPrimaryTitle() == false) {
                    behavior?.updateToolbar()
                }
            } else {
                behavior?.setScaleEnable(false)
            }
        }

        sharedViewModel.noteMode.observe(viewLifecycleOwner) { isGrid ->
            AppLogger.BASIC.d(TAG, "initiateObservers noteMode = isGridMode : $isGridMode ; isGrid = $isGrid")
            binding?.noteList?.postDelayed({
                if (isGrid) {
                    adapter.removeOther()
                } else {
                    adapter.addOther()
                }
                adapter.switchGrildListToppedDataHandle(isGrid)
            }, DELAY_200)
            if (isGridMode != isGrid) {
                isSwitchGrideModing = true
                noteModeSwitcher?.beginSwitchModeDelayedLayoutAnimation(isGrid) {
                    updateRecyclerViewPadding()
                    if (isGrid) {
                        behavior?.updateToolbar()
                    }
                    if (needRefreshList) {
                        AppLogger.BASIC.d(TAG, "refresh list when animate ended.")
                        // 触发列表刷新
                        noteListViewModel.dataRefresh.let {
                            it.value = it.value?.not() ?: false
                        }
                    }
                    isSwitchGrideModing = false
                    needRefreshList = false
                }
                isGridMode = isGrid
            }
        }
        weakObserver = WeakObserver(this)
        weakObserver?.let {
            ChangeToPaintFolderModel.noteChangeToPaintFolder.observe(viewLifecycleOwner, it)
        }
    }
    @Suppress("LongMethod", "ComplexCondition", "ComplexMethod")
    private fun updateNoteList(noteItems: List<RichNoteItem>, from: String) {
        AppLogger.BASIC.d(TAG, "updateNoteList: size=${noteItems.size}, from=$from")
        RichNoteSaveTransitionHelper.clearUpdatingData(noteItems)
        // 正在切换宫格模式执行动画，不更新List，防止卡顿
        if (isSwitchGrideModing) {
            needRefreshList = true
            AppLogger.BASIC.e(TAG, "switching grid mode, return")
            return
        }

        var realChangeToPaintFolder = false
        var changeToPaintFolderNoteId = ""
        /**子集笔记变成手写笔记时，observer 会触发多次，需要确定是正常的第一次进行操作*/
        if (noteListViewModel.changeToFolderModel?.isObserveNoteRealChange(noteItems.size, notebookAgent.getCurrentNotebook()) == true) {
            realChangeToPaintFolder = true
            changeToPaintFolderNoteId = noteListViewModel.changeToFolderModel?.noteLocalId ?: ""
            noteListViewModel.changeToFolderModel = null
        }

        initInfoNotifyControllerWrapper()
        /**修复bug 363281 ,在退出账号保留数据时，下次登录会重复触发mNoteItemList的observe，因为数据库自动同步有数据更新，此时
        如果当前文件夹0条数据，会重复resetEmptyView，导致空页面动画重复执行，所以为了避免重复observe，在这里判断如果上次和这次的笔记数量都是0，直接返回**/
        if (noteItems.isEmpty() && noteListCountPre == 0) {
            lifecycleScope.launch {
                adapter.refreshInfoBoardAndFolderHeaderAndQuestionnaire(false)
            }
            changeSort = false
            if (isCurrentRecentDeleteNotebook() && !isEditMode()) {
                mToolNavigationView?.visibility = View.GONE
                mToolNavigationViewSecondary?.visibility = View.GONE
                setOS16BottomMenuBgSameToNav()
            }
            correctSearchViewState()
            refreshCard(context, null, noteListViewModel.currentFolder.value?.guid, false)
            resetMainEmptyPage()
            return
        }

        if (noteListViewModel.isChangeFolderFromChoiceFolder) {
            layoutManager?.scrollToPositionWithOffset(0, 0)
            behavior?.expandPrimaryTitle()
            noteListViewModel.isChangeFolderFromChoiceFolder = false
        }

        noteListCountPre = noteItems.size
        val toAdapterList = noteItems.toMutableList()
        AppLogger.BASIC.d(TAG, "richNoteItemList changed noteItems size=${toAdapterList.size}, changeSort=$changeSort ," +
                "folderInitOrChange=${noteListViewModel.changeNoteListByInitOrChangeFolder}")
        loadDataFinished = true

        if (adapter.inSelectionMode()) {
            adapter.recalculateSelectionInfo(toAdapterList)
            if (toAdapterList.size == 0) {
                exitEditMode()
            }
        }
        val noNeedRefreshList = noteListViewModel.getSpeechInsertingList().apply {
            addAll(AIGCCollectManager.getInstance().getAllExecuteNoteId())
        }
        AppLogger.BASIC.d(TAG, "richNoteItemList observe noNeedRefreshList=$noNeedRefreshList")

        val countNotes = toAdapterList.count { it.viewType == RichNoteItem.TYPE_NOTE }
        val isSearch = sharedViewModel.isSearch.value == true
        if (toAdapterList.isNotEmpty()) {
            val isCheckedItemExist = refreshDetailFragmentIfNeed(toAdapterList, noNeedRefreshList)
            AppLogger.BASIC.d(TAG, "isCheckedItemExist=$isCheckedItemExist")
                //选中的笔记不在了(如删除,移动文件夹,加密等),更新子级页面为空页面
            if (twoPane && !isSearch && noteViewModel.hasSelectedRichNote() && !isCheckedItemExist && !isChangingPaintFolder) {
                resetCheckedInfo()
            }
        } else if (!isChangingPaintFolder && !isSearch) {
            resetCheckedInfo()
        }
        noteViewModel.noteCount.value = countNotes

        if (localId != null) {
            val position = getPositionByGuid(localId.toString())
            binding?.noteList?.postDelayed({
                //处理标题栏收缩
                if (position - adapter.getHeaderCount() > 2) {
                    binding?.noteList?.post {
                        behavior?.initBehavior(
                            binding?.parent ?: return@post,
                            binding?.appBar ?: return@post,
                            binding?.noteList ?: return@post
                        )
                        behavior?.onListScroll()
                    }
                    //RecyclerView滑动
                    binding?.noteList?.let {
                        val scrollPosition = (position - 1).coerceAtLeast(0)
                        adapter.highLightAnimationHelper.needScrollItem(it, scrollPosition)
                    }
                }
                //高亮闪烁两次
                binding?.noteList?.postDelayed({
                    binding?.noteList?.findViewHolderForAdapterPosition(position)?.itemView?.apply {
                        if (!twoPane) {
                            val highLightMask = this.findViewById<View>(R.id.highlight_mask)
                            highLightMask?.background = ContextCompat.getDrawable(
                                context,
                                adapter.getItemHighLightRes(position)
                            )
                            highLightAnimationHelper.requestHighLight(highLightMask, false)
                        }
                    }
                }, DELAY_TIME)
                if (twoPane) {
                    openNote(localId.toString())
                }
                localId = null
            }, DELAY_TIME)
        }

        if (FolderFactory.isRecentDeleteFolder(notebookAgent.getCurrentNotebook()) && !isEditMode()) {
            if (countNotes == 0) {
                mToolNavigationView?.visibility = View.GONE
                mToolNavigationViewSecondary?.visibility = View.GONE
            } else if (sharedViewModel.isSearch.value != true) {
                mToolNavigationView?.visibility = View.VISIBLE
            }
            setOS16BottomMenuBgSameToNav()
        }
        if (changeSort) {
            /**
             * 在执行动画期间，不再重复执行动画，不然列表会不停的闪
             */
            if (isSortAnimateRunning) {
                tempNotes = toAdapterList
                return
            }
            isSortAnimateRunning = true
            noteModeSwitcher?.beginSwitchSortDelayedLayoutAnimation(noteListViewModel.sortRule.value
                ?: SORT_RULE_BY_CREATE_TIME) {
                val list = tempNotes ?: toAdapterList
                if (!isGridMode) {
                    val haveOther = list.any { it.viewType == RichNoteItem.TYPE_NOTE_OTHER }
                    if (!haveOther) {
                        val count = list.filter { (it.data?.richNote?.topTime ?: 0) > 0 }.size
                        AppLogger.BASIC.d(TAG, "changeSort isGridMode count:$count")
                        list.add(count, RichNoteItem(RichNoteItem.TYPE_NOTE_OTHER))
                    }
                }
                handleDataToppedStatus(list, true)
                val hasNotes = adapter.getNoteItemCount() != 0
                correctSearchViewState()
                initiateEmptyPageIfNeeded(hasNotes)
                resetMainEmptyPageAndSyncTips(hasNotes)
                changeSort = false
                isSortAnimateRunning = false
                tempNotes = null
            }
            return
        }
        if (!isGridMode) {
            val count =
                toAdapterList.filter { (it.data?.richNote?.topTime ?: 0) > 0 }.size
            AppLogger.BASIC.d(TAG, "isGridMode count:$count")
            toAdapterList.add(count, RichNoteItem(RichNoteItem.TYPE_NOTE_OTHER))
        }
        val lastRealTotalNotes = adapter.mRealNoteItems.toMutableList()
        adapter.setRealTotalNotes(toAdapterList)
        if (NoteFeatureUtil.getIsFirstInApp()) {
            val isHigher = calIsHigherVersion()
            NoteFeatureUtil.setCurrentFolderHasHighOnlyFirst(isHigher)
        } else {
            NoteFeatureUtil.setCurrentFolderHasHighOnlyFirst(false)
        }
        if (isInMultiWindowMode() && UIConfigMonitor.isFoldingModeOpen(activity)) { //折叠屏展开时分屏
            setNoteItems(toAdapterList, true)
        } else {
            if (toAdapterList.size <= DELETE_ANIMATION_TRANSITION
                && adapter.getNoteItemCount() <= DELETE_ANIMATION_TRANSITION
            ) {
                if (noteListViewModel.changeNoteListByInitOrChangeFolder) {
                    /**
                     * bugfix-8431427
                     * noteItemAnimator在add动画结束时将isChangeFolder设置为false，而如果切换文件夹太快，早于动
                     * 画结束，就会出现这里将isChangerFolder设置为true之后，add动画结束时又被设置为了false，导致切
                     * 换文件夹的动画不符合预期。在设置isChangeFolder之前，结束当前的所有动画。
                     */
                    val noteItemAnimator = binding?.noteList?.itemAnimator as? NoteItemAnimator
                    noteItemAnimator?.endAnimations()
                    noteItemAnimator?.setChangeFolder(true)
                    noteListViewModel.changeNoteListByInitOrChangeFolder = false
                }
                if (isGridMode) {
                    val noteDiffCallBack = RichNoteDiffCallBack(adapter.getNoteItems(), toAdapterList, noNeedRefreshList)
                    noteDiffCallBack.setGridMode(true)
                    noteDiffCallBack.setSortRule(adapter.getSortRule())
                    val diffResult = DiffUtil.calculateDiff(noteDiffCallBack, true)
                    setNoteItems(toAdapterList, false)
                    diffResult.dispatchUpdatesTo(NoteListUpdateCallback((adapter as RecyclerView.Adapter<*>), adapter.getHeaderCount()))
                } else {
                    var datas = handleDataToppedStatus(toAdapterList, false)
                    if (noteListViewModel.isGroupByPeople.value == true) {
                        datas = toAdapterList
                    }
                    val noteDiffCallBack = RichNoteDiffCallBack(adapter.getNoteItems(), datas, noNeedRefreshList)
                    noteDiffCallBack.setGridMode(false)
                    noteDiffCallBack.setSortRule(adapter.getSortRule())
                    val diffResult = DiffUtil.calculateDiff(noteDiffCallBack, true)
                    diffResult.dispatchUpdatesTo(NoteListUpdateCallback((adapter as RecyclerView.Adapter<*>), adapter.getHeaderCount()))
                    val oldData = adapter.getAdapterNoteItems()
                    setNoteItems(datas, false)
                    val newData = adapter.getAdapterNoteItems()
                    val topStateChanged = checkTopStateChange(oldData, newData)
                    AppLogger.BASIC.d(TAG, "topStateChanged:$topStateChanged isSyncing:$isSyncing")
                    if (topStateChanged) {
                        (binding?.noteList?.itemAnimator as NoteItemAnimator).setToppedHeader(true)
                        adapter.notifyItemChanged(adapter.getTopHeaderPosition())
                        adapter.notifyItemChanged(adapter.getOtherPosition())
                    }
                }
            } else {
                setNoteItems(toAdapterList, true)
            }
        }

        val hasNotes = adapter.getNoteItemCount() != 0
        correctSearchViewState()
        initiateEmptyPageIfNeeded(hasNotes)
        //首次加载笔记数据的时候，不进行云同步tip相关刷新，因为此时云同步状态未知
        if (mIsFirstLoadNoteList) {
            mIsFirstLoadNoteList = false
            resetMainEmptyPage()
        } else {
            resetMainEmptyPageAndSyncTips(hasNotes)
        }
        lifecycleScope.launch {
            adapter.refreshInfoBoardAndFolderHeaderAndQuestionnaire(hasNotes)
        }

        //刷新选择项
        if (sharedViewModel.noteSelectionMode.value == true) {
            notifySelectionChange()
        }

        correctToolbarMenu()
        updateTitle()

        if (isSearch) {
            noteListViewModel.searchText.value = noteListViewModel.searchText.value
        }
        refreshCard(context, null, noteListViewModel.currentFolder.value?.guid, false)

        AppLogger.BASIC.d(
            TAG,
            "defaultSelectedFromChoiceFolder:${noteListViewModel.defaultSelectedFromChoiceFolder} " +
                    "realChangeToPaintFolder:$realChangeToPaintFolder"
        )
        if (realChangeToPaintFolder) {
            isChangingPaintFolder = false
        }
        if (twoPane && !isChangingPaintFolder &&
            noteListViewModel.defaultSelectedFromChoiceFolder) {
            val indexOfPaintNote = if (realChangeToPaintFolder && TextUtils.isEmpty(changeToPaintFolderNoteId)) -1 else {
                noteItems.indexOfFirst { it.data?.richNote?.localId == changeToPaintFolderNoteId }
            }
            AppLogger.BASIC.d(TAG, "indexOfPaintNote=$indexOfPaintNote")
            if (indexOfPaintNote >= 0) {
                openNote(changeToPaintFolderNoteId, force = true)
                val offset = appContext.resources.getDimensionPixelOffset(R.dimen.dp_30)
                layoutManager?.scrollToPositionWithOffset(indexOfPaintNote, offset)
            } else {
                trySelectFirstRichNote()
            }
            noteListViewModel.defaultSelectedFromChoiceFolder = false
        }
    }

    /**
     * 根据给定笔记本更新分组信息
     */
    private fun updateIsGroupByPeople(folder: Folder) {
        if (FolderFactory.isCallSummaryFolder(folder)) {
            noteListViewModel.isGroupByPeople.value = SortRule.isGroupByPeople()
        } else {
            noteListViewModel.isGroupByPeople.value = false
        }
    }

    private class WeakObserver(fragment: NoteListFragment) : Observer<ChangeToPaintFolderModel> {
        private val weakRef = WeakReference(fragment)
        override fun onChanged(value: ChangeToPaintFolderModel) {
            weakRef.get()?.apply {
                //全部笔记时，不切换到手写笔记本
                if (FolderFactory.isAllNotesFolder(getCurrentFolderInfo()?.guid)) {
                    AppLogger.BASIC.d(TAG, "noteChangeToPaintFolder failed. is all notes folder.")
                    return
                }
                if (activity?.taskId != value.taskId) {
                    // activity taskId 不一样不切换到手写笔记本，避免单应用分屏一端列表一端详情页添加涂鸦后保存，列表这边切换成了手写笔记本
                    AppLogger.BASIC.d(TAG, "noteChangeToPaintFolder failed. is different task.")
                    return
                }
                value.noteLocalId ?: return
                val selectedGuid = noteViewModel.getSelectedRichNote()?.localId ?: ""
                if (twoPane && !TextUtils.isEmpty(selectedGuid)) {
                    noteListViewModel.changeToFolderModel = value
                    isChangingPaintFolder = true
                }
                AppLogger.BASIC.d(TAG, "noteChangeToPaintFolder observer twoPane=$twoPane observer")
                switchFolder(value.folder)
            }
        }
    }

    /**
     *  父级列表数据有变化,需要更新子级页面
     *
     *  @return true 当前选中笔记在列表中
      */
    private fun refreshDetailFragmentIfNeed(toAdapterList: List<RichNoteItem>, noNeedRefreshList: List<String>): Boolean {
        var isCheckedItemExist = false
        toAdapterList.forEachIndexed { index, noteItem ->
            if (noteItem.viewType == RichNoteItem.TYPE_NOTE) {
                val guid = noteItem.data?.richNote?.localId ?: return@forEachIndexed
                guidHashMap?.put(guid, index + adapter.getHeaderCount())
                if (twoPane && noteViewModel.isSelectedRichNote(guid)) {
                    isCheckedItemExist = true
                    //摘要生成过程中，不刷新子级页面
                    if (!noNeedRefreshList.contains(guid)) {
                        val folder = notebookAgent.getNotebookOrDefault(noteItem.data.richNote.folderGuid)
                        noteViewModel.noteDataChanged.value = Pair(noteItem.data, folder)
                    }
                    val toppedTime = noteItem.data.richNote.topTime
                    noteListViewModel.selectionManager.updateTopped(guid, toppedTime)
                }
            }
        }
        return isCheckedItemExist
    }

    /**
     * 置顶状态 处理adapter数据
     */
    private fun handleDataToppedStatus(toAdapterList: MutableList<RichNoteItem>, shouldNotifyDataChange: Boolean): MutableList<RichNoteItem> {
        val datas = mutableListOf<RichNoteItem>()
        val toppedVisible = ToppedUtil.getToppedSharedPreferences(context, noteListViewModel.currentFolder.value?.guid ?: "")
        if (toppedVisible && !isGridMode) {
            val notoppedDats = toAdapterList.filter { it.data?.richNote?.topTime ?: 0 <= 0 }
            datas.clear()
            datas.addAll(notoppedDats)
        } else {
            datas.clear()
            datas.addAll(toAdapterList)
        }
        if (shouldNotifyDataChange) {
            //全量刷新时走这里，其余时刻走非全量
            setNoteItems(datas, true)
        }
        return datas
    }

    private fun setNoteItems(noteItems: MutableList<RichNoteItem>, shouldNotifyDataChange: Boolean) {
        hasBeenSetData = true
        adapter.setNoteItems(noteItems, shouldNotifyDataChange)
        correctSearchViewState()
        view?.post { initSyncGuideManagerWrapper() }
    }

    private fun correctDialogShow() {
        if (mDeleteDialog?.isShowing == true) {
            val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
            if (data?.first != null) {
                mNoteListHelper?.deleteNoteItems(data.first, isAllNoteSelected(), false)
            }
        }
    }

    private fun initSearchListObserver() {
        sharedViewModel.isSearch.observe(viewLifecycleOwner) { isSearch ->
            AppLogger.BASIC.d(TAG, "sharedViewModel.isSearch.observe isSearch = $isSearch")
            if (isSearch) {
                isRestoreFlag = true
                isQueryTextCleared = false
                var isFirst = true
                noteListViewModel.observeAllList(viewLifecycleOwner) {
                    // 第一次获取到全部笔记列表时，无需执行该回调，用于监听后续数据的变化
                    if (isFirst) {
                        isFirst = false
                        return@observeAllList
                    }
                    if (sharedViewModel.isSearch.value == true) {
                        noteListViewModel.search(true)
                        updateSearchNote(it)
                    }
                }
            } else {
                if (searchViewAnimatorHelper?.inSearchMode() == true) {
                    hasPlayAnimation = false
                    isRestoreFlag = false
                    isQueryTextCleared = true
                    searchViewAnimatorHelper?.animateSearchOut(onlyMaskAnim, twoPane)
                    correctSearchViewState()
                    resetMainEmptyPage()
                    lastSearchList = arrayListOf()
                    noteViewModel.mLastSearchList = arrayListOf()
                    noteViewModel.searchAttachmentId = ""
                }
                //笔记列表页，退出搜索模式清空搜索内容
                searchAdapter.setQueryStringList(arrayListOf())
                noteListViewModel.onExitSearchMode()
                noteListViewModel.removeObserveAllList()
                noteListViewModel.searchText.postValueSafe("")
                adapter.updateFooterView()
                binding?.notebookLabelContainer?.labelContainer?.visibility = View.VISIBLE
                binding?.noteList?.visibility = View.VISIBLE
            }

            if (sharedViewModel.isRecentDeleteFolder.value == true) {
                if (noteListViewModel.richNoteItemList.value?.isEmpty() == true) { //最近删除为空
                    mToolNavigationView?.visibility = View.GONE
                    mToolNavigationViewSecondary?.visibility = View.GONE
                } else { //最近删除不为空
                    mToolNavigationView?.visibility = if (isSearch) View.GONE else View.VISIBLE
                }
                setOS16BottomMenuBgSameToNav()
            }
        }
        noteListViewModel.searchText.observe(viewLifecycleOwner) { key ->
            if (!TextUtils.isEmpty(key) && (sharedViewModel.isSearch.value == true)) {
                noteListViewModel.search(key)
            } else {
                //清空搜索结果列表
                noteListViewModel.noteItemSearchList.postValueSafe(arrayListOf())
            }
        }
        NoteListViewModel.isShowPermissionsTips.observe(viewLifecycleOwner) { isShowPermissionsTips ->
            val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid
            if (isShowPermissionsTips && !isRecentDelete) {
                adapter.hideHeaderViewNoteTips()
            }
        }

        noteListViewModel.noteItemSearchList.observe(viewLifecycleOwner) { searchItems ->
            AppLogger.BASIC.d(TAG, "noteListViewModel.noteItemSearchList.observe noteItemSearchList.size = ${searchItems.size}")
            if (searchView?.searchView?.query.isNullOrEmpty()) {
                // 当搜索结果返回时，若当前已经没有搜索词则不应展示搜索结果
                AppLogger.BASIC.d(TAG, "searchText is null or empty")
                return@observe
            }
            if (sharedViewModel.isSearch.value != true) {
                if (noteListViewModel.isSupportAiAsk == true) {
                    searchViewAnimatorHelper?.emptyContainer?.visibility = View.GONE
                    searchViewAnimatorHelper?.resultList?.visibility = View.VISIBLE
                } else {
                    searchViewAnimatorHelper?.emptyContainer?.visibility = View.GONE
                    searchViewAnimatorHelper?.resultList?.visibility = View.GONE
                }
            } else if (searchItems.isEmpty()) {
                if (noteListViewModel.isSupportAiAsk == true) {
                    searchViewAnimatorHelper?.emptyContainer?.visibility = View.GONE
                    searchViewAnimatorHelper?.resultList?.visibility = View.VISIBLE
                } else {
                    if (searchViewAnimatorHelper?.initFinished == false) {
                        searchViewAnimatorHelper?.emptyContainer?.visibility = View.INVISIBLE
                    } else {
                        searchViewAnimatorHelper?.emptyContainer?.visibility = View.VISIBLE
                    }
                    searchViewAnimatorHelper?.resultList?.visibility = View.GONE
                    if (!hasPlayAnimation) {
                        searchViewAnimatorHelper?.noSearchResultLottie?.playAnimation()
                        hasPlayAnimation = true
                    }
                }
            } else {
                hasPlayAnimation = false
                searchViewAnimatorHelper?.emptyContainer?.visibility = View.GONE
                searchViewAnimatorHelper?.resultList?.visibility = View.VISIBLE
            }
            searchAdapter.setQueryStringList(noteListViewModel.searchList)
            searchAdapter.setSearchNoteItems(searchItems, noteListViewModel.isSupportAiAsk)
        }
    }

    private fun updateSearchNote(list: MutableList<RichNoteWithAttachments>) {
        val selectNoteId = noteViewModel.getSelectedRichNote()?.localId ?: return
        val searchNoteId = noteViewModel.searchPair?.first
        val isRecover = noteViewModel.searchPair?.second
        if ((selectNoteId == searchNoteId || selectNoteId == searchLongClickNoteId) && twoPane) {
            searchLongClickNoteId = null
            noteViewModel.searchPair = null
            val note = list.find { tempNote -> tempNote.richNote.localId == selectNoteId }
            if (note == null) {
                adapter.setCheckedGuid("")
                searchAdapter.setCheckedGuid("")
                noteViewModel.resetSelectedRichNote()
            } else {
                if (searchMenuHelper.isClickEncrypt() || note.richNote.recycleTime > 0 || isRecover == true) {
                    searchMenuHelper.setIsClickEncrypt(false)
                    searchAdapter.setCheckedGuid("")
                    noteViewModel.resetSelectedRichNote()
                } else {
                    noteViewModel.forceUpdate = true
                    openNote(note, force = true)
                }
            }
        }
    }

    private fun initRefreshAndPermissionObserver() {
        noteListViewModel.completeRefreshWithTipsAndDelay.observe(viewLifecycleOwner) { tipsAndDelay: Pair<String?, Int?> ->
            binding?.noteList?.postDelayed({
                binding?.refresh?.setRefreshCompleted()
            }, (tipsAndDelay.second ?: 0).toLong())
            AccessibilityUtils.broadcastAccessibilityContent(binding?.noteList, tipsAndDelay.first)
        }

        sharedViewModel.storagePermissionDenied.observe(viewLifecycleOwner) { denied: Boolean ->
            binding?.refresh?.visibility = View.VISIBLE
            val hasNotes = adapter.getNoteItemCount() > 0
            val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == noteListViewModel.currentFolder.value?.guid
            if (!ConfigUtils.isUseCloudKit) {
                binding?.subTitle?.visibility = if (hasNotes) View.VISIBLE else View.INVISIBLE
            } else {
                guideManager?.updateSubTitle("", if (hasNotes) View.VISIBLE else View.INVISIBLE)
            }
            AppLogger.BASIC.d(
                TAG, "NavigationAnimatorHelper storagePermissionDenied.observe: " + (isRecentDelete && hasNotes) + " , isEditMode() = " + isEditMode()
            )
            if (isRecentDelete && hasNotes) {
                mNavigationAnimatorHelper?.showToolNavigationWithoutAnim(isEditMode())
            }
            correctSearchViewState()
            resetMainEmptyPage()
        }

        noteListViewModel.syncEnable.observe(viewLifecycleOwner) { syncEnable: Boolean ->
            mSyncEnable = syncEnable
            if (null != guideManager) {
                guideManager?.queryNoteSyncCloudStateCompact(context,
                    object : CloudSyncStateCallback {
                        override fun refreshViewState(syncState: Int) {
                            AppLogger.NOTE.d(TAG, "notelist queryNoteSyncCloudState state = $syncState")
                            if (guideManager?.getSyncSwitchState() != syncState || syncEnable != mSyncEnable) {
                                guideManager?.updateSyncSwitchState(syncState)
                            }
                            adapter.setSyncEnable(syncEnable)
                            val hasNotes = adapter.getNoteItemCount() != 0
                            resetMainEmptyPageAndSyncTips(hasNotes)
                        }

                        override fun refreshModuleState(isSupport: Boolean) {}
                    })
            }
        }
    }

    private fun updateTitle() {
        val noteCount = getAllNoteCounts()
        AppLogger.BASIC.d(TAG, "updateTitle noteCount=$noteCount")
        context ?: return
        val subtitle = resources.getQuantityString(com.oplus.note.baseres.R.plurals.n_note, noteCount, noteCount)
        val visibility: Int
        if (isEditMode()) {
            visibility = View.INVISIBLE
            correctTitleInfo(mSelectItemSize, true)
        } else {
            binding?.mainTitle?.setText(R.string.memo_app_name)
            binding?.toolbar?.title = ""

            visibility = if (noteCount > 0) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
        }
        if (!ConfigUtils.isUseCloudKit) {
            binding?.subTitle?.text = subtitle
            if (behavior?.mHasSubTitleView == true) {
                binding?.subTitle?.visibility = visibility
                binding?.subTitleView?.root?.visibility = View.GONE
            }
        } else {
            if (behavior?.mHasSubTitleView == true) {
                binding?.subTitle?.visibility = View.GONE
            }
            guideManager?.updateSubTitle(subtitle, visibility)
        }
        updateEncryptedState()
        if (behavior?.mHasSubTitleView == true) {
            setSubtitleViewVisibility()
        }
        if (isDeleteOperation) {
            isDeleteOperation = false
            binding?.mainTitle?.postDelayed({
                behavior?.updateToolbar()
            }, DELAY_TIME)
        }
    }

    private fun refreshIsAllSyncSwitchClosedState(folders: List<Folder>) {
        // 所有笔记本的同步开关均关闭
        CloudKitSyncGuidManager.isAllSyncSwitchClosed = folders.filter {
            !FolderFactory.isAllNotesFolder(it) && !FolderFactory.isRecentDeleteFolder(it)
        }.none {
            it.extra?.getSyncState() == com.oplus.cloudkit.util.Constants.FOLDER_SYNC_ON
        }
    }

    private fun updateEncryptedState() {
        val isEncryptedFolder = noteBookViewModel.currentFolderEncrypted
        activity.setRecentScreenshotEnabled(!isEncryptedFolder)
        updateEncryptedMenu()
    }

    private fun updateEncryptedMenu() {
        val encryptMenu = binding?.toolbar?.menu?.findItem(R.id.encrypt)
        encryptMenu?.let {
            it.title = if (noteBookViewModel.currentFolderEncrypted) {
                getString(com.oplus.note.baseres.R.string.set_unencrypted_to_folder)
            } else {
                getString(com.oplus.note.baseres.R.string.set_encrypted_to_folder)
            }
        }
        encryptMenu?.isVisible = !isDisallowEncryptFolder() && !isEditMode()
    }

    private fun isDisallowEncryptFolder(): Boolean {
        val guid = getCurrentFolderInfo()?.guid
        return FolderInfo.FOLDER_GUID_NO_GUID == guid || FolderInfo.FOLDER_GUID_ENCRYPTED == guid
                 || FolderInfo.FOLDER_GUID_ALL == guid
    }

    /**
     * 更新副标题 高度，及 mPlaceHolderView 高度
     */
    private fun setSubtitleViewVisibility() {
        AppLogger.BASIC.d(TAG, "setSubtitleViewVisibility")
        val subTitleView = if (!ConfigUtils.isUseCloudKit) {
            binding?.subTitle as TextView
        } else {
            binding?.subTitleView?.root
        }
        val hasNote = getAllNoteCounts() > 0
        mSubTitleViewHelper.updateSubtitleViewHeight(
            subTitleView = subTitleView,
            adapter = adapter,
            behavior = behavior,
            refreshEnable = noteListViewModel.refreshEnable,
            hasList = hasNote,
            mPlaceHolderViewHeight = mPlaceHolderViewHeight,
            isCloudSyncing = nowShowSyncTip
        )
    }

    private fun correctToolbarMenu(isSelected: Boolean = false) {
        val menu: Menu? = binding?.toolbar?.menu
        if ((menu == null) || (menu.size() == 0)) {
            return
        }
        val cancel = menu.findItem(R.id.cancel)
        val select = menu.findItem(R.id.select_all)
        val search = menu.findItem(R.id.note_searchView)
        val edit = menu.findItem(R.id.edit_note)
        val mode = menu.findItem(R.id.mode_note)
        val toggleFinishedTodo = menu.findItem(R.id.toggle_finished_todo)
        val setting = menu.findItem(R.id.jump_setting)
        val encrypt = menu.findItem(R.id.encrypt)
        val sortCreateTime = menu.findItem(R.id.sort_create_time)
        val sortUpdateTime = menu.findItem(R.id.sort_update_time)
        val groupSortBy = menu.findItem(R.id.group_sort_by)
        if (cancel == null || select == null || search == null || edit == null
            || mode == null || toggleFinishedTodo == null || setting == null
            || encrypt == null || sortCreateTime == null || sortUpdateTime == null || groupSortBy == null) {
            return
        }

        val isSelectionMode = isEditMode()
        val hasNotes = hasNotesInCurrentFolder()
        val isAllNoteSelected = isAllNoteSelected()
        val isSearchMode = sharedViewModel.isSearch.value ?: false
        val isShowGroup = FolderFactory.isCallSummaryFolder(noteListViewModel.currentFolder.value?.guid)
        if (isSelected) {
            cancel.isEnabled = true
            select.isVisible = true
            binding?.toolbar?.menuView?.findViewById<View>(R.id.cancel)?.let {
                val maxPadding = max(it.paddingStart, it.paddingEnd)
                it.updatePadding(left = maxPadding, right = maxPadding)
            }
            binding?.toolbar?.menuView?.findViewById<View>(R.id.select_all)?.let {
                val maxPadding = max(it.paddingStart, it.paddingEnd)
                it.updatePadding(left = maxPadding, right = maxPadding)
            }
        } else {
            cancel.isEnabled = isSelectionMode
            select.isVisible = isSelectionMode
            edit.isVisible = !isSelectionMode && hasNotes
            mode.isVisible = !isSelectionMode
            toggleFinishedTodo.isVisible = false
            setting.isVisible = !isSelectionMode
            search.isVisible = !isSelectionMode
            sortCreateTime.isVisible = !isSelectionMode && !isShowGroup
            sortUpdateTime.isVisible = !isSelectionMode && !isShowGroup
            groupSortBy.isVisible = !isSelectionMode && isShowGroup
            if (!isEditMode()) {
                binding?.toolbar?.isTitleCenterStyle = false
            }
        }
        if (!PrimaryTitleBehavior.SHOW_MENU_TEXT_BTN) {
            if (cancel.isEnabled) {
                cancel.setIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                cancel.setIcon(null)
            }
        }
        val cancelView = binding?.toolbar?.menuView?.findViewById<View>(R.id.cancel)
        if (cancel.isEnabled) {
            cancel.setTitle(R.string.cancel)
            cancelView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_YES
        } else {
            cancel.setTitle("")
            cancelView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
        }
        if (!PrimaryTitleBehavior.SHOW_MENU_TEXT_BTN) {
            select.setIcon(if (isAllNoteSelected) {
                com.support.appcompat.R.drawable.coui_btn_check_on_normal
            } else {
                com.support.appcompat.R.drawable.coui_btn_check_off_normal
            })
        }
        select.setTitle(if (isAllNoteSelected) R.string.deselect_all else R.string.select_all)
        behavior?.setScaleEnable(hasNotes && !isSearchMode)
        updateUpdateToolbarWhenTextChange()
    }

    private fun initToolBarSearchView(search: MenuItem?) {
        AppLogger.BASIC.d(TAG, "initToolBarSearchView ")
        val actionView = search?.actionView
        if (actionView is COUISearchViewAnimate) {
            actionView.apply {
                setPaddingRelative(
                    context.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.coui_search_view_padding_start_in_toolbar),
                    0,
                    context.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.coui_search_view_padding_end_in_toolbar),
                    0
                )
                setSearchAnimateType(COUISearchViewAnimate.TYPE_INSTANT_SEARCH)
                setSearchBackgroundColor(resources.getColorStateList(com.support.appcompat.R.color.coui_color_container8, null))
                functionalButton.setOnClickListener {
                    checkAIGCState("functionalButton click", null, false) {
                        sharedViewModel.isSearch.value = false
                        Unit
                    }
                }
                setQueryHint(getString(R.string.memo_search_hint))
            }
            AppLogger.BASIC.d(TAG, "initToolBarSearchView end")
        }
    }

    private fun setSearchHint() {
        val folder = noteListViewModel.currentFolder.value
        if (folder == null) {
            AppLogger.BASIC.d(TAG, "setSearchHint folder is null")
            return
        }
        val isEncrypted = folder.encrypted == Folder.FOLDER_ENCRYPTED
        AppLogger.BASIC.d(TAG, "setSearchHint isEncrypted:$isEncrypted,${folder.name}")
        val hint = if (isEncrypted) {
            getString(com.oplus.note.baseres.R.string.search_private_note)
        } else {
            getString(R.string.memo_search_hint)
        }
        searchView?.setQueryHint(hint)
    }

    private fun initNoteBookLabel() {
        labelAdapter.entrance =  binding?.notebookLabelContainer?.entrance
        binding?.notebookLabelContainer?.listLabel?.run {
            val linearLayoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            layoutManager = linearLayoutManager
            labelAdapter.layoutManager = linearLayoutManager
            itemAnimator = null
            adapter = labelAdapter
            labelAdapter.entrance?.setOnClickListener {
                checkAIGCState {
                    tryShowNotebookList()
                }
            }
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    AppLogger.BASIC.d(TAG, "label onScrollStateChanged $newState")
                }
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)
                    checkIfFirstItemIsCovered()
                    checkIfLastItemIsCovered()
                }
            })
            /**
             * bugfix-8761699：
             * 在某些情况下layoutManager.scrollToPosition不会触发onScrolled回调导致left_fade遮罩没有按
             * 预期出现。下面这个监听器在listLabel改变时再做一次检查，以确保left_fade和right_fade按预期出现。
             */
            addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                checkIfFirstItemIsCovered()
                checkIfLastItemIsCovered()
            }
        }
        activity?.supportFragmentManager?.apply {
            removeOnBackStackChangedListener(notebookFragmentBackStackListener)
            addOnBackStackChangedListener(notebookFragmentBackStackListener)
        }
    }

    private fun checkIfFirstItemIsCovered() {
        if (isInLeftFadingAnimation) return
        leftFadeOfFloat()
    }

    private fun checkIfLastItemIsCovered() {
        if (isInRightFadingAnimation || !isSpecialDeviceType()) return
        rightFadeOfFloat()
    }

    private fun tryShowNotebookList() {
        if (sharedViewModel.isSearch.value != true) {
            VibrateUtils.execVibrate(appContext)
            if (isEditMode()) {
                exitEditMode()
            }

            val fm = activity?.supportFragmentManager
            fm?.apply {
                sharedViewModel.isNotebookListShow.value = true
                notebookAgent.isCloudEnable = isCloudSyncEnableFun
                notebookAgent.showNotebookList(R.id.notebook_container, this, MainActivity.STACK_NAME)
            }
        }
    }

    private val notebookFragmentBackStackListener = object : OnBackStackChangedListener {
        override fun onBackStackChanged() {
        }

        override fun onBackStackChangeStarted(fragment: Fragment, pop: Boolean) {
            super.onBackStackChangeStarted(fragment, pop)
            // pop为false表示笔记本列表界面显示
            val isShowNotebookList = !pop

            AppLogger.BASIC.d(TAG, "onBackStackChangeStarted:$fragment, $isShowNotebookList")
            if (!isShowNotebookList) {
                sharedViewModel.isNotebookListShow.value = false
                /*从笔记本页面返回笔记页面触发云同步功能*/
                CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
            }
        }
    }

    private fun checkTopStateChange(olds: List<RichNoteItem>, news: List<RichNoteItem>): Boolean {
        val oldPair = findTopOtherPosition(olds)
        val newPair = findTopOtherPosition(news)
        val oldTopPosition = oldPair.first
        val oldOtherPosition = oldPair.second
        val newTopPosition = newPair.first
        val newOtherPosition = newPair.second
        // 如果新旧数据top、other位置相同时，则不需要刷新
        if ((oldTopPosition == newTopPosition) && (oldOtherPosition == newOtherPosition)) {
            return false
        }
        if ((news.firstOrNull()?.tag == RichNoteItem.TAG.GROUP_BY_PEOPLE && olds.firstOrNull()?.tag == RichNoteItem.TAG.GROUP_BY_PEOPLE)) {
            //新旧数据都在联系人分组模式，不需要刷新
            return false
        } else {
            return true
        }
    }

    private fun findTopOtherPosition(list: List<RichNoteItem>): Pair<Int, Int> {
        var topPosition = -1
        var otherPosition = -1
        for (i in list.indices) {
            val item = list[i]
            if (item.viewType == RichNoteItem.TYPE_NOTE_TOPPED) {
                topPosition = i
            } else if (item.viewType == RichNoteItem.TYPE_NOTE_OTHER) {
                otherPosition = i
                return Pair(topPosition, otherPosition)
            }
        }
        return Pair(topPosition, otherPosition)
    }

    fun isEditMode(): Boolean {
        return sharedViewModel.noteSelectionMode.value ?: false
    }

    fun updateSyncStatus(isSyncing: Boolean) {
        this.isSyncing = isSyncing
    }

    private fun isAllNoteSelected(): Boolean {
        return noteListViewModel.isAllNoteSelected.value ?: false
    }

    private fun initToolNavigationMenu() {
        if (mToolNavigationView == null || mToolNavigationViewSecondary == null) {
            mToolNavigationView = editMenuStub.value?.inflate() as? COUINavigationView
            mToolNavigationViewSecondary = editMenuStubSecondary.value?.inflate() as? COUINavigationView
            if (ConfigUtils.isToDoDeprecated) {
                mOS16ToolNavigationBg = binding?.noteEditModeMenuBottomBgOs16
            }
            mToolNavigationView?.apply {
                setOnItemSelectedListener(onItemSelectedListener)
            }
            mToolNavigationViewSecondary?.apply {
                setOnItemSelectedListener(onItemSelectedListener)
                visibility = View.GONE
            }

            mNavigationAnimatorHelper = NavigationAnimatorHelper(requireContext())
            mNavigationAnimatorHelper?.initToolNavigationAnimator(mToolNavigationView!!)
            mNavigationAnimatorHelper?.initToolNavigationAnimatorSecondary(mToolNavigationViewSecondary!!)
        }
        updateMoveMenuState()
    }

    private fun updateMoveMenuState() {
        val isEncrypted = noteBookViewModel.currentFolderEncrypted
        mToolNavigationView?.let {
            it.menu.findItem(R.id.note_move_folder)?.isVisible = !isEncrypted
        }
        mToolNavigationViewSecondary?.let {
            it.menu.findItem(R.id.note_move_folder)?.isVisible = !isEncrypted
        }
    }

    private val onItemSelectedListener = NavigationBarView.OnItemSelectedListener { item ->
        AppLogger.BASIC.d(TAG, "onNavigationItemSelected twoPane=$twoPane,itemId=${item.itemId}")
        return@OnItemSelectedListener if (twoPane && noteViewModel.hasSelectedRichNote()) {
            //父子级结构,子级先保存数据,等子级数据保存完了再执行点击操作
            noteViewModel.notifyDetailSaveData.value = item.itemId
            true
        } else {
            handleNavigationItemSelected(item.itemId)
        }
    }

    private fun registerChooseNotebookListener() {
        notebookAgent.registerChooseNotebookListener(TAG, chooseNotebookListener)
    }

    private fun unregisterChooseNotebookListener() {
        notebookAgent.unregisterChooseNotebookListener(TAG, chooseNotebookListener)
    }

    private val chooseNotebookListener: (ChosenFolderInfo) -> Unit = { info ->
        AppLogger.BASIC.d(TAG, "chooseNotebookListener: ${info.newFolder}")
        kotlin.runCatching {
            val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotesData
            if (data?.first != null) {
                val selectedNotes: Set<String> = HashSet(data.first)
                val preFolderInfo = FolderInfo(info.preFolder)
                mNoteListHelper?.setChangeFolderCallback(
                    activity,
                    preFolderInfo,
                    selectedNotes,
                    FolderInfo(info.newFolder),
                    isAllNoteSelected()
                )
                opsStatistic("01010103", selectedNotes)
            }
        }
    }

    fun moveNotes(data: Pair<Set<String>?, Boolean?>?, currentFolderGuid: String?, isEncrypt: Boolean) {
        if (data?.first == null) {
            return
        }
        noteListViewModel.selectedNotesData = data
        val preFolder = notebookAgent.getNotebooks().find { currentFolderGuid == it.guid }
        notebookAgent.chooseNotebook(childFragmentManager, preFolder, isEncrypt, false, TAG)
        StatisticsUtils.setEventNoteMove()
        /*移动数据时触发*/
        CloudSyncTrigger.sendDataChangedBroadcast(appContext)
    }

    private fun moveNotes(isEncrypt: Boolean) {
        moveNotes(noteListViewModel.selectedNotes.value, noteListViewModel.currentFolder.value?.guid ?: "", isEncrypt)
    }

    fun noteEncrypt(data: Pair<Set<String>?, Boolean?>?, currentFolder: FolderInfo?, isAll: Boolean) {
        if (data?.first == null || currentFolder == null) {
            return
        }

        val isCurrentFolderEncrypted = currentFolder.encrypted == Folder.FOLDER_ENCRYPTED
        if (notebookAgent.shouldShowChooseEncryptedFolderPanel() && !isCurrentFolderEncrypted) {
            moveNotes(data, currentFolder.guid, true)
            return
        }

        val selectedNotes: Set<String> = HashSet(data.first)
        mNoteListHelper?.encryptOrDecryptAsDefault(this, currentFolder, selectedNotes, isAll)
        CloudSyncTrigger.sendDataChangedBroadcast(appContext)
    }

    private fun noteEncrypt() {
        noteEncrypt(noteListViewModel.selectedNotes.value, noteListViewModel.currentFolder.value, isAllNoteSelected())
    }

    fun noteTopped(data: Pair<Set<String>?, Boolean?>?, folder: FolderInfo?) {
        if (data?.first == null || folder == null) {
            return
        }
        val isAllTopped = data.second != null && data.second!!
        val selectedNotes: Set<String> = HashSet(data.first)
        opsStatistic("01010102", selectedNotes)

        mNoteListHelper?.toppedNoteItems(selectedNotes, isAllTopped, folder)
        if (isAllNoteSelected()) {
            if (isAllTopped) {
                StatisticsUtils.setEventTopped(context, StatisticsUtils.TYPE_NOTE_ALL_UN_TOP)
            } else {
                StatisticsUtils.setEventTopped(context, StatisticsUtils.TYPE_NOTE_ALL_TOP)
            }
        }
        StatisticsUtils.setEventNoteToTop()
    }

    private fun noteTopped() {
        noteTopped(noteListViewModel.selectedNotes.value, noteListViewModel.currentFolder.value)
    }

    fun noteRecover(noteId: String?, isAllNoteSelected: Boolean) {
        if (noteId.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "noteRecover noteId is empty")
            return
        }
        val set = mutableSetOf(noteId)
        noteListViewModel.selectedNotesData = Pair(set, null)
        deleteNoteRecover(set, isAllNoteSelected)
    }

    private fun deleteNoteRecover() {
        val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
        noteListViewModel.selectedNotesData = data
        deleteNoteRecover(data?.first, isAllNoteSelected())
    }

    private fun deleteNoteRecover(selectedNotes: Set<String>?, isAllNoteSelected: Boolean) {
        selectedNotes?.let {
            mNoteListHelper?.recoverNoteItems(it, isAllNoteSelected)
        }
        StatisticsUtils.setEventRecoverNote(context, StatisticsUtils.TYPE_RECOVER_LIST)
    }

    fun noteDeleteCompletely(noteId: String?, isAllNoteSelected: Boolean) {
        if (noteId.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "noteDeleteCompletely noteId is empty")
            return
        }
        val set = mutableSetOf(noteId)
        noteListViewModel.selectedNotesData = Pair(set, null)
        deleteNoteRemoveCompletely(set, isAllNoteSelected)
    }

    private fun deleteNoteRemoveCompletely() {
        val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotes.value
        noteListViewModel.selectedNotesData = data
        deleteNoteRemoveCompletely(data?.first, isAllNoteSelected())
    }

    private fun deleteNoteRemoveCompletely(selectedNotes: Set<String>?, isAllNoteSelected: Boolean) {
        selectedNotes?.let {
            mNoteListHelper?.deleteMarkNoteItems(
                it, isAllNoteSelected,
                isNotAllowSyncEncryptNoteToCloud() or isDeleteEncryptNoteWhenNotAllowSyncToCloud(), true
            )
        }
    }

    fun noteDelete(folder: FolderInfo?, data: Pair<Set<String>?, Boolean?>?, isAll: Boolean, isNotSync: Boolean) {
        if (data?.first == null || folder == null) {
            return
        }
        noteListViewModel.selectedFolder = folder
        noteListViewModel.selectedNotesData = data
        mNoteListHelper?.deleteNoteItems(data.first, isAll, isNotSync)
        StatisticsUtils.setEventNoteEditDelete(isAllNoteSelected())
        StatisticsUtils.setEventNoteLongClickDelete()
        /*触发删除云同步*/
        CloudSyncTrigger.sendDataChangedBroadcast(appContext)
    }

    private fun noteDelete() {
        noteDelete(
            noteListViewModel.currentFolder.value,
            noteListViewModel.selectedNotes.value,
            isAllNoteSelected(),
            isNotAllowSyncEncryptNoteToCloud()
        )
    }

    private fun deleteAll() {
        val noteSets: MutableSet<String> = HashSet()
        val richNoteItemList: List<RichNoteItem> = noteListViewModel.richNoteItemList.value
            ?: return
        for (richNoteItem in richNoteItemList) {
            if (richNoteItem.isRecycleNote()) {
                richNoteItem.data?.richNote?.localId?.let { noteSets.add(it) }
            }
        }
        if (noteSets.isEmpty()) {
            return
        }
        val data = Pair<Set<String>, Boolean?>(noteSets, null)
        noteListViewModel.selectedNotes.value = data
        noteListViewModel.selectedNotesData = data
        mNoteListHelper?.deleteMarkNoteItems(data.first, true,
            isNotAllowSyncEncryptNoteToCloud() or isDeleteEncryptNoteWhenNotAllowSyncToCloud(), false)
        /*全部删除时触发同步*/
        CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.appContext)
    }

    private fun handleNavigationItemSelected(itemId: Int): Boolean {
        if (!MultiClickFilter.isEffectiveShortClick()) {
            return true
        }
        when (itemId) {
            R.id.note_move_folder -> moveNotes(false)
            R.id.note_encrypted -> noteEncrypt()
            R.id.note_topped -> noteTopped()
            R.id.note_delete -> noteDelete()
            R.id.delete_note_recover -> deleteNoteRecover()
            R.id.delete_note_remove_completely -> deleteNoteRemoveCompletely()
            R.id.delete_all -> deleteAll()
        }
        return true
    }

    private fun opsStatistic(opsId: String, localIds: Collection<String>?) {
        kotlin.runCatching {
            // TODO 需要限制打印次数？
            localIds?.forEach { localId ->
                val findRichNote = noteListViewModel.richNoteItemList.value?.find { item ->
                    item.data != null && item.data.richNote.localId == localId
                }?.data?.richNote
                if (findRichNote != null) {
                    DataStatisticsHelper.noteUserOps(TAG, opsId, findRichNote)
                }
            }
        }
    }

    private fun updateNavigationViewMenuWithAnim(isSelectionMode: Boolean) {
        val isRecentDelete = FolderFactory.isRecentDeleteFolder(notebookAgent.getCurrentNotebook())
        if (isRecentDelete) {
            if (isSelectionMode) {
                mNavigationAnimatorHelper?.showToolNavigationSecondary(twoPane, onStart = {
                    setOS16BottomMenuBg()
                }, {
                    checkShowDialog()
                })
            } else {
                if (noteListViewModel.richNoteItemList.value.isNullOrEmpty()) {
                    mToolNavigationViewSecondary?.visibility = View.GONE
                    mToolNavigationView?.visibility = View.GONE
                    setOS16BottomMenuBg()
                } else {
                    mToolNavigationView?.inflateMenu(R.menu.menu_note_all_delete)
                    mNavigationAnimatorHelper?.dismissToolNavigationSecondary(withAnim = sharedViewModel.noteSelectionModeChangeWithAnim) {
                        setOS16BottomMenuBg()
                    }
                }
            }
        } else {
            if (isSelectionMode) {
                mToolNavigationView?.inflateMenu(R.menu.menu_note_list_edit)
                if (twoPane) {
                    mNavigationAnimatorHelper?.showToolNavigationTwo(onStart = {
                        setOS16BottomMenuBg()
                    }, {
                        checkShowDialog()
                    })
                } else {
                    mNavigationAnimatorHelper?.showToolNavigation(onStart = {
                        setOS16BottomMenuBg()
                    })
                    checkShowDialog()
                }
            } else {
                if (twoPane) {
                    mNavigationAnimatorHelper?.dismissToolNavigationTwo(withAnim = sharedViewModel.noteSelectionModeChangeWithAnim) {
                        setOS16BottomMenuBg()
                    }
                } else {
                    mNavigationAnimatorHelper?.dismissToolNavigation(withAnim = sharedViewModel.noteSelectionModeChangeWithAnim) {
                        setOS16BottomMenuBg()
                    }
                }
            }
        }
    }

    /**
     * 处理OS16以上底部菜单背景
     */
    private fun setOS16BottomMenuBg() {
        if (ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.d(TAG, "setOS16BottomMenuBg")
            if (mToolNavigationView?.visibility == View.VISIBLE || mToolNavigationViewSecondary?.visibility == View.VISIBLE) {
                val layoutParamsMenuBottomOs16 = mOS16ToolNavigationBg?.layoutParams as? ViewGroup.MarginLayoutParams
                layoutParamsMenuBottomOs16?.height = windowInsetsBottomValue
                mOS16ToolNavigationBg?.layoutParams = layoutParamsMenuBottomOs16
                mOS16ToolNavigationBg?.visibility = View.VISIBLE
            } else {
                mOS16ToolNavigationBg?.visibility = View.GONE
            }
        }
    }

    /**
     * OS16以上底部菜单背景与mToolNavigationView显隐逻辑保持一致
     */
    private fun setOS16BottomMenuBgSameToNav() {
        AppLogger.BASIC.d(TAG, "setOS16BottomMenuBgSameToNav")
        if (ConfigUtils.isToDoDeprecated) {
            if (mToolNavigationView?.isVisible == true) {
                // 显示前刷新view高度
                setOS16BottomMenuBg()
            }
            mOS16ToolNavigationBg?.visibility = mToolNavigationView?.visibility!!
        }
    }

    /**
     * Alpha animation of toolbar when switch between normal mode and edit mode
     */
    @SuppressLint("ObjectAnimatorBinding")
    private fun toolbarAnimation(isSelected: Boolean) {
        val toolbarOutAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 1f, 0f)
        val toolbarInAnimation = ObjectAnimator.ofFloat(binding?.toolbar, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet()
        toolbarAnimatorSet.duration = ALPHA_DURATION
        toolbarAnimatorSet.interpolator = LinearInterpolator()
        toolbarAnimatorSet.play(toolbarOutAnimation).before(toolbarInAnimation)
        toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                mIsAnimating = false
                if (!isAdded) {
                    return
                }

                val isSelectionMode = isEditMode()
                if (isSelectionMode) {
                    correctTitleInfo(mSelectItemSize, true)
                }
                updateTitle()
                correctToolbarMenu(isSelected)
                behavior?.apply {
                    val hasNotes = adapter.getNoteItemCount() > 0
                    setScaleEnable(hasNotes)
                    val isRunning = binding?.noteList?.itemAnimator?.isRunning
                    AppLogger.BASIC.d(TAG, "toolbarAnimationEnd, noteList itemAnimator isRunning=$isRunning")

                    if (isRunning == true) {
                        updateTitleMargin()
                    } else {
                        updateToolbar()
                    }
                }
            }
        })
        mIsAnimating = true
        behavior?.setScaleEnable(false)
        if (isSelected) {
            val menu: Menu? = binding?.toolbar?.menu
            if ((menu == null) || (menu.size() == 0)) {
                return
            }
            val hasNotes = hasNotesInCurrentFolder()
            val isSummaryFolder = isSummaryNotebook(noteListViewModel.currentFolder.value)
            menu.findItem(R.id.edit_note)?.isVisible = !isSelected && hasNotes
            menu.findItem(R.id.mode_note)?.isVisible = !isSelected
            menu.findItem(R.id.sort_create_time)?.isVisible = !isSelected
            menu.findItem(R.id.sort_update_time)?.isVisible = !isSelected
            menu.findItem(R.id.group_sort_by).isVisible = !isSelected && isSummaryFolder
            menu.findItem(R.id.encrypt).isVisible = !isSelected
            menu.findItem(R.id.jump_setting)?.isVisible = !isSelected
            menu.findItem(R.id.note_searchView)?.isVisible = !isSelected
        }
        toolbarAnimatorSet.start()
    }

    private fun correctTitleInfo(selectedSize: Int, isSelectionMode: Boolean) {
        if (isSelectionMode) {
            if (selectedSize == 0) {
                binding?.mainTitle?.setText(com.oplus.note.baseres.R.string.memo_select_note)
            } else {
                val isAllNoteSelected = (selectedSize == adapter.mRealNoteItems.size)
                binding?.mainTitle?.text = if (isAllNoteSelected) {
                    getString(com.oplus.note.baseres.R.string.memo_note_select_all)
                } else {
                    getString(com.oplus.note.baseres.R.string.memo_note_select_num, selectedSize.toString())
                }
            }
        }
        updateUpdateToolbarWhenTextChange()
    }

    private fun correctNavigationViewMenuState(selectedCount: Int, topped: Boolean) {
        initToolNavigationMenu()

        mToolNavigationView?.let { correctNavigationStateByMenu(selectedCount, topped, it) }
        mToolNavigationViewSecondary?.let { correctNavigationStateByMenu(selectedCount, topped, it) }

    }

    private fun correctNavigationStateByMenu(selectedCount: Int, topped: Boolean, mToolNavigationView: COUINavigationView) {
        var i = 0
        val size = mToolNavigationView.menu.size()
        while (i < size) {
            val item = mToolNavigationView.menu.getItem(i)
            when (item.itemId) {
                R.id.note_move_folder, R.id.note_delete, R.id.delete_note_recover, R.id.delete_note_remove_completely -> item.isEnabled = selectedCount > 0
                R.id.note_topped -> {
                    val flag = selectedCount > 0
                    item.isEnabled = flag
                    if (flag) {
                        correctToppedMenuState(item, topped)
                    } else {
                        correctToppedMenuState(item, false)
                    }
                }
                R.id.note_encrypted -> {
                    item.isEnabled = selectedCount > 0
                    correctEncryptMenuState(item)
                }
            }
            ++i
        }
    }

    private fun correctToppedMenuState(item: MenuItem, topState: Boolean) {
        item.setTitle(if (topState) R.string.option_note_cancel_toped else R.string.option_note_top)
        item.setIcon(if (topState) R.drawable.color_menu_ic_un_topped else R.drawable.color_menu_ic_topped)
    }

    private fun correctEncryptMenuState(item: MenuItem) {
        if (noteBookViewModel.currentFolderEncrypted) {
            item.setTitle(com.oplus.note.baseres.R.string.set_unencrypted_to_folder)
            item.contentDescription = resources.getString(com.oplus.note.baseres.R.string.set_unencrypted_to_folder)
            item.setIcon(com.oplus.note.baseres.R.drawable.note_ic_decrypt)
        } else {
            item.setTitle(com.oplus.note.baseres.R.string.set_encrypted_to_folder)
            item.contentDescription = resources.getString(com.oplus.note.baseres.R.string.set_encrypted_to_folder)
            item.setIcon(com.oplus.note.baseres.R.drawable.note_ic_encrypt)
        }
    }

    private fun correctToolbarSelect() {
        val menu: Menu? = binding?.toolbar?.menu
        if ((menu == null) || (menu.size() == 0)) {
            return
        }

        val select = menu.findItem(R.id.select_all) ?: return
        val isAllNoteSelected = isAllNoteSelected()
        if (!PrimaryTitleBehavior.SHOW_MENU_TEXT_BTN) {
            select.setIcon(if (isAllNoteSelected) {
                com.support.appcompat.R.drawable.coui_btn_check_on_normal
            } else {
                com.support.appcompat.R.drawable.coui_btn_check_off_normal
            })
        }
        select.setTitle(if (isAllNoteSelected) R.string.deselect_all else R.string.select_all)
        if (sharedViewModel.noteSelectionMode.value == true) {
            updateUpdateToolbarWhenTextChange()
        }
    }

    private fun correctSearchViewState() {
        if (searchItem == null) {
            return
        }
        noteListViewModel.haveNotes(lifecycleScope) { haveNotes ->
            val isPrivacyDenied = !PrivacyPolicyHelper.isAgreeUserNotice(context)
            val inSearchMode = sharedViewModel.isSearch.value ?: false
            val enable = haveNotes && !isEditMode() && !isPrivacyDenied || inSearchMode
            searchItem?.isEnabled = enable
        }
    }

    fun getCurrentFolderInfo(): FolderInfo? {
        return noteListViewModel.currentFolder.value
    }
    fun getFolderSyncState(): Int? {
        return noteBookViewModel.currentFolder?.value?.extra?.getSyncState()
    }

    private fun notifySelectionChange() {
        val selectedNotes: Set<String> = adapter.getSelectedNotes()
        val isAllTopped: Boolean = adapter.isAllSelectedNotesAreTopped()
        val data = Pair(selectedNotes, isAllTopped)
        noteListViewModel.selectedNotes.value = data
        val isAllNoteSelected = (selectedNotes.isNotEmpty()
                && selectedNotes.size == adapter.mRealNoteItems.size)
        if (isAllNoteSelected != noteListViewModel.isAllNoteSelected.value) {
            noteListViewModel.isAllNoteSelected.value = isAllNoteSelected
        }
        adapter.isSelectAll = isAllNoteSelected
    }

    private fun initNoteListHelper() {
        val noteListHelperCallBack = object : NoteListHelper.CallBack {
            override fun exitRefreshing(tips: String?, delay: Int) {
                noteListViewModel.completeRefreshWithTipsAndDelay.value = Pair(tips, delay)
            }

            override fun showTips(type: Int, arg: Bundle?) {
                if (mDialogFactory == null) {
                    initDialogFactory()
                }
                val dialog = mDialogFactory?.showDialog(type, arg)

                if (DialogFactory.TYPE_DIALOG_DELETE == type) {
                    mDeleteDialog = dialog
                }
            }

            override fun downloadSkin() {
                noteListViewModel.downloadSkin()
            }

            override fun setIsEncryptOrDecrypt(isEncryptOrDecrypt: Boolean) {
                mIsEncryptOrDecrypt = isEncryptOrDecrypt
            }

            override fun updateAdapterModeForListAndMenu(isGrid: Boolean) {
                switchAdapterMode(isGrid)
            }

            override fun turnToAllNoteFolder() {
                switchToAllNoteFolder()
            }
        }
        mNoteListHelper = NoteListHelper(noteListHelperCallBack)
        mNoteListHelper?.setMenuExecutorListener(object : ExecutorProgressListener {
            override fun onExecutorComplete(action: Int, selectedNotes: MutableSet<String>?) {
                AppLogger.BASIC.d(TAG, "onExecutorComplete action $action")
                //搜索过来不走退出编辑模式
                if (action != MenuExecutor.ACTION_CLEAN_DB && !noteListViewModel.isSearchButtonClicked) {
                    exitEditMode()
                    noteListViewModel.isDeletingOrRecovering = false
                }
                noteListViewModel.isSearchButtonClicked = false
                noteListViewModel.search(sharedViewModel.isSearch.value)
                when (action) {
                    MenuExecutor.ACTION_TOPPED,
                    MenuExecutor.ACTION_UN_TOPPED -> {
                        SplitScreenDataSyncManager.topped(selectedNotes)
                    }
                    MenuExecutor.ACTION_DELETE -> {
                        sharedViewModel.noteRecycledBlock?.invoke(selectedNotes)
                        SplitScreenDataSyncManager.deleteOrRestore(selectedNotes)
                    }
                    MenuExecutor.ACTION_DELETE_MARK,
                    MenuExecutor.ACTION_RECOVER -> {
                        SplitScreenDataSyncManager.deleteOrRestore(selectedNotes)
                    }
                }
            }

            override fun onMoveFolderComplete(currentFolderGuid: String, destFolderGuid: String, selectedNotes: MutableSet<String>?) {
                AppLogger.BASIC.d(TAG, "onMoveFolderComplete mIsEncryptOrDecrypt: $mIsEncryptOrDecrypt")
                noteListViewModel.search(sharedViewModel.isSearch.value)
                //搜索点击过来不走退出编辑模式
                if (!noteListViewModel.isSearchButtonClicked) {
                    exitEditMode()
                }
                noteListViewModel.isSearchButtonClicked = false
                noteListViewModel.isDeletingOrRecovering = false
                if (mIsEncryptOrDecrypt) {
                    /**
                     * bugfix-8953424
                     * 原本判断显示加密提示还是解密提示是根据当前笔记本是否是非加密笔记本来确定的（currentInfo.encrypted == DECRYPTED）
                     * 实际上应该以要移动到的笔记本是否是加密笔记本来确定（destFolder.encrypted == ENCRYPTED）。
                     */
                    val destFolder = notebookAgent.getNotebookOrDefault(destFolderGuid)
                    val folderName = destFolder.name
                    UiHelper.showEncryptOrDecryptTips(context, destFolder.encrypted == ENCRYPTED, folderName)
                    mIsEncryptOrDecrypt = false
                    SplitScreenDataSyncManager.moveFolder(selectedNotes)
                } else {
                    SplitScreenDataSyncManager.encrypt(selectedNotes)
                }
            }
        })
        mNoteListHelper?.initData(activity, true)
    }

    /**
     * Switch staggered grid mode and list mode
     */
    private fun switchAdapterMode(isGrid: Boolean) {
        sharedViewModel.noteMode.value = isGrid
        updateToolbarMenuByMode(isGrid)
    }

    private fun updateToolbarMenuByMode(isGrid: Boolean) {
        binding?.toolbar?.menu?.findItem(R.id.mode_note)?.setTitle(if (isGrid) R.string.note_list_mode else R.string.note_grid_mode)
    }

    private fun updateToolbarMenuGroupByPeople(isGroup: Boolean, folderGuid: String?) {
        binding?.toolbar?.menu?.findItem(R.id.group_sort_by)?.subMenu?.run {
            findItem(R.id.group_by_people)?.let {
                it.title = if (folderGuid == FolderInfo.FOLDER_GUID_CALL_SUMMARY) {
                    resources.getString(com.oplus.note.baseres.R.string.speech_group)
                } else {
                    resources.getString(com.oplus.note.baseres.R.string.group_by_source)
                }
                it.isChecked = isGroup
            }
            findItem(R.id.group_not_by_people)?.isChecked = !isGroup
        }
    }

    private fun updateToolbarMenuBySortRule(sortRule: Int?) {
        binding?.toolbar?.menu?.run {
            val isSortUpdateTime = sortRule == SORT_RULE_BY_UPDATE_TIME
            findItem(R.id.sort_create_time)?.isChecked = isSortUpdateTime.not()
            findItem(R.id.sort_update_time)?.isChecked = isSortUpdateTime
            findItem(R.id.group_sort_by)?.subMenu?.run {
                findItem(R.id.sort_create_time_1)?.isChecked = isSortUpdateTime.not()
                findItem(R.id.sort_update_time_1)?.isChecked = isSortUpdateTime
            }
        }
    }

    private fun switchToAllNoteFolder() {
        runCatching {
            switchFolder(notebookAgent.findAllNoteFolder())
        }.onFailure {
            AppLogger.BASIC.e(TAG, "switch to all note folder error: ${it.message}")
        }
    }

    private fun initDialogFactory() {
        mDialogClickListener = object : DialogFactory.DialogOnClickListener {
            override fun onDialogClickButton(type: Int, index: Int) {
            }

            override fun onDialogClickPositive(type: Int) {
                if (null != mNoteListHelper) {
                    val data: Pair<Set<String>?, Boolean?>? = noteListViewModel.selectedNotesData
                    when (type) {
                        DialogFactory.TYPE_DIALOG_DELETE_MARK -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_MARK,
                                        noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), true)
                                opsStatistic("01010106", data.first)
                            }
                        }

                        DialogFactory.TYPE_DIALOG_DELETE -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_DELETE_CONFIRM,
                                        noteListViewModel.selectedFolder, selectedNotes, isAllNoteSelected(), true)
                                isDeleteOperation = true
                                noteListViewModel.search(sharedViewModel.isSearch.value)
                                opsStatistic("01010104", data.first)
                            }
                        }
                        DialogFactory.TYPE_DIALOG_RECOVER -> {
                            if (data?.first != null) {
                                val selectedNotes: Set<String> = HashSet(data.first)
                                noteListViewModel.isDeletingOrRecovering = true
                                mNoteListHelper!!.noteListEdit(DialogFactory.TYPE_DIALOG_RECOVER,
                                        noteListViewModel.currentFolder.value, selectedNotes, isAllNoteSelected(), true)
                                opsStatistic("01010105", data.first)
                            }
                        }
                        DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                            mCallBack?.startRefresh()
                        }
                        DialogFactory.DIALOG_TYPE_STOP_AI_REWRITE_BY_CHANGENOTE -> {
                            needShowKit = false
                            aigcInterruptAction?.invoke()
                            sharedViewModel.stopAigcRewrite.value = true
                        }
                    }
                }
            }

            override fun onDialogClickNegative(type: Int) {
                if (type == DialogFactory.DIALOG_TYPE_DECLARE_ALERT_WINDOW_PERMISSION_REQUEST) {
                    binding?.refresh?.setRefreshCompleted()
                }
            }

            override fun onDialogDismiss(type: Int) {
                if ((DIALOG_TYPE_STOP_AI_REWRITE_BY_CHANGENOTE == type || DIALOG_TYPE_AIKIT_BACK_OR_FULL == type) && needShowKit) {
                    aigcTextHelper.showKitWindow()
                }
                needShowKit = true
            }
        }
        mDialogFactory = DialogFactory(activity, mDialogClickListener)
    }

    private fun isInMultiWindowMode(): Boolean {
        return activity?.isInMultiWindowMode == true
    }

    private fun switchAdapterSortRule(rule: Int = SORT_RULE_BY_CREATE_TIME) {
        if (!isAdded || isSwitchGrideModing || noteListViewModel.sortRule.value == rule) {
            return
        }
        val sortRule = if (rule == SORT_RULE_BY_UPDATE_TIME) {
            SORT_RULE_BY_UPDATE_TIME
        } else {
            SORT_RULE_BY_CREATE_TIME
        }
        val currentMode = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SharedPreferencesUtil.HOME_PAGE_MODE_KEY
        )
        val isGrid = currentMode == SharedPreferencesUtil.HOME_PAGE_MODE_GRID_MODE
        StatisticsUtils.setEventSortRule(sortRule, isGrid)
        changeSort = true
        noteListViewModel.sortRule.value = sortRule
        NoteCardWidgetProvider.instance.postUIToCard(false)
    }

    private fun updateBehavior(isEditMode: Boolean) {
        behavior?.setIsEditMode(isEditMode)
        var marginStart = 0
        if (isEditMode) {
            marginStart = supportTitleMarginStart
        }
        binding?.toolbar?.titleMarginStart = marginStart
    }

    private fun calIsHigherVersion(): Boolean {
        val lastRealTotalNotes = adapter.mRealNoteItems.toMutableList()
        lastRealTotalNotes.forEachIndexed { _, item ->
            val featureList = item.data?.richNote?.extra?.featureList
            val isHighVersion = featureList?.let { NoteFeatureUtil.isHighVersion(it) }
            if (isHighVersion == true) {
                AppLogger.BASIC.d(
                    TAG,
                    "isHighVersion SUPPORT_STRUCT_VERSION:"
                )
                return true
            }
        }
        return false
    }

    private fun resetMainEmptyPageAndSyncTips(hasNotes: Boolean) {
        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips cloudSyncTipCardShow $guideManager infoNotifyController$infoNotifyController $context")
        resetMainEmptyPage()
        if (guideManager == null || infoNotifyController == null || context == null) {
            return
        }

        val guid = noteListViewModel.currentFolder.value?.guid
        if (guid != null) {
            guideManager?.showNoteBookSyncTipView(guid, mSyncEnable)
        }
        refreshSyncTips(hasNotes)
    }

    fun refreshSyncTips(hasNote: Boolean? = null) {
        val hasNotes = hasNote ?: (adapter.getNoteItemCount() != 0)
        guideManager?.getCloudOperation(context, mSyncEnable, lifecycleScope) { cloudOperationShow ->
            if (cloudOperationShow) {
                AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips cloudOperationShow")
                return@getCloudOperation
            }
            context?.let {
                val isHigherVersion = !isIgnoreDiffVersion && calIsHigherVersion()
                if (isHigherVersion) {
                    AppLogger.BASIC.d(
                        TAG,
                        "resetMainEmptyPageAndSyncTips isHigherVersion"
                    )
                    guideManager?.showNotifyDiffVersionView()
                    return@getCloudOperation
                }
            }
            showCloudSyncTipCard(hasNotes) { cloudSyncTipCardShow ->
                if (cloudSyncTipCardShow) {
                    AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips cloudSyncTipCardShow")
                    return@showCloudSyncTipCard
                }
                context?.let {
                    if (!CheckNextAlarmUtils.getNotificationsEnabled(it)) {
                        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips Notifications showNotifyGuideView")
                        guideManager?.showNotifyGuideView(it as Activity, hasNotes, mSyncEnable)
                    } else if (!CommonPermissionUtils.getScheduleAlarmEnabled(it)) {
                        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips showAlarmGuideView")
                        guideManager?.showAlarmGuideView(it as Activity, hasNotes, mSyncEnable)
                    } else if (!CommonPermissionUtils.getScreenOnEnabled(it)) {
                        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips showScreenOnGuideView")
                        guideManager?.showScreenOnGuideView(it as Activity, hasNotes, mSyncEnable)
                    } else if (!CommonPermissionUtils.getOverlayEnabled(it)) {
                        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips showOverlayGuideView")
                        guideManager?.showOverlayGuideView(it as Activity, hasNotes, mSyncEnable)
                    } else {
                        AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips hideSyncGuideView")
                        guideManager?.hideSyncGuideView()
                    }
                    if (!isIgnoreDiffVersion && guideManager?.isShowingDiffVersion() == true) {
                        //当前没有新的提示更新，但是更新版本的提示正在展示，此时应该hide tips
                        guideManager?.hideSyncGuideView()
                    }
                }
            }
        }
    }

    private fun showCloudSyncTipCard(hasNotes: Boolean, showCallBack: ((show: Boolean) -> Unit)? = null) {
        lifecycleScope.launch(Dispatchers.Main) {
            AppLogger.BASIC.d(TAG, "resetMainEmptyPageAndSyncTips sync")
            val isLogin = withContext(Dispatchers.IO) {
                AccountManager.isLogin(activity)
            }
            val isAppInstalled = withContext(Dispatchers.Default) {
                DeviceInfoUtils.isAppInstalled(context, MbaUtils.PACKAGER_CLOUD)
            }
            //已登录，但云同步服务不可用
            val cloudDisable = isLogin && !CloudKitGlobalStateManager.cloudEnable()
            if (OsConfigurations.isMultiSystem || (context != null && !isAppInstalled)
                || cloudDisable) {
                showCallBack?.invoke(false)
            } else {
                withContext(Dispatchers.Default) {
                    if (!isNotAllowSyncEncryptNoteToCloud()) {
                        /**
                         * The information display of InfoBoard in the export version of OnePlus,
                         * Handed over to [RichNoteListAdapter.refreshInfoBoardAndFolderHeaderAndQuestionnaire] to control.
                         */
                        withContext(Main) {
                            if (mSyncEnable != false) {
                                showCallBack?.invoke(false)
                            } else {
                                guideManager?.showCloudSyncTipView(hasNotes, showCallBack)
                            }
                        }
                    } else {
                        if (mSyncEnable == true && hasNotes && isLogin) {
                            withContext(Main) {
                                adapter.showHeaderViewNoteTips()
                            }
                        } else {
                            AppLogger.BASIC.d(TAG, "Not logged in")
                        }
                        showCallBack?.invoke(false)
                    }
                }
            }
            infoNotifyController?.setSyncGuideViewState(!isEditMode(), mSyncEnable == true)
        }
    }

    private fun isNotAllowSyncEncryptNoteToCloud(): Boolean =
            adapter.isEncryptedFolder() && ConfigUtils.isNotAllowSyncEncryptNoteToCloud

    private fun isDeleteEncryptNoteWhenNotAllowSyncToCloud(): Boolean =
            adapter.isAllSelectedNotesAreEncrypt() && ConfigUtils.isNotAllowSyncEncryptNoteToCloud

    private fun resetMainEmptyPage() {
        AppLogger.BASIC.d(TAG, "resetMainEmptyPage in loader=${emptyContentViewLazyLoader == null}")
        if (emptyContentViewLazyLoader == null) {
            return
        }
        if (emptyContentViewLazyLoader?.isInitialized() == true) {
            val isRecentDelete = FolderInfo.FOLDER_GUID_RECENT_DELETE == adapter.getCurrentFolder()?.guid

            emptyContentViewLazyLoader?.switchImgEmptyContent(
                    isRecentDelete && ConfigUtils.isOplusExportVersion
            )
            //目前最近删除无笔记时，需要显示笔记/待办Tab，故空页面updateTopPadding无需特殊处理
            emptyContentViewLazyLoader?.setIsRecentDeleteFolder(isRecentDelete)
            var isInMultiWindowMode = false
            if (activity != null) {
                isInMultiWindowMode = requireActivity().isInMultiWindowMode
            }
            val hasNotes = noteListCountPre > 0
            val isSearchMode = sharedViewModel.isSearch.value ?: false
            val isEncryptedFolder = adapter.isEncryptedFolder()
            emptyContentViewLazyLoader?.resetMainEmptyPage(
                activity,
                isInMultiWindowMode,
                hasNotes,
                loadDataFinished,
                isSearchMode,
                mSyncEnable == true,
                isEncryptedFolder,
                isRecentDelete = isRecentDelete
            )
            /**
             * bugfix-8451860
             * 这里原本没有判断是否是摘要笔记本，导致空摘要笔记本互相切换时，底部的“由AI生成提示”会先消失，然后再出现。
             * 加上了是否是摘要笔记本的判断。另外，由于 isSummary 变量在这里为 false，所以使用 FolderFactory 判断。
             */
            if (!hasNotes && !FolderFactory.isSummaryFolder(adapter.getCurrentFolder())) {
                viewStubCreateByAi.createByAiLl?.visibility = View.INVISIBLE
            }

            viewStubCreateByAi.createByAiTv?.apply {
                this.text = WaterMark.getAIGCMarkTextWithHtmlTag()
            }
        }
    }

    private fun updateRecyclerViewPadding() {
        if (!isAdded) {
            return
        }

        val padding = DensityHelper.getDefaultConfigDimension(R.dimen.grid_item_content_margin_horizontal)
        val paddingBottom = resources.getDimensionPixelOffset(R.dimen.note_edit_mode_padding_bottom)
        binding?.noteList?.setPadding(padding, 0, padding, paddingBottom)
    }

    fun resetCheckedInfo() {
        AppLogger.BASIC.d(TAG, "resetCheckedInfo")
        noteViewModel.resetSelectedRichNote()
        adapter.setCheckedGuid("")
        searchAdapter.setCheckedGuid("")
    }

    /**
     * 无选中时，若第一条笔记为非加密笔记，默认选中当前笔记本第一条笔记。
     */
    private fun trySelectFirstRichNote() {
        val firstNote = adapter.getAdapterNoteItems().firstOrNull { it.viewType == RichNoteItem.TYPE_NOTE }?.data

        if (twoPane && !hasWVNoteViewEditFragment()) {
            AppLogger.BASIC.d(TAG, "updateFirstChecked:${firstNote?.richNote?.localId}")
            if (firstNote == null || notebookAgent.getNotebooks().find { it.guid == firstNote.richNote.folderGuid }?.isEncrypted == true) {
                resetCheckedInfo()
                return
            }
            noteViewModel.forceUpdate = true
            openNote(firstNote.richNote.localId, force = true)
        }
    }

    private fun refreshSelectedUiState(force: Boolean = false) {
        if (twoPane || force) {
            val selectedGuid = noteViewModel.getSelectedRichNote()?.localId ?: ""
            adapter.setCheckedGuid(selectedGuid)
            searchAdapter.setCheckedGuid(selectedGuid)

            //1.取消之前选中的item
            refreshPreSelectedItem()
            //2.刷新当前按下的item
            refreshSelectedItem(true)
        }
    }

    private fun refreshPreSelectedItem() {
        val preSelectedPosition = adapter.getAdapterNoteItems().indexOfFirst { noteViewModel.isPreSelectedRichNote(it.data?.richNote?.localId) }
        if (preSelectedPosition >= 0) {
            val preViewHolder = binding?.noteList?.findViewHolderForAdapterPosition(preSelectedPosition) as? NoteViewHolder
            val preItem = adapter.getAdapterNoteItems().getOrNull(preSelectedPosition)
            if (preViewHolder != null && preItem != null) {
                adapter.setDefaultItemBackground(preViewHolder, false, preItem)
            } else {
                adapter.notifyItemChanged(preSelectedPosition)
            }
        }
    }

    private fun refreshSelectedItem(isSelected: Boolean) {
        val selectedPosition = adapter.getAdapterNoteItems().indexOfFirst { noteViewModel.isSelectedRichNote(it.data?.richNote?.localId) }
        val firstNotePositon = adapter.getAdapterNoteItems().indexOfFirst { it.viewType == RichNoteItem.TYPE_NOTE }
        val folderId = adapter.getCurrentFolder()?.guid ?: ""
        //如果选中的位置是第一个且没有被折叠就不用取消选中
        if (selectedPosition == firstNotePositon && !isSelected && !ToppedUtil.getToppedSharedPreferences(context, folderId)) {
            return
        }
        if (selectedPosition >= 0) {
            val viewHolder = binding?.noteList?.findViewHolderForAdapterPosition(selectedPosition) as? NoteViewHolder
            val selectItem = adapter.getAdapterNoteItems().getOrNull(selectedPosition)
            if (viewHolder != null && selectItem != null) {
                adapter.setDefaultItemBackground(viewHolder, isSelected, selectItem)
            } else {
                adapter.notifyItemChanged(selectedPosition)
            }
        }
    }

    private fun refreshSelectedUiStateInSearchMode(force: Boolean = false) {
        if (twoPane || force) {
            //1.取消之前选中的item
            val preSelectedPosition = searchAdapter.mSearchItems.indexOfFirst { noteViewModel.isPreSelectedRichNote(it.data?.richNote?.localId) }
            val preViewHolder = searchViewAnimatorHelper?.resultList?.findViewHolderForAdapterPosition(preSelectedPosition)
            val preItemImageView = (preViewHolder as? NoteViewHolder)?.getItemPressAnimView(false)
            if (preItemImageView != null) {
                searchAdapter.setItemBackground(preItemImageView, false)
            } else {
                if (preSelectedPosition > 0) {
                    searchAdapter.notifyItemChanged(preSelectedPosition)
                }
            }

            //2.刷新当前按下的item
            val selectedPosition = searchAdapter.mSearchItems.indexOfFirst { noteViewModel.isSelectedRichNote(it.data?.richNote?.localId) }
            AppLogger.BASIC.d(TAG, "updateSelectedUiStateInSearchMode preSelectedPosition=$preSelectedPosition, selectedPosition=$selectedPosition")
            val viewHolder = searchViewAnimatorHelper?.resultList?.findViewHolderForAdapterPosition(selectedPosition)
            val itemImage = (viewHolder as? NoteViewHolder)?.getItemPressAnimView(false)
            if (itemImage != null) {
                searchAdapter.setItemBackground(itemImage, true)
            } else {
                searchAdapter.notifyItemChanged(selectedPosition)
            }
        }
    }

    private fun getPositionByGuid(guid: String): Int {
        return guidHashMap?.get(guid) ?: 0
    }

    fun onRestart() {
        if (!isAdded) {
            return
        }

        refreshResumeCloud()
        val isZoomWindowState = OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)
        AppLogger.BASIC.d(TAG, "onRestart isZoomWindowState=$isZoomWindowState, mInZoomWindowState=$mInZoomWindowState")
    }

    fun stringToList(input: String): List<String> {
        return if (input == "null") {
            emptyList() // 如果输入是 "null"，返回 null
        } else {
            val trimmed = input.trim('[', ']')
            if (trimmed.isEmpty()) emptyList() else trimmed.split(",").map { it.trim() }
        }
    }
    inner class LocalReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent == null || !isAdded) {
                return
            }
            if (Constants.ACTION_SAVE_NOTE_COMPLETE == intent.action) {
                val guid = IntentParamsUtil.getStringExtra(intent, Constants.EXTRA_NOTE_GUID, "")
                if (twoPane && guid.isNotEmpty()) {
                    val featureListStr = IntentParamsUtil.getStringExtra(intent, Constants.SAVE_NOTE_FEATURE_LIST, "")
                    val isFromEdit = IntentParamsUtil.getBooleanExtra(intent, Constants.IS_FROM_EDIT, false)
                    val position = getPositionByGuid(guid)
                    val featureList = stringToList(featureListStr)
                    AppLogger.BASIC.d(
                        TAG,
                        "onReceive folderid:$guid, position=$position featureList$featureList"
                    )
                    if (position > 0) {
                        adapter.notifyDataSetChangedDelegate()
                    }
                    for (realNoteItemsPosition in adapter.mRealNoteItems.indices) {
                        if (adapter.mRealNoteItems[realNoteItemsPosition].data?.richNote?.localId == guid) {
                            adapter.mRealNoteItems[realNoteItemsPosition].data?.richNote?.extra?.featureList = featureList
                            break // 完成赋值后立即跳出循环
                        }
                    }
                    if (isFromEdit) {
                        val isHighVersion = calIsHigherVersion()
                        AppLogger.BASIC.d(TAG, "isFromEdit$isFromEdit isHighVersion$isHighVersion")
                        if (!isHighVersion) {
                            //如果笔记列表没有高版本的笔记那么移除高低版本的提示
                            guideManager?.hideSyncGuideView()
                            NoteFeatureUtil.setIsIgnoreDiffVersion(true)
                        }
                    }
                }
            } else if (Constants.ACTION_SAVE_NOTE_FINISHED == intent.action) {
                AppLogger.BASIC.d(TAG, "onReceive itemId:${noteViewModel.notifyDetailSaveData.value}")
                if (twoPane && (sharedViewModel.noteSelectionMode.value == true || sharedViewModel.isRecentDeleteFolder.value == true)) {
                    handleNavigationItemSelected(noteViewModel.notifyDetailSaveData.value!!)
                }
            } else if (Constants.ACTION_SAVE_PICTURE_COMPLETE == intent.action) {
                val guid = IntentParamsUtil.getStringExtra(intent, Constants.EXTRA_NOTE_GUID, "")

                if (guid.isNotEmpty()) {
                    val position = getPositionByGuid(guid)
                    AppLogger.BASIC.d(TAG, "onReceive ACTION_SAVE_PICTURE_COMPLETE folderid:$guid, position=$position")
                    if (position > 0) {
                        adapter.notifyItemChanged(position)
                    }
                }
            } else if (Constants.ACTION_DOWNLOAD_SKIN_COMPLETE == intent.action) {
                AppLogger.BASIC.d(TAG, "onReceive ACTION_DOWNLOAD_SKIN_COMPLETE")
                adapter.notifyDataSetChanged()
            } else if (intent.action == Constants.ACTION_NOTIFICATION_GRANT) {
                if (adapter != null) {
                    val hasNotes = adapter.getNoteItemCount() != 0
                    resetMainEmptyPageAndSyncTips(hasNotes)
                }
            } else if (Constants.ACTION_REFRESH_DATA_ON_AIGC_DELETE == intent.action) {
                AppLogger.BASIC.d(TAG, "onReceive ACTION_REFRESH_DATA_ON_AIGC_DELETE")
                val value = noteListViewModel.dataRefresh.value
                noteListViewModel.dataRefresh.value = value != true
            } else if (SHOW_ALARM_TIPS_ACTION == intent.action) {
                val dialogMessage = intent.getIntExtra(DIALOG_MESSAGE, 0)
                if (dialogMessage == com.oplus.note.baseres.R.string.schedule_alarm_permission_dialog_msg
                    && sharedViewModel.currentTabIndex.value == MainActivity.NOTE_INDEX
                ) {
                    //当且仅当是时钟权限被取消并且处于笔记页才会走提示更新逻辑
                    AppLogger.BASIC.d(TAG, "SHOW_ALARM_TIPS_ACTION")
                    CloudKitInfoController.isAlarmIgnore = false
                    refreshNoteListTips()
                }
            }
        }
    }

    fun refreshCheckBox(selectMode: MenuMultiSelectHelper.MenuMode) {
        val listLayoutManager = binding?.noteList?.layoutManager as StaggeredGridLayoutManager
        val startArray = listLayoutManager.findFirstVisibleItemPositions(null)
        val endArray = listLayoutManager.findLastVisibleItemPositions(null)

        Arrays.sort(startArray)
        Arrays.sort(endArray)

        var start = startArray[0].coerceAtLeast(adapter.getHeaderCount())
        var end = endArray[endArray.size - 1]
        AppLogger.BASIC.d(TAG, "refreshCheckBox  start=$start,  end=$end")
        adapter.apply {
            for (i in start..end) {
                val holder =
                    binding?.noteList?.findViewHolderForLayoutPosition(i) as? NoteViewHolder
                        ?: continue
                val checkbox = if (getAdapterMode() == RichNoteListAdapter.ADAPTER_MODE_LIST) {
                    holder.mListCheckbox
                } else {
                    holder.mGridCheckbox
                }

                when (selectMode) {
                    MenuMultiSelectHelper.MenuMode.ENTER -> {
                        craeteAnimation(holder, true)?.start()
                        holder.mIsEditMode = true
                    }

                    MenuMultiSelectHelper.MenuMode.LEAVE -> {
                        holder.mIsEditMode = false
                        checkbox.isChecked = false
                        craeteAnimation(holder, false)?.start()
                    }

                    MenuMultiSelectHelper.MenuMode.SELECT_ALL -> {
                        checkbox.isChecked = true
                    }

                    MenuMultiSelectHelper.MenuMode.DE_SELECT_AL -> {
                        checkbox.isChecked = false
                    }
                }
            }

            notifyItemRangeChanged(0, start, CHECKBOX_POSITION)
            notifyItemRangeChanged(end, itemCount - end, CHECKBOX_POSITION)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        if (mDialogFactory?.lastDialog?.isShowing == true && (mDialogFactory != null)) {
            outState.putBoolean(Constants.DIALOG_REBUILD_TAG, true)
            noteListViewModel.dialogType = mDialogFactory!!.dialogType
            noteListViewModel.dialogExtra = mDialogFactory?.dialogExtra
        } else {
            noteListViewModel.isCreateDialog = false
            noteListViewModel.dialogType = DialogFactory.TYPE_DIALOG_DEFAULT
            noteListViewModel.dialogExtra = null
        }
    }

    fun refreshNoteListTips() {
        val hasNotes = adapter.getNoteItemCount() != 0
        resetMainEmptyPageAndSyncTips(hasNotes)
    }


    /**
     * 便签悬浮窗状态下
     * 更新最近删除笔记本的加密状态与隐私密码最近删除验证开关状态同步
     */
    fun updateRecentDelNoteStatus(state: Boolean, isFromInternal: Boolean) {
        AppLogger.BASIC.d(TAG, "updateRecentDelNoteStatus: $state, $isFromInternal")
        changeFoldersOfDeleteEncrypt(notebookAgent.getNotebooks())

        notebookAgent.getCurrentNotebook()?.let {
            noteListViewModel.currentFolder.value = FolderInfo(it)
        }

        val folder = notebookAgent.getCurrentNotebook()
        val isCheck = state && FolderFactory.isRecentDeleteFolder(folder)
        if (isCheck && !isFromInternal) {
            notebookEncryptAgent.check { success ->
                if (!success) {
                    switchToAllNoteFolder()
                }
            }
        }
    }

    /**
     * @param guid 切换笔记的时候传入切换后的笔记ID
     * @param checkGuid 是否需要检查笔记id，只有切换笔记场景生效
     *
     */
    private fun checkAIGCState(
        tag: String? = null,
        guid: String? = null,
        checkGuid: Boolean = false,
        after: () -> Unit?
    ) {
        if (twoPane.not()) {
            after.invoke()
            return
        }
        AppLogger.BASIC.d(TAG, "checkAIGCState $tag with checkGuid:$checkGuid")
        aigcInterruptAction = after
        if (noteViewModel.isSelectedRichNote(guid) && checkGuid) {
            AppLogger.BASIC.d(TAG, "abort open when excute aigc click same item return")
            return
        }
        if (sharedViewModel.currentNoteAigcState != AIGCState.STATE_IDLE) {
            AppLogger.BASIC.d(TAG, "abort open when excute aigc")
            lifecycleScope.launch(Dispatchers.Main) {
                if (mDialogFactory == null) {
                    initDialogFactory()
                }
                mDialogFactory?.showDialog(
                    DialogFactory.DIALOG_TYPE_STOP_AI_REWRITE_BY_CHANGENOTE, null
                )
                aigcTextHelper.hideKitWindow()
            }
        } else {
            after.invoke()
        }
    }

    override fun onStop() {
        super.onStop()
        AppLogger.BASIC.d(TAG, "onStop")
        if (IWebViewProxyCache.ENABLE_CACHE && !requireActivity().isChangingConfigurations) {
            /*在切换到后台时监听webview的destroy。
            在暗色模式切换、字体切换等configChanged场景下必须释放所有webview(包括正在使用中的)，否则新建的webview缓存的config配置可能还是引用切换前的，导致异常。
            在将该页面置于后台，然后连续切换两次暗色模式后返回到前台，页面的configChange恢复了原样因此不会触发重建，但是webview缓存要求是config变化一次就必须清除所有缓存.
            因此这里需要单独监听webview destroy，并主动将父子级中子级的fragment销毁掉，避免webview被销毁fragment还存在导致内容加载不出来。
             */
            Injector.injectFactory<IWebViewProxyCache>()?.addWebViewForceDestroyListener(webViewForceDestroyListener)
        }
//        GlideThumbManager.checkRelease("NoteListFragment #onStop")
    }

    override fun onStart() {
        super.onStart()
        if (IWebViewProxyCache.ENABLE_CACHE) {
            Injector.injectFactory<IWebViewProxyCache>()?.removeWebViewForceDestroyListener(webViewForceDestroyListener)
        }
    }

    private var imeVisible = false

    @SuppressLint("RestrictedApi")
    private val showSearchSoftInputRunnable = Runnable {
        if (imeVisible) {
            return@Runnable
        }
        searchView?.searchView?.searchAutoComplete?.let {
            it.requestFocus()
            kotlin.runCatching {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM)
            }.onFailure { e ->
                AppLogger.BASIC.e(TAG, "clearFlags: FLAG_ALT_FOCUSABLE_IM, e = ${e.message}")
            }
            inputMethodManager.showSoftInput(it, 0)
        }
    }

    /**
     * 取消按钮、标题、全选按钮文本变换时 需要更新标题位置
     */
    private fun updateUpdateToolbarWhenTextChange() {
        view?.post {
            behavior?.updateToolbar()
        }
    }

    fun dispatchKeyEvent(keyEvent: KeyEvent): Boolean {
        if (keyboardManager.isCtrlFEvent(keyEvent)) {
            onSearchViewClick()
            return true
        }
        return false
    }
}