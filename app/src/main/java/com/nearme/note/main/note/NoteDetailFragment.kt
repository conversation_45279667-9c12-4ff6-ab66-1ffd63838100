/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteDetailFragment.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.nearme.note.activity.richedit.NoteViewRichEditActivity.Companion.EXTRA_FOLDER
import com.nearme.note.activity.richedit.coverpaint.AdaptationCoverPaintUtil
import com.nearme.note.activity.richedit.coverpaint.WVNoteViewEditFragmentCoverPaint
import com.nearme.note.activity.richedit.coverpaint.WVNoteViewEditFragmentCoverPaint.Companion
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.common.Constants
import com.nearme.note.common.getPlayClickNum
import com.nearme.note.common.setCallContentTipsFlagEnable
import com.nearme.note.db.NotesProvider
import com.nearme.note.main.ActivitySharedViewModel
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.ScreenUtil
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.R
import com.oplus.note.databinding.NoteDetailFragmentBinding
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Attachment.Companion.TYPE_COVER_PAINT
import com.oplus.note.repo.note.entity.Attachment.Companion.TYPE_COVER_PICTURE
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import java.util.Objects

class NoteDetailFragment : Fragment() {
    companion object {
        const val TAG = "NoteDetailFragment"

        const val COUNT_100 = 100L
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @return A new instance of fragment NoteDetailFragment.
         */
        @JvmStatic
        fun newInstance(): NoteDetailFragment = NoteDetailFragment().apply {
            arguments = Bundle().apply {}
        }
    }

    private val sharedViewModel by viewModels<ActivitySharedViewModel>({ requireActivity() })
    private val noteViewModel by viewModels<NoteViewModel>({ requireActivity() })
    private var noteInfoTemp: SelectedRichNoteInfo? = null
    private var binding: NoteDetailFragmentBinding? = null

    private var mEmptyPageHelper: ImageHelper? = null
    private var mInitFinished = false
    private var isDevicePad = false
    private var mAttachmentId = ""
    private var screenWidth = 0f
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        AppLogger.BASIC.d(TAG, "onCreateView")
        binding = NoteDetailFragmentBinding.inflate(inflater, container, false)
        isDevicePad = UiHelper.isDevicePad()
        screenWidth = ScreenUtil.getScreenWidth().toFloat()
        return binding?.root
    }

    private var isRecreate: Boolean = false
    var isCoverPaint: Boolean = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppLogger.BASIC.d(TAG, "onViewCreated")

        binding?.emptyContentLottie?.alpha = ImageHelper.LOTTIE_ALPHA

        mEmptyPageHelper = ImageHelper(requireActivity())

        binding?.let {
            mEmptyPageHelper?.init(
                it.root,
                it.emptyContainer,
                it.emptyContentLottie,
                topOffSize = context?.resources?.getDimensionPixelSize(R.dimen.dp_30) ?: 0
            )
            mEmptyPageHelper?.calculateFinishCallBack = {
                mInitFinished = true
            }
        }

        isRecreate = savedInstanceState != null
        initiateObservers()
    }

    override fun onResume() {
        super.onResume()
        AppLogger.BASIC.d(TAG, "onResume")
        updateEmptyViewPosition()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AppLogger.BASIC.d(TAG, "NoteDetailFragment onConfigurationChanged")
        screenWidth = ScreenUtil.getScreenWidth().toFloat()
        updateEmptyViewPosition(true)
        removeEditFragmentIfNeed()
    }

    fun onFragmentSelected() {
        AppLogger.BASIC.d(TAG, "onFragmentSelected")
        updateEmptyViewPosition()
    }

    private fun initEmptyViewVisibility() {
        kotlin.runCatching {
            val isVisible = binding?.emptyContainer?.isVisible ?: false
            // noteViewModel使用的requireActivity初始化的，可能出现crash，使用runCatching保护一下
            if (isVisible || mInitFinished || noteViewModel.hasSelectedRichNote()) return
            binding?.emptyContainer?.visibility = View.VISIBLE
        }
    }

    private fun updateEmptyViewPosition(needDelay: Boolean = false) {
        binding?.let {
            if (it.emptyContainer.isVisible) {
                it.root.postDelayed({
                    AppLogger.BASIC.d(TAG, "updateEmptyViewPosition")
                    mEmptyPageHelper?.calculate(it.root, it.emptyContainer, it.emptyContentLottie)
                }, if (needDelay) COUNT_100 else 0)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (isCoverPaint) {
            handleActivityWVCoverPaintResult(requestCode, resultCode, data)
        } else {
            handleActivityWVResult(requestCode, resultCode, data)
        }
    }

    private fun handleActivityWVResult(requestCode: Int, resultCode: Int, data: Intent?) {
        val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
        if (sharedViewModel.twoPane && fragment != null && fragment.isAdded && fragment is WVNoteViewEditFragment) {
            AppLogger.BASIC.d(TAG, " handleActivityWVResult add to NoteViewEditFragment")
            (fragment as? WVNoteViewEditFragment)?.onActivityResult(requestCode, resultCode, data)
        }
    }
    private fun handleActivityWVCoverPaintResult(requestCode: Int, resultCode: Int, data: Intent?) {
        val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
        if (sharedViewModel.twoPane && fragment != null && fragment.isAdded && fragment is WVNoteViewEditFragmentCoverPaint) {
            AppLogger.BASIC.d(TAG, " handleActivityWVCoverPaintResult add to NoteViewEditFragment")
            (fragment as? WVNoteViewEditFragmentCoverPaint)?.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun initiateObservers() {
        noteViewModel.observeSelectedRichNote(viewLifecycleOwner) { info ->
            noteInfoTemp = info
            AppLogger.BASIC.d(TAG, "observeSelectedRichNote info:${info?.note?.richNote?.localId}")
            //为空的时候直接放开，否则移除fragment的操作无法执行
            if (lifecycle.currentState != Lifecycle.State.RESUMED && info != null) {
                AppLogger.BASIC.d(TAG, "detailFragment lifecycle ${lifecycle.currentState}")
                return@observeSelectedRichNote
            }
            val contextTemp = context
            AppLogger.BASIC.d(TAG, "contextTemp == null:${contextTemp == null}")
            contextTemp?.let {
                val note = info?.note
                val folder = info?.notebook
                var searchList = info?.highlightSearch?.let { list -> ArrayList(list) } ?: arrayListOf()
                val searchAttachmentId = noteViewModel.searchAttachmentId
                if (sharedViewModel.isSearch.value == false) {
                    searchList = arrayListOf()
                }

                if(isCoverPaint){
                    val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
                    if (fragment is WVNoteViewEditFragmentCoverPaint) {
                        fragment.enterViewMode()
                    }
                    handleFragment(note, folder, searchList, searchAttachmentId, fragment)
                }else{
                    val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
                    if (fragment is WVNoteViewEditFragment) {
                        fragment.enterViewMode()
                    }
                    handleFragment(note, folder, searchList, searchAttachmentId, fragment)
                }
                noteViewModel.mLastSearchList = searchList
            }
        }
        noteDataChangedObserve()
        sortRuleChangedObserve()
        noteCountObserve()
        notifyDetailSaveDataObserve()
        isSearchObserve()
        turnToAllNoteFolderObserve()
    }

    private fun handleFragment(
        note: RichNoteWithAttachments?,
        folder: Folder?,
        searchList: ArrayList<String>?,
        searchAttachmentId: String?,
        fragment: Fragment?
    ) {
        AppLogger.BASIC.d(TAG, "handleFragment ${note?.richNote?.localId}")
        if (note != null && folder != null) {
            if (!noteViewModel.isPreSelectedRichNote(note.richNote.localId) ||
                !Objects.equals(searchList, noteViewModel.mLastSearchList) || noteViewModel.forceUpdate) {
                isCoverPaint = AdaptationCoverPaintUtil.isCoverPaintNotCharCount(note)
                noteViewModel.forceUpdate = false
                val hasOffset =
                    sharedViewModel.noteSelectionMode.value != true && sharedViewModel.isSearch.value != true
                val oldFragment =if(isCoverPaint) childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG) else
                    childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
                childFragmentManager.commit { /*
                    此处切换动画如果使用animation动画，会概率性出现动画start后不会执行cancel或end，间接导致fragment无法真正的destroy，具体原因暂未找到。
                    但是从Fragment中源码发现，动画为animation和animator这两种不同类型时，处理方式也会不一样。
                    经实测后发现将切换动画改为animator后不会再出问题，因此参考原animation动画参数将切换动画替换为animator动画
                     */
                    setCustomAnimations(
                        R.animator.note_view_edit_fragment_enter,
                        R.animator.note_view_edit_fragment_exit
                    )
                    if(isCoverPaint){
                        childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)?.let {
                            childFragmentManager.beginTransaction().remove(it).commitAllowingStateLoss()
                        }
                        add(
                            R.id.content, WVNoteViewEditFragmentCoverPaint.newInstance(
                                true,
                                note,
                                folder.guid,
                                folder.name,
                                searchList = searchList,
                                searchAttachmentId = searchAttachmentId,
                                hasOffset = hasOffset
                            ).apply {
                                recycledCallBack =
                                    { guid: String, fragment: Fragment?, isRecover: Boolean -> //子级打开的笔记被删除后,更新页面为“未选中笔记”
                                        if (noteViewModel.isSelectedRichNote(guid)) {
                                            if (!isRecover) {
                                                saveNote(true)
                                            }
                                            if (sharedViewModel.isSearch.value == true) {
                                                noteViewModel.searchPair = Pair(guid, isRecover)
                                            }
                                        }
                                    }
                            }, WVNoteViewEditFragmentCoverPaint.TAG
                        )
                    }
                    else{
                        childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)?.let {
                            childFragmentManager.beginTransaction().remove(it).commitAllowingStateLoss()
                        }
                        add(
                            R.id.content, WVNoteViewEditFragment.newInstance(
                                true,
                                note,
                                folder.guid,
                                folder.name,
                                searchList = searchList,
                                searchAttachmentId = searchAttachmentId,
                                hasOffset = hasOffset
                            ).apply {
                                recycledCallBack =
                                    { guid: String, fragment: Fragment?, isRecover: Boolean -> //子级打开的笔记被删除后,更新页面为“未选中笔记”
                                        if (noteViewModel.isSelectedRichNote(guid)) {
                                            if (!isRecover) {
                                                saveNote(true)
                                            }
                                            if (sharedViewModel.isSearch.value == true) {
                                                noteViewModel.searchPair = Pair(guid, isRecover)
                                            }
                                        }
                                    }
                            }, WVNoteViewEditFragment.TAG
                        )
                    }
                    oldFragment?.let {
                        if(isCoverPaint){
                            (it as? WVNoteViewEditFragmentCoverPaint)?.apply {
                                //切换的时候先隐藏涂鸦，不然会出现涂鸦隐藏滞后的情况
                                mPaintView?.visibility = View.GONE
                                // 切换的时候取消播放语音
                                /*audioPlayerHelper.releasePlay()*/
                                audioPlayViewModel.currentDuration = 0
                                if (getPlayClickNum() >= 1) {
                                    setCallContentTipsFlagEnable()
                                    mCallContentTipsManager.dismissTips()
                                }
                            }
                            remove(it)
                        }else{
                            (it as? WVNoteViewEditFragment)?.apply {
                                //切换的时候先隐藏涂鸦，不然会出现涂鸦隐藏滞后的情况
                                mPaintView?.visibility = View.GONE
                                // 切换的时候取消播放语音
                                audioPlayerHelper.releasePlay()
                                audioPlayViewModel.currentDuration = 0
                                if (getPlayClickNum() >= 1) {
                                    setCallContentTipsFlagEnable()
                                    mCallContentTipsManager.dismissTips()
                                }
                            }
                            remove(it)
                        }
                    }
                }
                binding?.content?.visibility = View.VISIBLE
                binding?.emptyContainer?.visibility = View.GONE
                AppLogger.BASIC.d(TAG, "note guid changed, replace fragment")
            } else {
                AppLogger.BASIC.d(
                    TAG, "${!noteViewModel.isPreSelectedRichNote(note.richNote.localId)}" +
                            ",${!Objects.equals(searchList, noteViewModel.mLastSearchList)}" +
                            ",${noteViewModel.forceUpdate}"
                )
                AppLogger.BASIC.d(TAG, "note guid no changed, do noting")
            }
        } else {
            // note为空，仅当前笔记本下无笔记时，才显示空页面
            AppLogger.BASIC.d(TAG, "removeFragment")
            removeFragment(fragment)
        }
    }

    private fun noteDataChangedObserve() {
        noteViewModel.noteDataChanged.observe(viewLifecycleOwner) { noteInfo: Pair<RichNoteWithAttachments, Folder>? ->
            if (!isResumed) {
                return@observe
            }
            if (isRecreate) {
                isRecreate = false
                return@observe
            }
            isCoverPaint = noteInfo?.first?.let { AdaptationCoverPaintUtil.isCoverPaintNotCharCount(it) } ?: false
            AppLogger.BASIC.d(TAG, "noteDataChangedObserve isCoverPaint:$isCoverPaint")
            if (isCoverPaint) {
                val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
                if (fragment is WVNoteViewEditFragmentCoverPaint) {
                    activity?.let {
                        val newLocalId = noteInfo?.first?.richNote?.localId
                        val isUnFold = ResponsiveUIConfig.getDefault(it)?.uiStatus?.value == UIConfig.Status.UNFOLD
                        AppLogger.BASIC.d(TAG, "observed note data changed, isUnFold=$isUnFold, newLocalId=$newLocalId")

                        if (isUnFold && noteViewModel.isSelectedRichNote(newLocalId)) {
                            AppLogger.BASIC.d(TAG, "---noteDataChanged---")
                            val note = noteInfo?.first
                            val folder = noteInfo?.second
                            val args = Bundle()
                            args.putParcelable(WVNoteViewEditFragmentCoverPaint.ARGUMENTS_EXTRA_NOTE, note)
                            args.putString(NotesProvider.COL_NOTE_FOLDER_GUID, folder?.guid)
                            args.putString(NotesProvider.COL_NOTE_FOLDER, folder?.name)
                            args.putParcelable(EXTRA_FOLDER, folder)
                            val intent = Intent().putExtras(args)
                            fragment.notifyNoteDataChanged(intent)
                        }
                    }
                }
            } else {
                val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
                AppLogger.BASIC.d(TAG, "noteDataChangedObserve fragment==null${fragment == null}")
                if (fragment is WVNoteViewEditFragment) {
                    childFragmentManager.commit {
                        val fragmentCoverPaint = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
                        if (fragmentCoverPaint is WVNoteViewEditFragmentCoverPaint) {
                            AppLogger.BASIC.d(TAG, "remove fragment, noteCount=${noteViewModel.noteCount.value}")
                            remove(fragmentCoverPaint)
                        }
                    }
                    activity?.let {
                        val newLocalId = noteInfo?.first?.richNote?.localId
                        val isUnFold = ResponsiveUIConfig.getDefault(it)?.uiStatus?.value == UIConfig.Status.UNFOLD
                        AppLogger.BASIC.d(TAG, "observed note data changed, isUnFold=$isUnFold, newLocalId=$newLocalId")

                        if (isUnFold && noteViewModel.isSelectedRichNote(newLocalId)) {
                            AppLogger.BASIC.d(TAG, "---noteDataChanged---")
                            val note = noteInfo?.first
                            val folder = noteInfo?.second
                            val args = Bundle()
                            args.putParcelable(WVNoteViewEditFragment.ARGUMENTS_EXTRA_NOTE, note)
                            args.putString(NotesProvider.COL_NOTE_FOLDER_GUID, folder?.guid)
                            args.putString(NotesProvider.COL_NOTE_FOLDER, folder?.name)
                            args.putParcelable(EXTRA_FOLDER, folder)
                            val intent = Intent().putExtras(args)
                            fragment.notifyNoteDataChanged(intent)
                        }
                    }
                } else if (fragment == null && sharedViewModel.twoPane) {
                    val contextTemp = context
                    AppLogger.BASIC.d(TAG, "fragment is null contextTemp == null:${contextTemp == null}")
                    contextTemp?.let {
                        val note = noteInfo?.first
                        val folder = noteInfoTemp?.notebook
                        val searchList = noteInfoTemp?.highlightSearch?.let { list -> ArrayList(list) } ?: arrayListOf()
                        val searchAttachmentId = noteViewModel.searchAttachmentId
                        handleFragment(note, folder, searchList, searchAttachmentId, fragment)
                    }
                }
            }
        }
    }

    private fun sortRuleChangedObserve() {
        noteViewModel.sortRuleChanged.observe(viewLifecycleOwner) { sortRule ->
            if (isCoverPaint) {
                val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
                if (fragment is WVNoteViewEditFragmentCoverPaint) {
                    activity?.let {
                        val isUnFold =
                            ResponsiveUIConfig.getDefault(it)?.uiStatus?.value == UIConfig.Status.UNFOLD
                        AppLogger.BASIC.d(TAG, "observed sort rule changed, isUnFold=$isUnFold")

                        if (isUnFold) {
                            fragment.notifySortRuleChanged(sortRule)
                        }
                    }
                }
            } else {
                val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
                if (fragment is WVNoteViewEditFragment) {
                    activity?.let {
                        val isUnFold =
                            ResponsiveUIConfig.getDefault(it)?.uiStatus?.value == UIConfig.Status.UNFOLD
                        AppLogger.BASIC.d(TAG, "observed sort rule changed, isUnFold=$isUnFold")

                        if (isUnFold) {
                            fragment.notifySortRuleChanged(sortRule)
                        }
                    }
                }
            }
        }
    }

    private fun noteCountObserve() {
        noteViewModel.noteCount.observe(viewLifecycleOwner) { count ->
            if (count == 0) {
                binding?.emptyContainer?.visibility = View.VISIBLE
            } else {
                if (!noteViewModel.hasSelectedRichNote()) {
                    if (!mInitFinished) {
                        binding?.emptyContainer?.visibility = View.INVISIBLE
                    } else {
                        binding?.emptyContainer?.visibility = View.VISIBLE
                    }
                }
            }
        }
    }

    private fun notifyDetailSaveDataObserve() {
        noteViewModel.notifyDetailSaveData.observe(viewLifecycleOwner) { itemId ->
            AppLogger.BASIC.d(TAG, "notify detail save note data, itemId=$itemId")
            if (itemId == 0) {
                return@observe
            }

           if(isCoverPaint){
               val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
               if (fragment is WVNoteViewEditFragmentCoverPaint && fragment.isAdded) {
                   fragment.saveNoteAndDoodle(fromParent = true)
               } else {
                   val intent = Intent(Constants.ACTION_SAVE_NOTE_FINISHED)
                   LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(intent)
               }
           }else{
               val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
               if (fragment is WVNoteViewEditFragment && fragment.isAdded) {
                   fragment.saveNoteAndDoodle(fromParent = true)
               } else {
                   val intent = Intent(Constants.ACTION_SAVE_NOTE_FINISHED)
                   LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(intent)
               }
           }
        }
    }

    private fun isSearchObserve() {
        sharedViewModel.isSearch.observe(viewLifecycleOwner) { isSearchMode ->
            val hasOffset = !isSearchMode && sharedViewModel.noteSelectionMode.value != true
           if(isCoverPaint){
               val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
               if (fragment is WVNoteViewEditFragmentCoverPaint) {
                   fragment.notifyRichToolbarBottomOffsetStateChanged(hasOffset, fromSearch = true)
               }
           }else{
               val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG)
               if (fragment is WVNoteViewEditFragment) {
                   fragment.notifyRichToolbarBottomOffsetStateChanged(hasOffset, fromSearch = true)
               }
           }
        }
    }

    private fun turnToAllNoteFolderObserve() {
        sharedViewModel.turnToAllNoteFolder.observe(viewLifecycleOwner) { value ->
            if (value == true) {
                if (noteViewModel.noteCount.value != null && noteViewModel.noteCount.value!! > 0) {
                    binding?.emptyContainer?.visibility = View.VISIBLE
                }
                binding?.content?.visibility = View.GONE
            }
        }
    }

    private fun removeEditFragmentIfNeed() {
        // 非父子下不需要详情页
        val isTwoPane = resources.getBoolean(R.bool.is_two_panel)
        if (!isTwoPane) {
            removeFragment(if (isCoverPaint) getNoteViewCoverPaintEditFragment() else getNoteViewEditFragment())
        }
    }

    fun removeCoverFragment() {
        childFragmentManager.commit {
            val fragment = childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG)
            if (fragment is WVNoteViewEditFragmentCoverPaint) {
                AppLogger.BASIC.d(TAG, "remove fragment, noteCount=${noteViewModel.noteCount.value}")
                remove(fragment)
                binding?.content?.visibility = View.GONE
                if (sharedViewModel.isSearch.value == true) {
                    binding?.emptyContainer?.visibility = View.VISIBLE
                } else if (noteViewModel.noteCount.value != null) {
                    if (noteViewModel.noteCount.value!! == 0) {
                        binding?.emptyContainer?.visibility = View.VISIBLE
                    } else {
                        binding?.emptyContainer?.visibility = View.GONE
                    }
                }
            }
        }
    }

    private fun removeFragment(fragment: Fragment?) {
        childFragmentManager.commit {
           if(isCoverPaint){
               if (fragment is WVNoteViewEditFragmentCoverPaint) {
                   AppLogger.BASIC.d(TAG, "remove fragment, noteCount=${noteViewModel.noteCount.value}")
                   remove(fragment)
                   binding?.content?.visibility = View.GONE
                   if (sharedViewModel.isSearch.value == true) {
                       binding?.emptyContainer?.visibility = View.VISIBLE
                   } else if (noteViewModel.noteCount.value != null) {
                       if (noteViewModel.noteCount.value!! == 0) {
                           binding?.emptyContainer?.visibility = View.VISIBLE
                       } else {
                           binding?.emptyContainer?.visibility = View.GONE
                       }
                   }
               }
           }else{
               if (fragment is WVNoteViewEditFragment) {
                   AppLogger.BASIC.d(TAG, "remove fragment, noteCount=${noteViewModel.noteCount.value}")
                   remove(fragment)
                   binding?.content?.visibility = View.GONE
                   if (sharedViewModel.isSearch.value == true) {
                       binding?.emptyContainer?.visibility = View.VISIBLE
                   } else if (noteViewModel.noteCount.value != null) {
                       if (noteViewModel.noteCount.value!! == 0) {
                           binding?.emptyContainer?.visibility = View.VISIBLE
                       } else {
                           binding?.emptyContainer?.visibility = View.GONE
                       }
                   }
               }
           }
        }
    }

    fun getNoteViewEditFragment(): WVNoteViewEditFragment? {
        return if (isAdded) childFragmentManager.findFragmentByTag(WVNoteViewEditFragment.TAG) as? WVNoteViewEditFragment else null
    }
    fun getNoteViewCoverPaintEditFragment(): WVNoteViewEditFragmentCoverPaint? {
        return if (isAdded) childFragmentManager.findFragmentByTag(WVNoteViewEditFragmentCoverPaint.TAG) as? WVNoteViewEditFragmentCoverPaint else null
    }


    fun undoEvent() {
        getNoteViewEditFragment()?.undoEvent()
    }
}