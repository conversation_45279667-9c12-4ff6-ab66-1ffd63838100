/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - sf.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/11/24
 ** Author: W9039628
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ** ------------------------------------------------------------
 **  W9039628      2023/11/24      1.0     create Notes2
 ****************************************************************/
package com.nearme.note.main

import android.view.View
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.nearme.note.activity.list.TodoAdapter
import com.nearme.note.activity.richlist.RichNoteListAdapter
import com.nearme.note.view.scalebehavior.BaseTitleBehavior
import com.nearme.note.view.scalebehavior.PrimaryTitleBehavior
import com.oplus.cloudkit.view.HeadTipsBaseController
import com.oplus.note.R
import com.oplus.note.logger.AppLogger

/**
 * 提取公共方法，处理副标题高度变化
 */
class NoteSubTitleViewHelper {
    companion object {
        private const val TAG = "NoteSubTitleViewHelper"
        private const val WAITTIME_DURATION = 0L
    }

    /**
     * subTitleView 副标题 view
     * adapter 适配器
     * behavior
     * refreshEnable 下拉刷新是否被禁用
     * hasList 是否有列表数据
     * mPlaceHolderViewHeight   PlaceHolderView 高度
     */
    fun updateSubtitleViewHeight(
        subTitleView: View?,
        adapter: RecyclerView.Adapter<RecyclerView.ViewHolder>,
        behavior: PrimaryTitleBehavior?,
        refreshEnable: Boolean,
        hasList: Boolean,
        mPlaceHolderViewHeight: Int,
        isCloudSyncing: Boolean
    ) {
        subTitleView?.postDelayed(Runnable {
            var mIsShowHeader = true
            var type = 0
            if (adapter is RichNoteListAdapter) {
                mIsShowHeader = adapter.mIsShowHeader == HeadTipsBaseController.CLOUD_TIP_HEADER_STATUS_SHOW
                type = 0
            }
            if (adapter is TodoAdapter) {
                mIsShowHeader = adapter.isShowHeader
                type = 1
            }
            subTitleView.let {
                val subtitleHeight = it.context.resources.getDimensionPixelOffset(R.dimen.note_subtitle_height)
                var heightSubTitle = 0
                var heightPlaceHolderView = 0
                if (mIsShowHeader) {
                    //tip show 顶部提示出现
                    if (hasList || isCloudSyncing) {
                        // 有待办状态
                        heightSubTitle = subtitleHeight
                    } else {
                        heightSubTitle = 0
                    }
                } else {
                    // tip hide
                    if (!refreshEnable) {
                        //下拉被禁止，说明被 忽略了
                        if (hasList || isCloudSyncing) {
                            // 有笔记状态
                            heightSubTitle = subtitleHeight
                        } else {
                            heightSubTitle = 0
                        }
                    } else {
                        //可以下拉，说明 开启了云同步  ，此时可以不做任何操作  保持最初状态
                        if (isCloudSyncing || hasList) {
                            heightSubTitle = subtitleHeight
                        } else {
                            heightSubTitle = 0
                        }
                    }
                }

                if (behavior?.lastCurrentRatio == BaseTitleBehavior.RATIO_ZERO && heightSubTitle == 0) {
                    //appbar 标题和副标题完全展开
                    AppLogger.BASIC.d(TAG, "fix subTitle height when appbar unfold")
                    heightSubTitle = subtitleHeight
                } else if (behavior?.lastCurrentRatio == BaseTitleBehavior.RATIO_ONE && heightSubTitle == subtitleHeight) {
                    //appbar 标题和副标题收起
                    AppLogger.BASIC.d(TAG, "fix subTitle height when appbar fold")
                    heightSubTitle = 0
                }

                //更新副标题高度
                it.updateLayoutParams {
                    height = heightSubTitle
                }
                AppLogger.BASIC.d(TAG, "updateLayoutParams$type  refreshEnable = $refreshEnable " +
                        "heightSubTitle=$heightSubTitle, hasList=$hasList, isSyncing=$isCloudSyncing")
                // 在 heightSubTitle == subheight ，代表此时显示副标题 ，反之是隐藏副标题（高度为O）
                val subTitleChangeHeight = if (heightSubTitle == subtitleHeight) {
                    //此时补偿值
                    0
                } else {
                    -1 * subtitleHeight
                }
                AppLogger.BASIC.d(TAG, "updateLayoutParams subTitleChangeHeight=$subTitleChangeHeight")
                behavior?.setSubTitleChangeHeight(subTitleChangeHeight, heightSubTitle)
            }
        }, WAITTIME_DURATION)
    }
}