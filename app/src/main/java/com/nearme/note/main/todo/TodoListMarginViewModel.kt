package com.nearme.note.main.todo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.oplus.note.R

class TodoListMarginViewModel(application: Application) : AndroidViewModel(application) {
    private var contentMarginTop: LiveData<Int>? = null
    private var inMultiWindowBottomOrZoomWindow = MutableLiveData(false)
    private var scrollPaddingTop: LiveData<Int>? = null
    private val scrollPaddingBottom = MutableLiveData<Int>()

    init {
        contentMarginTop = inMultiWindowBottomOrZoomWindow.map { inMultiWindowBottom ->
            if (inMultiWindowBottom) {
                (application.resources.getDimensionPixelOffset(R.dimen.toolbar_height)
                        + application.resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_height))
            } else {
                (/*WindowInsetsUtil.getStatusBarHeight(application)
                        +*/ application.resources.getDimensionPixelOffset(R.dimen.toolbar_height)
                        + application.resources.getDimensionPixelOffset(R.dimen.toolbar_title_init_height))
            }
        }

        scrollPaddingTop = contentMarginTop!!.map { contentMarginTop: Int ->
            contentMarginTop + application.resources.getDimensionPixelSize(R.dimen.list_to_ex_top_padding)
        }

        scrollPaddingBottom.setValue(application.resources.getDimensionPixelSize(R.dimen.note_edit_mode_padding_bottom))
    }

    fun getScrollPaddingTop(): LiveData<Int>? {
        return scrollPaddingTop
    }

    fun getScrollPaddingBottom(): MutableLiveData<Int> {
        return scrollPaddingBottom
    }

    fun notifyInMultiWindowBottomOrZoomWindow(inMultiWindowBottom: Boolean) {
        inMultiWindowBottomOrZoomWindow.value = inMultiWindowBottom
    }
}