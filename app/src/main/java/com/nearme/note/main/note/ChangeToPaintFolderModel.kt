/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - ChangeToPaintFolderModel.kt
** Description:
** Version: 1.0
** Date : 2024/11/19
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2024/11/19      1.0     create file
****************************************************************/
package com.nearme.note.main.note

import com.oplus.note.data.SingleLiveEventData
import com.oplus.note.repo.note.entity.Folder

class ChangeToPaintFolderModel(
    val noteLocalId: String?,
    val folder: Folder,
    val taskId: Int,
    val activityHashCode: Int,
    val lastPaintNoteCount: Int
) {
    companion object {
        val noteChangeToPaintFolder = SingleLiveEventData<ChangeToPaintFolderModel>()
    }

    fun isObserveNoteRealChange(nowSize: Int?, currentFolder: Folder?): Boolean {
        return lastPaintNoteCount + 1 == nowSize && currentFolder?.guid == folder.guid
    }
}