/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SearchViewAnimatorHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2021/06/07
 * * Author: PengFei.Ma
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * PengFei.Ma   2021/06/07      1.0    build this module
 ****************************************************************/
@file:Suppress("DEPRECATION")

package com.nearme.note.main.note

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Color
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewStub
import android.view.animation.Interpolator
import android.view.animation.PathInterpolator
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.forEach
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView.Adapter
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.coui.appcompat.searchview.COUISearchViewAnimate
import com.coui.appcompat.toolbar.COUIToolbar
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.WindowInsetsUtil
import com.nearme.note.view.HeightView
import com.nearme.note.view.PaddingTopView
import com.nearme.note.view.TopMarginView
import com.nearme.note.view.scalebehavior.PrimaryTitleBehavior
import com.oplus.anim.EffectiveAnimationView
import com.oplus.note.R
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger

class SearchViewAnimatorHelper {
    companion object {
        private const val TITLE_FADE_DURATION = 60L
        private const val FADE_DURATION = 150L
        private const val TRANS_DURATION = 250L
    }

    private var contentView: View? = null
    private var maskContainerPaddingTopView: PaddingTopView? = null
    private var titleTopMarginView: TopMarginView? = null
    private var headerHeightView: HeightView? = null
    private var maskAlphaEnterAnimator: ObjectAnimator? = null
    private var headerHeightEnterAnimator: ObjectAnimator? = null
    private var headerHeightExitAnimator: ObjectAnimator? = null
    private var maskAlphaExitAnimator: ObjectAnimator? = null
    private var titleTopMarginEnterAnimator: ObjectAnimator? = null
    private var titleTopMarginExitAnimator: ObjectAnimator? = null
    private var maskPaddingTopEnterAnimator: ObjectAnimator? = null
    private var maskPaddingTopExitAnimator: ObjectAnimator? = null
    private var mainTitleAlphaEnterAnimator: ObjectAnimator? = null
    private var mainTitleAlphaExitAnimator: ObjectAnimator? = null
    private var subTitleAlphaEnterAnimator: ObjectAnimator? = null
    private var subTitleAlphaExitAnimator: ObjectAnimator? = null
    private var listScrollAnimator: ValueAnimator? = null
    private var animatorEnterSet: AnimatorSet? = null
    private var animatorExitSet: AnimatorSet? = null
    private var cubicBezierEnterInterpolator: Interpolator? = null
    private var cubicBezierExitInterpolator: Interpolator? = null
    private var tempScrollOffset = 0
    private var titleLayout: View? = null
    private var mainTitleLayout: View? = null
    private var subTitleView: View? = null
    private var notePlaceHolderView: View? = null
    var backgroundMask: View? = null
    var resultList: COUIRecyclerView? = null
    var emptyContainer: View? = null
    var adapter: Adapter<ViewHolder>? = null
    var layoutManager: LayoutManager? = null
    var noSearchResultLottie: EffectiveAnimationView? = null
    var resultContainer: View? = null
    private var resultRecyclerView: COUIRecyclerView? = null
    private var behavior: PrimaryTitleBehavior? = null
    private var recyclerView: COUIRecyclerView? = null
    private var searchView: COUISearchViewAnimate? = null
    private var toolbar: COUIToolbar? = null
    private var staggeredGridLayoutManager: StaggeredGridLayoutManager? = null

    private var statusBarHeight = 0
    private var notePlaceHolderViewHeight = 0
    private var onClickListener: OnClickListener? = null
    private var imageHelper: ImageHelper? = null
    var initFinished = false
    private var calculateFinishCallBack: (from: String) -> Unit = {
        initFinished = true
        emptyContainer?.let {
            if (it.visibility == View.INVISIBLE) {
                it.visibility = View.VISIBLE
            }
        }
    }

    fun setSearchView(searchView: COUISearchViewAnimate?) {
        this.searchView = searchView
    }

    fun setImageHelper(imageHelper: ImageHelper?) {
        this.imageHelper = imageHelper
    }

    fun setOnClickListener(onClickListener: OnClickListener) {
        this.onClickListener = onClickListener
    }

    fun setNotePlaceHolderViewHeight(height: Int) {
        notePlaceHolderViewHeight = height
    }

    fun initViewsAndAnimators(
        rootView: View?,
        viewStub: ViewStub?,
        placeHolderView: View?,
        behavior: PrimaryTitleBehavior?,
        staggeredGridLayoutManager: StaggeredGridLayoutManager?,
    ) {
        if (contentView != null || rootView == null) return
        contentView = runCatching { viewStub?.inflate() }.getOrNull() ?: return
        contentView = rootView
        statusBarHeight = WindowInsetsUtil.getStatusBarHeight(rootView.context)
        this.behavior = behavior
        this.notePlaceHolderView = placeHolderView
        this.staggeredGridLayoutManager = staggeredGridLayoutManager
        titleLayout = rootView.findViewById(R.id.title_container)
        mainTitleLayout = rootView.findViewById(R.id.main_title_container)
        subTitleView = rootView.findViewById(R.id.sub_title_view)
        toolbar = rootView.findViewById(R.id.toolbar)
        resultContainer = rootView.findViewById(R.id.resultContainer)
        resultContainer?.updatePadding(top = notePlaceHolderViewHeight)
        backgroundMask = rootView.findViewById(R.id.background_mask)
        backgroundMask?.setOnClickListener(onClickListener)
        resultList = rootView.findViewById(R.id.resultList)
        resultList?.let {
            it.adapter = adapter
            it.layoutManager = layoutManager
        }
        emptyContainer = rootView.findViewById(R.id.emptyContainer)
        recyclerView = rootView.findViewById(R.id.note_list)
        noSearchResultLottie = rootView.findViewById(R.id.no_search_result_lottie)
        noSearchResultLottie?.alpha = ImageHelper.LOTTIE_ALPHA
        initImageHelper(imageHelper)
        imageHelper?.calculateFinishCallBack = calculateFinishCallBack
        initAnimators()
        initWindowInsets()
    }

    private fun initWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(resultList) { v, insets ->
            AppLogger.BASIC.d("SearchViewAnimatorHelper", "initWindowInsets")
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updatePadding(bottom = systemBarInsets.bottom)
        }
    }

    private fun initImageHelper(imageHelper: ImageHelper?) {
        val content = emptyContainer ?: return
        val root = emptyContainer?.parent as? View ?: return
        val lottie = noSearchResultLottie ?: return
        imageHelper?.init(root, content, lottie)
    }

    private fun initAnimators() {
        initiateInterpolator()
        initiateSearchAnimatedViews()
        initiateSearchInAnimator()
        initiateSearchOutAnimator()
    }

    private fun initiateInterpolator() {
        cubicBezierEnterInterpolator = PathInterpolator(0.3F, 0F, 0.1F, 1F)
        cubicBezierExitInterpolator = PathInterpolator(0.3F, 0f, 0.9F, 1f)
    }

    private fun initiateSearchAnimatedViews() {
        titleTopMarginView = TopMarginView().apply {
            addView(titleLayout)
        }
        headerHeightView = HeightView(notePlaceHolderView)
        maskContainerPaddingTopView = PaddingTopView(resultContainer)
    }

    private fun initiateSearchInAnimator() {
        headerHeightEnterAnimator = ObjectAnimator().apply {
            target = headerHeightView
            setPropertyName("height")
            duration = TRANS_DURATION
        }
        titleTopMarginEnterAnimator = ObjectAnimator().apply {
            target = titleTopMarginView
            setPropertyName("topMargin")
            duration = TRANS_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    titleLayout?.visibility = View.GONE
                }
            })
        }
        maskPaddingTopEnterAnimator = ObjectAnimator().apply {
            setPropertyName("paddingTop")
            target = maskContainerPaddingTopView
            duration = TRANS_DURATION
        }
        maskAlphaEnterAnimator = ObjectAnimator.ofFloat(backgroundMask!!, "alpha", 0f, 1f).apply {
            duration = FADE_DURATION
            addUpdateListener {
                backgroundMask?.visibility = View.VISIBLE
            }
        }
        mainTitleAlphaEnterAnimator = ObjectAnimator().apply {
            target = mainTitleLayout
            setPropertyName("alpha")
            interpolator = cubicBezierExitInterpolator
            duration = TITLE_FADE_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    resultContainer?.visibility = View.VISIBLE
                    emptyContainer?.visibility = View.GONE
                    resultList?.visibility = View.GONE
                    backgroundMask?.visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    val hasPrimaryTitle = behavior?.hasPrimaryTitle() ?: true
                    if (!hasPrimaryTitle) {
                        titleLayout?.visibility = View.GONE
                    }
                }
            })
        }
        subTitleAlphaEnterAnimator = ObjectAnimator().apply {
            target = subTitleView
            setPropertyName("alpha")
            interpolator = cubicBezierExitInterpolator
            duration = TITLE_FADE_DURATION
        }
        animatorEnterSet = AnimatorSet().apply {
            playTogether(headerHeightEnterAnimator, titleTopMarginEnterAnimator, maskPaddingTopEnterAnimator, maskAlphaEnterAnimator)
            interpolator = cubicBezierEnterInterpolator
            duration = TRANS_DURATION
        }
    }

    private fun initiateSearchOutAnimator() {
        headerHeightExitAnimator = ObjectAnimator().apply {
            target = headerHeightView
            setPropertyName("height")
            duration = TRANS_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    recyclerView?.post {
                        behavior?.setScaleEnable(true)
                        titleLayout?.visibility = View.VISIBLE
                    }
                }
            })
        }
        titleTopMarginExitAnimator = ObjectAnimator().apply {
            target = titleTopMarginView
            setPropertyName("topMargin")
            duration = TRANS_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    titleLayout?.visibility = View.VISIBLE
                }
            })
        }
        maskPaddingTopExitAnimator = ObjectAnimator().apply {
            target = maskContainerPaddingTopView
            setPropertyName("paddingTop")
            duration = TRANS_DURATION
        }
        listScrollAnimator = ValueAnimator().apply {
            duration = TRANS_DURATION
            interpolator = cubicBezierExitInterpolator
            addUpdateListener { valueAnimator ->
                recyclerView?.scrollBy(0, valueAnimator.animatedValue as Int - tempScrollOffset)
                tempScrollOffset = valueAnimator.animatedValue as Int
            }
        }
        maskAlphaExitAnimator = ObjectAnimator.ofFloat(backgroundMask!!, "alpha", 1f, 0f).apply {
            duration = FADE_DURATION
            interpolator = cubicBezierExitInterpolator
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    resultContainer?.visibility = View.VISIBLE
                    emptyContainer?.visibility = View.GONE
                    resultList?.visibility = View.GONE
                }

                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    if (maskPaddingTopExitAnimator?.isRunning == false) {
                        val paddingTop: Int = notePlaceHolderViewHeight
                        maskContainerPaddingTopView?.paddingTop = paddingTop
                    }
                    resultContainer?.visibility = View.GONE
                    backgroundMask?.visibility = View.GONE
                }
            })
        }

        mainTitleAlphaExitAnimator = ObjectAnimator().apply {
            target = mainTitleLayout
            setPropertyName("alpha")
            duration = TITLE_FADE_DURATION
            startDelay = TITLE_FADE_DURATION
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    super.onAnimationStart(animation)
                    titleLayout?.visibility = View.VISIBLE
                }
            })
        }

        subTitleAlphaExitAnimator = ObjectAnimator().apply {
            target = subTitleView
            setPropertyName("alpha")
            duration = TITLE_FADE_DURATION
            startDelay = TITLE_FADE_DURATION
        }

        animatorExitSet = AnimatorSet().apply {
            playTogether(headerHeightExitAnimator, maskPaddingTopExitAnimator, titleTopMarginExitAnimator)
            interpolator = cubicBezierExitInterpolator
            duration = TRANS_DURATION
        }
    }

    fun animateSearchIn(onlyMaskAnim: Boolean) {
        if (searchView == null) {
            return
        }
        resultRecyclerView?.scrollToPosition(0)
        tempScrollOffset = 0
        behavior?.setScaleEnable(false)
        val paddingTop: Int = (toolbar?.height ?: 0)
        if (onlyMaskAnim) {
            maskContainerPaddingTopView?.paddingTop = paddingTop
            maskAlphaEnterAnimator?.start()
            mainTitleAlphaEnterAnimator?.apply {
                setFloatValues(mainTitleLayout?.alpha ?: 1F, 0F)
                start()
            }
        } else {
            val titleHeight = titleLayout?.height ?: 0
            val placeholderHeight = notePlaceHolderView?.height ?: 0
            headerHeightEnterAnimator?.setIntValues(placeholderHeight, placeholderHeight - titleHeight)
            titleTopMarginEnterAnimator?.setIntValues(TopMarginView.getViewTopMargin(titleLayout), -titleHeight)
            maskPaddingTopEnterAnimator?.setIntValues((resultContainer?.paddingTop ?: 0), paddingTop)
            subTitleAlphaEnterAnimator?.setFloatValues(subTitleView?.alpha ?: 1F, 0F)
            mainTitleAlphaEnterAnimator?.setFloatValues(mainTitleLayout?.alpha ?: 1F, 0F)
            subTitleAlphaEnterAnimator?.start()
            mainTitleAlphaEnterAnimator?.start()
            animatorEnterSet?.start()
        }
        searchView?.showInToolBar()
    }

    private fun hideSearchView() {
        toolbar?.forEach {
            it.visibility = View.VISIBLE
            it.alpha = 1F
        }
        searchView?.visibility = View.GONE
    }

    fun animateSearchOut(onlyMaskAnim: Boolean, twoPanel: Boolean = false) {
        tempScrollOffset = 0
        if (twoPanel) {
            hideSearchView()
        } else {
            searchView?.hideInToolBar()
        }
        searchView?.searchView?.setQuery("", false)
        toolbar?.setTitleTextColor(Color.argb(0, 0, 0, 0))
        mainTitleAlphaExitAnimator?.setFloatValues(mainTitleLayout?.alpha ?: 0F, 1f)
        if (!onlyMaskAnim) {
            val placeholderHeight = notePlaceHolderView?.height ?: 0
            headerHeightExitAnimator?.setIntValues(placeholderHeight, notePlaceHolderViewHeight)
            titleTopMarginExitAnimator?.setIntValues(TopMarginView.getViewTopMargin(titleLayout), 0)
            maskPaddingTopExitAnimator?.setIntValues(resultContainer?.paddingTop ?: 0, notePlaceHolderViewHeight)
            subTitleAlphaExitAnimator?.setFloatValues(subTitleView?.alpha ?: 0F, 1F)
            if ((recyclerView?.getChildAt(0)?.top ?: 0) < 0) {
                listScrollAnimator?.setIntValues(0, -calculateTotalScrollOffset())
                listScrollAnimator?.start()
            }
            subTitleAlphaExitAnimator?.start()
            mainTitleAlphaExitAnimator?.start()
            animatorExitSet?.start()
        } else {
            behavior?.setScaleEnable(true)
            mainTitleAlphaExitAnimator?.start()
        }
        if (resultContainer?.visibility == View.VISIBLE) {
            maskAlphaExitAnimator?.start()
        } else {
            resultContainer?.visibility = View.GONE
            backgroundMask?.visibility = View.GONE
            backgroundMask?.alpha = 0f
        }
        if (null != recyclerView) {
            recyclerView?.visibility = View.VISIBLE
            behavior?.setOnScrollListener(recyclerView)
        }
        val scrollAnimator = ObjectAnimator.ofFloat(0F, 1F).apply {
            duration = TRANS_DURATION
            addUpdateListener {
                behavior?.onListScroll()
            }
        }
        scrollAnimator.start()
    }

    fun inSearchMode(): Boolean {
        return titleLayout?.visibility == View.GONE
    }

    fun isAnimationRunning(): Boolean {
        if (headerHeightEnterAnimator?.isRunning == true || headerHeightExitAnimator?.isRunning == true) {
            return true
        }
        if (maskAlphaEnterAnimator?.isRunning == true || maskAlphaExitAnimator?.isRunning == true) {
            return true
        }
        return false
    }

    private fun calculateTotalScrollOffset(): Int {
        val firstVisibileView: View? = recyclerView?.getChildAt(0)
        val firstVisibilePosition: Int = staggeredGridLayoutManager?.findFirstVisibleItemPositions(null)?.get(0) ?: 0
        var distance = 0
        var position = 0
        while (true) {
            distance += if (position >= firstVisibilePosition) {
                break
            } else {
                getItemHeight(position)
            }
            position++
        }
        if (firstVisibileView != null) {
            distance -= firstVisibileView.top
        }
        return distance
    }

    private fun getItemHeight(position: Int): Int {
        val itemView: View? = staggeredGridLayoutManager?.findViewByPosition(position)
        return if (itemView != null) {
            val heightSpec: Int = if (position == 0) {
                View.MeasureSpec.makeMeasureSpec(notePlaceHolderViewHeight, View.MeasureSpec.EXACTLY)
            } else {
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            }
            val size = (recyclerView?.width ?: 0) - (recyclerView?.paddingStart ?: 0) - (recyclerView?.paddingEnd ?: 0)
            val widthSpec: Int = View.MeasureSpec.makeMeasureSpec(size, View.MeasureSpec.AT_MOST)
            itemView.measure(widthSpec, heightSpec)
            itemView.measuredHeight
        } else {
            0
        }
    }
}