/*************************************** ********************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteListSharedViewModel.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/5/26
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.main.note

import android.app.Application
import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.nearme.note.MyApplication
import com.nearme.note.activity.list.ToppedUtil
import com.nearme.note.activity.richlist.RichNoteGroupHelper
import com.nearme.note.activity.richlist.RichNoteItem
import com.nearme.note.activity.richlist.SpeechInfoHelper
import com.nearme.note.logic.SelectionManager
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.thirdlog.ThirdLogNoteManager
import com.nearme.note.util.AppExecutors
import com.nearme.note.util.LiveDataOperators
import com.nearme.note.util.SortRule
import com.nearme.note.util.SortRule.saveSortRule
import com.oplus.dmp.sdk.aiask.AIAskManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.search.NoteSearchResult
import com.oplus.note.search.NoteSearchResultItem
import com.oplus.note.utils.cloud.AppCloudConfigUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class NoteListViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "NoteListViewModel"
        val isShowPermissionsTips = MutableLiveData<Boolean>() //笔记列表是否展示了权限提示
    }

    private val richNoteRepo by lazy {
        RichNoteRepository
    }
    var isSearchButtonClicked: Boolean = false    // 表示是是否搜索长按点击过来
    var selectedNotes = MutableLiveData<Pair<Set<String>?, Boolean?>>()
    var selectedNotesData: Pair<Set<String>?, Boolean?>? = null
    var selectedFolder: FolderInfo? = null
    var isAllNoteSelected = MutableLiveData<Boolean>()
    var isDeletingOrRecovering = false
    var searchText = MutableLiveData<String>()
    var searchList: MutableList<String> = arrayListOf()
    var searchAttachmentId = ""
    var syncEnable = MutableLiveData<Boolean>()
    var refreshEnable: Boolean = false
    var completeRefreshWithTipsAndDelay = MutableLiveData<Pair<String?, Int?>>()
    var checkedGuid = ""
    var selectionManager = SelectionManager()

    var dialogType: Int = 0
    var dialogExtra: Bundle? = null
    var isCreateDialog = false
    var labelTypeList: MutableList<Int>? = null
    private val speechInsertingList = arrayListOf<String>()
    private var keyword: String = ""

    val currentFolder = MutableLiveData<FolderInfo>()
    val sortRule = MutableLiveData<Int>()
    val isGroupByPeople = MutableLiveData<Boolean>()
    val dataRefresh = MutableLiveData<Boolean>()
    var isChangeFolderFromChoiceFolder = false
    var defaultSelectedFromChoiceFolder = false
    var changeNoteListByInitOrChangeFolder = false
    var changeToFolderModel: ChangeToPaintFolderModel? = null
    private var isAiAskRealReWrite = false
    var isSupportAiAsk: Boolean = false
        set(value) {
            field = value
            isAiAskRealReWrite = true
        }
        get() = if (isAiAskRealReWrite) {
            field
        } else {
            false
        }
    private var observerSearchList: Observer<MutableList<RichNoteWithAttachments>>? = null
    var searchNoteList: LiveData<MutableList<RichNoteWithAttachments>>? = null

    init {
        initFolderAndSortRule()
        initAiAskIsSupport()
    }

    /**
     * AIASK是否支持
     */
    private fun initAiAskIsSupport() {
        AppExecutors.getInstance().submitInDiskIO<Unit> {
            runBlocking {
                val isSupport = when (AIAskManager.getInstance().getAIAskState(getApplication())) {
                    com.oplus.dmp.sdk.aiask.NotSupportState -> {
                        AppLogger.BASIC.d(TAG, "initAiAskIsSupport is NotSupport")
                        false
                    }

                    else -> {
                        AppLogger.BASIC.d(TAG, "initAiAskIsSupport is Support")
                        true
                    }
                }
                isSupportAiAsk = isAiAskSupportWithCloud(isSupport)
            }
        }
    }

    private fun isAiAskSupportWithCloud(supportSelf: Boolean): Boolean {
        val aiAskEnable = AppCloudConfigUtil.getDmpAiAskEnableFromCloud(MyApplication.appContext)
        val list = AppCloudConfigUtil.getDmpCloudConfig(MyApplication.appContext).aiAskDisableVersions
        var aiAskVersion = 0L
        if (aiAskEnable) {
            aiAskVersion = AIAskManager.getInstance().getAIAskVersion()
        }
        val disableByCloud = list.contains(aiAskVersion)

        AppLogger.THIRDLOG.d(
            TAG, "52020301,aiAskVersion:$aiAskVersion,isAIAskSupportSelf:$supportSelf," +
                    "isAiAskSupportCloud:$aiAskEnable,disableVersionByCloud:$disableByCloud,disableList:$list"
        )

        val support = supportSelf && aiAskEnable && !disableByCloud
        AppLogger.BASIC.d(TAG, "isAiAskSupportWithCloud support=$support")
        return support
    }

    private fun initFolderAndSortRule() {
        viewModelScope.launch(Dispatchers.IO) {
            val rule = SortRule.readSortRule()
            sortRule.postValue(rule)
        }
    }

    val richNoteItemList = LiveDataOperators.combineLatest(currentFolder, sortRule, isGroupByPeople, dataRefresh) { folder, rule, isGroup ->
        saveSortRule(rule)
        Triple(folder, rule, isGroup)
    }.switchMap { triple ->
        val realFolderGuid = triple.first?.guid ?: FolderFactory.getUncategorizedFolderGuid()
        val realRule = triple.second ?: SortRule.readSortRule()
        val realGroup = triple.third ?: SortRule.isGroupByPeople()
        val tag = if (realGroup) RichNoteItem.TAG.GROUP_BY_PEOPLE else RichNoteItem.TAG.NORMAL
        val findFlow = when {
            FolderFactory.isUncategorizedFolder(realFolderGuid) -> richNoteRepo.findUncategorized(realRule)
            FolderFactory.isSummaryFolder(realFolderGuid) -> richNoteRepo.findAllFormGroupPeople(realFolderGuid, realRule, realGroup, labelTypeList)
            FolderFactory.isRecentDeleteFolder(realFolderGuid) -> richNoteRepo.findRecentDeleted(realRule)
            FolderFactory.isAllNotesFolder(realFolderGuid) -> richNoteRepo.findAll(realRule)
            else -> richNoteRepo.findByFolder(realFolderGuid, realRule)
        }.map { list ->
            val topExpand = !ToppedUtil.getToppedSharedPreferences(application, realFolderGuid)
            list.mapIndexed { index, richNoteWithAttachments ->
                transform(list, index, richNoteWithAttachments, realFolderGuid, tag, topExpand)
            }
        }

        return@switchMap findFlow
    }

    private fun transform(
        list: List<RichNoteWithAttachments>,
        index: Int,
        note: RichNoteWithAttachments,
        realFolderGuid: String,
        tag: RichNoteItem.TAG,
        topExpand: Boolean
    ): RichNoteItem {
        val type = if (SpeechInfoHelper.isNotRichNote(note)) {
            RichNoteItem.TYPE_NOTE_SPEECH_NAME
        } else {
            RichNoteItem.TYPE_NOTE
        }
        val isGroupByPeople = tag == RichNoteItem.TAG.GROUP_BY_PEOPLE
        val isRecentDelete = FolderFactory.isRecentDeleteFolder(realFolderGuid)
        val isToppedPosition = note.richNote.topTime > 0
        val isSummary = FolderFactory.isSummaryFolder(realFolderGuid)

        val isGroupStart = if (isRecentDelete) {
            index == 0
        } else if (isGroupByPeople && isSummary) {
            RichNoteGroupHelper.isGroupStart(list, index)
        } else if (isToppedPosition) {
            RichNoteGroupHelper.isToppedStart(list, index)
        } else {
            RichNoteGroupHelper.isGroupOtherStart(list, index)
        }
        val isGroupEnd = if (isRecentDelete) {
            index == list.size - 1
        } else if (isGroupByPeople && isSummary) {
            RichNoteGroupHelper.isGroupEnd(list, index)
        } else if (isToppedPosition) {
            RichNoteGroupHelper.isToppedEnd(list, index, topExpand)
        } else {
            RichNoteGroupHelper.isGroupOtherEnd(list, index)
        }

        val bgPositionType = if (isGroupStart && isGroupEnd) {
            RichNoteItem.TYPE_BG_ROUND
        } else if (isGroupStart) {
            RichNoteItem.TYPE_BG_HEAD
        } else if (isGroupEnd) {
            RichNoteItem.TYPE_BG_TAIL
        } else {
            RichNoteItem.TYPE_BG_MIDDLE
        }
        val isFolderSameToCurrent = if (FolderFactory.isAllNotesFolder(realFolderGuid) || FolderFactory.isRecentDeleteFolder(realFolderGuid)) {
            FolderFactory.isUncategorizedFolder(note.richNote.folderGuid)
        } else {
            realFolderGuid == note.richNote.folderGuid
        }
        return RichNoteItem(type, note, tag, bgPositionType = bgPositionType, isFolderSameToCurrent = isFolderSameToCurrent)
    }

    val noteItemSearchList = MutableLiveData<MutableList<NoteSearchResultItem>>()
    fun search(key: String) {
        searchInternal(viewModelScope, key) {
            val result = it
            if (searchText.value == result.first) {
                //最底层searchList数组是同一个对象
                searchList = result.second.searchList.toMutableList()
                noteItemSearchList.value = result.second.searchResult
                AppLogger.BASIC.d(TAG, "searchInternal current searchText(${searchText.value}) == result.searchText(${result.first})")
            } else {
                AppLogger.BASIC.d(TAG, "searchInternal current searchText(${searchText.value}) != result.searchText(${result.first})")
            }
        }
    }

    /**
     * NoteListFragment退出搜索页调用此方法
     */
    fun onExitSearchMode() {
        selectedNotesData = null
        selectedFolder = null
    }

    private fun searchInternal(scope: CoroutineScope, key: String, callback: (Pair<String, NoteSearchResult>) -> Unit) {
        keyword = key
        var query = key.trim()
        if (query.isEmpty()) {
            query = key
        }
        NoteSearchManager.search(scope, MyApplication.appContext, query, currentFolder.value?.toFolder()) {
            scope.launch(Main) {
                callback.invoke(Pair(key, it))
            }
        }
    }

    fun search(isInSearch: Boolean?) {
        if (isInSearch == true) {
            search(keyword)
        } else {
            AppLogger.BASIC.d(TAG, "is not in search")
        }
    }

    fun downloadSkin() {
        if (SkinManager.getSkinDownloadingList().isNotEmpty()) {
            return
        }
        val noteItems = richNoteItemList.value
        if (noteItems == null || noteItems.isEmpty()) {
            return
        }
        val set = mutableSetOf<String>()
        for (noteItem in noteItems) {
            val skinId = noteItem.data?.richNote?.skinId ?: ""
            if (!TextUtils.isEmpty(skinId) && !SkinManager.isEmbedSkin(skinId)) {
                set.add(skinId)
            }
        }
        if (set.isEmpty()) {
            return
        }
        AppExecutors.getInstance().executeCommandInDiskIO {
            for (skinId in set) {
                AppLogger.BASIC.d(TAG, "downSkin $skinId")
                SkinManager.downSkin(skinId)
            }
        }
    }

    /**
     * 判断是否有通话摘要正在生成
     * 由于生成过程中数据库会频繁的写入，导致数据多次的回调，需要屏蔽一下，不然ui会因刷新的过于频繁而闪烁
     */
    fun getSpeechInsertingList(): MutableList<String> {
        val modelInsertingList =
            arrayListOf<String>().apply { addAll(ThirdLogNoteManager.getInstance().generatingIds) }
        val uiInsertingList = speechInsertingList
        val result = arrayListOf<String>()
        if (modelInsertingList.isEmpty() && uiInsertingList.isEmpty()) {
            return result
        }
        for (i in modelInsertingList.indices) {
            val id = modelInsertingList[i]
            if (uiInsertingList.contains(id)) {
                AppLogger.BASIC.d(TAG, "$id result is insert")
                result.add(id)
            } else {
                AppLogger.BASIC.d(TAG, "$id is insert")
                uiInsertingList.add(id)
            }
        }
        if (uiInsertingList.isNotEmpty()) {
            for (i in uiInsertingList.size - 1 downTo 0) {
                val id = uiInsertingList[i]
                if (!modelInsertingList.contains(id)) {
                    AppLogger.BASIC.d(TAG, "$id is remove")
                    uiInsertingList.removeAt(i)
                }
            }
        }
        return result
    }

    fun observeAllList(owner: LifecycleOwner, callback: (MutableList<RichNoteWithAttachments>) -> Unit) {
        searchNoteList = richNoteRepo.findNoteListAll()
        val observer = Observer<MutableList<RichNoteWithAttachments>> {
            callback.invoke(it)
        }
        searchNoteList?.observe(owner, observer)
        observerSearchList = observer
    }

    fun removeObserveAllList() {
        observerSearchList?.let {
            searchNoteList?.removeObserver(it)
        }
        searchNoteList = null
        observerSearchList = null
    }

    fun haveNotes(scope: CoroutineScope, callback: (haveNotes: Boolean) -> Unit) {
        scope.launch(IO) {
            val folder = currentFolder.value
            val isEncryptedNotebook = folder?.encrypted == Folder.FOLDER_ENCRYPTED
            val folderId = folder?.guid ?: FolderFactory.FOLDER_GUID_ALL_NOTES
            val count = richNoteRepo.getSearchRichNoteCount(isEncryptedNotebook, folderId)
            withContext(Main) {
                callback.invoke(count > 0)
            }
        }
    }
}