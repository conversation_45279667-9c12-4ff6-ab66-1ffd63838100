/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : ViewStubCreateByAi.kt
 * Description    : ViewStubCreateByAi.kt
 * Version        : 1.0
 * Date           : 2024/9/12
 * Author         : W9013333
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * W9013333     2024/9/12        1.0           create
 */

package com.nearme.note.main.note

import android.view.ViewStub
import android.widget.TextView
import com.nearme.note.util.WaterMark
import com.nearme.note.view.NoAnimationLinearLayout
import com.oplus.note.R

class ViewStubCreateByAi {
    var createByAiLl: NoAnimationLinearLayout? = null
    var createByAiTv: TextView? = null
    fun inflate(viewStub: ViewStub?) {
        runCatching {
            if (createByAiLl != null) return
            val view = viewStub?.inflate() ?: return
            createByAiLl = view.findViewById(R.id.create_by_ai_ll)
            createByAiTv = view.findViewById(R.id.create_by_ai_tv)
            createByAiTv?.let {
                it.text = WaterMark.getAIGCMarkTextWithHtmlTag()
            }
        }
    }
}