/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - MainFragment
 ** Description:
 **         v1.0:   MainFragment
 **
 ** Version: 1.0
 ** Date: 2024/05/28
 ** Author: <PERSON><PERSON><PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.Yan          2024/09/02   1.0      Create this module
 ********************************************************************************/
@file:Suppress("ForbiddenComment,FunctionOnlyReturningConstant")
package com.nearme.note.main

import android.animation.ObjectAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.RemoteException
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.annotation.MainThread
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.coui.component.responsiveui.window.WindowWidthSizeClass
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.NoteParentMaskHelper
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.main.note.NoteDetailFragment
import com.nearme.note.main.note.NoteListFragment
import com.nearme.note.main.todo.TodoFragment
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.view.helper.NavigationAnimatorHelper
import com.oplus.cloudkit.BaseSyncManager
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.JudgeGlobalStateCallback
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.note.R
import com.oplus.note.databinding.FragmentMainBinding
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.NotebookAgentFactory
import com.oplus.note.os.ResponsiveUiHelper
import com.oplus.note.osdk.proxy.OplusZoomWindowManagerProxy
import com.oplus.note.osdk.proxy.OplusZoomWindowObserverProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

private const val ARG_PARAM_TAB_POS = "init_tab_pos"

class MainFragment : BaseFragment() {
    private var initTabPosition = 0
    private var binding: FragmentMainBinding? = null
    private val fragmentList = SparseArray<Fragment>()
    private var currentTabIndex = 0
    private val sharedViewModel by viewModels<ActivitySharedViewModel> ({ requireActivity() })
    private val notebookAgent by NotebookAgentFactory.create(this)
    private var bottomMenuAnimatorHelper: NavigationAnimatorHelper? = null
    private var oplusZoomWindowManager: OplusZoomWindowManagerProxy? = null
    private var oplusZoomWindowServer: OplusZoomWindowObserverProxy? = null
    var onViewPagerSelectChange: ((pos: Int) -> Unit)? = null
    private var mMaskScreenHelper: NoteParentMaskHelper? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            initTabPosition = if (ConfigUtils.isToDoDeprecated) {
                NOTE_INDEX
            } else {
                it.getInt(ARG_PARAM_TAB_POS)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentMainBinding.inflate(inflater, container, false)
        binding?.lifecycleOwner = this
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initiateWindowInsets()
        registerZoomWindowObserver()
        initBottomMenu()
        initViewPager()
        initObservers()
        AppLogger.BASIC.d(TAG, "onViewCreated")
        activity?.let {
            sharedViewModel.fabAnimatorHelper?.updateFabMarginEnd(
                it, sharedViewModel.twoPane, currentTabIndex, sharedViewModel.inZoomWindowState, true
            )
        }
        mMaskScreenHelper = binding?.maskScreen?.viewStub?.let { NoteParentMaskHelper(it) }
    }

    fun onRestart() {
        if (fragmentList.size() > NOTE_INDEX &&
            fragmentList[NOTE_INDEX] is NoteListFragment &&
            fragmentList[NOTE_INDEX].isAdded) {
            (fragmentList[NOTE_INDEX] as NoteListFragment).onRestart()
        }
        if (fragmentList.size() > TODO_INDEX &&
            fragmentList[TODO_INDEX] is TodoFragment &&
            fragmentList[TODO_INDEX].isAdded) {
            (fragmentList[TODO_INDEX] as TodoFragment).refreshResumeCloud()
        }
    }

    fun updateView() {
        binding?.bottomMenuParent?.let {
            if (it.visibility == View.GONE) {
                it.visibility = View.VISIBLE
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        oplusZoomWindowServer?.let {
            oplusZoomWindowManager?.unregisterZoomWindowObserver(it)
        }
        oplusZoomWindowManager = null
        oplusZoomWindowServer = null
        syncInitReceiver?.let { LocalBroadcastManager.getInstance(MyApplication.appContext).unregisterReceiver(it) }
    }

    @Suppress("ForbiddenComment")
    private fun initViewPager() {
        val fragments = childFragmentManager.fragments
        AppLogger.BASIC.d(TAG, "initViewPager fragments=${fragments.size}")
        for (fragment in fragments) {
            if (fragment is NoteListFragment) {
                fragmentList.append(NOTE_INDEX, fragment)
            } else if (fragment is TodoFragment) {
                if (ConfigUtils.isToDoDeprecated) {
                    AppLogger.BASIC.i(TAG, "initViewPager error: Todo is deprecated. fragment is TodoFragment")
                } else {
                    fragmentList.append(TODO_INDEX, fragment)
                }
            }
        }

        if (fragmentList[NOTE_INDEX] == null) {
            fragmentList.append(NOTE_INDEX, NoteListFragment.newInstance())
        }

        if (fragmentList[TODO_INDEX] == null) {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "initViewPager error: Todo is deprecated. fragment is TodoFragment")
            } else {
                fragmentList.append(TODO_INDEX, TodoFragment())
            }
        }

        binding?.vpMain?.apply {
            offscreenPageLimit = 1
            (getChildAt(0) as? RecyclerView)?.overScrollMode = View.OVER_SCROLL_NEVER
        }
        activity?.let {
            (fragmentList[NOTE_INDEX] as? NoteListFragment)?.apply {
                twoPane = !ResponsiveUiHelper.isSmallScreen(it)
            }

            binding?.vpMain?.apply {
                adapter = object : FragmentStateAdapter(this@MainFragment) {
                    override fun getItemCount(): Int {
                        return fragmentList.size()
                    }

                    override fun createFragment(position: Int): Fragment {
                        return fragmentList[position]
                    }
                }

                currentTabIndex = initTabPosition
                sharedViewModel.currentTabIndex.value = initTabPosition
                AppLogger.BASIC.d(TAG, "initViewPager currentTabIndex=$currentTabIndex,currentItem=${this.currentItem}")
                setCurrentItem(initTabPosition, false)
                registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                    override fun onPageSelected(position: Int) {
                        super.onPageSelected(position)
                        AppLogger.BASIC.d(TAG, "onPageSelected currentTabIndex=$currentTabIndex,position=$position")
                        if (position != currentTabIndex) {
                            activity?.let {
                                sharedViewModel.fabAnimatorHelper?.systemBarsRight = (it as? MainActivity)?.getSuitableSystemBarsRight(position) ?: 0
                                sharedViewModel.fabAnimatorHelper?.systemBarsBottom = sharedViewModel.systemBarInsetBottom
                                sharedViewModel.fabAnimatorHelper?.startFabChangePageAnimation(
                                    it, position, sharedViewModel.twoPane, sharedViewModel.inZoomWindowState
                                )
                            }
                        }
                        currentTabIndex = position
                        sharedViewModel.currentTabIndex.value = position
                        arguments?.putInt(ARG_PARAM_TAB_POS, position)
                        binding?.bottomMenu?.selectedItemId = if (position == NOTE_INDEX) R.id.note_tab else R.id.todo_tab
                        onViewPagerSelectChange?.invoke(position)
                        if (activity is MainActivity) {
                            val noteDetailFragment = activity?.supportFragmentManager?.findFragmentByTag(NoteDetailFragment.TAG)
                            if (noteDetailFragment is NoteDetailFragment) {
                                if (noteDetailFragment.isCoverPaint) noteDetailFragment.getNoteViewCoverPaintEditFragment() else noteDetailFragment.getNoteViewEditFragment()
                                    ?.apply {
                                        postRichEditorViewHide(currentTabIndex == NOTE_INDEX)
                                        if (currentTabIndex == TODO_INDEX) {
                                            enterViewMode()
                                            voiceDestroy()
                                            webSearchOperationController?.quitSearchMode()
                                            mCallContentTipsManager.dismissTips()
                                        } else if (currentTabIndex == NOTE_INDEX) {
                                            postRunnableCheckShowTips(WVNoteViewEditFragment.DELAY_TIME_MILLS_500)
                                        }
                                    }
                            }
                        }
                        (activity as? MainActivity)?.binding?.fab?.mainFloatingButton?.apply {
                            contentDescription = if (currentTabIndex == NOTE_INDEX) {
                                getString(R.string.memo_new_note)
                            } else {
                                getString(R.string.todo_create)
                            }
                        }
                        /**
                         * 切换TAB后触发一次云同步
                         */
                        CloudSyncTrigger.sendDataChangedBroadcast(it)
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        super.onPageScrollStateChanged(state)
                        sharedViewModel.viewPagerScrollStateIdle.value = state == ViewPager2.SCROLL_STATE_IDLE
                    }
                })
            }
        } ?: kotlin.run {
            AppLogger.BASIC.e(TAG, "activity is null")
        }
    }

    /**
     * 设置蒙层是否可见
     * */
    private fun setMaskScreenVisibility(v: Boolean) {
        AppLogger.BASIC.d(TAG, "setMaskScreenVisibility perform")
        mMaskScreenHelper?.apply {
            if (v) {
                val noteListFragment = childFragmentManager.fragments.firstOrNull { it is NoteListFragment } as? NoteListFragment

                mMaskScreenHelper?.setScrollChild(noteListFragment?.binding?.noteList, null)
                visibility = View.VISIBLE
                setOnClickListener {
                    AppLogger.BASIC.d(TAG, "setMaskScreenVisibility perform click")
                }
            } else {
                visibility = View.GONE
            }

            binding?.fragmentMainContent?.let { parent ->
                ObjectAnimator.ofFloat(
                    parent,
                    "alpha",
                    parent.alpha,
                    if (v) ALPHA_DISABLE else ALPHA_ENABLE
                ).apply {
                    duration = MAST_DURATION
                    interpolator = AccelerateDecelerateInterpolator()
                    start()
                }
            }
        }
    }
    private fun initObservers() {
        sharedViewModel.isKitPanelAlive.observe(viewLifecycleOwner) { isShowing ->
            AppLogger.BASIC.d(TAG, "initiateObservers: isKitPanelShowing $isShowing")
            setMaskScreenVisibility(isShowing)
        }
        sharedViewModel.noteSelectionMode.observe(viewLifecycleOwner) { isSelectionMode: Boolean ->
            if (sharedViewModel.isRecentDeleteFolder.value != true && sharedViewModel.currentTabIndex.value == NOTE_INDEX) {
                bottomMenuAnimation(isSelectionMode, true, sharedViewModel.noteSelectionModeChangeWithAnim)
            }
        }

        sharedViewModel.todoSelectionMode.observe(viewLifecycleOwner) { isSelectionMode: Boolean ->
            if (sharedViewModel.currentTabIndex.value == TODO_INDEX) {
                bottomMenuAnimation(isSelectionMode, false, true)
            }
        }

        sharedViewModel.isUserInputEnabled().observe(viewLifecycleOwner) { enable ->
            binding?.vpMain?.isUserInputEnabled = enable
            AppLogger.BASIC.d(TAG, "isUserInputEnabled:$enable twopanel:${sharedViewModel.twoPane}")
        }

        val action = fun() {
            if (sharedViewModel.currentTabIndex.value != NOTE_INDEX) {
                //启动便器首页当默认我待办页时，笔记/待办ViewPager需要支持左右滑动
                sharedViewModel.isUserInputEnabledWhenRecentDeleteFolder.value = true
                return
            }
            val isRecentDelete = sharedViewModel.isRecentDeleteFolder.value
            if (isRecentDelete == true) {
                val count = notebookAgent.findRecentFolderRichNoteCountInFolder()
                if (count == 0) {
                    if (ConfigUtils.isToDoDeprecated) {
                        AppLogger.BASIC.i(TAG, "initObservers error: Todo is deprecated. isRecentDelete count=0")
                    } else {
                        binding?.bottomMenuParent?.visibility = View.VISIBLE
                        binding?.bottomMenu?.visibility = View.VISIBLE
                    }
                    sharedViewModel.isUserInputEnabledWhenRecentDeleteFolder.value = true
                } else {
                    binding?.bottomMenu?.visibility = View.GONE
                    sharedViewModel.isUserInputEnabledWhenRecentDeleteFolder.value = false
                }
            } else {
                if (sharedViewModel.noteSelectionMode.value != true && sharedViewModel.isSearch.value != true) {
                    binding?.bottomMenu?.visibility = View.VISIBLE
                    sharedViewModel.isUserInputEnabledWhenRecentDeleteFolder.value = true
                    if (binding?.bottomMenuParent?.visibility == View.GONE) {
                        if (ConfigUtils.isToDoDeprecated) {
                            AppLogger.BASIC.i(TAG, "initObservers error: Todo is deprecated. bottomMenuParent?.visibility = View.GONE")
                            binding?.bottomMenuParent?.visibility = View.GONE
                        } else {
                            binding?.bottomMenuParent?.visibility = if (sharedViewModel.isSearch.value == true) View.GONE else View.VISIBLE
                        }
                    }
                }
            }
        }
        sharedViewModel.isRecentDeleteFolder.observe(viewLifecycleOwner) { isRecentDelete ->
            action.invoke()
        }

        sharedViewModel.noteSelectionMode.observe(viewLifecycleOwner) {
            if (sharedViewModel.isRecentDeleteFolder.value == true) {
                action.invoke()
            }
        }

        sharedViewModel.isSearch.observe(viewLifecycleOwner) { isSearchMode ->
            if (sharedViewModel.isRecentDeleteFolder.value == true) {
                binding?.bottomMenuParent?.visibility = View.GONE
            } else {
                if (ConfigUtils.isToDoDeprecated) {
                    AppLogger.BASIC.i(TAG, "initObservers error: Todo is deprecated. sharedViewModel.isRecentDeleteFolder.value = false")
                    binding?.bottomMenuParent?.visibility = View.GONE
                } else {
                    binding?.bottomMenuParent?.visibility = if (isSearchMode) View.GONE else View.VISIBLE
                }
            }
        }
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.root) { _, insets ->
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            if (isAdded) {
                updateBottomMenuPadding(bottomPadding = systemBarInsets.bottom)
                (activity as? MainActivity)?.updateFabMarginBottom()
            }
        }
    }


    /**
     * 5562183：从分屏状态拉动分屏条进入浮窗，左侧父级显示异常
     */
    private fun registerZoomWindowObserver() {
        lifecycleScope.launch(Dispatchers.IO) {
            registerAndCheckZoomWindowObserver()
        }
    }

    private fun initBottomMenu() {
        binding?.bottomMenuParent?.apply {
            if (ConfigUtils.isToDoDeprecated) {
                AppLogger.BASIC.i(TAG, "initBottomMenu error: Todo is deprecated.")
                visibility = View.GONE
                return
            }
        }
        binding?.bottomMenu?.apply {
            val currentItemId = if (initTabPosition == NOTE_INDEX) R.id.note_tab else R.id.todo_tab
            selectedItemId = currentItemId
            setOnNavigationItemSelectedListener { menuItem ->
                if (bottomMenuAnimatorHelper?.tabNavigationExitAnimRunning() == true
                    || visibility == View.GONE
                ) {
                    return@setOnNavigationItemSelectedListener false
                }
                if (menuItem.itemId != selectedItemId) {
                    switchFragment(menuItem.itemId)
                }
                true
            }
            binding?.bottomMenuParent?.post {
                binding?.bottomMenuParent?.let { sharedViewModel.bottomMenuParentHeight = it.height }
            }
        }
    }

    private fun switchFragment(tabId: Int) {
        AppLogger.BASIC.d(TAG, "switchFragment $tabId")
        when (tabId) {
            R.id.note_tab -> {
                binding?.vpMain?.setCurrentItem(NOTE_INDEX, true)
                StatisticsUtils.setEventTabChange(StatisticsUtils.TYPE_CHANGE_TO_NOTE)
            }

            R.id.todo_tab -> {
                binding?.vpMain?.setCurrentItem(TODO_INDEX, true)
                StatisticsUtils.setEventTabChange(StatisticsUtils.TYPE_CHANGE_TO_TODO)
            }
        }
    }

    private fun bottomMenuAnimation(isSelectionMode: Boolean, isNote: Boolean = false, withAnim: Boolean) {
        AppLogger.BASIC.d(TAG, "bottomMenuAnimation twoPane=${sharedViewModel.twoPane},isNoteTab=$isNote,isSelectionMode=$isSelectionMode")
        if (!isNote && sharedViewModel.twoPane && isSelectionMode) {
            //待办父子级选中待办时不需要消失导航栏
            AppLogger.BASIC.d(TAG, "bottomMenuAnimation return todo two panel")
            return
        }
        doBottomMenuAnimation(isSelectionMode, isNote, withAnim)
    }

    fun doBottomMenuAnimation(isSelectionMode: Boolean, isNote: Boolean = false, withAnim: Boolean) {
        AppLogger.BASIC.d(TAG, "doBottomMenuAnimation isSelectionMode=$isSelectionMode,isNote=$isNote")
        if (bottomMenuAnimatorHelper == null) {
            bottomMenuAnimatorHelper = context?.let {
                NavigationAnimatorHelper(it).apply {
                    initTabNavigationAnimator(binding?.bottomMenu)
                }
            }
        }

        if (isNote && sharedViewModel.twoPane) { //父子级结构
            if (isSelectionMode) {
                bottomMenuAnimatorHelper?.dismissTabNavigation()
                sharedViewModel.enterSelectAnimOnlyAlpha = false
            } else {
                bottomMenuAnimatorHelper?.showTabNavigation(withAnim, sharedViewModel.enterSelectAnimOnlyAlpha)
            }
        } else {
            if (isSelectionMode) {
                bottomMenuAnimatorHelper?.dismissTabNavigationOnlyAlphaAnim()
                sharedViewModel.enterSelectAnimOnlyAlpha = true
            } else {
                bottomMenuAnimatorHelper?.showTabNavigationOnlyAlphaAnim(withAnim, sharedViewModel.enterSelectAnimOnlyAlpha)
            }
        }
    }

    @Synchronized
    private fun registerAndCheckZoomWindowObserver() {
        if (oplusZoomWindowManager == null) {
            oplusZoomWindowManager = OplusZoomWindowManagerProxy()
        }
        if (oplusZoomWindowServer == null) {
            oplusZoomWindowServer = getOplusZoomWindowObserver()
        }
        oplusZoomWindowServer?.let {
            oplusZoomWindowManager?.registerZoomWindowObserver(it)
        }
    }

    private fun getOplusZoomWindowObserver(): OplusZoomWindowObserverProxy? {
        runCatching {
            return OplusZoomWindowObserverImpl(this@MainFragment)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getOplusZoomWindowObserver", it)
        }
        return null
    }

    fun getNoteListFragment(): NoteListFragment? {
        return kotlin.runCatching {
            if (isAdded) {
                fragmentList[NOTE_INDEX] as? NoteListFragment
            } else {
                null
            }
        }.getOrNull()
    }

    fun getTodoFragment(): TodoFragment? {
        return kotlin.runCatching {
            if (isAdded) {
                fragmentList[TODO_INDEX] as? TodoFragment
            } else {
                null
            }
        }.getOrNull()
    }

    private fun updateBottomMenuPadding(bottomPadding: Int) {
        AppLogger.BASIC.d(TAG, "updateBottomMenuPadding$bottomPadding")
        binding?.bottomMenuParent?.updatePadding(bottom = bottomPadding)
    }

    fun refreshNoteListTips() {
        if (isAdded) {
            if (sharedViewModel.currentTabIndex.value == NOTE_INDEX) {
                getNoteListFragment()?.refreshNoteListTips()
            } else {
                getTodoFragment()?.refreshNoteListTips()
            }
        } else {
            AppLogger.BASIC.e(TAG, "refreshNoteListTips error with MainFragment is not added ")
        }
    }

    private fun resetCheckedInfo(isTwoPane: Boolean = false) {
        val fragment = fragmentList[NOTE_INDEX]
        if (fragment is NoteListFragment && fragment.isAdded) {
            fragment.resetCheckedInfo()
            fragment.twoPane = isTwoPane
            fragment.adapter.notifyDataSetChangedDelegate()
        }
    }

    fun getViewPager(): ViewPager2? {
        return binding?.vpMain
    }

    override fun onStart() {
        super.onStart()
        checkAndFetchCloudGlobalState()
    }

    private var syncInitReceiver: BroadcastReceiver? = null
    private var isFetchingCloudState = false

    /**每次进入应用时调用，确保在 CloudKit init 之后调用*/
    private fun checkAndFetchCloudGlobalState() {
        if (BaseSyncManager.syncInit) {
            fetchCloudGlobalState()
        } else if (ConfigUtils.isUseCloudKit) {
            if (syncInitReceiver == null) {
                syncInitReceiver = object : BroadcastReceiver() {
                    override fun onReceive(context: Context?, intent: Intent?) {
                        fetchCloudGlobalState()
                        LocalBroadcastManager.getInstance(MyApplication.appContext).unregisterReceiver(this)
                        syncInitReceiver = null
                    }
                }
                LocalBroadcastManager.getInstance(MyApplication.appContext)
                    .registerReceiver(syncInitReceiver!!, IntentFilter(BaseSyncManager.ACTION_SYNC_INIT_FINISH))
            }
        }
    }

    private fun fetchCloudGlobalState() {
        if (SyncSwitchStateRepository.isLoginAuthing || isFetchingCloudState) {
            return
        }
        CloudKitGlobalStateManager.judgeOCloudStateCheckLogin(lifecycleScope, JudgeGlobalStateCallbackImpl(this))
    }

    @MainThread
    fun refreshCloudSyncTips() {
        getNoteListFragment()?.takeIf { it.isAdded }?.also { it.refreshSyncTips() }
        getTodoFragment()?.takeIf { it.isAdded }?.also { it.refreshSyncTips() }
    }

    private class JudgeGlobalStateCallbackImpl(fragment: MainFragment) : JudgeGlobalStateCallback {
        private val weakRef = WeakReference(fragment)

        override fun onStart() {
            weakRef.get()?.isFetchingCloudState = true
        }

        override fun onSuccess(globalEnable: Boolean, cloudEnable: Boolean) {
            weakRef.get()?.isFetchingCloudState = false
            weakRef.get()?.let {
                it.lifecycleScope.launch(Dispatchers.Main) { it.refreshCloudSyncTips() }
            }
        }

        override fun onError(errorType: Int) {
            weakRef.get()?.isFetchingCloudState = false
            weakRef.get()?.let {
                it.lifecycleScope.launch(Dispatchers.Main) { it.refreshCloudSyncTips() }
            }
        }
    }


    private class OplusZoomWindowObserverImpl(fragment: MainFragment) : OplusZoomWindowObserverProxy() {
        private val weakRef = WeakReference(fragment)

        @Throws(RemoteException::class)
        override fun onZoomWindowShow() {
            AppLogger.BASIC.e(TAG, "onZoomWindowShow")
            weakRef.get()?.let {
                val size = ResponsiveUiHelper.getSmallestWindowSizeClass(it.requireActivity())
                val fragment = it.fragmentList[NOTE_INDEX]
                if (size == WindowWidthSizeClass.Compact) {
                    if (fragment is NoteListFragment && fragment.isAdded) {
                        fragment.twoPane = false
                    }
                    val detail = (it.activity as? MainActivity)?.binding?.vsEndContainer?.viewStub
                    detail?.post {
                        detail.visibility = View.GONE
                    }
                }
            }
        }

        @Throws(RemoteException::class)
        override fun onZoomWindowHide() {
            AppLogger.BASIC.e(TAG, "onZoomWindowHide")
            weakRef.get()?.let {
                val fragment = it.childFragmentManager.findFragmentByTag("f0")
                if (fragment is NoteListFragment && fragment.isAdded) {
                    fragment.binding?.root?.post {
                        fragment.adapter.notifyDataSetChanged()
                    }
                }
            }
        }

        @Throws(RemoteException::class)
        override fun onZoomWindowDied(p0: String?) {
        }

        @Throws(RemoteException::class)
        override fun onInputMethodChanged(p0: Boolean) {
        }
    }

    companion object {

        const val TAG = "MainFragment"
        const val NOTE_INDEX = 0
        const val TODO_INDEX = 1
        private const val MAST_DURATION = 100L
        private const val ALPHA_DISABLE = 0.3f
        private const val ALPHA_ENABLE = 1f
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         */
        @JvmStatic
        fun newInstance(initTabPos: Int): MainFragment = MainFragment().apply {
            arguments = Bundle().apply {
                putInt(ARG_PARAM_TAB_POS, initTabPos)
            }
        }
    }
}