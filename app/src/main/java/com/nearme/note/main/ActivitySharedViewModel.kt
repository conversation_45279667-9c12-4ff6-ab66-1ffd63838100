package com.nearme.note.main

import android.view.MotionEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.nearme.note.MyApplication
import com.nearme.note.logic.MoveFileRunnable
import com.nearme.note.main.todo.TodoListFragment
import com.nearme.note.util.FileUtil
import com.nearme.note.util.postValueSafe
import com.oplus.note.aigc.model.AIGCState
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ActivitySharedViewModel: ViewModel() {
    companion object {
        const val TAG = "ActivitySharedViewModel"
    }
    val isDetailEditMode = MutableLiveData<Boolean>()
    val isDetailOverlayMode = MutableLiveData<Boolean>()
    //note fragment shared to main activity
    var noteSelectionMode = MutableLiveData<Boolean>()
    var isSearch = MutableLiveData<Boolean>()
    var isRecentDeleteFolder = MutableLiveData<Boolean>()
    var isUserInputEnabledWhenRecentDeleteFolder = MutableLiveData<Boolean>()
    var noteSelectionModeChangeWithAnim = true

    /**
     * 智能摘要界面隐藏添加按钮
     */
    var isSummaryFolder = MutableLiveData<Boolean>()
    var isCollectionFolder = MutableLiveData<Boolean>()
    /**手写笔记需要隐藏添加按钮*/
    var isPaintFolder = MutableLiveData<Boolean>()
    var isNotebookListShow = MutableLiveData<Boolean>()
    var turnToAllNoteFolder = MutableLiveData<Boolean>()
    var isPullingDown = MutableLiveData<Boolean>()
    /**kit面板是否显示*/
    var isKitPanelAlive = MutableLiveData<Boolean>()
    var kitMotionEvent = MutableLiveData<MotionEvent>()

    var twoPane = false
    val noteMode = MutableLiveData<Boolean>()
    val disableBackAnim = MutableLiveData<Boolean>() // 目前被ThirdLogDetailFragment使用

    /**底部bar的高度**/
    var bottomMenuParentHeight = 0

    fun isGridMode(): Boolean {
        return SharedPreferencesUtil.getInstance().getInt(MyApplication.appContext, SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.HOME_PAGE_MODE_KEY) == SharedPreferencesUtil.HOME_PAGE_MODE_GRID_MODE
    }

    //main activity shared to fragment
    var storagePermissionDenied = MutableLiveData<Boolean>()
    var checkPermission = MutableLiveData<Boolean>()
    var openPermissionPage = MutableLiveData<Boolean>()
    val viewPagerScrollStateIdle = MutableLiveData<Boolean>(true)
    /**
     * 当前选中的是笔记TAB还是待办TAB
     */
    var currentTabIndex = MutableLiveData<Int>(MainActivity.NOTE_INDEX)
    var inZoomWindowState = false
    var navigationWindowInsetBottom = 0
    //系统底部手势栏高度
    var systemBarInsetBottom = 0
        set(value) {
            field = value
            fabAnimatorHelper?.systemBarsBottom = value
        }

    //main activity and todo fragment shared
    val notificationUUID = MutableLiveData<String>()
    var todoSelectionMode = MutableLiveData<Boolean>()
    var dragSortMode = MutableLiveData<Boolean>(false)
    /**平板父子集点击单应用分屏时，需要清除父子集的选中状态*/
    var needResetCheckedInfo = false
    var isSpeechRecording = false

    var noteRecycledBlock: ((Collection<String>?) -> Unit)? = null
    var currentNoteAigcState: AIGCState = AIGCState.STATE_IDLE
        set(value) {
            field = value
            AppLogger.BASIC.d(TAG, "currentNoteAigcState $value")
        }
    var stopAigcRewrite = MutableLiveData<Boolean>(false)
    var fabAnimatorHelper: FloatingButtonAnimatorHelper? = null
    var enterSelectAnimOnlyAlpha = true
    var isNeedUpdateSearchRes = MutableLiveData<Boolean>()
    fun setIsNeedUpdateSearchRes(isNeedUpdateSearchResFlag: Boolean) {
        isNeedUpdateSearchRes.value = isNeedUpdateSearchResFlag
    }

    var isHideFabFlag = MutableLiveData<Boolean>()
    fun setIsHideFabFlag(isContentMainEnd: Boolean) {
        isHideFabFlag.value = isContentMainEnd
    }

    val shouldHideMainFragment = MutableLiveData<Boolean>()
    fun setShouldHideMainFragment(shouldHide: Boolean) {
        shouldHideMainFragment.value = shouldHide
    }

    /**
     * 待办父子级显示的分组
     */
    var currentTodoType: Int = TodoListFragment.TODO_ALL

    fun setTodoSelectionMode(isEditMode: Boolean) {
        todoSelectionMode.value = isEditMode
    }

    fun setDragSortMode(isDragMode: Boolean) {
        dragSortMode.postValueSafe(isDragMode)
        AppLogger.BASIC.d(TAG, "isDragMode: $isDragMode")
    }
    fun isDragSortMode(): Boolean {
        return dragSortMode.value == true
    }

    fun moveBackUpNoteData() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (!FileUtil.isStorageNotEnough()) {
                    MoveFileRunnable(FileUtil.getOldNotePath(), FileUtil.getNotePath()).run()
                }
            }
        }
    }

    fun isUserInputEnabled(): LiveData<Boolean> {
        val inputEnabled = MediatorLiveData<Boolean>()
        val zipFunction = object :
            Function10<Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean?> {
            override fun invoke(
                noteSelectionMode: Boolean?,
                todoSelectionMode: Boolean?,
                todoDragMode: Boolean?,
                isSearch: Boolean?,
                isUserInputEnabledWhenRecentDeleteFolder: Boolean?,
                isDetailEditMode: Boolean?,
                isOverlayMode: Boolean?,
                isPullingDown: Boolean?,
                isNotebookListShow: Boolean?,
                isKitShowing: Boolean?
            ): Boolean {
                return !(noteSelectionMode == true || todoSelectionMode == true || isSearch == true
                        || isUserInputEnabledWhenRecentDeleteFolder != true || todoDragMode == true
                        || (twoPane && isDetailEditMode == true) || (twoPane && isOverlayMode == true) || isPullingDown == true
                        || isNotebookListShow == true || isKitShowing == true)
            }
        }
        inputEnabled.addSource(noteSelectionMode) { value: Boolean ->
            inputEnabled.value = zipFunction.invoke(value,
                todoSelectionMode.value,
                dragSortMode.value,
                isSearch.value,
                isUserInputEnabledWhenRecentDeleteFolder.value,
                isDetailEditMode.value,
                isDetailOverlayMode.value,
                isPullingDown.value,
                isNotebookListShow.value,
                isKitPanelAlive.value)
        }
        inputEnabled.addSource(todoSelectionMode) { value: Boolean ->
            inputEnabled.value = zipFunction.invoke(noteSelectionMode.value,
                value,
                dragSortMode.value,
                isSearch.value,
                isUserInputEnabledWhenRecentDeleteFolder.value,
                isDetailEditMode.value,
                isDetailOverlayMode.value,
                isPullingDown.value,
                isNotebookListShow.value,
                isKitPanelAlive.value)
        }
        inputEnabled.addSource(dragSortMode) { value: Boolean ->
            inputEnabled.value = zipFunction.invoke(noteSelectionMode.value,
                todoSelectionMode.value,
                value,
                isSearch.value,
                isUserInputEnabledWhenRecentDeleteFolder.value,
                isDetailEditMode.value,
                isDetailOverlayMode.value,
                isPullingDown.value,
                isNotebookListShow.value,
                isKitPanelAlive.value)
        }
        inputEnabled.addSource(isSearch) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(noteSelectionMode.value,
                    todoSelectionMode.value,
                    dragSortMode.value,
                    value,
                    isUserInputEnabledWhenRecentDeleteFolder.value,
                    isDetailEditMode.value,
                    isDetailOverlayMode.value,
                    isPullingDown.value,
                    isNotebookListShow.value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isUserInputEnabledWhenRecentDeleteFolder) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(noteSelectionMode.value, todoSelectionMode.value, dragSortMode.value, isSearch.value,
                    value, isDetailEditMode.value, isDetailOverlayMode.value, isPullingDown.value,
                    isNotebookListShow.value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isDetailEditMode) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(noteSelectionMode.value,
                    todoSelectionMode.value,
                    dragSortMode.value,
                    isSearch.value,
                    isUserInputEnabledWhenRecentDeleteFolder.value,
                    value,
                    isDetailOverlayMode.value,
                    isPullingDown.value,
                    isNotebookListShow.value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isDetailOverlayMode) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(noteSelectionMode.value, todoSelectionMode.value, dragSortMode.value, isSearch.value,
                    isUserInputEnabledWhenRecentDeleteFolder.value, isDetailEditMode.value, value, isPullingDown.value,
                    isNotebookListShow.value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isPullingDown) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(noteSelectionMode.value,
                    todoSelectionMode.value,
                    dragSortMode.value,
                    isSearch.value,
                    isUserInputEnabledWhenRecentDeleteFolder.value,
                    isDetailEditMode.value,
                    isDetailOverlayMode.value,
                    value,
                    isNotebookListShow.value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isNotebookListShow) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(
                    noteSelectionMode.value,
                    todoSelectionMode.value,
                    dragSortMode.value,
                    isSearch.value,
                    isUserInputEnabledWhenRecentDeleteFolder.value,
                    isDetailEditMode.value,
                    isDetailOverlayMode.value,
                    isPullingDown.value,
                    value,
                    isKitPanelAlive.value)
        }

        inputEnabled.addSource(isKitPanelAlive) { value: Boolean ->
            inputEnabled.value =
                zipFunction.invoke(
                    noteSelectionMode.value,
                    todoSelectionMode.value,
                    dragSortMode.value,
                    isSearch.value,
                    isUserInputEnabledWhenRecentDeleteFolder.value,
                    isDetailEditMode.value,
                    isDetailOverlayMode.value,
                    isPullingDown.value,
                    isNotebookListShow.value,
                    value)
        }

        return inputEnabled
    }

    fun floatButtonChange(): LiveData<Boolean> {
        val floatButtonChangeLiveData = MediatorLiveData<Boolean>()
        val zipFunction = object :
            Function10<Boolean?, Boolean?, Boolean?, Boolean?, Boolean?, Int?, Boolean?, Boolean?, Boolean?, Boolean?, Boolean> {
            override fun invoke(
                noteSelected: Boolean?,
                isRecentDeleteFolder: Boolean?,
                isSearch: Boolean?,
                todoSelectionMode: Boolean?,
                todoDragMode: Boolean?,
                tabIndex: Int?,
                isSummaryFolder: Boolean?,
                isCollectionFolder: Boolean?,
                isNotebookListShow: Boolean?,
                isPaintFolder: Boolean?
            ): Boolean {
                AppLogger.BASIC.d(
                    TAG,
                    "zipFunction invoke tabIndex=$tabIndex,noteSelected=$noteSelected,isRecentDeleteFolder=$isRecentDeleteFolder," +
                            "isSearch=$isSearch,todoSelectionMode=$todoSelectionMode,todoDragMode=$todoDragMode," +
                            "tabIndex=$tabIndex,isSummaryFolder=$isSummaryFolder,isCollectionFolder=$isCollectionFolder," +
                            "isPaintFolder=$isPaintFolder"
                )
                if (tabIndex == 1) { // is todos tab
                    return todoSelectionMode != true && todoDragMode != true
                            && (!twoPane || (currentTodoType != TodoListFragment.TODO_DONE))
                }

                if (listOf(
                        isSearch,
                        isRecentDeleteFolder,
                        isSummaryFolder,
                        isCollectionFolder,
                        isPaintFolder
                    ).any {
                        it == null
                    }
                ) {
                    return false
                }
                return when {
                    isSearch == true -> false
                    isRecentDeleteFolder == true -> false
                    isSummaryFolder == true -> false
                    isCollectionFolder == true -> false
                    isNotebookListShow == true -> false
                    isPaintFolder == true -> false
                    else -> noteSelected != true
                }
            }
        }

        floatButtonChangeLiveData.addSource(noteSelectionMode) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                value, isRecentDeleteFolder.value,
                isSearch.value, todoSelectionMode.value, dragSortMode.value, currentTabIndex.value,
                isSummaryFolder.value, isCollectionFolder.value, isNotebookListShow.value, isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(isRecentDeleteFolder) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value, value,
                isSearch.value, todoSelectionMode.value, dragSortMode.value, currentTabIndex.value,
                isSummaryFolder.value, isCollectionFolder.value, isNotebookListShow.value, isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(isSearch) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                value,
                todoSelectionMode.value,
                dragSortMode.value,
                currentTabIndex.value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(todoSelectionMode) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                value,
                dragSortMode.value,
                currentTabIndex.value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(dragSortMode) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                value,
                currentTabIndex.value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(currentTabIndex) { value: Int ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                dragSortMode.value,
                value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }

        floatButtonChangeLiveData.addSource(isSummaryFolder) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                dragSortMode.value,
                currentTabIndex.value,
                value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }
        floatButtonChangeLiveData.addSource(isCollectionFolder) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                dragSortMode.value,
                currentTabIndex.value,
                isSummaryFolder.value,
                value,
                isNotebookListShow.value,
                isPaintFolder.value
            )
        }
        floatButtonChangeLiveData.addSource(isNotebookListShow) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                dragSortMode.value,
                currentTabIndex.value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                value,
                isPaintFolder.value
            )
        }
        floatButtonChangeLiveData.addSource(isPaintFolder) { value: Boolean ->
            floatButtonChangeLiveData.value = zipFunction.invoke(
                noteSelectionMode.value,
                isRecentDeleteFolder.value,
                isSearch.value,
                todoSelectionMode.value,
                dragSortMode.value,
                currentTabIndex.value,
                isSummaryFolder.value,
                isCollectionFolder.value,
                isNotebookListShow.value,
                value
            )
        }
        return floatButtonChangeLiveData
    }
}