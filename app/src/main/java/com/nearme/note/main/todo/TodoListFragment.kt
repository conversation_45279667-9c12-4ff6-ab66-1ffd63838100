/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 ** VENDOR_EDIT
 ** File: - TodoListFragment
 ** Description:
 **         v1.0:   TodoListFragment , show items in a list
 *          [技术方案](https://odocs.myoas.com/docs/N2A1MEPwdLi8JxAD)
 **
 ** Version: 1.0
 ** Date: 2024/9/11
 ** Author: wangyinglei
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** wangyinglei        2024/09/11   1.0      Create this module
 ********************************************************************************/
@file:Suppress("ForbiddenComment")

package com.nearme.note.main.todo

import android.Manifest
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.app.Activity
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.text.format.DateFormat
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.animation.LinearInterpolator
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.doOnNextLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.nearme.note.DialogFactory
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.list.TodoAdapter
import com.nearme.note.activity.list.TodoItemAnimator
import com.nearme.note.activity.list.TodoItemTouchHelperCallBack
import com.nearme.note.activity.list.entity.ToDoItem
import com.nearme.note.activity.richedit.CheckPermissionHelper
import com.nearme.note.activity.richedit.CheckPermissionHelper.RequestResultCallback
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.NoteSyncProcessProxy
import com.nearme.note.main.ActivitySharedViewModel
import com.nearme.note.main.BaseFragment
import com.nearme.note.main.MainActivity
import com.nearme.note.main.UIConfigMonitor
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DeleteSoundUtils
import com.nearme.note.util.DeviceInfoUtils
import com.nearme.note.util.ImageHelper
import com.nearme.note.util.IntentParamsUtil
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.view.DialogUseMode
import com.nearme.note.view.TodoModalDialog
import com.nearme.note.view.helper.NavigationAnimatorHelper
import com.nearme.note.view.helper.UiHelper
import com.nearme.note.viewmodel.ToDoViewModel
import com.nearme.note.viewmodel.TodoSharedViewModel
import com.oplus.cloud.utils.PrefUtils
import com.oplus.cloudkit.view.CloudKitInfoController
import com.oplus.cloudkit.view.SyncGuideManagerWrapper
import com.oplus.note.R
import com.oplus.note.databinding.FragmentTodoListBinding
import com.oplus.note.edgeToEdge.EdgeToEdgeManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.OsConfigurations
import com.oplus.note.osdk.proxy.OplusFlexibleWindowManagerProxy
import com.oplus.note.permission.PermissionManager
import com.oplus.note.permission.PermissionsBlockedUtils
import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.scenecard.todo.TodoListActivity
import com.oplus.note.utils.SysDragManager
import com.oplus.note.utils.isPackageDisabled
import com.oplus.note.view.EmptyContentView
import com.oplus.richtext.editor.view.focus.FocusInfo
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 待办子级界面
 */
class TodoListFragment : BaseFragment() {
    companion object {
        @JvmStatic
        fun newInstance(): TodoListFragment = TodoListFragment()

        const val TAG = "TodoListFragment"
        const val ACTION_UPDATE_ITEM_EXPIRED = "action_update_item_expired"
        const val FRAGMENT_ALPHA_TIME = 500L

        //未完成待办
        const val TODO_ALL = 0
        //已完成待办
        const val TODO_DONE = 1
        private const val ALPHA_DURATION = 160L
        private const val DELAY_TIME = 100L
        private const val DELAY_REMOVE_ONGLOBAL_LAYOUT = 300L
        private const val FLOAT_150 = 150F
        private const val FLOAT_50 = 50F
    }

    private val todoMarginViewModel by viewModels<TodoListMarginViewModel>({ requireActivity() })
    private val todoListViewModel by viewModels<TodoSharedViewModel>({ requireActivity() })
    private val todoViewModel by viewModels<ToDoViewModel>({ requireActivity() })
    private val sharedViewModel by viewModels<ActivitySharedViewModel>({ requireActivity() })
    private var linearLayoutManager: COUILinearLayoutManager? = null

    private val adapter by lazy {
        TodoAdapter(viewLifecycleOwner, todoListViewModel)
    }

    private val editMenuStub = lazy { binding?.root?.findViewById<ViewStub>(R.id.todo_list_edit_menu_stub) }
    private var binding: FragmentTodoListBinding? = null
    private var guideManager: SyncGuideManagerWrapper? = null
    private var loadDataFinished = false

    private var toolNavigationView: COUINavigationView? = null
    private var navigationAnimatorHelper: NavigationAnimatorHelper? = null
    private var isAnimating = false
    private var selectItemSize = 0

    /**云同步开关状态，为 null 时表示还没有获取成功*/
    private var syncEnable: Boolean? = null
    private var todoModalDialog: TodoModalDialog? = null
    private var emptyContentPage: EmptyContentView? = null
    private var noteSyncProcess: NoteSyncProcessProxy? = null
    private var noteListHelper: NoteListHelper? = null
    private var dialogClickListener: DialogFactory.DialogOnClickListener? = null
    private var dialogFactory: DialogFactory? = null
    private var preHourFormat = false
    private var isNotificationInit = true
    private var isSelectionModeFirstInit = true


    private var mIsFirstLoadTodoList = true
    private var mEmptyContentPageHelper: ImageHelper? = null

    private val alarmPermissionHelper = CheckPermissionHelper()
    private var permissionManager: PermissionManager? = null
    private var isFirstOnResume = true
    private var itemTouchHelper: ItemTouchHelper? = null
    private var gestureDetector: GestureDetector? = null

    private var sysDragManager: SysDragManager? = null

    //当前加载的列表，区分已完成和未完成
    private val currentTodoList = mutableListOf<ToDoItem>()

    private val timeChangeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            adapter.notifyDataSetChanged()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (activity !is MainActivity && ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.i(TAG, "onCreate error: Todo is deprecated.")
            activity?.finish()
            return
        }
        permissionManager = PermissionManager(this)
        initReceiver()
        preHourFormat = DateFormat.is24HourFormat(context)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = FragmentTodoListBinding.inflate(inflater, container, false).apply {
            lifecycleOwner = viewLifecycleOwner
            viewModel = todoMarginViewModel
        }

        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initiateWindowInsets()
        initEditToolbar()
        initRecyclerView(savedInstanceState)
        initEmptyPage()
        initObservers()
        initDivider()
        val alphaAnimator = ObjectAnimator.ofFloat(binding?.root, "alpha", 0f, 1f)
        alphaAnimator.duration = FRAGMENT_ALPHA_TIME
        alphaAnimator.start()
        handleJumpScroll()
        sysDragManager = context?.let { SysDragManager(it, lifecycle) }
    }

    private fun initDivider() {
        binding?.todoList?.let {
            binding?.dividerbar?.bindRecyclerView(it)
        }
    }

    /**
     * 加载不同类型的待办
     * TODO_ALL=0 全部
     * TODO_DONE=1 已完成
     */
    fun loadTodos(todoType: Int) {
        kotlin.runCatching {
            if (!lifecycle.currentState.isAtLeast(Lifecycle.State.CREATED)) {
                AppLogger.BASIC.d(TAG, "loadTodos type=$todoType,return before CREATED")
                return
            }
            AppLogger.BASIC.d(TAG, "loadTodos type=$todoType,current=${sharedViewModel.currentTodoType}")
            if (sharedViewModel.currentTodoType == todoType) {
                return
            }
            sharedViewModel.currentTodoType = todoType

            if (isEditMode()) {
                doOnCancelEdit()
            }
            val list = getFilterList(todoListViewModel.toDoItems.value)
            updateTitle()
            val todoItemAnimator = binding?.todoList?.itemAnimator as? TodoItemAnimator
            todoItemAnimator?.setScale(1F)
            adapter.setData(list, sharedViewModel.twoPane, todoType, false)
            lifecycleScope.launch {
                if (list.isNotEmpty()) {
                    resetMainEmptyPage()
                }
                delay(todoItemAnimator?.removeDuration ?: 0)
                AppLogger.BASIC.d(TAG, "loadTodos reset scale")
                todoItemAnimator?.setScale(TodoItemAnimator.SCALE)
                if (list.isEmpty()) {
                    resetMainEmptyPage()
                } else {
                    scrollToPosition(0)
                }
            }
            sharedViewModel.fabAnimatorHelper?.apply {
                changeFloatButtonState(todoType == TODO_ALL)
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "loadTodos error ${it.message}")
        }
    }


    /**
     * 返回 全部/未完成的待办列表
     */
    private fun getFilterList(items: List<ToDoItem>?): List<ToDoItem> {
        if (items.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "getNoHeadList item null return empty")
            return mutableListOf()
        }
        val result = when (sharedViewModel.currentTodoType) {
            TODO_ALL -> items.filter { it.itemType == ToDoItem.ITEM_TYPE_HEADER || it.itemType == ToDoItem.ITEM_TYPE_TODO }
            else -> items.filter { it.itemType == ToDoItem.ITEM_TYPE_TODO && it.toDo.isComplete }
        }
        currentTodoList.clear()
        if (sharedViewModel.currentTodoType == TODO_ALL) {
            currentTodoList.addAll(result.filter { it.itemType == ToDoItem.ITEM_TYPE_TODO })
        } else {
            currentTodoList.addAll(result)
        }
        AppLogger.BASIC.d(
            TAG,
            "getNoHeadList items=${items.size},result=${result.size}, type:${sharedViewModel.currentTodoType}"
        )
        return result
    }

    private fun handleJumpScroll() {
        activity?.apply {
            val position = IntentParamsUtil.getIntExtra(this.intent, TodoListActivity.POSITION, -1)
            AppLogger.BASIC.d(TAG, "handleJumpScroll position: $position")
            if (position > 0) {
                binding?.todoList?.post {
                    scrollToPosition(position)
                    AppLogger.BASIC.d(TAG, "handleJumpScroll position $position")
                }
            }
        }
    }

    /**
     * 更新空页面布局的位置
     */
    private fun updateEmptyViewPosition(needDelay: Boolean = false) {
        if (emptyContentPage?.isVisible == true) {
            binding?.root?.postDelayed({
                mEmptyContentPageHelper?.let {
                    AppLogger.BASIC.d(TAG, "updateEmptyViewPosition")
                    emptyContentPage?.update(it)
                }
            }, if (needDelay) DELAY_TIME else 0)
        }
    }

    fun onFragmentSelected() {
        AppLogger.BASIC.d(TAG, "onFragmentSelected")
        updateEmptyViewPosition()
    }

    override fun onResume() {
        super.onResume()
        resetHourFormat()
        noteListHelper?.onResume()
        lifecycleScope.launch {
            delay(DELAY_TIME)
            val isFromAppCard = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.MAIN_ACTION_FROM, "")
            AppLogger.BASIC.d(TAG, "onResume: isFromAppCard: $isFromAppCard")
            if (isFromAppCard.equals(PrefUtils.APP_TODO_MIDDLE_CARD_OTHER)) {
                if (ConfigUtils.isToDoDeprecated) {
                    AppLogger.BASIC.i(TAG, "onResume error: Todo is deprecated.")
                    activity?.finish()
                }
                val toDoLocalId = IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.APP_TODO_CARD_LOCAL_ID, "")
                val toDoItemList: List<ToDoItem> = getFilterList(todoListViewModel.toDoItems.value)
                val position = toDoItemList.indexOfFirst { toDoLocalId == it.toDo.localId.toString() }
                if (position >= 0) {
                    scrollToPosition(position)
                } else {
                    AppLogger.BASIC.d(TAG, "current position $position can not be found")
                }
            }
        }
        if (!isFirstOnResume) {
            resetMainEmptyPage()
        }
        isFirstOnResume = false
    }

    override fun onPause() {
        super.onPause()
        AppLogger.BASIC.d(TAG, "onPause")
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        super.onMultiWindowModeChanged(isInMultiWindowMode)
        resetMainEmptyPage()
    }

    override fun backToTop() {
        binding?.todoList?.stopScroll()
        binding?.todoList?.smoothScrollToPosition(0)
    }

    override fun onStop() {
        super.onStop()
        AppLogger.BASIC.d(TAG, "onStop")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (sharedViewModel.todoSelectionMode.value == true) {
            sharedViewModel.todoSelectionMode.value = false
        }
        todoListViewModel.selectAll(false)
        todoListViewModel.mPendingDeleteSelectedToDos.value = false
    }

    override fun onDestroy() {
        super.onDestroy()
        AppLogger.BASIC.d(TAG, "onDestroy")
        permissionManager = null
        todoModalDialog?.onDestroy()
        todoModalDialog?.setDialogListener(null)
        todoModalDialog = null
        sharedViewModel.notificationUUID.value = ""
        guideManager?.release()
        appContext.unregisterReceiver(timeChangeReceiver)
        noteSyncProcess?.release()
        noteListHelper?.onBack()
        noteListHelper?.onDestroy()
        dialogFactory?.onDestory()
        dialogFactory = null
        todoListViewModel.removeOuterToDoAdapterCallback(callback)
    }

    private fun resetHourFormat() {
        val currentHourFormat = DateFormat.is24HourFormat(context)
        if (preHourFormat != currentHourFormat) {
            preHourFormat = currentHourFormat
            adapter.notifyDataSetChanged()
        }
    }

    private fun initiateWindowInsets() {
        EdgeToEdgeManager.observeOnApplyWindowInsets(binding?.root) { v, insets ->
            val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.statusBars())
            v.updatePadding(left = systemBarInsets.left, 0, right = systemBarInsets.right)
            initToolNavigationMenu()
            binding?.listContainer?.updatePadding(bottom = systemBarInsets.bottom)
            binding?.root?.visibility = View.VISIBLE
            binding?.todoListEditMenuGroup?.updatePadding(bottom = systemBarInsets.bottom)
        }
    }

    /**
     * 初始化编辑模式下的标题view
     */
    private fun initEditToolbar() {
        binding?.root?.fitsSystemWindows = true
        val menu = binding?.toolbarTodo?.menu ?: return
        val edit = menu.findItem(R.id.edit_todo)
        val cancel = menu.findItem(R.id.cancel)
        val select = menu.findItem(R.id.select_all)
        val showFinishTodo = menu.findItem(R.id.toggle_finished_todo)
        val setting = menu.findItem(R.id.jump_setting)
        edit.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        menu.removeItem(R.id.empty)

        showFinishTodo.isVisible = false
        setting.isVisible = false
        edit.setIcon(R.drawable.icon_todo_edit)

        edit.setOnMenuItemClickListener {
            AppLogger.BASIC.d(TAG, "onMenuItemClick edit")
            if (sharedViewModel.storagePermissionDenied.value == true) {
                sharedViewModel.checkPermission.value = true
                return@setOnMenuItemClickListener true
            }
            todoListViewModel.isDeletingOrHiding = false
            todoListViewModel.setSelectionMode(true, false)
            StatisticsUtils.setEventTodoMoreEdit(context ?: appContext)
            return@setOnMenuItemClickListener true
        }

        //全选按钮
        select.setOnMenuItemClickListener {
            val isSelectAll = getString(R.string.select_all).contentEquals(it.title ?: "")
            todoListViewModel.selectAll(isSelectAll)
            return@setOnMenuItemClickListener true
        }
        //取消按钮
        cancel.setOnMenuItemClickListener {
            doOnCancelEdit()
            return@setOnMenuItemClickListener true
        }
    }

    private fun doOnCancelEdit() {
        todoListViewModel.isDeletingOrHiding = false
        todoListViewModel.setSelectionMode(false, false)
        StatisticsUtils.setEventTodoEditCancel()
    }

    private fun registerContainer() {
        val isFromAppCard =
            IntentParamsUtil.getStringExtra(activity?.intent, PrefUtils.MAIN_ACTION_FROM, "")
        val mIsFromAppCard = !isFromAppCard.isNullOrEmpty()
        todoListViewModel.mShowStartContainerFlag.observe(viewLifecycleOwner) { showStartContainerFlag ->
            AppLogger.BASIC.d(
                TAG,
                "showStartContainerFlag:$showStartContainerFlag mIsFromAppCard:$mIsFromAppCard sharedViewModel.twoPane:${sharedViewModel.twoPane}"
            )
            if (showStartContainerFlag && sharedViewModel.twoPane && mIsFromAppCard && this.activity is MainActivity) {
                val activity = this.activity as MainActivity
                activity.setStartContainerVisible()
            }
        }
    }
    private fun initRecyclerView(savedInstanceState: Bundle?) {
        registerContainer()
        // 1.set recyclerview layout manager
        linearLayoutManager = object : COUILinearLayoutManager(context) {
            override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
                runCatching {
                    super.onLayoutChildren(recycler, state)
                }.onFailure {
                    AppLogger.BASIC.d(TAG, "onLayoutChildren error.", it)
                }
            }
        }
        binding?.todoList?.layoutManager = linearLayoutManager

        // 2.set recyclerview item animator
        val itemAnimator = TodoItemAnimator()
        binding?.todoList?.itemAnimator = itemAnimator
        // 4.set recyclerview adapter
        binding?.todoList?.adapter = adapter
        adapter.setmRecyclerView(binding?.todoList)
        itemTouchHelper = ItemTouchHelper(TodoItemTouchHelperCallBack(adapter))
        itemTouchHelper?.attachToRecyclerView(binding?.todoList)
        if (savedInstanceState != null) {
            binding?.todoList?.viewTreeObserver?.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    binding?.todoList?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                    binding?.todoList?.scrollToPosition(0)
                }
            })
        }
        addOnItemTouchListener()
    }

    private fun addOnItemTouchListener() {
        activity?.let {
            gestureDetector =
                GestureDetector(it, object : GestureDetector.SimpleOnGestureListener() {
                    override fun onLongPress(e: MotionEvent) {
                        super.onLongPress(e)
                        binding?.todoList?.let { list ->
                            val childView = list.findChildViewUnder(e.x, e.y)
                            if (childView != null) {
                                val position =
                                    list.getChildLayoutPosition(childView)
                                val viewHolder =
                                    list.findViewHolderForAdapterPosition(position) as? TodoAdapter.TodoViewHolder

                                viewHolder?.apply {
                                    mItemViewModel?.let { viewModel ->
                                        mBinding?.backgroundItem?.let { backgroundItem ->
                                            viewModel.onItemLongClick(this, backgroundItem, sysDragManager)
                                        }
                                    }
                                }
                                AppLogger.BASIC.d(
                                    TAG,
                                    "onLongPress viewHolder is null ${viewHolder == null} position $position"
                                )
                            }
                            AppLogger.BASIC.d(
                                TAG,
                                "onLongPress childView is null ${childView == null}"
                            )
                        }
                    }

                    override fun onSingleTapUp(e: MotionEvent): Boolean {
                        return super.onSingleTapUp(e)
                    }
                })
        }
        binding?.todoList?.addOnItemTouchListener(object :
            RecyclerView.SimpleOnItemTouchListener() {
            override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                return gestureDetector?.let {
                    it.onTouchEvent(e)
                } ?: super.onInterceptTouchEvent(rv, e)
            }
        })
    }

    private fun initObservers() {
        initShowFinishTodoObserve()
        initSortModeObserver()
        initToDoItemObserver()
        observeTodoSelectionMode()
        observeTodoDragMode()
        setOuterToDoAdapterCallback()
        observeStoragePermission()
        observeCanSaveTodo()
        observeGetFinished()
    }

    private fun observeGetFinished() {
        todoViewModel.getFinished(todoListViewModel.mCurrentDate).observe(viewLifecycleOwner) { toDos: List<ToDo> ->
            AppLogger.BASIC.d(TAG, "getFinished: " + toDos.size)
            updateTitle()
        }
    }

    /**
     * 显示/隐藏已完成，切换数据列表
     */
    private fun initShowFinishTodoObserve() {
        todoListViewModel.mHideFinishedTodo?.observe(viewLifecycleOwner) { hide ->
            AppLogger.BASIC.d(TAG, "initShowFinishTodoObserve hide=$hide")
            if (isEditMode()) {
                sharedViewModel.todoSelectionMode.value = false
                todoListViewModel.selectAll(false)
                todoListViewModel.mPendingDeleteSelectedToDos.value = false
            }
            sharedViewModel.currentTodoType = TODO_ALL
            sharedViewModel.fabAnimatorHelper?.apply {
                changeFloatButtonState(true)
            }
        }
    }

    private fun initToDoItemObserver() {
        todoListViewModel.toDoItems.observe(viewLifecycleOwner) { toDoItems: List<ToDoItem> ->
            if (todoListViewModel.isSortAction || !adapter.hasSubmit) {
                AppLogger.BASIC.d(TAG, "not refresh data by ${todoListViewModel.isSortAction},${adapter.hasSubmit}")
                return@observe
            }
            val hideDoneTodo: Boolean = todoListViewModel.mHideFinishedTodo.value ?: false
            val toDoItemList = getFilterList(toDoItems)
            AppLogger.BASIC.d(
                TAG, "initObservers hideDoneTodo=$hideDoneTodo,toDoItems=${toDoItems.size}, noHeadList=${toDoItemList.size}," +
                        "+currentTodoList=${currentTodoList.size}"
            )
            adapter.setData(toDoItemList, sharedViewModel.twoPane, sharedViewModel.currentTodoType, false)
            loadDataFinished = true
            correctNavigationViewMenuState(currentTodoList)
            if (mIsFirstLoadTodoList) {
                mIsFirstLoadTodoList = false
                resetMainEmptyPage()
            } else {
                resetMainEmptyPage()
            }
            initNotificationAnimator()

            selectItemSize = currentTodoList.count {
                it.isSelected
            }
            updateTitle()
        }
    }

    private fun initSortModeObserver() {
        todoListViewModel.mDragSortMode.observe(viewLifecycleOwner) {
            AppLogger.BASIC.d(TAG, "initObservers mDragSortMode: $it, twoPanel:${sharedViewModel.twoPane}")
            if (sharedViewModel.twoPane) {
                if (it) {
                    adapter.enterDragMode()
                } else {
                    adapter.exitDragMode()
                }
            }
        }
    }

    private fun observeCanSaveTodo() {
        todoListViewModel.canSave.observe(viewLifecycleOwner) {
            todoModalDialog?.updateCanSave(it)
        }
    }

    private fun observeTodoSelectionMode() {
        sharedViewModel.todoSelectionMode.observe(viewLifecycleOwner) { isSelectionMode: Boolean ->
            if (isSelectionMode) {
                isSelectionModeFirstInit = false
            }

            if (isSelectionModeFirstInit) {
                isSelectionModeFirstInit = false
                return@observe
            }

            todoListViewModel.mSelectionMode.value = isSelectionMode

            initToolNavigationMenu()
            updateNavigationViewMenuWithAnim(isSelectionMode)
            toolbarAnimation(isSelectionMode)

            if (isSelectionMode) {
                adapter.enterSelectionMode()
            } else {
                adapter.exitSelectionMode()
            }
        }
    }

    private fun observeTodoDragMode() {
        sharedViewModel.dragSortMode.observe(viewLifecycleOwner) { isDragSortMode ->
            if (isDragSortMode == false) {
                adapter.notifyDataSetChanged()
            }
        }
    }

    private val callback = object : TodoAdapter.Callback {
        override fun onItemClick(item: ToDoItem) {
            if (isEditMode() || !sharedViewModel.twoPane) {
                return
            }
            todoListViewModel.setToDoForEditing(item.toDo)
            showTodoModalDialog()
        }

        override fun onItemLongClick(
            item: ToDoItem,
            viewHolder: RecyclerView.ViewHolder,
            view: View,
            runnable: Runnable?,
            sysDragManager: SysDragManager
        ) {
            if (!sharedViewModel.twoPane) {
                return
            }
            viewHolder.let {
                itemTouchHelper?.startDrag(it)
            }
        }

        override fun onItemChecked(item: ToDoItem, checked: Boolean) {
            AppLogger.BASIC.i(TAG, "onItemChecked")
        }

        override fun onItemDragStateChanged(isDrag: Boolean) {
            linearLayoutManager?.let {
                adapter.notifyDragStateChanged(isDrag, it.findFirstVisibleItemPosition(), it.findLastVisibleItemPosition())
            }
        }

        override fun onDrag(
            item: ToDoItem?,
            view: View?,
            runnable: Runnable?,
            vibrate: Boolean,
            sysDragManager: SysDragManager
        ) {
            AppLogger.BASIC.i(TAG, "onDrag")
        }

        override fun hasSelectionMode(): Boolean {
            return isEditMode()
        }

        override fun onItemMove(sourceTodo: ToDoItem?, targetTodo: ToDoItem?) {
            //not use
        }

        override fun onItemClear(source: RecyclerView.ViewHolder?) {
            //not use
        }

        override fun onItemMoveStart(source: RecyclerView.ViewHolder?) {
            //not use
        }

        override fun onItemMoveEnd(source: RecyclerView.ViewHolder?) {
            updateTitle()
        }

        override fun onGroupItemClick(isUndo: Boolean?) {
            AppLogger.BASIC.d(TAG, "onGroupItemClick $isUndo, type=${sharedViewModel.currentTodoType}")
            val showUndo = isUndo ?: true
            if (showUndo && TODO_DONE == sharedViewModel.currentTodoType) {
                loadTodos(TODO_ALL)
            } else if (!showUndo && TODO_ALL == sharedViewModel.currentTodoType) {
                loadTodos(TODO_DONE)
            } else {
                AppLogger.BASIC.d(TAG, "onGroupItemClick 重复点击")
            }
        }
    }

    private fun setOuterToDoAdapterCallback() {
        todoListViewModel.setOuterToDoAdapterCallback(callback)
    }

    private fun observeStoragePermission() {
        sharedViewModel.storagePermissionDenied.observe(viewLifecycleOwner) { denied: Boolean ->
            if (denied) {
                binding?.todoList?.visibility = View.INVISIBLE
            } else {
                binding?.todoList?.visibility = View.VISIBLE
            }
            resetMainEmptyPage()
        }
    }

    /**
     * 底部删除按钮
     */
    private fun initToolNavigationMenu() {
        AppLogger.BASIC.d(TAG, "initToolNavigationMenu")
        if (!isAdded) {
            return
        }
        if (toolNavigationView == null) {
            toolNavigationView = editMenuStub.value?.inflate() as? COUINavigationView
            toolNavigationView?.setItemLayoutType(COUINavigationView.NAVIGATION_TYPE_TAB)
            toolNavigationView?.setOnItemSelectedListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.todo_delete -> todoListViewModel.pendingDeleteSelectedToDos()
                }
                true
            }
            navigationAnimatorHelper = NavigationAnimatorHelper(requireContext())
            toolNavigationView?.let {
                navigationAnimatorHelper?.initToolNavigationAnimator(it)
            }
        }
    }

    private fun initDialogFactory() {
        dialogClickListener = object : DialogFactory.DialogOnClickListener {
            override fun onDialogClickButton(type: Int, index: Int) {
                AppLogger.BASIC.i(TAG, "onDialogClickButton index:$index")
            }

            override fun onDialogClickPositive(type: Int) {
                when (type) {
                    DialogFactory.TYPE_DIALOG_TODO_DELETE -> {
                        DeleteSoundUtils.playDeleteSound()
                        todoListViewModel.deleteSelectedToDos()
                    }

                    DialogFactory.DIALOG_TYPE_ALERT_WINDOW_PERMISSION_REQUEST -> {
                        val isInMultiWindowMode = activity?.isInMultiWindowMode ?: false
                        UiHelper.turnToManageAppOverlayPage(activity, isInMultiWindowMode)
                        //儿童空间模式下，无法跳转到开启悬浮窗权限的界面
                        if (UiHelper.isChildrenMode() || UiHelper.isFocusMode()) {
                            onDialogClickNegative(type)
                        }
                    }
                }
            }

            override fun onDialogClickNegative(type: Int) {}

            override fun onDialogDismiss(type: Int) {}
        }
        dialogFactory = DialogFactory(activity, dialogClickListener)
    }

    private fun showTipsDialog(type: Int, bundle: Bundle?): Dialog? {
        if (dialogFactory == null) {
            initDialogFactory()
        }
        return dialogFactory?.showDialog(type, bundle)
    }

    private fun updateTitle(forceUpdate: Boolean = false) {
        AppLogger.BASIC.d(TAG, "updateTitle  forceUpdate=$forceUpdate,isAnimating=$isAnimating")
        if (!isAnimating || forceUpdate) {
            if (isEditMode()) {
                binding?.toolbarTodo?.isTitleCenterStyle = true
                updateSelectTitle(selectItemSize, isEditMode())
            } else {
                binding?.toolbarTodo?.isTitleCenterStyle = false
                updateNormalTitle()
            }
            correctToolbarMenu()
        }
    }

    private fun updateNormalTitle() {
        if (isEditMode()) {
            return
        }
        val size = currentTodoList.size
        AppLogger.BASIC.d(TAG, "updateNormalTitle  type=${sharedViewModel.currentTodoType},size=$size, isAnimating=$isAnimating")
        val content = if (sharedViewModel.currentTodoType == TODO_ALL) {
            context?.resources?.getQuantityString(com.oplus.note.baseres.R.plurals.todo_all_count, size, size) ?: ""
        } else {
            context?.resources?.getQuantityString(com.oplus.note.baseres.R.plurals.todo_done_count, size, size) ?: ""
        }
        binding?.toolbarTodo?.title = if (size <= 0) {
            ""
        } else {
            content
        }
    }

    private fun isEditMode(): Boolean {
        return sharedViewModel.todoSelectionMode.value == true
    }

    private fun updateNavigationViewMenuWithAnim(isSelectionMode: Boolean) {
        AppLogger.BASIC.d(TAG, "updateNavigationViewMenuWithAnim  isSelectionMode=$isSelectionMode")
        if (isSelectionMode) {
            toolNavigationView?.inflateMenu(R.menu.navi_menu_todo)
            navigationAnimatorHelper?.showViewTogether(binding?.todoListEditMenuGroup, onStart = {
                toolNavigationView?.doOnNextLayout {
                    binding?.todoList?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = toolNavigationView?.height ?: 0
                        AppLogger.BASIC.d(TAG, "updateNavigationViewMenuWithAnim  updateLayoutParams bottomMargin=${
                            toolNavigationView?.height ?: 0
                        }")
                    }
                }
            }, onEnd = {
                if (todoListViewModel.isDeleteDialogRebuild) {
                    todoListViewModel.pendingDeleteSelectedToDos()
                    todoListViewModel.isDeleteDialogRebuild = false
                }
            })
        } else {
            navigationAnimatorHelper?.hideViewTogether(binding?.todoListEditMenuGroup, onEnd = {
                binding?.todoList?.updateLayoutParams<ConstraintLayout.LayoutParams> { bottomMargin = 0 }
            })
        }
    }

    private fun toolbarAnimation(isSelectionMode: Boolean) {
        AppLogger.BASIC.d(TAG, "toolbarAnimation isSelectionMode=$isSelectionMode")
        val toolbarOutAnimation = ObjectAnimator.ofFloat(binding?.toolbarTodo, "alpha", 1f, 0f).apply {
            doOnEnd {
                if (!isAdded) {
                    return@doOnEnd
                }
                updateTitle(true)
            }
        }
        val toolbarInAnimation = ObjectAnimator.ofFloat(binding?.toolbarTodo, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet()
        toolbarAnimatorSet.duration = ALPHA_DURATION
        toolbarAnimatorSet.interpolator = LinearInterpolator()
        toolbarAnimatorSet.play(toolbarOutAnimation).before(toolbarInAnimation)


        toolbarAnimatorSet.doOnEnd {
            isAnimating = false
        }
        isAnimating = true
        toolbarAnimatorSet.start()
    }

    /**
     * Alpha animation of primary title when switch between normal mode and edit mode
     */
    private fun titleAnimation() {
        val alphaOutAnimation = ObjectAnimator.ofFloat(binding?.toolbarTodo, "alpha", 1f, 0f)
        val alphaInAnimation = ObjectAnimator.ofFloat(binding?.toolbarTodo, "alpha", 0f, 1f)
        val animationSet = AnimatorSet()
        animationSet.interpolator = LinearInterpolator()
        animationSet.duration = ALPHA_DURATION
        animationSet.play(alphaOutAnimation).before(alphaInAnimation)
        animationSet.start()
    }

    /**
     * 更新标题 已选则x项
     */
    private fun updateSelectTitle(selectedSize: Int, isSelectionMode: Boolean) {
        AppLogger.BASIC.d(TAG, "updateSelectTitle  size=$selectedSize, isSelectionMode=$isSelectionMode")
        if (isSelectionMode) {
            if (selectedSize == 0) {
                binding?.toolbarTodo?.title = context?.getText(com.oplus.note.baseres.R.string.memo_select_note) ?: ""
            } else {
                val isAllToDoSelected: Boolean = isAllToDosSelected()
                binding?.toolbarTodo?.title = if (isAllToDoSelected) {
                    getString(com.oplus.note.baseres.R.string.memo_note_select_all)
                } else {
                    getString(com.oplus.note.baseres.R.string.memo_note_select_num, selectedSize.toString())
                }
            }
        }
    }

    private fun correctToolbarMenu() {
        val isSelectionMode = isEditMode()
        AppLogger.BASIC.d(TAG, "correctToolbarMenu isSelectionMode=$isSelectionMode")
        val menu = binding?.toolbarTodo?.menu ?: return
        val isAllToDoSelected: Boolean = isAllToDosSelected()
        //编辑图标
        val editItem = menu.findItem(R.id.edit_todo)
        val cancel = menu.findItem(R.id.cancel)
        val select = menu.findItem(R.id.select_all)
        editItem.isVisible = !isSelectionMode && currentTodoList.isNotEmpty()
        cancel.isVisible = isSelectionMode
        select.isVisible = isSelectionMode
        select.setTitle(if (isAllToDoSelected) R.string.deselect_all else R.string.select_all)
    }

    private fun correctNavigationViewMenuState(items: List<ToDoItem>) {
        var hasSelected = false
        for (item in items) {
            if (item.isSelected) {
                hasSelected = true
                break
            }
        }
        updateTodoDeleteMenuState(hasSelected)
    }

    private fun isAllToDosSelected(): Boolean {
        val notSelect = currentTodoList.any {
            !it.isSelected
        }
        AppLogger.BASIC.d(TAG, "isAllToDosSelected notSelect=$notSelect")
        return !notSelect
    }

    private fun updateTodoDeleteMenuState(hasSelected: Boolean) {
        val item: MenuItem? = toolNavigationView?.menu?.findItem(R.id.todo_delete)
        item?.isEnabled = hasSelected
    }

    private fun initReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_TIME_CHANGED)
        filter.addAction(Intent.ACTION_DATE_CHANGED)
        filter.addAction(Intent.ACTION_TIMEZONE_CHANGED)
        filter.addAction(ACTION_UPDATE_ITEM_EXPIRED)
        ContextCompat.registerReceiver(appContext, timeChangeReceiver, filter, ContextCompat.RECEIVER_EXPORTED)
    }

    private fun initEmptyPage() {
        val stub: ViewStub? = binding?.emptyContentStub?.viewStub
        if (stub != null) {
            emptyContentPage = stub.inflate() as EmptyContentView
            mEmptyContentPageHelper = ImageHelper(requireActivity())
            AppLogger.BASIC.d(TAG, "initEmptyPage:")

            emptyContentPage?.init(
                mEmptyContentPageHelper,
                topOffSize = context?.resources?.getDimensionPixelSize(R.dimen.dp_30) ?: 0,
                bottomOffSize = 0
            )
            emptyContentPage?.setPageClickListener(object : EmptyContentView.EmptyPageClickListener {
                override fun onSwitch() {
                    if (context != null && isPackageDisabled(
                            MbaUtils.PACKAGER_CLOUD,
                            requireContext()
                        )
                    ) {
                        MbaUtils.showMbaCloudDialog(requireContext())
                    } else {
                        NoteSyncProcess.startCloudSettingActivity(context)
                    }
                }
            })
        }
    }

    private fun resetMainEmptyPage() {
        if (binding == null) {
            return
        }

        val isInMultiWindowMode = activity?.isInMultiWindowMode ?: false
        //分屏的时候,如果折叠屏展开且在竖屏情况下,需要显示空页面
        var isFoldingModeOpen = false
        if (isInMultiWindowMode) {
            isFoldingModeOpen = UIConfigMonitor.isFoldingModeOpen(context)
        }

        val isPrivacyDenied = (context == null) || !PrivacyPolicyHelper.isAgreeUserNotice(context)
        if (isPrivacyDenied && (!isInMultiWindowMode || isFoldingModeOpen)) {
            return
        }
        val hasToDos = currentTodoList.isNotEmpty()
        val isNeedHide = (isInMultiWindowMode && !isFoldingModeOpen && !UiHelper.isDevicePad())
        if (!loadDataFinished || hasToDos || isNeedHide) {
            emptyContentPage?.display(EmptyContentView.State.STATE_HIDE, false)
        } else if (OsConfigurations.isMultiSystem || !DeviceInfoUtils.isAppInstalled(
                context,
                MbaUtils.PACKAGER_CLOUD
            )
        ) {
            emptyContentPage?.display(EmptyContentView.State.NO_TODO_CONTENT, false)
        } else {
            if (syncEnable == true) {
                emptyContentPage?.display(EmptyContentView.State.PULL_RECOVERY_TODO, false)
            } else if (ConfigUtils.isUseCloudKit) {
                if (OplusFlexibleWindowManagerProxy.isInFreeFormMode(activity)) {
                    emptyContentPage?.display(EmptyContentView.State.STATE_HIDE, false)
                } else {
                    emptyContentPage?.display(EmptyContentView.State.NO_TODO_CONTENT, false)
                }
            } else {
                emptyContentPage?.display(EmptyContentView.State.SYNC_SWITCH, false)
            }
        }
    }

    private fun showTodoModalDialog() {
        if (!isAdded || activity?.isFinishing == true) {
            return
        }
        if (todoModalDialog != null && todoModalDialog?.isShowing == true) {
            todoModalDialog?.setToolbarTitle()
            return
        }
        todoListViewModel.showTodoEditDialog = true
        val switchColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        todoModalDialog =
            TodoModalDialog(requireActivity(), R.style.ToDoDialogStyle, DialogUseMode.NOTE_INSIDE, switchColor)
        todoModalDialog?.apply {
            mViewModel = todoListViewModel
            mLifecycleOwner = this@TodoListFragment
            setCheckPermissionCallback(object : TodoModalDialog.CheckPermissionCallback {
                override fun showDialog(type: Int, bundle: Bundle?): Dialog? {
                    return showTipsDialog(type, bundle)
                }

                override fun checkPermission(forceReminder: Boolean, function: () -> Unit) {
                    //隐藏输入法
                    FocusInfo.hideSoftInput(activity, <EMAIL>?.decorView)
                    alarmPermissionHelper.checkAlarmPermissions(
                        permissionManager,
                        activity,
                        0L,
                        forceReminder,
                        object : RequestResultCallback {
                            override fun onEnd(granted: Boolean) {
                                function.invoke()

                                if (!granted) {
                                    //未授予权限，显示引导tips
                                    CloudKitInfoController.resetPermissionGuideState()
                                    resetMainEmptyPage()
                                }
                                AppLogger.BASIC.d(TAG, "RequestResultCallback onEnd granted=$granted")
                            }
                        })
                }
            })
            setOnDismissListener {
                clearListenerAndDestroy()
                todoModalDialog = null
            }
        }
        todoModalDialog?.setDialogListener { type ->
            if (CheckNextAlarmUtils.isSpecialPermission(type)) {
                resetMainEmptyPage()
            }
        }
        if (todoModalDialog != null && todoModalDialog?.isShowing != true) {
            todoModalDialog?.show()
        }
    }

    private fun initNotificationAnimator() {
        AppLogger.BASIC.d(TAG, "initNotificationAnimator isNotificationInit=$isNotificationInit")
        if (isNotificationInit) {
            sharedViewModel.notificationUUID.observe(viewLifecycleOwner) { uuid: String? ->
                if (TextUtils.isEmpty(uuid)) {
                    AppLogger.BASIC.d(TAG, "notificationUUID observe, uuid is empty")
                    return@observe
                }
                val position = currentTodoList.indexOfFirst {
                    uuid == it.toDo.localId.toString()
                }
                if (position > -1) {
                    scrollToPosition(position)
                } else {
                    AppLogger.BASIC.d(TAG, "current position $position can not be found")
                }
                AppLogger.BASIC.d(
                    TAG,
                    "notificationUUID observe uuid=$uuid, list.size=${currentTodoList.size}, position=$position"
                )
            }
            isNotificationInit = false
        }
    }

    fun scrollToPosition(position: Int) {
        AppLogger.BASIC.d(TAG, "scrollToPosition $position")
        binding?.todoList?.scrollToPosition(position)
        binding?.todoList?.viewTreeObserver?.addOnGlobalLayoutListener(object :
            OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                (binding?.todoList?.layoutManager as? COUILinearLayoutManager)
                    ?.scrollToPosition(position)
//                    ?.scrollToPositionWithOffset(position, binding?.appBar?.height ?: 0)
                binding?.todoList?.postDelayed({
                    binding?.todoList?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                }, DELAY_REMOVE_ONGLOBAL_LAYOUT)
            }
        })
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (todoModalDialog?.isShowing == false) {
            AppLogger.BASIC.d(TAG, " todo dialog is not show,nothing todo ")
            return
        }
        AppLogger.BASIC.d(TAG, "[F] requestCode $requestCode")
        when (requestCode) {
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_ALARMBUTTON_NOTIFY_CODE -> {
                if (CheckNextAlarmUtils.permisionNotifyAlarmScreenOnGranded(requireContext())) {
                    todoModalDialog?.onRemindAreaClick()
                }
            }

            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_ALARM_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_SCREEN_ON_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_SAVE_NOTIFY_CODE -> {
                if (CheckNextAlarmUtils.permisionNotifyAlarmScreenOnGranded(requireContext())) {
                    if (WidgetUtils.isPrivacyDenied(requireContext())) {
                        Toast.makeText(context, R.string.save_todo_failed, Toast.LENGTH_SHORT).show()
                        return
                    }
                    todoModalDialog?.saveTodo()
                    todoModalDialog?.dismiss()
                }
            }

            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_SCREEN_CODE,
            CommonPermissionUtils.GUIDE_PERMISSIONS_TODO_FORCE_ALERT_CODE -> {
                todoModalDialog?.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    /**
     * 跳转应用详情页面，可手动设置通知权限
     */
    fun toNotificationSetting(activity: Activity?) {
        activity?.apply {
            if (PermissionsBlockedUtils.getCanNavigateToAppPermissions(this) && !com.oplus.note.BuildConfig.isExport) {
                val intent = Intent(PermissionsBlockedUtils.ACTION_SAFE_CENTER_PERMISSION)
                val bundle = Bundle()
                val arrayList = ArrayList<String>()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    arrayList.add(Manifest.permission.POST_NOTIFICATIONS)
                }
                bundle.putStringArrayList(PermissionsBlockedUtils.PERMISSION_LIST, arrayList)
                bundle.putString(
                    PermissionsBlockedUtils.PACKAGE_NAME_STRING,
                    activity.applicationContext.packageName
                )
                intent.putExtras(bundle)
                try {
                    activity.startActivity(intent)
                } catch (e: SecurityException) {
                    AppLogger.BASIC.e(
                        TAG,
                        "${PermissionsBlockedUtils.ACTION_SAFE_CENTER_PERMISSION} permission error  ${e.message}"
                    )
                    PermissionsBlockedUtils.setCanNavigateToAppPermissions(false)
                    defaultToNotificationSetting(this)
                }
            } else {
                defaultToNotificationSetting(this)
            }
        } ?: kotlin.run {
            AppLogger.BASIC.e(TAG, "activity is null")
        }
    }

    private fun defaultToNotificationSetting(activity: Activity) {
        val intent = Intent()
        val packageName: String = activity.applicationContext.packageName
        val extraPackageName = "android.intent.extra.PACKAGE_NAME"
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.parse("package:$packageName")
        intent.putExtra(extraPackageName, packageName)
        kotlin.runCatching {
            this.startActivity(intent)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "package not found:${it.message}}")
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        AppLogger.BASIC.d(TAG, "onConfigurationChanged")
        updateEmptyViewPosition(true)
    }
}