/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: Command.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/4/14
 * * Author: PengFei.Ma
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * PengFei.Ma   2020/4/14      1.0    build this module
 ****************************************************************/

package com.nearme.note.undo

abstract class Command(callback: NotifyCommandCallback) {
    /**操作序号,一次编辑可能对应多个操作，如替换文字，就是删除+插入*/
    var commandId = 0

    /**插入开始位置*/
    var start = 0

    /**插入结束位置.*/
    var end = -1

    /**操作类型*/
    var operatorType = 0

    /**预计文本长度*/
    var count = -1

    /**改变字符*/
    lateinit var targetText: CharSequence

    /**由子类实现撤销操作*/
    abstract fun undo()

    /**由子类实现恢复操作*/
    abstract fun redo()

    interface CommandListener {
        fun onUndo(command: Command)
        fun onRedo(command: Command)
    }
}