/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: RichEditTextCommand.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/4/14
 * * Author: MengHao.He
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * MengHao.He   2020/4/21      1.0    build this module
 ****************************************************************/
package com.nearme.note.undo

import com.oplus.note.logger.AppLogger
import com.nearme.note.activity.edit.NoteInfoAdapter
import com.nearme.note.activity.edit.NoteInfoListView
import com.nearme.note.util.StatisticsUtils

class RichEditTextCommand(
        val mListView: NoteInfoListView,
        private val mPosition: Int, val callback: NotifyCommandCallback) : RichEditCommand(mListView, mPosition, callback) {
    val tag = this.javaClass.simpleName
    override fun undo() {
        AppLogger.BASIC.d(tag, "undo : ${toString()}")
        StatisticsUtils.setEventUndoText()
        handlerText(true)
    }

    override fun redo() {
        AppLogger.BASIC.d(tag, "redo : ${toString()}")
        handlerText(false)
    }

    private fun handlerText(isUndo: Boolean) = getRichEdit(NoteInfoListView.FindEditTextCallBack {
        callback.notifyCommandState(true)
        try {
            it?.let { editText ->
                editText.requestFocus()
                val option = if (isUndo) operatorType == 1 else operatorType != 1
                if (option) {
                    //删除文本
                    editText.editableText?.let { editableText ->
                        //计算删除结束位置
                        if (end == -1) {
                            //判断editText实际文本长度与预计文本长度是否相同（待办中间插入文本导致实际文本长度变化）
                            if (count != -1 && editableText.length != count) {
                                val changeLength = editableText.length - count //待办造成文本增加长度
                                end = start + targetText.length + changeLength

                                if (end < start) {
                                    end = start + targetText.length
                                }
                                AppLogger.BASIC.d(tag, "delete change String ： start $start ,end $end")
                            } else {
                                end = start + targetText.length
                                AppLogger.BASIC.d(tag, "delete start $start ,end $end")
                            }
                        } else {
                            AppLogger.BASIC.d(tag, "delete start $start ,end $end")
                        }

                        if ((end > start) && (editableText.length >= end)) {
                            editableText.delete(start, end)
                        }
                    }
                    editText.setSelection(start)
                } else {
                    //添加
                    editText.editableText?.insert(start, targetText)
                    if (end == -1) {
                        editText.setSelection(start + targetText.length)
                    } else {
                        editText.setSelection(end)
                    }
                }
                mListView.noteInfoAdapter?.selectTitle(editText.position == NoteInfoAdapter.INDEX_TITLE)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            var message = if (isUndo) "undo" else "redo"
            message += " exception : ${e.message}"
            AppLogger.BASIC.e(tag, message)
        }
        callback.notifyCommandState(false)
    })


    override fun toString(): String {
        return "RichEditTextCommand(mPosition=$mPosition, commandId=$commandId, start=$start, end=$end, operatorType=$operatorType, count=$count, targetText=$targetText)"
    }
}