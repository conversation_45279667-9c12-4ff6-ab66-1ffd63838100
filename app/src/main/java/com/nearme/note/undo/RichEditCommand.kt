/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: RichEditCommand.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/4/14
 * * Author: MengHao.He
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * MengHao.He   2020/4/21      1.0    build this module
 ****************************************************************/
package com.nearme.note.undo

import com.nearme.note.activity.edit.NoteInfoListView
import java.lang.ref.WeakReference

abstract class RichEditCommand(
        mListView: NoteInfoListView,
        private val mPosition: Int, callback: NotifyCommandCallback) : Command(callback) {
    private var mWeakListView: WeakReference<NoteInfoListView> = WeakReference(mListView)
    fun getRichEdit(callback: NoteInfoListView.FindEditTextCallBack) {
        mWeakListView.get()?.getEditTextForPosition(mPosition, callback)
    }
}