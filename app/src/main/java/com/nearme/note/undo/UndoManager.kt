/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: UndoManager.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/4/14
 * * Author: PengFei.Ma
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * PengFei.Ma   2020/4/14      1.0    build this module
 ****************************************************************/
package com.nearme.note.undo

import com.oplus.note.logger.AppLogger
import com.nearme.note.util.StatisticsUtils
import java.util.Stack

@Deprecated("")
open class UndoManager : NotifyCommandCallback {
    companion object {
        const val INVALID_COMMAND_ID = -1
        const val HISTORY_SIZE = 50
        const val TAG = "UndoManager"

        interface StatusCallBackListener {
            fun statusChange(undoEnable: Boolean, redoEnable: Boolean);
        }
    }

    private var mCommandId = 0
    private val mUndoStack: Stack<Command> = Stack()
    private val mRedoStack: Stack<Command> = Stack()
    private var mHistorySize = HISTORY_SIZE
    private var mStatusCallBackListener: StatusCallBackListener? = null
    private var mUndoCommandIdCount = 0
    private var mRedoCommandIdCount = 0

    fun setStatusCallBackListener(statusCallBackListener: StatusCallBackListener) {
        mStatusCallBackListener = statusCallBackListener
    }

    fun getCommandId() = mCommandId++

    @Volatile
    var inUndo = false
        set(value) {
            field = value
            AppLogger.BASIC.d(TAG, "isUndo = $field")
        }

    /**
     * Performs undo on the topmost item in the undo stack
     */
    fun undo() {
        if (inUndo) return
        undoInternal()
    }

    private fun undoInternal() {
        if (isUndoAvailable()) {
            val command = mUndoStack.pop()
            AppLogger.BASIC.d(TAG, "undo commandId is " + command.commandId + " ,mCommandIdCount is $mUndoCommandIdCount")
            mRedoStack.push(command)
            command.undo()

            if (mUndoStack.isEmpty()) {
                mUndoCommandIdCount = 0
                mRedoCommandIdCount++
            } else if (mUndoStack.peek().commandId != command.commandId) {
                mUndoCommandIdCount--
                mRedoCommandIdCount++
            }

            while (!mUndoStack.empty() && mUndoStack.peek().commandId == command.commandId) {
                undoInternal()
            }
            if (mHistorySize == mRedoCommandIdCount) {
                AppLogger.BASIC.d(TAG, "undo cap click")
                StatisticsUtils.setEventUndoCapClick()
            }
            AppLogger.BASIC.d(TAG, "undo commandId is " + command.commandId + " end, mCommandIdCount is $mUndoCommandIdCount")
        }
        mStatusCallBackListener?.statusChange(isUndoAvailable(), isRedoAvailable())
    }

    fun redo() {
        if (inUndo) return
        redoInternal()
    }

    private fun redoInternal() {
        if (isRedoAvailable()) {
            val command = mRedoStack.pop()
            addToUndoStack(command, false)
            command.redo()

            if (mRedoStack.isEmpty()) {
                mRedoCommandIdCount = 0
            } else if (mRedoStack.peek().commandId != command.commandId) {
                mRedoCommandIdCount--
            }
            while (!mRedoStack.empty() && mRedoStack.peek().commandId == command.commandId) {
                redoInternal()
            }
            if (mHistorySize == mUndoCommandIdCount) {
                AppLogger.BASIC.d(TAG, "redo cap click")
                StatisticsUtils.setEventRedoCapClick()
            }
        }
        mStatusCallBackListener?.statusChange(isUndoAvailable(), isRedoAvailable())
    }

    private fun resetRedoStack() {
        mRedoStack.clear()
        mRedoCommandIdCount = 0
    }

    fun clear() {
        mUndoCommandIdCount = 0
        mRedoCommandIdCount = 0
        mRedoStack.clear()
        mUndoStack.clear()
    }

    fun isUndoAvailable(): Boolean {
        return mUndoStack.size > 0
    }

    fun isRedoAvailable(): Boolean {
        return mRedoStack.size > 0
    }

    /**
     * Returns true if we are currently inside of an undo/redo operation.  This is
     * useful for editors to know whether they should be generating new undo state
     * when they see edit operations happening.
     */
    fun isInUndo(): Boolean {
        return inUndo
    }

    private fun addToUndoStack(command: Command, shoulResetRedoStack: Boolean) {
        var preCommandId = INVALID_COMMAND_ID

        if (mUndoStack.isNotEmpty()) {
            preCommandId = mUndoStack.peek().commandId
        }

        if (command.commandId != preCommandId) {
            mUndoCommandIdCount++
        }

        mUndoStack.push(command)

        if (reachMaxUndoStackSizeLimit()) {
            val removedCount = forgetUndos(mUndoCommandIdCount - mHistorySize)
            mUndoCommandIdCount -= removedCount
        }

        if (shoulResetRedoStack) {
            resetRedoStack()
        }

        AppLogger.BASIC.d(TAG, "addToUndoStack preCommandId is $preCommandId" +
                ", $command ,mCommandIdCount = $mUndoCommandIdCount")
        mStatusCallBackListener?.statusChange(undoEnable = true, redoEnable = false)
    }

    fun addToUndoStack(command: Command) {
        addToUndoStack(command, true)
    }

    /**
     * Set the maximum number of undo states that will be retained.
     */
    fun setHistorySize(size: Int) {
        mHistorySize = size
        val undoCount = countUndos()

        if (mHistorySize in 0 until undoCount) {
            forgetUndos(undoCount - mHistorySize)
        }
    }

    /**
     * Return the number of undo states on the undo stack.
     */
    private fun countUndos(): Int {
        var preCommandId = INVALID_COMMAND_ID
        var currCommandId: Int
        var count = 0
        var i = 0
        var command: Command
        while (i < mUndoStack.size) {
            command = mUndoStack[i]
            currCommandId = command.commandId

            if (currCommandId != preCommandId) {
                count++
                preCommandId = currCommandId
            }

            i++
        }

        return count
    }

    private fun forgetUndos(count: Int): Int {
        if (count <= 0) {
            return 0
        }

        var commandId: Int
        var nextCommandId: Int
        var removed = 0

        while (mUndoStack.size > 0 && removed < count) {
            commandId = mUndoStack[0].commandId

            nextCommandId = if (mUndoStack.size > 1) {
                mUndoStack[1].commandId
            } else {
                Int.MAX_VALUE
            }

            mUndoStack.removeElementAt(0)

            if (commandId != nextCommandId) {
                removed++
            }
        }

        return removed
    }

    override fun notifyCommandState(start: Boolean) {
        inUndo = start
    }

    open fun reachMaxUndoStackSizeLimit(): Boolean {
        return mHistorySize in 0 until mUndoCommandIdCount
    }

    fun getUndoCommandsCount(): Int {
        return mUndoCommandIdCount;
    }

    fun getRedoCommandsCount(): Int {
        return mRedoCommandIdCount
    }

    fun getTopUndoCommand(): Command {
        return mUndoStack.peek()
    }
}