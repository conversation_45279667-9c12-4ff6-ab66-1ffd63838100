/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: RichEditTodoClickCommand.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/4/14
 * * Author: MengHao.He
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * MengHao.He   2020/4/21      1.0    build this module
 ****************************************************************/
package com.nearme.note.undo

import com.oplus.note.logger.AppLogger
import com.nearme.note.activity.edit.NoteInfoListView
import com.nearme.note.editor.span.CheckableSpan

class RichEditTodoClickCommand(
        mListView: NoteInfoListView,
        private val mPosition: Int, val callback: NotifyCommandCallback) : RichEditCommand(mListView, mPosition, callback) {

    val tag = this.javaClass.simpleName
    override fun undo() {
        AppLogger.BASIC.d(tag, "undo : ${toString()}")
        todoClick()
    }

    override fun redo() {
        AppLogger.BASIC.d(tag, "redo : ${toString()}")
        todoClick()
    }

    private fun todoClick() {
        getRichEdit(NoteInfoListView.FindEditTextCallBack { editText ->
            callback.notifyCommandState(true)
            try {
                editText?.editableText?.getSpans(start, end, CheckableSpan::class.java)?.forEach { checkableSpan: CheckableSpan? ->
                    checkableSpan?.onClick()
                }
            } catch (e: Exception) {
                AppLogger.BASIC.e(tag, "todoClick exception : ${e.message}")
            }
            callback.notifyCommandState(false)
        })

    }

    override fun toString(): String {
        return "RichEditTodoClickCommand(mPosition=$mPosition, commandId=$commandId, start=$start, end=$end, operatorType=$operatorType, count=$count )"
    }
}