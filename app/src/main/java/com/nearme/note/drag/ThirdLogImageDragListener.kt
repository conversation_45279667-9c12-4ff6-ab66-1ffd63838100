/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - ThirdLogImageDragListener.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/2/8
 * * Author: <EMAIL>
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  <EMAIL>      2024/2/8         1.0
 **************************************************************************/
package com.nearme.note.drag

import android.annotation.SuppressLint
import android.content.Intent
import android.text.TextUtils
import android.view.DragEvent
import android.view.View
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.thirdlog.ThirdLogDetailFragment
import com.nearme.note.activity.richedit.thirdlog.ThirdLogImageAdapter
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.util.filesDirAbsolutePath
import com.oplus.note.NoteFileProvider
import com.oplus.note.R
import com.oplus.note.data.third.ThirdLogScreenShot
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.toast

class ThirdLogImageDragListener(
    private val mAdapter: ThirdLogImageAdapter
) : View.OnDragListener {

    companion object {
        const val TAG = "ThirdLogImageDragListener"
        var lastLocalState: Any? = null
    }

    /**
     *  拖动数据
     */
    private var mDragData: ThirdLogScreenShot? = null
    private var mDragView: DragView? = null

    private var mNoteId: String? = null
    private val mFragment: ThirdLogDetailFragment = mAdapter.fragment

    fun setNoteId(noteId: String) {
        mNoteId = noteId
        AppLogger.BASIC.d(TAG, "mNoteId:$mNoteId")
    }

    override fun onDrag(v: View, event: DragEvent): Boolean {
        val dragEvent = event.action
        val clipData = event.clipData
        when (dragEvent) {
            /**
             * 拖拽结束，当前是有效拖拽,会通知外部刷新数据
             */
            DragEvent.ACTION_DROP -> {
                AppLogger.BASIC.d(TAG, "ACTION_DROP")
                // 待确认是否需要处理
            }
            /**
             * 正在拖拽
             */
            DragEvent.ACTION_DRAG_LOCATION -> {
                AppLogger.BASIC.d(TAG, "ACTION_DRAG_LOCATION")
                // 待确认是否需要处理
            }
            /**
             * 拖拽开始
             */
            DragEvent.ACTION_DRAG_STARTED -> {
                AppLogger.BASIC.d(TAG, "ACTION_DRAG_STARTED")
                // 待确认是否需要处理
            }
            /**
             * 拖拽结束，不管是否是有效拖拽都会回调
             */
            DragEvent.ACTION_DRAG_ENDED -> {
                AppLogger.BASIC.d(TAG, "ACTION_DRAG_ENDED result:${event.result}")
                if (!event.result) {
                    showDragResult(event.localState)
                }
                mDragView?.let {
                    it.updateDragUi(false)
                    onDragEnd()
                } ?: onDragEnd()
            }
        }
        return true
    }

    fun onDragStart(data: ThirdLogScreenShot, position: Int, view: DragView) {
        AppLogger.BASIC.d(TAG, "onDragStart")
        mDragData = data
//        mDataPosition = position
        mDragView = view
        mDragView?.run {
            view.updateDragUi(true)
        }
//        onViewModeState(true, viewType)
    }

    fun onDragEnd() {
        AppLogger.BASIC.d(TAG, "onDragEnd")
        revokePermission()
        mDragData = null
//        mDataPosition = null
        mDragView = null
        mAdapter.sysDragManager.unregisterSysDragReceiver()
        if (DragClipDataHelper.mIsDragAndDrop) {
//            onViewModeState(false, RichAdapter.VIEW_TYPE_UNKNOWN)
        }
    }

    @SuppressLint("WrongConstant")
    private fun revokePermission() {
        mFragment.context?.let {
            val imgPath =
                "${MyApplication.appContext.filesDirAbsolutePath()}/$mNoteId/${mDragData?.attachmentId}${ThumbFileConstants.THUMB}"
            if (!TextUtils.isEmpty(imgPath)) {
                val uriForCommonFile = NoteFileProvider.getUriForCommonFile(it, imgPath)
                if (uriForCommonFile != null) {
                    kotlin.runCatching {
                        it.revokeUriPermission(
                            RichDragListener.WECHAT_PACKAGE_NAME,
                            uriForCommonFile,
                            Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
                        )
                    }.onFailure {
                        AppLogger.BASIC.e(RichDragListener.TAG, "revokeUriPermission err $it")
                    }
                }
            }
        }
    }

    private fun showDragResult(localState: Any?) {
        localState?.let {
            if (it != lastLocalState) {
                lastLocalState = localState
                mFragment.toast(R.string.drag_not_consume)
            }
        }
    }
}