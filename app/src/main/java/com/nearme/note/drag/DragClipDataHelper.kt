/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * File: - RichDragListenerTest.kt
 * Description:
 * Version: 1.0
 * Date : 2023/3/20
 * Author: W9039628
 *
 * ---------------------Revision History: ---------------------
 * <author> <data>   <version>    <desc>
 * ------------------------------------------------------------
 * W9039628      2023/3/20      1.0     create Notes
</desc></version></data></author> */
package com.nearme.note.drag

import android.content.ClipData
import android.content.Context
import com.nearme.note.activity.richedit.DataOperatorController
import com.nearme.note.activity.richedit.RichAdapter
import com.nearme.note.activity.richedit.RichDataClipboardManager
import com.nearme.note.activity.richedit.RichDataClipboardManager.getAttachmentPicData
import com.nearme.note.activity.richedit.RichDataClipboardManager.getCardData
import com.nearme.note.activity.richedit.RichDataClipboardManager.getDefaultData
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.entity.findFirstIdentifyVoiceItemIndex
import com.nearme.note.activity.richedit.entity.findSpeechAudioItemIndex
import com.nearme.note.activity.richedit.webview.WVDragAndDropHelper
import com.nearme.note.model.getFileFormat
import com.oplus.note.NoteFileProvider
import com.oplus.note.data.third.ThirdLogScreenShot
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.utils.ShareHelper.extWithDisplayName

/**
 * 管理Clipdata  RichData.Data
 * */
object DragClipDataHelper {
    private const val TAG = "DragClipDataHelper"
    const val TYPE_DATA = "type_data"
    const val VIEW_D_TYPE = "view_type"
    const val NOT_FIND = -1
    const val TYPE_IMG = 0
    const val TYPE_CARD = 1
    const val DATA_FROM_NOTE = "data_from_note"

    var mIsDragAndDrop: Boolean = false  //true 为应用内拖拽， false 拖拽结束或 全局拖拽
    var VIEW_TYPE: Int = 0  //  2 text 3 img 4 voice  6  card

    fun setStateAndType(onDrap: Boolean, type: Int) {
        mIsDragAndDrop = onDrap
        VIEW_TYPE = type
    }

    @JvmStatic
    fun getClipDataFromData(context: Context?, data: Data, note: RichData? = null, viewType: Int = -1): ClipData? {
        AppLogger.BASIC.d(TAG, "getClipDataFromData: viewType=$viewType")
        if (context == null) {
            AppLogger.BASIC.d(TAG, "DragClipDataHelper context == null")
            return null
        }
        var dragType = RichDataClipboardManager.NOT_FIND    //  附件资源  的类型
        var subAttachment: Attachment? = null
        when (viewType) {
            RichAdapter.DRAG_TYPE_RECORD -> {
                dragType = Attachment.TYPE_VOICE
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_VOICE)
            }
            RichAdapter.DRAG_TYPE_SPEECH_AUDIO -> {
                if (RichData.isIdentityVoiceItem(data.attachment?.attachmentId, note?.subAttachments)) {
                    dragType = Attachment.TYPE_IDENTIFY_VOICE
                    subAttachment = note?.findSubAttachment(data, Attachment.TYPE_IDENTIFY_VOICE)
                } else {
                    dragType = Attachment.TYPE_SPEECH_AUDIO
                    subAttachment = note?.findSubAttachment(data, Attachment.TYPE_SPEECH_AUDIO)
                }
            }
            RichAdapter.VIEW_TYPE_VIDEO -> {
                dragType = Attachment.TYPE_VIDEO
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_VIDEO)
            }
            RichAdapter.VIEW_TYPE_AUDIO -> {
                dragType = Attachment.TYPE_EXTERNAL_AUDIO
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_EXTERNAL_AUDIO)
            }
            RichAdapter.VIEW_TYPE_IMAGE -> {
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_PAINT)
                if (subAttachment != null) {
                    dragType = Attachment.TYPE_PAINT
                }
            }
            RichAdapter.VIEW_TYPE_SUMMARY_DOCUMENT -> {
                dragType = Attachment.TYPE_FILE_CARD
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_FILE_CARD)
            }
            RichAdapter.VIEW_TYPE_FILE -> {
                dragType = Attachment.TYPE_FILE_CARD
                subAttachment = note?.findSubAttachment(data, Attachment.TYPE_FILE_CARD)
            }
        }
        // 用于跨笔记拖拽时使用
        val imgPath = data.attachment?.absolutePath(context) ?: ""
        AppLogger.BASIC.d(TAG, "getClipDataFromData")
        return when {
            RichData.isImageItem(data) -> {
                RichDataClipboardManager.copyAttachmentFromDrag(
                    context,
                    data,
                    note,
                    imgPath,
                    subAttachment,
                    dragType
                )
            }
            RichData.isCardType(data) -> RichDataClipboardManager.cardToDragString(data)
            else -> null
        }.also { d ->
            d?.description?.extras?.putBoolean(DATA_FROM_NOTE, true)
        }
    }

    @JvmStatic
    fun getClipDataFromThirdLog(context: Context?, data: ThirdLogScreenShot, noteId: String): ClipData? {
        AppLogger.BASIC.d(TAG, "getClipDataFromThirdLog")
        if (context == null) {
            AppLogger.BASIC.d(TAG, "DragClipDataHelper context == null")
            return null
        }

        val dragType = Attachment.TYPE_LRC_PICTURE
        val attachment = Attachment(data.attachmentId, noteId, Attachment.TYPE_LRC_PICTURE)

        val imgPath = attachment.absolutePath(context)
        AppLogger.BASIC.d(TAG, "getClipDataFromData")

        return RichDataClipboardManager.copyAttachmentFromImageDrag(context, attachment, imgPath, dragType)
    }

    /**
     * @return null is not import data pasted
     */
    @JvmStatic
    fun getDataFromClipData(clipData: ClipData): Data? {
        return clipData?.run {
            if (itemCount > 0) {
                val text = getItemAt(0)?.text ?: ""
                description?.extras?.run {
                    when (getInt(TYPE_DATA, NOT_FIND)) {
                        TYPE_IMG -> getAttachmentPicData()
                        TYPE_CARD -> getCardData()
                        else -> getDefaultData(text)
                    }
                } ?: getDefaultData(text)
            } else {
                AppLogger.BASIC.d(TAG, "itemCount <=0")
                null
            }
        }
    }

    /**
     * 获取拖动的块的数据 针对于笔记内
     */
    @JvmStatic
    fun getBlockNoteDataFromClipData(clipData: ClipData): String? {
        //因为内部拖拽的时候 是将数据放置在第一个和最后一个
        return clipData.run {
            if (itemCount > 0) {
                //如果第一个为空 那么就取最后一个
                getItemAt(0).htmlText ?: getItemAt(itemCount - 1).htmlText
            } else {
                return null
            }
        }
    }

    @JvmStatic
    fun getViewTypeFromClipData(clipData: ClipData): Int {
        return clipData.run {
            if (itemCount > 0) {
                val text = getItemAt(0)?.text ?: ""
                description?.extras?.run {
                    getInt(VIEW_D_TYPE, -1)
                } ?: -1
            } else {
                -1
            }
        }
    }

    @JvmStatic
    fun getDragBlockRecordUriPath(
        context: Context,
        richData: RichData,
        attachId: String
    ): String? {
        val viewType =
            if (richData.findSpeechAudioItemIndex() != -1 || richData.findFirstIdentifyVoiceItemIndex() != -1) {
                //通话摘要音频文件类型
                WVDragAndDropHelper.DRAG_TYPE_SPEECH_AUDIO
            } else {
                //外销音频文件类型
                WVDragAndDropHelper.DRAG_TYPE_RECORD
            }
        WVDragAndDropHelper.getDataAndAttachmentType(
            richData, viewType, attachId
        ).first?.apply {
            richData.findAudioAttachment(this)?.let {
                val displayName = it.fileName ?: "${
                    DataOperatorController.getSaveShareAudioFileName(
                        richData, it
                    )
                }.${it.getFileFormat()}"
                return NoteFileProvider.getUriForCommonFile(context, it.absolutePath(context))
                    .extWithDisplayName(displayName).toString()
            }
        }
        return null
    }
}