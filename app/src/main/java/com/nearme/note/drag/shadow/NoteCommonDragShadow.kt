/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - NoteCommonDragShadow.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/06/07
 ** Author: lin<PERSON><PERSON>@oppo.com
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  80262777       2023/06/07      1.0     create file
 ****************************************************************/
package com.nearme.note.drag.shadow

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Point
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.reddot.COUIHintRedDot
import com.coui.appcompat.roundRect.COUIRoundDrawable
import com.nearme.note.drag.ShadowContentHelper
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy

@SuppressLint("InflateParams")
class NoteCommonDragShadow(
    view: View,
    private val dragDropCount: Int,
    hasRadius: Boolean,
    isTodoItem: Boolean,
    shadowRadius: Float? = null,
    parentScale: Float = 1.0F,
    bgColor: Int? = null,
    path: String? = null,
    srcRect: Rect? = null,
    capturedElement: String? = null,
    maxDropCount: Int? = null, //当设置最大数量时  超出后就会显示为多少+
) :
    View.DragShadowBuilder(view) {


    private val scale: Float
    private val width: Int
    private val height: Int
    private var layoutPadding: Float
    private val layoutMarginEnd: Float
    private val layoutMarginTop: Int
    private var maxDropCount: Int? = null//最大显示的数量
    private var mShadowView: View

    companion object {
        private const val TAG = "NoteCommonDragShadow"
    }


    init {
        // 计算shadow大小
        val srcValidWidth = srcRect?.width() ?: view.width
        val srcValidHeight = srcRect?.height() ?: view.height
        val needCut = ShadowContentHelper.getScale(view.context, (srcValidWidth * parentScale).toInt(),
            (srcValidHeight * parentScale).toInt()).also {
            scale = parentScale * it.first
        }.second
        layoutPadding =
            view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_padding)
                .toFloat()
        if (scale > 1.0F) {
            layoutPadding *= scale
        }
        layoutMarginEnd =
            view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_margin_end)
                .toFloat()
        layoutMarginTop =
            view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_margin_top)
        this.maxDropCount = maxDropCount

        val rootView =
            LayoutInflater.from(view.context).inflate(R.layout.drawshadow_item_layout, null)
        rootView.findViewById<ShadowWrapperView>(R.id.shadow_content).run {
            AppLogger.BASIC.d(TAG, "init: hasRadius=$hasRadius, needCut=$needCut")
            val shadowContent = if (hasRadius && (!needCut)) {
                if (isTodoItem && OplusBuildProxy.isAboveOS160()) {
                    val contentBitmap = ShadowContentHelper.getBimapFromViewWithRadius(
                        view = view,
                        scale = scale,
                        contentRect = ShadowContentHelper.getShowRect(view, parentScale, srcRect),
                        radius = shadowRadius ?: view.context.resources.getDimensionPixelOffset(com.support.appcompat.R.dimen.coui_round_corner_m)
                            .toFloat(),
                        path = path,
                        capturedElement = capturedElement,
                    )
                    BitmapDefaultShadowContent(view, contentBitmap, dragDropCount, true)
                } else {
                    ViewDefaultShadowContent(view, scale, dragDropCount)
                }
            } else {
                val contentBitmap = ShadowContentHelper.getBimapFromViewWithRadius(
                    view = view,
                    scale = scale,
                    contentRect = ShadowContentHelper.getShowRect(view, parentScale, srcRect),
                    radius = shadowRadius ?: view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_blur_radius).toFloat(),
                    path = path,
                    capturedElement = capturedElement,
                )
                BitmapDefaultShadowContent(view, contentBitmap, dragDropCount, isTodoItem)
            }
            if (bgColor != null) {
                shadowContent.bgColor = bgColor
            }
            if (shadowRadius != null) {
                shadowContent.contentShadowRadius = shadowRadius
            }
            updateShadowContent(shadowContent)
            <EMAIL> =
                ((shadowContent.getScale() * shadowContent.getContentWidth())
                        + (2 * layoutPadding)
                        + layoutMarginEnd).toInt()
            <EMAIL> =
                ((shadowContent.getScale() * shadowContent.getContentHeight())
                        + (2 * layoutPadding)
                        + layoutMarginTop + getItemsExtraHeight()).toInt()
        }

        mShadowView = rootView
        // 低于16.0的版本自行绘制角标
        if (!OplusBuildProxy.isAboveOS160()) initCountView(rootView)
        // Important for certain APIs
        rootView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        AppLogger.BASIC.d(TAG, "init w=$width h=$height s=$scale vw=${view.width} vh=${view.height} oriScale=$parentScale")
    }

    override fun onProvideShadowMetrics(outShadowSize: Point, outShadowTouchPoint: Point) {
        super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint)
        outShadowSize.set(width, height)
        AppLogger.BASIC.d(TAG, "onProvideShadowMetrics w=$width h=$height")
        outShadowTouchPoint.set(width / 2, height / 2)
    }

    override fun onDrawShadow(canvas: Canvas) {
        val r = canvas.clipBounds
        AppLogger.BASIC.d(TAG, "onDrawShadow $r")

        val shadowView = mShadowView

        // Calling measure is necessary in order for all child views to get correctly laid out.
        shadowView.measure(
            View.MeasureSpec.makeMeasureSpec(r.right - r.left, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(r.bottom - r.top, View.MeasureSpec.EXACTLY)
        )
        shadowView.findViewById<ShadowWrapperView>(R.id.shadow_content).let {
            it.measureShadowBg()
            shadowView.layout(r.left, r.top, r.right, r.bottom)
            shadowView.draw(canvas)
            // 仅16.0以下版本做回收处理
            if (!OplusBuildProxy.isAboveOS160()) it.shadowContent?.release()
        }
    }

    /**
     * 获取实际拖拽图宽度
     */
    fun getRealWidth(): Int {
        return width
    }

    private fun initCountView(shadowView: View) {
        val countView: COUIHintRedDot =
            shadowView.findViewById(R.id.dragshadow_item_count) ?: return
        countView.pointMode = COUIHintRedDot.POINT_WITH_NUM_MODE
        if (dragDropCount > 1) {
            countView.pointNumber = dragDropCount
            countView.visibility = View.VISIBLE
            if (maxDropCount != null && dragDropCount > maxDropCount!!) {
                countView.pointText = "$maxDropCount+"
            }
        } else {
            countView.visibility = View.GONE
        }
    }
}

abstract class DefaultShadowContent(
    private val contentContext: Context,
    private val contentScale: Float,
    private val contentItemCount: Int,
) : ShadowWrapperView.ShadowContent {
    var contentShadowRadius: Float = contentContext.resources
        .getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius).toFloat()
    var contentShadowColor: Int = contentContext.getColor(R.color.drag_shadow_item_shadow_color)
    var contentShadowBlur: Float = contentContext.resources.getDimensionPixelOffset(R.dimen.drag_shadow_blur_radius).toFloat()
    var bgColor: Int = contentContext.getColor(R.color.drag_shadow_bg_color)
    var drawContent: Boolean = false
    override fun getShadowRadius() = contentShadowRadius

    override fun getShadowColor() = contentShadowColor

    override fun getScale() = contentScale

    override fun getContext() = contentContext

    override fun getItemCount() = contentItemCount

    override fun getShadowBlur() = contentShadowBlur
    override fun getContentBgColor() = bgColor
    override fun isDrawContentBg() = drawContent
}

class ViewDefaultShadowContent(
    private val vw: View,
    scale: Float,
    itemCount: Int,
) : DefaultShadowContent(
    vw.context, scale, itemCount
) {
    init {
        drawContent = (vw as? ViewGroup)?.getChildAt(0)?.background !is COUIRoundDrawable
    }
    override fun drawContent(canvas: Canvas) {
        vw.draw(canvas)
    }

    override fun getContentWidth() = vw.width.toFloat()
    override fun getContentHeight() = vw.height.toFloat()
}

class BitmapDefaultShadowContent(
    view: View,
    private val bitmap: Bitmap,
    itemCount: Int,
    isTodoItem: Boolean
) : DefaultShadowContent(view.context, 1.0F, itemCount) {
    init {
        if (isTodoItem) {
            drawContent = (view as? ViewGroup)?.getChildAt(0)?.background !is COUIRoundDrawable
        }
    }
    override fun drawContent(canvas: Canvas) {
        canvas.drawBitmap(bitmap, 0F, 0F, null)
    }

    override fun getContentWidth() = bitmap.width.toFloat()
    override fun getContentHeight() = bitmap.height.toFloat()

    override fun release() {
        if (!bitmap.isRecycled) bitmap.recycle()
    }
}