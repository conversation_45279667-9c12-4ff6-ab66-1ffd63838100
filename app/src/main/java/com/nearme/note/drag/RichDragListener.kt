package com.nearme.note.drag

import com.oplus.note.R

/**
 * view 的拖拽监听
 */
class RichDragListener {
    companion object {
        const val TAG = "RichDragListener"
        const val TAG_DRAG_IN_VIEW = R.id.tag_drag_on_view
        const val WHAT_SCROLL_UP = 1
        const val WHAT_SCROLL_DOWN = 2
        private const val HOT_CLICK_WIDTH_RATIO = 0.5f
        const val DISTANCES_MIN_SCROLL_BY = 20
        private const val HOTSPACXE_DEF = 50F
        private const val SPEED_SCAL = 0.6f
        const val WECHAT_PACKAGE_NAME = "com.tencent.mm"
    }
}
