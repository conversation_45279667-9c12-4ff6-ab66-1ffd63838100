/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - NoteListDragHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/06/07
 ** Author: lin<PERSON><PERSON>@oppo.com
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  80262777       2023/06/07      1.0     create file
 ****************************************************************/
package com.nearme.note.drag

import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Environment
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import com.nearme.note.activity.list.NoteViewHolder
import com.nearme.note.activity.list.entity.ToDoItem
import com.nearme.note.drag.shadow.NoteCommonDragShadow
import com.nearme.note.export.attachmentSize
import com.nearme.note.remind.RepeatDataHelper
import com.nearme.note.remind.RepeatManage
import com.nearme.note.util.OplusDateUtils
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.NoteFileProvider
import com.oplus.note.R
import com.oplus.note.common.SysDragConstants
import com.oplus.note.export.ExportAgentFactory
import com.oplus.note.export.doc.DocUtils
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.utils.IntegrationFeatures
import com.oplus.note.utils.SysDragManager
import com.oplus.note.view.TextViewSnippet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.Locale

object NoteListDragHelper {

    const val TAG = "NoteListDragHelper"
    internal const val DRAG_LABEL = "Note"
    internal const val DRAG_FLAG =
        View.DRAG_FLAG_GLOBAL or View.DRAG_FLAG_OPAQUE or View.DRAG_FLAG_GLOBAL_URI_READ or
                View.DRAG_FLAG_GLOBAL_URI_WRITE or View.DRAG_FLAG_GLOBAL_PERSISTABLE_URI_PERMISSION
    private const val MAX_DRAG_COUNT = 99
    private const val WECHAT_PACKAGE_NAME = "com.tencent.mm"

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    suspend fun startDragNoteItem(
        context: Context,
        notes: List<RichNoteWithAttachments?>,
        container: View?,
        viewHolder: NoteViewHolder?,
        dragView: View,
        sysDragManager: SysDragManager,
        dragResultCallback: DragResultCallback
    ) {
        if (!checkCanDrag(context, notes, dragView, dragResultCallback)) {
            return
        }
        val enough = DocUtils.checkNoteDragStorageEnough(context, notes.attachmentSize())
        if (!enough) {
            Toast.makeText(context, context.getString(R.string.doc_export_storage_not_enough), Toast.LENGTH_LONG).show()
            StatisticsUtils.setEventNoteDragOut(
                notes.size,
                StatisticsUtils.DRAG_RESULT_STORAGE_NOT_ENOUGH
            )
            AppLogger.BASIC.d(TAG, "Drag Storage not enough")
            dragResultCallback.onDragResult(false)
            return
        }

        var dragClipData: ClipData? = null
        DocUtils.clearDuplicateTitleMap()
        notes.forEach { note ->
            note?.let {
                val dataUri = withContext(Dispatchers.IO) {
                    NoteFileProvider.getDragAndDropFileUri(context.applicationContext, it)
                }
                context.grantUriPermission(
                    WECHAT_PACKAGE_NAME, dataUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
                )
                if (dragClipData == null) {
                    dragClipData =
                        ClipData.newUri(context.contentResolver, DRAG_LABEL, dataUri)
                } else {
                    dragClipData?.addItem(ClipData.Item(dataUri))
                }
            }
        }
        if (dragClipData != null) {
            AppLogger.BASIC.d(TAG, "startDragAndDrop")
            val shadowView: View? = initAISpaceView(dragView, container, viewHolder)
            val startTime = System.currentTimeMillis()
            val commonDragShow = shadowView?.let { createDragShadow(it, notes.size, false, false) }
            dragView.startDragAndDrop(
                dragClipData, commonDragShow, null, DRAG_FLAG
            ).also {
                AppLogger.BASIC.d(TAG, "startDragAndDrop result:$it cost time ${System.currentTimeMillis() - startTime}")
                if (!it) {
                    StatisticsUtils.setEventNoteDragOut(notes.size, StatisticsUtils.DRAG_RESULT_SYSTEM_FAILURE)
                }
                if (it && commonDragShow != null) {
                    sysDragManager.updateSysDragBadge(
                        dragView,
                        commonDragShow,
                        notes.size,
                        getNoteListSysDragBadgeOffsetX(commonDragShow, context),
                        UiHelper.dp2px(context.resources.getDimension(R.dimen.dp_9)),
                        SysDragConstants.DRAG_BADGE_FORBID,
                    )
                    sysDragManager.registerSysDragReceiver()
                }
                dragResultCallback.onDragResult(it)
            }
        } else {
            AppLogger.BASIC.d(TAG, "startDragNoteItem no data")
            StatisticsUtils.setEventNoteDragOut(notes.size, StatisticsUtils.DRAG_RESULT_OTHER_FAILURE)
            dragResultCallback.onDragResult(false)
        }
    }

    /**
     * 计算角标水平偏移量
     */
    private fun getNoteListSysDragBadgeOffsetX(shadowBuilder: View.DragShadowBuilder, context: Context): Int {
        val dragViewWidth = (shadowBuilder as? NoteCommonDragShadow)?.getRealWidth()!!
        // 检测镜像语言场景
        return if (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL) {
            UiHelper.dp2px(context.resources.getDimension(R.dimen.dp_16), UiHelper.getDensity())
        } else {
            dragViewWidth - UiHelper.dp2px(context.resources.getDimension(R.dimen.dp_10), UiHelper.getDensity())
        }
    }

    private suspend fun checkCanDrag(
        context: Context,
        notes: List<RichNoteWithAttachments?>,
        dragView: View,
        dragResultCallback: DragResultCallback
    ): Boolean {
        if (IntegrationFeatures.DRAG_OUT.isOn.not()) {
            AppLogger.BASIC.d(TAG, "DRAG_OUT isOn is ture")
            dragResultCallback.onDragResult(false)
            return false
        }
        if (ExportAgentFactory.getDocAgent()?.isSupport(context, true) != true) {
            AppLogger.BASIC.d(TAG, "Drag notes is not support doc")
            dragResultCallback.onDragResult(false)
            return false
        }
        if (notes.isEmpty()) {
            AppLogger.BASIC.d(TAG, "Drag notes is Empty")
            dragResultCallback.onDragResult(false)
            return false
        }
        if (notes.size > MAX_DRAG_COUNT) {
            AppLogger.BASIC.d(TAG, "Drag number over 99")
            showDragCountOverMax(context)
            StatisticsUtils.setEventNoteDragOut(notes.size, StatisticsUtils.DRAG_RESULT_OVER_99)
            dragResultCallback.onDragResult(false)
            return false
        }
        return true
    }

    private fun initAISpaceView(dragView: View, viewContainer: View?, viewHolder: NoteViewHolder?): View? {
        var shadowView: View?
        if (viewContainer != null) {
            viewContainer?.findViewById<TextViewSnippet>(R.id.tv_title_r)?.text = viewHolder?.mTextTitle?.text
            viewContainer?.findViewById<TextView>(R.id.tv_list_date)?.text = viewHolder?.mListDate?.text
            shadowView = viewContainer
        } else {
            shadowView = dragView
        }
        return shadowView
    }

    @VisibleForTesting
    internal fun createDragShadow(view: View, size: Int, hasRadius: Boolean, isTodoItem: Boolean): NoteCommonDragShadow {
        if (!OplusBuildProxy.isAboveOS160()) {
            return NoteCommonDragShadow(view, size, hasRadius, isTodoItem)
        }
        val gridCheckBox = view.findViewById<View>(R.id.cb_grid_select)
        val listCheckBox = view.findViewById<View>(R.id.cb_list_select)
        val todoCheckBox = view.findViewById<View>(R.id.select_checkbox)
        // 拖拽时隐藏复选框
        gridCheckBox?.alpha = 0f
        listCheckBox?.alpha = 0f
        todoCheckBox?.alpha = 0f
        val shadowBuilder = if (isTodoItem) {
            NoteCommonDragShadow(view, size, hasRadius, true)
        } else {
            NoteCommonDragShadow(view, size, hasRadius, false)
        }
        gridCheckBox?.alpha = 1f
        listCheckBox?.alpha = 1f
        todoCheckBox?.alpha = 1f
        return shadowBuilder
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun startDragTodoItem(
        context: Context,
        totoItems: List<ToDoItem>,
        view: View,
        sysDragManager: SysDragManager,
        dragResultCallback: DragResultCallback
    ) {
        if (IntegrationFeatures.DRAG_OUT.isOn.not()) {
            dragResultCallback.onDragResult(false)
            return
        }
        if (totoItems.isEmpty()) {
            dragResultCallback.onDragResult(false)
            return
        }
        if (totoItems.size > MAX_DRAG_COUNT) {
            showDragCountOverMax(context)
            StatisticsUtils.setEventTodoDragOut(totoItems.size, StatisticsUtils.DRAG_RESULT_OVER_99)
            dragResultCallback.onDragResult(false)
            return
        }
        var count = 0
        var dragClipData: ClipData? = null
        totoItems.filter { it.itemType == ToDoItem.ITEM_TYPE_TODO && it.isSelected }
            .forEach { todoItem ->
                if (dragClipData == null) {
                    dragClipData = ClipData.newPlainText(DRAG_LABEL, todoItem.getClipString(context))
                    count++
                } else {
                    dragClipData?.addItem(ClipData.Item(todoItem.getClipString(context)))
                    count++
                }
            }
        if (dragClipData != null) {
            val startTime = System.currentTimeMillis()
            val shadowBuilder = createDragShadow(view, count, true, true)
            view.startDragAndDrop(
                dragClipData, shadowBuilder, null, DRAG_FLAG
            ).also {
                AppLogger.BASIC.d(TAG, "startDragAndDrop result:$it cost time ${System.currentTimeMillis() - startTime}")
                if (!it) {
                    Toast.makeText(context, com.oplus.note.baseres.R.string.note_reach_folder_name_lenth_limit, Toast.LENGTH_LONG).show()
                    StatisticsUtils.setEventTodoDragOut(totoItems.size, StatisticsUtils.DRAG_RESULT_SYSTEM_FAILURE)
                }
                sysDragManager.updateSysDragBadge(
                    view,
                    shadowBuilder,
                    count,
                    getNoteListSysDragBadgeOffsetX(shadowBuilder, context),
                    UiHelper.dp2px(context.resources.getDimension(R.dimen.dp_9)),
                    SysDragConstants.DRAG_BADGE_FORBID,
                )
                sysDragManager.registerSysDragReceiver()
                dragResultCallback.onDragResult(it)
            }
        } else {
            StatisticsUtils.setEventTodoDragOut(totoItems.size, StatisticsUtils.DRAG_RESULT_SYSTEM_FAILURE)
            dragResultCallback.onDragResult(false)
        }
    }

    @VisibleForTesting
    internal fun showDragCountOverMax(context: Context) {
        Toast.makeText(
            context,
            context.resources.getQuantityString(
                R.plurals.drag_over_max,
                MAX_DRAG_COUNT,
                MAX_DRAG_COUNT
            ),
            Toast.LENGTH_LONG
        ).show()
    }

    @VisibleForTesting
    fun isMounted(): Boolean {
        return Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()
    }
}

interface DragResultCallback {
    fun onDragResult(success: Boolean)
}

@VisibleForTesting
internal fun ToDoItem.getClipString(context: Context): String {
    var clipString = toDo.content
    val alarmTime: Date? = toDo.alarmTime
    alarmTime?.let {
        val repeatHintStr = RepeatManage.getRepeatHintStr(context, RepeatDataHelper.getRepeatData(toDo))
        clipString += " " + OplusDateUtils.getTimeDesc(alarmTime.time) + repeatHintStr
    }
    return clipString + "\n"
}