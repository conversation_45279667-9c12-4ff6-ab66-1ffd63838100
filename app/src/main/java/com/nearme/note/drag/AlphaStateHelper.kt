package com.nearme.note.drag

import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.BitmapDrawable

import android.view.View
import com.oplus.note.R
import com.oplus.note.logger.AppLogger

/**
 * view  改变 Apha 值得
 */
object AlphaStateHelper {

    private const val TAG = "AlphaStateHelper"
    private const val ALPHA_SHADOW_VIEW = 0.9F
    const val ALPHA_SHADOW_PAINT = 90


    fun setViewAlpha(view: View, type: TYPE) {
        when (type) {
            TYPE.TYPE_SHADOW_VIEW -> view.alpha = ALPHA_SHADOW_VIEW
            TYPE.TYPE_RESTORE_ATTACHMENT_VIEW -> view.foreground = null
            TYPE.TYPE_CHECK_ATTACHMENT_VIEW -> {
                view.post {
                    kotlin.runCatching {
                        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
                        val canvas = Canvas(bitmap)
                        val paint = Paint()
                        paint.color = view.context.getColor(R.color.drag_mask_color)
                        paint.alpha = ALPHA_SHADOW_PAINT
                        canvas.drawRect(0f, 0f, view.width.toFloat(), view.height.toFloat(), paint)
                        val drawable = BitmapDrawable(view.resources, bitmap)
                        view.foreground = drawable
                    }.onFailure {
                        AppLogger.BASIC.e(TAG, "setViewAlpha error $it")
                    }
                }
            }
        }
    }

    fun setViewDragForeground(view: View, type: TYPE, radius: Float? = null) {
        when (type) {
            TYPE.TYPE_RESTORE_ATTACHMENT_VIEW, TYPE.TYPE_SHADOW_VIEW -> view.foreground = null
            TYPE.TYPE_CHECK_ATTACHMENT_VIEW -> {
                var realRadius: Float = radius ?: -1F
                if (radius == null) {
                    realRadius = when (view.background) {
                        is GradientDrawable -> (view.background as GradientDrawable).cornerRadius
                        is StateListDrawable -> (view.background.current as? GradientDrawable)?.cornerRadius ?: -1F
                        else -> -1F
                    }
                }
                realRadius.takeIf { it > 0F }?.let { rd ->
                    view.foreground = GradientDrawable().also {
                        it.shape = GradientDrawable.RECTANGLE
                        it.cornerRadius = rd
                        it.setColor(
                            view.resources.getColor(
                                R.color.drag_cover_color,
                                view.context.theme
                            )
                        )
                    }
                }
            }
        }
    }

    enum class TYPE {
        TYPE_SHADOW_VIEW, //  //附件缩略图阴影状态
        TYPE_RESTORE_ATTACHMENT_VIEW, //还原附件状态
        TYPE_CHECK_ATTACHMENT_VIEW, //选中附件状态
    }
}