/***********************************************************
 * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * File: - RichDragListenerTest.kt
 * Description:
 * Version: 1.0
 * Date : 2023/3/20
 * Author: W9039628
 *
 * ---------------------Revision History: ---------------------
 * <author> <data>   <version>    <desc>
 * ------------------------------------------------------------
 * W9039628      2023/3/20      1.0     create Notes
</desc></version></data></author> */
package com.nearme.note.drag

import android.graphics.Rect
import android.view.View
import android.view.View.DragShadowBuilder
import androidx.annotation.VisibleForTesting
import com.nearme.note.activity.richedit.RichAdapter
import com.nearme.note.drag.shadow.NoteCommonDragShadow
import com.oplus.note.R
import com.oplus.note.logger.AppLogger

/**
 * 管理拖拽的缩略图
 * */
object DragDropShadowHelper {

    private const val TAG = "DragDopShadowHelper"

    @JvmStatic
    fun getLocationDropShadow(
        view: View,
        viewType: Int,
        parentScale: Float = 1.0F,
        bgColor: Int? = null,
        path: String? = null,
        srcRect: Rect? = null,
        capturedElement: String? = null,
    ): DragShadowBuilder {
        AppLogger.BASIC.d(TAG, "getLocationDropShadow viewType = $viewType")
        if (view.width <= 0) {
            AppLogger.BASIC.d(TAG, "getLocationDropShadow invalid size")
            return View.DragShadowBuilder(view)
        }
        return when (viewType) {
            RichAdapter.VIEW_TYPE_IMAGE, RichAdapter.VIEW_TYPE_VIDEO -> {
                constructShadowBuilder(
                    view,
                    1,
                    false,
                    view.resources.getDimensionPixelOffset(R.dimen.dp_12).toFloat(),
                    parentScale = parentScale,
                    bgColor = bgColor,
                    path = path,
                    srcRect = srcRect
                )
            }

            RichAdapter.DRAG_TYPE_RECORD,
            RichAdapter.DRAG_TYPE_SPEECH_AUDIO,
            RichAdapter.VIEW_TYPE_CONTACT_CARD,
            RichAdapter.VIEW_TYPE_SCHEDULE_CARD,
            RichAdapter.VIEW_TYPE_SUMMARY_DOCUMENT,
            RichAdapter.VIEW_TYPE_AUDIO,
            RichAdapter.VIEW_TYPE_FILE -> {
                constructShadowBuilder(
                    view,
                    1,
                    false,
                    view.resources.getDimensionPixelOffset(R.dimen.speech_audio_card_radio).toFloat(),
                    parentScale = parentScale,
                    bgColor = bgColor,
                    srcRect = srcRect,
                    capturedElement = capturedElement,
                )
            }

            RichAdapter.VIEW_TYPE_CARD -> {
                constructShadowBuilder(
                    view,
                    1, false,
                    view.resources.getDimensionPixelOffset(R.dimen.dp_10).toFloat(), parentScale = parentScale,
                    bgColor = bgColor,
                    srcRect = srcRect,
                    capturedElement = capturedElement,
                )
            }

            else -> View.DragShadowBuilder(view)
        }
    }

    @VisibleForTesting
    fun constructShadowBuilder(
        view: View,
        dragDropCount: Int,
        hasRadius: Boolean,
        shadowRadius: Float?,
        parentScale: Float,
        bgColor: Int?,
        path: String? = null,
        srcRect: Rect? = null,
        capturedElement: String? = null,
    ): DragShadowBuilder {
        return NoteCommonDragShadow(
            view,
            dragDropCount,
            hasRadius,
            false,
            shadowRadius,
            parentScale,
            bgColor,
            path,
            srcRect,
            capturedElement
        )
    }

    @JvmStatic
    fun getBlockDropShadow(
        view: View,
        dragBlockNumber: Int,
        parentScale: Float = 1.0F,
        bgColor: Int? = null,
        srcRect: Rect? = null,
        capturedElement: String? = null,
    ): DragShadowBuilder {
        return  NoteCommonDragShadow(
            view = view,
            dragDropCount = dragBlockNumber,
            hasRadius = false,
            isTodoItem = false,
            shadowRadius = view.resources.getDimensionPixelOffset(R.dimen.dp_10).toFloat(),
            parentScale = parentScale,
            bgColor = bgColor,
            srcRect = srcRect,
            capturedElement = capturedElement,
            maxDropCount = 99
        )
    }
}