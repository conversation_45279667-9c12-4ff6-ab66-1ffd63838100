/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : WVDragDropShadow.kt
 * Description    : WVDragDropShadow.kt
 * Version        : 1.0
 * Date           : 2023/9/15
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2023/9/15         1.0           create
 */
package com.nearme.note.drag.shadow

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Point
import android.graphics.RectF
import android.view.View
import androidx.core.graphics.toRect
import androidx.core.view.drawToBitmap
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.R
import com.oplus.notes.webview.container.api.DragNodeInfo


class WVDragDropShadow(view: View, dragNodeInfo: DragNodeInfo) : View.DragShadowBuilder(view) {

    private val mRadius = view.context.resources.getDimension(R.dimen.dp_8) //25F
    private val mScale = 0.8F
    private var mBigBorderWidth = view.context.resources.getDimension(R.dimen.dp_5)  //15F
    private val density = UiHelper.getDensity()
    val mPaint by lazy {
        Paint().apply {
            color = mBorderColor
            isAntiAlias = true
        }
    }
    private val dragRectF = RectF(
        dragNodeInfo.rect.left * density,
        dragNodeInfo.rect.top * density,
        dragNodeInfo.rect.right * density,
        dragNodeInfo.rect.bottom * density
    )
    private var mWidth = ((dragNodeInfo.rect.right - dragNodeInfo.rect.left) * density).toInt()
    private var mHeight = ((dragNodeInfo.rect.bottom - dragNodeInfo.rect.top) * density).toInt()
    private val mBorderColor = view.context.resources.getColor(R.color.drag_shadow_border, view.context.theme)

    override fun onDrawShadow(canvas: Canvas) {
        // 绘制卡片拖拽效果的内容
        canvas.apply {
            drawRoundRect(
                0F,
                0F,
                width.toFloat() * mScale,
                height.toFloat() * mScale,
                mRadius,
                mRadius,
                mPaint
            )
            val borderWidth = mBigBorderWidth
            val rect = RectF(borderWidth, borderWidth, width - borderWidth, height - borderWidth).apply {
                scale(mScale, mScale)
            }
            drawBitmap(
                view.drawToBitmap(Bitmap.Config.RGB_565),
                dragRectF.toRect(),
                rect,
                mPaint
            )
        }
    }

    override fun onProvideShadowMetrics(outShadowSize: Point?, outShadowTouchPoint: Point?) {
        super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint)
        // 设置拖拽效果View的宽和高
        outShadowSize?.set(mWidth, mHeight)
        outShadowTouchPoint?.set(mWidth / 2, mHeight / 2)
    }
}