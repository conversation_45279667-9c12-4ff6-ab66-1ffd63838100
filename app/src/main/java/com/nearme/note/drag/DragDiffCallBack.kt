/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - DragDiffCallBack.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/06/07
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  80262777       2023/06/07      1.0     create file
 ****************************************************************/
package com.nearme.note.drag

import androidx.recyclerview.widget.DiffUtil

class DragDiffCallBack(private val dragDiffDataProvider: DragDiffDataProvider) :
    DiffUtil.Callback() {
    override fun getOldListSize() = dragDiffDataProvider.itemCount()

    override fun getNewListSize() = dragDiffDataProvider.itemCount()

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int) =
        oldItemPosition == newItemPosition

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        if (oldItemPosition != newItemPosition) {
            return false
        }
        return !(dragDiffDataProvider.isVisible(oldItemPosition) && dragDiffDataProvider.isItemSelected(
            oldItemPosition
        ))
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
        if ((oldItemPosition == newItemPosition) && dragDiffDataProvider.isItemSelected(
                oldItemPosition
            )
        ) {
            return DragStatePayload(dragDiffDataProvider.isDrag())
        }
        return super.getChangePayload(oldItemPosition, newItemPosition)
    }
}

interface DragDiffDataProvider {
    fun itemCount(): Int
    fun isItemSelected(pos: Int): Boolean
    fun isDrag(): Boolean

    fun isVisible(pos: Int): Boolean
}

data class DragStatePayload(val isDrag: Boolean)