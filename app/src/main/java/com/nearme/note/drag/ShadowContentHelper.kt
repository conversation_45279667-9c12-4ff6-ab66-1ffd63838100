/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ShadowContentHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2023/06/07
 ** Author: lin<PERSON><PERSON>@oppo.com
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  80262777       2023/06/07      1.0     create file
 ****************************************************************/
package com.nearme.note.drag

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.util.DisplayMetrics
import android.view.View
import androidx.annotation.VisibleForTesting
import com.coui.component.responsiveui.unit.dp
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.util.HtmlElementCaptureUtils.base642Bitmap
import com.nearme.note.util.windowManager
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webview.data.CaptureResult
import kotlin.math.pow

object ShadowContentHelper {
    private const val TAG = "NoteListDragShadow"
    private const val SCALE_110 = 1.10F
    private const val SCALE_85 = 0.85F
    private const val SCALE_60 = 0.6F
    private const val SCALE_45 = 0.45F
    private const val SCALE_30 = 0.30F
    private const val SCALE_40 = 0.4F
    private const val SCALE_20 = 0.2F
    private const val WIDTH_120 = 120
    private const val WIDTH_200 = 200
    private const val WIDTH_360 = 360
    private const val WIDTH_480 = 480

    fun getScale(context: Context, w: Int, h: Int): Pair<Float, Boolean> {
        val area = w * h
        if (WIDTH_120.dp.toPixel(context).pow(2) >= area) {
            return Pair(SCALE_110, false)
        } else if (WIDTH_200.dp.toPixel(context).pow(2) >= area) {
            return Pair(SCALE_85, false)
        } else if (WIDTH_360.dp.toPixel(context).pow(2) >= area) {
            return Pair(SCALE_60, false)
        } else if (WIDTH_480.dp.toPixel(context).pow(2) >= area) {
            return Pair(SCALE_40, false)
        } else if (getScreenHeightWithSystem(context) < h) {
            return Pair(SCALE_20, true)
        }
        return Pair(SCALE_30, false)
    }

    fun getShowRect(view: View, selfScale: Float, srcRect: Rect? = null): Rect {
        val dh = (getScreenHeightWithSystem(view.context) / selfScale).toInt()
        val srcValidWidth = srcRect?.width() ?: view.width
        val srcValidHeight = srcRect?.height() ?: view.height
        AppLogger.BASIC.d(TAG, "getShowRect: srcRect=$srcRect, dh=$dh, srcValidWidth=$srcValidWidth, srcValidHeight=$srcValidHeight")
        if (dh >= srcValidHeight) {
            return srcRect ?: Rect(0, 0, srcValidWidth, srcValidHeight)
        } else {
            val rect = Rect()
            view.getLocalVisibleRect(rect)
            return if (rect.top <= 0) {
                Rect(0, 0, srcValidWidth, dh)
            } else {
                if (rect.top + dh > srcValidHeight) {
                    Rect(0, srcValidHeight - dh, srcValidWidth, srcValidHeight)
                } else {
                    Rect(0, rect.top, srcValidWidth, rect.top + dh)
                }
            }
        }
    }

    fun getBimapFromViewWithRadius(
        view: View,
        scale: Float,
        contentRect: Rect,
        radius: Float,
        path: String? = null,
        capturedElement: String? = null,
    ): Bitmap {
        AppLogger.BASIC.d(
            TAG,
            "getBimapFromViewWithRadius vw=${view.width} vh=${view.height} s=$scale r=$contentRect " +
                    "path null=${path == null}, capturedElement null=${capturedElement == null}"
        )
        val targetWidth = (contentRect.width() * scale).toInt()
        val targetHeight = (contentRect.height() * scale).toInt()
        val scaledBitmap: Bitmap? = if (path != null) {
            getScaledBitmapFromPath(path, targetWidth, targetHeight)
        } else if (capturedElement != null) {
            getScaleBitmapFromCapturedElement(capturedElement, targetWidth, targetHeight)
        } else {
            null
        }
        AppLogger.BASIC.d(
            TAG,
            "getBimapFromViewWithRadius: scaledBitmap=${scaledBitmap.hashCode()}"
        )
        scaledBitmap?.let {
            val resultBitmap = Bitmap.createBitmap(
                it.width,
                it.height,
                Bitmap.Config.ARGB_8888
            )
            val bitmapCanvas = Canvas(resultBitmap)
            val paint = Paint(Paint.ANTI_ALIAS_FLAG)
            bitmapCanvas.drawRoundRect(
                RectF(0F, 0F, it.width.toFloat(), it.height.toFloat()),
                radius,
                radius,
                paint
            )
            paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
            bitmapCanvas.drawBitmap(it, 0F, 0F, paint)
            it.recycle()

            return resultBitmap
        } ?: run {
            // 根据Rect对WebView截图
            Bitmap.createBitmap(
                targetWidth,
                targetHeight,
                Bitmap.Config.ARGB_8888
            ).let {
                Canvas(it).let { bitmapCanvas ->
                    bitmapCanvas.save()
                    bitmapCanvas.scale(scale, scale)
                    bitmapCanvas.translate(-contentRect.left.toFloat(), -contentRect.top.toFloat() - view.scrollY)
                    kotlin.runCatching {
                        view.draw(bitmapCanvas)
                    }.onFailure {
                        AppLogger.BASIC.e(TAG, "getBimapFromViewWithRadius view draw error ${it.message}")
                    }
                    bitmapCanvas.restore()
                }

                val resultBitmap = Bitmap.createBitmap(
                    it.width,
                    it.height,
                    Bitmap.Config.ARGB_8888
                )
                val bitmapCanvas = Canvas(resultBitmap)
                val paint = Paint(Paint.ANTI_ALIAS_FLAG)
                bitmapCanvas.drawRoundRect(
                    RectF(0F, 0F, it.width.toFloat(), it.height.toFloat()),
                    radius,
                    radius,
                    paint
                )
                paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
                bitmapCanvas.drawBitmap(it, 0F, 0F, paint)
                it.recycle()

                return resultBitmap
            }
        }
    }

    @VisibleForTesting
    fun getScreenHeightWithSystem(ctx: Context): Int {
        val dme = DisplayMetrics()
        ctx.windowManager.defaultDisplay.getRealMetrics(dme)
        return dme.heightPixels
    }

    private fun getScaledBitmapFromPath(path: String?, targetWidth: Int, targetHeight: Int): Bitmap? {
        if (path.isNullOrEmpty()) return null
        val bm = BitmapFactory.decodeFile(path) ?: return null
        return Bitmap.createScaledBitmap(bm, targetWidth, targetHeight, true)
    }

    private fun getScaleBitmapFromCapturedElement(element: String?, targetWidth: Int, targetHeight: Int): Bitmap? {
        element?.let {
            val captureResults: List<CaptureResult> = Gson().fromJson(element, object : TypeToken<List<CaptureResult>>() {}.type)
            if (captureResults.isNotEmpty()) {
                val bitmap = base642Bitmap(captureResults[0].data)
                if (bitmap != null) {
                    return Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
                }
            }
        }
        return null
    }
}