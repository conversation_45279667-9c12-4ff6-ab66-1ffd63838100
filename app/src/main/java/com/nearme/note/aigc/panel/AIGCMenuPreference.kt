/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AIGCMenuPreference
 ** Description:
 **         v1.0:  copy from [com.coui.appcompat.preference.COUIJumpPreference]
 **
 ** Version: 1.0
 ** Date: 2024/07/30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2024/7/30   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.aigc.panel

import android.content.Context
import android.graphics.Typeface
import android.util.AttributeSet
import android.widget.FrameLayout
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUICustomListSelectedLinearLayout
import com.coui.appcompat.preference.COUIJumpPreference
import com.nearme.note.util.updateMarginLayoutParams
import com.oplus.note.logger.AppLogger
import com.support.preference.R

class AIGCMenuPreference : COUIJumpPreference {

    private var fontWeight400: Typeface? = null

    constructor(context: Context?) : super(context, null)

    constructor(context: Context?, attrs: AttributeSet) : super(context, attrs, R.attr.couiJumpPreferenceStyle)
    constructor(context: Context?, attrs: AttributeSet, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr, R.style.Preference_COUI_COUIJumpPreference
    )

    constructor(context: Context?, attrs: AttributeSet, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes)

    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        if (holder.itemView is COUICustomListSelectedLinearLayout) {
            (holder.itemView as? COUICustomListSelectedLinearLayout)?.refreshCardBg(
                context.getColor(com.oplus.note.R.color.aigc_panle_item_bg_color)
            )
        } else {
            AppLogger.BASIC.e(TAG, "error, item view not support ")
        }
        holder.itemView.findViewById<TextView>(android.R.id.title).apply {
            textSize = TITLE_TEXT_SIZE
            createTypeFaceIfNeed(this)
            setTypeface(fontWeight400)
        }
        holder.itemView.findViewById<FrameLayout>(R.id.img_layout).apply {
            updateMarginLayoutParams {
                marginEnd = context.resources.getDimensionPixelSize(com.oplus.note.R.dimen.dp_8)
            }
        }
    }

    private fun createTypeFaceIfNeed(textView: TextView) {
        if (fontWeight400 == null) {
            fontWeight400 = Typeface.create(textView.typeface, TITLE_FONT_WEIGHT, false)
        }
    }

    companion object {
        const val TAG = "AIGCMenuPreference"
        private const val TITLE_TEXT_SIZE = 14f
        private const val TITLE_FONT_WEIGHT = 400
    }
}