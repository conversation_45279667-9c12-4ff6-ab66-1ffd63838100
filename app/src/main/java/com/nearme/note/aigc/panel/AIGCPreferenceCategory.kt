/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - AIGCPreferenceCategory.kt
** Description:
** Version: 1.0
** Date : 2024/11/5
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2024/11/5      1.0     create file
****************************************************************/
package com.nearme.note.aigc.panel

import android.content.Context
import android.util.AttributeSet
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.oplus.note.R

class AIGCPreferenceCategory : COUIPreferenceCategory {

    constructor(context: Context?) : super(context, null)

    constructor(context: Context?, attrs: AttributeSet) : super(context, attrs)

    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        holder.itemView.layoutParams.height = context.resources.getDimensionPixelSize(R.dimen.dp_12)
    }
}