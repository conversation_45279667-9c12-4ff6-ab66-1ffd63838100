/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - AIGCHalfMenuPreference.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/4
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  yanglinlong       2024/11/4      1.0     create file
 ****************************************************************/
package com.nearme.note.aigc.panel

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.preference.Preference
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.preference.COUICustomListSelectedLinearLayout
import com.coui.appcompat.preference.COUIJumpPreference
import com.oplus.note.R

class AIGCTwoMenuPreference : COUIJumpPreference {

    private var preferenceClickListener: OnPreferenceClickListener? = null

    init {
        layoutResource = R.layout.layout_head_two_preference
    }

    constructor(context: Context?) : super(context, null)

    constructor(context: Context?, attrs: AttributeSet) : super(
        context,
        attrs,
        com.support.preference.R.attr.couiJumpPreferenceStyle
    )

    constructor(context: Context?, attrs: AttributeSet, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr, com.support.preference.R.style.Preference_COUI_COUIJumpPreference
    )

    constructor(context: Context?, attrs: AttributeSet, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes
    )

    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        holder.itemView.findViewById<COUICustomListSelectedLinearLayout?>(R.id.layout_composition)?.let {
            it.findViewById<TextView>(R.id.title)?.setText(com.oplus.note.baseres.R.string.aigc_composition)
            it.findViewById<ImageView>(R.id.icon)
                ?.setImageResource(com.oplus.note.baseres.R.drawable.icon_preference_aigc_composition)
            it.setOnClickListener {
                preferenceClickListener?.onPreferenceClick(Preference(context).apply {
                    key = context.getString(com.oplus.note.baseres.R.string.action_composition)
                })
            }
            it.orientation = LinearLayout.VERTICAL
            it.refreshCardBg(context.getColor(R.color.aigc_panle_item_bg_color))
        }
        holder.itemView.findViewById<COUICustomListSelectedLinearLayout?>(R.id.layout_adjust)?.let {
            it.findViewById<TextView>(R.id.title)?.setText(com.oplus.note.baseres.R.string.aigc_adjust)
            it.findViewById<ImageView>(R.id.icon)
                ?.setImageResource(com.oplus.note.baseres.R.drawable.icon_preference_aigc_organise)
            it.setOnClickListener {
                preferenceClickListener?.onPreferenceClick(Preference(context).apply {
                    key = context.getString(com.oplus.note.baseres.R.string.action_organise)
                })
            }
            it.orientation = LinearLayout.VERTICAL
            it.refreshCardBg(context.getColor(R.color.aigc_panle_item_bg_color))
        }
    }

    override fun setOnPreferenceClickListener(onPreferenceClickListener: OnPreferenceClickListener?) {
        this.preferenceClickListener = onPreferenceClickListener
    }

    companion object {
        const val TAG = "AIGCHalfMenuPreference"
    }
}