/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - ScannerLineView
 ** Description:
 **         v1.0:  ScannerLineView
 **
 ** Version: 1.0
 ** Date: 2023/07/04
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/7/4   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.ocr

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import com.oplus.note.R

class ScannerLineView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private var scanDrawable: Drawable? = null
    private var scanRect = Rect()
    private val drawingRect = Rect()
    private val drawableRect = Rect()
    private var canDraw = false
    private var keepDrawing = false
    private var scanAnim: ValueAnimator? = null

    init {
        initData(context)
    }

    private fun initData(context: Context) {
        canDraw = false
        scanRect.setEmpty()
        scanDrawable = ContextCompat.getDrawable(context, R.drawable.ic_barcode_scan_line)
        scanDrawable?.let {
            drawableRect.set(0, 0, it.intrinsicWidth, it.intrinsicHeight)
        }
    }

    fun setScanArea(
        with: Int,
        height: Int,
        paddingStart: Int,
        paddingEnd: Int,
        paddingTop: Int,
        paddingBottom: Int
    ) {
        scanRect.left = paddingStart
        scanRect.top = paddingTop
        scanRect.right = with - paddingEnd
        scanRect.bottom = height - paddingBottom
    }

    fun keepDrawing(keeping: Boolean) {
        if (keepDrawing == keeping) {
            return
        }
        keepDrawing = keeping
        if (keeping) {
            val scaleSize = scanRect.width() * 1.0f / drawableRect.width()
            drawingRect[0, 0, (drawableRect.width() * scaleSize).toInt()] =
                (drawableRect.height() * scaleSize).toInt()
            scanAnim = ValueAnimator.ofInt(scanRect.top, scanRect.bottom)
            scanAnim?.setDuration(SCAN_TIME)
            scanAnim?.interpolator = LinearInterpolator()
            scanAnim?.repeatCount = -1
            scanAnim?.repeatMode = ValueAnimator.RESTART
            scanAnim?.addUpdateListener { animation: ValueAnimator ->
                drawingRect.offsetTo(scanRect.left, (animation.animatedValue as Int))
                drawingRect.offset(0, -drawingRect.height())
                scanDrawable!!.bounds = drawingRect
                postInvalidate(
                    scanRect.left,
                    scanRect.top,
                    scanRect.right,
                    scanRect.bottom
                )
            }
            scanAnim?.start()
        } else {
            cancelScan()
        }
    }

    private fun cancelScan() {
        if (scanAnim != null && scanAnim?.isRunning == true) {
            scanAnim?.cancel()
        }
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        canDraw = visibility == VISIBLE
        if (visibility != VISIBLE) {
            cancelScan()
        }
        super.onVisibilityChanged(changedView, visibility)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        setMeasuredDimension(widthSize, heightSize)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (!canDraw || !keepDrawing) {
            return
        }
        scanDrawable?.draw(canvas)
    }

    companion object {
        private const val SCAN_TIME = 2000L
    }
}
