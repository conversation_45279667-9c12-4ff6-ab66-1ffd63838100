package com.nearme.note.ocr

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Bitmap
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.ImageView.ScaleType
import android.widget.Toast
import androidx.activity.addCallback
import androidx.annotation.RequiresApi
import androidx.core.view.ViewCompat
import androidx.databinding.DataBindingUtil
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.nearme.note.BaseActivity
import com.nearme.note.util.PreferencesUtils
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.databinding.OcrConverterActivityBinding
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.editor.utils.QuickNoteStatisticsUtils
import com.oplus.supertext.adapter.SuperTextEventAdapterWrapper
import com.oplus.supertext.adapter.SuperTextTouchEventCallbackWrapper
import com.oplus.supertext.core.data.OcrResultWrapProxy
import com.oplus.supertext.ostatic.SuperTextStaticWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class OcrConverterActivity : BaseActivity() {

    companion object{
        const val TAG = "OCR"
        const val EXTRA_MODE_MULTIWINDOW = "multi_window"
        const val FROM_OCR = "from_ocr"
        const val OCR_START = "enter_ocr"
        const val OCR_SUCCESS = "ocr_success"
        const val OCR_SELECT_STRING = "ocr_select_str"
        const val OCR_CLEAR_STRING = "ocr_clear_str"
        const val OCR_FAILURE = "ocr_failure"
        const val OCR_RESULT = "ocr_result"
        const val OCR_NEED_CLOSE = "ocr_need_close"
        const val OCR_STRING = "ocr_string"
        const val OCR_BITMAP = "ocr_bitmap"
        const val OCR_IN_VERTICAL_SPLIT_SCREEN = "ocr_in_vertical_split_screen"
    }

    private var binding: OcrConverterActivityBinding? = null
    private var ocrAllText: String? = null
    private var mSuperTextStatic: SuperTextStaticWrapper? = null
    private var hasSendOcrResult: Boolean = false

    inner class LocalReceiver : BroadcastReceiver() {
        @SuppressLint("UnsafeBroadcastReceiverActionDetector")
        override fun onReceive(context: Context?, intent: Intent?) {
            intent?.let {
                val action = it.action
                if (OCR_NEED_CLOSE == action) {
                    finish()
                }
            }
        }
    }
    private var quickNoteReceiver: LocalReceiver? = null

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        supportActionBar?.hide()
        binding = DataBindingUtil.setContentView(this, R.layout.ocr_converter_activity)
        mSuperTextStatic = SuperTextStaticWrapper(this.applicationContext)
        if (BuildConfig.DEBUG) {
            mSuperTextStatic!!.apply {
                setDebugMode(true)
                setCompressorEnable(false)
                setDumpBitmapAndResultEnable(true)
            }
        }
        mSuperTextStatic?.removeBlackRegion(false)
        binding?.superTextView?.setSuperTextEventListener(object : SuperTextEventAdapterWrapper() {
            override fun onDataCompletedCallback(allText: String?) {
                AppLogger.BASIC.d(TAG, "ocr result${allText?.length}")
                if (<EMAIL>) {
                    return
                }
                binding?.scanner?.keepDrawing(false)
                if (allText.isNullOrEmpty()) {
                    Toast.makeText(
                        this@OcrConverterActivity,
                        <EMAIL>(R.string.ocr_no_result),
                        Toast.LENGTH_SHORT
                    ).show()
                    finish()
                } else {
                    binding?.close?.visibility = View.VISIBLE
                    binding?.add?.visibility = View.VISIBLE
                    val intent = Intent(OCR_SUCCESS)
                    ocrAllText = allText
                    intent.putExtra(OCR_STRING, allText)
                    LocalBroadcastManager.getInstance(this@OcrConverterActivity)
                        .sendBroadcast(intent)
                }
            }

            override fun onSelectTextChangedCallback(newText: String?) {
                if (!newText.isNullOrEmpty()) {
                    val intent = Intent(OCR_SELECT_STRING)
                    intent.putExtra(OCR_STRING, newText)
                    LocalBroadcastManager.getInstance(this@OcrConverterActivity)
                        .sendBroadcast(intent)
                }else{
                    val intent = Intent(OCR_CLEAR_STRING)
                    LocalBroadcastManager.getInstance(this@OcrConverterActivity)
                        .sendBroadcast(intent)
                }
            }
        })
        binding?.superTextView?.setSuperTextTouchEventCallback(object :SuperTextTouchEventCallbackWrapper(){
            override fun onTouchedOutOfTextWrapper() {
                binding?.superTextView?.dismissAllSelect(true)
            }

            override fun onTouchedOutSideOfSelectedTextWrapper() {
                binding?.superTextView?.dismissAllSelect(true)
            }
        })
        binding?.superTextView?.setLimitTextToolShow(true)

        screenShot()
        initClickEvent()
        registerReceiver()
        val windowInsetsController =
            ViewCompat.getWindowInsetsController(window.decorView)

        windowInsetsController?.isAppearanceLightNavigationBars = true
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
        setBackInvokedCallback({ backPressed() })
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        finish()
    }

    private fun registerReceiver() {
        quickNoteReceiver = LocalReceiver()
        val filter = IntentFilter()
        filter.addAction(OCR_NEED_CLOSE)
        quickNoteReceiver?.let {
            LocalBroadcastManager.getInstance(this).registerReceiver(it, filter)
        }
    }

    private fun initClickEvent() {
        binding?.add?.setOnClickListener {
            sendResultToNote()
            QuickNoteStatisticsUtils.setEventOcrInsertText(this)
        }
        binding?.close?.setOnClickListener {
            finish()
        }
    }


    @SuppressLint("ServiceCast")
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun screenShot(){
        binding?.scanner?.setScanArea(
            resources.displayMetrics.widthPixels,
            resources.displayMetrics.heightPixels,
            0,
            0,
            0,
            0)
        val bundle:Bundle = intent.extras?: Bundle()
        val bitmapBinder: BitmapBinder? = bundle.getBinder(OCR_BITMAP) as? BitmapBinder
        val bitmap = bitmapBinder?.getBitMap()
        bitmap?.let {
            binding?.screen?.scaleType = getScaleType(bundle)
            binding?.screen?.setImageBitmap(it)
            binding?.scanner?.keepDrawing(true)
            CoroutineScope(Dispatchers.IO).launch {
                val re = it.copy(Bitmap.Config.ARGB_8888, true)
                AppLogger.BASIC.d(TAG,"start ocr")
                ocrBitamp(re)
            }
        }
    }

    private fun getScaleType(bundle: Bundle): ScaleType {
        return if (bundle.getBoolean(OCR_IN_VERTICAL_SPLIT_SCREEN, false)) {
            AppLogger.BASIC.d(TAG, "getScaleType with ScaleType.FIT_CENTER is isVerticalStandardTwoSplitScreen")
            ScaleType.FIT_CENTER
        } else {
            ScaleType.MATRIX
        }
    }


    private fun ocrBitamp(bitmap: Bitmap){
        binding?.superTextView?.apply {
            setDebugMode(false)
            setEnableHighlight(true)
            if (PreferencesUtils.isFirstClip(this@OcrConverterActivity)) {
                setEnableGuide(true)
                PreferencesUtils.setClipState(this@OcrConverterActivity, false)
            }
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(OCR_START))

        mSuperTextStatic?.ocrBitmap(bitmap, object : SuperTextStaticWrapper.StaticOcrCallBackWrapper() {
            override fun onErrorCallback(errorCode: Int) {
                binding?.scanner?.keepDrawing(false)
                AppLogger.BASIC.d(TAG,"ocr failure: $errorCode")
                Toast.makeText(
                    this@OcrConverterActivity,
                    <EMAIL>(R.string.ocr_failure),
                    Toast.LENGTH_SHORT
                ).show()
                LocalBroadcastManager.getInstance(this@OcrConverterActivity).sendBroadcast(Intent(OCR_FAILURE))
            }

            override fun onResultCallback(proxy: OcrResultWrapProxy) {
                AppLogger.BASIC.d(TAG, "ocr finish!")
                binding?.screen?.let { scaleImageView ->
                    binding?.superTextView?.setDataWithMatrix(
                        proxy,
                        scaleImageView.imageMatrix
                    )
                }
            }
        })
    }



    private fun sendResultToNote() {
        val intent = Intent()
        intent.component = ComponentName(
            BuildConfig.APPLICATION_ID,
            "com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity"
        )
        val options = Bundle()
        if (!isInMultiWindowMode) {
            options.putInt("android.activity.windowingMode", 100)
        }
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
        intent.putExtra(FROM_OCR, true)
        var result = binding?.superTextView?.getSelectedText()
        if (result.isNullOrEmpty()) {
            result = ocrAllText
        }
        intent.putExtra(OCR_STRING, result)
        startActivity(intent, options)
        finish()
    }

    private fun backPressed() {
        beforeActivityFinish()
        finish()
    }

    private fun beforeActivityFinish() {
        if (!isInMultiWindowMode) {
            overridePendingTransition(0, 0)
            val intent = Intent()
            intent.component = ComponentName(
                BuildConfig.APPLICATION_ID,
                "com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity"
            )
            val options = Bundle()
            options.putInt("android.activity.windowingMode", 100)
            startActivity(intent, options)
        }
    }

    override fun afterBackAnimFinish() {
        super.afterBackAnimFinish()
        beforeActivityFinish()
    }

    override fun finish() {
        super.finish()
        if (!hasSendOcrResult) {
            LocalBroadcastManager.getInstance(this@OcrConverterActivity).sendBroadcast(Intent(OCR_RESULT))
            hasSendOcrResult = true
        }
    }

    override fun onStop() {
        super.onStop()
        finish()
    }

    override fun onDestroy() {
        binding?.superTextView?.dismissTextHandler()
        quickNoteReceiver?.let {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(it)
        }
        mSuperTextStatic?.release()
        super.onDestroy()
    }
}