/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - SplitScreenUtil
 ** Description: 处理分屏逻辑的一些公共方法.
 ** Version: 1.0
 ** Date : 2022/8/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <data>   <version >    <desc>
 ** Wenhao.Deng   2022/8/16     1.0        created
 ****************************************************************/
package com.nearme.note.util

import android.widget.ImageView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.note.logger.AppLogger

object SplitScreenUtil {
    /**
     * 由分屏icon所在toolbar,反射的方法,拿到分屏icon的实例
     * @param toolbar 分屏icon所在的toolbar
     * @return 分屏icon的实例
     */
    const val TAG = "SplitScreenUtil"

    @JvmStatic
    fun getSplitScreenIcon(toolbar: COUIToolbar?): ImageView? {
        if (toolbar == null) {
            return null
        }
        kotlin.runCatching {
            val toolbarClass = COUIToolbar::class.java
            val logoViewField = toolbarClass.getDeclaredField("mLogoView")
            logoViewField.isAccessible = true
            return logoViewField[toolbar] as ImageView
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getSplitScreenIcon fail $it")
        }
        return null
    }
}