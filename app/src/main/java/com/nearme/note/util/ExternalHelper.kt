package com.nearme.note.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.provider.DocumentsContract
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.isPackageDisabled
import com.oplus.note.utils.isPackageInstall
import java.io.File

object ExternalHelper {
    private const val TAG = "ExternalHelper"
    private const val NOTE = "Notes"
    private const val ACTION_BROWSER_FILE = "oplus.intent.action.filemanager.BROWSER_FILE"
    private const val EXTRA_CURRENT_DIR = "CurrentDir"

    private val PATH_DOCUMENTS_IMG = "${Environment.DIRECTORY_PICTURES}/$NOTE"
    private val PATH_DOCUMENTS_AUDIO = "${Environment.DIRECTORY_MUSIC}/$NOTE"
    private val PATH_DOWNLOADS_TXT = Environment.DIRECTORY_DOWNLOADS
    private val PATH_DOWNLOADS_WORD = "${Environment.DIRECTORY_DOWNLOADS}/$NOTE"
    private val PATH_DOCUMENTS = "${Environment.DIRECTORY_DOCUMENTS}/$NOTE"
    private val PATH_MOVIES = "${Environment.DIRECTORY_MOVIES}/$NOTE"

    private const val URI_DOCUMENTS = "content://com.android.externalstorage.documents/document/primary:"
    private const val TYPE_ALL = "*/*"

    fun jumpToFileManager(context: Context, type: TYPE) {
        when (type) {
            TYPE.IMG -> jumpGallery(context)
            TYPE.AUDIO -> {
                val parent = Environment.getExternalStorageDirectory()
                val child = PATH_DOCUMENTS_AUDIO
                val dir = File(parent, child)
                jumpToFileManagerInternal(context, dir, type)
            }
            TYPE.TXT -> {
                val parent = Environment.getExternalStorageDirectory()
                val child = PATH_DOWNLOADS_TXT
                val dir = File(parent, child)
                jumpToFileManagerInternal(context, dir, type)
            }
            TYPE.WORD -> {
                val parent = Environment.getExternalStorageDirectory()
                val child = PATH_DOWNLOADS_WORD
                val dir = File(parent, child)
                jumpToFileManagerInternal(context, dir, type)
            }
            TYPE.DOCUMENT -> {
                val parent = Environment.getExternalStorageDirectory()
                val child = PATH_DOCUMENTS
                val dir = File(parent, child)
                jumpToFileManagerInternal(context, dir, type)
            }
            TYPE.VIDEO -> {
                jumpToGallery(context)
            }
        }
    }

    private fun jumpToFileManagerInternal(context: Context, dir: File, type: TYPE) {
        // try coloros FM
        runCatching {
            val intent = Intent(ACTION_BROWSER_FILE).apply {
                `package` = "com.coloros.filemanager"
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra(EXTRA_CURRENT_DIR, dir.absolutePath)
            }
            context.startActivity(intent)
            AppLogger.BASIC.d(TAG, "jumpToFileManager use coloros")
            return
        }.onFailure {
            AppLogger.BASIC.e(TAG, "jumpToFileManager coloros err:$it")
        }

        // try oplus FM
        runCatching {
            val intent = Intent(ACTION_BROWSER_FILE).apply {
                `package` = "com.oneplus.filemanager"
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
                putExtra(EXTRA_CURRENT_DIR, dir.absolutePath)
            }
            context.startActivity(intent)
            AppLogger.BASIC.d(TAG, "jumpToFileManager use oplus")
            return
        }.onFailure {
            AppLogger.BASIC.e(TAG, "jumpToFileManager oplus err:$it")
        }

        // try DocumentsUI
        AppLogger.BASIC.d(TAG, "jumpToFileManager use DocumentsUI")
        jumpToDocumentsUI(context, type)
    }

    fun jumpToDocumentsUI(context: Context, type: TYPE) {
        val path = when (type) {
            TYPE.IMG -> PATH_DOCUMENTS_IMG
            TYPE.AUDIO -> PATH_DOCUMENTS_AUDIO
            TYPE.TXT -> PATH_DOWNLOADS_TXT
            TYPE.WORD -> PATH_DOWNLOADS_WORD
            TYPE.DOCUMENT -> PATH_DOCUMENTS
            TYPE.VIDEO -> PATH_MOVIES
        }
        val uri = Uri.parse("$URI_DOCUMENTS$path")
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                this.type = TYPE_ALL
                addCategory(Intent.CATEGORY_OPENABLE)
                putExtra(DocumentsContract.EXTRA_INITIAL_URI, uri)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "jumpToDocumentsUI err:$e")
        }
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    fun jumpGallery(context: Context) {
        val intent = Intent().apply {
            action = Intent.ACTION_VIEW
            type = "image/*"
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }

        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "jumpGallery err:$e")
        }
    }

    fun jumpToGallery(context: Context) {
        val galleryOp = "com.oneplus.gallery"
        val gallery = "com.coloros.gallery3d"

        arrayOf(gallery, galleryOp).find {
            isPackageInstall(context, it) && !isPackageDisabled(it, context)
        }?.let { pkg ->
            val intent = context.packageManager.getLaunchIntentForPackage(pkg)
            runCatching {
                context.startActivity(intent)
            }.onFailure {
                AppLogger.BASIC.e(TAG, "jumpToGallery err:$it")
            }
        }
    }

    enum class TYPE {
        IMG, // 图片
        AUDIO, //音视频
        TXT, //文本
        WORD, // doc文件
        DOCUMENT, // 文档文件（包括word、excel、pdf等文件）
        VIDEO // 视频
    }
}