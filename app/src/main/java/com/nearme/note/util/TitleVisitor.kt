/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: TitleVisitor.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2025/06/20
 * * Author: niexiaokang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import com.nearme.note.activity.richedit.aigc.BaseNodeVisitor
import com.oplus.richtext.core.parser.HtmlFormats
import org.jsoup.nodes.Node
import kotlin.text.endsWith
import kotlin.text.substring

class TitleVisitor : BaseNodeVisitor() {
    companion object {
        const val CLASS = "class"
        const val DIV_COUNT = 2
    }

    /**
     * 是否不需要继续查找
     */
    private var isNoNeedFind = false
    private var title = ""
    private var isFindTitle = false
    private var divCount = 0

    override fun init() {
        isNoNeedFind = false
        isFindTitle = false
        divCount = 0
        title = ""
    }

    fun getTitle(): String {
        return title
    }

    fun getText(text: String): String {
        return if (title.isEmpty()) {
            text
        } else {
            if (text.startsWith(title)) {
                text.replaceFirst(title, "")
            } else {
                text
            }
        }
    }

    override fun parseHead(node: Node) {
        if (isNoNeedFind) return
        when (node.nodeName()) {
            HtmlFormats.DIV -> {
                divCount++
                if (divCount == DIV_COUNT) {
                    val attr = node.attr(CLASS)
                    if (attr.contains(HtmlFormats.H1)) {
                        isFindTitle = true
                    } else {
                        isNoNeedFind = true
                    }
                }
            }

            HtmlFormats.TEXT -> {
                if (isFindTitle) {
                    title = trim(node.toString())
                    isNoNeedFind = true
                }
            }
        }
    }

    override fun parseTail(node: Node) {
        if (isNoNeedFind) return
        if (node.nodeName() == HtmlFormats.DIV && divCount == DIV_COUNT) {
            isNoNeedFind = true
        }
    }

    override fun getResult(): String {
        return ""
    }

    private fun trim(msg: String): String {
        var result = msg.trim()
        val nbspHtmlEntity = "&nbsp;"
        val nbspUnicodeChar = '\u00A0'
        while (result.isNotEmpty()) {
            result = when {
                result.startsWith(nbspHtmlEntity) -> result.substring(nbspHtmlEntity.length)
                result.startsWith(nbspUnicodeChar) -> result.substring(1)
                else -> break
            }
        }
        while (result.isNotEmpty()) {
            result = when {
                result.endsWith(nbspHtmlEntity) -> result.substring(0, result.length - nbspHtmlEntity.length)
                result.endsWith(nbspUnicodeChar) -> result.substring(0, result.length - 1)
                else -> break
            }
        }
        return result
    }
}