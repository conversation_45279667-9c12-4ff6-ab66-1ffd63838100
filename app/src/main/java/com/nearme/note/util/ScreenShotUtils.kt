/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/12/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  zhuyixia       2023/12/20      1.0     create file
 ****************************************************************/
package com.nearme.note.util

import android.graphics.Bitmap
import android.graphics.Canvas
import android.text.TextUtils
import android.view.View
import android.widget.FrameLayout
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.nearme.note.MyApplication
import com.nearme.note.activity.richedit.RichAdapter
import com.nearme.note.activity.richedit.entity.Data
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

object ScreenShotUtils {
    var TAG = "ScreenShotUtils"
    var isShow = false
    var num = 0
    private val capturePath: String by lazy {
        val context = MyApplication.appContext
        var newPackagePath = context.filesDir.parent
        if (TextUtils.isEmpty(newPackagePath)) {
            // In case of return null, hardcode data/data
            newPackagePath = "/data/data/" + context.packageName
        }
        "$newPackagePath/capture/"
    }

    @JvmStatic
    fun shotRecyclerView(view: RecyclerView, captureCallBack: CaptureListViewCallback?) {
        // 执行截图前  删除上一次的
        FileUtil.clearDirectory(File(capturePath))
        captureCallBack?.onCaptureStart()
        val adapter = view.adapter
        // itemView的宽度是RecyclerView宽度-padding
        val itemViewWidth = view.width - view.paddingStart - view.paddingEnd
        AppLogger.BASIC.d(TAG, "itemViewWidth = $itemViewWidth")
        if (adapter != null) {
            val size = adapter.itemCount
            GlobalScope.launch(Dispatchers.IO) {
                for (i in 0 until size) {
                    val holder = adapter.createViewHolder(view, adapter.getItemViewType(i))
                    adapter.onBindViewHolder(holder, i)
                    holder.itemView.measure(
                        View.MeasureSpec.makeMeasureSpec(itemViewWidth, View.MeasureSpec.EXACTLY),
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                    )
                    holder.itemView.layout(
                        0, 0, holder.itemView.measuredWidth,
                        holder.itemView.measuredHeight
                    )
                    AppLogger.BASIC.d(TAG, "holder.itemView.measuredWidth = ${holder.itemView.measuredWidth}")
                    val itemBitmap = Bitmap.createBitmap(
                        holder.itemView.measuredWidth,
                        holder.itemView.measuredHeight,
                        Bitmap.Config.ARGB_8888
                    )
                    val canvas = Canvas(itemBitmap)
                    withContext(Dispatchers.Main) {
                        holder.itemView.draw(canvas)
                    }
                    saveBitmap(itemBitmap, getCaptureFilePath(i))
                }
                withContext(Dispatchers.Main) {
                    captureCallBack?.onCaptureEnd(size)
                }
            }
        } else {
            captureCallBack?.onCaptureEnd(0)
        }
    }

    /**
     * 音频卡片截图
     */
    @JvmStatic
    fun shotSoundCard(recyclerView: RecyclerView?, soundCallBack: CaptureSoundCallback?) {
        if (recyclerView == null) {
            soundCallBack?.onCaptureEnd()
            AppLogger.BASIC.d(TAG, "recycleview is null")
            return
        }
        // 执行截图前  删除上一次的文件
        val file = File(getCaptureSoundFilePath())
        if (file.exists()) {
            file.delete()
        }
        val ad = recyclerView.adapter
        val size = ad?.itemCount ?: 0
        if (size == 0) {
            soundCallBack?.onCaptureEnd()
            AppLogger.BASIC.d(TAG, "recycleview size is 0")
            return
        }
        val defaultWidth = recyclerView.width ?: 0
        for (i in 0..size) {
            val type: Int? = ad?.getItemViewType(i)
            if (type == RichAdapter.VIEW_TYPE_VOICE) {
                val holder = ad.createViewHolder(recyclerView, type)
                ad.bindViewHolder(holder, i)
                val soundView = holder.itemView.findViewById<FrameLayout>(R.id.fl_voice_layout)
                // 设计要求word分享时 图片要居左显示 因此要去掉图片的内边距
                soundView.setPadding(0, 0, 0, 0)
                relayoutChildView(soundView, defaultWidth)

                val itemBitmap = Bitmap.createBitmap(
                    soundView.measuredWidth,
                    soundView.measuredHeight,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(itemBitmap)
                // 音频卡片需要显示在图片分享中
                soundView.draw(canvas)
                saveBitmap(itemBitmap, getCaptureSoundFilePath())
                break
            } else if (type == RichAdapter.VIEW_TYPE_SUMMARY_DOCUMENT) {
                val holder = ad.createViewHolder(recyclerView, type)
                ad.bindViewHolder(holder, i)
                val soundView = holder.itemView.findViewById<FrameLayout>(R.id.fl_doc_layout)
                val id = (holder.itemView.getTag(RichAdapter.TAG_PIC_ATTR) as? Data)?.attachment?.attachmentId ?: ""
                if (id.isEmpty()) {
                    AppLogger.BASIC.w(TAG, "id is null ,skip this data")
                    continue
                }
                val tempFile = File(getCaptureFileCardPath(id))
                if (tempFile.exists()) {
                    tempFile.delete()
                }
                soundView.setPadding(0, 0, 0, 0)
                relayoutChildView(soundView, defaultWidth)
                val itemBitmap = Bitmap.createBitmap(
                    soundView.measuredWidth,
                    soundView.measuredHeight,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(itemBitmap)
                // 文档摘要卡片显示
                soundView.draw(canvas)
                saveBitmap(itemBitmap, getCaptureFileCardPath(id))
                break
            }
        }
        soundCallBack?.onCaptureEnd()
    }


    @JvmStatic
    fun saveBitmap(bitmap: Bitmap, path: String) {
        kotlin.runCatching {
            FileUtil.saveBmpToFile(
                bitmap,
                path
            )
        }.onFailure {
            if (isShow) {
                // 避免弹出多次
                isShow = false
                GlobalScope.launch {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(
                            MyApplication.appContext,
                            R.string.image_generation_failed,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
            AppLogger.BASIC.d(TAG, "saveBitmap failure $path  error:${it.message}")
        }
    }


    @JvmStatic
    fun getCaptureFilePath(count: Int): String {
        return capturePath + "history" + count + ".png"
    }

    @JvmStatic
    fun getCaptureSoundFilePath(): String {
        return FileUtil.getCacheDir().path + File.separator + "sound.png"
    }

    /**
     * WebView文档摘要DOC分享临时截图路径
     */
    @JvmStatic
    fun getCaptureFileCardPath(): String {
        return FileUtil.getCacheDir().path + File.separator + "fileCard.png"
    }

    @JvmStatic
    fun getCaptureFileCardPath(id: String): String {
        return FileUtil.getCacheDir().path + File.separator + id + "_file.png"
    }

    @JvmStatic
    fun getToolkitScreenshotPath(): String {
        return FileUtil.getCacheDir().path + File.separator + "toolkit_screenshot.png"
    }

    @JvmStatic
    fun getImgFileListSize(): Int {
        val captureDir = File(capturePath)
        if (!captureDir.exists()) {
            return 0
        }
        val captures = captureDir.listFiles() ?: return 0
        return captures.size
    }

    interface CaptureListViewCallback {
        fun onCaptureStart()
        fun onCaptureEnd(totalCaptureCount: Int)
    }

    interface CaptureSoundCallback {
        fun onCaptureStart()
        fun onCaptureEnd()
    }

    @JvmStatic
    private fun relayoutChildView(childView: View?, defaultWidth: Int) {
        childView?.measure(
            View.MeasureSpec.makeMeasureSpec(defaultWidth, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        childView?.layout(0, 0, childView.measuredWidth, childView.measuredHeight)
    }
}