/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ActivityUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/8/18       1      create
 ***********************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.os.Build
import com.nearme.note.MyApplication
import com.oplus.note.logger.AppLogger

inline fun <reified T : Activity> isTopActivity(): Boolean {
    val activities = MyApplication.myApplication.activities
    if (activities.isNotEmpty()) {
        val activity = activities[activities.size - 1]
        AppLogger.BASIC.i("ActivityUtil", "activity:$activity")
        if (activity is T) {
            return true
        }
    }
    return false
}
fun Activity.getCurrentWindowWidth(): Int {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        val rect = windowManager.currentWindowMetrics.bounds
        kotlin.math.abs(rect.right - rect.left)
    } else {
        resources.displayMetrics.widthPixels
    }
}

fun Activity.getCurrentWindowHeight(): Int {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        val rect = windowManager.currentWindowMetrics.bounds
        kotlin.math.abs(rect.bottom - rect.top)
    } else {
        resources.displayMetrics.heightPixels
    }
}