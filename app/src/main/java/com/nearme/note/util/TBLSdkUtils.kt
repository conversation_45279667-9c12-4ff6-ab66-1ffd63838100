/**
 * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * File           : TBLSdkUtils.kt
 * Description    : TBLSdkUtils.kt
 * Version        : 1.0
 * Date           : 2024/3/7
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2024/3/7         1.0           create
 */
package com.nearme.note.util

import android.app.Application
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.heytap.tbl.webkit.TBLSdk
import com.oplus.note.logger.AppLogger
import com.oplus.note.plugin.PluginManager
import com.oplus.note.utils.cloud.AppCloudConfigUtil
import com.oplus.notes.webview.cache.api.IWebViewProxyCache.Companion.FORCE_USE_SYSTEM_WEB_VIEW
import java.io.File

class TBLSdkUtils {
    companion object {
        private const val TAG = "TBLSdkUtils"
        private const val PROCESS_TBL_RENDER = "tbl_privileged_process"
        private const val PROCESS_NOTE = "com.coloros.note"
        private const val FILE_NAME_TBL_APK = "com.heytap.tbl.webview"

        // TBL Core is pending
        private const val ERROR_CODE_0 = 0

        // TBL Core matches the SDK
        private const val ERROR_CODE_1 = 1

        // TBL Core is not found
        private const val ERROR_CODE_2 = 2

        // The version of TBL Core is older than SDK
        private const val ERROR_CODE_3 = 3

        // The version of TBL Core is newer than SDK
        private const val ERROR_CODE_4 = 4

        // Missing certain necessary library
        private const val ERROR_CODE_5 = 5

        // Mismatch of CPU Architecture for TBL Core
        private const val ERROR_CODE_6 = 6

        // TBL Core signature verification failed
        private const val ERROR_CODE_7 = 7

        // TBL Core doesn't match SDK
        private const val ERROR_CODE_8 = 8

        // TBL So version code doesn't match current used
        private const val ERROR_CODE_9 = 9

        // The downloaded TBL APK version code is invalid
        private const val ERROR_CODE_10 = 10

        // TBL Core APK download failed
        private const val ERROR_CODE_11 = 11

        // TBL WebView hook failed
        private const val ERROR_CODE_12 = 12

        // TBL Core APK extract failed
        private const val ERROR_CODE_13 = 13

        // TBL Core APK decompress failed
        private const val ERROR_CODE_14 = 14

        // TBL Core add resource failed
        private const val ERROR_CODE_15 = 15

        // Set Core version code failed
        private const val ERROR_CODE_17 = 17

        // The downloaded TBL APK is invalid
        private const val ERROR_CODE_18 = 18

        // Get downloaded TBL APK info failed
        private const val ERROR_CODE_19 = 19

        // Older Version TBL Core
        private const val ERROR_CODE_21 = 21

        // TBL Core APK not exist in path
        private const val ERROR_CODE_22 = 22

        // SDK internal error
        private const val ERROR_CODE_23 = 23

        // Build TBL Core dir failed
        private const val ERROR_CODE_24 = 24

        @JvmStatic
        val coreReadyLiveData: MutableLiveData<Boolean> = MutableLiveData(false)

        val instance by lazy { TBLSdkUtils() }
    }

    /**
     * https://odocs.myoas.com/docs/L9kBMzxjO5igdgqK?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
     *
     * 按文档详情，需要在主线程中调用。
     */
    fun init(application: Application) {
        AppLogger.BASIC.d(TAG, "init: process=${Application.getProcessName()}, pkg:${application.packageName}")
        kotlin.runCatching {

            TBLSdk.disablePreInit()
            TBLSdk.initTBLEnvironment(application, object : TBLSdk.TBLSdkInitCallback {
                override fun onCoreReady() {
                    checkUseSystemWebView(application)
                    coreReadyLiveData.postValueSafe(true)
                    AppLogger.BASIC.d(TAG, "onCoreReady")
                }

                override fun onInitFinish() {
                    AppLogger.BASIC.d(TAG, "onInitFinish: getTBLCoreVersion=${getTBLCoreVersion()}")
                }

                override fun onInitError(errorCode: Int) {
                    AppLogger.BASIC.d(TAG, "onInitError: errorCode=$errorCode")
                    checkUseSystemWebView(application)
                    coreReadyLiveData.postValueSafe(true)
                }
            })
        }.onFailure {
            AppLogger.BASIC.e(TAG, "init: ${it.message}")
        }
        AppCloudConfigUtil.tblRefreshCallback = {
            checkAndDownloadNewestTBLApk(application)
            AppCloudConfigUtil.tblRefreshCallback = null
        }
    }

    private fun checkUseSystemWebView(application: Application) {
        val coreVersion = getTBLCoreVersion()
        AppLogger.BASIC.d(TAG, "checkUseSystemWebView coreVersion=$coreVersion")
        if (FORCE_USE_SYSTEM_WEB_VIEW || (coreVersion < AppCloudConfigUtil.getTblCloudConfig(application.applicationContext).minCoreVersion)) {
            AppLogger.BASIC.d(TAG, "forceUseSystemWebView")
            TBLSdk.forceUseSystemWebView()
        }
    }

    fun isRenderProcess(): Boolean {
        val curProcess = Application.getProcessName()
        return curProcess.contains(PROCESS_TBL_RENDER)
    }

    private fun isMainProcess(context: Context): Boolean {
        val curProcess = Application.getProcessName()
        return curProcess == context.packageName
    }

    private fun getTBLCoreVersion(): Int {
        var version = -1
        kotlin.runCatching {
            version = TBLSdk.getCoreVersion()
        }.onFailure {
            AppLogger.BASIC.d(TAG, "getTBLCoreVersion: ${it.message}")
        }
        return version
    }

    fun checkAndDownloadNewestTBLApk(context: Context) {
        val tblKitVersionCode = TBLSdk.getCoreVersion()
        if (isMainProcess(context)) {
            if (downloadTBLPluginByLocal(context, tblKitVersionCode)) {
                return
            }
            val mspEnableFromCloud = AppCloudConfigUtil.getMspEnableFromCloud(context)
            AppLogger.THIRDLOG.d(TAG, "53020101,mspEnableFromCloud:$mspEnableFromCloud,currentTblVersion=$tblKitVersionCode")
            if (mspEnableFromCloud) {
                //msp可用,检查版本号是否满足要求
                KitSyncSdkUtils.fetchKitInfo(context) { _, kitInfo ->
                    AppLogger.BASIC.d(
                        TAG,
                        "checkAndDownloadNewestTBLApk kitVersionCode = ${kitInfo?.kitVersionCode} ,currVersionCode=$tblKitVersionCode"
                    )
                    kitInfo?.let {
                        if (checkTBLVersionEnable(context, tblKitVersionCode, it.kitVersionCode)) {
                            val path = getDownloadApkPath(context, it.kitVersionCode)
                            val tblFile = File(path)
                            if (tblFile.exists()) {
                                AppLogger.BASIC.d(TAG, "checkAndDownloadNewestTBLApk, file exist")
                                AppLogger.THIRDLOG.d(
                                    TAG,
                                    "53020101,mspEnableFromCloud:true,currentTblVersion=$tblKitVersionCode," +
                                            "mspTblVersion${it.kitVersionCode}"
                                )
                                TBLSdk.setTBLApkPath(path)
                            } else {
                                KitSyncSdkUtils.downloadFile(context, it, tblFile) { downloadCode ->
                                    AppLogger.BASIC.d(TAG, "checkAndDownloadNewestTBLApk, downloadCode:$downloadCode")
                                    if (downloadCode == ERROR_CODE_0) {
                                        TBLSdk.setTBLApkPath(path)
                                        AppLogger.THIRDLOG.d(
                                            TAG,
                                            "53020101,mspEnableFromCloud:true,currentTblVersion=$tblKitVersionCode," +
                                                    "mspTblVersion${it.kitVersionCode}"
                                        )
                                    } else {
                                        downloadTBLPlugin(context, tblKitVersionCode)
                                    }
                                }
                            }
                        } else {
                            downloadTBLPlugin(context, tblKitVersionCode)
                        }
                    } ?: downloadTBLPlugin(context, tblKitVersionCode)
                }
            } else {
                downloadTBLPlugin(context, tblKitVersionCode)
            }
        }
    }

    /**
     * 从本地复制
     * @param tblKitVersionCode 当前tbl版本
     */
    private fun downloadTBLPluginByLocal(context: Context, tblKitVersionCode: Int): Boolean {
        val localDownloader = PluginManager.getPluginDownloader(PluginManager.PLUGIN_DOWNLOADER_LOCAL) ?: return false
        val tblKitVersionFromPlugin = localDownloader.getPluginVersion(PluginManager.PLUGIN_TBL)
        if (!checkTBLVersionEnable(context, tblKitVersionCode, tblKitVersionFromPlugin)) {
            return false
        }
        AppExecutors.getInstance().executeCommandInDiskIO {
            val destFile = File(getDownloadApkPath(context, tblKitVersionFromPlugin))
            localDownloader.copyPluginFile(PluginManager.PLUGIN_TBL, context, destFile)
            if (destFile.exists()) {
                TBLSdk.setTBLApkPath(destFile.absolutePath)
            }
        }
        return true
    }


    /**
     * 从oms下载
     * @param tblKitVersionCode 当前tbl版本
     */
    private fun downloadTBLPlugin(context: Context, tblKitVersionCode: Int) {
        if (AppCloudConfigUtil.getOmsEnableFromCloud(context)) {
            // 调用 PluginManager 下载插件
            if (!PluginManager.isPluginDownloaderEnable(PluginManager.PLUGIN_DOWNLOADER_OMS)) {
                AppLogger.BASIC.d(TAG, "downloadTBLPlugin PluginManager.isPluginEnable() is false")
                return
            }
            val pluginManager = PluginManager.getPluginDownloader(PluginManager.PLUGIN_DOWNLOADER_OMS)
            AppExecutors.getInstance().executeCommandInDiskIO {
                AppLogger.BASIC.d(TAG, "downloadTBLPlugin")
                pluginManager?.downloadPlugin(PluginManager.PLUGIN_TBL) {
                    AppLogger.BASIC.d(TAG, "downloadTBLPlugin done")
                    val tblKitVersionFromPlugin = pluginManager.getPluginVersion(PluginManager.PLUGIN_TBL)
                    AppLogger.BASIC.d(TAG, "downloadTBLPlugin = $tblKitVersionFromPlugin")

                    AppLogger.THIRDLOG.d(
                        TAG,
                        "53020201,omsEnableFromCloud:true,currentTblVersion=$tblKitVersionCode,omsTblVersion=$tblKitVersionFromPlugin"
                    )

                    if (checkTBLVersionEnable(context, tblKitVersionCode, tblKitVersionFromPlugin)) {
                        val destFile = File(getDownloadApkPath(context, tblKitVersionFromPlugin))
                        pluginManager.copyPluginFile(PluginManager.PLUGIN_TBL, context, destFile)
                        AppLogger.BASIC.d(TAG, "downloadTBLPlugin destFile length = ${destFile.length()}")
                        if (destFile.exists()) {
                            TBLSdk.setTBLApkPath(destFile.absolutePath)
                            AppLogger.BASIC.d(TAG, "downloadTBLPlugin TBLSdk.setTBLApkPath success")
                        } else {
                            AppLogger.BASIC.d(TAG, "downloadTBLPlugin destFile not exists")
                        }
                    } else {
                        AppLogger.BASIC.d(TAG, "checkTBLVersionEnable false, not to TBLSdk.setTBLApkPath")
                    }
                }
            }
        } else {
            AppLogger.THIRDLOG.d(TAG, "53020201,omsEnableFromCloud:false,currentTblVersion=$tblKitVersionCode")
        }
    }

    /**
     * 判断当前需要下载的tbl版本是否符合云控要求
     * @param currentVersion 当前tbl版本
     * @param downloadVersionCode 需要下载的tbl版本
     */
    private fun checkTBLVersionEnable(context: Context, currentVersion: Int, downloadVersionCode: Int): Boolean {
        //云控最低版本号
        val minCoreVersion = AppCloudConfigUtil.getTblCloudConfig(context).minCoreVersion
        //云控不支持的版本号
        val disableVersions = AppCloudConfigUtil.getTblCloudConfig(context).disableVersions
        val enable = downloadVersionCode > currentVersion && downloadVersionCode >= minCoreVersion && !disableVersions.contains(currentVersion)
        AppLogger.BASIC.d(TAG, "checkTBLVersionEnable $enable,minCoreVersion=$minCoreVersion,disableVersions=$disableVersions")
        return enable
    }

    private fun getDownloadApkPath(context: Context, versionCode: Int): String {
        return context.filesDir.absolutePath + File.separator + "$FILE_NAME_TBL_APK-$versionCode.apk"
    }
}