/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - DateUtils.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2024/4/22
 * Author: W9057214
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9057214                        2024/4/22         1.0    Create this module
 **********************************************************************************/
package com.nearme.note.util

import android.text.SpannableStringBuilder
import androidx.core.text.HtmlCompat
import com.nearme.note.MyApplication
import com.nearme.note.setting.privacypolicy.html.HtmlParser
import com.oplus.note.BuildConfig
import com.oplus.note.os.OsConfigurations

object WaterMark {

    private const val BRAND_OPPO = "OPPO"
    private const val BRAND_ONE_PLUS = "OnePlus"
    private const val BRAND_REALME = "NEXT"
    /**
     * 外销：由 AI 生成
     * 一加内销：由 小布 AI生成
     * 其他内销：由 oppo AI生成
     */
    @JvmStatic
    fun getAIGCMarkTextByOS(isAikit: Boolean = false): String {
        val res = if (isAikit) {
            /*https://www.figma.com/design/JS4wSDzfrKpKl19yRTvraz/16.0AI%E7%BB%84%E4%BB%B6?node-id=1481-27102&p=f&t=lxFQEAyVlML3PQsV-0*/
            val brandName = if (OsConfigurations.isOppoBrand) {
                BRAND_OPPO
            } else if (OsConfigurations.isOneplus) {
                if (BuildConfig.isExport) {
                    BRAND_ONE_PLUS
                } else {
                    BRAND_OPPO
                }
            } else {
                BRAND_REALME
            }
            MyApplication.appContext.getString(com.oplus.note.baseres.R.string.create_generate_end_tips, "<b>$brandName AI</b>")
        } else {
            /**
             * 外销：由 AI 生成
             * 一加内销：由 小布 AI生成
             * 其他内销：由 oppo AI生成
             */
            if (BuildConfig.isExport) {
                MyApplication.appContext.getString(com.oplus.note.baseres.R.string.aigc_text_create_by_hint)
            } else if (OsConfigurations.isOppoBrand) {
                MyApplication.appContext.getString(com.oplus.note.baseres.R.string.create_by_oppo_ai_new)
            } else {
                MyApplication.appContext.getString(com.oplus.note.baseres.R.string.create_by_xiaobu_ai_new)
            }
        }
        return res
    }

    /** 同 getAIGCMarkTextByOS ，只是去掉 string 中的 html 标签*/
    @JvmStatic
    fun getAIGCMarkTextWithHtmlTag(): String {
        val res = if (BuildConfig.isExport) {
            MyApplication.appContext.getString(com.oplus.note.baseres.R.string.aigc_text_create_by_hint)
        } else if (OsConfigurations.isOppoBrand) {
            removeHtmlTag(MyApplication.appContext.getString(com.oplus.note.baseres.R.string.create_by_oppo_ai_new))
        } else {
            removeHtmlTag(MyApplication.appContext.getString(com.oplus.note.baseres.R.string.create_by_xiaobu_ai_new))
        }
        return res
    }

    /**去掉 string 中的 html 的标签*/
    @JvmStatic
    private fun removeHtmlTag(input: String): String {
        val output = HtmlParser.fromHtml(
            input, HtmlCompat.FROM_HTML_MODE_LEGACY, null, null
        ) as SpannableStringBuilder
        return output.toString()
    }
}