/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.nearme.note.util;

/**
 * <AUTHOR>
 *
 *         TODO To change the template for this generated type comment go to Window - Preferences -
 *         Java - Code Style - Code Templates
 */

import android.annotation.SuppressLint;
import androidx.annotation.NonNull;
import androidx.annotation.VisibleForTesting;

import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

public class RandomGUID extends Object {

    private static final int PAD_BELOW = 0x10;
    private static final int TWO_BYTES = 0xFF;

    private static final int INDEX_0 = 0;
    private static final int INDEX_8 = 8;
    private static final int INDEX_12 = 12;
    private static final int INDEX_16 = 16;
    private static final int INDEX_20 = 20;

    private static final int LENGHT_64 = 64;

    private static Random sRand;
    private static SecureRandom sSecureRand;

    private static String sId = "null";

    private String mValueBeforeMD5 = "";
    private String mValueAfterMD5 = "";


    /*
     * Static block to take care of one time secureRandom seed. It takes a few seconds to initialize
     * SecureRandom. You might want to consider removing this static block or replacing it with a
     * "time since first loaded" seed to reduce this time. This block will run only once per JVM
     * instance.
     */

    static {
        sSecureRand = new SecureRandom();
        long secureInitializer = sSecureRand.nextLong();
        sRand = new Random(secureInitializer);
        try {
            sId = InetAddress.getLocalHost().toString();
        } catch (UnknownHostException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /*
     * Default constructor. With no specification of security option, this constructor defaults to
     * lower security, high performance.
     */
    public RandomGUID() {
        getRandomGUID(false);
    }

    /*
     * Constructor with security option. Setting secure true enables each random number generated to
     * be cryptographically strong. Secure false defaults to the standard Random function seeded
     * with a single cryptographically strong random number.
     */
    private RandomGUID(boolean secure) {
        getRandomGUID(secure);
    }

    /*
     * Method to generate the random GUID
     */
    @SuppressLint("UnsafeHashAlgorithmDetector")
    private void getRandomGUID(boolean secure) {
        MessageDigest md5 = null;
        StringBuilder sbValueBeforeMD5 = new StringBuilder(128);

        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("");
        }

        try {
            long time = System.currentTimeMillis();
            long rand = 0;

            if (secure) {
                rand = sSecureRand.nextLong();
            } else {
                rand = sRand.nextLong();
            }
            sbValueBeforeMD5.append(sId);
            sbValueBeforeMD5.append(":");
            sbValueBeforeMD5.append(Long.toString(time));
            sbValueBeforeMD5.append(":");
            sbValueBeforeMD5.append(Long.toString(rand));

            mValueBeforeMD5 = sbValueBeforeMD5.toString();
            md5.update(mValueBeforeMD5.getBytes("UTF-8"));

            byte[] array = md5.digest();
            StringBuilder sb = new StringBuilder(32);
            for (int j = 0; j < array.length; ++j) {
                int b = array[j] & TWO_BYTES;
                if (b < PAD_BELOW) {
                    sb.append('0');
                }
                sb.append(Integer.toHexString(b));
            }
            mValueAfterMD5 = sb.toString();

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * Convert to the standard format for GUID (Useful for SQL Server UniqueIdentifiers, etc.)
     * Example: C2FEEEAC-CFCD-11D1-8B05-00600806D9B6
     */
    @Override
    public String toString() {
        return toStringForNote("-");
    }

    @VisibleForTesting
    String toStringForNote(String separator) {
        String raw = mValueAfterMD5.toUpperCase();
        StringBuilder sb = new StringBuilder(LENGHT_64);
        sb.append(raw.substring(INDEX_0, INDEX_8));
        sb.append(separator);
        sb.append(raw.substring(INDEX_8, INDEX_12));
        sb.append(separator);
        sb.append(raw.substring(INDEX_12, INDEX_16));
        sb.append(separator);
        sb.append(raw.substring(INDEX_16, INDEX_20));
        sb.append(separator);
        sb.append(raw.substring(INDEX_20));

        return sb.toString();
    }

    @NonNull
    public static String createGuid() {
        return new RandomGUID(true).toStringForNote("_");
    }

    // Demonstraton and self test of class
    public static void main(String args[]) {
        // for (int i=0; i< 100; i++) {
        // RandomGUID myGUID = new RandomGUID();
        // System.out.println("Seeding String=" + myGUID.valueBeforeMD5);
        // System.out.println("rawGUID=" + myGUID.valueAfterMD5);
        // System.out.println("RandomGUID=" + myGUID.toString());
        // }
        long st = System.currentTimeMillis();
        RandomGUID myGUID = new RandomGUID(true);
        // System.out.println("Seeding String=" + myGUID.valueBeforeMD5);
        // System.out.println("rawGUID=" + myGUID.valueAfterMD5);
        System.out.println("RandomGUID=" + myGUID.toString() + ","
                + (int) (System.currentTimeMillis() - st));
    }

}