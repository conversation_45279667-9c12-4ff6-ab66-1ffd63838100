/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - OperationSPUtil.kt
 * Description: 记录云控运营相关数据
 *
 * Version: 1.0
 * Date: 2024/7/29
 * Author: W9028045
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * W9028045   2024/7/29    1.0              build this module
 ************************************************************************************************/
package com.nearme.note.util

import android.content.Context
import androidx.core.content.edit
import com.nearme.note.MyApplication
import com.oplus.note.utils.DateAndTimeUtils
import java.util.Date

object OperationSPUtil {

    private const val OPERATION_SHARED_PREFERENCE_NAME = "operation"
    private val sp by lazy {
        MyApplication.appContext.getSharedPreferences(
            OPERATION_SHARED_PREFERENCE_NAME,
            Context.MODE_PRIVATE
        )
    }
    private const val CLOUD_OPERATION_DATA_KEY_PREFIX = "cloud_operation_data_"
    private const val CLOUD_OPERATION_VALID_TIME = "cloud_valid_time"
    private const val INVALID_TIME = -1L

    /**
     * @param remindConfigId 运营Id
     * @param ignoreTime 忽略点击时间
     * @param validTime 运营id关联的有效期
     */
    @JvmStatic
    fun saveIgnoreTime(remindConfigId: String, ignoreTime: Long, validTime: Long) {
        sp.edit {
            putLong("${CLOUD_OPERATION_DATA_KEY_PREFIX}$remindConfigId", ignoreTime)
            putLong("${CLOUD_OPERATION_VALID_TIME}$remindConfigId", validTime)
            apply()
        }
    }

    @JvmStatic
    fun getIgnoreActionTime(remindConfigId: String): Long {
        return sp.getLong("${CLOUD_OPERATION_DATA_KEY_PREFIX}$remindConfigId", INVALID_TIME)
    }

    @JvmStatic
    fun isIgnoreAction(remindConfigId: String): Boolean {
        val remindConfigIdList = mutableSetOf<String>()
        val mutableMap = sp.all
        mutableMap.entries.filter {
            it.key.startsWith(CLOUD_OPERATION_DATA_KEY_PREFIX)
        }.map {
            it.key.split(CLOUD_OPERATION_DATA_KEY_PREFIX).last()
        }.let {
            remindConfigIdList.addAll(it)
        }
        return remindConfigIdList.contains(remindConfigId)
    }

    /**
     * 当天是否点击过忽略，降序排列
     */
    @JvmStatic
    fun isClickIgnoreToday(): Boolean {
        val currentMillis = System.currentTimeMillis()
        val mutableMap = sp.all
        mutableMap.entries.filter {
            it.key.startsWith(CLOUD_OPERATION_DATA_KEY_PREFIX)
        }.sortedByDescending {
            (it.value as Long)
        }.filter {
            val dateNow = Date(currentMillis)
            val dateRecord = Date(it.value as Long)
            DateAndTimeUtils.isSameDay(dateNow, dateRecord)
        }.let {
            return it.isNotEmpty()
        }
    }
}