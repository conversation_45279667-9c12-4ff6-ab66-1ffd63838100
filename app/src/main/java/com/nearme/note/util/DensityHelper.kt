package com.nearme.note.util

import android.content.Context
import android.util.DisplayMetrics
import androidx.annotation.DimenRes
import com.oplus.note.logger.AppLogger
import com.nearme.note.MyApplication
import com.oplus.richtext.core.utils.RichUiHelper

object DensityHelper {

    private const val HALF_ONE = 0.5f

    /**不跟随系统显示大小变化的 dimen 值*/
    @JvmStatic
    fun getDefaultConfigDimension(@DimenRes id: Int): Int {
        val resources = MyApplication.appContext.resources
        val physicalDensity = RichUiHelper.density / (DisplayMetrics.DENSITY_DEFAULT).toFloat()

        AppLogger.BASIC.d("DensityHelper", "$physicalDensity")
        return (resources.getDimension(id) / resources.displayMetrics.density *physicalDensity).toInt()
    }

    /**不跟随系统显示大小变化的 dimen 值*/
    @JvmStatic
    fun getDefaultConfigDimension(context: Context?, @DimenRes id: Int): Int {
        val resources = if (context == null) MyApplication.application.resources else context.resources
        val physicalDensity = RichUiHelper.density / (DisplayMetrics.DENSITY_DEFAULT).toFloat()
        AppLogger.BASIC.d("DensityHelper", "$physicalDensity")
        return (resources.getDimension(id) / resources.displayMetrics.density *physicalDensity).toInt()
    }

    @JvmStatic
    fun px2dip(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + HALF_ONE).toInt()
    }
}