/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9060       2023/10/21      1.0     create file
 ****************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import com.nearme.note.MyApplication

object KeyboardUtils {
    private val inputMethodManager by lazy {
        MyApplication.appContext.getSystemService(InputMethodManager::class.java)
    }

    @JvmStatic
    fun TextView.showSoftInput() {
        requestFocus()
        inputMethodManager.showSoftInput(this, 0)
    }

    @JvmStatic
    fun hideSoftInput(activity: Activity) {
        val view = activity.window.decorView
        inputMethodManager.hideSoftInputFromWindow(
            view.windowToken,
            0
        )
    }
}