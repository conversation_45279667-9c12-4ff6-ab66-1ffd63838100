/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util;

import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.provider.Settings;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.coui.appcompat.darkmode.COUIDarkModeUtil;
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil;
import com.coui.appcompat.version.COUIVersionUtil;
import com.oplus.note.R;
import com.oplus.note.logger.AppLogger;

public class NavigateUtils {
    private static final String TAG = "NavigateUtils";
    private static final String NAVIGATE_UP_TITLE_ID = "navigate_title_id";
    private static final String NAVIGATE_UP_TITLE_TEXT = "navigate_title_text";
    private static final String NAVIGATE_UP_PACKAGE = "navigate_parent_package";
    private static final String NAME_NAVIGATION_MODE = "navigation_mode";
    private static final int VIRTUAL_NAVIGATION_MODE_FULL_SCREEN_GESTURE = 2;

    public static void putTitleResId(Intent intent, int id) {
        intent.putExtra(NAVIGATE_UP_TITLE_ID, id);
    }

    public static void putTitleResIdForOtherApp(Context appContext, Intent intent, int id) {
        intent.putExtra(NAVIGATE_UP_TITLE_ID, id);
        if (appContext != null) {
            intent.putExtra(NAVIGATE_UP_PACKAGE, appContext.getPackageName());
        }
    }

    public static void updateStatusBar(Activity activity) {
        Window window = activity.getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    public static boolean baseOplusOS3() {
        try {
            if (COUIVersionUtil.getOSVersionCode() >= COUIVersionUtil.COUI_3_0) {
                return true;
            }
        } catch (Exception e) {
            AppLogger.BASIC.w("SDKUtils", "getOSVERSION() error");
        }
        return false;
    }

    public static int getStatusBarHeight(Context context) {
        try {
            int resId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
            return (resId > 0) ? context.getResources().getDimensionPixelSize(resId) : 0;
        } catch (Exception e) {
            AppLogger.BASIC.e(TAG, "getStatusBarHeight error: " + e.getMessage());
        }
        return 0;
    }

    @Deprecated
    public static void setStatusBarTransparentAndBlackFont(Activity mActivity, boolean isTransparent) {
        boolean isVirtualNavigationGesture = isFullScreenNavigationGesture(mActivity.getApplicationContext());
        AppLogger.BASIC.d(TAG, "setStatusBarTransparentAndBlackFont -> isVirtualNavigationGesture = "
                + isVirtualNavigationGesture + " ,isTransparent= " + isTransparent);
        Window window = mActivity.getWindow();
        View decorView = mActivity.getWindow().getDecorView();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
            window.setStatusBarColor(Color.TRANSPARENT);
            window.setNavigationBarColor((isTransparent && isVirtualNavigationGesture)
                    ? Color.TRANSPARENT : mActivity.getColor(R.color.note_navigation_bar_color));
        }
        int flag = decorView.getSystemUiVisibility();
        boolean white = mActivity.getResources().getBoolean(com.oplus.note.base.R.bool.note_is_status_white);
        if (baseOplusOS3() || (COUIVersionUtil.getOSVersionCode() == 0)) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            if (COUIDarkModeUtil.isNightMode(mActivity.getApplicationContext())) {
                flag &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                flag &= ~View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR;
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (!white) {
                        flag |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
                    } else {
                        flag |= View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
                    }
                } else {
                    flag |= COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT;
                }
            }
            decorView.setSystemUiVisibility(flag);
        }
    }

    public static boolean isFullScreenNavigationGesture(Context context) {
        if (context == null) {
            AppLogger.BASIC.d(TAG, "isFullScreenNavigationGesture: context is null.");
            return false;
        }
        ContentResolver contentResolver = context.getContentResolver();
        int navigationMode = Settings.Secure.getInt(contentResolver, NAME_NAVIGATION_MODE, 0);
        return navigationMode == VIRTUAL_NAVIGATION_MODE_FULL_SCREEN_GESTURE;
    }
}
