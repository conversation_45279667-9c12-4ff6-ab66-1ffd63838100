/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : DebounceHelper.kt
 * Description    : 用于短时间内收到action，忽略之前的action，只执行最后一次的action
 * Version        : 1.0
 * Date           : 2025/4/29
 * Author         : fan.luo
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * fan.luo     2025/4/29        1.0           create
 */

package com.nearme.note.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class DebounceHelper(private val delayMillis: Long, private val scope: CoroutineScope) {

    private var job: Job? = null

    fun debounce(action: () -> Unit) {
        job?.cancel()
        job = scope.launch {
            delay(delayMillis)
            action()
        }
    }
}