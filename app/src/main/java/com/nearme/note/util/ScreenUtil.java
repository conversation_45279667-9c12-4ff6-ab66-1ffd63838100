package com.nearme.note.util;

import static com.nearme.note.view.helper.UiHelper.isDevicePad;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import com.nearme.note.MyApplication;
import com.nearme.note.activity.richedit.QuickNoteViewRichEditActivity;
import com.nearme.note.view.helper.UiHelper;
import com.oplus.note.logger.AppLogger;

public class ScreenUtil {

    private static final int MIDDLE_AND_LARGE_SCREEN_SW_VALUE = 480;
    private static final int MIDDLE_SCREEN_SW_VALUE = 600;
    private static final int UNFOLD_SCREEN_SW_VALUE = 2000;
    private static final int LARGE_SCREEN_SW_VALUE = 840;
    private static final int PARENT_MIDDLE_SCREEN_SW_VALUE = 280;
    private static final int PARENT_LARGE_SCREEN_SW_VALUE = 360;
    private static final int SHOW_BUBBLE_TIPS_SCREEN_MH_VALUE = 450;

    /**
     * Return the real width of screen, in pixel.
     *
     * @return the real width of screen, in pixel
     */
    public static int getScreenWidth(@NonNull Context context) {
        Object service = context.getSystemService(Context.WINDOW_SERVICE);
        WindowManager wm = null;
        if (service instanceof WindowManager) {
            wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        }
        if (wm == null) return -1;
        Point point = new Point();
        wm.getDefaultDisplay().getRealSize(point);
        return point.x;
    }

    public static int getScreenWidth() {
        WindowManager wm = (WindowManager) MyApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        if (wm == null) return -1;
        Point point = new Point();
        wm.getDefaultDisplay().getRealSize(point);
        return point.x;
    }

    public static int getScreenWidthSize() {
        WindowManager wm = (WindowManager) MyApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        if (wm == null) return -1;
        Point point = new Point();
        wm.getDefaultDisplay().getSize(point);
        return point.x;
    }

    /**
     * Return the real height of screen, in pixel.
     *
     * @return the real height of screen, in pixel
     */
    public static int getScreenHeight(@NonNull Context context) {
        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        if (wm == null) return -1;
        Point point = new Point();
        wm.getDefaultDisplay().getRealSize(point);
        return point.y;
    }

    public static int getScreenHeight() {
        WindowManager wm = (WindowManager) MyApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE);
        if (wm == null) return -1;
        Point point = new Point();
        wm.getDefaultDisplay().getRealSize(point);
        return point.y;
    }

    public static Bitmap zoomImg(Bitmap bm, View view) {
        if (view.getMeasuredHeight() == 0 || view.getMeasuredWidth() == 0) {
            return bm;
        }
        int width = bm.getWidth();
        int height = bm.getHeight();
        float scaleWidth = ((float) view.getMeasuredWidth()) / width;
        float scaleHeight = ((float) view.getMeasuredHeight()) / height;
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        Bitmap newbm = Bitmap.createBitmap(bm, 0, 0, width, height, matrix, true);
        return newbm;
    }

    public static Bitmap drawableToBitmap(Drawable drawable) {
        Bitmap bitmap = null;
        int width = drawable.getIntrinsicWidth();
        int height = drawable.getIntrinsicHeight();
        Bitmap.Config config = Bitmap.Config.ARGB_8888;
        bitmap = Bitmap.createBitmap(width, height, config);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, width, height);
        drawable.draw(canvas);
        return bitmap;
    }

    /**
     * 判断详情或子级是否为小屏宽度
     */
    public static Boolean isEditScreenSmallScreen(int screenW, boolean twoPane) {
        int screenWidth = screenW;
        if (twoPane) {
            screenWidth = getEditScreenWidth(screenW);
        }
        return screenWidth < MIDDLE_SCREEN_SW_VALUE;
    }

    /**
     *
     * 获取子级宽度<br>
     *
     * 小于600为小屏   600小于840之间中屏   大于840为大屏
     * 父子宽度，当窗口宽度<840,父级为280dp；窗口宽度>840,父级为360dp
     */
    public static int getEditScreenWidth(int screenW) {
        int screenWidth = screenW;
        if (screenW >= MIDDLE_SCREEN_SW_VALUE && screenW < LARGE_SCREEN_SW_VALUE) {
            screenWidth = screenW - PARENT_MIDDLE_SCREEN_SW_VALUE;
        } else if (screenW >= LARGE_SCREEN_SW_VALUE) {
            screenWidth = screenW - PARENT_LARGE_SCREEN_SW_VALUE;
        }
        return screenWidth;
    }

    public static Boolean isFoldModeOpenSmallScreen(int screenW, boolean twoPane, boolean isMultiWindow) {
        if( (screenW < MIDDLE_AND_LARGE_SCREEN_SW_VALUE) && isMultiWindow &&  !twoPane) {
            return true;
        }
        return false;
    }

    public static float getMineScreenWidth(Activity activity) {
        if (activity == null) {
            WindowManager wm = (WindowManager) MyApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                return -1;
            }
            Point point = new Point();
            wm.getDefaultDisplay().getSize(point);
            return point.x;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowManager windowManager = (WindowManager) activity.getSystemService(Context.WINDOW_SERVICE);
            if (windowManager != null) {
                return windowManager.getCurrentWindowMetrics().getBounds().width();
            } else {
                AppLogger.BASIC.e("ScreenUtil", "window manager is null");
                return 0;
            }
        } else {
            return activity.getWindow().getDecorView().getWidth();
        }
    }

    public static float getMineScreenHeight(Activity activity) {
        if (activity == null) {
            WindowManager wm = (WindowManager) MyApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE);
            if (wm == null) {
                return -1;
            }
            Point point = new Point();
            wm.getDefaultDisplay().getSize(point);
            return point.y;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowManager windowManager = (WindowManager) activity.getSystemService(Context.WINDOW_SERVICE);
            if (windowManager != null) {
                return windowManager.getCurrentWindowMetrics().getBounds().height();
            } else {
                AppLogger.BASIC.e("ScreenUtil", "window manager is null");
                return 0;
            }
        } else {
            return activity.getWindow().getDecorView().getHeight();
        }
    }

    public static boolean isMidScreen(Activity activity) {
        return activity != null && DensityHelper.px2dip(activity, getMineScreenWidth(activity)) < LARGE_SCREEN_SW_VALUE && DensityHelper.px2dip(activity, getMineScreenWidth(activity)) > MIDDLE_SCREEN_SW_VALUE && !(activity instanceof QuickNoteViewRichEditActivity);
    }

    public static boolean isFoldLarge(Activity activity) {
        return UiHelper.isDeviceFold() && (DensityHelper.px2dip(activity, getMineScreenWidth(activity)) > MIDDLE_SCREEN_SW_VALUE ||
                (ScreenUtil.getScreenWidth() > UNFOLD_SCREEN_SW_VALUE && ScreenUtil.getScreenHeight() > UNFOLD_SCREEN_SW_VALUE));
    }

    public static boolean isEditScreenSmallScreen(Activity activity) {
        return activity != null && (DensityHelper.px2dip(activity, getMineScreenWidth(activity)) < MIDDLE_AND_LARGE_SCREEN_SW_VALUE || DensityHelper.px2dip(activity, getMineScreenHeight(activity)) < MIDDLE_AND_LARGE_SCREEN_SW_VALUE);
    }

    public static boolean isWindowHeightMore450(Activity activity) {
        return activity != null && DensityHelper.px2dip(activity, getMineScreenHeight(activity)) > SHOW_BUBBLE_TIPS_SCREEN_MH_VALUE;
    }

    public static boolean isLargeScreenRefinement(Activity activity) {
        return isFoldLarge(activity) || isDevicePad();
    }
}
