package com.nearme.note.util;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.oplus.cloud.data.Packet;
import com.oplus.cloud.data.PacketArray;
import com.oplus.cloud.data.PacketFactory;
import com.oplus.note.repo.todo.entity.StatusEnumConverters;
import com.oplus.note.repo.todo.entity.UUIDConverters;
import com.oplus.note.repo.todo.entity.ToDo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 用于转换 {@link ToDo} 和 {@link Packet}
 *
 * @see ToDo
 * @see Packet
 */
public class ToDoUtils {
    public static final String K_ITEM_ID = "itemId";
    public static final String K_GLOBAL_ID = "globalId";
    public static final String K_CREATE_TIME = "create_time";
    public static final String K_UPDATE_TIME = "update_time";
    public static final String K_ALARM_TIME = "alarm_time";
    public static final String K_FINISH_TIME = "finish_time";
    public static final String K_STATUS = "status";
    public static final String K_CONTENT = "content";
    public static final String K_EXTRA = "extra";

    private ToDoUtils() {
        // prevent instantiation
    }

    @NonNull
    public static List<ToDo> fromPacketArray(@NonNull PacketArray<?> packetArray) {
        final int size = packetArray.size();
        List<ToDo> list = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            list.add(fromPacket(packetArray.get(i)));
        }
        return list;
    }

    @NonNull
    public static ToDo fromPacket(@NonNull Packet<?> packet) {
        ToDo toDo = new ToDo();

        toDo.setLocalId(UUIDConverters.stringToUUID(packet.getString(K_ITEM_ID)));
        toDo.setGlobalId(UUIDConverters.stringToUUID(packet.getString(K_GLOBAL_ID)));
        toDo.setUpdateTime(timestampToDate(packet.getLong(K_UPDATE_TIME)));
        toDo.setCreateTime(timestampToDate(packet.getLong(K_CREATE_TIME)));
        toDo.setAlarmTime(timestampToDate(packet.getLong(K_ALARM_TIME)));
        toDo.setFinishTime(timestampToDate(packet.getLong(K_FINISH_TIME)));
        toDo.setStatus(StatusEnumConverters.intToEnum(packet.getInt(K_STATUS)));
        toDo.setContent(packet.getString(K_CONTENT));
        //toDo.setExtra(ToDoExtra.Companion.create(packet.getString(K_EXTRA)));
        return toDo;
    }

    @NonNull
    public static Packet<?> toPacket(@NonNull PacketFactory factory, @NonNull ToDo toDo) {
        final Packet packet = factory.newKv();

        packet.putString(K_ITEM_ID, UUIDConverters.UUIDToString(toDo.getLocalId()));
        packet.putString(K_GLOBAL_ID, UUIDConverters.UUIDToString(toDo.getGlobalId()));
        packet.putNumber(K_UPDATE_TIME, dateToTimestamp(toDo.getUpdateTime()));
        packet.putNumber(K_CREATE_TIME, dateToTimestamp(toDo.getCreateTime()));
        packet.putNumber(K_ALARM_TIME, dateToTimestamp(toDo.getAlarmTime()));
        packet.putNumber(K_FINISH_TIME, dateToTimestamp(toDo.getFinishTime()));
        packet.putInt(K_STATUS, StatusEnumConverters.enumToInt(toDo.getStatus()));
        packet.putString(K_CONTENT, toDo.getContent());
        if (null != toDo.getExtra()) {
            packet.putString(K_EXTRA, toDo.getExtra().toString());
        }
        return packet;
    }

    @Nullable
    public static Date timestampToDate(long timestamp) {
        return timestamp > 0 ? new Date(timestamp) : null;
    }

    public static long dateToTimestamp(@Nullable Date date) {
        return date == null ? 0 : date.getTime();
    }
}
