/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.app.NotificationManager;
import android.content.Context;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;

import com.oplus.note.logger.AppLogger;
import com.coui.appcompat.toolbar.COUIActionMenuView;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;

public class ReflectUtil {

    private static final String TAG = "ReflectUtil";

    @Deprecated
    public static boolean isInMultiWindowMode(Activity activity) {
        if (activity == null) {
            return false;
        }

        try {
            Class<?> activityClass = activity.getClass();
            Method method = activityClass.getMethod("isInMultiWindowMode");
            Object ret = method.invoke(activity);
            if (ret != null) {
                return (boolean) ret;
            }
        } catch (NoSuchMethodException e) {
            AppLogger.NOTE.e(TAG, "isInMultiWindowMode e=" + e);
        } catch (IllegalArgumentException e) {
            AppLogger.NOTE.e(TAG, "isInMultiWindowMode e=" + e);
        } catch (IllegalAccessException e) {
            AppLogger.NOTE.e(TAG, "isInMultiWindowMode e=" + e);
        } catch (InvocationTargetException e) {
            AppLogger.NOTE.e(TAG, "isInMultiWindowMode e=" + e);
        }
        return false;
    }

    @SuppressLint("SoonBlockedPrivateApi")
    public static final void stopTextActionMode(EditText editText) {
        Class<?> textViewClass = TextView.class;
        try {
            Method method = textViewClass.getDeclaredMethod("stopSelectionActionMode");
            method.setAccessible(true);
            method.invoke(editText);
        } catch (Exception e) {
            AppLogger.NOTE.w(TAG, "stopTextActionMode e=" + e);
            try {
                Method method = textViewClass.getDeclaredMethod("stopTextActionMode");
                method.setAccessible(true);
                method.invoke(editText);
            } catch (Exception e1) {
                AppLogger.NOTE.w(TAG, "stopTextActionMode e1=" + e1);
            }
        }
        try {
            Field field = textViewClass.getDeclaredField("LAST_CUT_OR_COPY_TIME");
            field.setAccessible(true);
            field.set(editText, (long) 0);
        } catch (Exception e) {
            AppLogger.NOTE.w(TAG, "stopTextActionMode e=" + e);
            try {
                Field field = textViewClass.getDeclaredField("sLastCutCopyOrTextChangedTime");
                field.setAccessible(true);
                field.set(editText, (long) 0);
            } catch (Exception e1) {
                AppLogger.NOTE.w(TAG, "stopTextActionMode e1=" + e1);
            }
        }
    }

    public static void setStatusBarFlag(Dialog alertDialog) {
        if (alertDialog == null) {
            return;
        }
        Window dialogWindow = alertDialog.getWindow();
        WindowManager.LayoutParams p = dialogWindow.getAttributes();
        Field isDisableStatusBar = null;
        try {
            isDisableStatusBar = WindowManager.LayoutParams.class.getDeclaredField("isDisableStatusBar");
            isDisableStatusBar.setAccessible(true);
            isDisableStatusBar.set(p, WindowManager.LayoutParams.TYPE_STATUS_BAR);
            dialogWindow.setAttributes(p);
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    private static Object createNoticeChannel(String id, CharSequence name, int importance) {
        try {
            Class cls = Class.forName("android.app.NotificationChannel");
            Constructor con = cls.getConstructor(String.class, CharSequence.class, int.class);
            return con.newInstance(id, name, importance);
        } catch (Exception e) {
            AppLogger.BASIC.e("createNoticeChannel", "error: " + e);
        }
        return null;
    }

    public static void initNoticeChannel(Context context, String id, CharSequence name, int importance) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        Object obj = createNoticeChannel(id, name, importance);
        if (null != obj) {
            try {
                Method createChannel = Class.forName("android.app.NotificationManager")
                        .getMethod("createNotificationChannel", new Class[]{obj.getClass()});
                createChannel.invoke(manager, obj);
                Method setName = Class.forName("android.app.NotificationChannel")
                        .getMethod("setName", CharSequence.class);
                setName.invoke(obj, name);
            } catch (Exception e) {
                AppLogger.BASIC.e("initNoticeChannel", "error: " + e);
            }
        }
    }

    public static void setMenuV6OverflowList(COUIActionMenuView colorActionMenuViewV6, ArrayList arrayList) {
        Class<?> v6Class = colorActionMenuViewV6.getClass();
        try {
            Field field = v6Class.getDeclaredField("mOverflowItems");
            field.setAccessible(true);
            field.set(colorActionMenuViewV6, arrayList);
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }

    }
}