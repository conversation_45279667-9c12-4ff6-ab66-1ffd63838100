/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: SnackBarManager.kt
 * Description: Manager for SnackBar
 *
 *
 * Version: 1.0
 * Date: 2023-02-08
 * Author: W9013986
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9013986                       2023-02-08    1.0    Create this module
 * W9005794                       2023-05-19    2.0    Update
 **********************************************************************************/
package com.nearme.note.util

import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.snackbar.COUISnackBar
import com.nearme.note.MyApplication
import com.nearme.note.view.LottieIconSnackBar
import com.oplus.note.R

object SnackBarManager {

    const val DURATION_DEFAULT = 5000
    const val DURATION_HAS_ICON = 5000

    fun show(params: SnackBarParams, actionText: String? = null, snackBarTextDirection: Int = -1, listener: View.OnClickListener? = null) {

        // 指定marginBottom的情况下使用当前指定的margin，否则使用默认的margin
        if (params.marginBottom > 0) {
            createSnackBar(params, 0).apply {
                actionText?.let {
                    this.setOnAction(it, listener)
                }
                if (snackBarTextDirection >= 0) {
                    this.textDirection = snackBarTextDirection
                }
            }.show()
        } else {
            if (params.view.rootWindowInsets == null) {
                return
            }
            val naviBarInsetsBottom = WindowInsetsCompat.toWindowInsetsCompat(params.view.rootWindowInsets)
                .getInsets(WindowInsetsCompat.Type.navigationBars()).bottom
            createSnackBar(params, naviBarInsetsBottom).apply {
                actionText?.let {
                    this.setOnAction(it, listener)
                }
                if (snackBarTextDirection >= 0) {
                    this.textDirection = snackBarTextDirection
                }
            }.show()
        }
    }

    fun showNew(params: SnackBarParams, actionText: String? = null, snackBarTextDirection: Int = -1, listener: View.OnClickListener? = null) {
        // 指定marginBottom的情况下使用当前指定的margin，否则使用默认的margin
        createSnackBar(params, 0).apply {
            actionText?.let {
                this.setOnAction(it, listener)
            }
            if (snackBarTextDirection >= 0) {
                this.textDirection = snackBarTextDirection
            }
        }.show()
    }

    fun showIconSnackBar(params: SnackBarParams, actionText: String? = null, listener: View.OnClickListener? = null): COUISnackBar {
        val snackBar = createSnackBar(params, 0).apply {
            actionText?.let {
                this.setOnAction(it, listener)
            }
        }.also { snackBar ->
            snackBar.showLottieIcon(R.raw.ai_generating)
            snackBar.contentView.updateMarginLayoutParams {
                marginStart = params.view.context.resources.getDimensionPixelSize(R.dimen.dp_6)
            }

            snackBar.actionView.setTextColor(
                COUIContextUtil.getAttrColor(
                    snackBar.actionView.context,
                    com.support.appcompat.R.attr.couiColorPrimary
                )
            )
            snackBar.actionView.invalidate()
            snackBar.setDismissWithoutAnimate(true)
            snackBar.show()
        }
        return snackBar
    }

    internal fun createSnackBar(
        params: SnackBarParams,
        naviBarHeight: Int
    ): LottieIconSnackBar {
        return with(params) {
            if (marginBottom > 0) {
                LottieIconSnackBar.make(view, text, duration, marginBottom)
            } else {
                val defaultMargin = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dp_88)
                if (naviBarHeight > 0) {
                    LottieIconSnackBar.make(view, text, duration, defaultMargin + naviBarHeight)
                } else {
                    LottieIconSnackBar.make(view, text, duration, defaultMargin)
                }
            }
        }
    }

    /**
     * 展示AIGC生成中SnackBar，此Snackbar左侧带有动画ICON
     * @param params snackbar构建参数
     * @param listener actionText点击回调
     */
    fun showAigcTextSnackBar(
        params: SnackBarParams,
        listener: View.OnClickListener? = null
    ): COUISnackBar {
        val snackBar = createSnackBar(params, 0)
        snackBar.showLottieIcon(R.raw.ai_generating)
        snackBar.textShowSingleLine(true)
        snackBar.contentView.updateMarginLayoutParams {
            marginStart = params.view.context.resources.getDimensionPixelSize(R.dimen.dp_6)
        }

        // 修改actionView布局样式为按钮
        snackBar.actionView?.apply {
            text = ""
            background = ContextCompat.getDrawable(
                params.view.context,
                com.oplus.note.baseres.R.drawable.icon_aigc_text_stop
            )
            this.updateLayoutParams {
                width = params.view.context.resources.getDimensionPixelOffset(R.dimen.dp_28)
                height = params.view.context.resources.getDimensionPixelOffset(R.dimen.dp_28)
            }
            this.updateMarginLayoutParams {
                marginEnd = params.view.context.resources.getDimensionPixelOffset(R.dimen.dp_10)
            }
            this.setOnClickListener {
                snackBar.dismiss()
                listener?.onClick(it)
            }
        }
        snackBar.duration = Integer.MAX_VALUE
        return snackBar
    }
}

data class SnackBarParams(
    val view: View,
    val text: String,
    val duration: Int = SnackBarManager.DURATION_DEFAULT,
    val marginBottom: Int = -1
)