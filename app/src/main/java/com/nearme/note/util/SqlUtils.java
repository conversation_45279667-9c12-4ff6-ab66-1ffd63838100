/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SqlUtils.java
 * * Description: SqlUtils.java
 * * Version: 1.0
 * * Date : 2020/1/8
 * * Author: <EMAIL>
 * * OPLUS Coding Static Checking Skip
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/8      1.0    build this module
 ****************************************************************/

package com.nearme.note.util;

import java.util.List;
import java.util.Set;

/**
 * Sql have the limit variable can't more than 999
 * <p>
 * Like : select * from notes where guid in (:guids)
 * if (:guids) is a List more than 999 items, Room will create 999 variables, so It will crash
 * <p>
 * The solution is : join all the ids manual
 */
public class SqlUtils {

    /**
     * format the strings to: 'aaaaaa','bbbbb','ccccc'
     *
     * @param ids
     * @return
     */
    public static String joinIds(Set<String> ids) {
        StringBuffer sb = new StringBuffer();
        String[] idArr = new String[ids.size()];
        idArr = ids.toArray(idArr);
        for (int i = 0; i < idArr.length; i++) {
            if (i == 0) {
                sb.append("'").append(idArr[i]).append("'");
            } else {
                sb.append(",'").append(idArr[i]).append("'");
            }
        }
        return sb.toString();
    }

    /**
     * format the strings to: 'aaaaaa','bbbbb','ccccc'
     *
     * @param ids
     * @return
     */
    public static String joinIds(List<String> ids) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < ids.size(); i++) {
            if (i == 0) {
                sb.append("'").append(ids.get(i)).append("'");
            } else {
                sb.append(",'").append(ids.get(i)).append("'");
            }
        }
        return sb.toString();
    }

    public static String sqliteEscape(String keyWord) {
        keyWord = keyWord.replace("/", "//");
        keyWord = keyWord.replace("'", "/'");
        keyWord = keyWord.replace("[", "/[");
        keyWord = keyWord.replace("]", "/]");
        keyWord = keyWord.replace("%", "/%");
        keyWord = keyWord.replace("&", "/&");
        keyWord = keyWord.replace("_", "/_");
        keyWord = keyWord.replace("(", "/(");
        keyWord = keyWord.replace(")", "/)");
        return keyWord;
    }
}
