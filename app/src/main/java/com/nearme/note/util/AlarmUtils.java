/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteAlarmUtils.java
 * * Description: Utils for inserting, removing, updating and querying note alarms
 * * Version: 1.0
 * * Date: 2019/7/18
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/7/18 1.0 build this module
 ****************************************************************/

package com.nearme.note.util;

import android.app.AlarmManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.ArrayMap;

import com.nearme.note.view.helper.UiHelper;
import com.oplus.note.logger.AppLogger;
import com.nearme.note.MyApplication;

import java.text.DateFormat;
import java.util.Date;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class AlarmUtils {
    public static final String TAG = AlarmUtils.class.getSimpleName();
    public static final String ALARM_TIME = "alarmTime";
    public static final String ALARM_TYPE = "alarmType";
    public static final String ALARM_TODO = "ALARM_TODO";
    public static final int FLAG_RECEIVER_INCLUDE_BACKGROUND = 0x01000000;
    private static final DateFormat DATE_FORMAT = DateFormat.getDateTimeInstance(
            DateFormat.SHORT, DateFormat.MEDIUM);

    public enum ControllerType {
        NOTE, TODO ,TODO_LATER
    }

    private static boolean sIsInitialized;
    private static AlarmManager sAlarmManager;

    private static ArrayMap<ControllerType, AlarmController> sControllerMap = new ArrayMap<>();

    static {
        sControllerMap.put(ControllerType.TODO, new ToDoAlarmController(MyApplication.getAppContext()));
        sControllerMap.put(ControllerType.TODO_LATER, new ToDoLaterAlarmController(MyApplication.getAppContext()));
        sControllerMap.put(ControllerType.NOTE, new RichNoteAlarmController(MyApplication.getAppContext()));
    }

    private AlarmUtils() {
    }

    public static void init() {
        if (sIsInitialized) {
            return;
        }
        sAlarmManager = (AlarmManager) MyApplication.getAppContext().getSystemService(Context.ALARM_SERVICE);
        sIsInitialized = true;
    }

    static AlarmManager getAlarmManager() {
        return sAlarmManager;
    }

    public interface ResetAlarmCallback {
        void onResetFinished();
    }


    public static void resetSystemAlarms(ControllerType controllerType) {
        init();
        resetSystemAlarms(controllerType, null);
    }

    private static void resetSystemAlarms(ControllerType controllerType, ResetAlarmCallback callback) {
        init();
        AlarmController alarmController = sControllerMap.get(controllerType);
        if (alarmController != null) {
            alarmController.resetSystemAlarms(MyApplication.getAppContext(), controllerType, callback);
        }
    }

    public static void resetAllSystemAlarms() {
        resetSystemAlarms(ControllerType.TODO, null);
        resetSystemAlarms(ControllerType.NOTE, null);
    }

    public static void resetSystemAlarmsAndCheck(ControllerType controllerType) {
        init();
        AlarmController alarmController = sControllerMap.get(controllerType);
        if (alarmController != null) {
            alarmController.resetSystemAlarmsAndCheck(MyApplication.getAppContext(), controllerType, null);
        }
    }

    public static class AlarmReceiver extends BroadcastReceiver {
        private static final String TAG = AlarmReceiver.class.getSimpleName();
        private AtomicInteger resetFinishedCount; // Count how many type is reset
        private static CountDownLatch mKillSelfLatch = new CountDownLatch(1);

        public static void killSelfCountDown() {
            mKillSelfLatch.countDown();
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();

            if (TextUtils.equals(action, Intent.ACTION_BOOT_COMPLETED)) {
                AppLogger.BASIC.d(TAG, "onReceive: boot completed");

                final int typeCount = sControllerMap.keySet().size();
                resetFinishedCount = new AtomicInteger(0);
                for (ControllerType type : sControllerMap.keySet()) {
                    resetSystemAlarms(type, new ResetAlarmCallback() {
                        @Override
                        public void onResetFinished() {
                            if (resetFinishedCount.addAndGet(1) == typeCount) {
                                try {
                                    mKillSelfLatch.await(10, TimeUnit.SECONDS);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                if (UiHelper.isSellModeVersion(context) && SellModeReceiver.getIsReceiverWorkNotFinish()) {
                                    return ;
                                } else {
                                    killSelfIfNeeded(context);
                                }
                            }
                        }
                    });
                }

                return;
            }

            long alarmTime = IntentParamsUtil.getLongExtra(intent, ALARM_TIME, 0);
            ControllerType type = null;
            try {
                type = ControllerType.valueOf(IntentParamsUtil.getStringExtra(intent, ALARM_TYPE));
                AppLogger.BASIC.d(TAG, "onReceive: time = " + DATE_FORMAT.format(new Date(alarmTime)) + ", type = " + type);
            } catch (IllegalArgumentException e) {
                AppLogger.BASIC.e(TAG, "onReceive: ControllerType.valueOf error ", e);
                return;
            }
            if (ControllerType.TODO_LATER == type){
                String todoId = IntentParamsUtil.getStringExtra(intent, ALARM_TODO);
                if (sControllerMap.get(type) != null && alarmTime > 0) {
                    sControllerMap.get(type).notifyByToDo(alarmTime, todoId);
                }
                return;
            }
            resetSystemAlarmsAndCheck(type);
            if (sControllerMap.get(type) != null && alarmTime > 0) {
                sControllerMap.get(type).notifyByAlarmTime(alarmTime);
            }
            AppLogger.OPS.d(TAG, "alarm: " + type + ", " + alarmTime);
        }

        // To add white list, Notes should kill self after boot completed
        private void killSelfIfNeeded(Context context) {
            boolean shouldKillSelf = true;
            if (context.getApplicationContext() instanceof MyApplication) {
                shouldKillSelf = !((MyApplication) context.getApplicationContext()).isAlive();
            }
            AppLogger.BASIC.d(TAG, "shouldKillSelf : " + shouldKillSelf);
            if (shouldKillSelf) {
                android.os.Process.killProcess(android.os.Process.myPid());
            }
        }
    }
}
