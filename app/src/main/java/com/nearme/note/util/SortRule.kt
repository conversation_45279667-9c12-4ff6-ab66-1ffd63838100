/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SortRule.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2020/9/2
 * * Author: MengHao.He
 * *
 * * ---------------------- Revision History: -------------------
 * *  <author>     <date>    <version>    <desc>
 * * MengHao.He   2020/9/2      1.0    build this module
 ****************************************************************/
package com.nearme.note.util

import android.content.Context
import androidx.annotation.IntDef
import com.oplus.note.utils.SharedPreferencesUtil
import com.nearme.note.MyApplication
import java.lang.annotation.Retention
import java.lang.annotation.RetentionPolicy

object SortRule {
    const val SORT_RULE_BY_UPDATE_TIME = 0
    const val SORT_RULE_BY_CREATE_TIME = 1

    @IntDef(value = [SORT_RULE_BY_UPDATE_TIME, SORT_RULE_BY_CREATE_TIME])
    @Retention(RetentionPolicy.SOURCE)
    annotation class SortRuleOption

    @SortRuleOption
    fun readSortRule(context: Context? = null): Int {
        val currentContext = context ?: MyApplication.appContext
        return SharedPreferencesUtil.getInstance().getInt(
            currentContext,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.SORT_RULE_KEY, SORT_RULE_BY_UPDATE_TIME
        )
    }

    fun saveSortRule(@SortRuleOption sortRule: Int?) {
        if (sortRule == null) {
            return
        }
        SharedPreferencesUtil.getInstance().putInt(MyApplication.appContext,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.SORT_RULE_KEY,
                sortRule)
    }

    fun resetSortRule() {
        saveSortRule(SORT_RULE_BY_UPDATE_TIME)
    }

    fun isGroupByPeople(): Boolean {
        return SharedPreferencesUtil.getInstance().getBoolean(
            MyApplication.appContext,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.GROUP_BY_PEOPLE, true
        )
    }

    fun saveGroupByPeople(isGroupByPeople: Boolean) {
        SharedPreferencesUtil.getInstance().putBoolean(
            MyApplication.appContext,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.GROUP_BY_PEOPLE,
            isGroupByPeople
        )
    }
}
