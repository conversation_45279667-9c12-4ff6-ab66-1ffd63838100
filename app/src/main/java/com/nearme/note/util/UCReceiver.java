/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.nearme.note.logic.AccountManager;
import com.oplus.cloud.agent.note.NoteSyncAgent;
import com.oplus.cloudkit.CloudKitGlobalStateManager;
import com.oplus.note.logger.AppLogger;
import com.oplus.cloudkit.SyncManager;
import com.oplus.note.common.PkgConstants;
import com.oplus.note.push.PushAgent;
import com.oplus.note.push.PushAgentFactory;

public class UCReceiver extends BroadcastReceiver {

    private static final String TAG = "UCReceiver";

    public static final String EXTRA_KEY_CLEAN_DATA = "com.heytap.usercenter.clean_data";

    @SuppressLint("UnsafeBroadcastReceiverActionDetector")
    @Override
    public void onReceive(Context context, Intent intent) {
        AppLogger.CLOUDKIT.d(TAG, "onReceive: " + intent);

        if (!PrivacyPolicyHelper.isAgreeUserNotice(context)) {
            return;
        }

        // clean data if needed.
        boolean shouldCleanData = intent.getBooleanExtra(EXTRA_KEY_CLEAN_DATA, false);
        NoteSyncAgent nsa = NoteSyncAgent.getInstance();
        if (!nsa.hasInit()) {
            nsa.onCreateAgent();
        }
        DataStatisticsHelper.INSTANCE.commonOps(TAG, "********", "" + shouldCleanData);
        nsa.onAccountLogOut(null, shouldCleanData, true);
        AppLogger.CLOUDKIT.d(TAG, "onReceive: " + shouldCleanData);

        //重置同步状态
        SyncManager.INSTANCE.resetRunningState();

        //清空同步数据
        SyncManager.INSTANCE.clearSyncDataByLogout();

        //注销push
        PushAgent pushAgent = PushAgentFactory.INSTANCE.get();
        if (pushAgent != null) {
            pushAgent.unregister();
        }

        CloudKitGlobalStateManager.resetState();

        // legacy logic
        AccountManager.logout(context);
        // notify AllNoteActivity to refresh empty page.
        Intent logOutIntent = new Intent(PkgConstants.CLOUD_ACTION_MODULE_SWITCH_STATE_CHANGED);
        logOutIntent.setPackage(context.getPackageName());
        context.sendBroadcast(logOutIntent);
    }

}
