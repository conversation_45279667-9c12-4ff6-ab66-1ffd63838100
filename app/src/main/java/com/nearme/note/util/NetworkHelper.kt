/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NetworkHelper.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/11/9
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication.Companion.appContext
import com.oplus.note.logger.AppLogger

object NetworkHelper {
    const val TAG = "NetworkHelper"
    private const val CARRIER = "ro.carrier"
    private const val WIFI_ONLY = "wifi-only"

    @JvmStatic
    @JvmOverloads
    fun isWifiOnline(context: Context? = null): Boolean {
        return runCatching {
            isWifiConnected(context) && isValidated(context)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "isWifiOnline failed")
        }.getOrDefault(false)
    }

    /**
     * Returns whether the device currently has wifi connection. This does not guarantee that
     * the connection is reliable.
     */
    @JvmStatic
    @JvmOverloads
    fun isWifiConnected(context: Context? = null): Boolean {
        return pickActiveNetworkCapabilities(
            detectConnectivityManager(context)
        ) {
            hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } ?: false
    }

    /**
     * Returns whether the device currently has network connection. This does not guarantee that
     * the connection is reliable.
     */
    @JvmStatic
    @JvmOverloads
    fun isConnected(context: Context? = null): Boolean {
        return pickActiveNetworkCapabilities(
            detectConnectivityManager(context)
        ) {
            hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } ?: false
    }

    /**
     * Determines if the network is validated - has a working Internet connection.
     *
     * @return {@code true} if the network is validated.
     */
    @JvmStatic
    @JvmOverloads
    fun isValidated(context: Context? = null): Boolean {
        return pickActiveNetworkCapabilities(
            detectConnectivityManager(context)
        ) {
            hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } ?: false
    }

    @VisibleForTesting
    internal fun detectConnectivityManager(context: Context?): ConnectivityManager {
        return (context ?: appContext).detectSystemService(Context.CONNECTIVITY_SERVICE)
    }

    @JvmStatic
    fun isOnlyWifi(): Boolean {
        return WIFI_ONLY == SystemPropertiesCustomize.get(CARRIER)
    }

    private inline fun <R> pickActiveNetworkCapabilities(
        cm: ConnectivityManager,
        block: NetworkCapabilities.() -> R
    ): R? = runCatching {
        cm.getNetworkCapabilities(cm.activeNetwork)?.let(block)
    }.onFailure {
        AppLogger.BASIC.e(TAG, "pickActiveNetworkCapabilities exception: ${it.message}")
    }.getOrNull()
}