/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: LiveDataUtils.java
 * * Description: LiveDataUtils
 * * Version: 1.0
 * * Date: 2020/1/2
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2020/1/2 1.0 build this module
 ****************************************************************/

package com.nearme.note.util;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.List;

public final class LiveDataUtils {
    private LiveDataUtils() {
    }

    @MainThread
    @NonNull
    public static <X, Y> LiveData<Y> listMap(
            @NonNull List<LiveData<X>> sourceList,
            @NonNull final ListFunction<X, Y> listFunction) {
        final MediatorLiveData<Y> result = new MediatorLiveData<>();

        for (LiveData<X> source : sourceList) {
            result.addSource(source, new Observer<X>() {
                @Override
                public void onChanged(X x) {
                    List<X> valueList = new ArrayList<>();
                    for (LiveData<X> source : sourceList) {
                        valueList.add(source.getValue());
                    }
                    result.setValue(listFunction.apply(valueList));
                }
            });
        }

        return result;
    }

    public interface ListFunction<I, O> {
        /**
         * Applies this function to the given input.
         *
         * @param inputList the input list
         * @return the function result.
         */
        O apply(List<I> inputList);
    }
}
