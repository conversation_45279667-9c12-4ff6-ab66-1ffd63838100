/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9060       2023/10/21      1.0     create file
 ****************************************************************/
package com.nearme.note.util

import android.os.SystemClock

object ClickUtils {

    const val DURATION_300 = 300L
    const val DURATION_500 = 500L
    private var slLastClickTime: Long = 0
    private var sLastClick = 0L

    @JvmStatic
    fun isFastDoubleClick(interval: Long = DURATION_500): Boolean {
        val time = System.currentTimeMillis()
        val timeD: Long = time - slLastClickTime
        if (timeD in 1 until interval) {
            return true
        }
        slLastClickTime = time
        return false
    }

    @JvmStatic
    fun isQuickClick(): Boolean {
        val thisClick = SystemClock.elapsedRealtime()
        return if (thisClick - sLastClick < DURATION_300) {
            true
        } else {
            sLastClick = thisClick
            false
        }
    }
}