/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: Web.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/11/13
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Browser
import androidx.appcompat.content.res.AppCompatResources
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.note.logger.AppLogger
import com.oplus.note.market.SuperLinkManager
import com.oplus.note.os.ResponsiveUiHelper
import com.oplus.note.osdk.proxy.OplusZoomWindowManagerProxy
import com.oplus.note.statistic.StatisticsCallSummary
import com.oplus.note.superlink.GROUP_1
import com.oplus.note.superlink.GROUP_2
import com.oplus.note.superlink.ITEM_POSITION_0
import com.oplus.note.superlink.ITEM_POSITION_1
import com.oplus.note.superlink.ITEM_POSITION_2
import com.oplus.note.superlink.ITEM_POSITION_3
import com.oplus.note.superlink.ITEM_POSITION_4
import com.oplus.note.superlink.SuperLinkMaker
import com.oplus.note.utils.copyToClipboard
import com.oplus.notes.webview.container.api.IWebViewContainer
import com.oplus.richtext.editor.utils.RichStatisticsUtils

class Web(val webViewContainer: IWebViewContainer?, val clickFrom: Int) : SuperLinkMaker {

    companion object {
        private const val TAG = "SuperLinkMaker.Web"
    }

    override fun create(context: Context, data: String, fromType: Int): COUIPopupListWindow {
        AppLogger.BASIC.d(TAG, "create PopWindow")

        val link: String = data
        val fixLink: String = SuperLinkManager.fixUrl(link)

        val itemsRes = arrayOf(
            context.resources.getString(com.oplus.note.baseres.R.string.web_dialog_items_open_with_overlay_window),
            context.resources.getString(com.oplus.note.baseres.R.string.web_dialog_items_open_url),
            context.resources.getString(com.oplus.note.baseres.R.string.toolbar_add_bookmark),
            context.resources.getString(com.oplus.note.baseres.R.string.web_dialog_items_copy_url),
            context.resources.getString(com.oplus.note.baseres.R.string.show_link_preview),
        )

        val isSupportZoomMode = hasSupportZoomModeHandle(context, fixLink)
        val isSupportOpenLink = isSupportOpenLink(context, fixLink)
        val isUnfold: Boolean = ResponsiveUiHelper.isUnfoldState(context)
        val items = mutableListOf<PopupListItem>().apply {
            if (clickFrom != SuperLinkPopWindowFactory.CLICK_FROM_TABLE) {
                // 转为卡片
                val showLinkBuilder = PopupListItem.Builder()
                    .setIcon(AppCompatResources.getDrawable(context, com.oplus.note.baseres.R.drawable.note_ic_show_link_preview))
                    .setTitle(itemsRes[ITEM_POSITION_4])
                    .setGroupId(GROUP_1)
                add(showLinkBuilder.build())
            }

            if (isSupportZoomMode && !isUnfold) {    // 支持闪达  且 非孔雀屏展开状态
                val openBuilder = PopupListItem.Builder()
                    .setIcon(AppCompatResources.getDrawable(context, com.oplus.note.baseres.R.drawable.note_ic_open))
                    .setTitle(itemsRes[ITEM_POSITION_0])
                    .setGroupId(GROUP_2)
                add(openBuilder.build())
            }

            if (isSupportOpenLink) {
                val linkBuilder = PopupListItem.Builder()
                    .setIcon(AppCompatResources.getDrawable(context, com.oplus.note.baseres.R.drawable.note_ic_link))
                    .setTitle(itemsRes[ITEM_POSITION_1])
                    .setGroupId(GROUP_2)
                add(linkBuilder.build())
            }

            if (isSupportSaveBookmark(context)) {
                val markBuilder = PopupListItem.Builder()
                    .setIcon(AppCompatResources.getDrawable(context, com.oplus.note.baseres.R.drawable.note_ic_mark))
                    .setTitle(itemsRes[ITEM_POSITION_2])
                    .setGroupId(GROUP_2)
                add(markBuilder.build())
            }

            val copyBuilder = PopupListItem.Builder()
                .setIcon(AppCompatResources.getDrawable(context, com.oplus.note.baseres.R.drawable.note_ic_copy_normal))
                .setTitle(itemsRes[ITEM_POSITION_3])
                .setGroupId(GROUP_2)
            add(copyBuilder.build())
        }

        val popWindow = COUIPopupListWindow(context)
        popWindow.setDismissTouchOutside(true)
        popWindow.itemList = items
        popWindow.setOnItemClickListener { _, _, position, _ ->
            when (itemsRes.indexOf(items[position].title)) {
                ITEM_POSITION_0 -> {
                    SuperLinkManager.openWebAddress(context, link, true)
                    StatisticsCallSummary.setEventWebLinkClick(StatisticsCallSummary.WEBLinkClickType.OPEN_WITH_OVERLAY_WINDOW, context)
                }

                ITEM_POSITION_1 -> {
                    SuperLinkManager.openWebAddress(context, link, isUnfold)
                    StatisticsCallSummary.setEventWebLinkClick(StatisticsCallSummary.WEBLinkClickType.OPEN_URL, context)
                }

                ITEM_POSITION_2 -> {
                    saveWebAddress(context, link)
                    StatisticsCallSummary.setEventWebLinkClick(StatisticsCallSummary.WEBLinkClickType.SAVE_URL, context)
                }

                ITEM_POSITION_3 -> {
                    copyToClipboard(context, link)
                    StatisticsCallSummary.setEventWebLinkClick(StatisticsCallSummary.WEBLinkClickType.COPY_URL, context)
                }

                ITEM_POSITION_4 -> {
                    webViewContainer?.convertToCard(link)
                    RichStatisticsUtils.setEventShowLinkPreview(context)
                    StatisticsCallSummary.setEventWebLinkClick(StatisticsCallSummary.WEBLinkClickType.CONVERT_TO_CARD, context)
                }

                else -> AppLogger.BASIC.e(TAG, "unsupported index selected.")
            }
            popWindow.dismiss()
        }
        return popWindow
    }

    /**
     * 检查是否有fixLink的应用，且支持浮窗模式。
     */
    @SuppressLint("UnsafeImplicitIntentLaunch")
    private fun hasSupportZoomModeHandle(context: Context, fixLink: String): Boolean {
        var isSupportZoomMode = false
        kotlin.runCatching {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(fixLink))
            val info = context.packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
            if (info?.activityInfo != null) {
                val packageName = info.activityInfo.packageName
                val activityName = info.activityInfo.name
                AppLogger.BASIC.d(TAG, "createWebDialog ActivityInfo toString = $packageName, activityName = $activityName")
                //如果链接打开的activity是com.ss.android.article.news/com.bytedance.news.foundation.router.AppLinkActivity，就屏蔽【浮窗打开】入口
                isSupportZoomMode = if (packageName == "com.ss.android.article.news"
                    && activityName == "com.bytedance.news.foundation.router.AppLinkActivity"
                ) {
                    false
                } else {
                    OplusZoomWindowManagerProxy.isSupportZoomMode(context, packageName)
                }
            }
            AppLogger.BASIC.i(TAG, "createWebDialog  isSupportZoomMode = $isSupportZoomMode")
        }.onFailure {
            AppLogger.BASIC.e(TAG, "failed to find handle of link", it)
        }
        return isSupportZoomMode
    }

    /**
     * 保存网址为书签
     */
    @SuppressLint("ImplicitIntentDetector")
    private fun saveWebAddress(context: Context, url: String): Boolean {
        return kotlin.runCatching {
            val intent = Intent(Intent.ACTION_INSERT)
            intent.type = "vnd.android.cursor.dir/bookmark"
            intent.putExtra("url", url)
            context.startActivity(intent)
            return@runCatching true
        }.onFailure {
            AppLogger.BASIC.e(TAG, "fail to save web address!", it)
            Browser.sendString(context, url)
        }.getOrDefault(false)
    }

    /**
     *  Is this device support open a link
     */
    private fun isSupportOpenLink(context: Context?, link: String): Boolean {
        return kotlin.runCatching {
            val support = if (context != null) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(link))
                intent.resolveActivity(context.packageManager) != null
            } else {
                false
            }
            AppLogger.BASIC.d(TAG, "isSupportOpenLink: $support")
            support
        }.getOrDefault(false)
    }

    /**
     * Is this device support save a bookmark
     */
    private fun isSupportSaveBookmark(context: Context?): Boolean {
        return kotlin.runCatching {
            val support = if (context != null) {
                val intent = Intent(Intent.ACTION_INSERT)
                intent.type = "vnd.android.cursor.dir/bookmark"
                intent.resolveActivity(context.packageManager) != null
            } else {
                false
            }
            AppLogger.BASIC.d(TAG, "isSupportSaveBookmark: $support")
            support
        }.getOrDefault(false)
    }
}