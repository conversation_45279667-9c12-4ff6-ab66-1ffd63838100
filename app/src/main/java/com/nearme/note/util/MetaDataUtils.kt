/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - MetaDataUtils.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/5/7
 * * Author: <EMAIL>
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  <EMAIL>      2024/5/7         1.0
 **************************************************************************/
package com.nearme.note.util

import android.content.Context
import android.content.pm.PackageManager
import com.oplus.note.logger.AppLogger

object MetaDataUtils {

    @JvmStatic
    fun getMetaDataBoolean(context: Context, packageName: String, name: String): Boolean {
        return runCatching {
            context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA).metaData?.let {
                return it.getBoolean(name)
            }
        }.onFailure {
            AppLogger.BASIC.e("MetaDataUtils", "getMetaDataBoolean error ${it.message}")
        }.getOrNull() ?: false
    }

    @JvmStatic
    fun getMetaDataString(context: Context, packageName: String, name: String): String {
        return runCatching {
            context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA).metaData?.let {
                return it.getString(name) ?: ""
            }
        }.onFailure {
            AppLogger.BASIC.e("MetaDataUtils", "getMetaDataString error ${it.message}")
        }.getOrNull() ?: ""
    }

    @JvmStatic
    fun getMetaDataInt(context: Context, packageName: String, name: String): Int {
        return runCatching {
            val metaData = context.packageManager.getApplicationInfo(
                packageName, PackageManager.GET_META_DATA
            ).metaData
            return metaData.getInt(name, -1)
        }.onFailure {
            AppLogger.BASIC.e("MetaDataUtils", "getMetaDataInt error ${it.message}")
        }.getOrNull() ?: -1
    }
}