package com.nearme.note.util

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.note.R
import com.nearme.note.util.MultiClickFilter.isEffectiveClick

object MbaUtils {

    var serviceDisabledDialog: AlertDialog? = null
    const val PACKAGER_CLOUD = "com.heytap.cloud"
    const val PACKAGER_SCANNER = "com.coloros.ocrscanner"
    const val PKG_SUPER_TEXT = "com.oplus.supertextinput"
    private const val TAG = "MbaUtils"
    private const val PACKAGE = "package"

    private fun getAppName(packageName: String, context: Context?): String {
        val pm = context?.packageManager
        var name = ""
        try {
            name = pm?.getApplicationLabel(pm.getApplicationInfo(packageName, PackageManager.GET_META_DATA)).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return name
    }

    fun showMbaDialog(packageName: String, context: Context) {
        if (!isEffectiveClick()) {
            return
        }
        val title = context.getString(R.string.dialog_app_forbidden_title, getAppName(packageName, context))
        val message = context.getString(R.string.dialog_app_forbidden_detail, getAppName(packageName, context), context.getString(R.string.app_name))
        serviceDisabledDialog = COUIAlertDialogBuilder(context)
            .setTitle(title)
            .setCancelable(true)
            .setMessage(message)
            .setPositiveButton(R.string.start_enabled) { _, _ ->
                gotoAppDetailsPage(context, PACKAGER_CLOUD)
            }.setNegativeButton(R.string.cancel) { _, _ ->
                serviceDisabledDialog?.dismiss()
                serviceDisabledDialog = null
            }
            .show()
    }

    fun showMbaCloudDialog(context: Context) {
        if (!isEffectiveClick()) {
            return
        }
        serviceDisabledDialog = COUIAlertDialogBuilder(context)
            .setTitle(R.string.mba_title_cloud_oplus)
            .setCancelable(true)
            .setMessage(R.string.mba_tips_cloud_oplus)
            .setPositiveButton(R.string.start_enabled) { _, _ ->
                gotoAppDetailsPage(context, PACKAGER_CLOUD)
            }.setNegativeButton(R.string.cancel) { _, _ ->
                serviceDisabledDialog?.dismiss()
                serviceDisabledDialog = null
            }
            .show()
    }

    fun gotoAppDetailsPage(context: Context, packageName: String) {
        val intent = Intent()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        intent.data = Uri.fromParts(PACKAGE, packageName, null)
        context.startActivity(intent)
    }
}