/**
 * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * File           : KitSyncSdkUtils.kt
 * Description    : KitSyncSdkUtils.kt
 * Version        : 1.0
 * Date           : 2024/3/7
 * Author         : Chandler
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * Chandler     2024/3/7         1.0           create
 */
package com.nearme.note.util

import android.content.Context
import com.heytap.msp.syncload.KitSyncSdk
import com.heytap.msp.syncload.base.KitInfo
import com.heytap.msp.syncload.base.KitRequestInfo
import com.oplus.note.logger.AppLogger
import java.io.File

object KitSyncSdkUtils {
    private const val TAG = "KitSyncSdkUtils"
    private const val NAME_TBL_WEBVIEW = "com.heytap.tbl.webview"

    /**
     * 取指定的Kit文件信息
     */
    @JvmStatic
    fun fetchKitInfo(context: Context, cb: (code: Int, kitInfo: KitInfo?) -> Unit) {
        AppExecutors.getInstance().executeCommandInDiskIO {
            kotlin.runCatching {
                val requestInfo = KitRequestInfo().apply {
                    kitName = NAME_TBL_WEBVIEW
                }
                KitSyncSdk.fetchKitInfo(context, requestInfo) { code: Int, kitInfo: KitInfo ->
                    AppLogger.BASIC.d(TAG, "fetchKitInfo: code=$code, kitInfo=$kitInfo")
                    cb.invoke(code, kitInfo)
                }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "fetchKitInfo: ${it.message}")
                //部分设备会报错，不回调会导致TBL插件不下载
                cb.invoke(-1, null)
            }
        }
    }

    /**
     * 下载指定的Kit文件
     * @param context
     * @param kitInfo, kit文件信息(通过fetchKitInfo获得)
     * @param file 需要下载的目标文件对象定义（文件级别）
     * @param cb 结果回调
     */
    @JvmStatic
    fun downloadFile(context: Context, kitInfo: KitInfo, file: File, cb: (code: Int) -> Unit) {
        AppExecutors.getInstance().executeCommandInDiskIO {
            kotlin.runCatching {
                AppLogger.BASIC.d(TAG, "downloadFile: kitInfo=$kitInfo")
                KitSyncSdk.downloadFile(
                    context,
                    kitInfo,
                    file
                ) { code: Int, _: File?, _: KitInfo? ->
                    AppLogger.BASIC.d(TAG, "downloadFile: code=$code")
                    cb.invoke(code)
                }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "fetchKitInfo: ${it.message}")
            }
        }
    }
}