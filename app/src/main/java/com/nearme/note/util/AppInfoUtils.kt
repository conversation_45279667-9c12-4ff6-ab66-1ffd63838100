/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - AppInfoUtils.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2022/3/21
 * * Author: <EMAIL>
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  <EMAIL>      2022/3/21         1.0
 **************************************************************************/

package com.nearme.note.util

import android.content.Context
import android.net.Uri
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication.Companion.appContext
import com.oplus.note.logger.AppLogger

object AppInfoUtils {
    private const val TAG = "AppInfoUtils"
    private const val AUTHORITY = "com.color.provider.removableapp"
    @VisibleForTesting
    internal const val TABLE_NAME_REMOVABLEAPP = "removableapp"
    @VisibleForTesting
    internal const val COL_PACKAGE_NAME = "package_name"

    @JvmStatic
    fun queryAppIsUninstall(packageName: String): Boolean {
        AppLogger.BASIC.d(TAG, "queryAppIsUninstall")
        val removableAppUri = Uri.parse("content://$AUTHORITY").let {
            Uri.withAppendedPath(it, TABLE_NAME_REMOVABLEAPP)
        }
        appContext.contentResolver.query(removableAppUri,
            null, null, null)?.use {
            while (it.moveToNext()) {
                val name = it.getString(it.getColumnIndexOrThrow(COL_PACKAGE_NAME))
                AppLogger.BASIC.d(TAG, "packageName: $name")
                if (packageName == name) {
                    return true
                }
            }
        }
        return false
    }


    /**
     * 通过包名判断应用是否安装
     * @param context Context
     * @param packageName 应用包名
     */
    fun checkAppInstalled(context: Context, packageName: String?): Boolean {
        packageName?.let {
            return runCatching {
                context.packageManager.getPackageInfo(packageName, 0) != null
            }.getOrDefault(false)
        } ?: return false
    }
}