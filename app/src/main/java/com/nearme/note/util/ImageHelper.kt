/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ImageHelper.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/6/15
 * * Author: niechuanxing
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.util

import android.app.Activity
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.VisibleForTesting
import androidx.core.view.doOnNextLayout
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import kotlin.math.roundToInt

class ImageHelper(activity: Activity) {
    companion object {
        const val LOTTIE_ALPHA = 1f

        private const val DENSITY_3 = 3.0f
        private const val MUL_SMALL = 3 / 5f
        private const val HEIGHT_PROPORTION = 0.45
        private const val TAG = "ImageHelper"
    }

    var calculateFinishCallBack: ((from: String) -> Unit)? = null
    // Judge whether empty image should be resized in small-size-style or not
    private var mWidthBound = 0
    private var mHeightBound = 0
    private var mHeightBound200 = 0
    private var mImageWidth = 0
    private var mImageHeight = 0
    private var imageHasInit = false
    private var rootHasInit = false
    private var mBottomOffSize = 0
    private var mTopOffSize = 0

    private val mActivity: Activity = activity

    init {
        mWidthBound = activity.resources?.getDimensionPixelOffset(R.dimen.width_360_density_3) ?: 0
        mHeightBound = activity.resources?.getDimensionPixelOffset(R.dimen.height_420_density_3) ?: 0
        mHeightBound200 =  activity.resources?.getDimensionPixelOffset(R.dimen.height_200_density_3) ?: 0
    }

    fun init(root: View, content: View, image: View, topOffSize: Int = 0, bottomOffSize: Int = 0, from: String = "") {
        AppLogger.BASIC.d(TAG, "init from=$from")
        mTopOffSize = topOffSize
        mBottomOffSize = bottomOffSize
        root.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
            override fun onLayoutChange(
                v: View?,
                left: Int,
                top: Int,
                right: Int,
                bottom: Int,
                oldLeft: Int,
                oldTop: Int,
                oldRight: Int,
                oldBottom: Int
            ) {
                if (isLayoutChange(left, oldLeft, right, oldRight)
                    || isLayoutChange(top, oldTop, bottom, oldBottom)
                ) {
                    rootHasInit = true
                    root.removeOnLayoutChangeListener(this)
                    root.post {
                        calculate(root, content, image)
                    }
                }
            }
        })
        image.doOnNextLayout {
            imageHasInit = true
            image.post {
                AppLogger.BASIC.d(TAG, "img doOnNextLayout from=$from,w=${image.width},h=${image.height},mw=$mImageWidth,mh=$mImageHeight")
                mImageWidth = image.width
                mImageHeight = image.height
                if (mImageWidth <= 0) {
                    mImageWidth = root.context.resources.getDimensionPixelSize(R.dimen.empty_animate_size_width)
                }
                if (mImageHeight <= 0) {
                    mImageHeight = root.context.resources.getDimensionPixelSize(R.dimen.empty_animate_size_height)
                }
                calculate(root, content, image, callBack = {
                    calculateFinishCallBack?.invoke(from)
                })
            }
        }
    }

    @Suppress("ComplexMethod")
    fun calculate(root: View, content: View, image: View, smallSize: Boolean? = null, callBack: (() -> Unit)? = null) {
        if ((smallSize == null) && (!imageHasInit || !rootHasInit)) {
            return
        }
        val isSmallSize = smallSize ?: isSmallSize(root)
        val isSmallSize200 = isSmallSize200(root)
        AppLogger.BASIC.d(TAG, "calculate isSmallSize=$isSmallSize isSmallSize200=$isSmallSize200")
        if (!isSmallSize && root.paddingTop != 0) {
            root.setPadding(root.paddingStart, 0, root.paddingEnd, root.paddingBottom)
        }
        var imageHeight = image.height
        image.apply {
            layoutParams?.apply {
                val widthTemp = if (isSmallSize) {
                    (mImageWidth * MUL_SMALL).roundToInt()
                } else {
                    mImageWidth
                }
                width = widthTemp
                val heightTemp = when {
                    isSmallSize200 -> {
                        0
                    }
                    isSmallSize -> {
                        (mImageHeight * MUL_SMALL).roundToInt()
                    }
                    else -> {
                        mImageHeight
                    }
                }
                imageHeight = heightTemp
                height = heightTemp
            }.also {
                layoutParams = it
            }
        }
        image.post {
            val contentHeight = content.height
            val delta = image.height - imageHeight
            var top =
                (root.height - root.paddingTop - root.paddingBottom - mTopOffSize - mBottomOffSize) * HEIGHT_PROPORTION - (contentHeight - delta) / 2
            AppLogger.BASIC.d(TAG, "calculate top=$top , contentHeight =$contentHeight")
            top = (if (top < 0) 0.0 else top) + mTopOffSize
            AppLogger.BASIC.d(TAG, "calculate top=$top")

            content.apply {
                (layoutParams as? FrameLayout.LayoutParams)?.apply {
                    topMargin = top.roundToInt()
                }?.also {
                    layoutParams = it
                }
            }
            callBack?.invoke()
        }
    }

    private fun isLayoutChange(data: Int, oldData: Int): Boolean {
        return data != oldData
    }

    @VisibleForTesting
    fun isLayoutChange(left: Int, oldLeft: Int, right: Int, oldRight: Int): Boolean {
        return (left != oldLeft || right != oldRight) && right - left != 0
    }

    private fun isSmallSize(root: View): Boolean {
        AppLogger.BASIC.d(TAG, "calculate isSmallSize root.width=${root.width}, height=${root.height}")
        return root.width < convertToPixelWithDensityEquals3(mWidthBound)
                || root.height < convertToPixelWithDensityEquals3(mHeightBound)
    }
    @VisibleForTesting
    fun isSmallSize200(root: View): Boolean {
        return  root.height < convertToPixelWithDensityEquals3(mHeightBound200)
    }

    @VisibleForTesting
    fun convertToPixelWithDensityEquals3(value: Int): Int {
        return (value / mActivity.resources.displayMetrics.density * DENSITY_3).toInt()
    }
}