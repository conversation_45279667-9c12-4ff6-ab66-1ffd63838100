/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.data;
import androidx.annotation.VisibleForTesting;
import com.oplus.note.logger.AppLogger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.EOFException;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedList;

public class HandWritingData implements Serializable {

    private static final String TAG = "HandWritingData";

    public static final int START_VERSION_CODE = 150;

    private static final long serialVersionUID = 7209380212324026056L;
    private int mBgWidth;
    private int mBgHeight;
    private LinkedList<HandWritingWordData> mWordsData = new LinkedList<HandWritingWordData>();

    public int getStorageSize() {
        int total = HandWritingWordData.INT_LENGTH * 3; // 绘制区域、宽、高、字数
        int words = mWordsData.size();
        for (int i = 0; i < words; ++i) {
            total += mWordsData.get(i).getStorageSize();
        }
        return total;
    }

    /**
     * 保存笔记数据到文件 手写数据格式： 绘制区域宽度+绘制区域高度+总字数+每个字
     * <p>
     * 字：类型 + 数据 手写类型：字宽 + 颜色 + 笔划数据 笔划数据：笔划数量 + 笔划1点数 + 笔划1点数据 + 笔划2点数 + 笔划2点数据 + ……
     * <p>
     * 普通文字类型：字符
     *
     * @param filepath
     */
    public void saveNoteData(String filepath) {
        DataOutputStream dos = null;
        try {
            FileOutputStream fos = new FileOutputStream(filepath);
            BufferedOutputStream bos = new BufferedOutputStream(fos);
            dos = new DataOutputStream(bos);
            /*
             * 存入版本号（存负数是为了区别1.4及以前版本数据）. 1.4以前的数据存的第一个数据是bgWidth（大于0）
             */
            // dos.writeInt(-versionCode);
            dos.writeInt(mBgWidth); // 存入绘制区域宽度
            dos.writeInt(mBgHeight); // 存入绘制区域高度
            int words = getWordsData().size(); // 存入字数
            dos.writeInt(words);
            int speeds = 0;
            for (int i = 0; i < words; ++i) {
                if (getWordsData().get(i).getSpeedList().size() > 0) {
                    speeds += 1;
                }
                getWordsData().get(i).toOutputStream(dos); // 存入字
            }
            dos.writeInt(speeds);
            for (int i = 0; i < words; ++i) {
                int index = getWordsData().get(i).getSpeedList().size();
                if (index > 0) {
                    getWordsData().get(i).speedToOutputStream(i, dos); // 存入速度
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            AppLogger.NOTE.e(TAG, "[HandWritingData] saveNoteData FileNotFoundException");
        } catch (IOException e) {
            e.printStackTrace();
            AppLogger.NOTE.e(TAG, "[HandWritingData] saveNoteData IOException");
        } finally {
            if (null != dos) {
                try {
                    dos.close();
                } catch (IOException e) {
                    //do noting
                }
            }

        }
    }

    public boolean readNoteData(String filepath) {
        DataInputStream dis = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filepath);
            BufferedInputStream bis = new BufferedInputStream(fis);
            dis = new DataInputStream(bis);
            mBgWidth = dis.readInt();
            mBgHeight = dis.readInt();
            clear();
            int words = dis.readInt();
            for (int i = 0; i < words; ++i) {
                getWordsData().add(HandWritingWordData.getDataFromInputStream(dis));
            }
            int speeds = 0;
            try {
                speeds = dis.readInt();
            } catch (EOFException e) {
                return true;
            }
            for (int i = 0; i < speeds; ++i) {
                int index = dis.readInt();
                HandWritingWordData tmp = getWordsData().get(index);
                tmp.getSpeedDataFromInputStream(dis, tmp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppLogger.NOTE.e(TAG, "[HandWritingData] readNoteData Exception filepath=" + filepath);
            return false;
        } finally {
            if (null != dis) {
                try {
                    dis.close();
                } catch (IOException e) {
                    //do noting
                }
            }
        }
        return true;
    }

    public boolean getDataFromFingerNoteData(FingerNoteData fingerNoteData) {
        boolean result = true;
        try {
            clear();
            mBgWidth = fingerNoteData.mBgWidth;
            mBgHeight = fingerNoteData.mBgHeight;
            int pathSum = fingerNoteData.mDataList.size();
            for (int i = 0; i < pathSum; ) { // 第三条语句为空
                HandWritingWordData mHandWritingWordData = new HandWritingWordData();
                FingerPathData fpd = fingerNoteData.mDataList.get(i);
                if (null == fpd.mFingerPath) {
                    addCommonText(fpd.getText());
                    ++i;
                    continue;
                }
                int nowId = fpd.mId;
                mHandWritingWordData.setTextWidth(fingerNoteData.getTextWidth(nowId));
                mHandWritingWordData.setPenColor(fpd.mPenColor);
                mHandWritingWordData.setType(HandWritingWordData.HANDWRITING_TYPE);
                ArrayList<float[]> pathList = mHandWritingWordData.getPathList();
                for (; i < pathSum; ++i) {
                    if (nowId == fingerNoteData.mDataList.get(i).mId) {
                        pathList.add(fingerNoteData.mDataList.get(i).mFingerPath);
                    } else {
                        break;
                    }
                }
                getWordsData().add(mHandWritingWordData);
                String pathText = fingerNoteData.mDataList.get(i - 1).getText();
                addCommonText(pathText);
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppLogger.NOTE.e(TAG, "[HandWritingData] getDataFromFingerNoteData Exception ");
            result = false;
        }

        return result;

    }

    @VisibleForTesting
    void addCommonText(String pathText) {
        if (null != pathText) {
            int textLength = pathText.length();
            for (int j = 0; j < textLength; ++j) {
                HandWritingWordData hwd = new HandWritingWordData();
                hwd.setType(HandWritingWordData.COMMON_TYPE);
                hwd.setText(pathText.charAt(j));
                getWordsData().add(hwd);
            }
        }
    }


    public int getBgWidth() {
        return mBgWidth;
    }

    public void setBgWidth(int bgWidth) {
        this.mBgWidth = bgWidth;
    }

    public int getBgHeight() {
        return mBgHeight;
    }

    public void setBgHeight(int bgHeight) {
        this.mBgHeight = bgHeight;
    }

    @VisibleForTesting
    void clear() {
        if (null != getWordsData()) {
            getWordsData().clear();
        }
    }


    public LinkedList<HandWritingWordData> getWordsData() {
        return mWordsData;
    }
}
