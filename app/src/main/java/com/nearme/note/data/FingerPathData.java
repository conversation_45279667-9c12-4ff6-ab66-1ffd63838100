/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.nearme.note.data;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;

import com.nearme.note.util.FileUtil;

/*
 * 手写或涂鸦笔记数据类 1.5版本开始FingerPathData 不再用作手写数据
 */
class FingerPathData implements Serializable {
    static final int BASE_BYTES_LEN = 8;
    int mId;
    int mPenColor;
    float[] mFingerPath;

    private static final long serialVersionUID = 8158037062125803807L;
    private String mText;// 在该手写文本后的文字

    public int getStorageSize() {
        final int FOUR = 4;
        int total = 8;
        if (mFingerPath != null) {
            total += mFingerPath.length * FOUR;
        }
        if (mText != null) {
            try {
                total += mText.getBytes("utf-8").length;
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return total;
    }

    public int toBytes(byte[] buf, int pos) {
        final int OFFSET = 4;
        FileUtil.intToBytes(mId, buf, pos);
        FileUtil.intToBytes(mPenColor, buf, pos += OFFSET);
        int len = mFingerPath.length;
        for (int j = 0; j < len; j++) {
            FileUtil.intToBytes(Float.floatToIntBits(mFingerPath[j]), buf, pos += OFFSET);
        }
        return pos;
    }

    public int setWithBytes(byte[] buf, int pos, int pathLen) {
        final int OFFSET = 4;
        mId = FileUtil.bytesToInt(buf, pos);
        mPenColor = FileUtil.bytesToInt(buf, pos += OFFSET);
        float[] datas = new float[pathLen];
        for (int j = 0; j < pathLen; j++) {
            datas[j] = Float.intBitsToFloat(FileUtil.bytesToInt(buf, pos += OFFSET));
        }
        mFingerPath = datas;
        return pos;
    }

    public int bytesLength() {
        final int FOUR = 4;
        if (mFingerPath != null) {
            return BASE_BYTES_LEN + mFingerPath.length * FOUR;
        } else {
            return BASE_BYTES_LEN;
        }
    }

    public int getPathLen() {
        if (mFingerPath != null) {
            return mFingerPath.length;
        }
        return 0;
    }

    public void setText(String text) {
        if (isAllEnter(text)) { // 现版本只能加入enter
            this.mText = text;
        } else {
            this.mText = "";
        }
    }

    public String getText() {
        return mText;
    }

    private boolean isAllEnter(String text) {
        for (int i = 0; i < text.length(); ++i) {
            if (text.charAt(i) != '\n') {
                return false;
            }
        }
        return true;
    }
}