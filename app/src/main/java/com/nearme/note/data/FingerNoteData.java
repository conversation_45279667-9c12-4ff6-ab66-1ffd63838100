/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.data;

import android.annotation.SuppressLint;

import com.oplus.note.logger.AppLogger;
import com.nearme.note.util.FileUtil;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;

public class FingerNoteData implements Serializable {
    private static final String TAG = "FingerNoteData";

    int mBgWidth;
    int mBgHeight;
    ArrayList<FingerPathData> mDataList = new ArrayList<FingerPathData>();

    private static final long serialVersionUID = 6655471358309518589L;

    private int mType = NoteAttribute.TYPE_HANDWRITING;
    @SuppressLint("UseSparseArrays")
    private HashMap<Integer, Integer> mTextWidths = null;

    @SuppressLint("UseSparseArrays")
    public FingerNoteData(int type) {
        this.mType = type;
        if (type == NoteAttribute.TYPE_HANDWRITING) {
            mTextWidths = new HashMap<Integer, Integer>();
        }
    }

    public int getStorageSize() {
        final int FOUR = 4;
        int total = 4 * 3;
        if (mDataList != null) {
            int size = mDataList.size();
            for (int i = 0; i < size; i++) {
                total += mDataList.get(i).getStorageSize();
            }
        }
        if (mTextWidths != null) {
            total += mTextWidths.size() * FOUR;
        }
        return total;
    }

    int getTextWidth(int id) {
        if (mTextWidths != null) {
            Integer retid = mTextWidths.get(id);
            if (retid == null) {
                return 0;
            } else {
                return retid.intValue();
            }
        }
        return 0;
    }

    private void add(FingerPathData data) {
        mDataList.add(data);
    }


    public FingerPathData getLast() {
        if (mDataList.size() > 0) {
            return mDataList.get(mDataList.size() - 1);
        } else {
            return null;
        }
    }

    public int getSize() {
        return mDataList.size();
    }

    public void setType(int t) {
        this.mType = t;
    }

    private FingerPathData newFingerPathData() {
        if (NoteAttribute.TYPE_TUYA == mType) {
            return new FingerPathDataTuya();
        } else {
            return new FingerPathData();
        }
    }

    private void clear() {
        mDataList.clear();
        if (mTextWidths != null) {
            mTextWidths.clear();
        }
    }

    /*
     * 从文件读取笔记数据到（手写涂鸦类) 格式： mBgWidth+mBgHeight+总个数+每条数据 每条数据：笔色+笔类型+path数据个数+path数据
     */
    public boolean readNoteData(String filepath) {
        AppLogger.NOTE.d(TAG, "[readNoteData] " + filepath);
        FingerNoteData noteData = this;
        final int OFFSET = 4;
        int pos = -4;
        try {
            byte[] buf = FileUtil.readAsBytes(filepath);
            if ((null != buf) && (buf.length > 0)) {
                noteData.mBgWidth = FileUtil.bytesToInt(buf, pos += OFFSET);
                noteData.mBgHeight = FileUtil.bytesToInt(buf, pos += OFFSET);
                int count = FileUtil.bytesToInt(buf, pos += OFFSET);
                for (int i = 0; i < count; i++) {
                    int pathLen = FileUtil.bytesToInt(buf, pos += OFFSET);
                    FingerPathData data = null;
                    if (pathLen > 0) {
                        data = noteData.newFingerPathData();
                        pos = data.setWithBytes(buf, pos += OFFSET, pathLen);
                        noteData.add(data);
                    }
                    int textLen = FileUtil.bytesToInt(buf, pos += OFFSET);
                    if (textLen > 0) {
                        pos += OFFSET;
                        String text = new String(buf, pos, textLen, StandardCharsets.UTF_8);
                        pos = pos + textLen - OFFSET;
                        if (data == null) {
                            data = noteData.newFingerPathData();
                            noteData.add(data);
                        }
                        if (data != null) {
                            data.setText(text);
                        }
                    }
                }
                pos += OFFSET;
                if (mTextWidths != null && pos < buf.length) {
                    int tsize = FileUtil.bytesToInt(buf, pos);
                    pos += OFFSET;
                    for (int i = 0; i < tsize; i++) {
                        int id = FileUtil.bytesToInt(buf, pos);
                        pos += OFFSET;
                        int width = FileUtil.bytesToInt(buf, pos);
                        pos += OFFSET;
                        mTextWidths.put(id, width);
                    }
                    AppLogger.NOTE.d(TAG, "[readNoteData] mTextWidths:" + tsize);
                }
            }
        } catch (OutOfMemoryError e) {
            AppLogger.NOTE.e(TAG, "[readNoteData] OutOfMemoryError");
            noteData.clear();
            return false;
        }
        return true;
    }

}