/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.nearme.note.data;

import android.annotation.SuppressLint;
import android.os.BadParcelableException;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.IntDef;

import androidx.annotation.IntDef;

@SuppressLint("ParcelCreator")
public class NoteAttribute implements Parcelable {

    public static final String OP_TYPE = "op_type";
    public static final int TYPE_ATTACH_INFO = -1;
    public static final int TYPE_HANDWRITING = 0;
    public static final int TYPE_TUYA = 1;
    public static final int TYPE_TEXT_CONTENT = 2;
    public static final int TYPE_ALBUM = 3;
    public static final int TYPE_PHOTOGRAPH = 4;
    public static final int TYPE_PHONE_EDIT_HINT = 5;
    public static final int TYPE_TITLE = 6;

    public static final byte OP_NONE = 0;
    public static final byte OP_ADD = 1;
    public static final byte OP_MODIFY = 2;
    public static final byte OP_DELETE = 3;
    public static final byte UNCHECKED = 5;


    public static final int STATE_DELETED = 2;

    @SuppressWarnings("unused")
    private static final String TAG = "NoteAttribute";

    /**
     * If the type is image, the value is GUID, if the type is text, the value is content.
     */
    protected String mContent;

    /**
     * Not use.
     */
    private int mVersion;
    /**
     * @see TYPE_HANDWRITING
     * @see TYPE_TUYA
     * @see TYPE_TEXT_CONTENT
     * @see TYPE_ALBUM
     * @see TYPE_PHOTOGRAPH
     * @see TYPE_TITLE
     */
    private int mType;
    /**
     * album file path.
     */
    private String mParam;
    /**
     * The current operation.
     */
    private byte mOp = OP_NONE;
    /**
     * State use for sync.
     */
    private int mState;
    private int mOwner;

    private int mWidth;
    private int mHeight;
    /**
     * Create time.
     */
    private long mCreated;
    /**
     * Identify from database.
     */
    private long mId;
    private String mAttachmentSyncUrl;
    private String mAttachmentMd5;
    private String mNoteGuid;

    @IntDef({TYPE_HANDWRITING, TYPE_TUYA, TYPE_TEXT_CONTENT, TYPE_ALBUM,
            TYPE_PHOTOGRAPH, TYPE_TITLE})
    public @interface Type {
        //type
    }

    @IntDef({TYPE_HANDWRITING, TYPE_TUYA, TYPE_ALBUM, TYPE_PHOTOGRAPH})
    public @interface PictureType {
        //picture type
    }

    private NoteAttribute() {

    }

    private NoteAttribute(Parcel in) {
        try {
            setType(in.readInt());
            setOwner(in.readInt());
            mContent = in.readString();
            setParam(in.readString());
            setOperation(in.readByte());
            setCreated(in.readLong());
            setState(in.readInt());
            setId(in.readLong());
            setNoteGuid(in.readString());
            setVersion(in.readInt());
            setAttachmentMd5(in.readString());
            setAttachmentSyncUrl(in.readString());
            mWidth = in.readInt();
            mHeight = in.readInt();
        } catch (BadParcelableException e) {
            e.printStackTrace();
        }
    }

    public void markDeleted() {
        this.mOp = OP_DELETE;
    }

    public boolean isSameWithOnline() {
        return mState == NoteInfo.STATE_UNCHANGE;
    }

    public boolean isNew() {
        return mOp == OP_ADD;
    }

    public boolean isDeleted() {
        return mOp == OP_DELETE;
    }

    public int getOperation() {
        return mOp;
    }

    void setStateBeforeSave() {
        switch (mOp) {
            case OP_ADD: {
                mState = NoteInfo.STATE_NEW;
                break;
            }
            case OP_MODIFY: {
                if (mState == NoteInfo.STATE_UNCHANGE) {
                    mState = NoteInfo.STATE_MODIFIED;
                }
                break;
            }
            case OP_DELETE: {
                if (mState == NoteInfo.STATE_UNCHANGE || mState == NoteInfo.STATE_MODIFIED) { // 标记删除
                    mOp = OP_MODIFY;
                    mState = NoteAttribute.STATE_DELETED;
                } else {
                    // 操作状态为删除，但从未同步过，即本地修改---直接删除
                }
                break;
            }
            default:
                break;
        }
    }

    public String getNoteGuid() {
        return mNoteGuid;
    }

    public void setNoteGuid(String noteGuid) {
        this.mNoteGuid = noteGuid;
    }

    public long getId() {
        return mId;
    }

    public void setId(long id) {
        this.mId = id;
    }

    public void setOwner(int noteOwnerUser) {
        mOwner = noteOwnerUser;
    }

    public void setOperation(byte newOp) {
        mOp = newOp;
    }

    public int getType() {
        return mType;
    }

    public void setType(@Type int type) {
        this.mType = type;
    }

    public int getState() {
        return mState;
    }

    public void setState(int state) {
        this.mState = state;
    }

    public long getCreated() {
        return mCreated;
    }

    public void setCreated(long created) {
        this.mCreated = created;
    }

    public String getAttachmentSyncUrl() {
        return mAttachmentSyncUrl;
    }

    public void setAttachmentSyncUrl(String attachmentSyncUrl) {
        this.mAttachmentSyncUrl = attachmentSyncUrl;
    }

    public String getAttachmentMd5() {
        return mAttachmentMd5;
    }

    public void setAttachmentMd5(String attachmentMd5) {
        this.mAttachmentMd5 = attachmentMd5;
    }

    public String getParam() {
        return mParam;
    }

    public void setParam(String para) {
        this.mParam = para;
    }

    public int getOwner() {
        return mOwner;
    }

    public String getContent() {
        return mContent;
    }

    public void setContent(String content) { mContent = content; }

    public int getVersion() {
        return mVersion;
    }

    public void setVersion(int mVersion) {
        this.mVersion = mVersion;
    }

    public void setHeight(int height) {
        this.mHeight = height;
    }

    public void setWidth(int width) {
        this.mWidth = width;
    }

    public int getHeight() {
        return this.mHeight;
    }

    public int getWidth() {
        return this.mWidth;
    }

    public void copyFrom(NoteAttribute src) {
        mAttachmentMd5 = src.mAttachmentMd5;
        mAttachmentSyncUrl = src.mAttachmentSyncUrl;
        mCreated = src.mCreated;
        mId = src.mId;
        mNoteGuid = src.mNoteGuid;
        mContent = src.mContent;
        mOp = src.mOp;
        mOwner = src.mOwner;
        mParam = src.mParam;
        mState = src.mState;
        mType = src.mType;
        mWidth = src.mWidth;
        mHeight = src.mHeight;
        src.setVersion(mVersion);

    }

    @Override
    public String toString() {
        return "NoteAttribute [content=" + mContent + ", version=" + mVersion + ", mType=" + mType
                + ", mParam=" + mParam + ", mOp=" + mOp + ", state=" + mState + ", mOwner="
                + mOwner + ", mWidth=" + mWidth + ", mHeight=" + mHeight + ", created="
                + mCreated + ", id=" + mId + ", mAttachmentSyncUrl=" + mAttachmentSyncUrl
                + ", attachmentMd5=" + mAttachmentMd5 + ", noteGuid=" + mNoteGuid + "]";
    }

    public static NoteAttribute newNoteAttribute(@Type int type, String object) {
        return newNoteAttribute(type, object, OP_NONE);
    }

    public static NoteAttribute newNoteAttribute(@Type int type, String object, byte op) {
        NoteAttribute attr = null;
        switch (type) {
            case NoteAttribute.TYPE_HANDWRITING:
            case NoteAttribute.TYPE_TUYA:
            case NoteAttribute.TYPE_ALBUM:
            case NoteAttribute.TYPE_PHOTOGRAPH: {
                final PictureAttribute pictureAttribute = NoteAttribute.newPictureAttribute(type);
                pictureAttribute.setAttrGuid(object);
                attr = pictureAttribute;
                break;
            }
            default:
            case NoteAttribute.TYPE_TEXT_CONTENT: {
                final TextAttribute textAttribute = NoteAttribute.newTextAttribute();
                textAttribute.setText(object);
                attr = textAttribute;
                break;
            }

        }
        attr.setOperation(op);
        return attr;
    }

    public static TextAttribute newTextAttribute() {
        return new TextAttribute();
    }

    public static TextAttribute newTitleAttribute() {
        return new TextAttribute(TYPE_TITLE);
    }

    public static TextAttribute newHintAttribute() {
        return new TextAttribute(TYPE_PHONE_EDIT_HINT);
    }

    public static PictureAttribute newPictureAttribute(@PictureType int type) {
        return new PictureAttribute(type);
    }

    public static PictureAttribute newPictureAttribute() {
        return new PictureAttribute(NoteAttribute.TYPE_ALBUM);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(mType);
        dest.writeInt(mOwner);
        dest.writeString(mContent);
        dest.writeString(mParam);
        dest.writeByte(mOp);
        dest.writeLong(mCreated);
        dest.writeInt(mState);
        dest.writeLong(mId);
        dest.writeString(mNoteGuid);
        dest.writeInt(getVersion());
        dest.writeString(mAttachmentMd5);
        dest.writeString(mAttachmentSyncUrl);
        dest.writeInt(mWidth);
        dest.writeInt(mHeight);
    }

    public static class TextAttribute extends NoteAttribute {
        private boolean mHighLightLink;

        private TextAttribute() {
            setType(TYPE_TEXT_CONTENT);
        }

        private TextAttribute(int type) {
            setType(type);
        }

        public void setText(String content) {
            this.mContent = content;
        }

        public String getText() {
            return mContent;
        }

        public boolean isHighLightLink() {
            return mHighLightLink;
        }

        public void setHighLightLink(boolean highLightLink) {
            this.mHighLightLink = highLightLink;
        }

        @Override
        public void copyFrom(NoteAttribute src) {
            super.copyFrom(src);
            if (src instanceof TextAttribute) {
                mHighLightLink = ((TextAttribute) src).mHighLightLink;
            }
        }

        public static final Parcelable.Creator<TextAttribute> CREATOR = new Parcelable.Creator<TextAttribute>() {
            @Override
            public TextAttribute createFromParcel(Parcel in) {
                return new TextAttribute(in);
            }

            @Override
            public TextAttribute[] newArray(int size) {
                return new TextAttribute[size];
            }
        };

        private TextAttribute(Parcel in) {
            super(in);
            final int iHighLightLink = in.readInt();
            mHighLightLink = iHighLightLink > 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeInt(mHighLightLink ? 1 : 0);
        }

    }

    public static class PictureAttribute extends NoteAttribute {
        private OnlineInfo mOnlineInfo;

        private PictureAttribute(int type) {
            setType(type);
        }

        public void setAttrGuid(String guid) {
            this.mContent = guid;
        }

        public String getAttrGuid() {
            return mContent;
        }

        public OnlineInfo getOnlineInfo() {
            return mOnlineInfo;
        }

        @Override
        public void copyFrom(NoteAttribute src) {
            super.copyFrom(src);
            if (src instanceof PictureAttribute) {
                mOnlineInfo = ((PictureAttribute) src).mOnlineInfo;
            }
        }

        public static final Parcelable.Creator<PictureAttribute> CREATOR = new Parcelable.Creator<PictureAttribute>() {
            @Override
            public PictureAttribute createFromParcel(Parcel in) {
                return new PictureAttribute(in);
            }

            @Override
            public PictureAttribute[] newArray(int size) {
                return new PictureAttribute[size];
            }
        };

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            super.writeToParcel(dest, flags);
            dest.writeParcelable(mOnlineInfo, flags);
        }

        private PictureAttribute(Parcel in) {
            super(in);
            in.readParcelable(OnlineInfo.class.getClassLoader());
        }

    }

}
