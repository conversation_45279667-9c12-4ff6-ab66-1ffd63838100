/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
/**
 * 文字信息类（包含手写、普通文字），笔记1.5版本开始使用
 */
package com.nearme.note.data;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;

public class HandWritingWordData implements Serializable {
    public static final int HANDWRITING_TYPE = 0;

    static final int INT_LENGTH = 4;
    static final int COMMON_TYPE = 1;
    
    private static final int FLOAT_LENGTH = 4;
    private static final int CHAR_LENGTH = 2;
    private static final int BYTE_LENGTH = 1;

    private static final long serialVersionUID = 6681767754837990112L;
    private int mPenColor; // 字体颜色
    private int mType = 0; // 文字类型 ，0为手写，1为普通文字
    private int mTextWidth;
    private ArrayList<float[]> mPathList = new ArrayList<float[]>();
    private ArrayList<float[]> mSpeedList = new ArrayList<float[]>();
    private char mText;

    public int getStorageSize() {
        int total = BYTE_LENGTH; // 文字类型
        if (HANDWRITING_TYPE == mType) {
            total += INT_LENGTH * 2; // 手写颜色 和 手写文字宽度
            int pathSum = getPathList().size();
            for (int i = 0; i < pathSum; ++i) {
                total += INT_LENGTH + getPathList().get(i).length * FLOAT_LENGTH; // 笔画长度
                                                                                  // +
                                                                                  // 笔画数据
            }
        } else {
            total += CHAR_LENGTH;
        }
        return total;
    }

    /**
     * 将文字数据写入流
     * 
     * @param mDataOutputStream
     * @return
     * @throws IOException
     */
    DataOutputStream toOutputStream(DataOutputStream dataOutputStream) throws IOException {
        dataOutputStream.writeByte(mType); // 存入文字类型
        if (HANDWRITING_TYPE == mType) { // 手写
            dataOutputStream.writeInt(mTextWidth); // 存入手写宽度
            dataOutputStream.writeInt(mPenColor); // 存入手写颜色
            int pathSum = getPathList().size();
            dataOutputStream.writeInt(pathSum); // 存入手写笔画数量
            for (int i = 0; i < pathSum; ++i) {
                float[] points = getPathList().get(i);
                int pointSum = points.length;
                dataOutputStream.writeInt(pointSum); // 存入笔画点数
                for (int j = 0; j < pointSum; ++j) {
                    dataOutputStream.writeFloat(points[j]); // 存入笔画点
                }
            }
        } else { // 普通文字
            dataOutputStream.writeChar(getText());
        }
        return dataOutputStream;
    }

    DataOutputStream speedToOutputStream(int index, DataOutputStream dataOutputStream)
            throws IOException {
        dataOutputStream.writeInt(index);
        int pathSum = getSpeedList().size();
        dataOutputStream.writeInt(pathSum); // 存入手写笔画数量
        for (int i = 0; i < pathSum; ++i) {
            float[] speeds = getSpeedList().get(i);
            int pointSum = speeds.length;
            dataOutputStream.writeInt(pointSum);
            for (int j = 0; j < pointSum; ++j) {
                dataOutputStream.writeFloat(speeds[j]);
            }
        }
        return dataOutputStream;
    }

    /**
     * 从流中读出文字数据
     * 
     * @param mDataInputStream
     * @return
     * @throws IOException
     */
    static HandWritingWordData getDataFromInputStream(DataInputStream dataInputStream)
            throws IOException {
        HandWritingWordData handWritingWordData = new HandWritingWordData();
        handWritingWordData.mType = dataInputStream.readByte(); // 读出文字类型
        if (HANDWRITING_TYPE == handWritingWordData.mType) { // 手写
            handWritingWordData.mTextWidth = dataInputStream.readInt(); // 读出手写宽度
            handWritingWordData.mPenColor = dataInputStream.readInt(); // 读出手写颜色
            int pathSum = dataInputStream.readInt(); // 读出手写笔画数量
            for (int i = 0; i < pathSum; ++i) {
                int pointSum = dataInputStream.readInt(); // 读出笔画点数
                float[] points = new float[pointSum];
                for (int j = 0; j < pointSum; ++j) {
                    points[j] = dataInputStream.readFloat(); // 读出笔画点
                }
                handWritingWordData.mPathList.add(points);
            }
        } else { // 普通文字
            handWritingWordData.setText(dataInputStream.readChar());
        }
        return handWritingWordData;
    }

    void getSpeedDataFromInputStream(DataInputStream dataInputStream,
            HandWritingWordData handWritingWordData) throws IOException {
        int pathSum = dataInputStream.readInt();
        for (int i = 0; i < pathSum; ++i) {
            int pointSum = dataInputStream.readInt();
            float[] speeds = new float[pointSum];
            for (int j = 0; j < pointSum; ++j) {
                speeds[j] = dataInputStream.readFloat();
            }
            handWritingWordData.mSpeedList.add(speeds);
        }
    }

    public int getTextWidth() {
        return mTextWidth;
    }

    public void setTextWidth(int textWidth) {
        this.mTextWidth = textWidth;
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }

    public int getPenColor() {
        return mPenColor;
    }

    public void setPenColor(int penColor) {
        this.mPenColor = penColor;
    }

    public ArrayList<float[]> getPathList() {
        return mPathList;
    }

    public ArrayList<float[]> getSpeedList() {
        return mSpeedList;
    }

    public void setPathList(ArrayList<float[]> pathList) {
        this.mPathList = pathList;
    }

    public void setSpeedList(ArrayList<float[]> speedList) {
        this.mSpeedList = speedList;
    }

    public char getText() {
        return mText;
    }

    public void setText(char text) {
        this.mText = text;
    }

    @SuppressWarnings("ucd")
    public void clear() {
        mPathList = null;
        mSpeedList = null;
        mType = HANDWRITING_TYPE;
        mPenColor = 0;
        mTextWidth = 0;
        mText = 0;
    }
}
