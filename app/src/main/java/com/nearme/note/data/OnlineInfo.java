/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.nearme.note.data;

import android.os.BadParcelableException;
import android.os.Parcel;
import android.os.Parcelable;

public class OnlineInfo implements Parcelable {
    private int mDataLen;
    private int mThumbLen;
    private String mDataFileUrl;
    private String mThumbFileUrl;

    public OnlineInfo() {
        //do nothing
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int arg1) {
        dest.writeInt(mDataLen);
        dest.writeInt(mThumbLen);
        dest.writeString(mDataFileUrl);
        dest.writeString(mThumbFileUrl);
    }

    public static final Parcelable.Creator<OnlineInfo> CREATOR = new Parcelable.Creator<OnlineInfo>() {
        public OnlineInfo createFromParcel(Parcel in) {
            return new OnlineInfo(in);
        }

        public OnlineInfo[] newArray(int size) {
            return new OnlineInfo[size];
        }
    };

    private OnlineInfo(Parcel in) {
        try {
            mDataLen = in.readInt();
            mThumbLen = in.readInt();
            mDataFileUrl = in.readString();
            mThumbFileUrl = in.readString();
        } catch (BadParcelableException e) {
            e.printStackTrace();
        }
    }

    public int getDataLen() {
        return mDataLen;
    }

    public void setDataLen(int mDataLen) {
        this.mDataLen = mDataLen;
    }

    public int getThumbLen() {
        return mThumbLen;
    }

    public void setThumbLen(int mThumbLen) {
        this.mThumbLen = mThumbLen;
    }

    public String getDataFileUrl() {
        return mDataFileUrl;
    }

    public void setDataFileUrl(String mDataFileUrl) {
        this.mDataFileUrl = mDataFileUrl;
    }

    public String getThumbFileUrl() {
        return mThumbFileUrl;
    }

    public void setThumbFileUrl(String mThumbFileUrl) {
        this.mThumbFileUrl = mThumbFileUrl;
    }
}