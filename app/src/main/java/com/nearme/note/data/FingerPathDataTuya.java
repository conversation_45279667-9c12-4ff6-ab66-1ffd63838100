/***********************************************************
** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.nearme.note.data;

import com.nearme.note.util.FileUtil;

/*
 * 手写或涂鸦笔记数据类
 */
class FingerPathDataTuya extends FingerPathData {

    private static final byte BASE_BYTES_LEN = FingerPathData.BASE_BYTES_LEN + 4;
    private static final long serialVersionUID = 2339133434861997789L;
    private int mPenType;

    public int toBytes(byte[] buf, int pos) {
        final int OFFSET = 4;
        FileUtil.intToBytes(mId, buf, pos);
        FileUtil.intToBytes(mPenColor, buf, pos += OFFSET);
        FileUtil.intToBytes(mPenType, buf, pos += OFFSET);
        int len = mFingerPath.length;
        for (int j = 0; j < len; j++) {
            FileUtil.intToBytes(Float.floatToIntBits(mFingerPath[j]), buf, pos += OFFSET);
        }
        return pos;
    }

    public int setWithBytes(byte[] buf, int pos, int pathLen) {
        final int OFFSET = 4;
        mId = FileUtil.bytesToInt(buf, pos);
        mPenColor = FileUtil.bytesToInt(buf, pos += OFFSET);
        mPenType = FileUtil.bytesToInt(buf, pos += OFFSET);
        float[] datas = new float[pathLen];
        for (int j = 0; j < pathLen; j++) {
            datas[j] = Float.intBitsToFloat(FileUtil.bytesToInt(buf, pos += OFFSET));
        }
        mFingerPath = datas;
        return pos;
    }

    public int bytesLength() {
        final int FOUR = 4;
        if (mFingerPath != null) {
            return BASE_BYTES_LEN + mFingerPath.length * FOUR;
        } else {
            return BASE_BYTES_LEN;
        }
    }
}