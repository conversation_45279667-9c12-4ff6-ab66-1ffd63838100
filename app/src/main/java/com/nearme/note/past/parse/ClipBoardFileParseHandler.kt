/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardFileParseHandler.kt
 * Description    : ClipBoardFileParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/13         1.0           create
 */
package com.nearme.note.past.parse

import android.graphics.drawable.Drawable
import android.net.Uri
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.external.GlideThumbManager
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.past.ClipBoardHtmlParser
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_ATTACH_ID
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_SRC
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.ClipBoardPastFileInfo
import com.nearme.note.past.parse.ClipBoardImageParseHandler.Companion.ATTR_IMG_HEIGHT
import com.nearme.note.past.parse.ClipBoardImageParseHandler.Companion.ATTR_IMG_WIDTH
import com.nearme.note.speech.utils.PresetNoteSpeechUtils
import com.oplus.note.doc.thumbnail.config.ThumbnailConfig
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.glide.DocThumbEntity
import com.oplus.note.glide.GlideApp
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.utils.DateAndTimeUtils
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import java.io.File
import java.util.UUID

class ClipBoardFileParseHandler : ClipBoardBaseParseHandler<ClipBoardPastFileInfo>() {
    companion object {
        private const val TAG = "ClipBoardFileParseHandler"
        private const val ATTR_DOC_ATTACH_ID = "docattachid"
        private const val ATTR_FILE_NAME = "filename"
        private const val ATTR_FILE_SIZE = "filesize"
        private const val ATTR_FILE_DATE = "filedate"
        private const val ATTR_FILE_TYPE = "filetype"
        private const val ATTR_FILE_PATH = "filepath"
        private const val ATTR_DOC_THUMBNAIL = "docThumbnail"
    }

    override suspend fun createHtmlElement(
        richNoteId: String,
        fileInfo: MediaFileInfo,
        document: Document,
        uri: Uri
    ): Pair<Element, ClipBoardPastFileInfo>? {
        val attachId = UUID.randomUUID().toString()
        val src = "/$richNoteId/${UUID.randomUUID()}${ThumbFileConstants.THUMB}"
        val docAttachId = UUID.randomUUID().toString()
        val sizeInfo = getAssertImageSize(getAssertFileName())
        val filePath = "${getAttachRootPath()}/$richNoteId/$docAttachId.${
            ClipBoardHtmlParser.getFileFormatByName(
                fileInfo.fileName
            )
        }"
        val element = document.createElement(ClipBoardHtmlParser.TAG_FILE_NAME)
        element.attr(ATTR_ATTACH_ID, attachId)
        element.attr(ATTR_DOC_ATTACH_ID, docAttachId)
        element.attr(ATTR_FILE_NAME, "${fileInfo.fileName}")
        element.attr(ATTR_FILE_SIZE, "${fileInfo.fileSize}")
        element.attr(
            ATTR_FILE_DATE,
            DateAndTimeUtils.timeInMillis2Date(appContext, System.currentTimeMillis(), true)
        )
        element.attr(ATTR_FILE_TYPE, "${Attachment.TYPE_FILE_CARD}")
        element.attr(ATTR_IMG_WIDTH, "${sizeInfo?.width ?: 0}")
        element.attr(ATTR_IMG_HEIGHT, "${sizeInfo?.height ?: 0}")
        element.attr(ATTR_SRC, src)
        element.attr(ATTR_DOC_THUMBNAIL, "")
        element.attr(ATTR_FILE_PATH, filePath)
        return Pair(element, ClipBoardPastFileInfo(attachId, docAttachId, src, filePath))
    }

    override suspend fun getAttachOperateResult(
        pastNodeInfo: ClipBoardPastNodeInfo
    ): JSONObject {
        val status = handleFileAttachOperate(pastNodeInfo)
        val fileInfo = pastNodeInfo.data as ClipBoardPastFileInfo
        val jsonResult = getCommonJsonResult(status, fileInfo.attachId)
        if (STATUS_FAIL == status) {
            checkDeleteFile(fileInfo.src)
            checkDeleteFile(fileInfo.docSrc)
        }
        return jsonResult
    }

    private fun getAssertFileName(): String {
        return PresetNoteSpeechUtils.PRESET_NOTE_FILE_CARD_ATTACHMENT
    }

    private suspend fun handleFileAttachOperate(
        pastNodeInfo: ClipBoardPastNodeInfo
    ): Int = withContext(IO) {
        val bitmap =
            MediaUtils.getThumbBitmapFromInputStream(appContext.assets.open(getAssertFileName()))
                ?: return@withContext STATUS_FAIL
        val fileInfo = pastNodeInfo.data as ClipBoardPastFileInfo
        val startTime = System.currentTimeMillis()
        var pngResult = false
        val pngJob = async(IO) {
            pngResult = saveCompressBmpToFile(bitmap, "${getAttachRootPath()}${fileInfo.src}")
        }
        var fileResult = false
        val fileJob = async(IO) {
            fileResult = copyAttachFromUri(pastNodeInfo.uri, fileInfo.docSrc)
        }
        pngJob.await()
        fileJob.await()
        AppLogger.BASIC.e(
            TAG,
            "handleAttachOperate coastTime=${System.currentTimeMillis() - startTime} pngResult=:$pngResult fileResult=$fileResult"
        )
        return@withContext if (pngResult && fileResult) STATUS_SUCCESS else STATUS_FAIL
    }

    fun handleFileThumbNailOperate(
        fragment: WVNoteViewEditFragment,
        pastNodeInfo: ClipBoardPastNodeInfo
    ) {
        fragment.lifecycleScope.launch(IO) {
            val mediaFileInfo = pastNodeInfo.mediaFileInfo
            val fileInfo = pastNodeInfo.data as ClipBoardPastFileInfo
            val doc = DocThumbEntity(
                pastNodeInfo.attachId,
                mediaFileInfo.uri,
                mediaFileInfo.mimeType,
                mediaFileInfo.fileName,
                mediaFileInfo.fileSize
            )
            GlideApp.with(fragment).downloadOnly().load(doc).override(ThumbnailConfig.THUMB_SIZE).into(object : CustomTarget<File>() {
                override fun onLoadFailed(errorDrawable: Drawable?) {
                    super.onLoadFailed(errorDrawable)
                    AppLogger.BASIC.e(TAG, "doc file download failed $errorDrawable")
                    val defaultIcon = GlideThumbManager.getDefaultThumbnail(appContext, mediaFileInfo.fileName)
                    fragment.webViewContainer?.updateDocThumbnail(
                        fileInfo.attachId,
                        fileInfo.docAttachId,
                        defaultIcon
                    ) {
                    }
                }

                override fun onResourceReady(
                    resource: File,
                    transition: Transition<in File>?
                ) {
                    AppLogger.BASIC.d(TAG, "doc file download success ${resource.path.length}")
                    val thumbnailPath = resource.path
                    val webPath = thumbnailPath.substring(thumbnailPath.indexOf(GlideThumbManager.GLIDE_CACHE_DIR))
                    fragment.webViewContainer?.updateDocThumbnail(
                        fileInfo.attachId, fileInfo.docAttachId, webPath
                    ) {
                    }
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    AppLogger.BASIC.d(TAG, "doc file download cleared")
                }
            })
        }
    }
}