/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardPastInfo.kt
 * Description    : ClipBoardPastInfo.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/11         1.0           create
 */
package com.nearme.note.past.data

import androidx.annotation.Keep
import com.oplus.notes.webview.data.clipboard.PasteResult

@Keep
interface CLipBoardPastBaseInfo {
    val attachId: String
}

@Keep
data class ClipBoardPastImageInfo(
    override val attachId: String,
    val src: String,
    val placeHolderSrc: String,
    var picWidth: Int,
    var picHeight: Int,
) : CLipBoardPastBaseInfo

@Keep
data class ClipBoardPastVideInfo(
    override val attachId: String,
    val src: String,
    val videoSrc: String,
) : CLipBoardPastBaseInfo

@Keep
data class ClipBoardPastAudioInfo(
    override val attachId: String,
    val recordId: String,
    val src: String,
    val audioSrc: String,
    var audioDuration: Long,
) : CLipBoardPastBaseInfo

@Keep
data class ClipBoardPastFileInfo(
    override val attachId: String,
    val docAttachId: String,
    val src: String,
    val docSrc: String,
) : CLipBoardPastBaseInfo

@Keep
data class ClipBoardPastInfoResult(
    val pastResult: PasteResult,
    val unSupportTypeList: MutableList<String> = mutableListOf(),
    val textItemCount: Int = 0,
    val limitPast: Boolean = false,
    val totalValidCount: Int = 0,
    val selectionInTable: Boolean = false,
    val maxLimitAttachCount: Int = 0
)