/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : HtmlPasteParser.kt
 * Description    : HtmlPasteParser.kt
 * Version        : 1.0
 * Date           : 2025/5/17
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/5/17         1.0           create
 */
package com.nearme.note.past

import androidx.annotation.Keep
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.logic.ThumbFileConstants
import com.oplus.note.NoteFileProvider
import com.oplus.note.utils.DateAndTimeUtils
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import java.io.File
import java.util.UUID

//针对于粘贴的时候将html内容解析附件信息
@Keep
data class PasteAttachInfo(
    //原始地址
    val orgSrc: String,
    //新地址信息
    val newSrc: String,
)

@Keep
data class PastNodeInfo(
    //处理类型
    val nodeType: String,
    //当前节点的attachId
    val attachId: String,
    //附件数据
    val attachList: MutableList<PasteAttachInfo>,
)

@Keep
data class PasteHtmlInfo(
    //html信息
    var htmlText: String?,
    //存储的节点信息
    var attachNodeList: MutableList<PastNodeInfo> = mutableListOf(),
    //针对于selectionTab的时候 是否过滤了所有的附件信息
    var ignoreAttach: Boolean = false,
    //是否达到最大的数量
    var limitPast: Boolean = false,
)

object HtmlPasteParser {
    private const val CACHE_DIRECTORY = "clipboard"
    //录音属性信息
    private const val ATTR_RECORD_ID_NAME = "recordid"

    //录音的识别属性信息
    private const val ATTR_RECORD_ASR_ATTACH_ID = "asrattachid"

    //针对于AI摘要的信息
    private const val ATTR_RECORD_LRC_SPEECH = "lrcspeech"

    //当前是否为asraudio
    private const val ATTR_RECORD_ARC_AUDIO = "isasraudio"

    //视频文件属性信息
    private const val ATTR_VIDEO_SRC_NAME = "videosrc"

    //视频attach_id
    private const val ATTR_VIDEO_ATTACH_ID = "videoattachid"

    //视频时间戳
    private const val ATTR_VIDEO_TIME_STAMP = "timeStamp"

    //视频添加到的时间
    private const val ATTR_VIDEO_ADD_NOTE_TIME = "addtonotetime"

    //文档attach_id
    private const val ATTR_DOC_ATTACH_ID = "docattachid"

    //针对于src的属性
    private const val ATTR_SRC = "src"

    //占位的placeholder的属性
    private const val ATTR_PLACEHOLDER_SRC = "placeholdersrc"

    //图片资源存在的属性
    private const val ATTR_SRC_EXIT = "srcexist"

    //针对于涂鸦的src的属性
    private const val ATTR_IMG_SRC = "imagesrc"

    //涂鸦的paintSrc的属性
    private const val ATTR_PAINT_SRC = "paintsrc"

    //涂鸦的paintId的属性
    private const val ATTR_PAINT_ID = "paintid"

    //uuid属性
    private const val ATTR_UUID = "uuid"

    private const val ATTR_ATTACH_ID = "attachid"

    //进行粘贴属性
    private const val ATTR_PAST_STATUS = "pastStatus"

    //文件名称attr
    private const val ATTR_FILE_NAME = "filename"

    //文件地址的attr
    private const val ATTR_FILE_PATH = "filepath"

    //涂鸦的标签
    private const val TAG_PAINT_NAME = "paint"

    //录音的标签
    private const val TAG_RECORD_NAME = "record"

    //视频的标签
    private const val TAG_VIDEO_NAME = "videocard"

    //文档的标签
    private const val TAG_FILE_NAME = "filecard"

    //针对于日程标签
    const val TAG_SCHEDULE_CARD_NAME = "schedulecard"

    //针对于联系人标签
    const val TAG_CONTACT_CARD_NAME = "contactcard"

    //针对于图片标签
    private const val TAG_IMG_NAME = "img"

    //针对于src采用split后的长度
    private const val SRC_SPLIT_LENGTH = 3

    /**
     * 将html中的信息进行解析成信息
     */
    @Suppress("LongMethod", "ComplexMethod")
    fun handlePastHtml(
        htmlText: String?,
        richNoteId: String?,
        maxCount: Int,
        selectionInTable: Boolean
    ): PasteHtmlInfo {
        val attachNodeList: MutableList<PastNodeInfo> = mutableListOf()
        val pasteHtmlInfo = PasteHtmlInfo(htmlText, attachNodeList)
        if (htmlText.isNullOrEmpty() || richNoteId.isNullOrEmpty()) {
            return pasteHtmlInfo
        }
        val document = Jsoup.parse(htmlText)
        document.outputSettings(Document.OutputSettings().prettyPrint(false))
        val elements = document.body().allElements.filter { element ->
            checkSupportTagName(element.tagName())
        }
        if (elements.isEmpty()) {
            //没有信息 直接返回
            return pasteHtmlInfo
        }
        var addAttachCount = 0
        var limitPast = false
        var orgNoteId = ""//复制前的笔记Id
        if (selectionInTable) {
            pasteHtmlInfo.ignoreAttach = true
        }
        //循环遍历
        for (element in elements) {
            if (selectionInTable || maxCount <= 0 || addAttachCount == maxCount) {
                //如果是表格内 或者是 已经到达最大数量  不能将元素进行添加
                element.remove()
                if (addAttachCount == maxCount) {
                    limitPast = true
                }
                continue
            }
            val tagName = element.tagName()
            var src = element.attr(ATTR_SRC)
            //在检测是否为涂鸦的资源
            if (TAG_PAINT_NAME == tagName) {
                src = element.attr(ATTR_IMG_SRC)
            }
            if (src.isNullOrEmpty() || !checkAttachSrc(src)) {
                continue
            }
            addAttachCount++
            //当有值的时候
            val srcSplitList = src.split("/")
            //直接取第二个表明为noteId
            if (orgNoteId.isEmpty() && srcSplitList.size > 1) {
                orgNoteId = srcSplitList[1]
            }
            val attachList: MutableList<PasteAttachInfo> = mutableListOf()
            val attachId = UUID.randomUUID().toString() //生成新的uuid
            val newStr = "/$richNoteId/$attachId${ThumbFileConstants.THUMB}"
            attachList.add(
                PasteAttachInfo(
                    src, newStr
                )
            )
            if (TAG_PAINT_NAME == tagName) {
                //当前为涂鸦资源
                element.attr(ATTR_IMG_SRC, newStr)
            } else {
                element.attr(ATTR_SRC, newStr)
            }
            element.attr(ATTR_PAST_STATUS, "0")
            //这里需要使用当前的attachid 否则动态生成会不匹配
            element.attr(ATTR_ATTACH_ID, attachId)
            when (tagName) {
                TAG_IMG_NAME -> {
                   //针对于图片处理
                    handlePastImg(element, richNoteId, attachId, attachList)
                }

                TAG_RECORD_NAME -> {
                    //针对于录音文件
                    handlePastRecord(element, orgNoteId, richNoteId, attachList)
                }

                TAG_VIDEO_NAME -> {
                    //针对于视频文件
                    handlePastVideo(element, richNoteId, attachList)
                }

                TAG_FILE_NAME -> {
                    //针对于文档文档
                    handlePastDocument(element, richNoteId, attachList)
                }

                TAG_PAINT_NAME -> {
                    //针对于涂鸦处理
                    handlePastPaint(element, richNoteId, attachList)
                }
            }
            attachNodeList.add(PastNodeInfo(tagName, attachId, attachList))
        }
        pasteHtmlInfo.htmlText = document.body().html()
        pasteHtmlInfo.limitPast = limitPast
        return pasteHtmlInfo
    }

    /**
     * 针对于粘贴图片
     */
    private fun handlePastImg(
        element: Element,
        richNoteId: String,
        attachId: String,
        attachList: MutableList<PasteAttachInfo>
    ) {
        val holderSrc = element.attr(ATTR_PLACEHOLDER_SRC)
        if (TAG_IMG_NAME != element.tagName() || !checkImgHolderSrc(holderSrc)) {
            return
        }
        val newSrc = "/$richNoteId/$attachId${ThumbFileConstants.PLACEHOLDER}"
        attachList.add(
            PasteAttachInfo(
                element.attr(ATTR_PLACEHOLDER_SRC), newSrc
            )
        )
        element.attr(ATTR_PLACEHOLDER_SRC, newSrc)
        val srcExitStr = element.attr(ATTR_SRC_EXIT)
        if (srcExitStr.isNotEmpty()) {
            element.attr(ATTR_SRC_EXIT, "false")
        }
    }

    /**
     * 检查图片的holder的图片是否存在
     */
    private fun checkImgHolderSrc(holderSrc: String): Boolean {
        return holderSrc.isNotEmpty() && checkAttachSrc(holderSrc) && holderSrc.endsWith(
            ThumbFileConstants.PLACEHOLDER
        )
    }

    /**
     * 处理粘贴音频文件
     */
    private fun handlePastRecord(
        element: Element,
        orgNoteId: String,
        richNoteId: String,
        attachList: MutableList<PasteAttachInfo>
    ) {
        if (TAG_RECORD_NAME != element.tagName() || element.attr(ATTR_RECORD_ID_NAME)
                .isNullOrEmpty()
        ) {
            return
        }
        //音频文件信息
        val uuid = UUID.randomUUID().toString() //生成新的uuid
        val recordId = element.attr(ATTR_RECORD_ID_NAME)
        //先通过缓存查找 然后在通过便签内查找
        var fileSuffix = getRecordFileSuffix(element, "${getCacheRootPath()}/$orgNoteId/", recordId)
        if (fileSuffix.isEmpty()) {
            fileSuffix = getRecordFileSuffix(element, "${getFileRootPath()}/$orgNoteId/", recordId)
        }
        val asrAttachId = element.attr(ATTR_RECORD_ASR_ATTACH_ID)
        val lrcSpeech = element.attr(ATTR_RECORD_LRC_SPEECH)
        var newSrcSuffix = fileSuffix
        if (asrAttachId.isEmpty() && lrcSpeech.isNotEmpty()) {
            newSrcSuffix = ".wav"
        }
        attachList.add(
            PasteAttachInfo(
                "/$orgNoteId/$recordId$fileSuffix",
                "/$richNoteId/$uuid$newSrcSuffix"
            )
        )
        element.attr(ATTR_RECORD_ID_NAME, uuid)
        if (asrAttachId.isNotEmpty()) {
            //录音的asr
            val asrUUId = UUID.randomUUID().toString() //生成新的uuid
            attachList.add(
                PasteAttachInfo(
                    "/$orgNoteId/$asrAttachId.json", "/$richNoteId/$asrUUId.json"
                )
            )
            element.attr(ATTR_RECORD_ASR_ATTACH_ID, asrUUId)
        } else {
            if (lrcSpeech.isNotEmpty()) {
                val lrcUUID = UUID.randomUUID().toString()
                val newLrcSpeech = "/$richNoteId/$lrcUUID.json"
                attachList.add(
                    PasteAttachInfo(
                        lrcSpeech, newLrcSpeech
                    )
                )
                //需要将摘要类型转变为普通录音类型 在Fragment的onNodeAdded有判断
                element.removeAttr(ATTR_RECORD_LRC_SPEECH)
                element.attr(ATTR_RECORD_ASR_ATTACH_ID, lrcUUID)
                element.attr(ATTR_RECORD_ARC_AUDIO, "true")
            }
        }
    }

    /**
     * 获取Record的文件的后缀 通过文件来查找 这里是单层文件目录
     */
    private fun getRecordFileSuffix(element: Element, fileDir: String, recordId: String): String {
        var fileSuffix = ""
        val fileName = element.attr(ATTR_FILE_NAME)
        if (fileName.isNotEmpty()) {
            fileSuffix = getSuffixFromFileUrl(fileName)
        }
        if (fileSuffix.isNotEmpty()) {
            return fileSuffix
        }
        val dirFile = File(fileDir)
        if (!dirFile.exists()) {
            return fileSuffix
        }
        val fileList = dirFile.listFiles()
        if (fileList.isNullOrEmpty()) {
            return fileSuffix
        }
        for (file in fileList) {
            if (file.name.startsWith(recordId)) {
                fileSuffix = getSuffixFromFileUrl(file.name)
                break
            }
        }
        return fileSuffix
    }

    /**
     * 处理粘贴视频文件
     */
    private fun handlePastVideo(
        element: Element,
        richNoteId: String,
        attachList: MutableList<PasteAttachInfo>
    ) {
        if (TAG_VIDEO_NAME != element.tagName()) {
            return
        }
        val uuid = UUID.randomUUID().toString() //生成新的uuid
        val videoSrc = element.attr(ATTR_VIDEO_SRC_NAME)
        if (videoSrc.isNotEmpty()) {
            val fileSuffix = getSuffixFromFileUrl(videoSrc)
            val newVideoSrc = "/$richNoteId/$uuid$fileSuffix"
            attachList.add(
                PasteAttachInfo(
                    videoSrc, newVideoSrc
                )
            )
            element.attr(ATTR_VIDEO_ATTACH_ID, uuid)
            element.attr(ATTR_VIDEO_SRC_NAME, newVideoSrc)
            val createTime = System.currentTimeMillis()
            element.attr(ATTR_VIDEO_TIME_STAMP, "$createTime")
            element.attr(
                ATTR_VIDEO_ADD_NOTE_TIME,
                DateAndTimeUtils.timeInMillis2Date(appContext, createTime, true)
            )
        }
    }

    /**
     * 处理粘贴文档
     */
    private fun handlePastDocument(
        element: Element,
        richNoteId: String,
        attachList: MutableList<PasteAttachInfo>
    ) {
        if (TAG_FILE_NAME != element.tagName()) {
            return
        }
        val uuid = UUID.randomUUID().toString()
        //获取原始路径 并将路径都替换为非全路径信息
        val fileOrgPath = element.attr(ATTR_FILE_PATH).replace(getFileRootPath(), "")
        if (fileOrgPath.isNotEmpty()) {
            val fileSuffix = getSuffixFromFileUrl(fileOrgPath)
            val newFileSrc = "/$richNoteId/$uuid$fileSuffix"
            attachList.add(
                PasteAttachInfo(
                    fileOrgPath, newFileSrc
                )
            )
            element.attr(ATTR_DOC_ATTACH_ID, uuid)
            element.attr(ATTR_FILE_PATH, "${getFileRootPath()}$newFileSrc")
        }
    }

    /**
     * 处理粘贴涂鸦
     */
    private fun handlePastPaint(
        element: Element,
        richNoteId: String,
        attachList: MutableList<PasteAttachInfo>
    ) {
        val paintSrc = element.attr(ATTR_PAINT_SRC)
        if (TAG_PAINT_NAME != element.tagName() || paintSrc.isNullOrEmpty()) {
            return
        }
        element.attr(ATTR_UUID, UUID.randomUUID().toString())
        val uuid = UUID.randomUUID().toString()
        val fileSuffix = getSuffixFromFileUrl(paintSrc)
        val newFileSrc = "/$richNoteId/$uuid$fileSuffix"
        attachList.add(
            PasteAttachInfo(
                paintSrc, newFileSrc
            )
        )
        element.attr(ATTR_PAINT_ID, uuid)
        element.attr(ATTR_PAINT_SRC, newFileSrc)
    }

    private fun getSuffixFromFileUrl(fileUrl: String): String {
        if (fileUrl.isEmpty()) {
            return ""
        }
        val lastDotIndex = fileUrl.lastIndexOf('.')
        if (lastDotIndex == -1 || lastDotIndex == 0 || lastDotIndex >= fileUrl.length - 1) {
            return ""
        }
        //返回带.的信息
        return fileUrl.substring(lastDotIndex)
    }

    /**
     * 检测src是为内部的
     */
    private fun checkAttachSrc(src: String): Boolean {
        if (src.isEmpty() || !src.startsWith("/")) {
            return false
        }
        val srcSplitList = src.split("/")
        //当前的src地址 都是为/noteId/地址
        return srcSplitList.size == SRC_SPLIT_LENGTH
    }

    /**
     * 检测是否能够处理的标签
     */
    private fun checkSupportTagName(tagName: String): Boolean {
        return TAG_IMG_NAME == tagName || TAG_RECORD_NAME == tagName
                || TAG_VIDEO_NAME == tagName || TAG_FILE_NAME == tagName
                || TAG_PAINT_NAME == tagName || TAG_SCHEDULE_CARD_NAME == tagName
                || TAG_CONTACT_CARD_NAME == tagName
    }

    private fun getFileRootPath(): String {
        return appContext.filesDir.absolutePath
    }

    private fun getCacheRootPath(): String {
        return "${appContext.cacheDir}/$CACHE_DIRECTORY"
    }

    /**
     * 针对于剪切板内部附件信息
     */
    @Suppress("ComplexMethod")
    fun getHtmlClipBoardAttachList(htmlText: String): MutableList<String> {
        val attachList: MutableList<String> = mutableListOf()
        if (htmlText.isEmpty()) {
            return attachList
        }
        val document = Jsoup.parse(htmlText)
        val elements = document.body().allElements.filter { element ->
            checkSupportTagName(element.tagName())
        }
        if (elements.isEmpty()) {
            //没有信息 直接返回
            return attachList
        }
        var orgNoteId = ""//复制前的笔记Id
        for (element in elements) {
            val tagName = element.tagName()
            var src = element.attr(ATTR_SRC)
            //在检测是否为涂鸦的资源
            if (TAG_PAINT_NAME == tagName) {
                src = element.attr(ATTR_IMG_SRC)
            }
            if (src.isNullOrEmpty() || !checkAttachSrc(src)) {
                continue
            }
            //当有值的时候
            val srcSplitList = src.split("/")
            if (orgNoteId.isEmpty() && srcSplitList.size > 1) {
                orgNoteId = srcSplitList[1]
            }
            attachList.add("${getFileRootPath()}$src")
            when (tagName) {
                TAG_IMG_NAME -> {
                    //针对于图片处理
                    handleClipBoardImg(element, attachList)
                }
                TAG_RECORD_NAME -> {
                    //针对于录音文件
                    handleClipBoardRecord(element, orgNoteId, attachList)
                }

                TAG_VIDEO_NAME -> {
                    //针对于视频文件
                    handleClipBoardVideo(element, attachList)
                }

                TAG_FILE_NAME -> {
                    //针对于文档
                    handleClipBoardDocument(element, attachList)
                }

                TAG_PAINT_NAME -> {
                    //针对于涂鸦
                    handleClipBoardPaint(element, attachList)
                }
            }
        }
        return attachList
    }

    /**
     * 处理剪切板图片文件
     */
    private fun handleClipBoardImg(element: Element, attachList: MutableList<String>) {
        val holderSrc = element.attr(ATTR_PLACEHOLDER_SRC)
        if (TAG_IMG_NAME != element.tagName() || !checkImgHolderSrc(holderSrc)) {
            return
        }
        attachList.add("${getFileRootPath()}$holderSrc")
    }

    /**
     * 处理剪切板音频文件
     */
    private fun handleClipBoardRecord(
        element: Element,
        orgNoteId: String,
        attachList: MutableList<String>
    ) {
        if (TAG_RECORD_NAME != element.tagName() || element.attr(ATTR_RECORD_ID_NAME)
                .isNullOrEmpty()
        ) {
            return
        }
        val recordId = element.attr(ATTR_RECORD_ID_NAME)
        val fileSuffix = getRecordFileSuffix(element, "${getFileRootPath()}/$orgNoteId/", recordId)
        attachList.add("${getFileRootPath()}/$orgNoteId/$recordId$fileSuffix")
        val asrAttachId = element.attr(ATTR_RECORD_ASR_ATTACH_ID)
        if (asrAttachId.isNotEmpty()) {
            //录音的asr
            attachList.add("${getFileRootPath()}/$orgNoteId/$asrAttachId.json")
        } else {
            val lrcSpeech = element.attr(ATTR_RECORD_LRC_SPEECH)
            if (lrcSpeech.isNotEmpty()) {
                attachList.add("${getFileRootPath()}$lrcSpeech")
            }
        }
    }

    /**
     * 处理剪切板视频文件
     */
    private fun handleClipBoardVideo(
        element: Element,
        attachList: MutableList<String>
    ) {
        val videoSrc = element.attr(ATTR_VIDEO_SRC_NAME)
        if (TAG_VIDEO_NAME != element.tagName() || videoSrc.isNullOrEmpty()) {
            return
        }
        attachList.add("${getFileRootPath()}$videoSrc")
    }

    /**
     * 处理剪切板文档
     */
    private fun handleClipBoardDocument(
        element: Element,
        attachList: MutableList<String>
    ) {
        if (TAG_FILE_NAME != element.tagName()) {
            return
        }
        //获取原始路径 并将路径都替换为非全路径信息
        val fileOrgPath = element.attr(ATTR_FILE_PATH)
        if (fileOrgPath.isNotEmpty()) {
            if (fileOrgPath.startsWith(getFileRootPath())) {
                attachList.add(fileOrgPath)
            } else {
                attachList.add("${getFileRootPath()}$fileOrgPath")
            }
        }
    }

    /**
     * 处理剪切板涂鸦
     */
    private fun handleClipBoardPaint(
        element: Element,
        attachList: MutableList<String>
    ) {
        val paintSrc = element.attr(ATTR_PAINT_SRC)
        if (TAG_PAINT_NAME != element.tagName() || paintSrc.isNullOrEmpty()) {
            return
        }
        attachList.add("${getFileRootPath()}$paintSrc")
    }

    /**
     * 获取对外的html信息
     */
    fun getHtmlClipBoardExtraHtml(htmlText: String): String {
        val document = Jsoup.parse(htmlText)
        //都是图片直接获取图片的标签
        val elements = document.getElementsByAttribute(ATTR_SRC)
        if (elements.isEmpty()) {
            //没有信息 直接返回
            return htmlText
        }
        for (element in elements) {
            val src = element.attr(ATTR_SRC)
            if (src.isNullOrEmpty()) {
                continue
            }
            //地址需要满足条件信息
            if (!checkAttachSrc(src)) {
                continue
            }
            val url = NoteFileProvider.getUriForCommonFile(
                appContext, "${getFileRootPath()}$src"
            )
            element.attr(ATTR_SRC, url.toString())
        }
        return document.body().html()
    }
}