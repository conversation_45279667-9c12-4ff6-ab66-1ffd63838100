/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : IClipBoardParseHandler.kt
 * Description    : IClipBoardParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/11         1.0           create
 */
package com.nearme.note.past.parse

import android.net.Uri
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.CLipBoardPastBaseInfo
import com.oplus.note.external.MediaFileInfo
import org.json.JSONObject
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element

interface IClipBoardParseHandler<T : CLipBoardPastBaseInfo> {
    /**
     * 通过Uri转换为element标签
     */
    suspend fun createHtmlElement(
        richNoteId: String,
        fileInfo: MediaFileInfo,
        document: Document,
        uri: Uri
    ): Pair<Element, T>?

    /**
     * 获取附件操作信息结果  返回jsonObject 到js 进行处理
     */
    suspend fun getAttachOperateResult(
        pastNodeInfo: ClipBoardPastNodeInfo
    ): JSONObject
}