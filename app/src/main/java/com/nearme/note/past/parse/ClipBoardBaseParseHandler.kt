/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardBaseParseHandler.kt
 * Description    : ClipBoardBaseParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/11         1.0           create
 */
package com.nearme.note.past.parse

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Size
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.CLipBoardPastBaseInfo
import com.nearme.note.util.FileUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.MediaStoreHelper
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File

abstract class ClipBoardBaseParseHandler<T : CLipBoardPastBaseInfo> : IClipBoardParseHandler<T> {
    companion object {
        private const val TAG = "ClipBoardBaseParseHandler"
        const val STATUS_FAIL = 2
        const val STATUS_SUCCESS = 1
        const val WEBP_COMPRESS_QUALITY = 10
        const val ATTACH_ID = "attachId"
        const val PAST_STATUS = "status"
    }

    fun getAttachRootPath(): String {
        return appContext.filesDir.absolutePath
    }

    fun checkDeleteFile(relativePath: String) {
        if (relativePath.isEmpty()) {
            return
        }
        val file = File("${getAttachRootPath()}$relativePath")
        if (file.exists()) {
            file.delete()
        }
    }

    open suspend fun copyAttachFromUri(uri: Uri, absolutePath: String): Boolean {
        if (absolutePath.isEmpty()) {
            AppLogger.BASIC.d(TAG, "copyAttachFromUri  the absolutePath is empty.")
            return false
        }
        kotlin.runCatching {
            MediaStoreHelper.copyAttFromUri(
                uri, absolutePath, appContext
            )
        }.onFailure {
            AppLogger.BASIC.e(TAG, "copyAttachFromUri  failed. ${it.message}")
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "copyAttachFromUri  success.")
            return true
        }
        return false
    }


    /**
     * 针对于文件的处理 根据不同的类型来
     */
    open suspend fun handleAttachOperate(pastNodeInfo: ClipBoardPastNodeInfo): Int =
        withContext(IO) {
            return@withContext STATUS_SUCCESS
        }

    fun getCommonJsonResult(result: Int, attachId: String): JSONObject {
        return if (STATUS_SUCCESS == result) {
            //成功
            JSONObject().apply {
                put(ATTACH_ID, attachId)
                put(PAST_STATUS, STATUS_SUCCESS)
            }
        } else {
            //失败
            JSONObject().apply {
                put(ATTACH_ID, attachId)
                put(PAST_STATUS, STATUS_FAIL)
            }
        }
    }

    open suspend fun getAssertImageSize(assertFileName: String): Size? =
        withContext(IO) {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            return@withContext kotlin.runCatching {
                appContext.assets.open(assertFileName).use { stream ->
                    BitmapFactory.decodeStream(stream, null, options)
                }
                Size(options.outWidth, options.outHeight)
            }.onFailure {
                AppLogger.BASIC.e(TAG, "getAssertImageSize error: ${it.message}")
            }.getOrNull()
        }

    /**
     * 获取图片的宽高
     */
    open suspend fun getImageSize(uri: Uri): Size? {
        return null
    }

    open suspend fun saveCompressBmpToFile(bitmap: Bitmap, realPath: String): Boolean {
        return FileUtil.saveCompressBmpToFile(
            bitmap,
            realPath,
            Bitmap.Config.ARGB_8888,
            Bitmap.CompressFormat.WEBP,
            WEBP_COMPRESS_QUALITY
        )
    }
}