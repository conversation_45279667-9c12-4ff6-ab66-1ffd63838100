/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardVideoParseHandler.kt
 * Description    : ClipBoardVideoParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/11         1.0           create
 */
package com.nearme.note.past.parse

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.util.Size
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.past.ClipBoardHtmlParser
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_ATTACH_ID
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_SRC
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.ClipBoardPastVideInfo
import com.nearme.note.view.NoteEditImageView
import com.oplus.note.R
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.DateAndTimeUtils
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import java.util.UUID

class ClipBoardVideoParseHandler : ClipBoardBaseParseHandler<ClipBoardPastVideInfo>() {
    companion object {
        private const val TAG = "ClipBoardVideoParseHandler"
        private const val ATTR_VIDEO_SRC = "videosrc"
        private const val ATTR_VIDEO_ATTACH_ID = "videoattachid"
        private const val ATTR_VIDEO_FILE_NAME = "filename"
        private const val ATTR_VIDEO_FILE_SIZE = "filesize"
        private const val ATTR_VIDEO_DURATION = "duration"
        private const val ATTR_CREATE_TIME = "timeStamp"
        private const val ATTR_ADD_NOTE_TIME = "addtonotetime"
        private const val ROTATION_90 = 90
        private const val ROTATION_270 = 270
    }

    override suspend fun createHtmlElement(
        richNoteId: String,
        fileInfo: MediaFileInfo,
        document: Document,
        uri: Uri
    ): Pair<Element, ClipBoardPastVideInfo>? {
        getImageSize(uri)?.let {
            val attachId = UUID.randomUUID().toString()
            val videoAttachId = UUID.randomUUID().toString()
            val src = "/$richNoteId/$attachId${ThumbFileConstants.THUMB}"
            val videoSrc =
                "/$richNoteId/$videoAttachId.${ClipBoardHtmlParser.getFileFormatByName(fileInfo.fileName)}"
            val element = document.createElement(ClipBoardHtmlParser.TAG_VIDEO_NAME)
            val duration = getVideoDuration(uri)
            element.attr(ATTR_ATTACH_ID, attachId)
            element.attr(ClipBoardImageParseHandler.ATTR_IMG_WIDTH, "${it.width}")
            element.attr(ClipBoardImageParseHandler.ATTR_IMG_HEIGHT, "${it.height}")
            element.attr(ATTR_SRC, src)
            element.attr(ATTR_VIDEO_SRC, videoSrc)
            element.attr(ATTR_VIDEO_ATTACH_ID, videoAttachId)
            element.attr(ATTR_VIDEO_FILE_NAME, fileInfo.fileName)
            element.attr(ATTR_VIDEO_FILE_SIZE, "${fileInfo.fileSize}")
            val createTime = System.currentTimeMillis()
            element.attr(
                ATTR_ADD_NOTE_TIME,
                DateAndTimeUtils.timeInMillis2Date(appContext, createTime, true)
            )
            element.attr(ATTR_CREATE_TIME, "$createTime")
            element.attr(ATTR_VIDEO_DURATION, "$duration")
            return Pair(
                element, ClipBoardPastVideInfo(
                    attachId, src, videoSrc
                )
            )
        } ?: let {
            AppLogger.BASIC.d(TAG, "getVideo ImageSize null")
            return null
        }
    }

    override suspend fun getImageSize(uri: Uri): Size? {
        return getFirstFrameInfo(uri)
    }

    /**
     * 获取视频第一帧图像宽高 若获取失败 就先使用默认图的宽高
     * 然后拷贝文件在进行调整
     */
    private suspend fun getFirstFrameInfo(uri: Uri): Size? {
        val retriever = MediaMetadataRetriever()
        runCatching {
            retriever.setDataSource(appContext, uri)
            //获取原始宽
            val widthStr =
                retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)
            //获取原始高
            val heightStr =
                retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)
            //获取旋转角度
            val rotationStr =
                retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
            if (!widthStr.isNullOrEmpty() && !heightStr.isNullOrEmpty()) {
                var width = widthStr.toIntOrNull()
                var height = heightStr.toIntOrNull()
                val rotation = rotationStr?.let {
                    it.toIntOrNull()
                }
                if (rotation != null && (rotation == ROTATION_90 || rotation == ROTATION_270)) {
                    //需要进行宽高交换
                    val temp = width
                    width = height
                    height = temp
                }
                if (width != null && height != null) {
                    return Size(width, height)
                }
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getVideoInfo error: ${it.message}")
        }.also {
            retriever.release()
        }
        val frameBitMap = getVideoFrame(uri)
        if (frameBitMap != null) {
            return Size(frameBitMap.width, frameBitMap.height)
        }
        return null
    }

    private fun getVideoDuration(uri: Uri): Long {
        val retriever = MediaMetadataRetriever()
        runCatching {
            retriever.setDataSource(appContext, uri)
            //获取duration
            val durationStr =
                retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            return durationStr?.toLongOrNull() ?: 0
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getVideoInfo error: ${it.message}")
        }.also {
            retriever.release()
        }
        return 0
    }

    private suspend fun getVideoFrame(
        videoUri: Uri,
    ): Bitmap? = withContext(IO) {
        val retriever = MediaMetadataRetriever()
        val start = System.currentTimeMillis()
        return@withContext kotlin.runCatching {
            // 设置数据源
            retriever.setDataSource(appContext, videoUri)
            // 获取首帧图像
            val frame = retriever.getFrameAtTime(0, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
            frame
        }.onFailure {
            AppLogger.BASIC.d(TAG, "getVideoFrame error:${it.message}")
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "getVideoFrame success:${System.currentTimeMillis() - start}")
        }.also {
            retriever.release()
        }.getOrDefault(
            BitmapFactory.decodeResource(
                appContext.resources,
                R.drawable.video_placeholder
            )
        )
    }


    override suspend fun getAttachOperateResult(pastNodeInfo: ClipBoardPastNodeInfo): JSONObject {
        val result = handleAttachOperate(pastNodeInfo)
        val videoInfo = pastNodeInfo.data as ClipBoardPastVideInfo
        val jsonResult = getCommonJsonResult(result, videoInfo.attachId)
        if (STATUS_FAIL == result) {
            checkDeleteFile(videoInfo.src)
            checkDeleteFile(videoInfo.videoSrc)
        }
        return jsonResult
    }

    override suspend fun handleAttachOperate(pastNodeInfo: ClipBoardPastNodeInfo): Int =
        withContext(IO) {
            val bitmap = getVideoFrame(pastNodeInfo.uri)
            if (bitmap == null || bitmap.byteCount > NoteEditImageView.MAX_BITMAP_SIZE) {
                return@withContext STATUS_FAIL
            }
            val videoInfo = pastNodeInfo.data as ClipBoardPastVideInfo
            val startTime = System.currentTimeMillis()
            var pngResult = false
            val pngJob = async {
                pngResult = saveCompressBmpToFile(bitmap, "${getAttachRootPath()}${videoInfo.src}")
            }
            var videoResult = false
            val videoJob = async {
                videoResult = copyAttachFromUri(pastNodeInfo.uri, "${getAttachRootPath()}${videoInfo.videoSrc}")
            }
            pngJob.await()
            videoJob.await()
            AppLogger.BASIC.e(
                TAG,
                "handleAttachOperate coastTime=${System.currentTimeMillis() - startTime} pngResult=:$pngResult videoResult=$videoResult"
            )
            if (pngResult && videoResult) {
                return@withContext STATUS_SUCCESS
            }
            return@withContext STATUS_FAIL
        }
}