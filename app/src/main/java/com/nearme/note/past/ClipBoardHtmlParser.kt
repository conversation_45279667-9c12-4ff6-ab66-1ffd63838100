/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardHtmlParser.kt
 * Description    : ClipBoardHtmlParser.kt
 * Version        : 1.0
 * Date           : 2025/06/09
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/10         1.0           create
 */
package com.nearme.note.past

import android.content.ClipData
import android.net.Uri
import androidx.annotation.Keep
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.past.data.CLipBoardPastBaseInfo
import com.nearme.note.past.parse.ClipBoardAudioParseHandler
import com.nearme.note.past.parse.ClipBoardFileParseHandler
import com.nearme.note.past.parse.ClipBoardImageParseHandler
import com.nearme.note.past.parse.ClipBoardVideoParseHandler
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.MediaFileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element

@Keep
data class ClipBoardHtmlInfo(
    //html信息
    var htmlText: String?,
    var attachNodeList: MutableList<ClipBoardPastNodeInfo> = mutableListOf(),
    //不支持的类型
    var unSupportTypeList: MutableList<String> = mutableListOf(),
    //文字Item数量
    var textItemCount: Int = 0,
    //超出最大限定
    var limitPast: Boolean = false,
    //总共Item数量
    var totalValidCount: Int = 0,
    //是否忽略attach
    var ignoreAttach: Boolean = false,
    //超过最大限定
    var maxLimitAttachCount: Int = 0,
)


@Keep
data class ClipBoardPastNodeInfo(
    val attachId: String,
    val uri: Uri,
    val nodeType: String,
    val mediaFileInfo: MediaFileInfo,
    val data: Any
)

object ClipBoardHtmlParser {
    //文本标签
    private const val TAG = "ClipBoardHtmlParser"
    private const val TAG_TEXT_NAME = "p"
    const val ATTR_SRC = "src"
    const val ATTR_ATTACH_ID = "attachid"
    private const val ATTR_PAST_STATUS = "pastStatus"
    const val TAG_IMG_NAME = "img"
    const val TAG_VIDEO_NAME = "videocard"
    const val TAG_AUDIO_NAME = "record"
    const val TAG_FILE_NAME = "filecard"

    private const val PAST_STATUS = "0"

    private val imageParseHandler by lazy {
        ClipBoardImageParseHandler()
    }

    private val videoParseHandler by lazy {
        ClipBoardVideoParseHandler()
    }

    private val audioParseHandler by lazy {
        ClipBoardAudioParseHandler()
    }

    private val fileParseHandler by lazy {
        ClipBoardFileParseHandler()
    }

    /**
     * 需要在IO线程中
     */
    fun handleClipBoardHtml(
        clipData: ClipData,
        richNoteId: String?,
        fragment: Fragment,
        maxCount: Int,
        selectionInTable: Boolean = false,
        callback: (info: ClipBoardHtmlInfo) -> Unit
    ) {
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            callback.invoke(getClipBoardHtml(clipData, richNoteId, maxCount, selectionInTable))
        }
    }

    /**
     * 将剪切板中的数据
     */
    @Suppress("LongMethod", "ComplexMethod")
    private suspend fun getClipBoardHtml(
        clipData: ClipData,
        richNoteId: String?,
        maxCount: Int,
        selectionInTable: Boolean = false,
    ): ClipBoardHtmlInfo {
        var totalValidCount = 0
        var maxLimitAttachCount = 0
        var limitPast = false
        val unSupportTypeList: MutableList<String> = mutableListOf()
        var textItemCount = 0
        val attachNodeList: MutableList<ClipBoardPastNodeInfo> = mutableListOf()
        val clipBoardHtmlInfo =
            ClipBoardHtmlInfo("", attachNodeList, unSupportTypeList)
        if (richNoteId.isNullOrEmpty()) {
            return clipBoardHtmlInfo
        }
        val document = Jsoup.parse("")
        document.outputSettings(Document.OutputSettings().prettyPrint(false))
        var addAttachCount = 0
        for (i in 0 until clipData.itemCount) {
            val item = clipData.getItemAt(i) ?: continue
            AppLogger.BASIC.d(TAG, "getClipBoardHtml: uri=u${item.uri}")
            if (item.uri == null) {
                //文本信息
                if (item.text != null) {
                    totalValidCount++
                    textItemCount++
                    document.body().appendElement(TAG_TEXT_NAME).text(item.text.toString())
                }
            } else if (selectionInTable) {
                AppLogger.BASIC.d(TAG, "getClipBoardHtml: selectionInTable need ignoreAttach")
                clipBoardHtmlInfo.ignoreAttach = true
            } else {
                //文件信息
                totalValidCount++
                val mediaInfo = MediaFileUtils.queryMediaFileInfo(appContext, item.uri)
                if (mediaInfo == null || mediaInfo.fileSize > MediaFileUtils.MAX_FILE_SIZE) {
                    if (mediaInfo == null) {
                        AppLogger.BASIC.d(TAG, "getClipBoardHtml:file type is not exit")
                    } else {
                        AppLogger.BASIC.d(TAG, "getClipBoardHtml:file size is large")
                        maxLimitAttachCount++
                    }
                    continue
                }
                AppLogger.BASIC.d(TAG, "getClipBoardHtml: mimeType=${mediaInfo.mimeType}")
                if (!mediaInfo.isSupport() || maxCount <= 0 || addAttachCount >= maxCount) {
                    if (!mediaInfo.isSupport()) {
                        AppLogger.BASIC.d(TAG, "getClipBoardHtml: has unsupportedType")
                        unSupportTypeList.add(mediaInfo.mimeType)
                    }
                    if (maxCount <= 0 || addAttachCount >= maxCount) {
                        AppLogger.BASIC.d(
                            TAG,
                            "getClipBoardHtml: limitPast maxCount=$maxCount addAttachCount=$addAttachCount"
                        )
                        limitPast = true
                    }
                } else {
                    addAttachCount++
                    when {
                        mediaInfo.isImage() -> {
                            imageParseHandler.createHtmlElement(
                                richNoteId, mediaInfo, document, item.uri
                            )?.let {
                                addAttachElement(document, it.first)
                                addAttachInfo(
                                    attachNodeList, mediaInfo, item.uri, TAG_IMG_NAME, it.second
                                )
                            }
                        }

                        mediaInfo.isVideo() -> {
                            videoParseHandler.createHtmlElement(
                                richNoteId, mediaInfo, document, item.uri
                            )?.let {
                                addAttachElement(document, it.first)
                                addAttachInfo(
                                    attachNodeList, mediaInfo, item.uri, TAG_VIDEO_NAME, it.second
                                )
                            }
                        }

                        mediaInfo.isAudio() -> {
                            audioParseHandler.createHtmlElement(
                                richNoteId, mediaInfo, document, item.uri
                            )?.let {
                                addAttachElement(document, it.first)
                                addAttachInfo(
                                    attachNodeList, mediaInfo, item.uri, TAG_AUDIO_NAME, it.second
                                )
                            }
                        }

                        mediaInfo.isDocument() -> {
                            fileParseHandler.createHtmlElement(
                                richNoteId, mediaInfo, document, item.uri
                            )?.let {
                                addAttachElement(document, it.first)
                                addAttachInfo(
                                    attachNodeList, mediaInfo, item.uri, TAG_FILE_NAME, it.second
                                )
                            }
                        }
                    }
                }
            }
        }
        clipBoardHtmlInfo.htmlText = document.body().html()
        clipBoardHtmlInfo.limitPast = limitPast
        clipBoardHtmlInfo.totalValidCount = totalValidCount
        clipBoardHtmlInfo.textItemCount = textItemCount
        clipBoardHtmlInfo.maxLimitAttachCount = maxLimitAttachCount
        return clipBoardHtmlInfo
    }

    private fun addAttachInfo(
        attachNodeList: MutableList<ClipBoardPastNodeInfo>,
        mediaInfo: MediaFileInfo,
        uri: Uri,
        tagName: String,
        recordInfo: CLipBoardPastBaseInfo
    ) {
        attachNodeList.add(
            ClipBoardPastNodeInfo(
                recordInfo.attachId, uri, tagName, mediaInfo, recordInfo
            )
        )
    }

    private fun addAttachElement(document: Document, element: Element) {
        element.attr(ATTR_PAST_STATUS, PAST_STATUS)
        document.body().appendChild(element)
    }

    fun getFileFormatByName(fileName: String): String {
        if (fileName.isEmpty()) {
            return ""
        }
        val regex = Regex("\\.(\\w+)\$")
        val matchResult = fileName.let { regex.find(it) }
        return matchResult?.groupValues?.getOrNull(1) ?: ""
    }
}