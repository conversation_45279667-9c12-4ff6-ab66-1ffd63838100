/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardImageParseHandler.kt
 * Description    : ClipBoardImageParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/11         1.0           create
 */
package com.nearme.note.past.parse

import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Size
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.past.ClipBoardHtmlParser
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_ATTACH_ID
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_SRC
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.ClipBoardPastImageInfo
import com.nearme.note.util.FileUtil
import com.nearme.note.view.NoteEditImageView
import com.nearme.note.view.helper.UiHelper
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import java.io.BufferedInputStream
import java.util.UUID
import kotlin.math.ceil

class ClipBoardImageParseHandler : ClipBoardBaseParseHandler<ClipBoardPastImageInfo>() {
    companion object {
        private const val TAG = "ClipBoardImageElementHandler"
        const val ATTR_IMG_WIDTH = "picwidth"
        const val ATTR_IMG_HEIGHT = "picheight"
        const val ATTR_SRC_EXIT = "srcexist"
        private const val ATTR_PLACEHOLDER_SRC = "placeholdersrc"
        private const val FILE_HEAD_SIZE = 1024
    }

    override suspend fun createHtmlElement(
        richNoteId: String,
        fileInfo: MediaFileInfo,
        document: Document,
        uri: Uri
    ): Pair<Element, ClipBoardPastImageInfo>? {
        getImageSize(uri)?.let {
            val attachId = UUID.randomUUID().toString()
            val src = "/$richNoteId/$attachId${ThumbFileConstants.THUMB}"
            val placeHolderSrc = "/$richNoteId/$attachId${ThumbFileConstants.PLACEHOLDER}"
            val element = document.createElement(ClipBoardHtmlParser.TAG_IMG_NAME)
            element.attr(ATTR_ATTACH_ID, attachId)
            element.attr(ATTR_IMG_WIDTH, "${it.width}")
            element.attr(ATTR_IMG_HEIGHT, "${it.height}")
            element.attr(ATTR_SRC_EXIT, "false")
            element.attr(ATTR_SRC, src)
            element.attr(ATTR_PLACEHOLDER_SRC, placeHolderSrc)
            val imageInfo =
                ClipBoardPastImageInfo(attachId, src, placeHolderSrc, it.width, it.height)
            return Pair(element, imageInfo)
        } ?: let {
            AppLogger.BASIC.d(TAG, "getImageSize null")
            return null
        }
    }

    /**
     * 通过inJustDecodeBounds优先获取图片宽高信息
     * 返回第一个为宽 第二个为高
     */
    override suspend fun getImageSize(uri: Uri): Size? {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        var screenWidth = UiHelper.getScreenWidth()
        if (screenWidth == 0) {
            screenWidth = UiHelper.getScreenWidthDirectly(appContext)
        }
        appContext.contentResolver.runCatching {
            openInputStream(uri).use { stream ->
                if (stream == null) return null
                // 转换为可标记的流以确保兼容性
                val bis = BufferedInputStream(stream)
                // 标记1MB位置（足够存放图片头部）
                bis.mark(FILE_HEAD_SIZE * FILE_HEAD_SIZE)
                // 第一次解析获取尺寸
                BitmapFactory.decodeStream(bis, null, options)
                // 重置流
                bis.reset()
                var width = options.outWidth
                var height = options.outHeight
                if (width > UiHelper.PIC_FILL_PARENT_WIDTH) {
                    val scale = screenWidth.toFloat() / width.toFloat()
                    if (scale >= 1) {
                        width = ceil(scale * width).toInt()
                        height = ceil(height * scale).toInt()
                    }
                }
                return Size(width, height)
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getImageInfo error: ${it.message}")
        }
        return null
    }

    override suspend fun getAttachOperateResult(pastNodeInfo: ClipBoardPastNodeInfo): JSONObject {
        val result = handleImageAttachOperate(pastNodeInfo)
        val bitMapSize = result.second
        val status = result.first
        val imageInfo = pastNodeInfo.data as ClipBoardPastImageInfo
        val jsonResult = getCommonJsonResult(status, imageInfo.attachId)
        if (STATUS_FAIL == status) {
            checkDeleteFile(imageInfo.placeHolderSrc)
            checkDeleteFile(imageInfo.src)
        } else {
            //表面成功
            bitMapSize?.let {
                if (it.width != imageInfo.picWidth || it.height != imageInfo.picHeight) {
                    val imageJson = JSONObject().apply {
                        put("picWidth", it.width)
                        put("picHeight", it.height)
                    }
                    jsonResult.put("img", imageJson)
                }
            }
        }
        return jsonResult
    }


    private suspend fun handleImageAttachOperate(
        pastNodeInfo: ClipBoardPastNodeInfo
    ): Pair<Int, Size?> = withContext(IO) {
        val bitmap = MediaUtils.getThumbBitmapFromUri(pastNodeInfo.uri)
        if (bitmap == null || bitmap.byteCount > NoteEditImageView.MAX_BITMAP_SIZE) {
            AppLogger.BASIC.e(TAG, "handleAttachOperate ")
            return@withContext Pair(STATUS_FAIL, null)
        }
        val imageInfo = pastNodeInfo.data as ClipBoardPastImageInfo
        val startTime = System.currentTimeMillis()
        var webpResult = false
        val webpJob = async(IO) {
            webpResult = saveCompressBmpToFile(bitmap, "${getAttachRootPath()}${imageInfo.placeHolderSrc}")
        }
        var pngResult = false
        val pngJob = async(IO) {
            pngResult = FileUtil.saveBmpToFile(bitmap, "${getAttachRootPath()}${imageInfo.src}")
        }
        webpJob.await()
        pngJob.await()
        AppLogger.BASIC.e(
            TAG,
            "handleAttachOperate coastTime=${System.currentTimeMillis() - startTime} webpResult=:$webpResult pngResult=$pngResult"
        )
        if (pngResult || webpResult) {
            return@withContext Pair(STATUS_SUCCESS, Size(bitmap.width, bitmap.height))
        }
        return@withContext Pair(STATUS_FAIL, null)
    }
}