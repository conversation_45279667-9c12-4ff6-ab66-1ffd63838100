/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardNotePastManager.kt
 * Description    : ClipBoardNotePastManager.kt
 * Version        : 1.0
 * Date           : 2025/06/04
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/04         1.0           create
 */
package com.nearme.note.past

import android.content.ClipData
import androidx.lifecycle.lifecycleScope
import co.touchlab.stately.collections.ConcurrentMutableList
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.entity.getCardCount
import com.nearme.note.activity.richedit.entity.getPicCount
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.past.data.ClipBoardPastInfoResult
import com.nearme.note.past.parse.ClipBoardAudioParseHandler
import com.nearme.note.past.parse.ClipBoardFileParseHandler
import com.nearme.note.past.parse.ClipBoardImageParseHandler
import com.nearme.note.past.parse.ClipBoardVideoParseHandler
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.toast
import com.oplus.notes.webview.data.clipboard.ClipboardData
import com.oplus.notes.webview.data.clipboard.PasteResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File

class ClipBoardNotePastManager(private val fragment: WVNoteViewEditFragment) {
    companion object {
        private const val TAG = "ClipBoardNotePastManager"

        /**
         * 拷贝成功状态
         */
        private const val COPY_STATUS_SUCCESS = 1

        /**
         * 拷贝失败状态
         */
        private const val COPY_STATUS_FAIL = 2
        private const val CACHE_DIRECTORY = "clipboard"
        private const val MAX_SEMAPHORE_VALUE = 5
        /**
         * 主动调用note保存的时长
         */
        private const val NOTE_SAVE_DELAY_TIME = 200L
        private const val PAST_TYPE_URL = 2
        private const val PAST_STATUS = "status"
    }

    /**
     * 当前所有需要处理的拷贝信息
     */
    private var totalPastNodeList: ConcurrentMutableList<PastNodeInfo> = ConcurrentMutableList()

    /**
     * 当前拷贝的粘贴的附件节点数据
     */
    private var currentPastNodeList: MutableList<PastNodeInfo> = mutableListOf()

    /**
     * 记录当前已完成的数据  因最后是通过richData的picCount这些进行的计算 已经抛开了 失败的信息
     * 这里采用list存储来进行最后的统计不使用迭代器 因为是多线程同时进行拷贝
     */
    private var finishPastNodeList: ConcurrentMutableList<PastNodeInfo> = ConcurrentMutableList()

    /**
     * 当前都为uri信息的剪切板数据
     */
    private var totalPastClipBoardNodeList: ConcurrentMutableList<ClipBoardPastNodeInfo> =
        ConcurrentMutableList()

    /**
     * 记录当前已经完成的数据 和添加时候 需要进行清除数据
     */
    private var finishPastClipBoardNodeList: ConcurrentMutableList<ClipBoardPastNodeInfo> = ConcurrentMutableList()

    /**
     * 当前的节点数据信息
     */
    private var currentPastClipBoardNodeList: MutableList<ClipBoardPastNodeInfo> = mutableListOf()

    /**
     * 当前执行拷贝的job信息
     */
    private var currentJob: Job? = null

    /**
     * 当前针对于Uri的job信息
     */
    private var currentUriJob: Job? = null

    /**
     * 当前存储的job信息
     */
    private var saveNoteJob: Job? = null

    /**
     * 针对于image的解析处理
     */
    private val imageParseHandler by lazy {
        ClipBoardImageParseHandler()
    }

    /**
     * 针对于视频的解析处理
     */
    private val videoParseHandler by lazy {
        ClipBoardVideoParseHandler()
    }

    /**
     * 针对于音频的解析处理
     */
    private val audioParseHandler by lazy {
        ClipBoardAudioParseHandler()
    }

    /**
     * 针对于文件的解析处理
     */
    private val fileParseHandler by lazy {
        ClipBoardFileParseHandler()
    }

    /**
     * 需要对数据进行解析处理 并且当为空的时候 不进行任何操作
     */
    fun getPastHtmlResultInfo(
        htmlText: String?,
        selectionInTable: Boolean
    ): PasteResult {
        val richData = fragment.mViewModel.mRichData
        val richNoteId = richData?.getNoteGuid()
        if (htmlText.isNullOrEmpty() || richNoteId.isNullOrEmpty()) {
            AppLogger.BASIC.d(
                TAG, "getPastHtmlResultInfo htmlText is empty or richNoteId is empty"
            )
            return PasteResult(false, ClipboardData(htmlText = htmlText), false)
        }
        val maxCount = getMaxCount(richData)
        val pastHtmlInfo =
            HtmlPasteParser.handlePastHtml(htmlText, richNoteId, maxCount, selectionInTable)
        val attachNodeList = pastHtmlInfo.attachNodeList
        val pasted = pastHtmlInfo.htmlText.isNullOrEmpty()
        if (selectionInTable && pastHtmlInfo.ignoreAttach) {
            showUnSupportDataInTable()
        } else if (pastHtmlInfo.limitPast) {
            //超出最大限定
            appContext.toast(R.string.toast_excceed_limit_of_attrs)
        }
        AppLogger.BASIC.d(
            TAG, "getPastHtmlResultInfo attachNodeListSize=${attachNodeList.size}"
        )
        if (attachNodeList.isEmpty()) {
            return PasteResult(pasted, ClipboardData(htmlText = pastHtmlInfo.htmlText), false)
        }
        currentPastNodeList.clear()
        currentPastNodeList.addAll(attachNodeList)
        return PasteResult(pasted, ClipboardData(htmlText = pastHtmlInfo.htmlText), true)
    }

    private fun showUnSupportDataInTable() {
        appContext.toast(com.oplus.note.baseres.R.string.table_limit_input_type)
    }

    @Suppress("LongMethod", "ComplexMethod")
    fun handleCopyAttach(
        pastType: Int,
        pastOk: Boolean,
        callBack: (resultJson: JSONObject) -> Unit
    ) {
        AppLogger.BASIC.d(TAG, "handleCopyAttach pastType=$pastType pastOk=$pastOk")
        if (!pastOk) {
            //当前为粘贴失败 将现有需要加入到拷贝中的文件去掉
            if (pastType == PAST_TYPE_URL) {
                currentPastClipBoardNodeList.clear()
            } else {
                currentPastNodeList.clear()
            }
            return
        }
        stopJob()
        if (pastType == PAST_TYPE_URL) {
            //清除所有已经拷贝了的文件
            if (finishPastClipBoardNodeList.isNotEmpty()) {
                AppLogger.BASIC.d(
                    TAG,
                    "handleCopyAttach Clear finishPastClipBoardNodeList size=${finishPastClipBoardNodeList.size}"
                )
                totalPastClipBoardNodeList.removeAll(finishPastClipBoardNodeList)
                finishPastClipBoardNodeList.clear()
            }
            if (currentPastClipBoardNodeList.isNotEmpty()) {
                AppLogger.BASIC.d(
                    TAG,
                    "handleCopyAttach currentPastClipBoardNodeList=${currentPastClipBoardNodeList.size}"
                )
                totalPastClipBoardNodeList.addAll(currentPastClipBoardNodeList)
                currentPastClipBoardNodeList.clear()
            }
        } else {
            //清除已经完成了的拷贝的文件
            if (finishPastNodeList.isNotEmpty()) {
                AppLogger.BASIC.d(
                    TAG, "handleCopyAttach Clear finishPastNodeList size=${finishPastNodeList.size}"
                )
                totalPastNodeList.removeAll(finishPastNodeList)
                finishPastNodeList.clear()
            }
            if (currentPastNodeList.isNotEmpty()) {
                AppLogger.BASIC.d(
                    TAG,
                    "handleCopyAttach currentPastNodeList=${currentPastNodeList.size}"
                )
                totalPastNodeList.addAll(currentPastNodeList)
                currentPastNodeList.clear()
            }
        }
        if (totalPastNodeList.isEmpty() && totalPastClipBoardNodeList.isEmpty()) {
            AppLogger.BASIC.d(
                TAG,
                "handleCopyAttach pastDataSize is Empty totalPastNodeList.size=${totalPastNodeList.size}" +
                        "  totalPastClipBoardNodeList.size=${totalPastClipBoardNodeList.size}"
            )
            return
        }
        AppLogger.BASIC.d(TAG, "handleCopyAttach start")
        if (totalPastNodeList.isNotEmpty()) {
            handleCopyOperate(callBack)
        }
        if (totalPastClipBoardNodeList.isNotEmpty()) {
            handleCopyUriOperate(callBack)
        }
    }

    @Suppress("LongMethod", "ComplexMethod")
    private fun handleCopyOperate(callBack: (resultJson: JSONObject) -> Unit) {
        val semaphore = Semaphore(MAX_SEMAPHORE_VALUE)
        currentJob = fragment.lifecycleScope.launch(Dispatchers.IO) {
            totalPastNodeList.forEach { pastNode ->
                semaphore.acquire()
                launch {
                    //是否拷贝成功 否则需要将文件进行删除
                    var copyOK = true
                    pastNode.attachList.forEach {
                        var copyFileStr = ""
                        //先检测缓存目录是否存在
                        if (cacheDirectoryExit()) {
                            val cacheFileStr = "${getCacheRootPath()}${it.orgSrc}"
                            val cacheFile = File(cacheFileStr)
                            if (cacheFile.exists()) {
                                copyFileStr = cacheFileStr
                            }
                        }
                        //检测便签笔记的文件是否存在
                        if (copyFileStr.isEmpty()) {
                            val noteFileStr = "${getFileRootPath()}${it.orgSrc}"
                            val noteFile = File(noteFileStr)
                            if (noteFile.exists()) {
                                copyFileStr = noteFileStr
                            }
                        }
                        if (copyFileStr.isNotEmpty() && it.newSrc.isNotEmpty()) {
                            runBlocking {
                                kotlin.runCatching {
                                    File(copyFileStr).copyTo(
                                        File("${getFileRootPath()}${it.newSrc}"), true
                                    ).absolutePath
                                }.onFailure { error ->
                                    AppLogger.BASIC.d(TAG, "copy error:${error.message}")
                                    copyOK = false
                                }
                            }
                        } else {
                            //当文件不存在的时候
                            AppLogger.BASIC.d(TAG, "fileSrc is empty copyFileStr=$copyFileStr newSrc=${it.newSrc}")
                            copyOK = false
                        }
                    }
                    if (!copyOK) {
                        //进行删除文件
                        AppLogger.BASIC.d(TAG, "copy file fail:${pastNode.attachId}")
                        pastNode.attachList.forEach {
                            if (it.newSrc.isNotEmpty()) {
                                val file = File("${getFileRootPath()}${it.newSrc}")
                                if (file.exists()) {
                                    file.delete()
                                }
                            }
                        }
                    }
                    runBlocking {
                        withContext(Dispatchers.Main) {
                            AppLogger.BASIC.d(TAG, "copy past AttachOk:${pastNode.attachId}")
                            callBack.invoke(JSONObject().apply {
                                put("attachId", pastNode.attachId)
                                put(
                                    PAST_STATUS, if (checkCopyOk(
                                            copyOK, pastNode.nodeType
                                        )
                                    ) COPY_STATUS_SUCCESS else COPY_STATUS_FAIL
                                )
                            })
                        }
                        finishPastNodeList.add(pastNode)
                    }
                    semaphore.release()
                    if (finishPastNodeList.isNotEmpty() && finishPastNodeList.size >= totalPastNodeList.size) {
                        //当拷贝执行处理完成后  需要执行清空处理
                        AppLogger.BASIC.d(
                            TAG,
                            "handleCopyOperate all finished finishPastNodeList.Size=${finishPastNodeList.size} " +
                                    "totalPastNodeList.size=${totalPastNodeList.size}"
                        )
                        clearPastNodeData()
                        //操作完成后 调用保存进行保存
                        saveNoteOperate()
                    }
                }
            }
        }
    }

    /**
     * 判断是否拷贝成功 如果日程和联系人 就算文件失败 也成功
     */
    private fun checkCopyOk(copyOK: Boolean, nodeType: String): Boolean {
        return copyOK || nodeType == HtmlPasteParser.TAG_SCHEDULE_CARD_NAME || nodeType == HtmlPasteParser.TAG_CONTACT_CARD_NAME
    }

    private fun getFileRootPath(): String {
        return appContext.filesDir.absolutePath
    }

    private fun clearCurrentPastData() {
        clearPastNodeData()
        clearPastClipBoardData()
    }

    /**
     * 清除粘贴数据
     */
    private fun clearPastClipBoardData() {
        totalPastClipBoardNodeList.clear()
        finishPastClipBoardNodeList.clear()
    }

    private fun clearPastNodeData() {
        totalPastNodeList.clear()
        finishPastNodeList.clear()
    }

    private fun stopJob() {
        //停止拷贝文件
        currentJob?.cancel()
        currentJob = null
        currentUriJob?.cancel()
        currentUriJob = null
    }

    /**
     * 进行释放信息
     */
    fun release() {
        stopJob()
        saveNoteJob?.cancel()
        saveNoteJob = null
        clearCurrentPastData()
        currentPastNodeList.clear()
        currentPastClipBoardNodeList.clear()
    }


    private fun getCacheRootPath(): String {
        return "${appContext.cacheDir}/$CACHE_DIRECTORY"
    }

    private fun cacheDirectoryExit(): Boolean {
        val file = File(getCacheRootPath())
        return file.exists()
    }

    /**
     * 获取当前正在粘贴中的数据的量
     */
    private fun getClipDataNumber(): Int {
        var pastNumber = 0
        if (totalPastNodeList.size > finishPastNodeList.size) {
            pastNumber = totalPastNodeList.size - finishPastNodeList.size
        }
        var pastBoardNumber = 0
        if (totalPastClipBoardNodeList.size > finishPastClipBoardNodeList.size) {
            pastBoardNumber = totalPastClipBoardNodeList.size - finishPastClipBoardNodeList.size
        }
        return pastNumber + pastBoardNumber
    }

    /**
     * 获取最大能够存储的附件数量
     */
    private fun getMaxCount(richData: RichData): Int {
        var maxCount =
            RichData.PICS_UPPER_BOUND - richData.getPicCount() - richData.getCardCount() - getClipDataNumber()
        if (maxCount < 0) {
            maxCount = 0
        }
        return maxCount
    }

    /**
     * 需要对剪切板中的数据进行处理 需要在IO线程中处理 需要通过callback来进行返回
     */
    fun getPastUriHtmlResultInfo(
        clipData: ClipData?,
        selectionInTable: Boolean = false,
        callback: (result: ClipBoardPastInfoResult) -> Unit
    ) {
        val richData = fragment.mViewModel.mRichData
        val richNoteId = richData?.getNoteGuid()
        if (clipData == null || richNoteId.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "getPastUriHtmlResultInfo clipData is Empty or richNoteId is empty")
            callback.invoke(ClipBoardPastInfoResult(PasteResult(true, null, false)))
        } else {
            val maxCount = getMaxCount(richData)
            ClipBoardHtmlParser.handleClipBoardHtml(
                clipData, richNoteId, fragment, maxCount, selectionInTable
            ) {
                runBlocking {
                    withContext(Dispatchers.Main) {
                        if (it.attachNodeList.isNotEmpty() && !selectionInTable) {
                            AppLogger.BASIC.d(TAG, "getPastUriHtmlResultInfo add attachNodeSize=${it.attachNodeList.size}")
                            currentPastClipBoardNodeList.clear()
                            currentPastClipBoardNodeList.addAll(it.attachNodeList)
                        }
                        if (selectionInTable && it.ignoreAttach) {
                            showUnSupportDataInTable()
                        }
                        val needCopyAttach = it.attachNodeList.isNotEmpty() && !selectionInTable
                        val pasted = it.htmlText.isNullOrEmpty()
                        callback.invoke(
                            ClipBoardPastInfoResult(
                                PasteResult(
                                    pasted,
                                    ClipboardData(htmlText = it.htmlText),
                                    needCopyAttach,
                                    pastType = PAST_TYPE_URL
                                ),
                                unSupportTypeList = it.unSupportTypeList,
                                textItemCount = it.textItemCount,
                                limitPast = it.limitPast,
                                totalValidCount = it.totalValidCount,
                                selectionInTable = selectionInTable,
                                maxLimitAttachCount = it.maxLimitAttachCount
                            )
                        )
                    }
                }
            }
        }
    }

    /**
     * 针对于Uri的处理
     */
    @Suppress("LongMethod", "ComplexMethod")
    private fun handleCopyUriOperate(
        callBack: (resultJson: JSONObject) -> Unit
    ) {
        val semaphore = Semaphore(MAX_SEMAPHORE_VALUE)
        currentUriJob = fragment.lifecycleScope.launch(Dispatchers.IO) {
            totalPastClipBoardNodeList.forEach { pastNode ->
                semaphore.acquire()
                launch {
                    val result = when (pastNode.nodeType) {
                        ClipBoardHtmlParser.TAG_IMG_NAME -> {
                            //针对于图片处理
                            imageParseHandler.getAttachOperateResult(pastNode)
                        }

                        ClipBoardHtmlParser.TAG_VIDEO_NAME -> {
                            //针对于视频的处理
                            videoParseHandler.getAttachOperateResult(pastNode)
                        }

                        ClipBoardHtmlParser.TAG_AUDIO_NAME -> {
                            //针对于音频的处理
                            audioParseHandler.getAttachOperateResult(pastNode)
                        }

                        ClipBoardHtmlParser.TAG_FILE_NAME -> {
                            //针对于文件的处理
                            fileParseHandler.getAttachOperateResult(pastNode)
                        }

                        else -> null
                    }
                    runBlocking {
                        result?.let {
                            withContext(Dispatchers.Main) {
                                AppLogger.BASIC.d(
                                    TAG,
                                    "copy past Uri AttachOk:${pastNode.attachId}"
                                )
                                callBack.invoke(result)
                            }
                            if (pastNode.nodeType == ClipBoardHtmlParser.TAG_FILE_NAME && result.has(
                                    PAST_STATUS
                                ) && result.getInt(PAST_STATUS) == COPY_STATUS_SUCCESS
                            ) {
                               //表明当前为文件且是成功状态
                                fileParseHandler.handleFileThumbNailOperate(fragment, pastNode)
                            }
                        }
                        finishPastClipBoardNodeList.add(pastNode)
                    }
                    semaphore.release()
                    if (finishPastClipBoardNodeList.isNotEmpty() && finishPastClipBoardNodeList.size >= totalPastClipBoardNodeList.size) {
                        //执行完成后清除数据
                        AppLogger.BASIC.d(
                            TAG,
                            "handleCopyUriOperate all finished finishPastClipBoardNodeList.Size=${finishPastClipBoardNodeList.size} " +
                                    "totalPastClipBoardNodeList.size=${totalPastClipBoardNodeList.size}"
                        )
                        clearPastClipBoardData()
                        //操作完成后 调用保存进行保存
                        saveNoteOperate()
                    }
                }
            }
        }
    }

    /**
     * 主动进行保存
     */
    private fun saveNoteOperate() {
        //先进行取消之前的job
        saveNoteJob?.cancel()
        saveNoteJob = fragment.lifecycleScope.launch(Dispatchers.IO) {
            delay(NOTE_SAVE_DELAY_TIME)
            withContext(Dispatchers.Main) {
                fragment.saveNoteIfNeeded(TAG, true)
            }
        }
    }
}