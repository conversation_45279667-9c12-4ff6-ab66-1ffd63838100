/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : PastUtilHelper.kt
 * Description    : PastUtilHelper.kt
 * Version        : 1.0
 * Date           : 2025/06/04
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/04         1.0           create
 */
package com.nearme.note.past

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.PersistableBundle
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.richedit.webview.WVNoteViewEditFragment
import com.nearme.note.util.clipboardManager
import com.oplus.note.logger.AppLogger
import com.oplus.notes.webview.data.clipboard.ClipDataInfo
import com.oplus.notes.webview.data.clipboard.PasteResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption

object PastUtilHelper {
    private const val TAG = "PastUtilHelper"
    private const val CLIP_BOARD_KEY_FROM = " fromNote"
    private const val CLIP_BOARD_KEY_VALUE = "clipData"
    private const val CLIP_DES_LABEL = "note_html"
    private const val CACHE_DIRECTORY = "clipboard"
    @JvmStatic
    fun getCopyOrCutClipBoard(clipDataInfo: ClipDataInfo): ClipData? {
        //将信息内容填充到ClipBoard 防止三方处理第一个的时候判断问题 需要第一个有数据
        val plainText = clipDataInfo.plainText ?: ""
        var clipData: ClipData? = null
        val attachFileUrlList = clipDataInfo.attachFileList?.filter {
            it.src.isNotEmpty()
        }?.map {
            it.src
        } ?: mutableListOf()
        if (plainText.isEmpty() && attachFileUrlList.isNotEmpty()) {
            //全部是附件
            attachFileUrlList.forEach {
                it.toNoteUrl()?.apply {
                    if (clipData == null) {
                        clipData = ClipData.newUri(
                            appContext.contentResolver, CLIP_DES_LABEL, this
                        )
                    } else {
                        clipData?.addItem(ClipData.Item(this))
                    }
                }
            }
        } else {
            val extraHtmlText =
                HtmlPasteParser.getHtmlClipBoardExtraHtml(clipDataInfo.htmlText ?: "")
            clipData = ClipData.newHtmlText(CLIP_DES_LABEL, plainText, extraHtmlText)
            attachFileUrlList.forEach {
                it.toNoteUrl()?.apply {
                    clipData?.addItem(ClipData.Item(this))
                }
            }
        }
        //通过extra标识来源于内部note
        clipData?.let {
            val bundle = PersistableBundle()
            bundle.putBoolean(CLIP_BOARD_KEY_FROM, true)
            bundle.putString(CLIP_BOARD_KEY_VALUE, clipDataInfo.htmlText ?: "")
            it.description.extras = bundle
            clipboardManager.setPrimaryClip(it)
            clearDirectory()
        }
        return clipData
    }

    @JvmStatic
    private fun getClipboardManager(context: Context): ClipboardManager {
        return context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    }

    /**
     * 检测是否是通过内部来的复制粘贴
     */
    @JvmStatic
    fun isNoteClipboard(context: Context): Boolean {
        val manager = getClipboardManager(context)
        return manager.primaryClip?.description?.run {
            extras?.getBoolean(CLIP_BOARD_KEY_FROM, false) ?: false
        } ?: false
    }

    /**
     * 获取返回的结果信息
     */
    @JvmStatic
    fun getPastResult(
        context: Context,
        fragment: WVNoteViewEditFragment,
        selectionInTable: Boolean
    ): PasteResult {
        val manager = getClipboardManager(context)
        var htmlText: String? = null
        manager.primaryClip?.description?.run {
            if (extras.getBoolean(CLIP_BOARD_KEY_FROM, false)) {
                htmlText = extras.getString(CLIP_BOARD_KEY_VALUE, "")
            }
        }
        return fragment.mNotePastManager.getPastHtmlResultInfo(htmlText, selectionInTable)
    }

    /**
     * 需要对剪切板中的数据进行检测 如果是当前的需要移动到缓存中
     */
    @JvmStatic
    fun checkDeleteAttachFile(
        context: Context,
        fileList: MutableList<String>
    ): MutableList<String> {
        if (fileList.isEmpty()) {
            return fileList
        }
        val manager = getClipboardManager(context)
        var htmlText: String? = null
        manager.primaryClip?.description?.run {
            if (extras.getBoolean(CLIP_BOARD_KEY_FROM, false)) {
                htmlText = extras.getString(CLIP_BOARD_KEY_VALUE, "")
            }
        }
        if (htmlText == null) {
            return fileList
        }
        val attachList = HtmlPasteParser.getHtmlClipBoardAttachList(htmlText ?: "")
        val filterList = fileList.filter {
            !attachList.contains(it)
        } as MutableList
        val moveAttachList = fileList.filter {
            attachList.contains(it)
        } as MutableList
        moveFileToCache(moveAttachList)
        //这里需要对可以移动的文件进行移动
        return filterList
    }

    /**
     * 清除整个缓存目录
     */
    @JvmStatic
    private fun clearDirectory() {
        val file = File(getCacheRootPath())
        if (file.exists()) {
            file.deleteRecursively()
        }
    }

    @JvmStatic
    private fun getCacheRootPath(): String {
        return "${appContext.cacheDir}/$CACHE_DIRECTORY"
    }

    @JvmStatic
    private fun getFileRootPath(): String {
        return appContext.filesDir.absolutePath
    }

    /**
     * 移动文件到缓存目录
     */
    @JvmStatic
    private fun moveFileToCache(fileList: MutableList<String>) {
        CoroutineScope(Dispatchers.IO).launch {
            fileList.forEach {
                if (it.isNotEmpty() && it.startsWith(getFileRootPath())) {
                    //表明当前为需要移动的文件
                    val targetFile =
                        File("${getCacheRootPath()}${it.replace(getFileRootPath(), "")}")
                    val parentDir = targetFile.parentFile
                    if (parentDir != null && !parentDir.exists()) {
                        parentDir.mkdirs()
                    }
                    val moveFile = File(it)
                    try {
                        Files.move(
                            moveFile.toPath(),
                            targetFile.toPath(),
                            StandardCopyOption.ATOMIC_MOVE,
                            StandardCopyOption.REPLACE_EXISTING
                        )
                    } catch (_: Exception) {
                        AppLogger.BASIC.d(TAG, "move File error:${moveFile.absolutePath}")
                        if (moveFile.exists()) {
                            moveFile.delete()
                        }
                    }
                }
            }
        }
    }
}