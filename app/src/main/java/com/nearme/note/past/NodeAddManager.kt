/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : NodeAddManager.kt
 * Description    : NodeAddManager.kt
 * Version        : 1.0
 * Date           : 2025/06/10
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/10         1.0           create
 */
package com.nearme.note.past

import android.content.Context
import com.google.gson.Gson
import com.nearme.note.activity.richedit.RichDataClipboardManager
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.past.data.ContactNodeInfo
import com.nearme.note.past.data.ScheduleNodeInfo
import com.nearme.note.util.filesDirAbsolutePath
import com.oplus.note.data.CardContact
import com.oplus.note.data.CardSchedule
import com.oplus.note.data.CombinedCard
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.CommonExtra
import com.oplus.note.repo.note.entity.SubAttachment
import com.oplus.notes.webview.container.api.ImageInfo
import java.util.UUID

object NodeAddManager {
    private const val TAG = "NodeAddManager"
    private fun getCommonPicAttach(
        context: Context,
        noteId: String,
        imageAttr: ImageInfo
    ): Attachment {
        val width = imageAttr.width
        val height = imageAttr.height
        val rootPath = context.filesDirAbsolutePath()
        val imagePath = rootPath + imageAttr.src
        val pic = RichDataClipboardManager.getPicture(width, height, imagePath)
        val attachment = Attachment(
            imageAttr.attachId, noteId, Attachment.TYPE_PICTURE, picture = pic
        )
        return attachment
    }

    /**
     * 处理日程Node添加的时候
     */
    fun handleScheduleNodeAdd(
        context: Context?,
        richData: RichData?,
        scheduleInfo: ScheduleNodeInfo,
        deletedAttachmentList: MutableList<String>
    ) {
        if (context == null || richData?.getNoteGuid() == null) {
            return
        }
        val noteGuid = richData.getNoteGuid()
        val attachmentId = scheduleInfo.imageAttr.attachId
        val existData = richData.findAttachData(attachmentId, Data.TYPE_ATTACHMENT)
        if (existData != null) {
            AppLogger.BASIC.d(TAG, "onNodeAdded: webItems already exists video data")
            return
        }
        val picAttachment = getCommonPicAttach(context, noteGuid, scheduleInfo.imageAttr)
        val scheduleAttachment = Attachment(
            attachmentId = UUID.randomUUID().toString(),
            richNoteId = noteGuid,
            type = Attachment.TYPE_SPEECH_SCHEDULE,
            subAttachment = SubAttachment(associateAttachmentId = attachmentId),
        )
        handleScheduleCombinedCard(scheduleInfo, scheduleAttachment, richData)
        val data = Data(Data.TYPE_ATTACHMENT, attachment = picAttachment)
        richData.webItems.add(data)
        richData.subAttachments.add(scheduleAttachment)
        deletedAttachmentList.remove(picAttachment.absolutePath(context))
        deletedAttachmentList.remove(
            picAttachment.absolutePath(
                context, picSuffix = ThumbFileConstants.PLACEHOLDER
            )
        )
    }

    /**
     * 处理日程卡片
     */
    private fun handleScheduleCombinedCard(
        scheduleInfo: ScheduleNodeInfo,
        attachment: Attachment,
        richData: RichData
    ) {
        if (richData.speechLogInfo != null) {
            //数据存储到speechLogInfo中
            val combinedCard = if (richData.speechLogInfo!!.combinedCard != null) {
                Gson().fromJson(richData.speechLogInfo!!.combinedCard, CombinedCard::class.java)
            } else {
                CombinedCard(null, mutableListOf())
            }
            if (combinedCard.cardSchedules == null) {
                combinedCard.cardSchedules = mutableListOf()
            }
            val cardScheduleList = combinedCard.cardSchedules as MutableList
            cardScheduleList.add(
                CardSchedule(
                    attId = attachment.attachmentId,
                    event = scheduleInfo.scheduleName,
                    scheduleTime = scheduleInfo.scheduleTime,
                    location = scheduleInfo.scheduleAddress,
                    time = scheduleInfo.serverOrgTime,
                    associateId = attachment.subAttachment?.associateAttachmentId,
                    timeInitialed = scheduleInfo.timeInitialed,
                    nlpResult = null,
                    isLastOne = null
                )
            )
            //将数据进行重新存储
            richData.speechLogInfo?.combinedCard = Gson().toJson(combinedCard)
        } else {
            //当非AI语言摘要的时候 需要存储在cardData当中 当前已经添加了attachMent
            if (attachment.extra == null) {
                attachment.extra = CommonExtra()
            }
            attachment.extra?.cardData = Gson().toJson(
                CardSchedule(
                    attId = null,
                    event = scheduleInfo.scheduleName,
                    scheduleTime = scheduleInfo.scheduleTime,
                    location = scheduleInfo.scheduleAddress,
                    time = scheduleInfo.serverOrgTime,
                    associateId = null,
                    timeInitialed = scheduleInfo.timeInitialed,
                    nlpResult = null,
                    isLastOne = null
                )
            )
        }
    }

    /**
     * 处理联系人Node的添加
     */
    fun handleContactNodeAdd(
        context: Context?,
        richData: RichData?,
        contactInfo: ContactNodeInfo,
        deletedAttachmentList: MutableList<String>
    ) {
        if (context == null || richData?.getNoteGuid() == null) {
            return
        }
        val noteGuid = richData.getNoteGuid()
        val attachmentId = contactInfo.imageAttr.attachId
        val existData = richData.findAttachData(attachmentId, Data.TYPE_ATTACHMENT)
        if (existData != null) {
            AppLogger.BASIC.d(TAG, "onNodeAdded: webItems already exists video data")
            return
        }
        val picAttachment = getCommonPicAttach(context, noteGuid, contactInfo.imageAttr)
        val contactAttachment = Attachment(
            attachmentId = UUID.randomUUID().toString(),
            richNoteId = noteGuid,
            type = Attachment.TYPE_SPEECH_CONTACT,
            subAttachment = SubAttachment(associateAttachmentId = attachmentId),
        )
        handleContactCombinedCard(contactInfo, contactAttachment, richData)
        val data = Data(Data.TYPE_ATTACHMENT, attachment = picAttachment)
        richData.webItems.add(data)
        richData.subAttachments.add(contactAttachment)
        deletedAttachmentList.remove(picAttachment.absolutePath(context))
        deletedAttachmentList.remove(
            picAttachment.absolutePath(
                context, picSuffix = ThumbFileConstants.PLACEHOLDER
            )
        )
    }

    /**
     * 处理日程卡片
     */
    private fun handleContactCombinedCard(
        contactInfo: ContactNodeInfo,
        attachment: Attachment,
        richData: RichData
    ) {
        if (richData.speechLogInfo != null) {
            //数据存储到speechLogInfo中
            val combinedCard = if (richData.speechLogInfo!!.combinedCard != null) {
                Gson().fromJson(richData.speechLogInfo!!.combinedCard, CombinedCard::class.java)
            } else {
                CombinedCard(mutableListOf(), null)
            }
            if (combinedCard.cardContacts == null) {
                combinedCard.cardContacts = mutableListOf()
            }
            val cardContactList = combinedCard.cardContacts as MutableList
            cardContactList.add(
                CardContact(
                    attId = attachment.attachmentId,
                    name = contactInfo.contactName,
                    phone = contactInfo.contactPhone,
                    position = contactInfo.contactPosition,
                    company = contactInfo.contactCompany,
                    associateId = attachment.subAttachment?.associateAttachmentId,
                    isLastOne = null,
                )
            )
            //将数据进行重新存储
            richData.speechLogInfo?.combinedCard = Gson().toJson(combinedCard)
        } else {
            //当非AI语言摘要的时候 需要存储在cardData当中 当前已经添加了attachMent
            if (attachment.extra == null) {
                attachment.extra = CommonExtra()
            }
            attachment.extra?.cardData = Gson().toJson(
                CardContact(
                    attId = null,
                    name = contactInfo.contactName,
                    phone = contactInfo.contactPhone,
                    position = contactInfo.contactPosition,
                    company = contactInfo.contactCompany,
                    associateId = null,
                    isLastOne = null,
                )
            )
        }
    }
}