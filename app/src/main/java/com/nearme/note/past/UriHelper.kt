/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: UriHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2025/06/04
 * * Author: wuwenliang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.past

import android.net.Uri
import com.nearme.note.MyApplication.Companion.appContext
import com.oplus.note.NoteFileProvider

fun String.toNoteUrl(): <PERSON><PERSON>? {
    //当为空 或者不是以/开头的数据 都进行直接返回
    if (this.isEmpty()) {
        return null
    }
    if (this.startsWith("content://")) {
        return Uri.parse(this)
    }
    if (!this.startsWith("/")) {
        return null
    }
    val absolutePrefix = appContext.filesDir.absolutePath
    var realPath = this
    if (!this.startsWith(absolutePrefix)) {
        realPath = "$absolutePrefix$this"
    }
    return NoteFileProvider.getUriForCommonFile(appContext, realPath)
}