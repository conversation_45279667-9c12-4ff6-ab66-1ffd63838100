/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ClipBoardAudioParseHandler.kt
 * Description    : ClipBoardAudioParseHandler.kt
 * Version        : 1.0
 * Date           : 2025/06/11
 * Author         :
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * wenliang.wu     2025/06/13         1.0           create
 */
package com.nearme.note.past.parse

import android.net.Uri
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.NoteViewRichEditViewModel
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.past.ClipBoardHtmlParser
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_ATTACH_ID
import com.nearme.note.past.ClipBoardHtmlParser.ATTR_SRC
import com.nearme.note.past.ClipBoardPastNodeInfo
import com.nearme.note.past.data.ClipBoardPastAudioInfo
import com.nearme.note.past.parse.ClipBoardImageParseHandler.Companion.ATTR_IMG_HEIGHT
import com.nearme.note.past.parse.ClipBoardImageParseHandler.Companion.ATTR_IMG_WIDTH
import com.nearme.note.speech.utils.PresetNoteSpeechUtils
import com.oplus.note.audioplayer.AudioPlayerManager
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.DateAndTimeUtils
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.json.JSONObject
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import java.util.UUID

class ClipBoardAudioParseHandler : ClipBoardBaseParseHandler<ClipBoardPastAudioInfo>() {
    companion object {
        private const val TAG = "ClipBoardAudioParseHandler"
        private const val ATTR_CALL_LOG = "hascalllogs"
        private const val ATTR_HAS_MARK = "hasmarks"
        private const val ATTR_FILE_NAME = "filename"
        private const val ATTR_TITLE = "title"
        private const val ATTR_FILE_SIZE = "filesize"
        private const val ATTR_EXTERNAL = "isexternal"
        private const val ATTR_SPEECH_AUDIO = "isspeechaudio"
        private const val ATTR_ASR_AUDIO = "isasraudio"
        private const val ATTR_RECORD_ID = "recordid"
        private const val ATTR_FILE_DATE = "filedate"
        private const val ATTR_STATE = "state"
        private const val AUDIO_DURATION = "duration"
    }

    override suspend fun createHtmlElement(
        richNoteId: String,
        fileInfo: MediaFileInfo,
        document: Document,
        uri: Uri
    ): Pair<Element, ClipBoardPastAudioInfo>? {
        val attachId = UUID.randomUUID().toString()
        val src = "/$richNoteId/$attachId${ThumbFileConstants.THUMB}"
        val recordId = UUID.randomUUID().toString()
        val sizeInfo = getAssertImageSize(getAssertFileName())
        val audioSrc =
            "/$richNoteId/$recordId.${ClipBoardHtmlParser.getFileFormatByName(fileInfo.fileName)}"
        val element = document.createElement(ClipBoardHtmlParser.TAG_AUDIO_NAME)
        element.attr(ATTR_ATTACH_ID, attachId)
        element.attr(ATTR_IMG_WIDTH, "${sizeInfo?.width ?: 0}")
        element.attr(ATTR_IMG_HEIGHT, "${sizeInfo?.height ?: 0}")
        element.attr(ATTR_CALL_LOG, "false")
        element.attr(ATTR_HAS_MARK, "false")
        element.attr(ATTR_TITLE, "${fileInfo.fileName}")
        element.attr(ATTR_FILE_NAME, "${fileInfo.fileName}")
        element.attr(ATTR_FILE_SIZE, "${fileInfo.fileSize}")
        element.attr(ATTR_EXTERNAL, "true")
        element.attr(ATTR_SPEECH_AUDIO, "false")
        element.attr(ATTR_ASR_AUDIO, "false")
        element.attr(ATTR_RECORD_ID, recordId)
        element.attr(
            ATTR_FILE_DATE,
            DateAndTimeUtils.timeInMillis2Date(appContext, System.currentTimeMillis(), true)
        )
        element.attr(ATTR_STATE, "${NoteViewRichEditViewModel.TYPE_VOICE_NORMAL}")
        element.attr(ATTR_SRC, src)
        return Pair(element, ClipBoardPastAudioInfo(attachId, recordId, src, audioSrc, 0))
    }

    override suspend fun getAttachOperateResult(pastNodeInfo: ClipBoardPastNodeInfo): JSONObject {
        val result = handleAttachOperate(pastNodeInfo)
        val audioInfo = pastNodeInfo.data as ClipBoardPastAudioInfo
        val jsonResult = getCommonJsonResult(result, audioInfo.attachId)
        if (STATUS_FAIL == result) {
            checkDeleteFile(audioInfo.src)
            checkDeleteFile(audioInfo.audioSrc)
        } else {
            val audioJson = JSONObject().apply {
                put(AUDIO_DURATION, audioInfo.audioDuration)
            }
            jsonResult.put("audio", audioJson)
        }
        return jsonResult
    }

    private fun getAssertFileName(): String {
        return PresetNoteSpeechUtils.PRESET_NOTE_SPEECH_VOICE_ATTACHMENT
    }

    override suspend fun handleAttachOperate(pastNodeInfo: ClipBoardPastNodeInfo): Int =
        withContext(IO) {
            val bitmap =
                MediaUtils.getThumbBitmapFromInputStream(appContext.assets.open(getAssertFileName()))
                    ?: return@withContext STATUS_FAIL
            val audioInfo = pastNodeInfo.data as ClipBoardPastAudioInfo
            val startTime = System.currentTimeMillis()
            var pngResult = false
            val pngJob = async {
                pngResult = saveCompressBmpToFile(bitmap, "${getAttachRootPath()}${audioInfo.src}")
            }
            var audioResult = false
            val audioJob = async {
                audioResult = copyAttachFromUri(
                    pastNodeInfo.uri,
                    "${getAttachRootPath()}${audioInfo.audioSrc}"
                )
            }
            pngJob.await()
            audioJob.await()
            AppLogger.BASIC.e(
                TAG,
                "handleAttachOperate coastTime=${System.currentTimeMillis() - startTime}  pngResult=:$pngResult audioResult=$audioResult"
            )
            if (pngResult && audioResult) {
                audioInfo.audioDuration = AudioPlayerManager.loadAudioFileDuration(
                    appContext, "${getAttachRootPath()}${audioInfo.audioSrc}"
                )
                return@withContext STATUS_SUCCESS
            }
            return@withContext STATUS_FAIL
        }
}