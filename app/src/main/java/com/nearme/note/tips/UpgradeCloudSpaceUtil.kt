package com.nearme.note.tips

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.util.Constants

class UpgradeCloudSpaceUtil {

    companion object {
        private var instance: UpgradeCloudSpaceUtil? = null
            get() {
                if (field == null) {
                    field = UpgradeCloudSpaceUtil()
                }
                return field
            }

        @Synchronized
        fun instance(): UpgradeCloudSpaceUtil {
            return instance!!
        }

    }

    val isPaySuccessLiveData = MutableLiveData(false)

    fun doUpgradeCloudSpace(mActivity: AppCompatActivity) {
        if (mActivity.isFinishing) {
            return
        }
        CloudKitSdkManager.showUpdateCloudSpaceDialog(mActivity, Constants.MODULE_NOTE, isPaySuccessLiveData)
    }
}