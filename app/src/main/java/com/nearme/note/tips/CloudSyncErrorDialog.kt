package com.nearme.note.tips

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.theme.COUIThemeOverlay
import com.oplus.note.logger.AppLogger
import com.oplus.note.R
import com.oplus.note.databinding.DialogCloudSyncErrorBinding

class CloudSyncErrorDialog(val mContext: Context, val mType: CloudSyncErrorType) :
    COUIBottomSheetDialog(mContext, com.support.panel.R.style.DefaultBottomSheetDialog) {

    companion object {
        const val TAG = "NetworkConnectErrorDialog"
        const val DIALOG_DELAY_TIME = 1000L
    }

    interface RetryStartSyncListener {
        fun retryStartSync(dialog: CloudSyncErrorDialog)
    }

    var mRetryStartSyncListener: RetryStartSyncListener? = null
    private var binding: DialogCloudSyncErrorBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        COUIThemeOverlay.getInstance().applyThemeOverlays(mContext)
        binding = DialogCloudSyncErrorBinding.inflate(LayoutInflater.from(mContext))
        contentView = binding?.root
        super.onCreate(savedInstanceState)
        window?.findViewById<View>(com.support.panel.R.id.panel_outside)?.setOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                dismiss()
            }
            true
        }
        initViews(mType)
        val behavior = behavior
        if (behavior is COUIBottomSheetBehavior) {
            behavior.addBottomSheetCallback(object :
                COUIBottomSheetBehavior.COUIBottomSheetCallback() {
                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    AppLogger.BASIC.d(TAG, "COUIBottomSheetCallback onSlide")
                }

                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == COUIBottomSheetBehavior.STATE_HIDDEN) {
                        dismiss()
                    }
                }
            })
        }
    }

    private fun initViews(type: CloudSyncErrorType) {
        binding?.btnText?.isClickable = true
        when (type) {
            CloudSyncErrorType.WLANConnectionExceptionType -> {
                binding?.let { bin ->
                    bin.imageInfo.setImageResource(R.drawable.no_connection)
                    bin.textInfo.setText(R.string.network_connection_error)
                    bin.btnText.setText(R.string.connect_WLAN)
                    bin.btnText.setOnClickListener {
                        bin.btnText.isClickable = false
                        mContext.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                        dismiss()
                    }
                }
            }
            CloudSyncErrorType.CloudkitSyncErrorType -> {
                binding?.let { bin ->
                    bin.imageInfo.setImageResource(R.drawable.server_exception)
                    bin.textInfo.setText(R.string.service_exception_not_start)
                    bin.btnText.setText(R.string.download_control_retry)
                    bin.btnText.setOnClickListener {
                        bin.btnText.setText(R.string.retrying)
                        bin.btnText.setTextColor(context.getColor(R.color.item_folder_text_color))
                        bin.btnText.isClickable = false
                        mRetryStartSyncListener?.retryStartSync(this)
                    }
                }
            }
            CloudSyncErrorType.NetWorkConnectionErrorType -> {
                binding?.let { bin ->
                    bin.imageInfo.setImageResource(R.drawable.network_exception)
                    bin.textInfo.setText(R.string.network_exception)
                    bin.btnText.setText(R.string.download_control_retry)
                    bin.btnText.setOnClickListener {
                        bin.btnText.setText(R.string.retrying)
                        bin.btnText.setTextColor(context.getColor(R.color.item_folder_text_color))
                        bin.btnText.isClickable = false
                        mRetryStartSyncListener?.retryStartSync(this)
                    }
                }
            }
        }
    }


}