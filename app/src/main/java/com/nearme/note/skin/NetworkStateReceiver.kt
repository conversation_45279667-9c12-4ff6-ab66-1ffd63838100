/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  NetworkStateReceiver.kt
 * * Description: NetworkStateReceiver.kt
 * * Version: 1.0
 * * Date : 2020/7/7
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/7/7      1.0    build this module
 ****************************************************************/

package com.nearme.note.skin

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.widget.Toast
import com.oplus.note.R
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.util.NetworkUtils

class NetworkStateReceiver : BroadcastReceiver() {

    var isResume = false

    @SuppressLint("UnsafeBroadcastReceiverActionDetector")
    override fun onReceive(context: Context, intent: Intent?) {
        if (isResume && NetworkUtils.isMobileDataConnected(context)
                && SkinManager.hasNewManualDownloadSkin()) {
            Toast.makeText(context, R.string.not_wlan_toast, Toast.LENGTH_SHORT).show()
        }
    }
}