/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinSummaryAdapter.kt
 * * Description: SkinSummaryAdapter
 * * Version: 1.0
 * * Date: 2020/06/01
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.skin

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.core.graphics.drawable.toDrawable
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.Key
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.TransformationUtils
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.request.transition.Transition
import com.coui.appcompat.progressbar.COUICircleProgressBar
import com.nearme.note.skin.api.SkinManager
import com.oplus.note.repo.skin.bean.SkinSummary
import com.nearme.note.util.DensityHelper
import com.nearme.note.util.glideWithAvailable
import com.nearme.note.view.PressFeedbackHelper
import com.oplus.note.R
import com.oplus.note.downloader.core.Downloader
import java.lang.ref.WeakReference
import java.security.MessageDigest
import kotlin.properties.Delegates


class SkinSummaryAdapter(context: Context) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val TAG = "SkinSummaryAdapter"
        const val TYPE_TITLE = 0
        const val TYPE_VIEW = 1
    }

    var panelView: View? = null
    var mSelectedPosition = -1
    var mSpanCount by Delegates.notNull<Int>()
    private var mFistUnDownloadItemPosition = -1
    private var mHasInit = false
    private var mInitSkin: String = SkinData.COLOR_SKIN_WHITE
    private var mItemsData = ArrayList<SkinSummary>()
    private var mDownloadingItems = SkinManager.getSkinDownList()
    /**
     *  这里主要控制本地图片皮肤和下载图片皮肤的加载，但是实测，使用同一个尺寸加载时，本地皮肤圆角并不对，只能用两个transformation控制
     */
    private val mImageTransformation by lazy { SkinSelectedBitmapTransformation(context, itemRoundRadius) }
    private val mImageLocalTransformation by lazy {
        SkinSelectedBitmapTransformation(
            context,
            context.resources.getDimensionPixelOffset(R.dimen.skin_item_round_radius_outer)
        )
    }
    private var mRecyclerView: RecyclerView? = null
    private val itemRoundRadius = DensityHelper.getDefaultConfigDimension(R.dimen.skin_item_round_radius)
    private var itemSize = 0

    fun refresh(data: List<SkinSummary>) {
        if (!mHasInit) {
            init(data)
        }

        mItemsData.clear()
        mItemsData.addAll(data)
        notifyDataSetChanged()
    }

    private fun init(data: List<SkinSummary>) {
        mHasInit = true
        data.forEachIndexed { index, skinSummary ->
            if (mInitSkin == skinSummary.id) {
                mSelectedPosition = index
                if (!skinSummary.isDownloaded()) {
                    mSelectedPosition = 0
                }

                if (mFistUnDownloadItemPosition != -1) {
                    return@forEachIndexed
                }
            }

            if (!isDownloading(skinSummary) && !skinSummary.isDownloaded()) {
                if (mFistUnDownloadItemPosition == -1) {
                    mFistUnDownloadItemPosition = index
                }

                if (mSelectedPosition != -1) {
                    return@forEachIndexed
                }
            }
        }
    }

    fun setInitSkin(skin: String?) {
        skin?.apply {
            mInitSkin = this
            mItemsData.forEachIndexed { index, skinSummary ->
                if (mInitSkin == skinSummary.id) {
                    mSelectedPosition = index
                    notifyDataSetChanged()
                    return@forEachIndexed
                }
            }
        }
    }

    inner class SkinTitleHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mSkinClassifyName: TextView = itemView.findViewById(R.id.skin_title)

        fun setTitle(title: String?) {
            mSkinClassifyName.text = title
        }
    }

    inner class SkinViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val mItemContainer: ConstraintLayout = itemView.findViewById(R.id.item_container)
        val mIvBackground: CoverRounderImageView = itemView.findViewById(R.id.iv_skin_bg)
        val mProgress: COUICircleProgressBar = itemView.findViewById(R.id.progress)
        val mDownload: ImageView = itemView.findViewById(R.id.download)

        private val mPressFeedbackHelper = PressFeedbackHelper(itemView)

        init {
            val panelWidth = panelView!!.width
            val skinBoardPadding = DensityHelper.getDefaultConfigDimension(R.dimen.skin_board_end_padding)
            val spanSpace = DensityHelper.getDefaultConfigDimension(R.dimen.skin_board_between_margin)
            itemSize = (panelWidth - skinBoardPadding * 2 - spanSpace * (mSpanCount - 1)) / mSpanCount
            mItemContainer.layoutParams.apply {
                width = itemSize
                height = itemSize
            }
        }

        fun setProgress(progress: Int) {
            mIvBackground.showMask(true)
            mDownload.visibility = View.GONE
            mProgress.visibility = View.VISIBLE
            mProgress.progress = progress
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_VIEW) {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.skin_board_item, parent, false)
            SkinViewHolder(view)
        } else {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.skin_board_title_item, parent, false)
            SkinTitleHolder(view)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (mItemsData[position].id == SkinData.SKIN_CLASSIFY) {
            TYPE_TITLE
        } else {
            TYPE_VIEW
        }
    }

    fun getItem(position: Int): SkinSummary {
        return mItemsData[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemCount() = mItemsData.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = mItemsData[position]
        val context = holder.itemView.context

        if (holder is SkinViewHolder) {
            when {
                SkinData.isColorSkin(item.id) -> {
                    val drawable = ContextCompat.getDrawable(context, R.drawable.skin_color_unselected_container) as LayerDrawable
                    if (position == COLOR_WHITE_SKIN_INDEX) {
                        drawable.setDrawableByLayerId(R.id.skin_foreground,
                                ContextCompat.getDrawable(context, R.drawable.skin_special_white_color_foreground))
                    } else {
                        drawable.setDrawableByLayerId(R.id.skin_foreground,
                                ContextCompat.getDrawable(context, R.drawable.skin_color_foreground))
                    }
                    drawable.findDrawableByLayerId(R.id.skin_background).setTint(context.getColor(SkinData.getColorIdByResName(item.id)))
                    drawable.findDrawableByLayerId(R.id.skin_foreground).setTint(context.getColor(SkinData.getColorIdByResName(item.thumbnail)))
                    holder.mIvBackground.setImageBitmap(drawable.toBitmap())
                }
                SkinData.isManualSkin(item.id) -> {
                    val drawable = ContextCompat.getDrawable(context, R.drawable.grid_skin_unselected_container) as LayerDrawable
                    when (item.id) {
                        SkinData.COLOR_SKIN_HORIZON_LINE -> {
                            drawable.setDrawableByLayerId(R.id.grid_skin_foreground,
                                    ContextCompat.getDrawable(context, R.drawable.skin_horizon_line))
                        }
                        SkinData.COLOR_SKIN_GRID_LINE -> {
                            drawable.setDrawableByLayerId(R.id.grid_skin_foreground,
                                    ContextCompat.getDrawable(context, R.drawable.skin_grid_line_vector))
                        }
                        SkinData.COLOR_SKIN_GRID_DOT -> {
                            drawable.setDrawableByLayerId(R.id.grid_skin_foreground,
                                    ContextCompat.getDrawable(context, R.drawable.skin_grid_dot))
                        }
                    }
                    holder.mIvBackground.setImageBitmap(drawable.toBitmap())
                }
                SkinData.isImgSkin(item.id) -> {
                    context.glideWithAvailable()?.asBitmap()?.load(item.thumbnail)?.override(itemSize)?.into(object : CustomTarget<Bitmap>() {
                        override fun onResourceReady(
                            resource: Bitmap,
                            transition: Transition<in Bitmap>?
                        ) {
                            holder.mIvBackground.setImageBitmap(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                        }
                    })
                }
                else -> {
                    context.glideWithAvailable()?.asBitmap()?.load(Downloader.getBaseUrl() + item.thumbnail)
                        ?.override(itemSize)?.addListener(object : RequestListener<Bitmap> {
                            override fun onLoadFailed(
                                e: GlideException?,
                                model: Any?,
                                target: Target<Bitmap>,
                                isFirstResource: Boolean
                            ): Boolean {
                                if (item.isDownloaded()) {
                                    holder.mIvBackground.showMask(true)
                                    holder.mDownload.visibility = View.GONE
                                    holder.mProgress.visibility = View.GONE
                                }
                                return false
                            }

                            override fun onResourceReady(
                                resource: Bitmap,
                                model: Any,
                                target: Target<Bitmap>?,
                                dataSource: DataSource,
                                isFirstResource: Boolean
                            ): Boolean {
                                return false
                            }
                        })?.into(object : CustomTarget<Bitmap>() {
                            override fun onResourceReady(
                                resource: Bitmap,
                                transition: Transition<in Bitmap>?
                            ) {
                                holder.mIvBackground.setImageBitmap(resource)
                            }

                            override fun onLoadCleared(placeholder: Drawable?) {
                            }
                        })
                }
            }

            holder.mIvBackground.isSelect =  (position == mSelectedPosition)

            // check should show foreground
            if (position < DEFAULT_COLOR_SKIN_COUNT + DEFAULT_IMAGE_SKIN_COUNT) {
                holder.mIvBackground.showMask(false)
                holder.mDownload.visibility = View.GONE
                holder.mProgress.visibility = View.GONE
            } else {
                if (item.isDownloaded()) {
                    holder.mIvBackground.showMask(false)
                    holder.mDownload.visibility = View.GONE
                    holder.mProgress.visibility = View.GONE
                } else {
                    // check if is downloading
                    if (mDownloadingItems.containsKey(item)) {
                        holder.setProgress(mDownloadingItems[item]!!)
                    } else {
                        holder.mIvBackground.showMask(true)
                        holder.mDownload.visibility = View.VISIBLE
                        holder.mProgress.visibility = View.GONE
                    }
                }
            }
        } else if (holder is SkinTitleHolder) {
            holder.setTitle(getTitle(context, mItemsData[position].detail))
        }
    }

    fun getTitle(context: Context, detail: String?): String {
        val title = when (detail) {
            SkinData.COLOR_TITLE -> context.resources.getString(R.string.skin_title_pure_color)
            SkinData.GRID_TITLE -> context.resources.getString(R.string.grid_title)
            SkinData.CUSTOM_TITLE -> context.resources.getString(R.string.custom_title)
            else -> ""
        }
        return title
    }

    fun notifyProgress(skinSummary: SkinSummary, progress: Int) {
        mDownloadingItems[skinSummary] = progress
        val position = mItemsData.indexOf(skinSummary)

        val vh = mRecyclerView?.findViewHolderForAdapterPosition(position)
        if (vh is SkinViewHolder) {
            vh.setProgress(progress)
        }
    }

    fun notifyDownloadComplete(skinSummary: SkinSummary) {
        mDownloadingItems.remove(skinSummary)
    }

    fun isDownloading(skinSummary: SkinSummary): Boolean {
        return mDownloadingItems.containsKey(skinSummary)
    }

    fun setRecyclerView(skinBoard: RecyclerView?) {
        mRecyclerView = skinBoard
    }

    private class SkinSelectedBitmapTransformation(context: Context, private val itemRoundRadius: Int) : BitmapTransformation() {

        private val id = "${context.packageName}.skin.ItemRoundTransformation"
        private val idBytes = id.toByteArray(Key.CHARSET)

        private val refs = WeakReference(context)

        override fun updateDiskCacheKey(messageDigest: MessageDigest) {
            messageDigest.update(idBytes)
        }

        override fun transform(pool: BitmapPool, toTransform: Bitmap, outWidth: Int, outHeight: Int): Bitmap {
            refs.get()?.apply {
                val centerCrop = TransformationUtils.centerCrop(pool, toTransform, outWidth, outHeight)
                val roundedCorners = TransformationUtils.roundedCorners(pool, centerCrop, itemRoundRadius)
                val drawable = ContextCompat.getDrawable(this, R.drawable.skin_unselected_container) as LayerDrawable
                drawable.setDrawableByLayerId(R.id.background, roundedCorners.toDrawable(resources))
                return drawable.toBitmap()
            }
            return toTransform
        }

        override fun equals(other: Any?): Boolean {
            return other is SkinSelectedBitmapTransformation
        }

        override fun hashCode(): Int {
            return id.hashCode()
        }
    }
}