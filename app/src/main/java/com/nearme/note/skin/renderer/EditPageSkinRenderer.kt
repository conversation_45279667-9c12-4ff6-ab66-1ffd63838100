/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: EditPageSkinRenderer.kt
 * * Description: EditPageSkinRenderer
 * * Version: 1.0
 * * Date: 2020/06/09
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.skin.renderer

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.net.Uri
import androidx.collection.LruCache
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import com.nearme.note.activity.edit.NinePatchBitmapFactory
import com.nearme.note.skin.SkinBoardDialog
import com.nearme.note.skin.SkinData
import com.nearme.note.skin.api.SkinManager
import com.oplus.note.repo.skin.bean.Skin.EditPage.Checkbox
import com.nearme.note.util.DarkModeUtil
import com.nearme.note.util.MyAppUtil
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.skin.api.SkinContent.SIZE_CONTENT_BOTTOM
import com.oplus.note.repo.skin.api.SkinContent.SIZE_CONTENT_LEFT
import com.oplus.note.repo.skin.api.SkinContent.SIZE_CONTENT_RIGHT
import com.oplus.note.repo.skin.api.SkinContent.SIZE_CONTENT_TOP
import com.oplus.note.repo.skin.bean.Skin
import java.io.File
import java.io.FileNotFoundException

class EditPageSkinRenderer {
    companion object {
        private const val TAG = "EditPageSkinRenderer"
        private const val LRU_CACHE_MAX_SIZE = 10

        @SuppressLint("Range")
        fun getCheckDrawable(context: Context, skinId: String, checkboxRes: Checkbox, skinDialog: SkinBoardDialog?): Drawable? {
            try {
                val check = checkboxRes.check
                if (Skin.EditPage.TYPE_PICTURE == check.type) {
                    val path = SkinManager.getUri(skinId, check.value)
                    AppLogger.BASIC.e(TAG, "getCheckDrawable skinId:--$skinId")
                    return if (SkinManager.isEmbedSkin(skinId)) {
                        context.contentResolver.openInputStream(Uri.parse(path))?.use {
                            BitmapFactory.decodeStream(it).toDrawable(context.resources)
                        }
                    } else {
                        if (File(path).exists()) {
                            BitmapFactory.decodeFile(path).toDrawable(context.resources)
                        } else {
                            resetSkinState(skinId, skinDialog)
                            null
                        }
                    }
                } else if (Skin.EditPage.TYPE_COLOR == check.type) {
                    val drawable = ContextCompat.getDrawable(context, R.drawable.note_detail_todo_checked_on)
                    //默认白色皮肤下，需要显示主题色图标，不主动设置icon颜色
                    if (SkinData.COLOR_SKIN_WHITE != skinId && !SkinData.isManualSkin(skinId)) {
                        drawable?.setTint(Color.parseColor(check.value))
                    }
                    return drawable
                }
            } catch (e: FileNotFoundException) {
                AppLogger.BASIC.e(TAG, "CheckDrawable FileNotFoundException:${e.message}")
                resetSkinState(skinId, skinDialog)
                return null
            } catch (e: java.lang.Exception) {
                return null
            }
            return null
        }

        private fun resetSkinState(skinId: String, skinDialog: SkinBoardDialog?) {
            skinDialog?.updateDefaultSkin()
            val skinListData = SkinManager.getSkinListData()
            skinListData.value?.run {
                forEach { data ->
                    if (skinId == data.id) {
                        data.detail = ""
                    }
                }
                SkinManager.updateSkinList(this)
            }
        }

        @SuppressLint("Range")
        fun getUnCheckDrawable(context: Context, skinId: String, checkboxRes: Checkbox, skinDialog: SkinBoardDialog?): Drawable? {
            try {
                val uncheck = checkboxRes.uncheck
                if (Skin.EditPage.TYPE_PICTURE == uncheck.type) {
                    val path = SkinManager.getUri(skinId, uncheck.value)
                    return if (SkinManager.isEmbedSkin(skinId)) {
                        context.contentResolver.openInputStream(Uri.parse(path))?.use {
                            BitmapFactory.decodeStream(it).toDrawable(context.resources)
                        }
                    } else {
                        if (File(path).exists()) {
                            BitmapFactory.decodeFile(path).toDrawable(context.resources)
                        } else {
                            resetSkinState(skinId, skinDialog)
                            null
                        }
                    }
                } else if (Skin.EditPage.TYPE_COLOR == uncheck.type) {
                    return if (SkinData.COLOR_SKIN_WHITE == skinId && DarkModeUtil.isDarkMode(context)) {
                        ContextCompat.getDrawable(context, R.drawable.note_detail_todo_checked_off_night)
                    } else {
                        val drawable = ContextCompat.getDrawable(context, R.drawable.note_detail_todo_checked_off)
                        drawable?.setTint(Color.parseColor(uncheck.value))
                        drawable
                    }
                }
            } catch (e: FileNotFoundException) {
                AppLogger.BASIC.e(TAG, "UnCheckDrawable FileNotFoundException:${e.message}")
                resetSkinState(skinId, skinDialog)
                return null
            } catch (e: java.lang.Exception) {
                return null
            }
            return null
        }
    }

    private val mCachedTopDrawables = LruCache<String, Drawable>(LRU_CACHE_MAX_SIZE)
    private val mCachedContentDrawables = LruCache<String, Drawable>(LRU_CACHE_MAX_SIZE)

    fun getEditPageContentBackground(
        context: Context,
        skinId: String,
        configuration: Skin.EditPage,
        block: (conentDrawable: Drawable?, topDrawable: Drawable?) -> Unit
    ) {
        configuration.background.contentCenterBg.let { bgColor ->
            val type = bgColor.type
            when (type) {
                Skin.EditPage.TYPE_PICTURE -> handleTypePicture(skinId, context, configuration, block)
                Skin.EditPage.TYPE_COLOR -> {
                    val color = if (DarkModeUtil.isDarkMode(MyAppUtil.getContext())) {
                        bgColor.color.darkMode
                    } else {
                        bgColor.color.lightMode
                    }
                    fun getDrawable(): Drawable {
                        if (mCachedContentDrawables[color] != null) {
                            return mCachedContentDrawables[color]!!
                        }
                        ContextCompat.getDrawable(context, R.drawable.skin_spotlight_pure_color)!!.apply {
                            setTint(SkinManager.getColor(color))
                            mCachedContentDrawables.put(color, this)
                            return mCachedContentDrawables[color]!!
                        }
                    }
                    block.invoke(getDrawable(), getTimeInfoDrawable(context, configuration))
                }
            }
        }
    }
    private fun getTimeInfoDrawable(context: Context, configuration: Skin.EditPage): Drawable {
        configuration.background.toolbarBgColor.let { timeInfoBg ->
            val timeInfo = if (DarkModeUtil.isDarkMode(MyAppUtil.getContext())) {
                timeInfoBg.darkMode
            } else {
                timeInfoBg.lightMode
            }
            fun getDrawable(): Drawable {
                if (mCachedTopDrawables[timeInfo] != null) {
                    return mCachedTopDrawables[timeInfo]!!
                }
                ContextCompat.getDrawable(context, R.drawable.skin_spotlight_pure_color)!!.apply {
                    setTint(SkinManager.getColor(timeInfo))
                    mCachedTopDrawables.put(timeInfo, this)
                    return mCachedTopDrawables[timeInfo]!!
                }
            }
            return getDrawable()
        }
    }

    private fun handleTypePicture(
        skinId: String,
        context: Context,
        configuration: Skin.EditPage,
        block: (drawable: Drawable?, topExtraBg: Drawable?) -> Unit
    ) {
        val contentValue = configuration.background.contentCenterBg.img
        val res = SkinManager.getUri(skinId, contentValue)
        val srcBitmap = BitmapFactory.decodeFile(res)
        if (srcBitmap == null) {
            AppLogger.BASIC.e(TAG, "Source bitmap is null")
            return
        }
        val ninePatchDrawable = NinePatchBitmapFactory.createNinePatchDrawable(context.resources, srcBitmap)
        block.invoke(ninePatchDrawable, getTimeInfoDrawable(context, configuration))
    }

    fun getEditPageContentOffset(): Rect {
        return try {
            Rect(SkinManager.getStableSize(SIZE_CONTENT_LEFT.toFloat()).toInt(),
                SkinManager.getStableSize(SIZE_CONTENT_TOP.toFloat()).toInt(),
                SkinManager.getStableSize(SIZE_CONTENT_RIGHT.toFloat()).toInt(),
                SkinManager.getStableSize(SIZE_CONTENT_BOTTOM.toFloat()).toInt())
        } catch (e: Exception) {
            Rect()
        }
    }
}