/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SkinBoardDialog.kt
 * * Description: SkinBoardDialog
 * * Version: 1.0
 * * Date: 2020/05/25
 * * Author: zengzhigang
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.nearme.note.skin

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.os.Build
import android.os.Bundle
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.toolbar.COUIActionMenuItemView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.nearme.note.DialogFactory
import com.nearme.note.MyApplication
import com.nearme.note.activity.list.ItemClickHelper
import com.nearme.note.skin.api.SkinManager
import com.oplus.note.repo.skin.bean.Skin
import com.oplus.note.protocol.IHttpTransferListener
import com.nearme.note.util.DarkModeUtil
import com.nearme.note.util.DensityHelper
import com.nearme.note.util.FileUtil
import com.nearme.note.util.MultiClickFilter
import com.nearme.note.util.NetworkUtils
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.ScreenUtil
import com.nearme.note.util.StatisticsUtils
import com.nearme.note.view.helper.UiHelper
import com.oplus.forcealertcomponent.ForceAlertUtils
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.databinding.SkinDialogBinding
import com.oplus.note.downloader.core.Downloader
import com.oplus.note.downloader.util.Constants
import com.oplus.note.downloader.util.DeviceUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.skin.bean.SkinSummary
import com.oplus.richtext.editor.utils.ScreenSizeHelper
import java.lang.ref.WeakReference

// all bellow values are come from UI design in 1080P, used to percent layout
const val DEFAULT_COLOR_SKIN_COUNT = 7
const val DEFAULT_IMAGE_SKIN_COUNT = 4
const val DEFAULT_GRID_SKIN_COUNT = 3
const val DEFAULT_SKIN_TITLE_COUNT = 3
const val DEFAULT_SKIN_COUNT = DEFAULT_COLOR_SKIN_COUNT + DEFAULT_IMAGE_SKIN_COUNT +
        DEFAULT_GRID_SKIN_COUNT + DEFAULT_SKIN_TITLE_COUNT
const val COLOR_WHITE_SKIN_INDEX = 0
const val STABLE_SCALE_SIZE = 1.2f
// all above values are come from UI design in 1080P

open class SkinBoardDialog(context: Context, theme: Int) : COUIBottomSheetDialog(context, theme) {

    private var mApplySkinListener: ((itemView: View?, skin: SkinSummary) -> Unit)? = null

    private val mAdapter = SkinSummaryAdapter(context).apply { setHasStableIds(true) }
    private val mContext = context
    private var mSkinSummarySize = 0
    private var binding: SkinDialogBinding? = null
    protected var mStartDownSkin = false
    var updateDefaultSkinCallback: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        binding = SkinDialogBinding.inflate(LayoutInflater.from(mContext))
        binding?.root?.let { setContentView(it) }
        super.onCreate(savedInstanceState)
        window?.setGravity(Gravity.BOTTOM)
        window?.decorView?.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION)

        setCanceledOnTouchOutside(false)
        val color = context.resources.getColor(R.color.skin_panel_background_color)
        setPanelBackgroundTintColor(color)

        if (!ScreenSizeHelper.isMiddleAndLargeScreen(context)) {
            window?.findViewById<View>(com.support.panel.R.id.panel_outside)?.setBackgroundResource(
                com.oplus.note.scenecard.R.color.transparent)
            setPanelBackground(context.getDrawable(com.support.panel.R.drawable.coui_panel_bg_with_shadow))
        } else {
            window?.findViewById<View>(com.support.panel.R.id.panel_outside)?.setBackgroundResource(R.color.panel_bg_outside_color)
        }
        window?.findViewById<View>(com.support.panel.R.id.panel_outside)?.setOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                dismiss()
            }
            true
        }

        initSkinBoard()

        val behavior = behavior
        if (behavior is COUIBottomSheetBehavior) {
            behavior.addBottomSheetCallback(object : COUIBottomSheetBehavior.COUIBottomSheetCallback() {
                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    AppLogger.BASIC.i("SkinBoardDialog", "addBottomSheetCallback: onSlide")
                }

                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == COUIBottomSheetBehavior.STATE_COLLAPSED) {
                        behavior.setPanelState(COUIBottomSheetBehavior.STATE_HIDDEN)
                    } else if (newState == COUIBottomSheetBehavior.STATE_HIDDEN) {
                        dismiss()
                    }
                }
            })
        }
    }

    private fun initToolbar() {
//        hideDragView()
        binding?.toolbar?.apply {
            isTitleCenterStyle = true
            title = context.getString(R.string.skin)
            inflateMenu(R.menu.menu_panel_edit)
            menu.findItem(R.id.save).apply {
                title = ""
                isEnabled = false
                icon = null
            }
            findViewById<COUIActionMenuItemView>(R.id.cancel)?.let {
                it.setTextColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimaryTextOnPopup))
                it.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    ForceAlertUtils.getTextSizeLargestToG3(com.oplus.forcealertcomponent.R.dimen.sp_force_alert_16, context))
            }
            menu.findItem(R.id.cancel).apply {
                title = context.getString(R.string.edit_complete)
                setOnMenuItemClickListener {
                    dismiss()
                    true
                }
            }
        }
    }

    private fun initSkinBoard() {
        setSkinBoardBackgroundTint()
        resolveSkinBoardSize()
        showOrHideSkinDownLoadTips()
        initToolbar()
        binding?.downloadSkinTips?.let { COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G4) }
        val panelView: View? = window?.findViewById(com.google.android.material.R.id.design_bottom_sheet)
        binding?.downloadSkinTips?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding?.downloadSkinTips?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                val spanCount = SkinListHelper.getSkinSpanCount(context, ScreenUtil.getScreenWidth())
                binding?.skinBoard?.apply {
                    layoutManager = GridLayoutManager(context, spanCount).apply {
                        spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                            override fun getSpanSize(position: Int): Int {
                                return if (mAdapter.getItem(position).id == SkinData.SKIN_CLASSIFY) {
                                    spanCount
                                } else {
                                    1
                                }
                            }
                        }
                    }
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        isForceDarkAllowed = false
                    }
                    mAdapter.mSpanCount = spanCount
                    adapter = mAdapter
                    mAdapter.setRecyclerView(this)
                }
                mAdapter.panelView = panelView
            }
        })

        binding?.skinBoard?.addOnItemTouchListener(object : ItemClickHelper() {
            override fun onItemClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int) {
                if (MultiClickFilter.isMultiClick(MultiClickFilter.DEFAULT_MULTI_CLICK_DELAY_SHORT)) {
                    return
                }

                if (adapter is SkinSummaryAdapter) {
                    if (position == adapter.mSelectedPosition) {
                        return
                    }
                    val skinSummary = adapter.getItem(position)
                    if (mAdapter.isDownloading(skinSummary)) {
                        return
                    }
                    if (skinSummary.id == SkinData.SKIN_CLASSIFY) {
                        return
                    }
                    if (!skinSummary.isDownloaded()) {
                        if (!NetworkUtils.isNetworkConnected(MyApplication.appContext)) {
                            Toast.makeText(context, R.string.network_unavailable, Toast.LENGTH_SHORT).show()
                            return
                        } else if (NetworkUtils.isMobileDataConnected(MyApplication.appContext)) {
                            Toast.makeText(context, R.string.not_wlan_toast, Toast.LENGTH_SHORT).show()
                        }
                        val listener = SkinDownloadingListener(binding?.skinBoard!!, mAdapter, skinSummary, this@SkinBoardDialog)
                        listener.onProgress(0, 100)
                        mStartDownSkin = true
                        SkinManager.downSkin(skinSummary, listener, true)
                        if (DeviceUtil.getDeviceType() == Constants.SCREEN_TYPE_FOLD) {
                            SkinManager.downExtraSkin(skinSummary)
                        }
                        return
                    }

                    adapter.mSelectedPosition = position
                    adapter.notifyDataSetChanged()
                    mApplySkinListener?.invoke(view, skinSummary)
                    StatisticsUtils.setEventClickSkin(
                        MyApplication.appContext,
                        SkinStatisticsUtils.conversionReportSkinId(skinSummary)
                    )
                }
            }

            override fun onItemLongClick(adapter: RecyclerView.Adapter<*>?, view: View, position: Int, x: Float, y: Float) {
                super.onItemLongClick(adapter, view, position, x, y)
                if (adapter is SkinSummaryAdapter) {
                    val skinSummary = adapter.getItem(position)
                    val previewDialog = PreviewDialog(mContext, Downloader.getBaseUrl() + skinSummary.preview, getContainerView())
                    onItemLongClick(adapter.getItem(position), previewDialog, mContext)
                }
            }
        })

        reattachDownloadingListener()
    }

    fun onItemLongClick(skinSummary: SkinSummary, previewDialog: PreviewDialog, context: Context) {
        if (SkinManager.isEmbedSkin(skinSummary.id) || skinSummary.isDownloaded()) {
            return
        }
        if (isNetworkNotConnected(context)) {
            Toast.makeText(context, R.string.skin_preview_no_network_toast, Toast.LENGTH_SHORT).show()
            return
        }
        previewDialog.show()
    }

    open fun isNetworkNotConnected(context: Context): Boolean {
        return !NetworkUtils.isNetworkConnected(context)
    }

    private fun reattachDownloadingListener() {
        SkinManager.getSkinDownloadingList().forEach {
            val listener =
                binding?.skinBoard?.let { it1 -> SkinDownloadingListener(it1, mAdapter, it, this) }
            SkinManager.reattachDownloadingListener(it, listener)
        }
    }

    fun refresh(skinSummaries: List<SkinSummary>) {
        mSkinSummarySize = skinSummaries.size
        mAdapter.refresh(skinSummaries)
        showOrHideSkinDownLoadTips()
    }

    private fun resolveSkinBoardSize() {
        val param = binding?.skinBoard?.layoutParams
        if (param is MarginLayoutParams) {
            param.marginStart = DensityHelper.getDefaultConfigDimension(R.dimen.skin_board_start_padding)
            param.marginEnd = DensityHelper.getDefaultConfigDimension(R.dimen.skin_board_end_padding)
        }
    }

    override fun show() {
        val behavior = behavior
        if (behavior is COUIBottomSheetBehavior && (behavior.state == BottomSheetBehavior.STATE_HIDDEN
                || behavior.state == BottomSheetBehavior.STATE_EXPANDED)) {
            behavior.setPanelState(COUIBottomSheetBehavior.STATE_HALF_EXPANDED)
        }
        super.show()
        setPanelBackgroundTintColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard))
    }

    fun setOnSkinApplyListener(listener: (itemView: View?, skin: SkinSummary) -> Unit) {
        mApplySkinListener = listener
    }

    fun setInitSkin(skin: String?) {
        mAdapter.setInitSkin(skin)
    }

    fun updateSelected(position: Int) {
        mAdapter.mSelectedPosition = position
        mAdapter.notifyDataSetChanged()
    }

    fun updateDefaultSkin() {
        mAdapter.setInitSkin(SkinData.COLOR_SKIN_WHITE)
        updateDefaultSkinCallback?.invoke()
    }

    fun showStorageNotEnoughTips(totalLength: Long) {
        if (mStartDownSkin) {
            mStartDownSkin = false
            if (FileUtil.isStorageNotEnough(SkinManager.getSkinBasePath(), totalLength)
                    || FileUtil.isStorageNotEnough(totalLength.toInt())) {
                Toast.makeText(context, R.string.storage_not_enough_please_clean_up, Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun getContainerView(): FrameLayout {
        val bo: BottomSheetDialog = this
        val clazz = BottomSheetDialog::class.java
        val filed = clazz.getDeclaredField("container")
        filed.isAccessible = true

        return filed.get(bo) as FrameLayout
    }

    fun showDownLoadFailureTips() {
        if (!FileUtil.isStorageNotEnough(SkinManager.getSkinBasePath())) {
            Toast.makeText(context, R.string.download_exception_please_retry, Toast.LENGTH_SHORT).show()
        }
        mAdapter.notifyDataSetChanged()
    }

    fun showOrHideSkinDownLoadTips() {
        if (BuildConfig.isExport || UiHelper.isDevicePad()) {
            return
        }
        if (!PrivacyPolicyHelper.isAgreeUserNotice(mContext)
            && (mSkinSummarySize in 1..DEFAULT_SKIN_COUNT)) {
            binding?.skinBoard?.setPadding(0, 0, 0, 0)
            binding?.downloadSkinTips?.visibility = View.VISIBLE

            binding?.downloadSkinTips?.text = mContext.resources.getString(R.string.skin_load_more)
            binding?.downloadSkinTips?.setTextColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary))
            binding?.downloadSkinTips?.setOnClickListener {
                val dialogFactory = DialogFactory(mContext as Activity, null)
                dialogFactory.checkDeclareRequestDialog(DialogFactory.GET_NETWORK_FOR_SKIN)
            }
        } else {
            binding?.downloadSkinTips?.text = mContext.resources.getString(R.string.connect_network_load_more_skin)
            binding?.downloadSkinTips?.setTextColor(mContext.resources.getColor(R.color.skin_connect_network_color))
            binding?.downloadSkinTips?.setOnClickListener(null)

            if ((mSkinSummarySize in 1..DEFAULT_SKIN_COUNT)
                    && !NetworkUtils.isNetworkConnected(MyApplication.appContext)) {
                binding?.skinBoard?.setPadding(0, 0, 0, 0)
                binding?.downloadSkinTips?.visibility = View.VISIBLE
            } else {
                binding?.skinBoard?.setPadding(0, 0, 0, mContext.resources.getDimensionPixelSize(R.dimen.dp_30))
                binding?.downloadSkinTips?.visibility = View.GONE
            }
        }
    }

    fun setSkinBoardBackgroundTint() {
        if (DarkModeUtil.isDarkMode(context)) {
            binding?.skinContainer?.backgroundTintList = ColorStateList.valueOf(mContext.resources.getColor(R.color.bg_skin_board_in_dark))
        }
    }
}

private class SkinDownloadingListener(skinBoard: View, adapter: SkinSummaryAdapter?, val skinSummary: SkinSummary,
                                      skinDialog: SkinBoardDialog) : IHttpTransferListener<Skin> {

    private val viewRef = WeakReference(skinBoard)
    private val adapterRef = WeakReference(adapter)
    private val dialogRef = WeakReference(skinDialog)

    override fun onProgress(currentLength: Long, totalLength: Long) {
        viewRef.get()?.post {
            adapterRef.get()?.notifyProgress(skinSummary, (currentLength * 100 / totalLength).toInt())

            if (currentLength > 0) {
                dialogRef.get()?.showStorageNotEnoughTips(totalLength)
            }
        }
    }

    override fun onSuccess(response: Skin?) {
        viewRef.get()?.post {
            adapterRef.get()?.notifyDownloadComplete(skinSummary)
        }
    }

    override fun onFailure(exception: Exception?) {
        viewRef.get()?.post {
            adapterRef.get()?.notifyDownloadComplete(skinSummary)
            dialogRef.get()?.showDownLoadFailureTips()
        }
    }

    override fun onFailure(error: AssertionError?) {
        viewRef.get()?.post {
            adapterRef.get()?.notifyDownloadComplete(skinSummary)
            dialogRef.get()?.showDownLoadFailureTips()
        }
    }
}