/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  NoteDao.java
 * * Description: NoteDao.java
 * * Version: 1.0
 * * Date : 2020/1/9
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/9      1.0    build this module
 ****************************************************************/

package com.nearme.note.db.daos;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.RawQuery;
import androidx.room.Transaction;
import androidx.room.Update;
import androidx.sqlite.db.SimpleSQLiteQuery;
import androidx.sqlite.db.SupportSQLiteQuery;
import androidx.sqlite.db.SupportSQLiteStatement;

import com.nearme.note.activity.list.entity.NoteItem;
import com.nearme.note.activity.list.entity.SearchItem;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.nearme.note.data.NoteAttribute;
import com.nearme.note.data.NoteInfo;
import com.nearme.note.db.AppDatabase;
import com.nearme.note.db.entities.Note;
import com.nearme.note.db.entities.SkinCountEntity;
import com.nearme.note.util.SortRule;
import com.nearme.note.util.SqlUtils;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Set;

/**
 * 注意：
 * <p>
 * 部分Dao接口没有使用Room生成，主要原因是Sql中不允许超过 999 个参数，如果用 " where guid in (:guids)"，如果guids的数目超过999，就会崩溃
 */
@Dao
public abstract class NoteDao extends BaseDao {

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "FROM notes")
    public abstract List<Note> getAll();

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where guid = :guid")
    public abstract Note findByGuid(String guid);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where guid = :guid AND recycled_time = 0 AND deleted != 1 AND note_folder_guid != :excludedFolderGuid")
    public abstract Note findMainNoteByGuid(String guid, String excludedFolderGuid);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where note_folder_guid = :folderGuid")
    public abstract List<Note> findByFolderGuid(String folderGuid);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract long[] insert(List<Note> notes);

    @Insert
    public abstract long insert(Note note);

    public LiveData<List<NoteItem>> findMainListNotes(@SortRule.SortRuleOption int sortRule) {
        if (sortRule == SortRule.SORT_RULE_BY_UPDATE_TIME) {
            return doFindMainListNotes(FolderInfo.FOLDER_GUID_ENCRYPTED);
        } else {
            return doFindMainListNotesOrderByCreateTime(FolderInfo.FOLDER_GUID_ENCRYPTED);
        }
    }

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            "WHERE notes.recycled_time = 0 AND notes.deleted != 1 AND notes.note_folder_guid != :excludedFolderGuid " +
            "GROUP BY notes.guid ORDER BY notes.topped DESC, notes.updated DESC")
    protected abstract LiveData<List<NoteItem>> doFindMainListNotes(String excludedFolderGuid);

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            "WHERE notes.recycled_time = 0 AND notes.deleted != 1 AND notes.note_folder_guid != :excludedFolderGuid " +
            "GROUP BY notes.guid ORDER BY notes.topped DESC, notes.created DESC")
    protected abstract LiveData<List<NoteItem>> doFindMainListNotesOrderByCreateTime(String excludedFolderGuid);


    public LiveData<List<NoteItem>> findFolderListNotes(String folderGuid, @SortRule.SortRuleOption int sortRule) {
        if (sortRule == SortRule.SORT_RULE_BY_UPDATE_TIME) {
            return findFolderListNotes(folderGuid);
        } else {
            return findFolderListNotesOrderByCreateTime(folderGuid);
        }
    }

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            " WHERE notes.recycled_time = 0 AND notes.deleted != 1 AND notes.note_folder_guid = :folderGuid GROUP BY notes.guid " +
            " ORDER BY notes.topped DESC, notes.updated DESC")
    public abstract LiveData<List<NoteItem>> findFolderListNotes(String folderGuid);

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            " WHERE notes.recycled_time = 0 AND notes.deleted != 1 AND notes.note_folder_guid = :folderGuid GROUP BY notes.guid " +
            " ORDER BY notes.topped DESC, notes.created DESC")
    public abstract LiveData<List<NoteItem>> findFolderListNotesOrderByCreateTime(String folderGuid);

    public LiveData<List<NoteItem>> findRecentDeleteNotes(@SortRule.SortRuleOption int sortRule) {
        if (sortRule == SortRule.SORT_RULE_BY_UPDATE_TIME) {
            return findRecentDeleteNotes();
        } else {
            return findRecentDeleteNotesOrderByCreateTime();
        }
    }

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            " WHERE notes.recycled_time > 0 AND notes.deleted != 1 GROUP BY notes.guid " +
            " ORDER BY notes.recycled_time DESC, notes.updated DESC")
    public abstract LiveData<List<NoteItem>> findRecentDeleteNotes();

    @Transaction
    @Query("SELECT notes.thumb_type, notes.guid, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', " +
            "notes.note_folder, notes.updated, notes.created, notes.topped, notes.alarm_time, notes.state, " +
            "notes.note_skin, notes.note_folder_guid, notes.extra, notes.description, " +
            "substr(group_concat(notes_attributes.filename), 0, 40000) AS 'filename' " +
            " FROM notes LEFT JOIN notes_attributes ON (notes.guid = notes_attributes.note_guid AND notes_attributes.type IS " + NoteAttribute.TYPE_TEXT_CONTENT + ") " +
            " WHERE notes.recycled_time > 0 AND notes.deleted != 1 GROUP BY notes.guid " +
            " ORDER BY notes.recycled_time DESC, notes.created DESC")
    public abstract LiveData<List<NoteItem>> findRecentDeleteNotesOrderByCreateTime();

    @Transaction
    @Query("SELECT words.note_guid, substr(words.content, 0, 40000) AS 'content', " +
            "words.updated, notes.thumb_type, " +
            "substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', notes.topped, notes.state " +
            " FROM words INNER JOIN notes ON words.note_guid = notes.guid " +
            " WHERE (words.content LIKE :searchText escape '/' AND notes.deleted != " + NoteInfo.STATE_MARK_DELETED + " AND notes.recycled_time = 0 AND note_folder_guid != :encryptFolderId) " +
            " ORDER BY words.updated DESC")
    public abstract LiveData<List<SearchItem>> findSearchItemMainList(String searchText, String encryptFolderId);

    @Transaction
    @Query("SELECT words.note_guid, substr(words.content, 0, 40000) AS 'content', " +
            "words.updated, notes.thumb_type, " +
            "substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', notes.topped, notes.state " +
            " FROM words INNER JOIN notes ON words.note_guid = notes.guid " +
            " WHERE (words.content LIKE :searchText escape '/' AND notes.deleted != " + NoteInfo.STATE_MARK_DELETED + " AND notes.note_folder_guid = :folderGuid AND notes.recycled_time = 0) " +
            " ORDER BY words.updated DESC")
    public abstract LiveData<List<SearchItem>> findSearchItemFolderList(String searchText, String folderGuid);

    @Transaction
    @Query("SELECT words.note_guid, substr(words.content, 0, 40000) AS 'content', " +
            "words.updated, notes.thumb_type, " +
            "substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', notes.topped, notes.state " +
            " FROM words INNER JOIN notes ON words.note_guid = notes.guid " +
            " WHERE (words.content LIKE :searchText escape '/' AND notes.deleted != " + NoteInfo.STATE_MARK_DELETED + " AND notes.recycled_time > 0 AND note_folder_guid != :encryptFolderId) " +
            " ORDER BY words.updated DESC")
    public abstract LiveData<List<SearchItem>> findSearchItemRecentDelete(String searchText, String encryptFolderId);

    @Query("DELETE FROM notes WHERE guid = :guid")
    public abstract int deleteNoteByGuid(String guid);

    @Query("DELETE FROM notes")
    public abstract int deleteAll();

    @Query("select guid from notes where recycled_time > 0 and recycled_time <= :thirtyDaysBefore")
    public abstract List<String> findExpiredNotesGuid(long thirtyDaysBefore);

    @Update
    public abstract int updateNotes(List<Note> notes);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where note_folder_guid = :folderGuid and (note_folder != :folderName or note_folder_guid != :folderInfoGuid)")
    public abstract List<Note> findNotesByFolderInfo(String folderGuid, String folderName, String folderInfoGuid);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where deleted = :deletedState order by updated desc")
    public abstract List<Note> findNotesByDeletedOrderbyUpdated(String deletedState);

    @Query("select count(*) from notes")
    public abstract int getAllCount();

    @Query("select count(*) from notes where alarm_time > 0 and deleted = " + NoteInfo.STATE_UNMARK_DELETED)
    public abstract int getAllRemindNoteCount();

    @Query("SELECT note_skin, COUNT(notes.note_skin) as count from notes " +
            "WHERE note_skin != null and note_skin != null and deleted = " + NoteInfo.STATE_UNMARK_DELETED + " GROUP BY note_skin ;")
    public abstract List<SkinCountEntity> getAllSkinCount();

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes " +
            "where (state != " + NoteInfo.STATE_UNCHANGE + " or globalId is null or deleted=" + NoteInfo.STATE_MARK_DELETED + ") " +
            //当未同步的新建数据并且彻底删除后，该数据已不需要同步操作，所以需要在同步数据查询中剔除掉该类数据，
            //防止未同步数据的查询数量比实际需要同步的数据量多，而该类数据按照原有的逻辑会在云同步时从数据库中删除
            "and not (globalId is null and deleted=" + NoteInfo.STATE_MARK_DELETED + ") " +
            "order by updated desc")
    public abstract List<Note> findAllDirtyNotesOrderbyUpdate();

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where deleted = " + NoteInfo.STATE_UNMARK_DELETED +
            " and alarm_time >= :currentTimeMillis and recycled_time=0 " +
            " order by alarm_time asc limit 1 ")
    public abstract List<Note> findNextAlarmNote(long currentTimeMillis);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where globalId = :globalId")
    public abstract Note findNoteByGlobalGuid(String globalId);

    @Query("update notes set recycled_time = :curTime , state = :noteState where guid = :guid")
    public abstract int updateNoteRecycleTimeAndState(String guid, long curTime, int noteState);

    @Update
    public abstract int updateNote(Note note);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where globalId = :globalId")
    public abstract Note findbyGlobalId(String globalId);

    @Query("delete from notes where globalId = :globalId")
    public abstract void deleteNoteByGlobalGuid(String globalId);

    @Query("update notes set globalId = null , state = " + NoteInfo.STATE_NEW + ", account = :userName")
    public abstract void clearAllNoteSyncState(String userName);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where guid=:guid and globalId=:globalId")
    public abstract Note findbyGuidAndGlobalId(String guid, String globalId);

    @Query("delete from notes where globalId is not null")
    public abstract void deleteNotesGlobalIdNotNull();

    @Query("select guid from notes where globalId is not null")
    public abstract List<String> findNoteWhereGlobalIdNotNull();

    @Query("update notes set state = :state , globalId = :globalId")
    public abstract void updateNotesStateAndGlobalId(int state, String globalId);

    @Query("select count(*) from notes where globalId is not null and (account is null or account!=:username)")
    public abstract int findOtherUserNotesCount(String username);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "from notes where recycled_time = 0 and note_folder_guid != :folderGuid order by topped desc, updated desc")
    public abstract List<Note> findRecycledNotesDesc(String folderGuid);

    @Transaction
    public List<Note> findByFolderGuids(List<String> folderGuids) {
        String ids = SqlUtils.joinIds(folderGuids);
        String sql = "SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
                "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
                "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
                "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
                "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
                "FROM notes where note_folder_guid in (" + ids + ")";
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnList(query);
    }

    @Transaction
    public int deleteNotesByGuids(Set<String> guids) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "DELETE FROM notes where guid IN (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Transaction
    public int setNotesDeletedMark(Set<String> guids) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "UPDATE notes SET deleted = " + NoteInfo.STATE_MARK_DELETED + " , state = "
                + NoteInfo.STATE_MODIFIED + " WHERE guid IN (" + guidsStr + ")";
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Transaction
    public List<Note> findByGuids(Set<String> guids) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
                "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
                "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
                "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
                "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
                "FROM notes where guid in (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnList(query);
    }

    @Transaction
    public List<Note> findNotDeleteNoteByGuids(Set<String> guids) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
                "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
                "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
                "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
                "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
                "FROM notes WHERE deleted = " + NoteInfo.STATE_UNMARK_DELETED + " AND guid IN (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnList(query);
    }

    @Transaction
    public int setUnchanageNoteModified(Set<String> guids) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "UPDATE notes SET state = " + NoteInfo.STATE_MODIFIED + " WHERE state = " + NoteInfo.STATE_UNCHANGE + " AND guid IN (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Transaction
    public int setNotesToped(Set<String> guids, long topped) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "UPDATE notes SET topped = ? WHERE guid IN (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql, new Object[]{topped});
        return executeSqlReturnInt(query);
    }

    @Transaction
    public int setNotesFolerAndFolderGuid(Set<String> guids, String folder, String folderGuid) {
        String guidsStr = SqlUtils.joinIds(guids);
        String sql = "UPDATE notes SET note_folder = ? , note_folder_guid = ? WHERE guid IN (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql, new Object[]{folder, folderGuid});
        return executeSqlReturnInt(query);
    }

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "FROM notes WHERE deleted=" + NoteInfo.STATE_MARK_DELETED + " AND globalId IS NULL")
    public abstract List<Note> findInvalidNotes();

    @Query("SELECT count(*) FROM notes WHERE note_folder_guid=:folderGuid AND recycled_time=0")
    public abstract int findEncrptedNoteCount(String folderGuid);

    @RawQuery
    abstract List<Note> executeSqlReturnList(SupportSQLiteQuery query);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "FROM notes WHERE deleted=" + NoteInfo.STATE_UNMARK_DELETED + " AND recycled_time=0" +
            " ORDER BY updated DESC")
    public abstract List<Note> findAlarmNoteInfos();

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "FROM notes ORDER BY updated DESC")
    public abstract List<Note> findAlarmNoteInfosWithoutDeleted();

    @Query("SELECT count(*) FROM notes WHERE notes.topped > 0 AND notes.recycled_time = 0")
    public abstract int findToppedNoteCount();

    @Transaction
    public void runInTransaction(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        runnable.run();
    }

    @Query("UPDATE notes set note_skin=null, note_skin_pre=null where note_skin in (:skinIdList)")
    abstract public void clearSkin(@NotNull List<String> skinIdList);

    @Query("SELECT _id, guid, version, topped, updated, recycled_time, state, description, extra, " +
            "note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, uid, " +
            "deleted, para, created, globalId, attachment_id, attachment_md5, account, " +
            "alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, " +
            "timestamp, substr(notes.thumb_filename, 0, 40000) AS 'thumb_filename', sysVersion " +
            "FROM notes WHERE deleted=" + NoteInfo.STATE_UNMARK_DELETED +
            " ORDER BY updated DESC")
    public abstract List<Note> findNotesByUnMarkDelete();

    @Query("SELECT count(*) FROM notes WHERE deleted=" + NoteInfo.STATE_UNMARK_DELETED + " ORDER BY updated DESC")
    public abstract int findOldNoteCount();
}
