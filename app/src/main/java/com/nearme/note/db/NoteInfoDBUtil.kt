/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoDBUtil.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2017/07/26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>       2017/07/26        build this module
 * W9005794                2023/10/26        change java to kotlin
 **********************************************************************************/
package com.nearme.note.db

import android.content.Context
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.entities.Note
import com.nearme.note.db.entities.NotesAttribute
import com.nearme.note.db.entity.NoteInfoAdd
import com.nearme.note.db.entity.NoteInfoDelete
import com.nearme.note.db.entity.NoteInfoQuery
import com.nearme.note.db.entity.NoteInfoRecover
import com.nearme.note.db.entity.NoteInfoUpdate
import com.oplus.cloud.data.Packet

object NoteInfoDBUtil {
    const val TYPE_INSERT = 1
    const val TYPE_UPDATE = 2

    @JvmStatic
    fun insertNote(noteInfo: NoteInfo) {
        NoteInfoAdd.getInstance().insertNote(noteInfo)
    }

    @JvmStatic
    fun insertOrUpdateNote(noteInfo: NoteInfo) {
        NoteInfoAdd.getInstance().insertOrUpdateNote(noteInfo)
    }

    @JvmStatic
    fun insertNote(noteInfo: NoteInfo, userName: String?) {
        NoteInfoAdd.getInstance().insertNoteOfCloud(noteInfo, userName)
    }

    @JvmStatic
    fun insertNoteList(noteInfos: List<NoteInfo>, userName: String?) {
        NoteInfoAdd.getInstance().insertNoteListOfCloud(noteInfos, userName)
    }

    /*** delete behaviors of note info  */
    @JvmStatic
    fun cleanDatabase(ctx: Context): Boolean {
        return NoteInfoDelete.getInstance().cleanDatabase(ctx)
    }

    @JvmStatic
    fun clearSyncStateInfo(ctx: Context?, userName: String?) {
        NoteInfoDelete.getInstance().clearSyncStateInfo(ctx, userName)
    }

    @JvmStatic
    fun deleteInvalidNote() {
        NoteInfoDelete.getInstance().deleteInvalidNote()
    }

    @JvmStatic
    fun deleteNote(ctx: Context, guid: String): Boolean {
        return NoteInfoDelete.getInstance().deleteNote(ctx, guid)
    }

    @JvmStatic
    fun deleteNote(guid: String, isReal: Boolean): Boolean {
        return NoteInfoDelete.getInstance().deleteNote(guid, isReal)
    }

    @JvmStatic
    fun deleteNote(
        ctx: Context,
        globalId: String?,
        isReal: Boolean,
        isRecovery: Boolean
    ): String? {
        return NoteInfoDelete.getInstance().deleteNote(ctx, globalId, isReal, isRecovery)
    }

    @JvmStatic
    fun updateNote(noteInfo: NoteInfo) {
        NoteInfoUpdate.getInstance().updateNote(noteInfo)
    }

    @JvmStatic
    fun updateNote(noteInfo: NoteInfo?, userName: String?) {
        NoteInfoUpdate.getInstance().updateNote(noteInfo, userName)
    }

    @JvmStatic
    fun updateNotes(noteInfoList: List<NoteInfo>, userName: String?) {
        NoteInfoUpdate.getInstance().updateNotes(noteInfoList, userName)
    }

    @JvmStatic
    fun updateConflictNote(noteInfo: NoteInfo, userName: String?): Boolean {
        return NoteInfoUpdate.getInstance().updateConflictNote(noteInfo, userName)
    }

    @JvmStatic
    fun reNewLocalNote(ctx: Context, noteInfo: NoteInfo): Boolean {
        return NoteInfoUpdate.getInstance().reNewLocalNote(ctx, noteInfo)
    }

    @JvmStatic
    fun updateNote(ctx: Context, packet: Packet<*>, userName: String?): Boolean {
        return NoteInfoUpdate.getInstance().updateNote(ctx, packet, userName)
    }

    @JvmStatic
    fun updateNoteList(ctx: Context, packet: List<Packet<*>>, userName: String?): Boolean {
        return NoteInfoUpdate.getInstance().updateNoteList(ctx, packet, userName)
    }

    @JvmStatic
    fun updateNote(
        ctx: Context,
        packet: Packet<*>,
        userName: String?,
        isModify: Boolean
    ): Boolean {
        return NoteInfoUpdate.getInstance().updateNote(ctx, packet, userName, isModify)
    }

    @JvmStatic
    fun recoverNote(guid: String): Boolean {
        return NoteInfoRecover.getInstance().recoverNote(guid)
    }

    /*** query behaviors of note info  */
    @JvmStatic
    fun queryNoteInfoByGuid(guid: String): NoteInfo? {
        return NoteInfoQuery.getInstance().queryNoteInfoByGuid(guid)
    }

    /*** query behaviors of note info  */
    @JvmStatic
    fun queryNoteInfoByGlobleId(globleId: String): NoteInfo? {
        return NoteInfoQuery.getInstance().queryNoteInfoByGlobleId(globleId)
    }

    /*** query and update note info  */
    @JvmStatic
    fun queryAndUpdateNoteInfo(noteInfo: NoteInfo): Boolean {
        return NoteInfoQuery.getInstance().queryAndUpdateNoteInfo(noteInfo)
    }

    @JvmStatic
    fun queryAllNoteInfoByGuidSet(
        ctx: Context?,
        allNoteInfo: ArrayList<NoteInfo>,
        guidSet: Set<String>
    ) {
        NoteInfoQuery.getInstance().queryAllNoteInfoByGuidSet(ctx, allNoteInfo, guidSet)
    }

    @JvmStatic
    fun queryAllNoteInfoOfLoacalDirtyNote(allNoteInfo: ArrayList<NoteInfo>) {
        NoteInfoQuery.getInstance().queryAllNoteInfoOfLocalDirtyNote(allNoteInfo)
    }

    @JvmStatic
    fun queryLocalDirtyNotesCount(): Int {
        return NoteInfoQuery.getInstance().queryLocalDirtyNoteCount()
    }

    @JvmStatic
    fun queryAllNoteInfo(
        ctx: Context,
        allNoteInfo: ArrayList<NoteInfo>,
        isFilterDeleted: Boolean
    ) {
        NoteInfoQuery.getInstance().queryAllNoteInfo(ctx, allNoteInfo, isFilterDeleted)
    }

    @JvmStatic
    fun getNextAlarm(): Long {
        return NoteInfoQuery.getInstance().getNextAlarm()
    }

    @JvmStatic
    fun queryAlarmNoteInfo(
        ctx: Context,
        allNoteInfo: ArrayList<NoteInfo>,
        isFilterDeleted: Boolean
    ) {
        NoteInfoQuery.getInstance().queryAlarmNoteInfo(ctx, allNoteInfo, isFilterDeleted)
    }

    @JvmStatic
    fun queryNoteAttributes(
        ctx: Context,
        info: NoteInfo,
        isSortByCreated: Boolean,
        isFilterDeleted: Boolean
    ) {
        NoteInfoQuery.getInstance().queryNoteAttributes(ctx, info, isSortByCreated, isFilterDeleted)
    }

    @JvmStatic
    fun queryNoteAttributes(
        datas: ArrayList<NoteAttribute>,
        guid: String,
        isFilterDeleted: Boolean,
        isSortByCreated: Boolean
    ) {
        NoteInfoQuery.getInstance().queryNoteAttributes(datas, guid, isFilterDeleted, isSortByCreated)
    }

    @JvmStatic
    fun queryAllNotesCount(): Int {
        return NoteInfoQuery.getInstance().queryAllNotesCount()
    }

    @JvmStatic
    fun queryAllNotes(): List<Note> {
        return NoteInfoQuery.getInstance().getAllNote()
    }

    @JvmStatic
    fun queryAllRemindNotesCount(ctx: Context): Int {
        return NoteInfoQuery.getInstance().queryAllRemindNotesCount(ctx)
    }

    @JvmStatic
    fun queryAllKindsOfSkinCount(): HashMap<String, Int> {
        return NoteInfoQuery.getInstance().queryAllKindsOfSkinCount()
    }

    @JvmStatic
    fun queryNotesAttributes(attrGuid: String?): NotesAttribute? {
        return NoteInfoQuery.getInstance().queryNotesAttributes(attrGuid)
    }

    @JvmStatic
    fun updateNotesAttributes(attributes: NotesAttribute) {
        AppDatabase.getInstance().noteAttributeDao().update(attributes)
    }

    @JvmStatic
    fun queryEncryptedNotesCount(): Int {
        return NoteInfoQuery.getInstance().queryEncryptedNotesCount()
    }
}