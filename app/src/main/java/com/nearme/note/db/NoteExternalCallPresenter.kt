package com.nearme.note.db

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.core.content.FileProvider
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.logic.ThumbFileManager
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.model.ToDoRepository
import com.nearme.note.util.CloudSyncTrigger
import com.nearme.note.util.getBoolean
import com.nearme.note.util.getStringArrayList
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.statistic.StatisticsNoteCard
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

class NoteExternalCallPresenter(private val context: Context) {

    private data class CheckClickData(var job: Job? = null)

    companion object {
        const val TAG = "NoteExternalCallPresenter"
        private const val METHOD_CALL_FILE_URI_PERMISSION = "grant_file_uri_permission"
        private const val METHOD_CALL_TOP_LIST = "list_call_desc"
        private const val LIST_CALLING_COUNT = "list_call_count"
        private const val LIST_CALLING_PAGE = "list_call_page"
        private const val LIST_CALLING_RESULT = "list_call_result"
        private const val NOTE_ITEM_TITLE = "title"
        private const val NOTE_ITEM_IMG_PATH = "img_path"
        private const val LIST_CALLING_COUNT_DEFAULT = 10
        private const val AUTHORITY = BuildConfig.APPLICATION_ID + ".fileprovider"
        private const val METHOD_CALL_TODO_ITEM_CLICK = "todoRadioButtonClick"
        private const val DELAY_COMPLETE_TODO = 300L
        private const val REVOKE_PERMISSION = "revoke_permission"
        private const val FILE_URI_LIST = "file_uri_list"
        private const val SUCCESS_FILE_URI_LIST = "success_file_uri_list"
        private val mCheckBoxClickMap = ConcurrentHashMap<String, CheckClickData?>()
        private val safeGetCallingPackage = arrayOf("com.oplus.dmp")

        @JvmStatic
        fun makeUriWithPermission(context: Context, filePath: String, permissionPkg: ArrayList<String>): Uri? {
            if (TextUtils.isEmpty(filePath) || permissionPkg.isEmpty()) {
                AppLogger.BASIC.d(TAG, "makeUriWithPermission parameter error")
                return null
            }
            var uri: Uri? = null
            try {
                uri = FileProvider.getUriForFile(context, AUTHORITY, File(filePath))
            } catch (ex: IllegalArgumentException) {
                AppLogger.BASIC.e(TAG, "makeUriWithPermission error e = " + ex.message)
            } catch (e: java.lang.Exception) {
                AppLogger.BASIC.e(TAG, "makeUriWithPermission error !")
            }

            if (uri != null) {
                // just grant authorization to DESK APP
                permissionPkg.forEach { pkg ->
                    if (!TextUtils.isEmpty(pkg)) {
                        context.grantUriPermission(pkg, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
                    }
                }
            }
            return uri
        }

        @SuppressLint("WrongConstant")
        @JvmStatic
        fun revokeUriPermission(context: Context, uri: Uri?) {
            if (uri != null) {
                kotlin.runCatching {
                    context.revokeUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
                }
            }
        }

        @JvmStatic
        fun sendDataChangeNotify(context: Context) {
            try {
                context.contentResolver.notifyChange(NotesProvider.DATA_CHANGE_URI, null)
            } catch (ignore: java.lang.Exception) {
                AppLogger.BASIC.e("sendDataChangeNotify", ignore.message)
            }
        }
    }

    /**
     * 负一屏展示便签卡片走这个方法
     * data provider for external's calling
     */
    fun call(method: String, arg: String?, extras: Bundle?, callingPackage: String): Bundle? {
        AppLogger.BASIC.d(TAG, "call method = $method arg = $arg, extras: $extras")
        if (null == extras) {
            AppLogger.BASIC.e(TAG, "call method input param error !")
            return null
        }
        return if (METHOD_CALL_TOP_LIST == method) {
            callListDesc(extras, method)
        } else if (METHOD_CALL_TODO_ITEM_CLICK == method) {
            callToDoItemClick(extras)
        } else if (METHOD_CALL_FILE_URI_PERMISSION == method) {
            callGrantFileUriPermission(extras, callingPackage)
        } else {
            TodoProviderHelper.call(context, method, arg, extras, callingPackage)
        }
    }

    private fun callToDoItemClick(extras: Bundle): Bundle {
        val bundle = Bundle()
        val isChecked = extras.getString("isChecked", "false").toBoolean()
        val localIdAndWidgetCode = extras.getString("localIdAndWidgetCode", "")
        val toDoViewId = extras.getString("viewId", "")

        toDoViewId?.let { viewId ->
            GlobalScope.launch(Dispatchers.IO) {
                val list = localIdAndWidgetCode.split(",")
                var localId = ""
                var widgetCode = ""
                if (list.isNotEmpty()) {
                    localId = list[0]
                }
                if (list.size >= 2) {
                    widgetCode = list[1]
                }
                var checkClickData = mCheckBoxClickMap[localIdAndWidgetCode]
                if (checkClickData == null) {
                    checkClickData = CheckClickData(null)
                    mCheckBoxClickMap[localIdAndWidgetCode] = checkClickData
                }
                val instance = NoteCardWidgetProvider.instance
                AppLogger.BASIC.d(TAG, "isChecked: $isChecked viewId: $viewId, localIdAndWidgetCode: $localIdAndWidgetCode")
                try {
                    if (!isChecked) {
                        instance.removeData(localIdAndWidgetCode)
                        checkClickData.job?.cancel()
                    } else {
                        instance.setData(localIdAndWidgetCode, isChecked)
                        checkClickData.job = launch {
                            delay(DELAY_COMPLETE_TODO)
                            AppLogger.BASIC.d(TAG, "after delay isChecked: $isChecked, localId: $localId")
                            if (isChecked && !TextUtils.isEmpty(localId)) {
                                completeTodo(localId, localIdAndWidgetCode)
                                mCheckBoxClickMap.remove(localIdAndWidgetCode)
                                val cardType = extras.getString(StatisticsNoteCard.EXTRA_CARD_TYPE, "")
                                StatisticsNoteCard.setEventCompleteTodo(context, cardType)
                            }
                        }
                    }
                } catch (e: Exception) {
                    AppLogger.BASIC.e(TAG, "callToDoItemClick error: ${e.message}")
                }
            }
        }
        return bundle
    }

    private fun completeTodo(localId: String, localIdAndWidgetCode: String) {
        val instance = NoteCardWidgetProvider.instance
        val uuid = UUID.fromString(localId)
        val toDo = ToDoRepository.getInstance().getByLocalIdSync(uuid)
        toDo?.let {
            val callback = object : ToDoRepository.ResultCallback<Int> {
                override fun onResult(result: Int) {
                    instance.removeData(localIdAndWidgetCode)
                    instance.postUIToCard(false)
                    CloudSyncTrigger.sendDataChangedBroadcast(appContext)
                    WidgetUtils.sendTodoDataChangedBroadcast(appContext)
                }
            }
            ToDoRepository.getInstance().updateFinishTime(it, callback)
        }

    }

    private fun callListDesc(extras: Bundle, method: String): Bundle {
        val bundle = Bundle()
        val page = extras.getInt(LIST_CALLING_PAGE)
        if (page > 0) {
            var count = extras.getInt(LIST_CALLING_COUNT)
            count = if (count > 0) count else LIST_CALLING_COUNT_DEFAULT
            try {
                // move to target page index
                val notes: List<RichNoteWithAttachments> = queryAllViewableNotes()
                val start = (page - 1) * LIST_CALLING_COUNT_DEFAULT
                val array = JSONArray()
                var i = start
                while (i < notes.size && i < start + count) {
                    val note = notes[i]
                    array.put(packetRichNote(note))
                    i++
                }
                val result = array.toString()
                AppLogger.BASIC.d(TAG, "callListDesc result = $result")
                bundle.putString(LIST_CALLING_RESULT, result)
                bundle.putBoolean(method, array.length() > 0)
            } catch (e: Exception) {
                AppLogger.BASIC.e(TAG, "callListDesc error = " + e.message)
            }
        }
        return bundle
    }

    private fun queryAllViewableNotes(): List<RichNoteWithAttachments> {
        return AppDatabase.getInstance().richNoteDao().getAllViewableRichNotesWithAttachments(FolderInfo.FOLDER_GUID_ENCRYPTED)
    }

    @Throws(JSONException::class)
    private fun packetRichNote(noteWithAttachments: RichNoteWithAttachments): JSONObject {
        val note = noteWithAttachments.richNote

        val noteObj = JSONObject()

        // add note's type
        val type = noteWithAttachments.getType()
        noteObj.put(NotesProvider.COL_TYPE, type)

        // add note's counts of attachment
        val size = noteWithAttachments.attachments?.size ?: 0
        noteObj.put(NotesProvider.COL_ATTR_COUNT, size)

        val title = if (type == RichNoteWithAttachments.TYPE_IMAGE && note.title.isNullOrEmpty() && note.text.isEmpty()) {
            getAttrDefTitle(type)
        } else {
            note.title ?: ""
        }
        // add note's title of text
        noteObj.put(NOTE_ITEM_TITLE, title)
        // add note's description
        noteObj.put(NotesProvider.COL_DESCRIPTION, note.text)

        // add note's update time
        noteObj.put(NotesProvider.COL_UPDATED, note.updateTime)

        // add note's guidId
        val guid = note.localId
        noteObj.put(NotesProvider.COL_GUID, guid)

        // add note's image path
        if (type == RichNoteWithAttachments.TYPE_IMAGE) {
            val attachmentId = noteWithAttachments.attachments!![0].attachmentId
            val path = ThumbFileManager.getThumbFilePathInData(context, guid, attachmentId)
            val packList = ArrayList<String>()
            packList.add("com.coloros.assistantscreen")
            packList.add("com.oppo.launcher")
            packList.add("com.android.launcher")
            val imgUri = makeUriWithPermission(context, path, packList)
            noteObj.put(NOTE_ITEM_IMG_PATH, imgUri ?: "")
        }
        return noteObj
    }

    private fun getAttrDefTitle(noteType: Int) = when (noteType) {
        RichNoteWithAttachments.TYPE_IMAGE -> {
            context.getString(R.string.memo_picture)
        }
        else -> {
            ""
        }
    }

    private fun callGrantFileUriPermission(extras: Bundle, callingPackage: String): Bundle {
        AppLogger.BASIC.d(TAG, "callGrantFileUriPermission:$callingPackage")
        val successList = arrayListOf<String>()
        if (safeGetCallingPackage.contains(callingPackage)) { // 判断是否是白名单应用
            val uriList = getStringArrayList(FILE_URI_LIST, extras, successList) //获取Uri列表
            val revokePermission = getBoolean(REVOKE_PERMISSION, extras, false) // 是否撤销授权
            AppLogger.BASIC.d(TAG, "uriList size:${uriList.size},revokePermission:$revokePermission")
            uriList.forEach {
                val uri = Uri.parse(it)
                if (uri.authority == AUTHORITY) {
                    if (revokePermission) {
                        context.revokeUriPermission(callingPackage, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    } else {
                        context.grantUriPermission(callingPackage, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    }
                    successList.add(it)
                } else {
                    AppLogger.BASIC.d(TAG, "is not note file:$uri")
                }
            }
        }
        val result = Bundle()
        result.putStringArrayList(SUCCESS_FILE_URI_LIST, successList)
        return result
    }
}