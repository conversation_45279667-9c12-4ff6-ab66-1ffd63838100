/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoDelete.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2017/07/26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>       2017/07/26        build this module
 * W9005794                2023/10/26        change java to kotlin
 **********************************************************************************/
package com.nearme.note.db.entity

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NotesProvider
import com.oplus.cloud.sync.note.SyncDataProvider
import com.oplus.note.logger.AppLogger

class NoteInfoDelete private constructor() {
    companion object {
        private const val TAG = "NoteInfoDelete"

        @JvmStatic
        fun getInstance(): NoteInfoDelete {
            return NoteInfoDeleteHolder.instance
        }
    }

    private object NoteInfoDeleteHolder {
        val instance = NoteInfoDelete()
    }

    fun deleteInvalidNote() {
        AppDatabase.getInstance().noteDao().findInvalidNotes().forEach {
            it.guid?.let { it1 -> deleteNote(it1, true) }
        }
    }

    fun deleteNote(ctx: Context?, guid: String): Boolean {
        val count = AppDatabase.getInstance().noteDao().updateNoteRecycleTimeAndState(
            guid,
            System.currentTimeMillis(),
            NoteInfo.STATE_MODIFIED
        )
        notifyDataChanged()
        return count > 0
    }

    private inline fun Boolean.doPositive(block: () -> Unit): Boolean {
        if (this) {
            block()
        }
        return this
    }

    fun deleteNote(guid: String, isReal: Boolean): Boolean {
        AppLogger.CLOUD.d(TAG, "[DBUtil] deleteNote:$isReal")
        return if (isReal) {
            val result = AppDatabase.getInstance().noteDao().deleteNoteByGuid(guid) > 0
            result.doPositive {
                deleteNoteAttributes(guid)
                notifyDataChanged()
            }
        } else {
            val note =
                AppDatabase.getInstance().noteDao().findByGuid(guid) ?: return false
            note.deleted = NoteInfo.STATE_MARK_DELETED.toInt()
            note.state = NoteInfo.STATE_MODIFIED
            val result = AppDatabase.getInstance().noteDao().updateNote(note) > 0
            result.doPositive {
                deleteNoteAttributes(guid)
            }
        }
    }

    fun deleteNote(
        ctx: Context?,
        globalId: String?,
        isReal: Boolean,
        isRecovery: Boolean
    ): String? {
        if (ctx == null) {
            AppLogger.CLOUD.e(TAG, "context is null")
            return ""
        }

        if (globalId.isNullOrEmpty()) {
            AppLogger.CLOUD.e(TAG, "global is null or empty")
            return ""
        }
        val noteDao = AppDatabase.getInstance().noteDao()
        val note = noteDao.findbyGlobalId(globalId) ?: return null
        val guid = note.guid
        AppLogger.CLOUD.d(TAG, "[DBUtil] deleteNote:$isReal, guid: $guid, globalId: $globalId")
        isReal.doPositive {
            if (guid.isEmpty()) {
                noteDao.deleteNoteByGlobalGuid(note.globalId)
            } else {
                noteDao.deleteNoteByGuid(guid)
                deleteNoteAttributes(guid)
            }
        }
        (!isReal).doPositive {
            if (guid.isEmpty()) {
                note.deleted = NoteInfo.STATE_MARK_DELETED.toInt()
                noteDao.updateNote(note)
            } else {
                deleteNoteAttributes(note.guid)
                note.deleted = NoteInfo.STATE_MARK_DELETED.toInt()
                noteDao.updateNote(note)
            }
        }
        notifyDataChanged()
        return guid
    }

    fun clearSyncStateInfo(ctx: Context?, userName: String?) {
        // clean the same sync field on the list
        runCatching {
            AppDatabase.getInstance().commonDao().clearSyncStateInfo(userName)
            notifyDataChanged()
        }.onFailure {
            AppLogger.BASIC.e(TAG, "clearSyncStateInfo failed: ${it.message}")
        }
    }

    fun cleanDatabase(ctx: Context?): Boolean {
        if (ctx == null) {
            return false
        }
        return runCatching {
            AppDatabase.getInstance().commonDao().deleteAllWithoutFolder()
            notifyDataChanged()
            val state = ctx.contentResolver.update(
                SyncDataProvider.CONTENT_URI_CLEAN_SYNC_STATE,
                null,
                null,
                null
            )
            state != -1
        }.onFailure {
            AppLogger.CLOUD.e(TAG, "[DBUtil] cleanDatabase error = ${it.message}")
        }.getOrDefault(false)
    }

    @VisibleForTesting
    internal fun deleteNoteAttributes(guid: String): Int {
        AppLogger.CLOUD.d(TAG, "[DBUtil] deleteNoteAttributes guid:$guid")
        notifyDataChanged()
        return AppDatabase.getInstance().noteAttributeDao().deletebyNoteGuid(guid)
    }

    @VisibleForTesting
    internal fun notifyDataChanged() {
        appContext.contentResolver.apply {
            notifyChange(NotesProvider.DATA_CHANGE_URI, null)
            notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI, null)
            notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI_NEW, null)
        }
    }
}