/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - FolderSyncUtils
 ** Description:
 **         v1.0:   Create FolderSyncUtils file
 **
 ** Version: 1.0
 ** Date: 2023/11/03
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/11/3   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.db

import com.google.gson.Gson
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderExtra
import com.nearme.note.util.NoteBookHeadViewUtils
import com.oplus.cloudkit.transformer.FolderSyncData
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.util.Constants.Companion.FOLDER_SYNC_OFF
import com.oplus.cloudkit.util.Constants.Companion.FOLDER_SYNC_ON
import com.oplus.note.logger.AppLogger

object FolderSyncSwitchManager {


    /**
     * init sync switch of [FOLDER_GUID_CALL_SUMMARY] and [FOLDER_GUID_QUICK] .
     * store them in Shared preference
     */
    @JvmStatic
    fun initRememberFolderSync() {
        setInitialSyncSwitch(FolderInfo.FOLDER_GUID_CALL_SUMMARY, FOLDER_SYNC_OFF)
        setInitialSyncSwitch(FolderInfo.FOLDER_GUID_QUICK, FOLDER_SYNC_ON)
    }

    /**
     * set initial sync switch state when create call summary folder or create quick note.
     */
    @JvmStatic
    fun setInitialSyncSwitch(guid: String, state: Int) {
        if (guid != FolderInfo.FOLDER_GUID_QUICK && guid != FolderInfo.FOLDER_GUID_CALL_SUMMARY) {
            AppLogger.BASIC.d(TAG, "create folder not quick or call summary.")
            return
        }
        setFolderSyncSwitch(guid, state)
    }


    /**
     * query sync switch state of folder by [guid]
     * @param guid  guid of folder
     * @return sync switch state of folder
     * [FOLDER_SYNC_ON] is on else is off
     */
    @JvmStatic
    fun getFoldSyncSwitch(guid: String): Int {

        if (guid.isEmpty()) {
            AppLogger.BASIC.e(TAG, "guid is null")
            return -1
        }

        val folder = AppDatabase.getInstance().foldersDao().findByGuid(guid)
        if (folder != null) {
            return folder.extra?.getSyncState() ?: FOLDER_SYNC_ON
        } else {
            FOLDER_SYNC_ON
        }

        return -1
    }


    /**
     * set sync switch of folder
     * @param guid folder guid
     * @param switch  state must be [Constants.FOLDER_SYNC_ON] or [Constants.FOLDER_SYNC_OFF]
     * @param fromCloudMerge 来自云端同步下来的修改, 不能将状态改成 FOLDER_STATE_MODIFIED 否则又会上传一次脏数据
     *
     * @return success of fail
     */
    @JvmStatic
    fun setFolderSyncSwitch(guid: String, switch: Int, fromCloudMerge: Boolean = false) {
        if (guid.isEmpty()) {
            AppLogger.BASIC.e(TAG, "guid is null")
            return
        }

        val folder = AppDatabase.getInstance().foldersDao().findByGuid(guid)
        if (folder == null) {
            return
        } else {
            if (folder.extra != null) {
                folder.extra.setSync(switch)
            } else {
                val folderExtra = FolderExtra.create(null)
                folderExtra.setSync(switch)
                folder.extra = folderExtra
            }
            if (folder.state != FolderInfo.FOLDER_STATE_NEW) {
                if (fromCloudMerge) {
                    folder.state = FolderInfo.FOLDER_STATE_UNCHANGE
                } else {
                    folder.state = FolderInfo.FOLDER_STATE_MODIFIED
                }
            }
            AppDatabase.getInstance().foldersDao().updateFolder(folder)
            NoteBookHeadViewUtils.clearIgnoreState(MyApplication.appContext, folder.guid)
        }
    }


    fun getRememberSyncFolderStateForBackup(): String {

        val list = mutableListOf<Folder>()
        val syncData = mutableListOf<FolderSyncData?>()
        list.addAll(
            AppDatabase.getInstance().foldersDao().getFoldersByIdList(
                listOf(
                    FolderInfo.FOLDER_GUID_QUICK,
                    FolderInfo.FOLDER_GUID_CALL_SUMMARY,
                    FolderInfo.FOLDER_GUID_NO_GUID
                )
            )
        )
        list.forEach { folder ->
            if (folder.state == FolderInfo.FOLDER_STATE_MODIFIED
                || folder.state == FolderInfo.FOLDER_STATE_NEW) {
                val data = FolderSyncData(folder.guid, folder.extra.getSyncState())
                syncData.add(data)
            }
        }

        return if (syncData.isEmpty()) {
            ""
        } else {
            Gson().toJson(syncData)
        }
    }

    fun getLocalRememberSyncFolderState(): String {
        val list = mutableListOf<Folder>()
        val syncData = mutableListOf<FolderSyncData?>()
        list.addAll(
            AppDatabase.getInstance().foldersDao().getFoldersByIdList(
                listOf(
                    FolderInfo.FOLDER_GUID_QUICK,
                    FolderInfo.FOLDER_GUID_CALL_SUMMARY
                )
            )
        )
        list.forEach { folder ->
            val data = FolderSyncData(folder.guid, folder.extra.getSyncState())
            syncData.add(data)
        }

        return if (syncData.isEmpty()) {
            ""
        } else {
            Gson().toJson(syncData)
        }
    }


    fun setRememberSyncFolderSyncState(data: List<FolderSyncData>?) {
        data?.forEach {
            val foldId = it.folderId
            setFolderSyncSwitch(foldId, it.sync, fromCloudMerge = true)
        }
    }

    private const val TAG = "FolderSyncSwitchManager"
}