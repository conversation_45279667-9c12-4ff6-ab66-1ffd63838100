package com.nearme.note.db.daos;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;
import androidx.sqlite.db.SimpleSQLiteQuery;

import com.nearme.note.data.NoteAttribute;
import com.nearme.note.db.entities.NotesAttribute;
import com.nearme.note.util.SqlUtils;

import java.util.List;
import java.util.Set;

@Dao
public abstract class NoteAttributeDao extends BaseDao {

    @Transaction
    public int deleteByNoteGuids(Set<String> noteGuids) {
        String ids = SqlUtils.joinIds(noteGuids);
        String sql = "DELETE FROM notes_attributes WHERE note_guid IN (" + ids + ")";
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Transaction
    public int deleteByNoteGuids(List<String> noteGuids) {
        String ids = SqlUtils.joinIds(noteGuids);
        String sql = "DELETE FROM notes_attributes WHERE note_guid IN (" + ids + ")";
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Query("DELETE FROM notes_attributes")
    public abstract int deleteAll();

    @Query("SELECT * FROM notes_attributes")
    public abstract List<NotesAttribute> getAll();

    @Query("DELETE FROM notes_attributes WHERE note_guid = :noteGuid")
    public abstract int deletebyNoteGuid(String noteGuid);

    @Insert
    public abstract long insert(NotesAttribute attribute);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract long[] insert(List<NotesAttribute> attributeList);

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes where note_guid = :guid and type = :type")
    public abstract List<NotesAttribute> findByGuidAndType(String guid, int type);

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes where filename = :filename")
    public abstract NotesAttribute findByFilename(String filename);

    @Update
    public abstract int update(NotesAttribute attribute);

    @Query("delete from notes_attributes where filename = :filename")
    public abstract void deleteByFilename(String filename);

    @Transaction
    public int deleteByFilename(List<String> contentList) {
        String guidsStr = SqlUtils.joinIds(contentList);
        String sql = "Delete from notes_attributes where filename in (" + guidsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes where note_guid = :noteGuid")
    public abstract List<NotesAttribute> findByNoteGuid(String noteGuid);

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes where note_guid = :noteGuid order by updated asc")
    public abstract List<NotesAttribute> findByNoteGuidOrderByUpdated(String noteGuid);

    @Query("update notes_attributes set attachment_sync_url = null , attachment_md5 = null , sync_data1 = 0")
    public abstract void clearAllAttrsState();

    @Query("update notes_attributes set note_guid = :newGuid where note_guid = :oldGuid")
    public abstract void updateNewGuid(String newGuid, String oldGuid);

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes " +
            "where type != " + NoteAttribute.TYPE_TEXT_CONTENT + " and attachment_md5 is null order by note_guid")
    public abstract List<NotesAttribute> findByTypeContentMD5();

    @Query("select notes_attributes._id, notes_attributes.note_guid, notes_attributes.type, " +
            "notes_attributes.version, notes_attributes.updated, notes_attributes.para, " +
            "notes_attributes.state, notes_attributes.attachment_md5, " +
            "notes_attributes.attachment_sync_url, notes_attributes.sync_data1, " +
            "notes_attributes.width, notes_attributes.height, " +
            "substr(notes_attributes.filename, 0, 40000) AS 'filename' " +
            "from notes_attributes where _id = :id")
    public abstract NotesAttribute findById(long id);

    @Update
    public abstract void updateAttributes(List<NotesAttribute> updateAttrs);

    @Query("update notes_attributes set attachment_sync_url = :url,attachment_md5 = :md5,sync_data1 = :syncData")
    public abstract void updateNotesUrlMd5Syncdata(String url, String md5, String syncData);

}
