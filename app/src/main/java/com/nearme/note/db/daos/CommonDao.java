package com.nearme.note.db.daos;

import android.text.TextUtils;

import androidx.room.Dao;
import androidx.room.Transaction;

import com.nearme.note.MyApplication;
import com.nearme.note.activity.edit.NoteEntityUtils;
import com.nearme.note.data.NoteAttribute;
import com.nearme.note.data.NoteInfo;
import com.nearme.note.db.AppDatabase;
import com.nearme.note.db.entities.Note;
import com.nearme.note.db.entities.NotesAttribute;
import com.nearme.note.db.entities.Word;
import com.nearme.note.logic.ThumbFileManager;
import com.oplus.note.repo.note.entity.RichNoteWithAttachments;
import com.nearme.note.util.FileUtil;
import com.nearme.note.util.RandomGUID;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.R;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import kotlin.io.FilesKt;

@Dao
public abstract class CommonDao {

    private static final String TAG = "CommonDao";

    @Transaction
    public void deleteAllWithoutFolder() {
        AppDatabase.getInstance().noteDao().deleteAll();
        AppDatabase.getInstance().noteAttributeDao().deleteAll();
        AppDatabase.getInstance().alarmNoteDao().deleteAll();
        AppDatabase.getInstance().wordsDao().deleteAll();
    }

    @Transaction
    public void clearSyncStateInfo(String userName) {
        AppDatabase.getInstance().noteDao().clearAllNoteSyncState(userName);
        AppDatabase.getInstance().noteAttributeDao().clearAllAttrsState();
    }

    @Transaction
    public void updateLocalNoteGuid(NoteInfo noteInfo) {
        String oldGuid = noteInfo.getGuid();
        String newGuid = RandomGUID.createGuid();
        AppLogger.CLOUD.i(TAG, "insertConflictNote local record rename newGuid = " + newGuid);

        Note note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.getGuid());
        if (note == null) {
            return;
        }
        note.guid = newGuid;
        note.globalId = null;
        note.state = NoteInfo.STATE_NEW;
        note.noteFolder = noteInfo.getFolderName();
        note.noteFolderGuid = noteInfo.getFolderGuid();
        AppDatabase.getInstance().noteDao().updateNote(note);

        AppDatabase.getInstance().noteAttributeDao().updateNewGuid(newGuid, oldGuid);
        AppDatabase.getInstance().alarmNoteDao().updateNewGuid(newGuid, oldGuid);
        AppDatabase.getInstance().wordsDao().updateNewGuid(newGuid, oldGuid);

        File originalExternalDirectory = new File(ThumbFileManager.getFolderPathInSD(noteInfo.getGuid()));
        File originalInnerDirectory = new File(FileUtil.getFolderPathInData(MyApplication.getAppContext(), noteInfo.getGuid()));
        File targetExternalDirectory = new File(ThumbFileManager.getFolderPathInSD(newGuid));
        File targetInnerDirectory = new File(FileUtil.getFolderPathInData(MyApplication.getAppContext(), newGuid));
        try {
            if (originalExternalDirectory.exists()) {
                FilesKt.copyRecursively(originalExternalDirectory, targetExternalDirectory, true, (file, e) -> null);
            }
            if (originalInnerDirectory.exists()) {
                FilesKt.copyRecursively(originalInnerDirectory, targetInnerDirectory, true, (file, e) -> null);
            }
            FileUtil.deleteDirectory(originalExternalDirectory);
            FileUtil.deleteDirectory(originalInnerDirectory);
        } catch (Exception e) {
            AppLogger.CLOUD.e(TAG, "insertConflictNote update local record exception 1 = " + e.getMessage());
        }
    }

    @Transaction
    public void updateNoteAndAttributes(List<NotesAttribute> updateAttrs, List<Note> notes) {
        AppDatabase.getInstance().noteDao().updateNotes(notes);
        AppDatabase.getInstance().noteAttributeDao().updateAttributes(updateAttrs);
    }

    @Transaction
    public void restoreSyncInfo() {
        AppDatabase.getInstance().noteDao().updateNotesStateAndGlobalId(NoteInfo.STATE_NEW, null);
        AppDatabase.getInstance().noteAttributeDao().updateNotesUrlMd5Syncdata(null, null, "0");
    }

    @Transaction
    public boolean transformNoteToRichNote(String guid, RichNoteWithAttachments richNoteWithAttachments) {
        boolean success = false;
        AppDatabase.getInstance().richNoteDao().insert(richNoteWithAttachments);
        HashSet<String> guids = new HashSet<>();
        guids.add(guid);
        AppDatabase.getInstance().noteDao().setNotesDeletedMark(guids);
        AppDatabase.getInstance().noteAttributeDao().deletebyNoteGuid(guid);
        success = AppDatabase.getInstance().wordsDao().deleteByNoteGuid(guid) > 0;
        return success;
    }

    @Transaction
    public void saveNoteAttributes(NoteInfo noteInfo, boolean isSync) {
        AppLogger.CLOUD.d(TAG, "[DBUtil] saveNoteAttributes");

        int size = noteInfo.getAttributesSize();
        if (size > 0) {
            // Cloud sync note, first delete all attribute by GUID, then insert.
            if (isSync) {
                AppDatabase.getInstance().noteAttributeDao().deletebyNoteGuid(noteInfo.getGuid());
            }
            String content = "";
            long updatetime = (isSync ? 0 : noteInfo.getUpdated());
            int state = 0;
            ArrayList<String> redundancyText = new ArrayList<String>();
            boolean isUpdate = false;
            final Collection<NoteAttribute> attributes = noteInfo.getAttributesIncWholeContent();
            for (NoteAttribute attr : attributes) {
                switch (attr.getOperation()) {
                    case NoteAttribute.OP_ADD: {
                        NotesAttribute attribute = new NotesAttribute();
                        attribute.noteGuid = noteInfo.getGuid();
                        attribute.type = attr.getType();
                        attribute.filename = attr.getContent();
                        attribute.noteAttrOwner = attr.getOwner();
                        attribute.state = attr.getState();
                        attribute.attrCreated = new Date(attr.getCreated());
                        attribute.width = attr.getWidth();
                        attribute.height = attr.getHeight();

                        if (isSync) {
                            attribute.attachmentSyncUrl = attr.getAttachmentSyncUrl();
                            attribute.attachmentMd5 = attr.getAttachmentMd5();
                        }

                        if (attr.getParam() != null) {
                            attribute.para = attr.getParam();
                        }
                        if (attr.getType() == NoteAttribute.TYPE_TEXT_CONTENT) {
                            content = attr.getContent();
                            state = attr.getState();
                            if (isSync) {
                                updatetime = attr.getCreated();
                            }
                        } else {
                            redundancyText.add(attr.getContent());
                        }
                        AppDatabase.getInstance().noteAttributeDao().insert(attribute);
                        break;
                    }
                    case NoteAttribute.OP_MODIFY: {
                        List<NotesAttribute> attributeList = AppDatabase.getInstance().noteAttributeDao().findByGuidAndType(noteInfo.getGuid(), NoteAttribute.TYPE_TEXT_CONTENT);
                        if ((attributeList != null) && !attributeList.isEmpty()) {
                            for (NotesAttribute attribute : attributeList) {
                                if (attribute == null) {
                                    continue;
                                }
                                if (attr.getType() == NoteAttribute.TYPE_TEXT_CONTENT) {
                                    attribute.state = attr.getState();
                                    attribute.type = attr.getType();
                                    isUpdate = true;
                                    String value = attr.getContent();
                                    state = attr.getState();
                                    attribute.filename = value;
                                    content = attr.getContent();
                                    if (isSync) {
                                        updatetime = attr.getCreated();
                                    }
                                } else {
                                    attribute = AppDatabase.getInstance().noteAttributeDao().findByFilename(attr.getContent());
                                    attribute.state = attr.getState();
                                    attribute.type = attr.getType();
                                    if (isSync) {
                                        attribute.attachmentSyncUrl = attr.getAttachmentSyncUrl();
                                        attribute.attachmentMd5 = attr.getAttachmentMd5();
                                    }
                                    redundancyText.add(attr.getContent());
                                }
                                AppDatabase.getInstance().noteAttributeDao().update(attribute);
                            }
                        }
                        break;
                    }
                    case NoteAttribute.OP_DELETE: { // refrence:
                        // NoteInfo.deleteFilesOfDeletedStateAttr
                        AppDatabase.getInstance().noteAttributeDao().deleteByFilename(attr.getContent());
                        if (attr.getType() == NoteAttribute.TYPE_TEXT_CONTENT) {
                            AppDatabase.getInstance().wordsDao().deleteByNoteGuid(noteInfo.getGuid());
                        }
                        redundancyText.add(attr.getContent());
                        break;
                    }
                    default:
                        redundancyText.add(attr.getContent());
                        break;
                }
            }
            String title = noteInfo.getExtra().getTitle();
            if (!TextUtils.isEmpty(title)) {
                content = title + "\n" + content;
            }
            if (!TextUtils.isEmpty(content)) {
                for (String rt : redundancyText) {
                    content = content.replace(NoteInfo.DIVISION + rt + NoteInfo.DIVISION, "\n");
                }
                if ((!isSync) && (NoteEntityUtils.isNullOrEmpty(content))) {
                    content = MyApplication.getApplication().getResources().getString(R.string.memo_picture);
                }
                AppDatabase.getInstance().wordsDao().deleteByNoteGuid(noteInfo.getGuid());
                Word word = new Word();
                word.content = content;
                word.state = state;
                word.updated = new Date(updatetime);
                word.noteGuid = noteInfo.getGuid();
                AppDatabase.getInstance().wordsDao().insert(word);
            }
        }
    }

}
