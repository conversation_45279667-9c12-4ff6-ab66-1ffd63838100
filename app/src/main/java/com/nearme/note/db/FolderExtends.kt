/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - FolderExtends
 ** Description:
 **         v1.0:   Extension Methods for [com.oplus.note.repo.note.entity.Folder]
 **
 ** Version: 1.0
 ** Date: 2023/10/26
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/10/26   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.db

import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.cloud.agent.BaseSyncAgent.FolderBean

/**
 * 加密笔记本为Encrypted == 1 且非隐私笔记本的笔记本
 */
fun Folder?.isEncryptFolder(): Boolean {
    return this?.isEncrypted == true && this.guid != FolderInfo.FOLDER_GUID_ENCRYPTED
}

fun Folder?.isNormalFolder(): Boolean {
    return this?.isEncrypted == false && this.guid != FolderInfo.FOLDER_GUID_ENCRYPTED
}

/**
 * 是否是旧的隐私笔记本
 */
fun Folder?.isPrivateFolder(): Boolean {
    return this?.guid == FolderInfo.FOLDER_GUID_ENCRYPTED
}

/**
 * Are local modifications later than cloud modifications
 * @param other 对比的云端FolderBean
 * @return 本地修改是否比云端更晚
 */
fun Folder.isLatestChangeThan(other: FolderBean): Boolean {

    val localModify = this.modifyTime?.time ?: 0L
    val otherModify = other.mModifyTime

    return localModify > otherModify
}

/**
 * 笔记本是否在本地已加密
 * pre = 未加密
 * encrypt = 加密
 */
fun Folder.isEncryptLocal(): Boolean {
    return ((this.encrypted == FolderInfo.FOLDER_ENCRYPTED) &&
        (this.encryptedPre == FolderInfo.FOLDER_UNENCRYPTED) &&
        (this.guid != FolderInfo.FOLDER_GUID_ENCRYPTED))
}


/**
 * 笔记本是否在本地已解密
 * pre = 加密
 * encrypt = 未加密
 */
fun Folder.isDecryptLocal(): Boolean {
    return ((this.encrypted == FolderInfo.FOLDER_UNENCRYPTED) && (this.encryptedPre == FolderInfo.FOLDER_ENCRYPTED))
}

/**
 * 笔记本是否本地操作过加解密
 * 原隐私笔记本一直时满足encrypted ！= encrypted_pre，故需要排除
 */
fun Folder.localEncryptChanged(): Boolean {
    return this.encrypted != this.encryptedPre && this.guid != FolderInfo.FOLDER_GUID_ENCRYPTED
}

