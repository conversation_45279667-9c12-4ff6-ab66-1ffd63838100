/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  BaseDao.java
 * * Description: BaseDao.java
 * * Version: 1.0
 * * Date : 2020/1/9
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/9      1.0    build this module
 ****************************************************************/

package com.nearme.note.db.daos;

import androidx.room.RawQuery;
import androidx.sqlite.db.SupportSQLiteQuery;

public abstract class BaseDao {

    @RawQuery
    public abstract int executeSqlReturnInt(SupportSQLiteQuery query);
}
