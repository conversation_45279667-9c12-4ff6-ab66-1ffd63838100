/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd. * VENDOR_EDIT * File: -
 * NotesProviderPresenter.java * Description: NotesProvider control * Version: 1.0 * Date :
 * 2017/07/26 * Author: <EMAIL> * * ---------------------Revision History:
 * --------------------- * <author> <data> <version> <desc> * <EMAIL> 2017/07/26 build
 * this module
 *
 * OPLUS Java File Skip Rule:ParameterNumber
 ****************************************************************/
package com.nearme.note.db;

import static com.nearme.note.db.NotesProvider.NOTE_DATA_CHANGE_URI;
import static com.nearme.note.db.NotesProvider.NOTE_DATA_CHANGE_URI_NEW;
import static com.nearme.note.db.NotesProvider.TODO_DATA_CHANGE_URI;
import static com.nearme.note.db.NotesProvider.TODO_DATA_CHANGE_URI_NEW;

import android.annotation.SuppressLint;
import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.OperationApplicationException;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteQueryBuilder;

import com.coui.appcompat.version.COUIVersionUtil;
import com.nearme.note.MyApplication;
import com.nearme.note.activity.edit.NoteEntityUtils;
import com.nearme.note.appwidget.WidgetUtils;
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider;
import com.nearme.note.common.feedbacklog.FeedbackLog;
import com.nearme.note.common.feedbacklog.ToDoLogger;
import com.nearme.note.data.FingerNoteData;
import com.nearme.note.data.HandWritingData;
import com.nearme.note.data.NoteAttribute;
import com.nearme.note.data.NoteInfo;
import com.nearme.note.logic.ThumbFileConstants;
import com.nearme.note.logic.ThumbFileManager;
import com.nearme.note.model.RichNoteRepository;
import com.nearme.note.model.RichNoteTransformer;
import com.nearme.note.remind.RepeatDataHelper;
import com.nearme.note.remind.RepeatManage;
import com.nearme.note.skin.SkinData;
import com.nearme.note.util.AlarmUtils;
import com.nearme.note.util.CloudSyncTrigger;
import com.nearme.note.util.ConfigUtils;
import com.nearme.note.util.FileUtil;
import com.nearme.note.util.RandomGUID;
import com.nearme.note.util.StatisticsUtils;
import com.oplus.cloud.status.Device;
import com.oplus.cloud.sync.richnote.RichNoteFactory;
import com.oplus.note.R;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.oplus.note.repo.note.entity.RichNote;
import com.oplus.note.repo.todo.ToDoExtra;
import com.oplus.note.repo.todo.entity.ToDo;
import com.oplus.todo.search.TodoSearchManager;

import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArraySet;

import kotlin.io.FilesKt;

public final class NotesProviderPresenter {
    public static final String TAG = "NotesProviderPresenter";

    public static final String KEY_CONTENT = "content";
    public static final String KEY_HTML_CONTENT = "html_content";
    public static final String KEY_CREATE_TIME = "create_time";
    public static final String KEY_ALARM_TIME = "alarm_time";
    public static final String KEY_RECYCLE_TIME = "recycle_time";
    public static final String KEY_TITLE = "title";
    public static final String KEY_TEXT = "text";
    public static final String KEY_UPDATE_TIME = "update_time";
    public static final String KEY_SPEECH_LOG_ID = "speech_log_id";
    public static final String KEY_SPEECH_CREATE_TIME = "speech_create_time";
    public static final String KEY_EXTRA = "extra";
    public static final String KEY_EXTRA_INFO = "extra_info";
    public static final String KEY_SPEECH_TYPE = "speech_type";
    public static final String KEY_EXTRA_AUDIO_INFO = "audio_info";
    public static final String KEY_EXTRA_AUDIO_PATH = "\"absolute_path\"";
    public static final String KEY_EXTRA_AUDIO_MEDIA_ID = "\"mediaId\"";
    public static final String KEY_ENCRYPTED = "encrypted";
    public static final String KEY_FOLDER_ID = "folder_id";
    public static final String KEY_PACKAGE_NAME = "package_name";
    public static final String KEY_FROM_PACKAGE = "from_package";
    public static final String KEY_IS_LOCAL = "is_local";
    public static final String KEY_LOCAL_ID = "local_id";
    public static final String KEY_FORCE_REMINDER = "force_reminder";
    public static final String KEY_REPEAT_RULE = "repeat_rule";
    public static final String KEY_TOP = "top";
    public static final String KEY_FOLDER_GUID = "folder_guid";
    public static final String KEY_WEB_NOTES = "web_notes";
    public static final String KEY_SKIN_ID = "skin_id";
    public static final String KEY_GUID = "guid";
    public static final String KEY_COUNT = "count";
    public static final String KEY_THUMB_FILENAME = "thumb_filename";
    public static final String INSERT_RESULT = "result";
    public static final String INSERT_RESULT_ERROR = "error";
    public static final String INSERT_RESULT_SUCCESS = "success";
    public static final String INSERT_RESULT_KEY_LOCAL_ID = "localId";
    public static final String INSERT_RESULT_KEY_MESSAGE = "message";
    public static final String MESSAGE_NO_PERMISSION = "no permission!";
    public static final String MESSAGE_NOT_NULL_OR_ZERO_LENGTH = " not null or zero length!";
    public static final String MESSAGE_NOT_NULL_AND_CANNOT_BE_NEGATIVE = " not null and cannot be negative!";
    public static final String MESSAGE_CANNOT_BE_NEGATIVE = " cannot be negative!";
    public static final String MESSAGE_INSERT_FAIL = "insert fail!";
    public static final String MESSAGE_ILLEGAL_PARAMETER = " illegal parameter";
    private static final String TABLE_NAME_RICH_NOTES = "rich_notes";
    private static final String NOTE_ATTR_WRITING_TYPE = "0";
    private static final String NOTE_ATTR_TUYA_TYPE = "1";
    private static final String NOTE_MIX_TYPE = "0";
    private static final String NOTE_WRITING_TYPE = "1";
    private static final int TRUNCATE_TEXT_LENGTH = 40000;

    private final CopyOnWriteArraySet<Integer> mSystemAppUids = new CopyOnWriteArraySet<Integer>();
    private PackageManager mPackageManager;
    private NoteExternalCallPresenter mNoteExternalCallPresenter;

    protected void onCreate(Context context) {
        mPackageManager = context.getApplicationContext().getPackageManager();
    }

    protected ContentProviderResult[] applyBatch(ContentProvider provider,
                                                 ArrayList<ContentProviderOperation> operations)
            throws OperationApplicationException {

        if (!MyApplication.getMyApplication().isDbUpgradeFinished()) {
            return new ContentProviderResult[0];
        }

        final SupportSQLiteDatabase db = AppDatabase.getInstance().getOpenHelper().getWritableDatabase();
        db.beginTransaction();
        try {
            final int size = operations.size();
            final ContentProviderResult[] result = new ContentProviderResult[size];
            for (int i = 0; i < size; i++) {
                final ContentProviderOperation opertaion = operations.get(i);
                result[i] = opertaion.apply(provider, result, i);
            }
            db.setTransactionSuccessful();
            return result;
        } finally {
            db.endTransaction();
        }

    }

    protected int bulkInsert(Context context, Uri uri, ContentValues[] values) {
        if (!MyApplication.getMyApplication().isDbUpgradeFinished()) {
            return 0;
        }

        final SqlArguments args = new SqlArguments(uri);
        final SupportSQLiteDatabase db = AppDatabase.getInstance().getOpenHelper().getWritableDatabase();
        db.beginTransaction();
        try {
            final int numValues = values.length;
            for (ContentValues value : values) {
                if (db.insert(args.mTable, SQLiteDatabase.CONFLICT_NONE, value) < 0) {
                    return 0;
                }
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }

        sendNotify(context, uri);
        return values.length;
    }

    protected String getType(Uri uri) {
        final SqlArguments args = new SqlArguments(uri, null, null);
        if (TextUtils.isEmpty(args.mWhere)) {
            return "vnd.android.cursor.dir/" + args.mTable;
        } else {
            return "vnd.android.cursor.item/" + args.mTable;
        }
    }

    /*** insert behaviors by provider ***/
    protected Uri insert(Context context, Uri uri, ContentValues values, String callingPackage) {
        if (values == null) {
            return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                    .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, "not values!")
                    .build();
        }

        if (NotesProvider.matchUrl(uri, NotesProvider.TABLE_NAME_TODO)) {
            return insertTodo(context, uri, values);
        } else if (NotesProvider.matchUrl(uri, NotesProvider.TEXT_NOTE)) {
            return NoteProviderHelper.insertTextNote(context, uri, values, callingPackage);
        } else if (NotesProvider.matchUrl(uri, NotesProvider.REPEAT_TODO)) {
            return insertRepeatTodo(context, uri, values);
        } else {
            return defaultInsert(context, uri, values);
        }
    }

    private boolean isValidUUID(String uuid) {
        if (uuid == null) {
            AppLogger.BASIC.d(TAG,"uuid is null");
            return false;
        }
        String regex = "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$";
        if (uuid.matches(regex)) {
            return true;
        }
        return false;
    }

    private boolean isValidRepeateRule(String repeateRule) {
        return RepeatManage.isValidRepeatRule(repeateRule);
    }

    protected Uri insertRepeatTodo(Context context, Uri uri, ContentValues values) {
        try {
            // OS16及以上版本屏蔽待办数据操作入口
            if (ConfigUtils.isToDoDeprecated()) {
                String packageName = values.getAsString(KEY_PACKAGE_NAME);
                AppLogger.BASIC.i(TAG, "insertRepeatTodo error: Todo is deprecated. from_package_name: " + packageName);
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, "Todo is deprecated.").build();
            }
            ToDo toDo = new ToDo();
            String localId = values.getAsString(KEY_LOCAL_ID);
            if (TextUtils.isEmpty(localId)) {
                toDo.setLocalId(UUID.randomUUID());
            } else {
                //校验uuid 是否合法
               if (isValidUUID(localId)) {
                   toDo.setLocalId(UUID.fromString(localId));
               } else {
                   return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                           .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_LOCAL_ID + MESSAGE_ILLEGAL_PARAMETER)
                           .build();
               }
            }

            String content = values.getAsString(KEY_CONTENT);
            if (TextUtils.isEmpty(content)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_CONTENT + MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                        .build();
            }
            toDo.setContent(content);

            Long createTime = values.getAsLong(KEY_CREATE_TIME);
            if ((createTime == null) || (createTime < 0)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_CREATE_TIME + MESSAGE_NOT_NULL_AND_CANNOT_BE_NEGATIVE)
                        .build();
            }
            toDo.setCreateTime(new Date(createTime));
            toDo.setUpdateTime(new Date(createTime));

            Long alarmTime = values.getAsLong(KEY_ALARM_TIME);
            if ((alarmTime != null) && (alarmTime < 0)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_ALARM_TIME + MESSAGE_CANNOT_BE_NEGATIVE)
                        .build();
            }

            long alarmTimeLong = (alarmTime == null) ? -1 : alarmTime;
            toDo.setAlarmTime(new Date(alarmTimeLong));
            toDo.setNextAlarmTime(new Date(alarmTimeLong));

            String fromPackage = values.getAsString(KEY_FROM_PACKAGE);
            if (TextUtils.isEmpty(fromPackage)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_FROM_PACKAGE + MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                        .build();
            }
            toDo.setFromPackage(fromPackage);
            boolean isLocal = values.getAsBoolean(KEY_IS_LOCAL); //是否需要云同步，本地false，留言条是true。
            toDo.setIsLocal(isLocal);
            boolean forceReminder = values.getAsBoolean(KEY_FORCE_REMINDER);
            ToDoExtra toDoExtra = new ToDoExtra();
            String repeateRule = values.getAsString(KEY_REPEAT_RULE); //Check legitimacy
            if (isValidRepeateRule(repeateRule)) {
                toDoExtra.setRepeatRule(repeateRule);
            } else {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_REPEAT_RULE + MESSAGE_ILLEGAL_PARAMETER).build();
            }
            toDoExtra.setForceReminder(forceReminder);
            toDo.setExtra(toDoExtra);

            if (RepeatDataHelper.INSTANCE.isRepeat(toDo) && (alarmTimeLong != -1) && (alarmTimeLong < System.currentTimeMillis())) {
                long nextTime = RepeatManage.nextAlarmTimeByRepeat(RepeatDataHelper.INSTANCE.getRepeatData(toDo), toDo.getAlarmTime().getTime());
                if (nextTime > 0) {
                    toDo.setNextAlarmTime(new Date(nextTime));
                }
            }

            toDo.setStatus(ToDo.StatusEnum.NEW);
            ToDoLogger.INSTANCE.printLog(FeedbackLog.Operation.Insert, toDo);
            long result = AppDatabase.getInstance().toDoDao().tripartiteInsert(toDo);
            AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO);
            AppLogger.BASIC.d(TAG, "insertRepeatTodo result: " + result + ", from_package: " + fromPackage);
            StatisticsUtils.setEventInsertTodo(fromPackage);
            if (result > 0) {
                sendNotify(context, TODO_DATA_CHANGE_URI);
                sendNotify(context, TODO_DATA_CHANGE_URI_NEW);
                NoteCardWidgetProvider.Companion.getInstance().postUIToCard(false);
                TodoSearchManager.INSTANCE.notifyDataChange(true, false);
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_SUCCESS)
                        .appendQueryParameter(INSERT_RESULT_KEY_LOCAL_ID, toDo.getLocalId().toString()).build();
            } else {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, MESSAGE_INSERT_FAIL).build();
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "insertRepeatTodo error :" + e.getMessage());
            return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                    .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, e.getMessage()).build();
        }
    }

    protected Uri insertTodo(Context context, Uri uri, ContentValues values) {
        try {
            // OS16及以上版本屏蔽待办数据操作入口
            if (ConfigUtils.isToDoDeprecated()) {
                String packageName = values.getAsString(KEY_PACKAGE_NAME);
                AppLogger.BASIC.i(TAG, "insertTodo error: Todo is deprecated. from_package_name: " + packageName);
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, "Todo is deprecated.").build();
            }
            ToDo toDo = new ToDo();
            toDo.setLocalId(UUID.randomUUID());

            String content = values.getAsString(KEY_CONTENT);
            if (TextUtils.isEmpty(content)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_CONTENT + MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                        .build();
            }
            toDo.setContent(content);

            Long createTime = values.getAsLong(KEY_CREATE_TIME);
            if ((createTime == null) || (createTime < 0)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_CREATE_TIME + MESSAGE_NOT_NULL_AND_CANNOT_BE_NEGATIVE)
                        .build();
            }
            toDo.setCreateTime(new Date(createTime));
            toDo.setUpdateTime(new Date(createTime));

            Long alarmTime = values.getAsLong(KEY_ALARM_TIME);
            if ((alarmTime != null) && (alarmTime < 0)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_ALARM_TIME + MESSAGE_CANNOT_BE_NEGATIVE)
                        .build();
            }
            toDo.setAlarmTime((alarmTime == null) ? null : new Date(alarmTime));
            toDo.setNextAlarmTime((alarmTime == null) ? null : new Date(alarmTime));

            String packageName = values.getAsString(KEY_PACKAGE_NAME);
            if (TextUtils.isEmpty(packageName)) {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, KEY_PACKAGE_NAME + MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                        .build();
            }

            toDo.setStatus(ToDo.StatusEnum.NEW);
            ToDoLogger.INSTANCE.printLog(FeedbackLog.Operation.Insert, toDo);
            long result = AppDatabase.getInstance().toDoDao().insert(toDo);
            AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO);
            AppLogger.BASIC.d(TAG, "insertTodo result: " + result + ", from_package_name: " + packageName);
            StatisticsUtils.setEventInsertTodo(packageName);
            if (result > 0) {
                sendNotify(context, TODO_DATA_CHANGE_URI);
                sendNotify(context, TODO_DATA_CHANGE_URI_NEW);
                NoteCardWidgetProvider.Companion.getInstance().postUIToCard(false);
                TodoSearchManager.INSTANCE.notifyDataChange(true, false);
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_SUCCESS)
                        .appendQueryParameter(INSERT_RESULT_KEY_LOCAL_ID, toDo.getLocalId().toString()).build();
            } else {
                return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                        .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, MESSAGE_INSERT_FAIL).build();
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "insertTodo error :" + e.getMessage());
            return uri.buildUpon().appendQueryParameter(INSERT_RESULT, INSERT_RESULT_ERROR)
                    .appendQueryParameter(INSERT_RESULT_KEY_MESSAGE, e.getMessage()).build();
        }
    }

    @Nullable
    @SuppressLint("RestrictedApi")
    private Uri defaultInsert(Context context, Uri uri, ContentValues values) {
        if (NotesProvider.matchUrl(uri, NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES)
                || NotesProvider.matchUrl(uri, NotesProvider.NOTES_WORDS)) {
            return ContentUris.withAppendedId(NotesProvider.DATA_CHANGE_URI, 1);
        }

        String content = "";
        String guid = UUID.randomUUID().toString();
        long currentTime = System.currentTimeMillis();

        for (Map.Entry<String, Object> entry : values.valueSet()) {
            if (entry == null) {
                continue;
            }

            if (entry.getValue() instanceof String) {
                String key = entry.getKey();
                String entryValue = (String) entry.getValue();
                AppLogger.BASIC.d(TAG, "key = " + key + " value = " + entryValue);
                // TRUNCATE large text
                if (entryValue.length() > TRUNCATE_TEXT_LENGTH) {
                    entryValue = entryValue.substring(0, TRUNCATE_TEXT_LENGTH);
                }

                if (KEY_GUID.equals(key)) {
                    guid = entryValue;
                }

                if (KEY_THUMB_FILENAME.equals(key)) {
                    content = entryValue;
                }
            }
        }

        RichNote richNote = RichNoteFactory.createRichNote(guid, currentTime);
        richNote.setText(content);
        String[] text = RichNoteTransformer.INSTANCE.transformerRawText(richNote.getTitle(), content, new ArrayList<>());
        richNote.setRawText(text[1]);

        //必要参数，外部不需要提供
        richNote.setUpdateTime(currentTime);
        richNote.setState(RichNote.STATE_NEW);
        richNote.setFolderGuid(FolderInfo.FOLDER_GUID_NO_GUID);
        richNote.setSkinId(SkinData.COLOR_SKIN_WHITE);

        try {
//            AppDatabase.getInstance().richNoteDao().insert(richNote);
            RichNoteRepository.INSTANCE.insert(richNote);
            uri = ContentUris.withAppendedId(NotesProvider.DATA_CHANGE_URI, 1);
            AppDatabase.getInstance().getInvalidationTracker().notifyObserversByTableNames(TABLE_NAME_RICH_NOTES);
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "defaultInsert error :" + e.getMessage());
            return null;
        }

        sendNotify(context, uri);
        sendNotify(context, NOTE_DATA_CHANGE_URI);
        sendNotify(context, NOTE_DATA_CHANGE_URI_NEW);
        return uri;
    }

    /*** delete behaviors by provider ***/
    protected int delete(Context context, Uri uri, String selection, String[] selectionArgs, String callingPackage) {
        AppLogger.BASIC.w(TAG, "delete uri: " + uri + " selection " + selection);
        if (!MyApplication.getMyApplication().isDbUpgradeFinished()) {
            return 0;
        }

        if (NotesProvider.matchUrl(uri, NotesProvider.REPEAT_TODO)) {
            if (ConfigUtils.isToDoDeprecated()) {
                AppLogger.BASIC.i(TAG, "delete error: Todo is deprecated. callingPackage: " + callingPackage);
                return 0;
            }
            return TodoProviderHelper.INSTANCE.deleteToDoByLocalId(context, selectionArgs);
        } else if (NoteProviderHelper.isMatchedTextNoteUri(uri)) {
            return NoteProviderHelper.delete(context, uri, callingPackage);
        }

        final SqlArguments args = new SqlArguments(uri, selection, selectionArgs);
        final SupportSQLiteDatabase db = AppDatabase.getInstance().getOpenHelper().getWritableDatabase();
        final int count = db.delete(args.mTable, args.mWhere, args.mArgs);

        if (count > 0) {
            sendNotify(context, NotesProvider.DATA_CHANGE_URI);
        }
        return count;
    }

    /*** update behaviors by provider ***/
    @SuppressLint("getLastPathSegmentRisk")
    protected int update(Context context, Uri uri, ContentValues values, String selection,
                         String[] selectionArgs, String callingPackage) {
        AppLogger.BASIC.d(TAG, "update selection " + selection);
        if (!MyApplication.getMyApplication().isDbUpgradeFinished()) {
            return 0;
        }

        final SupportSQLiteDatabase db = AppDatabase.getInstance().getOpenHelper().getWritableDatabase();
        if (NotesProvider.CLEAN_NOTES.equals(uri.getLastPathSegment())) {
            AppLogger.BASIC.w(TAG, "CLEAN_ALL_NOTES");
            db.beginTransaction();
            try {
                db.delete(NotesProvider.TABLE_NAME_NOTES, null, null);
                db.delete(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, null, null);
                db.delete(NotesProvider.TABLE_NAME_ALARM_TIME, null, null);
                db.delete(NotesProvider.NOTES_WORDS, null, null);
                db.setTransactionSuccessful();
                return 1;
            } finally {
                db.endTransaction();
            }
        } else if (TodoProviderHelper.INSTANCE.handleableUri(uri)) {
            // OS16及以上版本屏蔽待办数据操作入口
            if (ConfigUtils.isToDoDeprecated()) {
                AppLogger.BASIC.i(TAG, "update error: Todo is deprecated. callingPackage: " + callingPackage);
                return 0;
            }
            final int count = TodoProviderHelper.INSTANCE.update(uri, values, callingPackage);
            if (count > 0) {
                WidgetUtils.sendTodoDataChangedBroadcast(MyApplication.getAppContext());
                CloudSyncTrigger.sendDataChangedBroadcast(MyApplication.getAppContext());
            }
            return count;
        } else {
            final SqlArguments args = new SqlArguments(uri, selection, selectionArgs);
            final int count = db.update(args.mTable, SQLiteDatabase.CONFLICT_NONE, values, args.mWhere, args.mArgs);

            if (count > 0) {
                sendNotify(context, NotesProvider.DATA_CHANGE_URI);
            }
            return count;
        }
    }

    /*** query behaviors by provider ***/
    public Cursor query(Context context, Uri uri, String[] projection, String selection,
                           String[] selectionArgs, String sortOrder, String callingPackage) {
        if ((null == context) || (null == uri)) {
            return null;
        }
        if (!MyApplication.getMyApplication().isDbUpgradeFinished()) {
            return null;
        }

        AppLogger.BASIC.d(TAG, "query selection " + selection);
        Cursor result = null;
        String uriPath = uri.getPath();
        if (TextUtils.isEmpty(uriPath)) {
            return null;
        }

        if (uriPath.contains("Search")) { // 目前只有外部搜索才会走到这里
            String keyWord = uri.getQueryParameter("pattern");
            if (TextUtils.isEmpty(keyWord)) {
                return null;
            }
            String queryStr = sqliteEscape(keyWord);
            final String searchString = "%" + queryStr + "%";
            String searchList = "[" + queryStr + "]";
            try {
                result = AppDatabase.getInstance().richNoteDao().search(searchString, searchList, FolderInfo.FOLDER_GUID_ENCRYPTED);
            } catch (final Exception ex) {
                AppLogger.NOTE.e(TAG, "got exception: " + ex.toString());
            }
        } else if (TodoProviderHelper.INSTANCE.handleableUri(uri)) {
            result = TodoProviderHelper.INSTANCE.query(uri, projection, selection, selectionArgs, sortOrder);
        } else if (NoteProviderHelper.isMatchedTextNoteUri(uri)) {
            result = NoteProviderHelper.query(context, uri, projection, selection, selectionArgs, sortOrder, callingPackage, true);
        } else if (NotesProvider.matchUrl(uri, NotesProvider.QUERY_NOTES)) {
            result = NoteProviderHelper.query(context, uri, projection, selection, selectionArgs, sortOrder, callingPackage, false);
        } else {
            SqlArguments args = new SqlArguments(uri, selection, selectionArgs);
            SupportSQLiteDatabase db = AppDatabase.getInstance().getOpenHelper().getReadableDatabase();
            SupportSQLiteQueryBuilder builder = SupportSQLiteQueryBuilder.builder(args.mTable);
            builder.selection(args.mWhere, args.mArgs);
            builder.columns(projection);
            builder.orderBy(sortOrder);
            result = db.query(builder.create());
        }
        if (result != null) {
            result.setNotificationUri(context.getContentResolver(), NotesProvider.DATA_CHANGE_URI);
        }
        return result;
    }

    /***
     * data provider for external's calling
     * @param method
     * @param arg
     * @param extras
     * @return
     */
    protected Bundle call(Context context, @NonNull String method, @Nullable String arg, @Nullable Bundle extras, String callingPackage) {
        if (null == mNoteExternalCallPresenter) {
            mNoteExternalCallPresenter = new NoteExternalCallPresenter(context);
        }
        return mNoteExternalCallPresenter.call(method, arg, extras, callingPackage);
    }

    public static void sendNotify(Context context, Uri uri) {
        if (context == null) {
            return;
        }
        final String notify = uri.getQueryParameter(NotesProvider.PARAMETER_NOTIFY);
        if ((null == notify) || ("true".equals(notify))) {
            context.getContentResolver().notifyChange(uri, null);
        }
    }

    private String sqliteEscape(String keyWord) {
        keyWord = keyWord.replace("/", "//");
        keyWord = keyWord.replace("'", "''");
        keyWord = keyWord.replace("[", "/[");
        keyWord = keyWord.replace("]", "/]");
        keyWord = keyWord.replace("%", "/%");
        keyWord = keyWord.replace("&", "/&");
        keyWord = keyWord.replace("_", "/_");
        keyWord = keyWord.replace("(", "/(");
        keyWord = keyWord.replace(")", "/)");
        return keyWord;
    }

    protected boolean checkPermission(Context context) {
        boolean result = true;
        final int uid = Binder.getCallingUid();
        if (mSystemAppUids.contains(uid)) {
            return true;
        }

        if ((uid == Process.myUid()) || (uid == Process.SYSTEM_UID)) {
            mSystemAppUids.add(uid);
            return true;
        }

        if (mPackageManager == null) {
            mPackageManager = context.getApplicationContext().getPackageManager();
        }
        try {
            final ApplicationInfo info = mPackageManager
                    .getApplicationInfo(mPackageManager.getNameForUid(uid), 0);
            if ((info.flags & ApplicationInfo.FLAG_SYSTEM) == ApplicationInfo.FLAG_SYSTEM) {
                mSystemAppUids.add(uid);
                result = true;
            }
        } catch (final NameNotFoundException e) {
            AppLogger.NOTE.w(TAG, "checkPermission e:" + e);
        }
        AppLogger.NOTE.d(TAG, "checkPermission:result:" + result);
        return result;
    }

    private static final class SqlArguments {
        public final String mTable;
        public final String mWhere;
        public final String[] mArgs;

        public SqlArguments(Uri url) {
            if (url.getPathSegments().size() == 1) {
                mTable = url.getPathSegments().get(0);
                mWhere = null;
                mArgs = null;
            } else {
                throw new IllegalArgumentException("Invalid URI: " + url);
            }
        }

        public SqlArguments(Uri url, String where, String[] args) {
            if (url.getPathSegments().size() == 1) {
                this.mTable = url.getPathSegments().get(0);
                this.mWhere = where;
                this.mArgs = args;
            } else if (url.getPathSegments().size() != 2) {
                throw new IllegalArgumentException("Invalid URI: " + url);
            } else if (!TextUtils.isEmpty(where)) {
                throw new UnsupportedOperationException("WHERE clause not supported: " + url);
            } else {
                this.mTable = url.getPathSegments().get(0);
                this.mWhere = "_id=" + ContentUris.parseId(url);
                this.mArgs = null;
            }
        }
    }

    /**
     * This class helps open, create, and upgrade the database file.
     */
    public static class DatabaseHelper extends SQLiteOpenHelper {

        public static final String TAG = "DatabaseHelper";

        private static final int VERSION_NUM_1 = 1;
        private static final int VERSION_NUM_2 = 2;
        private static final int VERSION_NUM_3 = 3;
        private static final int VERSION_NUM_4 = 4;
        private static final int VERSION_NUM_5 = 5;
        private static final int VERSION_NUM_6 = 6;
        private static final int VERSION_NUM_7 = 7;
        private static final int VERSION_NUM_8 = 8;
        private static final int VERSION_NUM_9 = 9;
        private static final int VERSION_NUM_11 = 11;
        private static final int VERSION_NUM_12 = 12;
        private static final int VERSION_NUM_13 = 13;
        private static final int VERSION_NUM_14 = 14;
        private static final int VERSION_NUM_15 = 15;
        private static final int VERSION_NUM_16 = 16;
        private static final int VERSION_NUM_17 = 17;
        private static final int VERSION_NUM_18 = 18;
        private static final int VERSION_NUM_19 = 19;
        private static final int VERSION_NUM_20 = 20;
        private static final int VERSION_NUM_21 = 21;
        private static final int VERSION_NUM_22 = 22;
        private static final int VERSION_NUM_23 = 23;

        private static Context sAppContext;

        private static volatile DatabaseHelper sDatabaseHelper;

        public static DatabaseHelper getInstance() {
            if (sDatabaseHelper == null) {
                synchronized (DatabaseHelper.class) {
                    if (sDatabaseHelper == null) {
                        sDatabaseHelper = new DatabaseHelper();
                    }
                }
            }
            AppLogger.BASIC.d(TAG, "[Room] DatabaseHelper getInstance()");
            return sDatabaseHelper;
        }

        private DatabaseHelper() {
            super(MyApplication.getAppContext(), NotesProvider.DATABASE_NAME, null, NotesProvider.DATABASE_VERSION);
            sAppContext = MyApplication.getAppContext();
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            AppLogger.BASIC.d(TAG, "[Room] DatabaseHelper onCreate()");
            db.execSQL("CREATE TABLE " + NotesProvider.TABLE_NAME_NOTES + " ("
                    + NotesProvider.COL_ID + " Integer PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL,"
                    + NotesProvider.COL_GUID + " Varchar(36) UNIQUE,"
                    + NotesProvider.COL_VERSION + "  int DEFAULT 0,"
                    + NotesProvider.COL_TOPPED + "  long DEFAULT 0,"
                    + NotesProvider.COL_UPDATED + "  long DEFAULT 0,"
                    + NotesProvider.COL_RECYCLED_TIME + "  long DEFAULT 0,"
                    + NotesProvider.COL_STATE + "  int DEFAULT 0,"
                    + NotesProvider.COL_DESCRIPTION + " TEXT,"
                    + NotesProvider.COL_NOTE_FOLDER + " TEXT,"
                    + NotesProvider.COL_NOTE_FOLDER_GUID + " TEXT NOT NULL DEFAULT '" + FolderInfo.FOLDER_GUID_NO_GUID + "',"
                    + NotesProvider.COL_ATTR_COUNT + " int DEFAULT 0,"
                    + NotesProvider.COL_SORT + " integer DEFAULT 0,"
                    + NotesProvider.COL_CREATED_CONSOLE + "  int DEFAULT 0,"
                    + NotesProvider.COL_THUMB_TYPE + " int DEFAULT 0,"
                    + NotesProvider.COL_THUMB_ATTR_GUID + " Varchar(100),"
                    + NotesProvider.COL_NOTE_OWNER + " int DEFAULT 0,"
                    + NotesProvider.COL_DELETED + " int DEFAULT 0,"
                    + NotesProvider.COL_PARA + " int DEFAULT 0,"
                    + NotesProvider.COL_CREATED + " long DEFAULT 0, "
                    + NotesProvider.COL_GLOBAL_ID + " Varchar(128), "
                    + NotesProvider.COL_ATTACHMENT_ID + " Varchar(256), "
                    + NotesProvider.COL_ATTACHMENT_MD5 + " Varchar(256), "
                    + NotesProvider.COL_ACCOUNT + " Varchar(128), "
                    + NotesProvider.COL_ALARM_TIME + " long DEFAULT 0,"
                    + NotesProvider.COL_NOTE_SKIN + " Varchar(36),"
                    + NotesProvider.COL_RECYCLED_TIME_PRE + "  long DEFAULT 0,"
                    + NotesProvider.COL_ALARM_TIME_PRE + " long DEFAULT 0,"
                    + NotesProvider.COL_NOTE_SKIN_PRE + " Varchar(36), "
                    + NotesProvider.COL_TIMESTAMP + " long DEFAULT 0"
                    + ");");

            db.execSQL("CREATE TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES + " ("
                    + NotesProvider.COL_ID + " Integer PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL, "
                    + NotesProvider.COL_NOTE_GUID + " Varchar(36),"
                    + NotesProvider.COL_TYPE + " int DEFAULT 0,"
                    + NotesProvider.COL_FILENAME + " TEXT,"
                    + NotesProvider.COL_NOTE_ATTR_OWNER + "  int DEFAULT 0,"
                    + NotesProvider.COL_ATTR_CREATED + "  long DEFAULT 0,"
                    + NotesProvider.COL_PARA + "  Varchar(36),"
                    + NotesProvider.COL_STATE + "  int DEFAULT 1, "
                    + NotesProvider.COL_ATTACHMENT_MD5 + " Varchar(256), "
                    + NotesProvider.COL_ATTACHMENT_SYNC_URL + " TEXT, "
                    + NotesProvider.COL_SYNC_DATA1 + " TEXT, "
                    + NotesProvider.COL_WIDTH + " integer DEFAULT 0, "
                    + NotesProvider.COL_HEIGHT + " integer DEFAULT 0" + ");");

            db.execSQL("CREATE TABLE " + NotesProvider.TABLE_NAME_ALARM_TIME + " ("
                    + NotesProvider.COL_GUID + " Varchar(36) PRIMARY KEY,"
                    + NotesProvider.COL_ALARM_TIME + " long DEFAULT 0" + ");");

            db.execSQL("CREATE INDEX note_state ON notes(state);");

            createWordsTables(db);
            createFolderTableContainEncrypted(db);
        }

        private void createFolderTable(SQLiteDatabase db) {
            db.execSQL("CREATE TABLE IF NOT EXISTS " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                    + NotesProvider.COL_FOLDER_ID + " INTEGER PRIMARY KEY, "
                    + NotesProvider.COL_FOLDER_NAME + " TEXT NOT NULL, "
                    + NotesProvider.COL_FOLDER_GUID + " TEXT NOT NULL, "
                    + NotesProvider.COL_FOLDER_STATE + " INTEGER, "
                    + NotesProvider.COL_FOLDER_CREATED_TIME + " INTEGER, "
                    + NotesProvider.COL_FOLDER_MODIFY_DEVICE + " TEXT, "
                    + NotesProvider.COL_FOLDER_DATA1 + " TEXT, "
                    + NotesProvider.COL_FOLDER_DATA2 + " TEXT "
                    + ");");

            createAllNoteFolder(db);
        }

        private void createFolderTableContainEncrypted(SQLiteDatabase db) {
            db.execSQL("CREATE TABLE IF NOT EXISTS " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                    + NotesProvider.COL_FOLDER_ID + " INTEGER PRIMARY KEY, "
                    + NotesProvider.COL_FOLDER_NAME + " TEXT NOT NULL, "
                    + NotesProvider.COL_FOLDER_GUID + " TEXT NOT NULL, "
                    + NotesProvider.COL_FOLDER_STATE + " INTEGER, "
                    + NotesProvider.COL_FOLDER_CREATED_TIME + " INTEGER, "
                    + NotesProvider.COL_FOLDER_MODIFY_DEVICE + " TEXT, "
                    + NotesProvider.COL_FOLDER_DATA1 + " TEXT, "
                    + NotesProvider.COL_FOLDER_DATA2 + " TEXT, "
                    + NotesProvider.COL_FOLDER_ENCRYPTED + " INTEGER DEFAULT 0 "
                    + ");");

            createAllNoteFolder(db);

            createdEncryptedFolder(db);
        }

        private void createAllNoteFolder(SQLiteDatabase db) {
            db.execSQL("INSERT INTO " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                    + NotesProvider.COL_FOLDER_NAME + ", "
                    + NotesProvider.COL_FOLDER_GUID + ", "
                    + NotesProvider.COL_FOLDER_STATE + ", "
                    + NotesProvider.COL_FOLDER_CREATED_TIME + ", "
                    + NotesProvider.COL_FOLDER_MODIFY_DEVICE
                    + ") VALUES ("
                    + "'" + MyApplication.getAppContext().getResources().getString(R.string.memo_all_notes) + "', "
                    + "'" + FolderInfo.FOLDER_GUID_NO_GUID + "', "
                    + FolderInfo.FOLDER_STATE_NEW + ", "
                    + Long.MAX_VALUE + ", '"
                    + Device.getDeviceIMEI(sAppContext)
                    + "');");
        }

        private void createdEncryptedFolder(SQLiteDatabase db) {
            // Create an encrypted folder on an OS version that supports a private password
            if (COUIVersionUtil.getOSVersionCode() >= COUIVersionUtil.COUI_5_2) {
                db.execSQL("INSERT INTO " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                        + NotesProvider.COL_FOLDER_NAME + ", "
                        + NotesProvider.COL_FOLDER_GUID + ", "
                        + NotesProvider.COL_FOLDER_STATE + ", "
                        + NotesProvider.COL_FOLDER_CREATED_TIME + ", "
                        + NotesProvider.COL_FOLDER_MODIFY_DEVICE + ", "
                        + NotesProvider.COL_FOLDER_ENCRYPTED
                        + ") VALUES ("
                        + "'" + MyApplication.getAppContext().getResources().getString(com.oplus.note.baseres.R.string.encrypted_note) + "', "
                        + "'" + FolderInfo.FOLDER_GUID_ENCRYPTED + "', "
                        + FolderInfo.FOLDER_STATE_NEW + ", "
                        + Long.MIN_VALUE + ", '"
                        + Device.getDeviceIMEI(sAppContext) + "', "
                        + FolderInfo.FOLDER_ENCRYPTED
                        + ");");
            }
        }

        private void createWordsTables(SQLiteDatabase db) {
            try {
                db.execSQL("CREATE TABLE words (_id INTEGER PRIMARY KEY, note_guid TEXT, content TEXT, updated LONG, state INTEGER);");

                db.execSQL("CREATE TRIGGER content_delete AFTER DELETE ON notes_attributes BEGIN DELETE FROM "
                        + "  words WHERE note_guid = OLD.note_guid AND OLD.type = 2; END;");
                db.execSQL("CREATE TRIGGER content_update AFTER UPDATE ON notes_attributes BEGIN UPDATE words "
                        + " SET state = NEW.state WHERE (note_guid=NEW.note_guid); "
                        + " END;");
            } catch (final Exception ex) {
                AppLogger.NOTE.e(TAG, "got exception creating words table: " + ex.toString());
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            AppLogger.BASIC.w(TAG, "note provider down grade! check database contains the necessary fields old:" + oldVersion + ", new:" + newVersion);
            try {
                final File backup = new File(sAppContext.getApplicationInfo().dataDir
                        + NotesProvider.DB_BACKUP_FOLDER);
                FilesKt.deleteRecursively(backup);
                final File originalDb = sAppContext.getDatabasePath(NotesProvider.DATABASE_NAME);
                if (null != originalDb) {
                    String parent = originalDb.getParent();
                    if (!TextUtils.isEmpty(parent)) {
                        FilesKt.copyRecursively(new File(parent), backup, true, (file, e) -> null);
                    }
                }
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "Upgrading database copy original database file.");
            } catch (final Exception e) {
                // do nothing
            }
            try {
                boolean isNotesEffective = judgeTableEffective(db, NotesProvider.TABLE_NAME_NOTES, NotesProvider.COL_GUID, NotesProvider.COL_VERSION, NotesProvider.COL_TOPPED, NotesProvider.COL_UPDATED,
                        NotesProvider.COL_STATE, NotesProvider.COL_SORT, NotesProvider.COL_DESCRIPTION, NotesProvider.COL_ATTR_COUNT, NotesProvider.COL_CREATED_CONSOLE,
                        NotesProvider.COL_THUMB_TYPE, NotesProvider.COL_THUMB_ATTR_GUID, NotesProvider.COL_NOTE_OWNER, NotesProvider.COL_DELETED, NotesProvider.COL_PARA,
                        NotesProvider.COL_CREATED, NotesProvider.COL_GLOBAL_ID, NotesProvider.COL_ACCOUNT, NotesProvider.COL_ATTACHMENT_ID, NotesProvider.COL_ATTACHMENT_MD5);
                /** if the target table exist issue, will rebuild the table */
                if (!isNotesEffective) {
                    rebuildTables(db);
                    return;
                }

                boolean isAttrsEffective = judgeTableEffective(db, NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, NotesProvider.COL_NOTE_GUID, NotesProvider.COL_TYPE, NotesProvider.COL_FILENAME, NotesProvider.COL_NOTE_ATTR_OWNER,
                        NotesProvider.COL_ATTR_CREATED, NotesProvider.COL_PARA, NotesProvider.COL_STATE, NotesProvider.COL_ATTACHMENT_MD5, NotesProvider.COL_ATTACHMENT_SYNC_URL, NotesProvider.COL_SYNC_DATA1);
                if (!isAttrsEffective) {
                    rebuildTables(db);
                    return;
                }

                boolean isAlarmEffective = judgeTableEffective(db, NotesProvider.TABLE_NAME_ALARM_TIME, NotesProvider.COL_GUID, NotesProvider.COL_ALARM_TIME);
                if (!isAlarmEffective) {
                    rebuildTables(db);
                    return;
                }

                boolean isWordsEffective = judgeTableEffective(db, NotesProvider.NOTES_WORDS, NotesProvider.COL_NOTE_GUID, NotesProvider.COL_WORD_CONTENT, NotesProvider.COL_ATTR_CREATED, NotesProvider.COL_STATE);
                if (!isWordsEffective) {
                    rebuildTables(db);
                    return;
                }

                boolean isFolderEffective = judgeTableEffective(db, NotesProvider.TABLE_NAME_FOLDERS, NotesProvider.COL_FOLDER_NAME, NotesProvider.COL_FOLDER_GUID, NotesProvider.COL_FOLDER_STATE, NotesProvider.COL_FOLDER_CREATED_TIME, NotesProvider.COL_FOLDER_MODIFY_DEVICE, NotesProvider.COL_FOLDER_DATA1, NotesProvider.COL_FOLDER_DATA2);
                if (!isFolderEffective) {
                    rebuildTables(db);
                    return;
                }
            } catch (final Exception w) {
                w.printStackTrace();
                AppLogger.BASIC.e(TAG, "note provider down grade! database lack the necessary fields");
            }
        }

        private boolean judgeTableEffective(SQLiteDatabase db, String table, String... tarCols) {
            if ((null == db) || (TextUtils.isEmpty(table)) || (null == tarCols) || (tarCols.length < 1)) {
                AppLogger.BASIC.e(TAG, "judgeTableEffective input param error !");
                return false;
            }
            final ArrayList<String> columns = tableColumns(db, table);
            for (String col : tarCols) {
                if ((!TextUtils.isEmpty(col)) && columns.contains(col)) {
                    continue;
                } else {
                    return false;
                }
            }
            return true;
        }

        private void rebuildTables(SQLiteDatabase db) {
            AppLogger.BASIC.d(TAG, "rebuildTables");
            try {
                dropTables(db);
                onCreate(db);
            } catch (final SQLException e) {
                AppLogger.BASIC.e(TAG, "rebuildTables error = " + e.getMessage());
            }
        }

        private void dropTables(SQLiteDatabase db) {
            db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.TABLE_NAME_NOTES);
            db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES);
            db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.TABLE_NAME_ALARM_TIME);
            db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.NOTES_WORDS);
            db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.TABLE_NAME_FOLDERS);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            AppLogger.NOTE.i(TAG, "[NotesProvider]" + "Upgrading database from version " + oldVersion + " to "
                    + newVersion + ", which will destroy all old data");
            int upgradeVersion = oldVersion;
            try {
                final File backup = new File(sAppContext.getApplicationInfo().dataDir
                        + NotesProvider.DB_BACKUP_FOLDER);
                FilesKt.deleteRecursively(backup);
                final File originalDb = sAppContext.getDatabasePath(NotesProvider.DATABASE_NAME);
                if (null != originalDb) {
                    String fileParent = originalDb.getParent();
                    if (!TextUtils.isEmpty(fileParent)) {
                        FilesKt.copyRecursively(new File(fileParent), backup, true, (file, e) -> null);
                    }
                }
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "Upgrading database copy original database file.");
            } catch (final Exception e) {
                // do nothing
            }
            if (upgradeVersion < VERSION_NUM_12) {
                createAlarmTable(db, oldVersion);
                alterTableNotes(db, oldVersion);
                upgradeDataToMix(db, oldVersion);
                alterNewField(db, oldVersion);
                modifyAttrRepeatId(db, oldVersion);
                modifyHandWritingData(db, oldVersion);
                fillWordsTables(db, oldVersion);
                addColumnGlobalId(db, oldVersion);
                addColumnAttachmentId(db, oldVersion);
                addColumnAttachmentSync(db, oldVersion);
                cleanOldSyncState(db, oldVersion);
                upgradeVersion = VERSION_NUM_12;
            }
            if (upgradeVersion < VERSION_NUM_13) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 13");
                addColumnAccount(db);
                upgradeVersion = VERSION_NUM_13;
            }
            if (upgradeVersion < VERSION_NUM_14) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 14");
                reCreateWordsTrigger(db);
                reCreateNotesTable(db);
                upgradeVersion = VERSION_NUM_14;
            }

            if (upgradeVersion < VERSION_NUM_15) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 15");
                addColumnWidthAndHeight(db, oldVersion);
                upgradeVersion = VERSION_NUM_15;
            }

            if (upgradeVersion < VERSION_NUM_16) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 16");
                addColumnTopped(db, oldVersion);
                upgradeVersion = VERSION_NUM_16;
            }

            if (upgradeVersion < VERSION_NUM_18) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 18");
                addColumnNoteFolder(db, oldVersion);
                addColumnNoteFolderGuid(db, oldVersion);
                createFolderTable(db);
                upgradeVersion = VERSION_NUM_18;
            }

            if (upgradeVersion < VERSION_NUM_19) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 19");
                addColumnRecycledTime(db, oldVersion);
                addColumnAlarmTime(db, oldVersion);
                upgradeVersion = VERSION_NUM_19;
            }

            if (upgradeVersion < VERSION_NUM_20) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 20");
                addColumnNoteSkin(db, oldVersion);
                upgradeVersion = VERSION_NUM_20;
            }

            if (upgradeVersion < VERSION_NUM_21) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 21");
                addColumnPre(db, oldVersion);
                upgradeVersion = VERSION_NUM_21;
            }

            if (upgradeVersion < VERSION_NUM_22) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 22");
                addColumnEncrypted(db, oldVersion);
                createdEncryptedFolder(db);
                upgradeVersion = VERSION_NUM_22;
            }

            if (upgradeVersion < VERSION_NUM_23) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database to 23");
                addColumnTimestamp(db, oldVersion);
                upgradeVersion = VERSION_NUM_23;
            }
        }

        @SuppressLint("Range")
        private void reCreateNotesTable(SQLiteDatabase db) {
            // 读出数据
            Cursor cursor = null;
            try {
                cursor = db.query(NotesProvider.TABLE_NAME_NOTES, null, null, null, null, null,
                        null);
                if (cursor != null) {
                    db.execSQL("CREATE TABLE IF NOT EXISTS " + "tmp_table_notes" + " ("
                            + NotesProvider.COL_ID
                            + " Integer PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL, "
                            + NotesProvider.COL_GUID + " Varchar(36) UNIQUE,"
                            + NotesProvider.COL_VERSION + "  int DEFAULT 0,"
                            + NotesProvider.COL_UPDATED + "  long DEFAULT 0,"
                            + NotesProvider.COL_STATE + "  int DEFAULT 0,"
                            + NotesProvider.COL_DESCRIPTION + " TEXT,"
                            + NotesProvider.COL_ATTR_COUNT + " int DEFAULT 0,"
                            + NotesProvider.COL_SORT + " integer DEFAULT 0,"
                            + NotesProvider.COL_CREATED_CONSOLE + "  int DEFAULT 0,"
                            + NotesProvider.COL_THUMB_TYPE + " int DEFAULT 0,"
                            + NotesProvider.COL_THUMB_ATTR_GUID + " Varchar(100),"
                            + NotesProvider.COL_NOTE_OWNER + " int DEFAULT 0,"
                            + NotesProvider.COL_DELETED + " int DEFAULT 0,"
                            + NotesProvider.COL_PARA + " int DEFAULT 0,"
                            + NotesProvider.COL_CREATED + " long DEFAULT 0, "// 备用
                            + NotesProvider.COL_GLOBAL_ID + " Varchar(128), "
                            + NotesProvider.COL_ATTACHMENT_ID + " Varchar(256), "
                            + NotesProvider.COL_ATTACHMENT_MD5 + " Varchar(256), "
                            + NotesProvider.COL_ACCOUNT + " Varchar(128), "
                            + NotesProvider.COL_TOPPED + "  long DEFAULT 0"
                            + ");");

                    final int count = cursor.getCount();
                    AppLogger.NOTE.d(TAG, "[NotesProvider]upgrading data count: " + count);
                    // 转移数据
                    if (count > 0) {
                        if (cursor.moveToFirst()) {
                            do {
                                final ContentValues values = new ContentValues();
                                // 原表有效数据
                                values.put(NotesProvider.COL_GUID, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_GUID)));
                                values.put(NotesProvider.COL_VERSION, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_VERSION)));
                                values.put(NotesProvider.COL_STATE, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_STATE)));
                                values.put(NotesProvider.COL_SORT, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_SORT)));
                                values.put(NotesProvider.COL_UPDATED, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_UPDATED)));
                                values.put(NotesProvider.COL_THUMB_TYPE, cursor.getString(cursor
                                        .getColumnIndex(NotesProvider.COL_THUMB_TYPE)));
                                values.put(NotesProvider.COL_THUMB_ATTR_GUID,
                                        cursor.getString(cursor.getColumnIndex(
                                                NotesProvider.COL_THUMB_ATTR_GUID)));
                                values.put(NotesProvider.COL_GLOBAL_ID, cursor.getString(cursor
                                        .getColumnIndex(NotesProvider.COL_GLOBAL_ID)));
                                values.put(NotesProvider.COL_ACCOUNT, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_ACCOUNT)));
                                values.put(NotesProvider.COL_CREATED, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_CREATED)));
                                values.put(NotesProvider.COL_DELETED, cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_DELETED)));
                                db.insert("tmp_table_notes", null, values);
                            } while (cursor.moveToNext());
                        } else {
                            AppLogger.NOTE.d(TAG, "[NotesProvider] upgrading error!!!!!!!!!!!");
                        }
                    }
                    // 删除原数据表
                    // db.execSQL("DROP TABLE IF EXISTS " + TABLE_NAME_NOTES);
                    db.execSQL("Alter TABLE notes RENAME TO notes_backup");
                    // 改名
                    db.execSQL("Alter TABLE tmp_table_notes RENAME TO notes");
                    AppLogger.NOTE.d(TAG, "[NotesProvider] reCreateNotesTable finish!!!!!!!!!!!");
                }
            } catch (SQLException e) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]upgrading SQLException e = " + e.getMessage());

            } catch (Exception e) {
                AppLogger.NOTE.d(TAG, "[NotesProvider]upgrading e = " + e.getMessage());

            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            final HashMap<String, ContentValues> newValues = new HashMap<String, ContentValues>();
            try {
                cursor = db.query(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES,
                        new String[]{"note_guid", "count(*) - 1"}, null, null,
                        "note_guid", null, null);
                if ((cursor != null) && cursor.moveToFirst()) {
                    do {
                        final ContentValues values = new ContentValues();
                        values.put(NotesProvider.COL_ATTR_COUNT, cursor.getInt(1));
                        newValues.put(cursor.getString(0), values);
                    } while (cursor.moveToNext());
                }
            } catch (final Exception e) {
                e.printStackTrace();
            }
            if (cursor != null) {
                cursor.close();
            }
            try {
                // FIXME There is a problem with the subtitle rules
                cursor = db.query(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES,
                        new String[]{"note_guid", "filename"},
                        "notes_attributes.type = 2", null, null, null, null);
                if ((cursor != null) && (cursor.moveToFirst())) {
                    do {
                        final String guid = cursor.getString(0);
                        final ContentValues values = newValues.get(guid);
                        String attrContent = cursor.getString(1);
                        if (attrContent.contains(NoteInfo.DIVISION)) {
                            final String[] contentText = attrContent.split(NoteInfo.DIVISION);
                            final int size = contentText.length;
                            for (int i = 0; i < size; i += 2) {
                                final String string = contentText[i].trim();
                                if (!"".equals(string)) {
                                    int subLen = Math.min(string.length(), NoteInfo.TEXT_NOTE_THUMB_LEN);
                                    values.put(NotesProvider.COL_DESCRIPTION, string.substring(0, subLen));
                                    break;
                                }
                            }
                        } else {
                            if (attrContent.contains("\n")) {
                                final String[] contentText = attrContent.split("\n");
                                final int size = contentText.length;
                                int index = 0;
                                for (int i = 0; i < size; i++) {
                                    final String string = contentText[i].trim();
                                    if (!"".equals(string)) {
                                        ++index;
                                        if (index == 2) {
                                            int subLen = Math.min(string.length(), NoteInfo.TEXT_NOTE_THUMB_LEN);
                                            values.put(NotesProvider.COL_DESCRIPTION, string.substring(0, subLen));
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        db.update(NotesProvider.TABLE_NAME_NOTES, values, "guid = '" + guid + "'",
                                null);
                    } while (cursor.moveToNext());
                }
            } catch (final Exception e) {
                e.printStackTrace();
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
        }

        private void reCreateWordsTrigger(SQLiteDatabase db) {
            db.execSQL("DROP TRIGGER content_delete;");
            db.execSQL("CREATE TRIGGER content_delete AFTER DELETE ON notes_attributes BEGIN DELETE FROM "
                    + "  words WHERE note_guid = OLD.note_guid AND OLD.type = 2; END;");
        }

        private void cleanOldSyncState(SQLiteDatabase db, int oldVersion) {
            if (oldVersion < VERSION_NUM_11) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "cleanOldSyncState oldVersion = " + oldVersion);
                final ContentValues values = new ContentValues();
                values.put(NotesProvider.COL_STATE, NoteInfo.STATE_NEW);
                values.put(NotesProvider.COL_VERSION, 1);
                db.update(NotesProvider.TABLE_NAME_NOTES, values, null, null);
            }
        }

        private void addColumnGlobalId(SQLiteDatabase db, int oldVersion) {
            if (oldVersion < VERSION_NUM_9) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "addColumnGlobalId oldVersion = " + oldVersion);
                final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
                if (!columns.contains(NotesProvider.COL_GLOBAL_ID)) {
                    db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                            + NotesProvider.COL_GLOBAL_ID + " Varchar(128);");
                }
            }
        }

        private void addColumnAttachmentId(SQLiteDatabase db, int oldVersion) {
            if (oldVersion < VERSION_NUM_9) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "addColumnAttachmentId oldVersion = " + oldVersion);
                final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
                if (!columns.contains(NotesProvider.COL_ATTACHMENT_ID)) {
                    db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                            + NotesProvider.COL_ATTACHMENT_ID + " Varchar(256);");
                }
                if (!columns.contains(NotesProvider.COL_ATTACHMENT_MD5)) {
                    db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                            + NotesProvider.COL_ATTACHMENT_MD5 + " Varchar(256);");
                }
            }
        }

        private void addColumnWidthAndHeight(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db,
                    NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES);
            if (!columns.contains(NotesProvider.COL_WIDTH)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add width oldVersion = " + oldVersion);
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES
                        + " ADD COLUMN " + NotesProvider.COL_WIDTH + " integer DEFAULT 0;");
            }
            if (!columns.contains(NotesProvider.COL_HEIGHT)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add height oldVersion = " + oldVersion);
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES
                        + " ADD COLUMN " + NotesProvider.COL_HEIGHT
                        + " integer DEFAULT 0;");
            }
        }

        private void addColumnTopped(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_TOPPED)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_TOPPED + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_TOPPED + " long DEFAULT 0;");
            }
        }

        private void addColumnNoteFolder(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_NOTE_FOLDER)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_NOTE_FOLDER + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_NOTE_FOLDER + " TEXT;");
            }
        }

        private void addColumnNoteFolderGuid(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_NOTE_FOLDER_GUID)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "addquery " + NotesProvider.COL_NOTE_FOLDER_GUID + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_NOTE_FOLDER_GUID + " TEXT NOT NULL DEFAULT '" + FolderInfo.FOLDER_GUID_NO_GUID + "';");
            }
        }

        private void addColumnRecycledTime(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_RECYCLED_TIME)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_RECYCLED_TIME + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_RECYCLED_TIME + " LONG DEFAULT 0;");
            }
        }

        private void addColumnAlarmTime(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_ALARM_TIME)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_ALARM_TIME + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_ALARM_TIME + " LONG DEFAULT 0;");
            }
        }

        private void addColumnNoteSkin(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_NOTE_SKIN)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_NOTE_SKIN + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_NOTE_SKIN + " VarChar(36);");
            }
        }

        private void addColumnEncrypted(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_FOLDERS);
            if (!columns.contains(NotesProvider.COL_FOLDER_ENCRYPTED)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_FOLDER_ENCRYPTED + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_FOLDERS + " ADD COLUMN "
                        + NotesProvider.COL_FOLDER_ENCRYPTED + " INTEGER DEFAULT 0;");
            }
        }

        private void addColumnPre(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_RECYCLED_TIME_PRE)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_RECYCLED_TIME_PRE + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_RECYCLED_TIME_PRE + " LONG DEFAULT 0;");
            }
            if (!columns.contains(NotesProvider.COL_ALARM_TIME_PRE)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_ALARM_TIME_PRE + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_ALARM_TIME_PRE + " LONG DEFAULT 0;");
            }
            if (!columns.contains(NotesProvider.COL_NOTE_SKIN_PRE)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_NOTE_SKIN_PRE + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_NOTE_SKIN_PRE + " VarChar(36);");
            }
        }

        private void addColumnAttachmentSync(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db,
                    NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES);
            if (!columns.contains(NotesProvider.COL_ATTACHMENT_MD5)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add attachment md5 oldVersion = " + oldVersion);
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES
                        + " ADD COLUMN " + NotesProvider.COL_ATTACHMENT_MD5
                        + " Varchar(256);");
            }
            if (!columns.contains(NotesProvider.COL_ATTACHMENT_SYNC_URL)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add attachment sync url oldVersion = " + oldVersion);
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES
                        + " ADD COLUMN " + NotesProvider.COL_ATTACHMENT_SYNC_URL
                        + " TEXT;");
            }
            if (!columns.contains(NotesProvider.COL_SYNC_DATA1)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add attachment sync data1 oldVersion = "
                        + oldVersion);
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES
                        + " ADD COLUMN " + NotesProvider.COL_SYNC_DATA1 + " TEXT;");
            }
        }

        private void addColumnAccount(SQLiteDatabase db) {
            AppLogger.NOTE.i(TAG, "[NotesProvider]" + "addColumnAccount");
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_ACCOUNT)) {
                db.execSQL("ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_ACCOUNT + " Varchar(128);");
            }
        }

        private void addColumnTimestamp(SQLiteDatabase db, int oldVersion) {
            final ArrayList<String> columns = tableColumns(db, NotesProvider.TABLE_NAME_NOTES);
            if (!columns.contains(NotesProvider.COL_TIMESTAMP)) {
                AppLogger.NOTE.i(TAG, "[NotesProvider]" + "add " + NotesProvider.COL_TIMESTAMP + ", oldVersion = " + oldVersion);
                sqlExecCommand(db, "ALTER TABLE " + NotesProvider.TABLE_NAME_NOTES + " ADD COLUMN "
                        + NotesProvider.COL_TIMESTAMP + " LONG DEFAULT 0;");
            }
        }

        private void sqlExecCommand(SQLiteDatabase db, String command) {
            if ((null == db) || (TextUtils.isEmpty(command))) {
                AppLogger.NOTE.e(TAG, "[NotesProvider] sqlExecCommand input param error!");
                return;
            }
            try {
                db.execSQL(command);
            } catch (Exception e) {
                AppLogger.NOTE.e(TAG, "[NotesProvider] sqlExecCommand error = " + e.getMessage());
            }
        }

        private ArrayList<String> tableColumns(SQLiteDatabase db, String table) {
            final ArrayList<String> columns = new ArrayList<String>();
            final String queryStr = "PRAGMA table_info(" + table + ")";
            Cursor cursor = null;
            int index = 0;
            try {
                cursor = db.rawQuery(queryStr, null);
                if ((cursor != null) && (cursor.moveToFirst())) {
                    index = cursor.getColumnIndex("name");
                    do {
                        final String name = cursor.getString(index);
                        columns.add(name);
                    } while (cursor.moveToNext());
                }
            } catch (final Exception e) {
                // do nothings
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return columns;
        }

        /**
         * 4.0版本
         *
         * @param db
         * @param oldVersion
         */
        private void fillWordsTables(SQLiteDatabase db, int oldVersion) {
            if (oldVersion < VERSION_NUM_8) {
                createWordsTables(db);
                // Log.d("test", "fillWordsTables");
                db.beginTransaction();
                // 将attr表中的文本填充到words表
                Cursor cursor = null;
                final ArrayList<NoteAttribute> attributes = new ArrayList<NoteAttribute>();
                String noteGuid = "";
                try {
                    cursor = db.query(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, new String[]{
                                    NotesProvider.NOTES_ATTR[1], NotesProvider.NOTES_ATTR[2],
                                    NotesProvider.NOTES_ATTR[3], NotesProvider.NOTES_ATTR[5],
                                    NotesProvider.NOTES_ATTR[7]}, null, null, null, null,
                            NotesProvider.NOTES_ATTR[1] + " asc");
                    if ((null != cursor) && (cursor.moveToFirst())) {
                        final int index_0 = 0;
                        final int index_1 = 1;
                        final int index_2 = 2;
                        final int index_3 = 3;
                        final int index_4 = 4;
                        do {
                            NoteAttribute att = null;
                            final String temp = cursor.getString(index_0);
                            if (!noteGuid.equalsIgnoreCase(temp)) {
                                insertWord(db, attributes, noteGuid);
                                att = NoteAttribute.newNoteAttribute(cursor.getInt(index_1),
                                        cursor.getString(index_2));
                                att.setState(cursor.getInt(index_4));
                                att.setCreated(cursor.getLong(index_3));
                                attributes.add(att);
                                noteGuid = temp;
                            } else {
                                att = NoteAttribute.newNoteAttribute(cursor.getInt(index_1),
                                        cursor.getString(index_2));
                                att.setState(cursor.getInt(index_4));
                                att.setCreated(cursor.getLong(index_3));
                                attributes.add(att);
                            }
                        } while (cursor.moveToNext());
                        insertWord(db, attributes, noteGuid);
                    }
                    db.setTransactionSuccessful();
                } catch (final Exception e) {
                    // Log.d("test", e.toString());
                } finally {
                    if (null != cursor) {
                        cursor.close();
                    }
                    db.endTransaction();
                }
            }
        }

        private void insertWord(SQLiteDatabase db, ArrayList<NoteAttribute> attributes,
                                String noteGuid) {
            if (attributes.size() > 0) {
                String contentText = null;
                final ArrayList<String> redundancy_text = new ArrayList<String>();
                int state = 0;
                long update = 0;
                for (final NoteAttribute a : attributes) {
                    if (a.getType() == 2) {
                        contentText = a.getContent();
                        state = a.getState();
                        update = a.getCreated();
                    } else {
                        redundancy_text.add(a.getContent());
                    }
                }
                if (contentText != null) {
                    for (final String rt : redundancy_text) {
                        contentText = contentText
                                .replace(NoteInfo.DIVISION + rt + NoteInfo.DIVISION, "\n");
                    }
                    if (NoteEntityUtils.isNullOrEmpty(contentText)) {
                        contentText = sAppContext.getResources()
                                .getString(R.string.memo_picture);
                    }
                }
                final ContentValues values = new ContentValues();
                values.put("content", contentText);
                values.put("state", state);
                values.put("updated", update);
                values.put(NotesProvider.COL_NOTE_GUID, noteGuid);
                db.insert("words", null, values);
                attributes.clear();
            }
        }

        /**
         * 1.5版本用于修改手写笔迹数据结构
         *
         * @param db
         * @param oldVersion
         */
        private void modifyHandWritingData(SQLiteDatabase db, int oldVersion) {
            AppLogger.NOTE.d(TAG, "[NotesProvider] modifyHandWritingData");

            if (oldVersion >= VERSION_NUM_6) {
                return;
            }
            final String[] projection = {NotesProvider.NOTES_ATTR[1],
                    NotesProvider.NOTES_ATTR[3]};
            final String[] selectionArgs = {NOTE_ATTR_WRITING_TYPE};
            Cursor cursor = null;
            try {
                cursor = db.query(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, projection,
                        NotesProvider.NOTES_ATTR[2] + "=?", selectionArgs, null, null,
                        null);
                if ((null != cursor) && (cursor.moveToFirst())) {
                    do {
                        final String noteId = cursor.getString(0);
                        final String mHandWritingFileName = cursor.getString(1);
                        final String mHandWritingFilePath = ThumbFileManager.getFilePathInSD(noteId,
                                mHandWritingFileName);
                        final HandWritingData mHandWritingData = new HandWritingData();
                        final FingerNoteData mFingerNoteData = new FingerNoteData(
                                NoteAttribute.TYPE_HANDWRITING);
                        if (mFingerNoteData.readNoteData(mHandWritingFilePath) && // 数据能读出来 转换过来
                                mHandWritingData.getDataFromFingerNoteData(
                                        mFingerNoteData)) {
                            mHandWritingData.saveNoteData(mHandWritingFilePath);
                        }
                    } while (cursor.moveToNext());
                }
            } catch (final Exception e) {
                // do nothing
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
        }

        /**
         * 用于修复1.0版本创建的涂鸦、手写、拍照笔记的属性id与笔记id重合的bug
         *
         * @param db
         * @param oldVersion
         */
        private void modifyAttrRepeatId(SQLiteDatabase db, int oldVersion) {
            AppLogger.NOTE.d(TAG, "[NotesProvider] modifyAttrRepeatId");
            if (oldVersion < VERSION_NUM_5) {
                final String[] projection = {NotesProvider.NOTES_ATTR[1],
                        NotesProvider.NOTES_ATTR[2], NotesProvider.NOTES_ATTR[3]};
                Cursor cursor = null;
                try {
                    cursor = db.query(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, projection,
                            NotesProvider.COL_NOTE_GUID + "=" + NotesProvider.COL_FILENAME,
                            null, null, null, null);
                    final ContentValues values = new ContentValues();
                    if (null != cursor) {
                        if (cursor.moveToFirst()) {
                            do {
                                final String noteId = cursor.getString(0);
                                final String noteFolderPath = ThumbFileManager
                                        .getFolderPathInSD(noteId);
                                final String newAttrId = RandomGUID.createGuid();
                                values.clear();
                                values.put(NotesProvider.COL_FILENAME, newAttrId);
                                final String[] whereArgs = {noteId};
                                db.update(NotesProvider.TABLE_NAME_NOTES_ATTRIBUTES, values, // 修改笔记属性表冲突
                                        NotesProvider.COL_FILENAME + "=?", whereArgs);
                                values.clear();
                                values.put(NotesProvider.COL_THUMB_ATTR_GUID, newAttrId);
                                db.update(NotesProvider.TABLE_NAME_NOTES, values, // 修改笔记主表冲突
                                        NotesProvider.COL_THUMB_ATTR_GUID + "=?",
                                        whereArgs);
                                FileUtil.rename(noteFolderPath + noteId + ThumbFileConstants.THUMB,
                                        noteFolderPath + newAttrId
                                                + ThumbFileConstants.THUMB);
                                final int attrType = cursor.getInt(1);
                                if ((NoteAttribute.TYPE_HANDWRITING == attrType)
                                        || (NoteAttribute.TYPE_HANDWRITING == NoteAttribute.TYPE_TUYA)) {
                                    FileUtil.rename(noteFolderPath + noteId,
                                            noteFolderPath + newAttrId);
                                } else if (NoteAttribute.TYPE_PHOTOGRAPH == attrType) {
                                    FileUtil.rename(noteFolderPath + noteId
                                                    + ThumbFileConstants.IMAGE_EXT,
                                            noteFolderPath + newAttrId
                                                    + ThumbFileConstants.IMAGE_EXT);
                                } else if (NoteAttribute.TYPE_ALBUM == attrType) {
                                    FileUtil.rename(noteFolderPath + noteId + ThumbFileConstants.GIF, // 相册类型可能是gif文件
                                            noteFolderPath + newAttrId
                                                    + ThumbFileConstants.GIF);
                                }

                            } while (cursor.moveToNext());

                        }
                    }
                } catch (SQLException e) {
                    AppLogger.NOTE.e(TAG, "[NotesProvider] modifyAttrRepeatId SQLException e = " + e.getMessage());
                } catch (Exception e) {
                    AppLogger.NOTE.e(TAG, "[NotesProvider] modifyAttrRepeatId e = " + e.getMessage());
                } finally {
                    if (cursor != null) {
                        cursor.close();
                    }
                }
            }
        }

        @SuppressLint("Range")
        private void upgradeDataToMix(SQLiteDatabase db, int oldVersion) {
            AppLogger.NOTE.d(TAG, "[NotesProvider] upgradeDataToMix");
            if (oldVersion <= 2) { // 用于将1.0\1.0.1的纯手写和纯涂鸦笔记升级为混合型笔记
                final ContentValues values = new ContentValues();
                // sort= 1表示纯手写,sort=2表示纯涂鸦
                Cursor cursor = null;
                try {
                    cursor = db.query(NotesProvider.TABLE_NAME_NOTES, null, "sort=1 or sort=2",
                            null, null, null, null);
                    if (null != cursor) {
                        if (cursor.moveToFirst()) {
                            do {
                                values.put(NotesProvider.NOTES_ATTR[1], cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_GUID)));
                                final String sort = cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_SORT));
                                if (sort.equals(NOTE_WRITING_TYPE)) {
                                    values.put(NotesProvider.NOTES_ATTR[2], NOTE_ATTR_WRITING_TYPE);
                                } else {
                                    values.put(NotesProvider.NOTES_ATTR[2], NOTE_ATTR_TUYA_TYPE);
                                }
                                values.put(NotesProvider.NOTES_ATTR[5], cursor.getString(
                                        cursor.getColumnIndex(NotesProvider.COL_UPDATED)));
                                values.put(NotesProvider.NOTES_ATTR[3], cursor.getString(cursor
                                        .getColumnIndex(NotesProvider.COL_THUMB_ATTR_GUID)));

                                final long i = db.insert("notes_attributes", null, values);
                                AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database i= " + i);
                            } while (cursor.moveToNext());
                        }
                        values.clear();
                        values.put(NotesProvider.COL_SORT, NOTE_MIX_TYPE);
                        final int sum = db.update(NotesProvider.TABLE_NAME_NOTES, values, null,
                                null);
                        AppLogger.NOTE.d(TAG, "[NotesProvider]" + "Upgrading database sum= " + sum);
                    }
                } catch (final Exception e) {
                    // do nothing
                } finally {
                    if (cursor != null) {
                        cursor.close();
                    }
                }
            }
        }

        private void alterNewField(SQLiteDatabase db, int oldVision) {
            AppLogger.NOTE.d(TAG, "[NotesProvider] AlterNewField");
            if (VERSION_NUM_4 <= oldVision) {
                return;
            }
            db.beginTransaction();
            try {
                String sql = "alter table " + NotesProvider.TABLE_NAME_NOTES + " add "
                        + NotesProvider.COL_CREATED + " long DEFAULT 0";
                db.execSQL(sql);
                sql = "update " + NotesProvider.TABLE_NAME_NOTES + " set "
                        + NotesProvider.COL_CREATED + " = " + NotesProvider.COL_UPDATED;
                db.execSQL(sql);
                db.setTransactionSuccessful();
            } finally {
                db.endTransaction();
            }
        }

        private void createAlarmTable(SQLiteDatabase db, int oldVersion) {
            if (oldVersion >= VERSION_NUM_7) {
                return;
            }
            db.execSQL("CREATE TABLE IF NOT EXISTS " + NotesProvider.TABLE_NAME_ALARM_TIME + " ("
                    + NotesProvider.COL_GUID + " Varchar(36) PRIMARY KEY,"
                    + NotesProvider.COL_ALARM_TIME + " long DEFAULT 0" + ");");
        }

        @SuppressLint("Range")
        private void alterTableNotes(SQLiteDatabase db, int oldVersion) {
            AppLogger.NOTE.d(TAG, "[NotesProvider] alterTableNotes");
            if (oldVersion == VERSION_NUM_1) {
                // 读出数据
                Cursor cursor = null;
                try {
                    cursor = db.query(NotesProvider.TABLE_NAME_NOTES, null, null, null, null, null,
                            null);
                    if (cursor != null) {
                        final int COL_COUNT = 11;
                        final int columnCount = cursor.getColumnCount();
                        AppLogger.NOTE.d(TAG, "[NotesProvider]upgrading data columnCount: " + columnCount);
                        if (columnCount == COL_COUNT) { // 13及以后版本
                            AppLogger.NOTE.d(TAG, "[NotesProvider] no need upgrading!!!!");
                            return;
                        }
                        db.execSQL("CREATE TABLE IF NOT EXISTS " + "tmp_table_notes" + " ("
                                + NotesProvider.COL_GUID + " Varchar(36) PRIMARY KEY,"
                                + NotesProvider.COL_VERSION + "  int DEFAULT 0,"
                                + NotesProvider.COL_UPDATED + "  long DEFAULT 0,"
                                + NotesProvider.COL_STATE + "  int DEFAULT 0,"
                                + NotesProvider.COL_SORT + " integer DEFAULT 0,"
                                + NotesProvider.COL_CREATED_CONSOLE + "  int DEFAULT 0,"
                                + NotesProvider.COL_THUMB_TYPE + " int DEFAULT 0,"
                                + NotesProvider.COL_THUMB_ATTR_GUID + " Varchar(100),"
                                + NotesProvider.COL_NOTE_OWNER + " int DEFAULT 0,"
                                + NotesProvider.COL_DELETED + " int DEFAULT 0,"
                                + NotesProvider.COL_PARA + " int DEFAULT 0"// 备用
                                + ");");

                        final int count = cursor.getCount();
                        AppLogger.NOTE.d(TAG, "[NotesProvider]upgrading data count: " + count);
                        // 转移数据
                        if (count > 0) {
                            if (cursor.moveToFirst()) {
                                do {
                                    final ContentValues values = new ContentValues();
                                    // 原表有效数据
                                    values.put(NotesProvider.COL_GUID, cursor.getString(
                                            cursor.getColumnIndex(NotesProvider.COL_GUID)));
                                    values.put(NotesProvider.COL_SORT, cursor.getString(
                                            cursor.getColumnIndex(NotesProvider.COL_SORT)));
                                    values.put(NotesProvider.COL_UPDATED, cursor.getString(cursor
                                            .getColumnIndex(NotesProvider.COL_UPDATED)));
                                    values.put(NotesProvider.COL_THUMB_TYPE, cursor.getString(cursor
                                            .getColumnIndex(NotesProvider.COL_THUMB_TYPE)));
                                    values.put(NotesProvider.COL_THUMB_ATTR_GUID,
                                            cursor.getString(cursor.getColumnIndex(
                                                    NotesProvider.COL_THUMB_ATTR_GUID)));

                                    db.insert("tmp_table_notes", null, values);
                                } while (cursor.moveToNext());
                            } else {
                                AppLogger.NOTE.d(TAG, "[NotesProvider] upgrading error!!!!!!!!!!!");
                            }
                        }
                        // 删除原数据表
                        db.execSQL("DROP TABLE IF EXISTS " + NotesProvider.TABLE_NAME_NOTES);
                        // 改名
                        db.execSQL("Alter TABLE tmp_table_notes RENAME TO notes");
                        // 往noteAttribute数据表中添加state字段
                        db.execSQL("alter table notes_attributes add state int DEFAULT 0");// miaodf
                        // modify
                        // 1
                        // to
                        // 0
                        // 2012-7-25
                        // DE9A496E_D56C_AF81_15AE_09B508D282C0
                        // 转移文件夹
                        final String projectFolder = FileUtil.getProjectFolderPath();
                        final String noteRootPath = ThumbFileManager.getNoteRootFolderPath();
                        final File folder = new File(projectFolder);
                        final File[] files = folder.listFiles();
                        if (files != null) {
                            final int NAME_LEN = 30;
                            for (int i = 0; i < files.length; i++) {
                                final File file = files[i];
                                if (file.exists() && file.isDirectory()) {
                                    final String name = file.getName();
                                    if (name.length() > NAME_LEN) {
                                        final String dest = noteRootPath + name;
                                        if (file.renameTo(new File(dest))) {
                                            AppLogger.NOTE.d(TAG, "[NotesProvider] upgrading move file ok: "
                                                    + dest);
                                        } else {
                                            AppLogger.NOTE.d(TAG, "[NotesProvider] upgrading move file fail!!!: "
                                                    + dest);
                                        }
                                    }
                                }
                            }
                        }
                        AppLogger.NOTE.d(TAG, "[NotesProvider] upgrading finish!!!!!!!!!!!");
                    }
                } catch (SQLException e) {
                    AppLogger.NOTE.d(TAG, "[NotesProvider] alterTableNotes SQLException e = " + e.getMessage());

                } catch (Exception e) {
                    AppLogger.NOTE.d(TAG, "[NotesProvider] alterTableNotes e = " + e.getMessage());

                } finally {
                    if (cursor != null) {
                        cursor.close();
                    }
                }
            }
        }
    }
}
