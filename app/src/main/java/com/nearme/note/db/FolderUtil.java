/***********************************************************
 * * Copyright (C), 2008-2018, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: FolderUtil.java
 * * Description:
 * * Version: 1.0
 * * Date : 2019/08/06 12:30
 * * Author:
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.db;

import static com.nearme.note.util.CardRefreshUtilsKt.postMeetingIntentionExitedIfNeed;
import static com.nearme.note.util.CardRefreshUtilsKt.refreshCardList;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.nearme.note.MyApplication;
import com.nearme.note.data.NoteInfo;
import com.nearme.note.db.entities.Note;
import com.nearme.note.logic.NoteSyncProcess;
import com.nearme.note.model.RichNoteRepository;
import com.nearme.note.util.CloudSyncTrigger;
import com.nearme.note.util.FolderStatisticUtils;
import com.nearme.note.util.StatisticsUtils;
import com.oplus.cloud.agent.SyncAgentContants;
import com.oplus.cloud.status.Device;
import com.oplus.cloud.sync.note.AnchorManager;
import com.oplus.cloudkit.util.Constants;
import com.oplus.note.R;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.notebook.internal.NoteBookData;
import com.oplus.note.repo.note.entity.Folder;
import com.oplus.note.repo.note.entity.FolderExtra;
import com.oplus.note.repo.note.entity.FolderFactory;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.oplus.note.repo.note.entity.RichNote;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class FolderUtil {


    public static final int MAX_FOLDER_COUNT = Integer.MAX_VALUE;

    public static final int CODE_QUERY_FOLDERS_OK = 0;
    public static final int CODE_QUERY_FOLDERS_EXCEPTION = 1;

    public static final int CODE_INSERT_FOLDER_OK = 100;
    public static final int CODE_INSERT_FOLDER_NAME_EMPTY = 101;
    public static final int CODE_INSERT_FOLDER_EXISTED = 102;
    public static final int CODE_INSERT_FOLDER_EXCEPTION = 103;
    public static final int CODE_INSERT_FOLDER_REACH_LIMIT_COUNT = 104;

    public static final int CODE_UPDATE_FOLDER_OK = 200;
    public static final int CODE_UPDATE_FOLDER_GUID_EMPTY = 201;
    public static final int CODE_UPDATE_FOLDER_NEW_NAME_EMPTY = 202;
    public static final int CODE_UPDATE_FOLDER_GUID_NOT_EXISTED = 203;
    public static final int CODE_UPDATE_FOLDER_EXCEPTION = 204;

    public static final int CODE_DELETE_FOLDER_OK = 300;
    public static final int CODE_DELETE_FOLDER_EXCEPTION = 301;
    public static final int CODE_DELETE_INVALID = -1;

    public static final int TYPE_FROM_DRAWER = 0;
    public static final int TYPE_FROM_NOTE_DETAIL = 1;
    public static final int TYPE_FROM_NOTE_LIST_BOTTOM_MENU = 2;
    public static final String KEY_NOTE_BOOK_TYPE = "key_note_book_type";
    public static final String KEY_FOLDER_NAME = "key_folder_name";
    public static final String KEY_FOLDER_GUID = "key_folder_guid";
    public static final String KEY_FOLDER_COVER = "key_folder_cover";
    public static final String KEY_REQUEST_CODE = "key_request_code";
    public static final String DEFAULT_QUICK_NOTE = MyApplication.getAppContext().getResources().getString(R.string.quick_note);

    public static final String DEFAULT_CALL_SUMMARY =
            MyApplication.getApplication().getResources().getString(com.oplus.note.baseres.R.string.ai_call_summary);
    private static final String TAG = "FolderUtil";

    private static FolderUtil sInstance = new FolderUtil();

    private FolderUtil() {
        // do noting.
    }

    public static FolderUtil getInstance() {
        return sInstance;
    }

    // ========================================================================>
    // ================================== query ===============================>
    // ========================================================================>

    public static int queryFoldersTotalCountSync(Context context) {
        int count = 0;
        try {
            count = AppDatabase.getInstance().foldersDao().getNotDeletedFolderCount();
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "queryFoldersTotalCountSync(), exception: " + e);
        }

        AppLogger.BASIC.d(TAG, "queryFoldersTotalCountSync(), count: " + count);
        return count;
    }

    public boolean queryDirtyFoldersSync() {
        try {
            boolean hasDirtyFolders = AppDatabase.getInstance().foldersDao().existDirtyFolder();
            AppLogger.BASIC.d(TAG, "queryDirtyFoldersSync(), hasDirtyFolders: " + hasDirtyFolders);
            return hasDirtyFolders;
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "queryDirtyFoldersSync(), exception: " + e);
        }
        return true;
    }

    public List<FolderInfo> queryAllFoldersSync() {
        return queryAllFoldersSync(false);
    }

    public List<FolderInfo> queryAllFoldersSync(boolean includeDeleted) {
        List<FolderInfo> list = new ArrayList<>();
        int resultCode = queryAllFoldersInner(includeDeleted, list);
        AppLogger.BASIC.d(TAG, "queryAllFoldersSync(), resultCode: " + resultCode);
        return list;
    }

    public List<FolderInfo> queryAllFoldersExceptEncryptSync() {
        List<FolderInfo> list = new ArrayList<>();
        try {
            List<Folder> folders = AppDatabase.getInstance().foldersDao().getNotDeletedFoldersExcludeOrderbyCreatedTime(FolderInfo.FOLDER_GUID_ENCRYPTED);
            for (Folder folder : folders) {
                FolderInfo folderInfo = new FolderInfo(folder);
                list.add(folderInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    private int queryAllFoldersInner(boolean includeDeleted, List<FolderInfo> list) {
        int resultCode = CODE_QUERY_FOLDERS_OK;
        try {
            List<Folder> folders = null;
            if (includeDeleted) {
                folders = AppDatabase.getInstance().foldersDao().getAllFoldersOrderByCreateTime();
            } else {
                folders = AppDatabase.getInstance().foldersDao().getNotDeletedFoldersOrderbyCreatedTime();
            }

            for (Folder folder : folders) {
                FolderInfo folderInfo = new FolderInfo(folder);
                list.add(folderInfo);
            }

        } catch (Exception e) {
            resultCode = CODE_QUERY_FOLDERS_EXCEPTION;
        }

        return resultCode;
    }

    public static int getFolderEncrypt(String guid) {
        Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(guid);
        if (folder == null) {
            return FolderInfo.FOLDER_UNENCRYPTED;
        } else {
            return folder.encrypted;
        }
    }

    // <========================================================================
    // <================================== query ===============================
    // <========================================================================

    // ========================================================================>
    // ================================== insert ==============================>
    // ========================================================================>

    public static int insertFolderSync(Context context, FolderInfo folderInfo) {
        int totalCount = queryFoldersTotalCountSync(context);
        if (totalCount >= MAX_FOLDER_COUNT) {
            AppLogger.BASIC.d(TAG, "insertFolderSync(), reach folder count limit. totalCount: " + totalCount);
            return CODE_INSERT_FOLDER_REACH_LIMIT_COUNT;
        }

        int resultCode = CODE_INSERT_FOLDER_OK;
        boolean existed = isFolderNameExistedSync(context, folderInfo.getName());
        AppLogger.BASIC.d(TAG, "insertFolderSync(), existed: " + existed);
        if (!existed) {
            try {
                Folder folder = folderInfo.toFolder();
                AppDatabase.getInstance().foldersDao().insert(folder);
                DeleteFolderCacheHolder.removeFolderCache(context, folderInfo);
            } catch (Exception e) {
                resultCode = CODE_INSERT_FOLDER_EXCEPTION;
                AppLogger.BASIC.d(TAG, "insertFolderSync(), exception: " + e);
            }
        } else {
            resultCode = CODE_INSERT_FOLDER_EXISTED;
        }

        if (resultCode == CODE_INSERT_FOLDER_OK) {
            FolderSyncSwitchManager.setInitialSyncSwitch(folderInfo.getGuid(), Constants.FOLDER_SYNC_ON);
        }

        AppLogger.BASIC.d(TAG, "insertFolderSync(), resultCode: " + resultCode);
        return resultCode;
    }

    public static int insertFolderNameSync(Context context, String name, String guid, int encrypted, String coverRes) {
        AppLogger.BASIC.d(TAG, "insertFolderNameSync(), folderid: " + guid);
        return insertFolderNameSync(context, name, guid, Device.getDeviceIMEI(context),
                System.currentTimeMillis(), FolderInfo.FOLDER_STATE_NEW, encrypted, coverRes);
    }

    public static void createSummaryNoteByFolderGuid(String folderGuiId) {
        Folder folder = AppDatabase.getInstance().foldersDao()
                .findByGuid(folderGuiId);
        String folderType = "";
        String folderName = "";
        if (folderGuiId.equals(FolderInfo.FOLDER_GUID_CALL_SUMMARY)) {
            folderType = FolderInfo.FOLDER_GUID_CALL_SUMMARY;
            folderName = MyApplication.getApplication().getResources().getString(com.oplus.note.baseres.R.string.ai_call_summary);
        } else if (folderGuiId.equals(FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY)) {
            folderType = FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY;
            folderName = MyApplication.getApplication().getResources().getString(com.oplus.note.baseres.R.string.ai_article_summary);
        } else if (folderGuiId.equals(FolderInfo.FOLDER_GUID_AUDIO_SUMMARY)) {
            folderType = FolderInfo.FOLDER_GUID_AUDIO_SUMMARY;
            folderName = MyApplication.getAppContext().getString(com.oplus.note.baseres.R.string.ai_voice_summary);
        }
        syncHandle(folderGuiId, folder, folderType, folderName);
    }

    public static void createCollectionNoteByFolderGuid() {
        String folderType = FolderInfo.FOLDER_GUID_COLLECTION;
        String folderName = MyApplication.getAppContext().getResources().getString(R.string.memo_collection);
        syncHandle(FolderInfo.FOLDER_GUID_COLLECTION, null, folderType, folderName);
    }

    private static String calCover(String folderGuiId) {
        if (folderGuiId.equals(FolderInfo.FOLDER_GUID_COLLECTION)) {
            return NoteBookData.INSTANCE.getCollectionNoteDefaultCover();
        }
        return NoteBookData.INSTANCE.getAllNoteDefaultCover();
    }
    private static void syncHandle(String folderGuiId, Folder folder, String folderType, String folderName) {

        if (folder == null) {
            int result = FolderUtil.insertFolderNameSync(
                    MyApplication.getAppContext(),
                    folderName,
                    folderType,
                    FolderInfo.FOLDER_UNENCRYPTED,
                    calCover(folderGuiId)
            );
            AppLogger.BASIC.d(TAG, "syncHandle result: " + result);
            if (FolderUtil.CODE_INSERT_FOLDER_EXISTED == result) {
                dealRenameFolders(folderName, folderGuiId);
            } else if (FolderUtil.CODE_INSERT_FOLDER_OK == result) {
                /**
                 * 默认同步开关为关
                 */
                syncSwitchDefaultClose(folderGuiId, true);
                StatisticsUtils.setEventCreateSystemFolder(FolderStatisticUtils.getFolderType(folderGuiId));
            }
        }
    }

    private static void dealRenameFolders(String folderName, String folderGuiId) {
        List<Folder> manualRenameFolders = AppDatabase.getInstance().foldersDao()
                .findNotDeletedFolderByName(folderName);
        folderRename(manualRenameFolders);
        if (folderGuiId.equals(FolderInfo.FOLDER_GUID_CALL_SUMMARY)
                || folderGuiId.equals(FolderInfo.FOLDER_GUID_AUDIO_SUMMARY)
                || folderGuiId.equals(FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY) || folderGuiId.equals(FolderInfo.FOLDER_GUID_COLLECTION)) {
            if (FolderUtil.CODE_INSERT_FOLDER_OK == FolderUtil.insertFolderNameSync(
                    MyApplication.getAppContext(),
                    folderName,
                    folderGuiId,
                    FolderInfo.FOLDER_UNENCRYPTED,
                    NoteBookData.INSTANCE.getAllNoteDefaultCover()
            )
            ) {
                /**
                 * 默认同步开关为关
                 */
                syncSwitchDefaultClose(folderGuiId, true);
                StatisticsUtils.setEventCreateSystemFolder(FolderStatisticUtils.getFolderType(folderGuiId));
            }
        }
    }

    private static void folderRename(List<Folder> manualSummaryFolders) {
        int index = 0;
        if (manualSummaryFolders != null) {
            for (Folder fo : manualSummaryFolders) {
                if (!FolderInfo.FOLDER_GUID_CALL_SUMMARY.equals(fo.guid)) {
                    String newFolderName = fo.name + (index + 1);
                    FolderUtil.updateFolderNameSync(
                            MyApplication.getAppContext(),
                            fo.guid,
                            newFolderName,
                            fo.extra.getCover()
                    );
                    index++;
                }
            }
        }
    }

    private static void syncSwitchDefaultClose(String folderId, boolean isSync) {
        Folder folderCallSummary = AppDatabase.getInstance().foldersDao()
                .findByGuid(folderId);
        FolderExtra newExtra = FolderExtra.Companion.create(folderCallSummary.extra.upToDate());
        newExtra.setDefaultSyncWithGuid(folderId, isSync);
        folderCallSummary.extra = newExtra;
        AppDatabase.getInstance().foldersDao().updateFolder(folderCallSummary);
    }

    public static int insertFolderNameSync(Context context, String name, String guid, String modifyDevice,
                                           long createTime, int state, int encrypted, String coverRes) {
        return insertFolderNameSync(context, name, guid, modifyDevice,
                createTime, state, encrypted, coverRes, null, 0);
    }

    public static int insertFolderNameSync(Context context, String name, String guid, String modifyDevice,
                                           long createTime, int state, int encrypted, String coverRes, long sysVersion) {
        return insertFolderNameSync(context, name, guid, modifyDevice,
                createTime, state, encrypted, coverRes, null, sysVersion);
    }

    public static int insertFolderNameSync(Context context, String name, String guid, String modifyDevice,
                                           long createTime, int state, int encrypted, String coverRes,
                                           String extra, long sysVersion) {
        AppLogger.BASIC.d(TAG, "insertFolderNameSync(), folderid: " + guid + ", createTime: "
                + createTime + " ,coverRes = " + coverRes);

        if (TextUtils.isEmpty(name)) {
            AppLogger.BASIC.d(TAG, "insertFolderNameSync(), name empty.");
            return CODE_INSERT_FOLDER_NAME_EMPTY;
        }

        FolderInfo folderInfo = new FolderInfo(name);
        folderInfo.setCreateTime(createTime);
        folderInfo.setModifyTime(createTime);
        folderInfo.setModifyDevice(modifyDevice);
        folderInfo.setState(state);
        folderInfo.setGuid(guid);
        folderInfo.setEncrypted(encrypted);
        FolderExtra folderExtra = FolderExtra.Companion.create(extra);
        if (!TextUtils.isEmpty(coverRes)) {
            folderExtra.setCover(coverRes, NoteBookData.INSTANCE.isOldCover(coverRes));
        }
        folderInfo.setExtra(folderExtra);
        folderInfo.setSysVersion(sysVersion);
        return insertFolderSync(context, folderInfo);
    }

    // <========================================================================
    // <================================== insert ==============================
    // <========================================================================

    // ========================================================================>
    // ================================== update ==============================>
    // ========================================================================>
    @Deprecated
    public int updateFolderWithValuesSync(String guid, String name, String modifyDevice, String extra, int state) {
        AppLogger.BASIC.d(TAG, "updateFolderWithValuesSync(), folderid: " + guid
                + ", modifyDevice: " + modifyDevice + ", extra: " + extra + ", state: " + state);

        if (TextUtils.isEmpty(guid)) {
            AppLogger.BASIC.d(TAG, "updateFolderWithValuesSync(), folderid empty.");
            return CODE_UPDATE_FOLDER_GUID_EMPTY;
        }

        int resultCode = 0;

        try {
            Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(guid);
            if (folder == null) {
                resultCode = CODE_UPDATE_FOLDER_GUID_NOT_EXISTED;
            } else {
                folder.name = name;
                folder.modifyDevice = modifyDevice;
                folder.state = state;
                folder.extra = folder.extra.updateExtraInfo(FolderExtra.Companion.create(extra));
                AppDatabase.getInstance().foldersDao().updateFolder(folder);
                resultCode = CODE_UPDATE_FOLDER_OK;
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "updateFolderWithValuesSync(), exception: " + e);
            resultCode = CODE_UPDATE_FOLDER_EXCEPTION;
        }

        return resultCode;
    }

    public int updateFolderWithFolderInfoSyncForRichNote(String guid, FolderInfo folderInfo) {
        AppLogger.BASIC.d(TAG, "updateFolderWithFolderInfoSyncForRichNote(), folderid: " + guid);

        if (TextUtils.isEmpty(guid)) {
            AppLogger.BASIC.d(TAG, "updateFolderWithFolderInfoSyncForRichNote(), guid empty.");
            return CODE_UPDATE_FOLDER_GUID_EMPTY;
        }

        int resultCode = CODE_UPDATE_FOLDER_OK;
        try {
            Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(guid);
            FolderInfo.copyValuesFromFolderInfoToFolder(folder, folderInfo);
            int count = AppDatabase.getInstance().foldersDao().updateFolder(folder);

            List<RichNote> notes = AppDatabase.getInstance().richNoteDao().findByFolderGuids(Collections.singletonList(guid));
            for (RichNote note : notes) {
                note.setFolderGuid(folderInfo.getGuid());
                note.setState(RichNote.STATE_MODIFIED);
                note.setUpdateTime(System.currentTimeMillis());
            }

            count += RichNoteRepository.INSTANCE.updateNotes(notes, false);

            if (count == 0) {
                resultCode = CODE_UPDATE_FOLDER_GUID_NOT_EXISTED;
            }
        } catch (Exception e) {
            resultCode = CODE_UPDATE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "updateFolderWithFolderInfoSyncForRichNote(), exception: " + e);
        }

        return resultCode;
    }

    // this method is only used to clean folder data when logout
    public int updateFoldersWithFolderInfoSyncForLogout(@NonNull List<FolderInfo> folderInfos) {
        for (FolderInfo folder : folderInfos) {
            if (TextUtils.isEmpty(folder.getGuid())) {
                AppLogger.BASIC.d(TAG, "updateFoldersWithFolderInfoSync(), some folder guid are empty.");
                return CODE_UPDATE_FOLDER_GUID_EMPTY;
            }
        }

        int resultCode = CODE_UPDATE_FOLDER_OK;
        try {
            List<Folder> folders = new ArrayList<>();
            for (FolderInfo folderInfo : folderInfos) {
                Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(folderInfo.getGuid());
                if (folder == null) {
                    continue;
                }
                FolderInfo.copyValuesFromFolderInfoToFolder(folder, folderInfo);
                folders.add(folder);
            }
            int count = AppDatabase.getInstance().foldersDao().updateFolders(folders);
            if (count == 0) {
                resultCode = CODE_UPDATE_FOLDER_GUID_NOT_EXISTED;
            }
        } catch (Exception e) {
            resultCode = CODE_UPDATE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "updateFolderWithFolderInfoSync(), exception: " + e);
        }

        return resultCode;
    }

    public static int updateFolderNameSync(Context context, String guid, String newName, String folderCover) {
        AppLogger.BASIC.d(TAG, "updateFolderNameSync(), folderid: " + guid);

        if (TextUtils.isEmpty(guid)) {
            AppLogger.BASIC.d(TAG, "updateFolderNameSync(), guid empty.");
            return CODE_UPDATE_FOLDER_GUID_EMPTY;
        }

        if (TextUtils.isEmpty(newName)) {
            AppLogger.BASIC.d(TAG, "updateFolderNameSync(), newName empty.");
            return CODE_UPDATE_FOLDER_NEW_NAME_EMPTY;
        }

        int resultCode = CODE_UPDATE_FOLDER_OK;
        try {
            Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(guid);
            boolean onlyChangeCover = folder.name.equals(newName);
            folder.name = newName;
            folder.modifyDevice = Device.getDeviceIMEI(context);
            folder.state = FolderInfo.FOLDER_STATE_MODIFIED;
            folder.extra.setCover(folderCover, NoteBookData.INSTANCE.isOldCover(folderCover));

            int folderCount = AppDatabase.getInstance().foldersDao().updateFolder(folder);
            int noteCount = 0;
            if (!onlyChangeCover) {
                List<Note> notes = AppDatabase.getInstance().noteDao().findByFolderGuid(guid);
                for (Note note : notes) {
                    note.noteFolder = newName;
                    note.state = NoteInfo.STATE_MODIFIED;
                }
                noteCount = AppDatabase.getInstance().noteDao().updateNotes(notes);
            }
            if ((folderCount == 0) && (noteCount == 0)) {
                resultCode = CODE_UPDATE_FOLDER_GUID_NOT_EXISTED;
            }
        } catch (Exception e) {
            resultCode = CODE_UPDATE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "updateFolderNameSync(), exception: " + e);
        }

        return resultCode;
    }

    public static int updateFolderNameSyncForRicNote(Context context, String guid, String newName, String folderCover) {
        AppLogger.BASIC.d(TAG, "updateFolderNameSyncForRicNote(), folderid: " + guid);

        if (TextUtils.isEmpty(guid)) {
            AppLogger.BASIC.d(TAG, "updateFolderNameSyncForRicNote(), guid empty.");
            return CODE_UPDATE_FOLDER_GUID_EMPTY;
        }

        if (TextUtils.isEmpty(newName)) {
            AppLogger.BASIC.d(TAG, "updateFolderNameSyncForRicNote(), newName empty.");
            return CODE_UPDATE_FOLDER_NEW_NAME_EMPTY;
        }

        int resultCode = CODE_UPDATE_FOLDER_OK;
        try {
            Folder folder = AppDatabase.getInstance().foldersDao().findByGuid(guid);
            folder.name = newName;
            folder.modifyDevice = Device.getDeviceIMEI(context);
            folder.modifyTime = new Date(System.currentTimeMillis());
            if (folder.state != FolderInfo.FOLDER_STATE_NEW) {
                folder.state = FolderInfo.FOLDER_STATE_MODIFIED;
            }
            folder.extra.setCover(folderCover, NoteBookData.INSTANCE.isOldCover(folderCover));

            int folderCount = AppDatabase.getInstance().foldersDao().updateFolder(folder);
            if (folderCount == 0) {
                resultCode = CODE_UPDATE_FOLDER_GUID_NOT_EXISTED;
            }
        } catch (Exception e) {
            resultCode = CODE_UPDATE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "updateFolderNameSyncForRicNote(), exception: " + e);
        }

        return resultCode;
    }

    public static void insertFolderRecovery(Folder folder) {
        AppDatabase.getInstance().foldersDao().insert(folder);
        DeleteFolderCacheHolder.removeFolderCacheRecovery(MyApplication.getAppContext(), folder);
    }

    // <========================================================================
    // <================================== update ==============================
    // <========================================================================

    // ========================================================================>
    // ================================== delete ==============================>
    // ========================================================================>

    public int deleteDeletedFoldersSync() {
        AppLogger.BASIC.d(TAG, "deleteDeletedFoldersSync()");

        int resultCode = CODE_DELETE_FOLDER_OK;
        try {
            int deleted = AppDatabase.getInstance().foldersDao().deleteFolderByState(FolderInfo.FOLDER_STATE_DELETED);
            AppLogger.BASIC.d(TAG, "deleteDeletedFoldersSync(), deleted: " + deleted);
        } catch (Exception e) {
            resultCode = CODE_DELETE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "deleteDeletedFoldersSync(), exception: " + e);
        }

        return resultCode;
    }

    public int deleteFoldersOnlySync(@NonNull Context context, @NonNull List<String> guids) {
        AppLogger.BASIC.d(TAG, "deleteFoldersOnlySync(), folderids: " + guids);

        int resultCode = CODE_DELETE_FOLDER_OK;
        try {
            List<String> willDeleteFolderGuids = new ArrayList<>();
            for (String guid : guids) {
                if (TextUtils.isEmpty(guid)) {
                    AppLogger.BASIC.d(TAG, "deleteFoldersOnlySync(), guid empty.");
                    continue;
                }
                willDeleteFolderGuids.add(guid);
            }

            AppDatabase.getInstance().foldersDao().deleteFolderByGuid(willDeleteFolderGuids);

        } catch (Exception e) {
            resultCode = CODE_DELETE_FOLDER_EXCEPTION;
            AppLogger.BASIC.d(TAG, "deleteFoldersOnlySync(), exception: " + e);
        }

        return resultCode;
    }

    public int deleteFoldersSync(@NonNull Context context, @NonNull List<String> guids, boolean deleteNotes, boolean shouldChangeStateIfNotDeleteNotes, boolean isCloudDelete) {
        if (guids.isEmpty()) {
            return CODE_DELETE_INVALID;
        }

        boolean isCloudSyncClose = NoteSyncProcess.isCloudSyncSwitchClose(context.getApplicationContext());
        AppLogger.BASIC.d(TAG, "deleteFoldersSync(), folderids: " + guids
                + ", deleteNotes: " + deleteNotes + ", isCloudSyncClose: " + isCloudSyncClose);

        int resultCode = CODE_DELETE_FOLDER_OK;
        boolean shouldClearAnchor = false;

        List<String> deleteOnCloudCloseFolderList = new ArrayList<>();
        List<String> deleteOnCloudOpenFolderList = new ArrayList<>();
        List<String> deleteNotesList = new ArrayList<>();
        List<String> notDeleteNotesList = new ArrayList<>();

        try {
            for (String guid : guids) {
                if (TextUtils.isEmpty(guid)) {
                    AppLogger.BASIC.e(TAG, "deleteFoldersSync(), guid empty.");
                    continue;
                }

                if (isCloudSyncClose) {
                    // totally delete folders: delete the folders if cloud sync is closed.
                    deleteOnCloudCloseFolderList.add(guid);

                    if (!shouldClearAnchor && hasNonStateNewFolders()) {
                        shouldClearAnchor = true;
                    }
                } else {
                    // totally delete folders: delete the folders which the state is STATE_NEW.
                    // mark folders as deleted: update the folders which the state is not STATE_NEW.
                    deleteOnCloudOpenFolderList.add(guid);
                }

                if (deleteNotes) {
                    deleteNotesList.add(guid);
                } else {
                    notDeleteNotesList.add(guid);
                }
            }

            if (deleteOnCloudCloseFolderList.size() > 0) {
                AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deleteOnCloudCloseFolderList);
            }
            if (deleteOnCloudOpenFolderList.size() > 0) {
                if (isCloudDelete) {
                    AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deleteOnCloudOpenFolderList);
                } else {
                    AppDatabase.getInstance().foldersDao().deleteNewStateFolderByGuid(deleteOnCloudOpenFolderList);
                    AppDatabase.getInstance().foldersDao().updateNotNewStateFolderStateToDeletedByGuid(deleteOnCloudOpenFolderList);
                }
            }
            if (deleteNotesList.size() > 0) {
                List<Note> notes = AppDatabase.getInstance().noteDao().findByFolderGuids(deleteNotesList);
                for (Note note : notes) {
                    note.noteFolderGuid = FolderInfo.FOLDER_GUID_NO_GUID;
                    note.noteFolder = context.getResources().getString(R.string.memo_all_notes);
                    note.recycledTime = new Date(System.currentTimeMillis());
                    note.state = NoteInfo.STATE_MODIFIED;
                }
                AppDatabase.getInstance().noteDao().updateNotes(notes);
            }
            if (notDeleteNotesList.size() > 0) {
                List<Note> notes = AppDatabase.getInstance().noteDao().findByFolderGuids(notDeleteNotesList);
                for (Note note : notes) {
                    note.noteFolderGuid = FolderInfo.FOLDER_GUID_NO_GUID;
                    note.noteFolder = context.getResources().getString(R.string.memo_all_notes);
                    if (shouldChangeStateIfNotDeleteNotes) {
                        note.state = NoteInfo.STATE_MODIFIED;
                    }
                }
                AppDatabase.getInstance().noteDao().updateNotes(notes);
            }

            if (shouldClearAnchor) {
                new AnchorManager(context).clearAnchors(SyncAgentContants.DataType.NOTE);
            }
        } catch (Exception e) {
            resultCode = CODE_DELETE_FOLDER_EXCEPTION;
            AppLogger.BASIC.e(TAG, "deleteFoldersSync(), exception: " + e);
        }
        CloudSyncTrigger.sendDataChangedBroadcast(context);
        return resultCode;
    }

    public static int deleteFoldersSyncForRichNote(@NonNull Context context, @NonNull List<String> guids, boolean deleteNotes, boolean shouldChangeStateIfNotDeleteNotes, boolean isCloudDelete) {
        if (guids.isEmpty()) {
            return CODE_DELETE_INVALID;
        }

        boolean isCloudSyncClose = NoteSyncProcess.isCloudSyncSwitchClose(context.getApplicationContext());
        AppLogger.BASIC.d(TAG, "deleteFoldersSyncForRichNote(), folderids: " + guids
                + ", deleteNotes: " + deleteNotes + ", shouldChangeStateIfNotDeleteNotes: "
                + shouldChangeStateIfNotDeleteNotes + ", isCloudDelete: " + isCloudDelete + "isCloudSyncClose:" + isCloudSyncClose);

        int resultCode = CODE_DELETE_FOLDER_OK;
        boolean shouldClearAnchor = false;
        boolean withOutTimestamp = !isCloudDelete; //本地操作不更新timestamp

        List<String> deleteOnCloudCloseFolderList = new ArrayList<>();
        List<String> deleteOnCloudOpenFolderList = new ArrayList<>();
        List<String> deleteNotesList = new ArrayList<>();
        List<String> notDeleteNotesList = new ArrayList<>();

        try {
            for (String guid : guids) {
                if (TextUtils.isEmpty(guid)) {
                    AppLogger.BASIC.e(TAG, "deleteFoldersSyncForRichNote(), guid empty.");
                    continue;
                }

                if (isCloudSyncClose) {
                    // totally delete folders: delete the folders if cloud sync is closed.
                    deleteOnCloudCloseFolderList.add(guid);

                    if (!shouldClearAnchor && hasNonStateNewFolders()) {
                        shouldClearAnchor = true;
                    }
                } else {
                    // totally delete folders: delete the folders which the state is STATE_NEW.
                    // mark folders as deleted: update the folders which the state is not STATE_NEW.
                    deleteOnCloudOpenFolderList.add(guid);
                }

                if (deleteNotes) {
                    deleteNotesList.add(guid);
                } else {
                    notDeleteNotesList.add(guid);
                }
            }

            List<Folder> deletingFolders = new ArrayList<>();
            if (!deleteOnCloudCloseFolderList.isEmpty()) {
                List<Folder> result = AppDatabase.getInstance().foldersDao().findByGuids(deleteOnCloudCloseFolderList);
                deletingFolders.addAll(result);

                AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deleteOnCloudCloseFolderList);
            }
            if (!deleteOnCloudOpenFolderList.isEmpty()) {
                if (isCloudDelete) {
                    AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deleteOnCloudOpenFolderList);
                } else {
                    List<Folder> result = AppDatabase.getInstance().foldersDao().findByGuids(deleteOnCloudOpenFolderList);
                    deletingFolders.addAll(result);

                    AppDatabase.getInstance().foldersDao().deleteNewStateFolderByGuid(deleteOnCloudOpenFolderList);
                    AppDatabase.getInstance().foldersDao().updateNotNewStateFolderStateToDeletedByGuid(deleteOnCloudOpenFolderList);
                }
            }
            if (!deleteNotesList.isEmpty()) {
                List<RichNote> notes = AppDatabase.getInstance().richNoteDao().findByFolderGuids(deleteNotesList);
                adjustRichNoteWithFolder(isCloudDelete, notes, deletingFolders);

                RichNoteRepository.INSTANCE.updateNotes(notes, withOutTimestamp);
            }

            if (!notDeleteNotesList.isEmpty()) {
                List<RichNote> notes = AppDatabase.getInstance().richNoteDao().findByFolderGuids(notDeleteNotesList);
                for (RichNote note : notes) {
                    //note.setFolderGuid(FolderInfo.FOLDER_GUID_NO_GUID);
                    if (shouldChangeStateIfNotDeleteNotes) {
                        note.setState(RichNote.STATE_MODIFIED);
                    }
                }
                RichNoteRepository.INSTANCE.updateNotes(notes, withOutTimestamp);
            }
            refreshCardList(context, null, guids, false);
            postMeetingIntentionExitedIfNeed(context, false);
            if (shouldClearAnchor) {
                // NOTE: 清空旧笔记锚点，全量恢复文件夹数据，文件夹信息由旧笔记接口同步，以此恢复被删除的文件夹。
                new AnchorManager(context).clearAnchors(SyncAgentContants.DataType.NOTE);
            }
        } catch (Exception e) {
            resultCode = CODE_DELETE_FOLDER_EXCEPTION;
            AppLogger.BASIC.e(TAG, "deleteFoldersSyncForRichNote(), exception: " + e);
        }
        CloudSyncTrigger.sendDataChangedBroadcast(context);
        return resultCode;
    }

    private static void adjustRichNoteWithFolder(boolean isCloudDelete, List<RichNote> notes, List<Folder> deletingFolders) {
        boolean shouldChangeFolder = !isCloudDelete;
        for (RichNote note : notes) {
            if (shouldChangeFolder) {
                for (Folder deletingFolder : deletingFolders) {
                    if (deletingFolder.guid.equals(note.getFolderGuid())) {
                        if (deletingFolder.isEncrypted()) {
                            note.setFolderGuid(FolderFactory.INSTANCE.getDefaultEncryptedFolderGuid());
                        } else {
                            note.setFolderGuid(FolderFactory.INSTANCE.getUncategorizedFolderGuid());
                        }
                        break;
                    }
                }
            }
            note.setRecycleTime(System.currentTimeMillis());
            note.setState(RichNote.STATE_MODIFIED);
        }
    }

    public static boolean isFolderNameExistedSync(Context context, String folderName) {
        AppLogger.BASIC.d(TAG, "isFolderNameExistedSync(), folderName: ");

        if (TextUtils.isEmpty(folderName)) {
            return false;
        } else if (folderName.equals(context.getResources().getString(R.string.recent_delete))) {
            return true;
        } else if (folderName.equals(context.getResources().getString(R.string.memo_all_notes))) {
            return true;
        } else if (folderName.equals(context.getResources().getString(com.oplus.note.baseres.R.string.encrypted_note))) {
            return true;
        } else if (folderName.equals(context.getResources().getString(com.oplus.note.baseres.R.string.uncategorized_folder))) {
            return true;
        }

        List<Folder> folders = AppDatabase.getInstance().foldersDao().findNotDeletedFolderByName(folderName);
        if (folderName.equals(context.getResources().getString(R.string.quick_note))) {
            if (folders != null) {
                for (Folder folder : folders) {
                    if (FolderInfo.FOLDER_GUID_QUICK.equals(folder.guid)) {
                        return true;
                    }
                }
                return false;
            }
        }
        if (folderName.equals(context.getResources().getString(com.oplus.note.baseres.R.string.encrypted_note))) {
            if (folders != null) {
                for (Folder folder : folders) {
                    if (!FolderInfo.FOLDER_GUID_ENCRYPTED.equals(folder.guid)) {
                        return true;
                    }
                }
                return false;
            }
        }

        return (folders != null) && (folders.size() > 0);
    }

    private static boolean hasNonStateNewFolders() {
        List<Folder> folders = AppDatabase.getInstance().foldersDao().findFoldersByStateNotEquals(FolderInfo.FOLDER_STATE_NEW);
        return folders.size() > 0;
    }
}
