/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - ShareRichSpiltHelper
 ** Description:
 **         v1.0:   Create ShareRichSpiltHelper file
 **
 ** Version: 1.0
 ** Date: 2023/05/22
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/5/22   1.0      Create this module
 ********************************************************************************/
package com.nearme.note.db

import android.content.Context
import android.net.Uri
import android.text.Editable
import android.text.SpannableStringBuilder
import androidx.annotation.VisibleForTesting
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import com.nearme.note.MyApplication
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.RichAdapter
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.ShareData
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.isTitleEmpty
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.logic.ThumbFileManager
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.PageResult
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.model.toRichNoteWithAttachments
import com.nearme.note.skin.SkinData
import com.nearme.note.util.FileUtil
import com.oplus.cloud.sync.richnote.RichNoteFactory
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.SourcePackage
import com.oplus.richtext.core.node.IItemNode
import com.oplus.richtext.core.node.MediaNode
import com.oplus.richtext.core.node.SpannedNode
import com.oplus.richtext.core.parser.HtmlParser
import com.oplus.richtext.core.parser.HtmlStandardParser
import com.oplus.richtext.editor.utils.RichEditorUtil
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import java.util.UUID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import org.jsoup.Jsoup

object ShareRichSplitHelper {

    /**
     * Plain text, split into one note per 30,000 words
     */
    @JvmStatic
    fun splitTextData(rawText: String?): List<ShareData>? {
        if (rawText.isNullOrEmpty()) {
            return null
        }

        return rawText.chunked(RichEditorUtil.CONTENT_TEXT_LENGTH_MAX).map {
            ShareData(
                ShareData.TYPE_TEXT,
                SpannableStringBuilder(it)
            )
        }
    }

    /**
     * Files ,split into one note per 50 images
     */
    @JvmStatic
    fun splitFilesData(restMediaFiles: List<MediaFileInfo>): Set<List<MediaFileInfo>>? {
        if (restMediaFiles.isEmpty()) {
            return null
        }

        return restMediaFiles.chunked(RichData.PICS_UPPER_BOUND).toSet()
    }

    /**
     * @param html            The html content obtained through
     * @param textCapacity    The capacity of text that can be added to the current note
     * @param imageCapacity   The capacity of images that can be added to the current note
     * @param contents        Save the result.
     * @return                The content to add to notes, and the content to generate new notes
     */
    fun splitHtml(
        html: String?,
        textCapacity: Int,
        imageCapacity: Int,
        contents: ArrayList<String>
    ) {
        if (html.isNullOrEmpty()) {
            return
        }
        clearRichNoteSize()
        val htmlText = HtmlTransformManagerFactory.gainHtmlTransformManager().addHtmlHead(html)
        val itemNodeList = HtmlStandardParser.fromHtml(source = htmlText, shouldSkipTidying = true)

        collectNodes(itemNodeList) { spannedNodes, mediaNodes ->
            handleNodes(itemNodeList.toMutableList(), contents, spannedNodes, mediaNodes, textCapacity, imageCapacity)
        }
    }

    @VisibleForTesting
    inline fun collectNodes(
        nodes: List<IItemNode<*>>,
        block: (ArrayMap<Int, SpannedNode>, ArrayMap<Int, MediaNode>) -> Unit
    ) {
        val spannedNodes = arrayMapOf<Int, SpannedNode>()
        val mediaNodes = arrayMapOf<Int, MediaNode>()
        nodes.forEachIndexed { index, node ->
            when (node) {
                is SpannedNode -> spannedNodes[index] = node
                is MediaNode -> mediaNodes[index] = node
                else -> {
                    AppLogger.BASIC.e(
                        TAG, "collectNodes:" +
                                "unknown node:$node"
                    )
                }
            }
        }
        AppLogger.BASIC.d(TAG, "spannedNodes size: ${spannedNodes.size}, mediaNodes size: ${mediaNodes.size}")
        block(spannedNodes, mediaNodes)
    }

    @VisibleForTesting
    @JvmStatic
    fun handleNodes(
        nodes: MutableList<IItemNode<*>>,
        contents: ArrayList<String>,
        spannedNodes: ArrayMap<Int, SpannedNode>,
        mediaNodes: ArrayMap<Int, MediaNode>,
        textCapacity: Int,
        imageCapacity: Int
    ) {
        findSpannedNodeForSplit(spannedNodes, textCapacity) { index, offset, needToSplit ->
            val finalIndex = findSplitIndex(mediaNodes, index, needToSplit, imageCapacity)
            if (finalIndex == index) {
                spannedNodes[index]?.let { node ->
                    // 如果offset不等于SpannedNode长度，说明该SpannedNode需要拆分
                    if (offset != node.length) {
                        nodes.remove(node).also {
                            AppLogger.BASIC.d(TAG, "remove node success: $it")
                        }
                        // 拆分完成后，重新add到原node列表中
                        with(splitSpannedNode(node, offset)) {
                            nodes.add(index, first)
                            nodes.add(index + 1, second)
                        }
                    }
                }
            }

            // 取出拆分的nodes转化为HTML
            val list = nodes.take(finalIndex + 1)
            transNodesToHtml(contents, list)
            // 其他的nodes继续拆分
            val other = nodes.drop(finalIndex + 1)
            if (other.isEmpty()) {
                AppLogger.BASIC.d(TAG, "other list is empty")
                return
            }
            // 递归处理其他的nodes
            collectNodes(other) { spans, medias ->
                handleNodes(
                    other.toMutableList(),
                    contents,
                    spans,
                    medias,
                    RichEditorUtil.CONTENT_TEXT_LENGTH_MAX,
                    RichData.PICS_UPPER_BOUND
                )
            }
        }
    }

    private fun findSplitIndex(
        mediaNodes: ArrayMap<Int, MediaNode>,
        index: Int,
        needToSplit: Boolean,
        imageCapacity: Int
    ): Int {

        val mediaNodeIndexes = mediaNodes.keys
        // 计算mediaNodes中keys小于index的数量
        val count = mediaNodeIndexes.count { it < index }
        AppLogger.BASIC.d(TAG, "findSplitIndex, count :$count")

        return if (count <= imageCapacity) {
            /**
             * 若小于等于50并且SpannedNode需要拆分，则直接返回index
             */
            if (needToSplit) {
                index
            } else {
                /**
                 * 此种场景理论上很小概率触发，需要spanNode刚好被整分时才走此逻辑
                 * 若等于50，说明刚好满足50张图，3万字以内，直接返回index
                 * 若小于50，需要判断小于50张的图片是否正好依次跟随在需要拆分的SpannedNode后面，尽量补齐50张图
                 **/
                getFollowedImagesIndex(mediaNodeIndexes, index, imageCapacity - count)
            }
        } else {
            mediaNodeIndexes.toList()[imageCapacity - 1]
        }
    }

    @VisibleForTesting
    fun getFollowedImagesIndex(mediaNodeIndexes: Set<Int>, index: Int, count: Int): Int {
        var result = index
        for (i in 1..count) {
            val element = index + i
            if (mediaNodeIndexes.contains(element).not()) {
                break
                //非图片，直接退出循环
            } else {
                result = index + i
            }
        }
        return result
    }

    /***
     * 找到需要拆分的文本所在SpannedNodes的index和该文本需要拆分的位置
     */
    private inline fun findSpannedNodeForSplit(
        spannedNodes: ArrayMap<Int, SpannedNode>,
        textCapacity: Int,
        block: (Int, Int, Boolean) -> Unit
    ) {
        var index = 0
        var offset = 0
        var length = 0
        var needToSplit = false
        for (entry in spannedNodes) {
            length += entry.value.length
            index = entry.key
            AppLogger.BASIC.d(TAG, "length: $length, value length: ${entry.value.length}")
            // 找到文本总长度大于textCapacity字的index和offset
            if (length >= textCapacity) {
                offset = entry.value.length - (length - textCapacity)
                needToSplit = (length != textCapacity)
                break
            } else {
                offset = entry.value.length
            }
        }
        block(index, offset, needToSplit)
    }

    @VisibleForTesting
    fun splitSpannedNode(node: SpannedNode, offset: Int): Pair<SpannedNode, SpannedNode> {
        val first = node.subSequence(0, offset) as SpannableStringBuilder
        val second = node.subSequence(offset, node.length) as SpannableStringBuilder
        return Pair(createSpannedNode(first), createSpannedNode(second))
    }

    @VisibleForTesting
    internal fun createSpannedNode(charSequence: CharSequence): SpannedNode {
        return SpannedNode(charSequence)
    }

    @VisibleForTesting
    fun transNodesToHtml(contents: ArrayList<String>, nodes: List<IItemNode<*>>) {
        val sb = StringBuilder()
        nodes.forEach {
            when (it) {
                is MediaNode -> HtmlStandardParser.withinMediaNode(sb, it)
                is SpannedNode -> HtmlStandardParser.withinSpanned(sb, it.data, false)
            }
        }
        contents.add(sb.toString())
    }

    private var richNoteSize = 0

    @VisibleForTesting
    @JvmStatic
    fun clearRichNoteSize() {
        richNoteSize = 0
    }

    fun getRichNoteSize(): Int = richNoteSize

    suspend fun saveRichToNewNote(
        context: Context?,
        htmlContent: String,
        oldRichData: RichData,
        index: Int = 0
    ): RichNoteWithAttachments {

        richNoteSize = index

        val attachmentList = mutableListOf<Attachment>()
        val newTitle = getIndexedTitle(oldRichData, index - 1)
        val guid = UUID.randomUUID().toString()
        val currentTime = System.currentTimeMillis()
        val note = RichNoteFactory.createRichNote(guid, currentTime).also { note ->
            note.title = newTitle.toString()
            note.rawTitle = newTitle?.let { HtmlParser.serialize(it) }
        }

        val attachmentMap = HashMap<String, Attachment>()
        val htmlText =
            HtmlTransformManagerFactory.gainHtmlTransformManager().addHtmlHead(htmlContent)
        HtmlStandardParser.fromHtml(htmlText).filterIsInstance<MediaNode>().forEach { node ->
            val uri = node.data
            if (attachmentMap.contains(uri)) {
                return@forEach
            } else {
                val picAttachment = createAttachment(note.localId)
                FileUtil.copyFileFromUri(
                    MyApplication.appContext,
                    Uri.parse(uri),
                    picAttachment.absolutePath(MyApplication.appContext)
                )
                attachmentList.add(picAttachment)
                attachmentMap[uri] = picAttachment
            }
        }
        var result = htmlText
        attachmentMap.forEach { map ->
            result = result.replace(map.key, map.value.attachmentId)
        }
        note.rawText = HtmlTransformManagerFactory.gainHtmlTransformManager().toRawText(result)
        note.text = Jsoup.parse(result).text()
        //必要参数，外部不需要提供
        note.updateTime = currentTime
        note.state = RichNote.STATE_NEW
        note.folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
        note.skinId = SkinData.COLOR_SKIN_WHITE
        return RichNoteWithAttachments(note, attachments = attachmentList)
    }

    @JvmStatic
    fun createAttachment(localId: String): Attachment {
        return UUID.randomUUID().toString().let {
            Attachment(
                attachmentId = it,
                type = Attachment.TYPE_PICTURE,
                richNoteId = localId
            )
        }
    }

    @JvmStatic
    @VisibleForTesting
    suspend fun createNewTextNote(
        context: Context?,
        newTitle: Data,
        content: Editable?,
        restCard: PageResult?,
        sourcePackage: SourcePackage?
    ): RichNoteWithAttachments {
        val items = mutableListOf<Data>()

        items.add(
            Data(
                type = Data.TYPE_TEXT,
                text = SpannableStringBuilder(content)
            )
        )
        restCard?.apply {
            items.add(
                Data(
                    type = Data.TYPE_CARD,
                    card = this
                )
            )
        }

        val richData = RichData(
            metadata = RichNote(folderGuid = FolderInfo.FOLDER_GUID_NO_GUID).apply {
                updateTime = System.currentTimeMillis()
                CoroutineScope(Dispatchers.IO).launch {
                    ThumbFileManager.ensureRichNoteFolderExist(localId)
                }
            },
            title = newTitle,
            items = items,
            coverPictureAttachment = null
        )

        return richData.toRichNoteWithAttachments(sourcePackage = sourcePackage)
    }

    @JvmStatic
    suspend fun createNoteInternal(context: Context?, note: RichNoteWithAttachments) {
        kotlin.runCatching {
            RichNoteRepository.insert(note)
            NoteCardWidgetProvider.instance.postUIToCard(false)
            sendNotify(context)
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "createNewNote success")
        }.onFailure {
            AppLogger.BASIC.d(TAG, "createNewNote failed")
        }
    }

    @JvmStatic
    suspend fun createNotesInternal(context: Context?, notes: List<RichNoteWithAttachments>) {
        if (notes.isEmpty()) {
            return
        }
        kotlin.runCatching {
            AppLogger.BASIC.d(TAG, "createNewNote begin")
            RichNoteRepository.insertList(notes)
            NoteCardWidgetProvider.instance.postUIToCard(false)
            sendNotify(context)
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "createNewNote success")
        }.onFailure {
            AppLogger.BASIC.d(TAG, "createNewNote failed")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun sendNotify(context: Context?) {
        val uri = NotesProvider.NOTE_DATA_CHANGE_URI
        val uriNew = NotesProvider.NOTE_DATA_CHANGE_URI_NEW
        val notify = uri.getQueryParameter(NotesProvider.PARAMETER_NOTIFY)
        val notifyNew = uriNew.getQueryParameter(NotesProvider.PARAMETER_NOTIFY)
        if (null == notify || "true" == notify) {
            context?.contentResolver?.notifyChange(uri, null)
        }
        if (null == notifyNew || "true" == notifyNew) {
            context?.contentResolver?.notifyChange(uriNew, null)
        }
    }

    @JvmStatic
    fun getIndexedTitle(oldRichData: RichData, index: Int): Editable? {
        return if (oldRichData.isTitleEmpty() || (oldRichData.title.text?.length
                ?: 0) >= RichAdapter.TITLE_MAX_LENGTH
        ) {
            oldRichData.title.text
        } else {
            SpannableStringBuilder(oldRichData.title.text).append("(${index + 1})")
        }
    }

    const val CONTENT = "content"
    const val TITLE = "title"
    const val PARAMETER_NOTIFY = "notify"
    const val TAG = "ShareRichSplitHelper"
}