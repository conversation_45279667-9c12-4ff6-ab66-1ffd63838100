package com.nearme.note.db.daos;

import android.database.Cursor;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;
import androidx.sqlite.db.SimpleSQLiteQuery;

import com.oplus.note.repo.note.entity.FolderInfo;
import com.nearme.note.db.entities.Word;
import com.nearme.note.util.SqlUtils;

import java.util.List;

@Dao
public abstract class WordDao extends BaseDao {

    @Query("SELECT * FROM words")
    public abstract List<Word> getAll();

    @Query("delete from words")
    public abstract int deleteAll();

    @Query("delete from words where note_guid = :noteGuid")
    public abstract int deleteByNoteGuid(String noteGuid);

    @Transaction
    public int deleteByNoteGuids(List<String> noteGuids) {
        String ids = SqlUtils.joinIds(noteGuids);
        String sql = "DELETE FROM words WHERE note_guid IN (" + ids + ")";
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Query("select * from words where note_guid = :noteGuid")
    public abstract Word findByNoteGuid(String noteGuid);

    @Update
    public abstract void update(Word word);

    @Insert
    public abstract void insert(Word word);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insert(List<Word> word);

    @Query("update words set note_guid=:newGuid where note_guid=:oldGuid")
    public abstract void updateNewGuid(String newGuid, String oldGuid);

    /**
     * This method is call from External searching
     *
     * @param searchText
     * @return
     */
    @Query("SELECT words._id, words.note_guid, words.content, words.updated FROM words LEFT JOIN notes on words.note_guid = notes.guid "
            + "WHERE notes.note_folder_guid != :encrypted  AND (words.content LIKE :searchText escape '/') "
            + "ORDER BY words.updated DESC")
    public abstract Cursor doFindNoteFromExternalSearch(String searchText, String encrypted);

    public Cursor findNoteFromExternalSearch(String searchText) {
        return doFindNoteFromExternalSearch(searchText, FolderInfo.FOLDER_GUID_ENCRYPTED);
    }
}
