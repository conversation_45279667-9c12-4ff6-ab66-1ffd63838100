package com.nearme.note.db.entities;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.oplus.note.repo.todo.entity.DateConverters;

import java.util.Date;

@Entity(tableName = "notes_attributes")
public class NotesAttribute {

    @NonNull
    @ColumnInfo(name = "_id")
    @PrimaryKey(autoGenerate = true)
    public int id;

    @ColumnInfo(name = "note_guid")
    public String noteGuid;

    @NonNull
    @ColumnInfo(name = "type", defaultValue = "0")
    public int type;

    @ColumnInfo(name = "filename")
    public String filename;

    @NonNull
    @ColumnInfo(name = "version", defaultValue = "0")
    public int noteAttrOwner;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "updated", defaultValue = "0")
    public Date attrCreated;

    @Nullable
    @ColumnInfo(name = "para")
    public String para;

    @NonNull
    @ColumnInfo(name = "state", defaultValue = "1")
    public int state;

    /*
     * 附件MD5, TEXT类型，提供给云服务便签同步使用，用于保存附件同步时的通过本地压缩后的附件生成的MD5。 用于对比本地附件是否有修改，本地和服务端附件是否一致的判断。
     */
    @Nullable
    @ColumnInfo(name = "attachment_md5")
    public String attachmentMd5;

    /*
     * 附件同步url，TEXT类型，用于保存在同步过程中，服务器下发的附件保存地址。
     */
    @Nullable
    @ColumnInfo(name = "attachment_sync_url")
    public String attachmentSyncUrl;

    /*
     * 附件同步备用字段
     */
    @Nullable
    @ColumnInfo(name = "sync_data1")
    public String syncData1;

    /*
     * picture width。
     */
    @NonNull
    @ColumnInfo(name = "width", defaultValue = "0")
    public int width;

    /*
     * picture height。
     */
    @NonNull
    @ColumnInfo(name = "height", defaultValue = "0")
    public int height;
}
