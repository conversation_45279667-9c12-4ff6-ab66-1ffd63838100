/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: RichNoteExtensions.kt
 * Description: RichNote extensions
 *
 *
 * Version: 1.0
 * Date: 2023-05-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                       2023-05-17    1.0    Create this module
 **********************************************************************************/
@file:JvmName("RichNoteExtensions")

package com.nearme.note.db

import android.content.ContentValues
import android.content.Context
import android.text.SpannableStringBuilder
import androidx.annotation.VisibleForTesting
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.skin.SkinData
import com.nearme.note.thirdlog.ThirdLogNoteBuildHelper
import com.nearme.note.util.OplusDateUtils
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.parser.HtmlParser

internal fun RichNote.initSkin(values: ContentValues) {
    val skinId = values.getAsString(NotesProviderPresenter.KEY_SKIN_ID)
    this.skinId = if (skinId.isNullOrEmpty()) {
        SkinData.COLOR_SKIN_WHITE
    } else {
        if (SkinData.isImgSkin(skinId) || SkinData.isColorSkin(skinId)) {
            skinId
        } else {
            SkinData.COLOR_SKIN_WHITE
        }
    }
}

internal fun RichNote.initFolderGuid(values: ContentValues, context: Context?) {
    val folderGuid = values.getAsString(NotesProviderPresenter.KEY_FOLDER_GUID)
    this.folderGuid = if (folderGuid.isNullOrEmpty()) {
        FolderInfo.FOLDER_GUID_NO_GUID
    } else {
        val folder = AppDatabase.getInstance().foldersDao().findByGuid(folderGuid)
        AppLogger.BASIC.d(NoteProviderHelper.TAG, "folder guid is not empty")
        if (folder == null) {
            if (folderGuid == FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY) {
                FolderUtil.createSummaryNoteByFolderGuid(folderGuid)
                FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY
            } else if (folderGuid == FolderInfo.FOLDER_GUID_CALL_SUMMARY) {
                FolderUtil.createSummaryNoteByFolderGuid(folderGuid)
                FolderInfo.FOLDER_GUID_CALL_SUMMARY
            } else if (folderGuid == FolderInfo.FOLDER_GUID_AUDIO_SUMMARY) {
                FolderUtil.createSummaryNoteByFolderGuid(folderGuid)
                FolderInfo.FOLDER_GUID_AUDIO_SUMMARY
            } else {
                FolderInfo.FOLDER_GUID_NO_GUID
            }
        } else {
            AppLogger.BASIC.d(NoteProviderHelper.TAG, "folder has been created ,folder = $folder")
            folder.guid
        }
    }
}

internal fun RichNote.initTime(time: Long, values: ContentValues) {
    this.updateTime = time
    this.initTopTime(time, values)
    this.initRecycleTime(time, values)
}

@VisibleForTesting
internal fun RichNote.initTopTime(time: Long, values: ContentValues) {
    if (values.getBoolean(NotesProviderPresenter.KEY_TOP)) {
        this.topTime = time
    }
}

@VisibleForTesting
internal fun RichNote.initRecycleTime(time: Long, values: ContentValues) {
    if (values.getBoolean(NotesProviderPresenter.KEY_RECYCLE_TIME)) {
        this.recycleTime = time
    }
}

internal fun RichNote.initAlarmTime(values: ContentValues): Boolean {
    val alarmTime = values.getAsLong(NotesProviderPresenter.KEY_ALARM_TIME)
    return alarmTime.moreThanZero().also {
        if (it) {
            this.alarmTime = OplusDateUtils.calendarSwitchByMinuteAddOneMinute(alarmTime)
        }
    }
}

internal fun RichNote.initTitle(values: ContentValues) {
    var title = values.getAsString(NotesProviderPresenter.KEY_TITLE)
    if (title.isNullOrEmpty()) {
        this.title = ""
        this.rawTitle = ""
    } else {
        if (title.length > ThirdLogNoteBuildHelper.SUMMARY_TITLE_LIMIT) {
            title = title.substring(0, ThirdLogNoteBuildHelper.SUMMARY_TITLE_LIMIT)
        }
        this.title = title
        this.rawTitle = HtmlParser.serialize(SpannableStringBuilder(title))
    }
}

internal fun Number?.moreThanZero(): Boolean {
    return this != null && this.toLong() > 0
}

internal fun ContentValues.getBoolean(key: String): Boolean {
    return this.getAsBoolean(key) ?: false
}