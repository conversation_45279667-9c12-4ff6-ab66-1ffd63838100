/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoRecover.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023/10/31
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Unknown                         2019/08/06    1.0     Create this module
 * W9005794                        2023/10/31    2.0     change java to kt
 **********************************************************************************/
package com.nearme.note.db.entity

import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import java.util.Date

class NoteInfoRecover {
    companion object {
        @JvmStatic
        fun getInstance(): NoteInfoRecover {
            return NoteInfoRecoverHolder.instance
        }
    }

    private object NoteInfoRecoverHolder {
        val instance = NoteInfoRecover()
    }

    fun recoverNote(guid: String?): Boolean {
        val note = AppDatabase.getInstance().noteDao().findByGuid(guid) ?: return false
        note.recycledTime = Date(0)
        note.state = NoteInfo.STATE_MODIFIED
        return AppDatabase.getInstance().noteDao().updateNote(note) > 0
    }
}