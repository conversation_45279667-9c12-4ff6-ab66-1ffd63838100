/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: RichNoteSplitHelper.kt
 * Description: Split helper for html content insert
 *
 *
 * Version: 1.0
 * Date: 2023-05-18
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                       2023-05-18    1.0    Create this module
 **********************************************************************************/
package com.nearme.note.db

import android.content.ContentValues
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Path
import android.net.Uri
import android.text.SpannableStringBuilder
import androidx.annotation.VisibleForTesting
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import com.nearme.note.MyApplication
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.db.NotesProviderPresenter.KEY_COUNT
import com.nearme.note.util.FileUtil
import com.nearme.note.util.StatisticsUtils
import com.oplus.note.logger.AppLogger
import com.oplus.note.os.MediaStoreHelper
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.SubAttachment
import com.nearme.note.speech.utils.PresetNoteSpeechUtils
import com.oplus.note.repo.note.entity.CommonExtra
import com.oplus.richtext.core.html.HtmlTags
import com.oplus.richtext.core.node.IItemNode
import com.oplus.richtext.core.node.MediaNode
import com.oplus.richtext.core.node.SpannedNode
import com.oplus.richtext.core.node.SpannedNode.Companion.TYPE_SUMMARY
import com.oplus.richtext.core.parser.HtmlStandardParser
import com.oplus.richtext.core.parser.RawTextParser
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import java.util.UUID

object RichNoteSplitHelper {
    private const val TAG = "RichNoteSplitHelper"
    private const val TITLE_MAX_LENGTH = 50
    @VisibleForTesting
    internal const val IMAGE_LIMIT = 50

    @VisibleForTesting
    internal const val CONTENT_LIMIT = 30000
    internal const val KEY_SPLIT = "has_split"
    internal const val KEY_IS_FIRST_NOTE = "is_first_note"

    @VisibleForTesting
    internal fun splitHtmlContent(contents: ArrayList<String>, html: String) {
        val nodes = HtmlStandardParser.fromHtml(html)
        collectNodes(nodes) { spannedNodes, mediaNodes ->
            handleStatistics(spannedNodes)
            handleNodes(nodes.toMutableList(), contents, spannedNodes, mediaNodes)
        }
    }

    private inline fun collectNodes(
        nodes: List<IItemNode<*>>,
        block: (ArrayMap<Int, SpannedNode>, ArrayMap<Int, MediaNode>) -> Unit
    ) {
        val spannedNodes = arrayMapOf<Int, SpannedNode>()
        val mediaNodes = arrayMapOf<Int, MediaNode>()
        nodes.forEachIndexed { index, node ->
            when (node) {
                is SpannedNode -> spannedNodes[index] = node
                is MediaNode -> mediaNodes[index] = node
                else -> AppLogger.BASIC.d(TAG, "unknown node: $node")
            }
        }
        AppLogger.BASIC.d(
            TAG,
            "spannedNodes size: ${spannedNodes.size}, mediaNodes size: ${mediaNodes.size}"
        )
        block(spannedNodes, mediaNodes)
    }

    @VisibleForTesting
    internal fun handleNodes(
        nodes: MutableList<IItemNode<*>>,
        contents: ArrayList<String>,
        spannedNodes: ArrayMap<Int, SpannedNode>,
        mediaNodes: ArrayMap<Int, MediaNode>
    ) {
        findSpannedNodeForSplit(spannedNodes) { index, offset, needToSplit ->
            val finalIndex = findSplitIndex(mediaNodes, index, needToSplit)
            AppLogger.BASIC.d(TAG, "index: $index, offset: $offset, finalIndex: $finalIndex")
            if (finalIndex == index) {
                handleSplitSpannedNode(nodes, index, offset, spannedNodes, needToSplit)
            }
            // 取出拆分的nodes转化为HTML
            val list = nodes.take(finalIndex + 1)
            transNodesToHtml(contents, list)
            handleOtherNodes(nodes, contents, finalIndex)
        }
    }

    @VisibleForTesting
    internal fun handleStatistics(spannedNodes: ArrayMap<Int, SpannedNode>) {
        spannedNodes.values.sumOf { it.length }.also {
            AppLogger.BASIC.d(TAG, "text count: $it")
            StatisticsUtils.setSpeechToTextWords(it)
        }
    }

    @VisibleForTesting
    internal fun handleOtherNodes(
        nodes: MutableList<IItemNode<*>>,
        contents: ArrayList<String>,
        finalIndex: Int
    ) {
        // 其他的nodes继续拆分
        val other = nodes.drop(finalIndex + 1)
        if (other.isEmpty()) {
            AppLogger.BASIC.d(TAG, "other list is empty")
            return
        }
        // 递归处理其他的nodes
        collectNodes(other) { spans, medias ->
            handleNodes(other.toMutableList(), contents, spans, medias)
        }
    }

    @VisibleForTesting
    internal fun handleSplitSpannedNode(
        nodes: MutableList<IItemNode<*>>,
        index: Int,
        offset: Int,
        spannedNodes: ArrayMap<Int, SpannedNode>,
        needToSplit: Boolean
    ) {
        spannedNodes[index]?.let { node ->
            // 如果offset不等于SpannedNode长度，说明该SpannedNode需要拆分
            if (needToSplit) {
                // 拆分完成后，重新add到原node列表中
                with(splitSpannedNode(node, offset)) {
                    nodes[index] = first
                    if (index + 1 > nodes.size) {
                        nodes.add(second)
                    } else {
                        nodes.add(index + 1, second)
                    }
                }
            }
        }
    }

    @VisibleForTesting
    internal fun createSpannedNode(charSequence: CharSequence): SpannedNode {
        return SpannedNode(charSequence)
    }

    @VisibleForTesting
    internal fun splitSpannedNode(node: SpannedNode, offset: Int): Pair<SpannedNode, SpannedNode> {
        val first = node.subSequence(0, offset) as SpannableStringBuilder
        val second = node.subSequence(offset, node.length) as SpannableStringBuilder
        return Pair(createSpannedNode(first), createSpannedNode(second))
    }

    @VisibleForTesting
    internal fun transNodesToHtml(contents: ArrayList<String>, nodes: List<IItemNode<*>>) {
        val sb = StringBuilder()
        nodes.forEach {
            when (it) {
                is MediaNode -> HtmlStandardParser.withinMediaNode(sb, it)
                is SpannedNode -> {
                    wrapCustomDiv(it, sb) {
                        HtmlStandardParser.withinSpanned(sb, it, false)
                    }
                }
            }
        }
        contents.add(sb.toString())
    }

    /***
     * 找到整个富文本最终需要拆分的位置
     * @param mediaNodes 图片节点集合
     * @param index 需要拆分的SpannedNode的index位置
     */
    @VisibleForTesting
    internal fun findSplitIndex(
        mediaNodes: ArrayMap<Int, MediaNode>,
        index: Int,
        needToSplit: Boolean
    ): Int {
        val mediaNodeIndexes = mediaNodes.keys
        // 计算mediaNodes中keys小于index的数量
        val count = mediaNodeIndexes.count { it < index }
        AppLogger.BASIC.d(TAG, "findSplitIndex count: $count")
        return if (count <= IMAGE_LIMIT) {
            // 若小于等于50并且SpannedNode需要拆分，则直接返回index
            if (needToSplit) {
                index
            } else {
                /** 若等于50，说明刚好满足50张图，3万字以内，直接返回index
                 ** 若小于50，需要判断小于50张的图片是否正好依次跟随在需要拆分的SpannedNode后面，尽量补齐50张图
                 **/
                getFollowedImagesIndex(mediaNodeIndexes, index, IMAGE_LIMIT - count)
            }
        } else {
            // 若大于50，则需要拆分图片，找到第51张图的位置，返回其前一位置
            mediaNodeIndexes.toList()[IMAGE_LIMIT] - 1
        }
    }

    @VisibleForTesting
    internal fun getFollowedImagesIndex(mediaNodeIndexes: Set<Int>, index: Int, count: Int): Int {
        var result = index
        for (i in 1..count) {
            val element = index + i
            if (mediaNodeIndexes.contains(element)) {
                result = element
            } else {
                // 遍历到非图片时，退出循环
                break
            }
        }
        return result
    }

    /***
     * 找到需要拆分的文本所在SpannedNodes的index和该文本需要拆分的位置
     */
    private inline fun findSpannedNodeForSplit(
        spannedNodes: ArrayMap<Int, SpannedNode>,
        block: (Int, Int, Boolean) -> Unit
    ) {
        var index = 0
        var offset = 0
        var length = 0
        var needToSplit = false
        for (entry in spannedNodes) {
            length += entry.value.length
            index = entry.key
            AppLogger.BASIC.d(TAG, "length: $length, value length: ${entry.value.length}")
            // 找到文本总长度大于3万字的index和offset
            if (length >= CONTENT_LIMIT) {
                offset = entry.value.length - (length - CONTENT_LIMIT)
                needToSplit = length != CONTENT_LIMIT
                break
            } else {
                offset = entry.value.length
            }
        }
        block(index, offset, needToSplit)
    }

    @VisibleForTesting
    internal fun handleHtmlPart(
        context: Context?,
        uri: Uri,
        values: ContentValues,
        callingPackage: String?,
        htmlPart: String
    ): Uri {
        val htmlContent = getStandardHtmlContent(htmlPart)
        val doc = Jsoup.parse(htmlContent)
        doc.select("ol br, ul br").forEach { br ->
            // 直接移除 br 标签
            br.remove()
        }
        return with(values) {
            remove(NotesProviderPresenter.KEY_CONTENT)
            put(NotesProviderPresenter.KEY_HTML_CONTENT, doc.html())
            NoteProviderHelper.insertTextNote(context, uri, this, callingPackage)
        }
    }

    @VisibleForTesting
    internal fun getStandardHtmlContent(htmlPart: String): String {
        return HtmlTransformManagerFactory.gainHtmlTransformManager().addHtmlHead(htmlPart)
    }

    internal fun handleMediaAttachment(
        html: String,
        localId: String,
        attachmentList: MutableList<Attachment>,
        isAccess: Boolean = false
    ): String {
        val attachmentMap = HashMap<String, Attachment>()
        val nodes = HtmlStandardParser.fromHtml(html)
        nodes.filterIsInstance<MediaNode>().forEach {
            val uri = it.data
            AppLogger.BASIC.d(TAG, "handleMediaAttachment uri: $uri - type:${it.type}")
            if (attachmentMap.contains(uri)) {
                return@forEach
            } else {
               when (it.type) {
                   MediaNode.TYPE_IMAGE -> {
                       val picAttachment = createImageAttachment(localId, it.alt)
                       AppLogger.BASIC.d(TAG, "save pic attachment to note: $localId")
                       FileUtil.copyFileFromUri(
                           appContext,
                           Uri.parse(uri),
                           picAttachment.absolutePath(appContext)
                       )
                       if (isAccess) {
                           val pairAttr = calPictureAttr(picAttachment)
                           picAttachment.picture = Picture(pairAttr.first, pairAttr.second)
                       }
                       attachmentList.add(picAttachment)
                       attachmentMap[uri] = picAttachment
                   }

                   MediaNode.TYPE_FILE, MediaNode.TYPE_AUDIO -> {
                       AppLogger.BASIC.d(TAG, "save file attachment to note: $localId")
                       val attachments = createMediaAttachments(localId, it)
                       attachments.first.apply {
                           attachmentList.add(this)
                           attachmentMap[uri] = this
                       }
                       attachmentList.add(attachments.second)
                   }

                   else -> AppLogger.BASIC.e(TAG, "not support:${it.type}")
               }
            }
        }
        var result = transToRawText(nodes)
        attachmentMap.forEach {
            result = result.replace(it.key, it.value.attachmentId)
        }
        return result
    }
    fun calPictureAttr(picAttachment: Attachment, picAttrPath: Path? = null): Pair<Int, Int> {
        val picPath = picAttrPath ?: picAttachment.absolutePath(appContext)
        var pictureAttrWidth = picAttachment.picture?.width ?: 0
        var pictureAttrHeight = picAttachment.picture?.height ?: 0
        if (pictureAttrWidth == 0 && pictureAttrHeight == 0) {
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(picPath.toString(), options)
            pictureAttrWidth = options.outWidth
            pictureAttrHeight = options.outHeight
            AppLogger.BASIC.d(
                TAG,
                "handleMediaAttachment  pictureAttrWidth:$pictureAttrWidth  pictureAttrHeight:$pictureAttrHeight"
            )
        }
        return Pair(pictureAttrWidth, pictureAttrHeight)
    }

    @VisibleForTesting
    internal fun createImageAttachment(localId: String, alt: String?): Attachment {
        return UUID.randomUUID().toString().let {
            Attachment(
                attachmentId = it,
                type = Attachment.TYPE_PICTURE,
                richNoteId = localId,
                fileName = alt
            ).apply {
                this.picture?.height
            }
        }
    }

    @VisibleForTesting
    internal fun createMediaAttachments(localId: String, mediaNode: MediaNode): Pair<Attachment, Attachment> = runBlocking {
        AppLogger.BASIC.d(TAG, "createAttachment_ start: ${Thread.currentThread().name}")
        val startTime = System.currentTimeMillis()
        val fileName = mediaNode.alt ?: ""
        val type = mediaNode.type
        val sourceUri = mediaNode.data
        val picAttachment = UUID.randomUUID().toString().let {
            Attachment(
                attachmentId = it,
                type = Attachment.TYPE_PICTURE,
                richNoteId = localId
            )
        }
        val attType = when (type) {
            MediaNode.TYPE_AUDIO -> Attachment.TYPE_VOICE
            MediaNode.TYPE_VIDEO -> Attachment.TYPE_VOICE
            MediaNode.TYPE_FILE -> Attachment.TYPE_FILE_CARD
            else -> throw IllegalStateException("not support type:$type")
        }
        val spaceImage = when (attType) {
            Attachment.TYPE_VOICE -> PresetNoteSpeechUtils.PRESET_NOTE_SPEECH_VOICE_ATTACHMENT
            Attachment.TYPE_FILE_CARD -> PresetNoteSpeechUtils.PRESET_NOTE_FILE_CARD_ATTACHMENT
            else -> throw IllegalStateException("not support type:$type")
        }

        val attachment = Attachment(
            attachmentId = UUID.randomUUID().toString(),
            richNoteId = localId,
            type = attType,
            fileName = fileName
        )
        attachment.subAttachment = SubAttachment(associateAttachmentId = picAttachment.attachmentId)
        attachment.extra = CommonExtra(createTime = System.currentTimeMillis())

        withContext(Dispatchers.IO) {
            val bitmap =
                MyApplication.appContext.assets.open(spaceImage)
                    .let { com.nearme.note.activity.edit.MediaUtils.getThumbBitmapFromInputStream(it) }
            bitmap?.apply {
                picAttachment.picture = Picture(bitmap.width, bitmap.height)
            }

            kotlin.runCatching {
                FileUtil.saveBmpToFile(bitmap, picAttachment.absolutePath(appContext))
                MediaStoreHelper.copyAttFromUri(
                    Uri.parse(sourceUri),
                    attachment.absolutePath(MyApplication.appContext),
                    MyApplication.appContext
                )
            }.onFailure {
                AppLogger.BASIC.e(TAG, "copyAttFromUri error:${it.message}")
            }.onSuccess {
                AppLogger.BASIC.d(TAG, "copyAttFromUri end.")
            }
        }
        AppLogger.BASIC.d(TAG, "createAttachment_ end ,cost = ${System.currentTimeMillis() - startTime}ms")
        return@runBlocking Pair(picAttachment, attachment)
    }

    fun insertHtmlNote(
        context: Context?,
        uri: Uri,
        values: ContentValues,
        callingPackage: String?,
        html: String
    ): Uri {
        val contents = arrayListOf<String>()
        splitHtmlContent(contents, html)
        val results = arrayListOf<Uri>()
        values.put(KEY_SPLIT, true)
        val size = contents.size
        AppDatabase.getInstance().runInTransaction {
            val title = values.getAsString(NotesProviderPresenter.KEY_TITLE)
            contents.forEachIndexed { index, content ->
                values.put(KEY_IS_FIRST_NOTE, index == 0)
                updateTitle(title, values, index, size)
                results.add(handleHtmlPart(context, uri, values, callingPackage, content))
            }
        }
        return size.let {
            AppLogger.BASIC.d(TAG, "insert html count: $it")
            StatisticsUtils.setSpeechNumberOfNotes(it)
            results.first().buildUpon().appendQueryParameter(KEY_COUNT, "$size")
                .build()
        }
    }

    @VisibleForTesting
    internal fun updateTitle(title: String?, values: ContentValues, index: Int, size: Int) {
        if (title.isNullOrEmpty() || size <= 1 || title.length >= TITLE_MAX_LENGTH) {
            return
        }
        values.put(NotesProviderPresenter.KEY_TITLE, "$title(${index + 1})")
    }

    @VisibleForTesting
    @JvmStatic
    internal fun transToHtmlText(nodes: List<IItemNode<*>>): String {
        val out = StringBuilder()
        nodes.forEach {
            when (it) {
                is MediaNode -> HtmlStandardParser.parseMediaNodeToHtmlText(out, it)
                is SpannedNode -> {
                    wrapCustomDiv(it, out) {
                        HtmlStandardParser.withinSpanned(out, it, false)
                    }
                }
            }
        }
        return getStandardHtmlContent(out.toString())
    }

    @VisibleForTesting
    @JvmStatic
    internal fun transToRawText(nodes: List<IItemNode<*>>): String {
        val out = StringBuilder()
        nodes.forEach {
            when (it) {
                is MediaNode -> RawTextParser.parseMediaNodeToRawText(out, it)
                is SpannedNode -> {
                    wrapCustomDiv(it, out) {
                        RawTextParser.withinSpanned(out, it, false)
                    }
                }
            }
        }
        return "<${HtmlTags.DIV}>$out</${HtmlTags.DIV}>"
    }

    @VisibleForTesting
    inline fun wrapCustomDiv(node: SpannedNode, out: StringBuilder, block: () -> Unit) {
        when (node.type) {
            TYPE_SUMMARY -> {
                out.append("<div class=\"summary\">")
                block.invoke()
                out.append("</div>")
            }
            else -> block.invoke()
        }
    }
}