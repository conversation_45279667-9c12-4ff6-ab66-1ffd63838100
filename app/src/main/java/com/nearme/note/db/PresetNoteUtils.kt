package com.nearme.note.db

import android.content.Context
import android.graphics.BitmapFactory
import android.text.SpannableStringBuilder
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.nearme.note.model.RichNoteRepository.insertList
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.PreferencesUtils
import com.oplus.note.R
import com.oplus.note.asr.SpeechServiceAgentFactory
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.richtext.core.parser.HtmlParser.serialize
import java.io.*
import java.util.*

object PresetNoteUtils {
    const val TAG = "PresetNoteUtils"
    private const val PRESET_NOTE_COUNT = 2
    private val PRESET_NOTE_ONE_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_TWO_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_THREE_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_ONE_ATTACHMENT_1_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_ONE_ATTACHMENT_2_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_TWO_ATTACHMENT_1_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_TWO_ATTACHMENT_2_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_THREE_ATTACHMENT_1_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_THREE_ATTACHMENT_2_GUID = UUID.randomUUID().toString()
    private val PRESET_NOTE_THREE_ATTACHMENT_3_GUID = UUID.randomUUID().toString()

    private const val PRESET_ATTACHMENT_FOLDER = "preset_note_images"
    private val PRESET_NOTE_ONE_ATTACHMENT_1_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_1_attachment_1.png"
    private val PRESET_NOTE_ONE_ATTACHMENT_2_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_1_attachment_2.png"
    private val PRESET_NOTE_TWO_ATTACHMENT_1_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_2_attachment_1.png"
    private val PRESET_NOTE_TWO_ATTACHMENT_2_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_2_attachment_2.png"
    private val PRESET_NOTE_THREE_ATTACHMENT_1_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_3_attachment_1.png"
    private val PRESET_NOTE_THREE_ATTACHMENT_2_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_3_attachment_2.jpg"
    private val PRESET_NOTE_THREE_ATTACHMENT_3_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_3_attachment_3.jpg"

    private val PRESET_NOTE_COLLECTION_ATTACHMENT_1_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_collection_attachment_1.png"
    private val PRESET_NOTE_COLLECTION_ATTACHMENT_2_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_collection_attachment_2.png"
    private val PRESET_NOTE_COLLECTION_ATTACHMENT_3_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_collection_attachment_3.png"
    private val PRESET_NOTE_COLLECTION_ATTACHMENT_4_ASSERT_NAME =
        PRESET_ATTACHMENT_FOLDER + File.separator + "preset_note_collection_attachment_4.png"


    fun insetPresetNote(context: Context) {
        if (PreferencesUtils.isPresetInsert(context)) {
            return
        }
        val runnable = Runnable {
            insertResetNoteInternal(context)
            PreferencesUtils.setPresetInsert(context, true)
        }
        Thread(runnable).start()
    }

    @VisibleForTesting
    @WorkerThread
    fun insertResetNoteInternal(context: Context) {
        val supportSpeechEngine = SpeechServiceAgentFactory.isSupportBreenoOrAzure(context)
        val presetNotes: MutableList<RichNoteWithAttachments> = ArrayList(PRESET_NOTE_COUNT)
        if (ConfigUtils.isSupportPresetNotes) {
            if (supportSpeechEngine) {
                val presetNote3 = buildPreset(context, PRESET_NOTE_THREE_GUID)
                presetNotes.add(presetNote3)
            }
            val presetNote2 = buildPreset(context, PRESET_NOTE_TWO_GUID)
            val presetNote1 = buildPreset(context, PRESET_NOTE_ONE_GUID)

            presetNotes.add(presetNote2)
            presetNotes.add(presetNote1)
        }
        insertList(presetNotes)
    }

    private fun buildPreset(context: Context, noteGuid: String): RichNoteWithAttachments {
        val textArray = genPresetNoteText(context, noteGuid)
        val text = if (textArray[0] == null) "" else textArray[0]
        val rawText = if (textArray[1] == null) "" else textArray[1]
        val t = System.currentTimeMillis()
        val richNote = RichNote().copy(
            localId = noteGuid,
            text = text!!,
            rawText = rawText!!,
            timestamp = t,
            createTime = t,
            updateTime = t,
            title = textArray[2],
            rawTitle = textArray[3],
            isPreset = true
        )

        val attachments: MutableList<Attachment> = ArrayList()
        buildAttachments(context, noteGuid, attachments)
        return RichNoteWithAttachments(richNote, attachments)
    }

    private fun buildAttachments(
        context: Context,
        localId: String,
        attachments: MutableList<Attachment>
    ) {
        when {
            PRESET_NOTE_ONE_GUID == localId -> {
                val attachmentId1 = PRESET_NOTE_ONE_ATTACHMENT_1_GUID
                val attachmentId2 = PRESET_NOTE_ONE_ATTACHMENT_2_GUID
                val whs1 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId1,
                    PRESET_NOTE_ONE_ATTACHMENT_1_ASSERT_NAME
                )
                val whs2 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId2,
                    PRESET_NOTE_ONE_ATTACHMENT_2_ASSERT_NAME
                )
                val attachment1 = Attachment(
                    attachmentId1, localId, 0, 1, null, null, Picture(
                        whs1[0], whs1[1]
                    ), null
                )
                val attachment2 = Attachment(
                    attachmentId2, localId, 0, 1, null, null, Picture(
                        whs2[0], whs2[1]
                    ), null
                )
                attachments.add(attachment1)
                attachments.add(attachment2)
            }

            PRESET_NOTE_TWO_GUID == localId -> {
                val attachmentId1 = PRESET_NOTE_TWO_ATTACHMENT_1_GUID
                val attachmentId2 = PRESET_NOTE_TWO_ATTACHMENT_2_GUID
                val whs1 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId1,
                    PRESET_NOTE_TWO_ATTACHMENT_1_ASSERT_NAME
                )
                val whs2 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId2,
                    PRESET_NOTE_TWO_ATTACHMENT_2_ASSERT_NAME
                )
                val attachment1 = Attachment(
                    attachmentId1, localId, 0, 1, null, null, Picture(
                        whs1[0], whs1[1]
                    ), null
                )
                val attachment2 = Attachment(
                    attachmentId2, localId, 0, 1, null, null, Picture(
                        whs2[0], whs2[1]
                    ), null
                )
                attachments.add(attachment1)
                attachments.add(attachment2)
            }

            PRESET_NOTE_THREE_GUID == localId -> {
                val attachmentId1 = PRESET_NOTE_THREE_ATTACHMENT_1_GUID
                val attachmentId2 = PRESET_NOTE_THREE_ATTACHMENT_2_GUID
                val attachmentId3 = PRESET_NOTE_THREE_ATTACHMENT_3_GUID
                val whs1 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId1,
                    PRESET_NOTE_THREE_ATTACHMENT_1_ASSERT_NAME
                )
                val whs2 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId2,
                    PRESET_NOTE_THREE_ATTACHMENT_2_ASSERT_NAME
                )
                val whs3 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId3,
                    PRESET_NOTE_THREE_ATTACHMENT_3_ASSERT_NAME
                )
                val attachment1 = Attachment(
                    attachmentId1, localId, 0, 1, null, null, Picture(
                        whs1[0], whs1[1]
                    ), null
                )
                val attachment2 = Attachment(
                    attachmentId2, localId, 0, 1, null, null, Picture(
                        whs2[0], whs2[1]
                    ), null
                )
                val attachment3 = Attachment(
                    attachmentId3, localId, 0, 1, null, null, Picture(
                        whs3[0], whs3[1]
                    ), null
                )
                attachments.add(attachment1)
                attachments.add(attachment2)
                attachments.add(attachment3)
            }

        }
    }

    private fun genPresetNoteText(context: Context, noteGuid: String): Array<String?> {
        //text-rawText-title-rawTitle
        val result = arrayOfNulls<String>(4)
        when {
            PRESET_NOTE_ONE_GUID == noteGuid -> {
                result[0] = context.getString(
                    R.string.preset_1_text,
                    context.getString(R.string.preset_1_text_part_1),
                    context.getString(R.string.preset_1_text_part_2),
                    context.getString(R.string.preset_1_text_part_3),
                    context.getString(R.string.preset_1_text_part_4),
                    context.getString(R.string.preset_1_text_part_5)
                )
                result[1] = context.getString(
                    R.string.preset_1_rawtext,
                    context.getString(R.string.preset_1_text_part_1),
                    context.getString(R.string.preset_1_text_part_2),
                    context.getString(R.string.preset_1_text_part_3),
                    PRESET_NOTE_ONE_ATTACHMENT_1_GUID,
                    context.getString(R.string.preset_1_text_part_4),
                    context.getString(R.string.preset_1_text_part_5),
                    PRESET_NOTE_ONE_ATTACHMENT_2_GUID
                )
                result[2] = context.getString(R.string.preset_1_title)
                result[3] = serialize(
                    SpannableStringBuilder(
                        result[2]
                    )
                )
            }

            PRESET_NOTE_TWO_GUID == noteGuid -> {
                result[0] = context.getString(
                    R.string.preset_2_text,
                    context.getString(R.string.preset_2_text_part_1),
                    context.getString(R.string.preset_2_text_part_2),
                    context.getString(R.string.preset_2_text_part_3),
                    context.getString(R.string.preset_2_text_part_4),
                    context.getString(R.string.preset_2_text_part_5)
                )
                result[1] = context.getString(
                    R.string.preset_2_rawtext,
                    context.getString(R.string.preset_2_text_part_1),
                    context.getString(R.string.preset_2_text_part_2),
                    context.getString(R.string.preset_2_text_part_3),
                    PRESET_NOTE_TWO_ATTACHMENT_1_GUID,
                    context.getString(R.string.preset_2_text_part_4),
                    context.getString(R.string.preset_2_text_part_5),
                    PRESET_NOTE_TWO_ATTACHMENT_2_GUID
                )
                result[2] = context.getString(R.string.preset_2_title)
                result[3] = serialize(
                    SpannableStringBuilder(
                        result[2]
                    )
                )
            }

            PRESET_NOTE_THREE_GUID == noteGuid -> {
                result[0] = context.getString(
                    R.string.preset_3_text,
                    context.getString(R.string.preset_3_text_part_1),
                    context.getString(R.string.preset_3_text_part_2),
                    context.getString(R.string.preset_3_text_part_3),
                    context.getString(R.string.preset_3_text_part_4),
                    context.getString(R.string.preset_3_text_part_5),
                    context.getString(R.string.preset_3_text_part_6_click_button),
                    context.getString(R.string.preset_3_text_part_7),
                    context.getString(R.string.preset_3_text_part_8)
                )
                result[1] = context.getString(
                    R.string.preset_3_rawtext,
                    context.getString(R.string.preset_3_text_part_1),
                    context.getString(R.string.preset_3_text_part_2),
                    context.getString(R.string.preset_3_text_part_3),
                    context.getString(R.string.preset_3_text_part_4),
                    PRESET_NOTE_THREE_ATTACHMENT_1_GUID,
                    context.getString(R.string.preset_3_text_part_5),
                    context.getString(R.string.preset_3_text_part_6_click_button),
                    PRESET_NOTE_THREE_ATTACHMENT_2_GUID,
                    context.getString(R.string.preset_3_text_part_7),
                    context.getString(R.string.preset_3_text_part_8),
                    PRESET_NOTE_THREE_ATTACHMENT_3_GUID
                )
                result[2] = context.getString(R.string.preset_3_title)
                result[3] = serialize(
                    SpannableStringBuilder(
                        result[2]
                    )
                )
            }
        }
        return result
    }

    private fun copyAttachmentsAndReturnWHs(
        context: Context,
        noteGuid: String,
        attachmentName: String,
        assertName: String
    ): IntArray {
        val whs = IntArray(2)
        val destPath = createAttachmentFiles(context, noteGuid, attachmentName)
        copyFromAssert(context, destPath, assertName)
        val bitmap = BitmapFactory.decodeFile(destPath)
        if (bitmap != null) {
            whs[0] = bitmap.width
            whs[1] = bitmap.height
            bitmap.recycle()
        }
        return whs
    }

    private fun createAttachmentFiles(
        context: Context,
        noteGuid: String,
        attachmentName: String
    ): String {
        val path = context.filesDir.absolutePath + File.separator + noteGuid
        val folder = File(path)
        if (!folder.exists()) {
            folder.mkdir()
        }
        val destPath = path + File.separator + attachmentName + "_thumb.png"
        val destFile = File(destPath)
        if (!destFile.exists()) {
            try {
                destFile.createNewFile()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return destPath
    }

    private fun copyFromAssert(context: Context, destPath: String, assertName: String) {
        var `in`: InputStream? = null
        var out: OutputStream? = null
        val assetManager = context.assets
        try {
            `in` = assetManager.open(assertName)
            File(destPath).createNewFile()
            out = FileOutputStream(destPath)
            copyFile(`in`, out)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            try {
                `in`?.close()
                if (out != null) {
                    out.flush()
                    out.close()
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    @Throws(IOException::class)
    private fun copyFile(`in`: InputStream, out: OutputStream) {
        val buffer = ByteArray(1024)
        var read: Int
        while (`in`.read(buffer).also { read = it } != -1) {
            out.write(buffer, 0, read)
        }
    }

    fun isPresetNote(richNote: RichNote): Boolean {
        return richNote.isPreset
    }
}