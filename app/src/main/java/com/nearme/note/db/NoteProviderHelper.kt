/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteProviderHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2022/1/10
 * * Author: PengFei.Ma
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.db

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import android.text.SpannableStringBuilder
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.sqlite.db.SupportSQLiteQueryBuilder
import com.google.gson.Gson
import com.google.gson.JsonParser
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.common.Constants
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.RichNoteSplitHelper.KEY_IS_FIRST_NOTE
import com.nearme.note.db.RichNoteSplitHelper.KEY_SPLIT
import com.nearme.note.model.*
import com.nearme.note.thirdlog.ThirdLogNoteBuildHelper
import com.nearme.note.util.*
import com.oplus.cloud.sync.richnote.RichNoteFactory.Companion.createRichNote
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.*
import com.oplus.note.utils.NoteStatusProviderUtil
import com.oplus.notes.aikit.util.AiKitFormatUtil
import com.oplus.richtext.core.entity.ImageFormat
import com.oplus.richtext.core.parser.HtmlParser.serialize
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.jsoup.Jsoup
import org.jsoup.nodes.Entities
import java.util.*
import java.util.stream.Collectors
import org.json.JSONObject

object NoteProviderHelper {
    const val TAG = "NoteProviderHelper"
    const val PACKAGE_NAME_DIRECT_SERVICE_UI = "com.coloros.colordirectservice"
    private const val PACKAGE_NAME_CALENDAR = "com.coloros.calendar"
    private const val PACKAGE_NAME_XIAOBU = "com.heytap.speechassist"
    private const val PACKAGE_NAME_RECORDER = "com.coloros.soundrecorder"
    private const val PACKAGE_NAME_ONEPLUS_RECORDER = "com.oneplus.soundrecorder"
    private const val PACKAGE_NAME_DIRECTUI = "com.coloros.directui"
    private const val PACKAGE_NAME_ASSISTANT = "com.coloros.accessibilityassistant"
    //创建时间
    private const val QUERY_CREATE_TIME_START = "query_create_time_start"
    private const val QUERY_CREATE_TIME_END = "query_create_time_end"
    //更新时间
    private const val QUERY_UPDATE_TIME_START = "query_update_time_start"
    private const val QUERY_UPDATE_TIME_END = "query_update_time_end"
    //正文
    private const val QUERY_CONTENT = "query_content"
    //id
    private const val QUERY_IDS = "query_ids"
    private const val QUERY_LOCAL_IDS = "local_id_list"
    private const val QUERY_MEDIA_ID = "media_id"
    private const val QUERY_AUDIO_PATH = "audio_path"
    private const val QUERY_KEYWORD = "keyword"

    private const val DIRECTUI_PACKAGE_NAME = "package_name"
    private const val DIRECTUI_URL = "url"
    private const val DIRECTUI_TITLE = "title"
    private const val CARD_BEGIN_PAGE = "cardBeginPage"

    private val AVAILABLE_COLUMNS = arrayOf(
        NotesProviderPresenter.KEY_LOCAL_ID,
        NotesProviderPresenter.KEY_TITLE,
        NotesProviderPresenter.KEY_TEXT,
        NotesProviderPresenter.KEY_ALARM_TIME,
        NotesProviderPresenter.KEY_UPDATE_TIME,
        NotesProviderPresenter.KEY_CREATE_TIME
    )

    private val AVAILABLE_COLUMNS_XIAOBU = arrayOf(
        NotesProviderPresenter.KEY_LOCAL_ID, //local_id
        NotesProviderPresenter.KEY_TITLE, //title
        NotesProviderPresenter.KEY_TEXT,
        NotesProviderPresenter.KEY_ALARM_TIME, //alarm_time
        NotesProviderPresenter.KEY_UPDATE_TIME,
        NotesProviderPresenter.KEY_CREATE_TIME, //create_time
        NotesProviderPresenter.KEY_ENCRYPTED, //1为加密，0为未加密
        NotesProviderPresenter.KEY_FOLDER_ID
    )

    private val AVAILABLE_COLUMNS_RECORDER = arrayOf(
        NotesProviderPresenter.KEY_LOCAL_ID, //local_id
        NotesProviderPresenter.KEY_TITLE, //title
        NotesProviderPresenter.KEY_TEXT,
        NotesProviderPresenter.KEY_SPEECH_LOG_ID, //speech_log_id
        NotesProviderPresenter.KEY_SPEECH_CREATE_TIME, //speech_create_time
        NotesProviderPresenter.KEY_EXTRA_INFO //extra_info
    )

    /**不限制包名查询笔记的接口白名单*/
    private val AVAILABLE_PACKAGE = arrayOf(
        PACKAGE_NAME_CALENDAR,
        PACKAGE_NAME_XIAOBU,
        PACKAGE_NAME_RECORDER,
        PACKAGE_NAME_ONEPLUS_RECORDER
    )
    private val mLimitedDispatcher: CoroutineDispatcher by lazy {
        val limitedCount = Math.max(Math.floor(Runtime.getRuntime().availableProcessors() * 0.4).toInt(), 1)
        AppLogger.BASIC.d(TAG, "limitedCount $limitedCount")
        Dispatchers.IO.limitedParallelism(limitedCount)
    }

    @JvmStatic
    fun isMatchedTextNoteUri(uri: Uri): Boolean {
        return NotesProvider.matchUrl(uri, NotesProvider.TEXT_NOTE)
    }

    @JvmStatic
    fun insertTextNote(context: Context? = null, uri: Uri, values: ContentValues, callingPackage: String? = null): Uri {
        //必要参数，外部必须提供
        val packageName = values.getAsString(NotesProviderPresenter.KEY_PACKAGE_NAME)

        if (isInValidPackageName(packageName, callingPackage)) {
            AppLogger.BASIC.d(TAG, "packageName is invalid")
            return uri.buildUpon().appendQueryParameter(NotesProviderPresenter.INSERT_RESULT, NotesProviderPresenter.INSERT_RESULT_ERROR)
                .appendQueryParameter(NotesProviderPresenter.INSERT_RESULT_KEY_MESSAGE,
                    NotesProviderPresenter.KEY_PACKAGE_NAME + NotesProviderPresenter.MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                .build()
        }

        val content = values.getAsString(NotesProviderPresenter.KEY_CONTENT)
        val htmlContent = values.safeGetString(NotesProviderPresenter.KEY_HTML_CONTENT)
        val tableContent = AiKitFormatUtil.formatTable(htmlContent)
        if (content.isNullOrEmpty() && htmlContent.isEmpty()) {
            AppLogger.BASIC.d(TAG, "content is invalid")
            return uri.buildUpon().appendQueryParameter(NotesProviderPresenter.INSERT_RESULT, NotesProviderPresenter.INSERT_RESULT_ERROR)
                .appendQueryParameter(NotesProviderPresenter.INSERT_RESULT_KEY_MESSAGE,
                    NotesProviderPresenter.KEY_CONTENT + NotesProviderPresenter.MESSAGE_NOT_NULL_OR_ZERO_LENGTH)
                .build()
        }

        val isHtmlContent = htmlContent.isNotEmpty()
        val hasSplit = values.getBoolean(KEY_SPLIT)
        val hasTable = tableContent.isNotEmpty()
        AppLogger.BASIC.d(TAG, "content is html: $isHtmlContent, hasSplit: $hasSplit hasTable:$hasTable")
        if (isHtmlContent && !hasSplit && !hasTable) {
            return RichNoteSplitHelper.insertHtmlNote(context, uri, values, callingPackage, htmlContent)
        }

        val guid = UUID.randomUUID().toString()
        val note = createRichNote(guid, packageName, values, context)
        val attachmentList: MutableList<Attachment> = mutableListOf()
        if (isHtmlContent) {
            if (hasTable) {
                RichNoteSplitHelper.handleMediaAttachment(htmlContent, note.localId, attachmentList, false)
                val title = values.getAsString(NotesProviderPresenter.KEY_TITLE)
                note.text = Jsoup.parse(tableContent).text()
                note.title = title
                note.rawTitle = title
                note.rawText = AiKitFormatUtil.appendTitle(tableContent, title)
            } else {
                var isAccess = false
                if (packageName == PACKAGE_NAME_ASSISTANT) {
                    isAccess = true
                }
                val html =
                    RichNoteSplitHelper.handleMediaAttachment(
                        htmlContent,
                        note.localId,
                        attachmentList,
                        isAccess
                    )
                note.text = Jsoup.parse(htmlContent).text()
                note.rawText = HtmlTransformManagerFactory.gainHtmlTransformManager().toRawText(html)
            }
        } else {
            val outText = StringBuilder(content)
            note.rawText = transformerRawText(guid, content, outText, attachmentList)
            note.text = outText.toString()
            RichNoteRepository.transToRawText(note)
        }
        AppLogger.BASIC.d(TAG, "insertTextNote packageName = $packageName")

        val hasAlarm = note.initAlarmTime(values)
        var richNoteWithAttachments = RichNoteWithAttachments(note, attachmentList)
        val isFirstNote = values.getBoolean(KEY_IS_FIRST_NOTE)
        if (hasSplit && isFirstNote || !hasSplit) {
            richNoteWithAttachments = addWebCardToRichNoteWithAttachments(richNoteWithAttachments, values)
        }
        return runCatching {
            RichNoteRepository.insert(richNoteWithAttachments)
            NoteCardWidgetProvider.instance.postUIToCard(false)
            sendNotify(context)
            if (hasAlarm) {
                AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
            }
            StatisticsUtils.setEventInsertTextNote(packageName)
            uri.buildUpon().appendQueryParameter(NotesProviderPresenter.INSERT_RESULT, NotesProviderPresenter.INSERT_RESULT_SUCCESS)
                .appendQueryParameter(NotesProviderPresenter.INSERT_RESULT_KEY_LOCAL_ID, guid)
                .build()
        }.getOrElse {
            AppLogger.BASIC.d(TAG, "insertNote error :" + it.message)
            uri.buildUpon().appendQueryParameter(NotesProviderPresenter.INSERT_RESULT, NotesProviderPresenter.INSERT_RESULT_ERROR)
                .appendQueryParameter(NotesProviderPresenter.INSERT_RESULT_KEY_MESSAGE, it.message)
                .build()
        }
    }

    @VisibleForTesting
    fun addWebCardToRichNoteWithAttachments(
        richNote: RichNoteWithAttachments,
        values: ContentValues
    ): RichNoteWithAttachments {
        var richNoteWithAttachments = richNote
        val webCard = values.getAsString(NotesProviderPresenter.KEY_WEB_NOTES)
        var data: ShareResult? = null
        kotlin.runCatching {
            val hasWebCard = !webCard.isNullOrEmpty()
            if (hasWebCard) {
                val gson = Gson()
                data = gson.fromJson(webCard, ShareResult::class.java)
            }
            AppLogger.BASIC.d(TAG, "has web card info: $hasWebCard")
        }.onFailure {
            AppLogger.BASIC.e(TAG, "Parse error")
            return richNoteWithAttachments
        }
        data?.let {
            //兼容cui入口需求，webcard必须传入title和iconUrl，现在改为不强制要求这两个有值，无值就默认为""字符串。
            val urlIsNotEmpty = it.url.isNullOrEmpty().not()
            if (urlIsNotEmpty) {
                val repository = RichNoteRepository
                val richData =
                    repository.convertTorichData(richNoteWithAttachments)
                var cardBeginPage = false
                kotlin.runCatching {
                    val jsonObject = JSONObject(webCard)
                    if (jsonObject.has(CARD_BEGIN_PAGE)) {
                        cardBeginPage = jsonObject.getBoolean(CARD_BEGIN_PAGE)
                    }
                }.onFailure {
                    AppLogger.BASIC.d(TAG, "convert JSONObject exception: ${it.message}")
                    return richNoteWithAttachments
                }
                val index = if (cardBeginPage) 0 else richData.items.size
                val dataItem = Data(
                    Data.TYPE_CARD,
                    card = PageResult(
                        url = (it.url ?: ""),
                        cover = (it.iconUrl ?: "")
                    ).apply {
                        title = (it.title ?: "")
                    }
                )
                richData.addItem(
                    index, dataItem
                )
                richData.addExtItem(richData.extItems.size, dataItem)
                richNoteWithAttachments = richData.toRichNoteWithAttachments()
            } else {
                AppLogger.BASIC.d(TAG, "url is not empty: $urlIsNotEmpty")
            }
        }
        return richNoteWithAttachments
    }

    @VisibleForTesting
    @JvmStatic
    internal fun createRichNote(guid: String, packageName: String, values: ContentValues, context: Context?): RichNote {
        val createTime = values.getAsLong(NotesProviderPresenter.KEY_CREATE_TIME)
        val currentTime = if (createTime.moreThanZero()) {
            createTime
        } else {
            System.currentTimeMillis()
        }
        return createRichNote(guid, currentTime).apply {
            state = RichNote.STATE_NEW
            this.packageName = packageName
            initTime(currentTime, values)
            initTitle(values)
            initFolderGuid(values, context)
            initSkin(values)
            initSpeechType(this, values)
        }
    }


    /**
     * 当创建笔记的folder是文档摘要笔记本时，要判断从values中读取extra
     * 并将extra的speech_type字段值写入speechLogInfo，一并创建笔记
     */
    @VisibleForTesting
    @JvmStatic
    internal fun initSpeechType(
        note: RichNote,
        values: ContentValues
    ) {
        if (note.folderGuid == FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY) {
            val extra = values.getAsString(NotesProviderPresenter.KEY_EXTRA)
            if (extra?.isEmpty() == true) {
                AppLogger.BASIC.e(TAG, "extra is empty")
                return
            }
            runCatching {
                val jsonObject = JsonParser.parseString(extra)?.asJsonObject
                val speechType = jsonObject?.get(ThirdLogNoteBuildHelper.KEY_TYPE)?.asString
                AppLogger.BASIC.d(TAG, "initSpeechType, speechType=$speechType")
                if (speechType.isNullOrEmpty().not()) {
                    if (speechType?.toIntOrNull() == ThirdLogNoteBuildHelper.SPEECH_TYPE_ARTICLE_SUMMARY) {
                        AppLogger.BASIC.d(TAG, "not create speechType when article summary")
                        return
                    }
                    note.extra = RichNoteExtra.create(null)
                    speechType?.let { note.extra?.setSpeechType(it) }
                    note.extra = RichNoteExtra.create(note.extra?.upToDate())
                } else {
                    AppLogger.BASIC.e(TAG, "speechType is empty")
                    return
                }
            }.onFailure {
                AppLogger.BASIC.e(TAG, "initSpeechType, error:${it.message}")
            }
        } else {
            AppLogger.BASIC.w(TAG, "initSpeechType create note not article summary")
        }
    }

    @Suppress("LongParameterList,Range")
    @JvmStatic
    fun query(
        context: Context,
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?,
        callingPackage: String? = null,
        packageNameLimit: Boolean = true
    ): Cursor? {
        AppDatabase.getInstance().openHelper.readableDatabase.let { db ->
            try {
                val packageName = uri.getQueryParameter("caller_package")
                if (packageNameLimit) {
                    if (isInValidPackageName(packageName, callingPackage)) {
                        AppLogger.BASIC.d(TAG, "[query] the $packageName is invalid, $callingPackage")
                        return null
                    }
                } else if (!AVAILABLE_PACKAGE.contains(callingPackage)){
                    AppLogger.BASIC.d(TAG, "[query] the $callingPackage is invalid")
                    return null
                }

                if (callingPackage == PACKAGE_NAME_XIAOBU) {
                    val specialArgs = mutableListOf<String>()
                    if (!PrivacyPolicyHelper.isAgreeUserNotice(context)) {
                        NoteStatusProviderUtil.setStatus(context, NoteStatusProviderUtil.FLAG_PRIVACY_AGREE_STATUS, false)
                        return null
                    }
                    val table = "rich_notes INNER JOIN speech_log_info ON rich_notes.local_id = speech_log_info.rich_note_id"
                    val builder = SupportSQLiteQueryBuilder.builder(table)

                    if (projection != null) {
                        builder.columns(projection.filter { it in AVAILABLE_COLUMNS_XIAOBU }
                            .toTypedArray())
                    } else {
                        builder.columns(AVAILABLE_COLUMNS_XIAOBU)
                    }

                    val where = StringBuilder(NotesProviderPresenter.KEY_RECYCLE_TIME + "=0")
                        .append(" AND deleted != 1").append(" AND speech_type = 0")

                    uri.getQueryParameter(QUERY_CONTENT)?.apply {
                        if (this.trim().isNotEmpty()) {
                            where.append(" AND ( text like ? escape \'/\' OR title like ? escape \'/\')")
                            val content = SqlUtils.sqliteEscape(this)
                            specialArgs.add("%$content%")
                            specialArgs.add("%$content%")
                        }
                    }

                    uri.getQueryParameter(QUERY_CREATE_TIME_START)?.apply {
                        where.append(
                            " AND speech_create_time between $this and ${
                                uri.getQueryParameter(
                                    QUERY_CREATE_TIME_END
                                )
                            }"
                        )
                    }
                    uri.getQueryParameter(QUERY_UPDATE_TIME_START)?.apply {
                        where.append(
                            " AND update_time between $this and ${
                                uri.getQueryParameter(
                                    QUERY_UPDATE_TIME_END
                                )
                            }"
                        )
                    }
                    uri.getQueryParameter(QUERY_IDS)?.apply {
                        val ids = this
                        where.append(" AND local_id in($ids)")
                    }
                    val querySelection =
                        if (!selection.isNullOrEmpty()) "$selection and $where" else where.toString()
                    builder.selection(querySelection, selectionArgs)

                    if (sortOrder != null) {
                        builder.orderBy(sortOrder)
                    }

                    val sql = builder.create()
                    val result = if (specialArgs.isNotEmpty()) {
                        db.query(sql.sql, specialArgs.toTypedArray())
                    } else {
                        db.query(sql.sql)
                    }
                    return getSuitableXiaoBuCursor(result)
                } else if ((callingPackage == PACKAGE_NAME_RECORDER) || (callingPackage == PACKAGE_NAME_ONEPLUS_RECORDER)) {
                    val table = "rich_notes INNER JOIN speech_log_info ON rich_notes.local_id = speech_log_info.rich_note_id"
                    val builder = SupportSQLiteQueryBuilder.builder(table)

                    if (projection != null) {
                        builder.columns(projection.filter { it in AVAILABLE_COLUMNS_RECORDER }
                            .toTypedArray())
                    } else {
                        builder.columns(AVAILABLE_COLUMNS_RECORDER)
                    }
                    val localIdsDecode = uri.getQueryParameter(QUERY_LOCAL_IDS)
                    val mediaIdDecode = uri.getQueryParameter(QUERY_MEDIA_ID)
                    val audioPathDecode = uri.getQueryParameter(QUERY_AUDIO_PATH)
                    val keyWordDecode = uri.getQueryParameter(QUERY_KEYWORD)

                    if ((localIdsDecode == null) && (mediaIdDecode == null) && (audioPathDecode == null) && (keyWordDecode == null)) {
                        return null
                    }
                    val queryLocalIds = Uri.decode(localIdsDecode)
                    val queryMediaId = Uri.decode(mediaIdDecode)
                    val queryAudioPath = Uri.decode(audioPathDecode)
                    val queryKeyWord = Uri.decode(keyWordDecode)

                    //1：录音-通话录音摘要    2：录音-QQ通话录音摘要  3：录音-微信通话录音摘要  6：录音-非通话录音摘要
                    val where = StringBuilder("${NotesProviderPresenter.KEY_SPEECH_TYPE} in(" +
                            "${ThirdLogNoteBuildHelper.SPEECH_TYPE_RECORD_CALL_SUMMARY}," +
                            "${ThirdLogNoteBuildHelper.SPEECH_TYPE_RECORD_QQ_SUMMARY}," +
                            "${ThirdLogNoteBuildHelper.SPEECH_TYPE_RECORD_WC_SUMMARY}," +
                            "${ThirdLogNoteBuildHelper.SPEECH_TYPE_RECORD_NORMAL_SUMMARY})")

                    if (queryLocalIds != null) {
                        kotlin.runCatching {
                            val localIds = Gson().fromJson<List<String>>(
                                queryLocalIds,
                                object : TypeToken<List<String?>?>() {}.type
                            )
                            if (localIds?.isNotEmpty() == true) {
                                localIds.stream().map { "\"${it}\"" }?.collect(Collectors.joining(","))?.let {
                                    where.append(" AND ${NotesProviderPresenter.KEY_LOCAL_ID} in ($it)")
                                }
                            }
                        }.onFailure {
                            AppLogger.BASIC.e(TAG, "json parse error: ${it.message}")
                        }
                    }
                    if (queryMediaId != null) {
                        where.append(" AND ${NotesProviderPresenter.KEY_EXTRA_INFO} like " +
                                    "'%${NotesProviderPresenter.KEY_EXTRA_AUDIO_MEDIA_ID}:$queryMediaId%'")
                    }
                    if (queryAudioPath != null) {
                        where.append(" AND ${NotesProviderPresenter.KEY_EXTRA_INFO} like " +
                                "'%${NotesProviderPresenter.KEY_EXTRA_AUDIO_PATH}:\"$queryAudioPath\"%'")
                    }
                    if (queryKeyWord != null) {
                        where.append(" AND ${NotesProviderPresenter.KEY_TEXT} like '%$queryKeyWord%'")
                    }

                    val querySelection =
                        if (!selection.isNullOrEmpty()) "$selection and $where" else where.toString()
                    builder.selection(querySelection, selectionArgs)

                    if (sortOrder != null) {
                        builder.orderBy(sortOrder)
                    }

                    val sql = builder.create()
                    return db.query(sql)
                } else if (callingPackage == PACKAGE_NAME_DIRECTUI || callingPackage == PACKAGE_NAME_DIRECT_SERVICE_UI) {
                    val table = "rich_notes"
                    val builder = SupportSQLiteQueryBuilder.builder(table)
                    if (projection != null) {
                        builder.columns(projection.filter { it in AVAILABLE_COLUMNS }
                            .toTypedArray())
                    } else {
                        builder.columns(AVAILABLE_COLUMNS)
                    }
                    var where = StringBuilder(NotesProviderPresenter.KEY_RECYCLE_TIME + "=0")
                        .append(" AND folder_id != '${FolderInfo.FOLDER_GUID_ENCRYPTED}'")
                        .append(" AND deleted != 1")
                    where = assembleSql(uri, where)
                    if (where.isNullOrEmpty()) {
                        return null
                    }
                    val querySelection = where.toString()
                    builder.selection(querySelection, null)
                    if (sortOrder != null) {
                        builder.orderBy(sortOrder)
                    }
                    val sql = builder.create()
                    return getSuitableCursor(context, db.query(sql))
                } else {
                    val table = "rich_notes"
                    val builder = SupportSQLiteQueryBuilder.builder(table)

                    if (projection != null) {
                        builder.columns(projection.filter { it in AVAILABLE_COLUMNS }.toTypedArray())
                    } else {
                        builder.columns(AVAILABLE_COLUMNS)
                    }

                    val where = StringBuilder(NotesProviderPresenter.KEY_RECYCLE_TIME + "=0")
                        .append(" AND folder_id != '${FolderInfo.FOLDER_GUID_ENCRYPTED}'")
                        .append(" AND deleted != 1")

                    if (packageNameLimit) {
                        where.append(" AND from_package ='$packageName'")
                    }

                    val querySelection =
                        if (!selection.isNullOrEmpty()) "$selection and $where" else where.toString()
                    builder.selection(querySelection, selectionArgs)

                    if (sortOrder != null) {
                        builder.orderBy(sortOrder)
                    }

                    val sql = builder.create()
                    return getSuitableCursor(context, db.query(sql))
                }
            } catch (e: Exception) {
                AppLogger.BASIC.e(TAG, "${e.message}")
                return null
            }
        }
    }

    private fun assembleSql(
        uri: Uri,
        strSql: StringBuilder
    ): StringBuilder? {
        var assignmentFlag = 0
        uri.getQueryParameter(DIRECTUI_PACKAGE_NAME)?.let {
            strSql.append(dealOriginStr(" AND web_notes LIKE '%\"$DIRECTUI_PACKAGE_NAME\":\"$it\"%'"))
            assignmentFlag++
        }
        uri.getQueryParameter(DIRECTUI_URL)?.let {
            strSql.append(dealOriginStr(" AND web_notes LIKE '%\"$DIRECTUI_URL\":\"$it\"%'"))
            assignmentFlag++
        }
        uri.getQueryParameter(DIRECTUI_TITLE)?.let {
            strSql.append(dealOriginStr(" AND web_notes LIKE '%\"$DIRECTUI_TITLE\":\"$it\"%'"))
            assignmentFlag++
        }
        if (assignmentFlag < 2) {
            //因为这里的识屏侧传过的参数必须两个、及两个以上才认为是有效的查询参数
            return null
        }
        return strSql
    }

    private fun dealOriginStr(originalString: String): String {
        val modifiedString = originalString.replace(Regex("\\s*:\\s*"), ":")
        return modifiedString
    }
    @JvmStatic
    fun delete(context: Context?, uri: Uri, callingPackage: String? = null): Int {
        val packageName = uri.getQueryParameter("caller_package")
        val localId = uri.getQueryParameter("local_id")
        val speechLogId = uri.getQueryParameter("speech_log_id")

        if (isInValidPackageName(packageName, callingPackage)) {
            AppLogger.BASIC.d(TAG, "[delete] the $packageName is invalid")
            return 0
        }
        if (localId.isNullOrEmpty() && speechLogId.isNullOrEmpty()) {
            AppLogger.BASIC.d(TAG, "[delete] invalid [local_id] or [speech_log_id] $packageName")
            return 0
        }
        if (speechLogId?.isNotEmpty() == true) {
            val result = RichNoteRepository.maskCallSummaryNoteDelete(speechLogId)
            AppLogger.BASIC.d(TAG, "delete speechLogNote=$speechLogId,count=$result")
            if (result > 0 && context != null) {
                sendNotify(context)
                if (context != null) {
                    sendNoteDataChangedBroadcastOnDelete(context)
                }
            }
            return result
        }
        if (localId?.isNotEmpty() == true) {
            val count = AppDatabase.getInstance().richNoteDao()
                .recycled(mutableSetOf(localId), System.currentTimeMillis())
            AppLogger.BASIC.d(TAG, "delete localId=$localId,count=$count")

            if (count > 0 && context != null) {
                sendNotify(context)
            }
            return count
        }
        return 0
    }

    private fun sendNoteDataChangedBroadcastOnDelete(context: Context) {
        val intent = Intent(Constants.ACTION_REFRESH_DATA_ON_AIGC_DELETE)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
        AppLogger.BASIC.d(TAG, "delete sendNoteDataChangedBroadcastOnDelete")
    }

    fun isInValidPackageName(packageName: String?, callingPackage: String?): Boolean {
        return TextUtils.isEmpty(packageName) || (callingPackage?.isNotEmpty() == true && !TextUtils.equals(callingPackage, packageName))
    }

    private fun sendNotify(context: Context?) {
        val uri = NotesProvider.NOTE_DATA_CHANGE_URI
        val uriNew = NotesProvider.NOTE_DATA_CHANGE_URI_NEW
        val notify = uri.getQueryParameter(NotesProvider.PARAMETER_NOTIFY)
        val notifyNew = uriNew.getQueryParameter(NotesProvider.PARAMETER_NOTIFY)
        if (null == notify || "true" == notify) {
            context?.contentResolver?.notifyChange(uri, null)
        }
        if (null == notifyNew || "true" == notifyNew) {
            context?.contentResolver?.notifyChange(uriNew, null)
        }
        NoteSearchManagerWrapper.notifyDataChange()
    }

    @SuppressLint("Range")
    private fun getSuitableXiaoBuCursor(cursor: Cursor?): Cursor? {
        kotlin.runCatching {
            if (cursor != null && cursor.moveToFirst()) {
                val matrixCursor = MatrixCursor(cursor.columnNames)
                var returnMatrixCursor = false

                val encryptedFolders = AppDatabase.getInstance().foldersDao().encryptedFolders
                try {
                    do {
                        val encrypted = cursor.getInt(cursor.getColumnIndex(NotesProviderPresenter.KEY_ENCRYPTED))
                        val folderId = cursor.getString(cursor.getColumnIndex(NotesProviderPresenter.KEY_FOLDER_ID))
                        AppLogger.BASIC.d(TAG, "encrypted: $encrypted")
                        //仅图片或者仅录音文件的时候，返回给三方app“图片笔记”或者“语音笔记”的假标题
                        if (encryptedFolders.contains(folderId) || folderId == FolderInfo.FOLDER_GUID_ENCRYPTED) {
                            val builder = matrixCursor.newRow()
                            for (column in 0 until cursor.columnCount) {
                                when (val columnName = cursor.getColumnName(column)) {
                                    NotesProviderPresenter.KEY_ENCRYPTED -> builder.add(columnName, 1)
                                    NotesProviderPresenter.KEY_LOCAL_ID -> builder.add(columnName, cursorValue(cursor, column))
                                    else -> builder.add(columnName, null)
                                }
                            }
                            returnMatrixCursor = true
                        } else {
                            val builder = matrixCursor.newRow()
                            for (column in 0 until cursor.columnCount) {
                                builder.add(cursor.getColumnName(column), cursorValue(cursor, column))
                            }
                        }
                    } while (cursor.moveToNext())

                    return if (returnMatrixCursor) {
                        matrixCursor
                    } else {
                        cursor
                    }
                } finally {
                    if (returnMatrixCursor) {
                        cursor.close()
                    } else {
                        matrixCursor.close()
                    }
                }
            }
        }

        return cursor
    }

    @SuppressLint("Range")
    private fun getSuitableCursor(context: Context?, cursor: Cursor?): Cursor? {
        kotlin.runCatching {
            if (cursor != null && cursor.moveToFirst()) {
                val matrixCursor = MatrixCursor(cursor.columnNames)
                var returnMatrixCursor = false

                try {
                    do {
                        val title = cursor.getString(cursor.getColumnIndex(NotesProviderPresenter.KEY_TITLE))
                        val content = cursor.getString(cursor.getColumnIndex(NotesProviderPresenter.KEY_TEXT))
                        //仅图片或者仅录音文件的时候，返回给三方app“图片笔记”或者“语音笔记”的假标题
                        if (title.isNullOrEmpty() && content.isNullOrEmpty()) {
                            val localId = cursor.getString(cursor.getColumnIndex(NotesProviderPresenter.KEY_LOCAL_ID))
                            val richNote = RichNoteRepository.getRichNoteWithAttachments(localId)
                            val isPictureNote = richNote?.isPictureNote()
                            val isCoverPictureNote = richNote?.isCoverPictureNote()
                            val isVoiceNote = richNote?.isVoiceNote()

                            if (isPictureNote == true || isVoiceNote == true || isCoverPictureNote == true) {
                                val builder = matrixCursor.newRow()
                                for (column in 0 until cursor.columnCount) {
                                    val columnName = cursor.getColumnName(column)
                                    val value = cursorValue(cursor, column)
                                    if (columnName == NotesProviderPresenter.KEY_TITLE) {
                                        var stringTitle = R.string.memo_picture
                                        if (isPictureNote == true){
                                            stringTitle = R.string.memo_picture
                                        }
                                        if (isVoiceNote == true){
                                            stringTitle = R.string.memo_voice
                                        }
                                        if (isCoverPictureNote == true){
                                            stringTitle = R.string.memo_cover
                                        }
                                        builder.add(columnName, stringTitle)
                                    } else {
                                        builder.add(columnName, value)
                                    }
                                }
                                returnMatrixCursor = true
                            } else {
                                val builder = matrixCursor.newRow()
                                for (column in 0 until cursor.columnCount) {
                                    builder.add(cursor.getColumnName(column), cursorValue(cursor, column))
                                }
                            }
                        } else {
                            val builder = matrixCursor.newRow()
                            for (column in 0 until cursor.columnCount) {
                                builder.add(cursor.getColumnName(column), cursorValue(cursor, column))
                            }
                        }
                    } while (cursor.moveToNext())

                    return if (returnMatrixCursor) {
                        matrixCursor
                    } else {
                        cursor
                    }
                } finally {
                    if (returnMatrixCursor) {
                        cursor.close()
                    } else {
                        matrixCursor.close()
                    }
                }
            }
        }

        return cursor
    }

    private fun cursorValue(cursor: Cursor, column: Int): Any? {
        return when (cursor.getType(column)) {
            Cursor.FIELD_TYPE_BLOB -> cursor.getBlob(column)
            Cursor.FIELD_TYPE_INTEGER -> cursor.getLong(column)
            Cursor.FIELD_TYPE_FLOAT -> cursor.getFloat(column)
            Cursor.FIELD_TYPE_STRING -> cursor.getString(column)
            Cursor.FIELD_TYPE_NULL -> null
            else -> {
                AppLogger.BASIC.d(TAG, "Invalid value in cursor: " + cursor.getType(column))
                null
            }
        }
    }

    data class PicInfo(val index: Int, val noteGuid: String, val imageUri: Uri, val picGuid: String)

    fun transformerRawText(
        noteGuid: String,
        content: String,
        outText: StringBuilder,
        attachmentList: MutableList<Attachment>,
        picGuidList: MutableList<String>? = null
    ): String {
        val outRawText = StringBuilder()
        if (content.contains(NoteInfo.DIVISION)) {
            val contentText = content.split(NoteInfo.DIVISION)
            val size = contentText.size

            val attachmentMap = HashMap<Int, Attachment>()
            runBlocking {

                for (i in 0 until size) {
                    if (i % 2 != 0) {
                        val imageUri = Uri.parse(contentText[i])
                        val picGuid = UUID.randomUUID().toString()
                        picGuidList?.add(picGuid)
                        launch(mLimitedDispatcher) {
                            recordAttachment(PicInfo(i, noteGuid, imageUri, picGuid), attachmentMap)
                        }
                    }
                }
            }

            for (i in 0 until size) {
                if (i % 2 == 0) { //text
                    outRawText.append("<div>")
                    outRawText.append(Entities.escape(contentText[i]))
                    outRawText.append("</div>")
                } else { //picture
                    val attachment = attachmentMap[i]
                    if (attachment != null) {
                        attachmentList.add(attachment)
                    }

                    val imageString = NoteInfo.DIVISION + contentText[i] + NoteInfo.DIVISION
                    AppLogger.BASIC.d(TAG, "transformerRawText imageString=$imageString")
                    val outIndex = outText.indexOf(imageString)

                    if (outIndex != -1) {
                        outText.delete(outIndex, outIndex + imageString.length)
                    }

                    if (attachmentList.size > 0 && attachment != null) {
                        outRawText.append(ImageFormat.createSource(attachment.attachmentId))
                    } else {
                        outRawText.append("\n")
                    }
                }
            }

            return outRawText.toString().replace("\r\n", "<br>").replace("\n", "<br>")
        } else {
            outRawText.append(serialize(SpannableStringBuilder(content)))
            return outRawText.toString()
        }
    }

    suspend fun recordAttachment(picInfo: PicInfo, attachmentMap: HashMap<Int, Attachment>) {
        AppLogger.BASIC.d(TAG, "insertAttachment imageUri=${picInfo.imageUri}")
        val picAttachment = Attachment(attachmentId = picInfo.picGuid, type = Attachment.TYPE_PICTURE, richNoteId = picInfo.noteGuid)
        FileUtil.copyFileFromUri(MyApplication.appContext, picInfo.imageUri, picAttachment.absolutePath(MyApplication.appContext))
        val bitmap = MediaUtils.getThumbBitmapFromUri(picInfo.imageUri)
        bitmap?.let {
            picAttachment.picture = Picture(it.width, it.height)
        }
        attachmentMap[picInfo.index] = picAttachment
    }
}