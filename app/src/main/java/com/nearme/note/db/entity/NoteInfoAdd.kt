/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoAdd.kt
 ** Description:
 **  Change java to kotlin
 *
 * Version: 1.0
 * Date: 2023/11/21
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023/11/21         1.0    Create this module
 **********************************************************************************/
package com.nearme.note.db.entity

import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.MyApplication.Companion.application
import com.nearme.note.activity.edit.NoteEntityUtils
import com.oplus.note.repo.todo.entity.DateConverters
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NoteInfoDBUtil
import com.nearme.note.db.NotesProvider
import com.nearme.note.db.entities.Note
import com.nearme.note.db.entities.NotesAttribute
import com.nearme.note.db.entities.Word
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import java.util.Date

class NoteInfoAdd private constructor() {
    companion object {
        private const val TAG = "NoteInfoAdd"

        @JvmStatic
        fun getInstance(): NoteInfoAdd {
            return NoteInfoAddHolder.instance
        }

        @JvmStatic
        fun copyUpdateNoteValues(note: Note, noteInfo: NoteInfo) {
            if (noteInfo.content.isNotEmpty()) {
                note.thumbFilename = noteInfo.content
            }
            note.updated = Date(noteInfo.updated)
            note.topped = DateConverters.timestampToDate(noteInfo.topped)
            note.para = noteInfo.backgroudRes
            note.thumbType = noteInfo.thumbType
            note.version = noteInfo.version
            note.state = noteInfo.state
            note.deleted = noteInfo.delete.toInt()
            note.attrCount = noteInfo.pictureAttributeSize
            note.description = noteInfo.description
            note.noteFolderGuid = noteInfo.folderGuid
            note.noteFolder = noteInfo.folderName
            note.alarmTime = Date(noteInfo.alarmTime)
            note.noteSkin = noteInfo.noteSkin
            noteInfo.timestamp = System.currentTimeMillis()
            note.timeStamp = Date(noteInfo.timestamp)
            note.extra = note.extra?.updateExtraInfo(noteInfo.extra)
            note.attachmentId = noteInfo.attachmentId
            note.globalId = noteInfo.globalId
            note.recycledTime = Date(noteInfo.recycled)
            note.sysVersion = noteInfo.sysVersion
        }

        @JvmStatic
        fun convertNoteInfoToNote(noteInfo: NoteInfo): Note {
            val note = Note()
            note.guid = noteInfo.guid
            note.updated = Date(noteInfo.updated)
            note.created = Date(noteInfo.created)
            note.version = noteInfo.version
            note.createdConsole = noteInfo.createConsole
            note.uid = noteInfo.owner
            note.para = noteInfo.backgroudRes
            note.thumbFilename = noteInfo.content
            note.thumbType = noteInfo.thumbType
            note.state = noteInfo.state
            note.attrCount = noteInfo.pictureAttributeSize
            note.description = noteInfo.description
            note.noteFolderGuid = noteInfo.folderGuid
            note.noteFolder = noteInfo.folderName
            note.alarmTime = Date(noteInfo.alarmTime)
            note.alarmTimePre = Date(noteInfo.alarmTimePre)
            note.timeStamp = Date(noteInfo.timestamp)
            note.noteSkin = noteInfo.noteSkin
            note.noteSkinPre = noteInfo.noteSkinPre
            note.recycledTime = Date(noteInfo.recycled)
            note.recycledTimePre = Date(noteInfo.recycledPre)
            note.extra = noteInfo.extra
            note.globalId = noteInfo.globalId
            note.topped = DateConverters.timestampToDate(noteInfo.topped)
            note.sysVersion = noteInfo.sysVersion
            return note
        }
    }

    private object NoteInfoAddHolder {
        val instance = NoteInfoAdd()
    }

    fun insertNote(noteInfo: NoteInfo) {
        insertOrUpdateNote(noteInfo, NoteInfoDBUtil.TYPE_INSERT, false, null)
    }

    fun insertNoteOfCloud(cloudNoteInfo: NoteInfo, userName: String?) {
        insertOrUpdateNote(cloudNoteInfo, NoteInfoDBUtil.TYPE_INSERT, true, userName)
    }

    fun insertNoteListOfCloud(cloudNoteInfos: List<NoteInfo>, userName: String?) {
        insertOrUpdateNotes(cloudNoteInfos, NoteInfoDBUtil.TYPE_INSERT, true, userName)
    }

    fun insertOrUpdateNote(noteInfo: NoteInfo) {
        val note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.guid)
        if (note != null) {
            NoteInfoUpdate.copyUpdateNoteValues(note, noteInfo)
            val count = AppDatabase.getInstance().noteDao().updateNote(note)
            if (count > 0) {
                saveNoteAttributes(noteInfo, false)
            }
        } else {
            AppLogger.CLOUD.d(TAG, "[DBUtil]updateNote fail and insert!!!!!!!")
            insertNote(noteInfo) // interrupted by sync or click the 'save' button
        }
    }

    /**
     ** save enclosure note info
     **/
    @VisibleForTesting
    internal fun saveNoteAttributes(noteInfo: NoteInfo?, isSync: Boolean) {
        AppDatabase.getInstance().commonDao().saveNoteAttributes(noteInfo, isSync)
        notifyDataChanged()
    }

    /**
     * @param noteInfo 需要操作的数据
     * @param type     One of {NoteInfoDBUtil.TYPE_INSERT}, {NoteInfoDBUtil.TYPE_UPDATE}}.
     * @param isSync   云端数据插入和更新是true，否则是false
     * @param username 云端数据插入和更新需要带username，其他情况传入null即可
     */
    @Suppress("LongMethod", "NestedBlockDepth")
    fun insertOrUpdateNote(noteInfo: NoteInfo, type: Int, isSync: Boolean, username: String?) {
        AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteInfo = $noteInfo")
        AppLogger.CLOUD.d(
            TAG,
            "insertOrUpdateNote: type = $type, isSync = $isSync, username = $username"
        )
        val size = noteInfo.attributesSize
        if (size <= 0 || noteInfo.content.isNullOrEmpty()) {
            AppLogger.CLOUD.d(TAG, "insertOrUpdateNote error, noteInfo is invalid")
            return
        }
        val note: Note?
        when (type) {
            NoteInfoDBUtil.TYPE_INSERT -> {
                note = if (isSync) {
                    convertCloudNoteInfoToNote(noteInfo, username)
                } else {
                    convertNoteInfoToNote(noteInfo)
                }
            }
            NoteInfoDBUtil.TYPE_UPDATE -> {
                if (isSync) {
                    note = AppDatabase.getInstance().noteDao()
                        .findbyGuidAndGlobalId(noteInfo.guid, noteInfo.globalId)
                    if (note == null) {
                        NoteInfoUpdate.getInstance().insertConflictNote(noteInfo, username)
                        return
                    }
                    noteInfo.timestamp = System.currentTimeMillis()
                    copyUpdateNoteValues(note, noteInfo)
                    if (!username.isNullOrEmpty()) {
                        note.account = username
                    }
                } else {
                    note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.guid)
                    copyUpdateNoteValues(note, noteInfo)
                }
            }
            else -> return
        }
        var content = ""
        var updateTime = if (isSync) 0 else noteInfo.updated
        var state = 0
        val redundancyText = ArrayList<String>()
        val attributes: Collection<NoteAttribute> = noteInfo.attributesIncWholeContent
        val addAttributesList: MutableList<NotesAttribute> = ArrayList()
        val updateAttributesList: MutableList<NotesAttribute> = ArrayList()
        val deleteContentList: MutableList<String> = ArrayList()
        val insertWord = Word()
        for (attr in attributes) {
            when (attr.operation) {
                NoteAttribute.OP_ADD.toInt() -> {
                    val attribute = NotesAttribute()
                    attribute.noteGuid = noteInfo.guid
                    attribute.type = attr.type
                    attribute.filename = attr.content
                    attribute.noteAttrOwner = attr.owner
                    attribute.state = attr.state
                    attribute.attrCreated = Date(attr.created)
                    attribute.width = attr.width
                    attribute.height = attr.height
                    attribute.para = attr.param
                    if (isSync) {
                        attribute.attachmentSyncUrl = attr.attachmentSyncUrl
                        attribute.attachmentMd5 = attr.attachmentMd5
                    }
                    if (attr.type == NoteAttribute.TYPE_TEXT_CONTENT) {
                        content = attr.content
                        state = attr.state
                        if (isSync) {
                            updateTime = attr.created
                        }
                    } else {
                        redundancyText.add(attr.content)
                    }
                    addAttributesList.add(attribute)
                }

                NoteAttribute.OP_MODIFY.toInt() -> {
                    if (attr.type == NoteAttribute.TYPE_TEXT_CONTENT) {
                        val attributeList = AppDatabase.getInstance().noteAttributeDao()
                            .findByGuidAndType(noteInfo.guid, NoteAttribute.TYPE_TEXT_CONTENT)
                        if (attributeList != null && attributeList.isNotEmpty()) {
                            for (attribute in attributeList) {
                                if (attribute == null) {
                                    continue
                                }
                                attribute.state = attr.state
                                attribute.type = attr.type
                                val value = attr.content
                                state = attr.state
                                attribute.filename = value
                                content = attr.content
                                if (isSync) {
                                    updateTime = attr.created
                                }
                                updateAttributesList.add(attribute)
                            }
                        }
                    } else {
                        val attribute = AppDatabase.getInstance().noteAttributeDao()
                            .findByFilename(attr.content)
                        if (isSync) {
                            attribute.attachmentSyncUrl = attr.attachmentSyncUrl
                            attribute.attachmentMd5 = attr.attachmentMd5
                        }
                        redundancyText.add(attr.content)
                        updateAttributesList.add(attribute)
                    }
                }

                NoteAttribute.OP_DELETE.toInt() -> {
                    deleteContentList.add(attr.content)
                    redundancyText.add(attr.content)
                }

                else -> redundancyText.add(attr.content)
            }
        }
        val title = noteInfo.extra.title
        if (!title.isNullOrEmpty()) {
            content = """
            $title
            $content
            """.trimIndent()
        }
        if (content.isNotEmpty()) {
            for (rt in redundancyText) {
                content = content.replace(NoteInfo.DIVISION + rt + NoteInfo.DIVISION, "\n")
            }
            if (!isSync && NoteEntityUtils.isNullOrEmpty(content)) {
                content = application.resources.getString(R.string.memo_picture)
            }
            insertWord.content = content
            insertWord.state = state
            insertWord.updated = Date(updateTime)
            insertWord.noteGuid = noteInfo.guid
        }
        val finalContent = content
        AppDatabase.getInstance().runInTransaction {
            if (isSync) {
                AppDatabase.getInstance().noteAttributeDao()
                    .deletebyNoteGuid(note?.guid)
            }
            when (type) {
                NoteInfoDBUtil.TYPE_INSERT -> {
                    AppDatabase.getInstance().noteDao()
                        .insert(note)
                }
                NoteInfoDBUtil.TYPE_UPDATE -> {
                    AppDatabase.getInstance().noteDao()
                        .updateNote(note)
                }
            }
            if (addAttributesList.isNotEmpty()) {
                AppDatabase.getInstance().noteAttributeDao()
                    .insert(addAttributesList)
            }
            if (updateAttributesList.isNotEmpty()) {
                AppDatabase.getInstance().noteAttributeDao()
                    .updateAttributes(updateAttributesList)
            }
            if (deleteContentList.isNotEmpty()) {
                AppDatabase.getInstance().noteAttributeDao()
                    .deleteByFilename(deleteContentList)
            }
            if (finalContent.isNotEmpty()) {
                AppDatabase.getInstance().wordsDao().deleteByNoteGuid(note?.guid)
                AppDatabase.getInstance().wordsDao().insert(insertWord)
            }
        }
        notifyDataChanged()
    }

    @Suppress("LongMethod", "NestedBlockDepth")
    fun insertOrUpdateNotes(
        noteInfoList: List<NoteInfo>?,
        type: Int,
        isSync: Boolean,
        username: String?
    ) {
        if (noteInfoList.isNullOrEmpty()) {
            AppLogger.BASIC.w(TAG, "insertOrUpdateNote error, noteInfoList is empty.")
            return
        }
        val addNoteList: MutableList<Note?> = ArrayList()
        val updateNoteList: MutableList<Note?> = ArrayList()
        val addAttributesList: MutableList<NotesAttribute> = ArrayList()
        val updateAttributesList: MutableList<NotesAttribute> = ArrayList()
        val deleteContentList: MutableList<String> = ArrayList()
        val insertWordList: MutableList<Word> = ArrayList()
        val guidList: MutableList<String> = ArrayList()
        for (noteInfo in noteInfoList) {
            AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteInfo = $noteInfo")
            AppLogger.CLOUD.d(
                TAG,
                "insertOrUpdateNote: type = $type, isSync = $isSync, username = $username"
            )
            val size = noteInfo.attributesSize
            if (size <= 0 || TextUtils.isEmpty(noteInfo.content) && TextUtils.isEmpty(noteInfo.title)) {
                AppLogger.CLOUD.d(
                    TAG,
                    "insertOrUpdateNote error, noteInfo is invalid : $noteInfo"
                )
                continue
            }
            val note: Note?
            when (type) {
                NoteInfoDBUtil.TYPE_INSERT -> {
                    note = if (isSync) {
                        convertCloudNoteInfoToNote(noteInfo, username)
                    } else {
                        convertNoteInfoToNote(noteInfo)
                    }
                    addNoteList.add(note)
                }

                NoteInfoDBUtil.TYPE_UPDATE -> {
                    if (isSync) {
                        note = AppDatabase.getInstance().noteDao()
                            .findbyGuidAndGlobalId(noteInfo.guid, noteInfo.globalId)
                        if (note == null) {
                            NoteInfoUpdate.getInstance().insertConflictNote(noteInfo, username)
                            return
                        }
                        noteInfo.timestamp = System.currentTimeMillis()
                        copyUpdateNoteValues(note, noteInfo)
                        if (!TextUtils.isEmpty(username)) {
                            note.account = username
                        }
                    } else {
                        note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.guid)
                        copyUpdateNoteValues(note, noteInfo)
                    }
                    updateNoteList.add(note)
                }

                else -> return
            }
            guidList.add(note?.guid ?: "")
            var content = ""
            var updateTime = if (isSync) 0 else noteInfo.updated
            var state = 0
            val redundancyText = ArrayList<String>()
            val attributes: Collection<NoteAttribute> = noteInfo.attributesIncWholeContent
            for (attr in attributes) {
                when (attr.operation) {
                    NoteAttribute.OP_ADD.toInt() -> {
                        val attribute = NotesAttribute()
                        attribute.noteGuid = noteInfo.guid
                        attribute.type = attr.type
                        attribute.filename = attr.content
                        attribute.noteAttrOwner = attr.owner
                        attribute.state = attr.state
                        attribute.attrCreated = Date(attr.created)
                        attribute.width = attr.width
                        attribute.height = attr.height
                        attribute.para = attr.param
                        if (isSync) {
                            attribute.attachmentSyncUrl = attr.attachmentSyncUrl
                            attribute.attachmentMd5 = attr.attachmentMd5
                        }
                        if (attr.type == NoteAttribute.TYPE_TEXT_CONTENT) {
                            content = attr.content
                            state = attr.state
                            if (isSync) {
                                updateTime = attr.created
                            }
                        } else {
                            redundancyText.add(attr.content)
                        }
                        addAttributesList.add(attribute)
                    }

                    NoteAttribute.OP_MODIFY.toInt() -> {
                        if (attr.type == NoteAttribute.TYPE_TEXT_CONTENT) {
                            val attributeList = AppDatabase.getInstance().noteAttributeDao()
                                .findByGuidAndType(noteInfo.guid, NoteAttribute.TYPE_TEXT_CONTENT)
                            if (attributeList != null && attributeList.isNotEmpty()) {
                                for (attribute in attributeList) {
                                    if (attribute == null) {
                                        continue
                                    }
                                    attribute.state = attr.state
                                    attribute.type = attr.type
                                    val value = attr.content
                                    state = attr.state
                                    attribute.filename = value
                                    content = attr.content
                                    if (isSync) {
                                        updateTime = attr.created
                                    }
                                    updateAttributesList.add(attribute)
                                }
                            }
                        } else {
                            val attribute = AppDatabase.getInstance().noteAttributeDao()
                                .findByFilename(attr.content)
                            if (isSync) {
                                attribute.attachmentSyncUrl = attr.attachmentSyncUrl
                                attribute.attachmentMd5 = attr.attachmentMd5
                            }
                            redundancyText.add(attr.content)
                            updateAttributesList.add(attribute)
                        }
                    }

                    NoteAttribute.OP_DELETE.toInt() -> {
                        deleteContentList.add(attr.content)
                        redundancyText.add(attr.content)
                    }

                    else -> redundancyText.add(attr.content)
                }
            }
            val title = noteInfo.extra.title
            if (!TextUtils.isEmpty(title)) {
                content = """
                $title
                $content
                """.trimIndent()
            }
            if (content.isNotEmpty()) {
                for (rt in redundancyText) {
                    content = content.replace(NoteInfo.DIVISION + rt + NoteInfo.DIVISION, "\n")
                }
                if (!isSync && NoteEntityUtils.isNullOrEmpty(content)) {
                    content = application.resources.getString(R.string.memo_picture)
                }
                val insertWord = Word()
                insertWord.content = content
                insertWord.state = state
                insertWord.updated = Date(updateTime)
                insertWord.noteGuid = noteInfo.guid
                insertWordList.add(insertWord)
            }
        }
        AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: AppDatabase")
        AppDatabase.getInstance().runInTransaction {
            if (isSync && guidList.isNotEmpty()) {
                AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: deleteByNoteGuids")
                AppDatabase.getInstance().noteAttributeDao()
                    .deleteByNoteGuids(guidList)
            }
            when (type) {
                NoteInfoDBUtil.TYPE_INSERT -> {
                    AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteDao insert")
                    AppDatabase.getInstance().noteDao()
                        .insert(addNoteList)
                }

                NoteInfoDBUtil.TYPE_UPDATE -> {
                    AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteDao updateNotes")
                    AppDatabase.getInstance().noteDao()
                        .updateNotes(updateNoteList)
                }
            }
            if (addAttributesList.isNotEmpty()) {
                AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteAttributeDao insert")
                AppDatabase.getInstance().noteAttributeDao()
                    .insert(addAttributesList)
            }
            if (updateAttributesList.isNotEmpty()) {
                AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteAttributeDao updateAttributes")
                AppDatabase.getInstance().noteAttributeDao()
                    .updateAttributes(updateAttributesList)
            }
            if (deleteContentList.isNotEmpty()) {
                AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: noteAttributeDao deleteByFilename")
                AppDatabase.getInstance().noteAttributeDao()
                    .deleteByFilename(deleteContentList)
            }
            AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: wordsDao deleteByNoteGuids")
            AppDatabase.getInstance().wordsDao().deleteByNoteGuids(guidList)
            AppLogger.CLOUD.d(TAG, "insertOrUpdateNote: wordsDao insert")
            AppDatabase.getInstance().wordsDao().insert(insertWordList)
        }
        notifyDataChanged()
    }

    private fun convertCloudNoteInfoToNote(cloudNoteInfo: NoteInfo, userName: String?): Note? {
        if (cloudNoteInfo.content.isEmpty() && cloudNoteInfo.title.isEmpty()) {
            AppLogger.CLOUD.d(TAG, "cloudNoteInfo.thumbAttrGuid is empty")
            return null
        }
        val note = Note()
        note.guid = cloudNoteInfo.guid
        note.updated = Date(cloudNoteInfo.updated)
        note.created = Date(cloudNoteInfo.created)
        note.version = cloudNoteInfo.version
        note.createdConsole = cloudNoteInfo.createConsole
        note.uid = cloudNoteInfo.owner
        note.para = cloudNoteInfo.backgroudRes
        note.recycledTime = Date(cloudNoteInfo.recycled)
        note.recycledTimePre = Date(cloudNoteInfo.recycled)
        note.alarmTime = Date(cloudNoteInfo.alarmTime)
        note.alarmTimePre = Date(cloudNoteInfo.alarmTime)
        val extraSkinId = cloudNoteInfo.extra.skinId
        var skin = cloudNoteInfo.noteSkin
        if (skin.isNullOrEmpty()) {
            skin = null
        }
        note.noteSkin = skin
        if (extraSkinId.isNullOrEmpty()) {
            note.noteSkinPre = skin
        } else {
            note.noteSkinPre = extraSkinId
        }
        cloudNoteInfo.timestamp = System.currentTimeMillis()
        note.timeStamp = Date(cloudNoteInfo.timestamp)
        cloudNoteInfo.setThumbInfoForAllNoteList()
        if (cloudNoteInfo.topped > -1) {
            // the value has been set if it is not equals '-1'
            note.topped = Date(cloudNoteInfo.topped)
        }
        note.thumbFilename = cloudNoteInfo.content
        note.thumbType = cloudNoteInfo.thumbType
        note.state = cloudNoteInfo.state
        note.globalId = cloudNoteInfo.globalId
        note.attachmentId = cloudNoteInfo.attachmentId
        if (!userName.isNullOrEmpty()) {
            note.account = userName
        }
        note.attrCount = cloudNoteInfo.pictureAttributeSize
        note.description = cloudNoteInfo.description
        note.noteFolderGuid = cloudNoteInfo.folderGuid
        note.noteFolder = cloudNoteInfo.folderName
        note.extra = cloudNoteInfo.extra
        note.sysVersion = cloudNoteInfo.sysVersion
        return note
    }

    @VisibleForTesting
    fun notifyDataChanged() {
        appContext.contentResolver.notifyChange(NotesProvider.DATA_CHANGE_URI, null)
        appContext.contentResolver.notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI, null)
        appContext.contentResolver.notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI_NEW, null)
    }
}