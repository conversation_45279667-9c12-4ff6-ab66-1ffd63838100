/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AppDatabaseRecover.java
 * * Description: AppDatabaseRecover.java
 * * Version: 1.0
 * * Date : 2020/3/9
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/3/9      1.0    build this module
 ****************************************************************/

package com.nearme.note.db;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import androidx.annotation.VisibleForTesting;
import androidx.room.OnConflictStrategy;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.oplus.note.logger.AppLogger;
import com.nearme.note.MyApplication;
import com.nearme.note.util.StatisticsUtils;

public class AppDatabaseRecover {

    public static final String TAG = "AppDatabaseRecover";

    public void startRecover(SupportSQLiteDatabase database) {
        boolean success = true;
        if (AppDatabase.isUpgradeTo23Failed()) {
            try {
                success &= startFixUpgradeTo23(database);
            } catch (Exception e) {
                StatisticsUtils.setEventDbRecoverFailed(MyApplication.getAppContext(), StatisticsUtils.TYPE_OLD_DB_RECOVER, e.toString());
                success = false;
            }
        }
        if (AppDatabase.isUpgradeToRoomDbFailed()) {
            try {
                success &= startFixUpgradeToRoom(database);
            } catch (Exception e) {
                StatisticsUtils.setEventDbRecoverFailed(MyApplication.getAppContext(), StatisticsUtils.TYPE_ROOM_UPGRADE_RECOVER, e.toString());
                success = false;
            }
        }
        if (AppDatabase.isMigrateTodoDataFailed()) {
            try {
                success &= startFixMigrateTodoData(database);
            } catch (Exception e) {
                success = false;
                StatisticsUtils.setEventDbRecoverFailed(MyApplication.getAppContext(), StatisticsUtils.TYPE_MIGRATE_TODO_RECOVER, e.toString());
            }
        }

        if (success) {
            AppDatabase.clearFailedFlags();
        } else {
            AppDatabase.setShouldShowFailedDialogFlag(true);
        }
    }

    private boolean startFixUpgradeTo23(SupportSQLiteDatabase roomDatabse) {
        StatisticsUtils.setEventDbRecoverSuccess(MyApplication.getAppContext(), StatisticsUtils.TYPE_OLD_DB_RECOVER);
        return true;
    }

    @SuppressLint("Range")
    private boolean startFixUpgradeToRoom(SupportSQLiteDatabase roomDatabse) {
        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoom");
        long start = System.currentTimeMillis();
        Cursor cursor = null;
        // copy folders_backup table
        if (AppDatabase.isTableExist(roomDatabse, DBConstants.NOTE_BACK.TABLE_FOLDERS_BACK)) {
            cursor = roomDatabse.query("select * from " + DBConstants.NOTE_BACK.TABLE_FOLDERS_BACK);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(NotesProvider.COL_FOLDER_ID, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_FOLDER_ID)));
                        contentValues.put(NotesProvider.COL_FOLDER_NAME, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FOLDER_NAME)));
                        contentValues.put(NotesProvider.COL_FOLDER_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FOLDER_GUID)));
                        contentValues.put(NotesProvider.COL_FOLDER_STATE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_FOLDER_STATE)));
                        contentValues.put(NotesProvider.COL_FOLDER_CREATED_TIME, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_FOLDER_CREATED_TIME)));
                        contentValues.put(NotesProvider.COL_FOLDER_MODIFY_DEVICE, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FOLDER_MODIFY_DEVICE)));
                        contentValues.put(NotesProvider.COL_FOLDER_DATA1, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FOLDER_DATA1)));
                        contentValues.put(NotesProvider.COL_FOLDER_DATA2, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FOLDER_DATA2)));
                        contentValues.put(NotesProvider.COL_FOLDER_ENCRYPTED, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_FOLDER_ENCRYPTED)));
                        roomDatabse.insert(DBConstants.NOTE.TABLE_FOLDERS, OnConflictStrategy.IGNORE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoomFailed : folders: " + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
            }
            if (cursor != null) {
                cursor.close();
            }
        }

        // copy notes_back table
        if (AppDatabase.isTableExist(roomDatabse, DBConstants.NOTE_BACK.TABLE_NOTES_BACK)) {
            cursor = roomDatabse.query("select * from " + DBConstants.NOTE_BACK.TABLE_NOTES_BACK);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(NotesProvider.COL_ID, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ID)));
                        contentValues.put(NotesProvider.COL_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_GUID)));
                        contentValues.put(NotesProvider.COL_VERSION, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_VERSION)));
                        contentValues.put(NotesProvider.COL_TOPPED, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_TOPPED)));
                        contentValues.put(NotesProvider.COL_UPDATED, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_UPDATED)));
                        contentValues.put(NotesProvider.COL_RECYCLED_TIME, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_RECYCLED_TIME)));
                        contentValues.put(NotesProvider.COL_STATE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_STATE)));
                        contentValues.put(NotesProvider.COL_DESCRIPTION, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_DESCRIPTION)));
                        contentValues.put(NotesProvider.COL_NOTE_FOLDER, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_NOTE_FOLDER)));
                        contentValues.put(NotesProvider.COL_NOTE_FOLDER_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_NOTE_FOLDER_GUID)));
                        contentValues.put(NotesProvider.COL_ATTR_COUNT, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_ATTR_COUNT)));
                        contentValues.put(NotesProvider.COL_SORT, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_SORT)));
                        contentValues.put(NotesProvider.COL_CREATED_CONSOLE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_CREATED_CONSOLE)));
                        contentValues.put(NotesProvider.COL_THUMB_TYPE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_THUMB_TYPE)));
                        contentValues.put(NotesProvider.COL_THUMB_ATTR_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_THUMB_ATTR_GUID)));
                        contentValues.put(NotesProvider.COL_NOTE_OWNER, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_NOTE_OWNER)));
                        contentValues.put(NotesProvider.COL_DELETED, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_DELETED)));
                        contentValues.put(NotesProvider.COL_PARA, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_PARA)));
                        contentValues.put(NotesProvider.COL_CREATED, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_CREATED)));
                        contentValues.put(NotesProvider.COL_GLOBAL_ID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_GLOBAL_ID)));
                        contentValues.put(NotesProvider.COL_ATTACHMENT_ID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_ATTACHMENT_ID)));
                        contentValues.put(NotesProvider.COL_ATTACHMENT_MD5, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_ATTACHMENT_MD5)));
                        contentValues.put(NotesProvider.COL_ACCOUNT, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_ACCOUNT)));
                        contentValues.put(NotesProvider.COL_ALARM_TIME, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ALARM_TIME)));
                        contentValues.put(NotesProvider.COL_NOTE_SKIN, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_NOTE_SKIN)));
                        contentValues.put(NotesProvider.COL_RECYCLED_TIME_PRE, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_RECYCLED_TIME_PRE)));
                        contentValues.put(NotesProvider.COL_ALARM_TIME_PRE, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ALARM_TIME_PRE)));
                        contentValues.put(NotesProvider.COL_NOTE_SKIN_PRE, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_NOTE_SKIN_PRE)));
                        contentValues.put(NotesProvider.COL_TIMESTAMP, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_TIMESTAMP)));
                        roomDatabse.insert(DBConstants.NOTE.TABLE_NOTES, OnConflictStrategy.IGNORE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoomFailed : notes:" + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
            }
            if (cursor != null) {
                cursor.close();
            }
        }

        // copy notes_attribute table
        if (AppDatabase.isTableExist(roomDatabse, DBConstants.NOTE_BACK.TABLE_NOTES_ATTRIBUTES_BACK)) {
            cursor = roomDatabse.query("select * from " + DBConstants.NOTE_BACK.TABLE_NOTES_ATTRIBUTES_BACK);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(NotesProvider.COL_ID, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ID)));
                        contentValues.put(NotesProvider.COL_NOTE_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_NOTE_GUID)));
                        contentValues.put(NotesProvider.COL_TYPE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_TYPE)));
                        contentValues.put(NotesProvider.COL_FILENAME, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_FILENAME)));
                        contentValues.put(NotesProvider.COL_NOTE_ATTR_OWNER, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_NOTE_ATTR_OWNER)));
                        contentValues.put(NotesProvider.COL_ATTR_CREATED, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ATTR_CREATED)));
                        contentValues.put(NotesProvider.COL_PARA, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_PARA)));
                        contentValues.put(NotesProvider.COL_STATE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_STATE)));
                        contentValues.put(NotesProvider.COL_ATTACHMENT_MD5, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_ATTACHMENT_MD5)));
                        contentValues.put(NotesProvider.COL_ATTACHMENT_SYNC_URL, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_ATTACHMENT_SYNC_URL)));
                        contentValues.put(NotesProvider.COL_SYNC_DATA1, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_SYNC_DATA1)));
                        contentValues.put(NotesProvider.COL_WIDTH, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_WIDTH)));
                        contentValues.put(NotesProvider.COL_HEIGHT, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_HEIGHT)));
                        roomDatabse.insert(DBConstants.NOTE.TABLE_NOTES_ATTRIBUTES, OnConflictStrategy.IGNORE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoomFailed : notes_attributes:" + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
            }
            if (cursor != null) {
                cursor.close();
            }
        }

        // copy words table
        if (AppDatabase.isTableExist(roomDatabse, DBConstants.NOTE_BACK.TABLE_WORDS_BACK)) {
            cursor = roomDatabse.query("select * from " + DBConstants.NOTE_BACK.TABLE_WORDS_BACK);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(NotesProvider.COL_ID, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ID)));
                        contentValues.put(NotesProvider.COL_WORDS_NOTE_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_WORDS_NOTE_GUID)));
                        contentValues.put(NotesProvider.COL_WORDS_CONTENT, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_WORDS_CONTENT)));
                        contentValues.put(NotesProvider.COL_WORDS_UPDATED, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_WORDS_UPDATED)));
                        contentValues.put(NotesProvider.COL_WORDS_STATE, cursor.getInt(cursor.getColumnIndex(NotesProvider.COL_WORDS_STATE)));
                        roomDatabse.insert(DBConstants.NOTE.TABLE_WORDS, OnConflictStrategy.IGNORE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoomFailed : words:" + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
            }
            if (cursor != null) {
                cursor.close();
            }
        }

        // copy alarm_note table
        if (AppDatabase.isTableExist(roomDatabse, DBConstants.NOTE_BACK.TABLE_ALARM_NOTE_BACK)) {
            cursor = roomDatabse.query("select * from " + DBConstants.NOTE_BACK.TABLE_ALARM_NOTE_BACK);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(NotesProvider.COL_GUID, cursor.getString(cursor.getColumnIndex(NotesProvider.COL_GUID)));
                        contentValues.put(NotesProvider.COL_ALARM_TIME, cursor.getLong(cursor.getColumnIndex(NotesProvider.COL_ALARM_TIME)));
                        roomDatabse.insert(DBConstants.NOTE.TABLE_ALARM_NOTE, OnConflictStrategy.IGNORE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoomFailed : alarm_note:" + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
            }
            if (cursor != null) {
                cursor.close();
            }
        }

        dropAllBackupTables(roomDatabse);
        AppLogger.BASIC.d(TAG, "[Room] startFixUpgradeToRoom finish cost:" + (System.currentTimeMillis() - start));
        StatisticsUtils.setEventDbRecoverSuccess(MyApplication.getAppContext(), StatisticsUtils.TYPE_ROOM_UPGRADE_RECOVER);
        return true;
    }

    @VisibleForTesting
    static void dropAllBackupTables(SupportSQLiteDatabase database) {
        try {
            database.beginTransaction();
            database.execSQL("DROP TABLE IF EXISTS " + DBConstants.NOTE_BACK.TABLE_FOLDERS_BACK);
            database.execSQL("DROP TABLE IF EXISTS " + DBConstants.NOTE_BACK.TABLE_NOTES_BACK);
            database.execSQL("DROP TABLE IF EXISTS " + DBConstants.NOTE_BACK.TABLE_NOTES_ATTRIBUTES_BACK);
            database.execSQL("DROP TABLE IF EXISTS " + DBConstants.NOTE_BACK.TABLE_WORDS_BACK);
            database.execSQL("DROP TABLE IF EXISTS " + DBConstants.NOTE_BACK.TABLE_ALARM_NOTE_BACK);
            database.setTransactionSuccessful();
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] drop table error:" + e);
        } finally {
            database.endTransaction();
        }
    }

    @SuppressLint("Range")
    private boolean startFixMigrateTodoData(SupportSQLiteDatabase roomDatabse) {
        AppLogger.BASIC.d(TAG, "[Room] startFixMigrateTodoData");
        long start = System.currentTimeMillis();
        SQLiteDatabase todoDb = TodoSqlOpenHelper.getInstance().getReadableDatabase();
        if (AppDatabase.isTableExist(todoDb, DBConstants.TODO.TABLE_TODO)) {
            Cursor cursor = todoDb.query(DBConstants.TODO.TABLE_TODO, null, null, null, null, null, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                do {
                    try {
                        roomDatabse.beginTransaction();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.LOCAL_ID, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.LOCAL_ID)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.GLOBAL_ID, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.GLOBAL_ID)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.CONTENT, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.CONTENT)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.ALARM_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.ALARM_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.CREATE_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.CREATE_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.UPDATE_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.UPDATE_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.FINISH_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.FINISH_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.STATUS, cursor.getInt(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.STATUS)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.IS_DELETE, cursor.getInt(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.IS_DELETE)));
                        if (cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.TIMESTAMP) > 0) {
                            contentValues.put(DBConstants.TODO.TODO_COLUMN.TIMESTAMP, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.TIMESTAMP)));
                        } else {
                            contentValues.put(DBConstants.TODO.TODO_COLUMN.TIMESTAMP, 0);
                        }
                        roomDatabse.insert(DBConstants.TODO.TABLE_TODO, OnConflictStrategy.REPLACE, contentValues);
                        roomDatabse.setTransactionSuccessful();
                    } catch (Exception e) {
                        AppLogger.BASIC.e(TAG, "[Room] todo recover data failed:" + e);
                    } finally {
                        roomDatabse.endTransaction();
                    }
                } while (cursor.moveToNext());
                todoDb.execSQL("DROP TABLE IF EXISTS " + DBConstants.TODO.TABLE_TODO);
            }
            if (cursor != null) {
                cursor.close();
            }
            todoDb.close();
        }
        AppLogger.BASIC.d(TAG, "[Room] startFixMigrateTodoData finish cost:" + (System.currentTimeMillis() - start));
        StatisticsUtils.setEventDbRecoverSuccess(MyApplication.getAppContext(), StatisticsUtils.TYPE_MIGRATE_TODO_RECOVER);
        return true;
    }
}
