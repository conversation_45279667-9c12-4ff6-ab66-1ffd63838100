/*
 * Copyright (C) 2007 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.nearme.note.db;

import android.content.ContentProvider;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentValues;
import android.content.OperationApplicationException;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.nearme.note.MyApplication;
import com.nearme.note.common.feedbacklog.FeedbackLog;
import com.nearme.note.upgrade.MigrateOldPackageManager;
import com.nearme.note.util.DataStatisticsHelper;
import com.oplus.note.BuildConfig;
import com.oplus.note.logger.AppLogger;

import java.util.ArrayList;

/**
 * Provides access to a database of notes. Each note has a title, the note
 * itself, a creation date and a modified data.
 */
public class NotesProvider extends ContentProvider {

    public static final String AUTHORITY = "com.nearme.note";
    public static final String AUTHORITY_NEW = BuildConfig.APPLICATION_ID + ".notesprovider";
    public static final String DATABASE_NAME = "nearme_note.db";

    public static final String DB_BACKUP_FOLDER = "/databases_backup";
    /*
     * Tables
     */
    static final String TABLE_NAME_NOTES = "notes";
    static final String TABLE_NAME_NOTES_ATTRIBUTES = "notes_attributes";
    static final String TABLE_NAME_ALARM_TIME = "alarm_note";
    static final String TABLE_NAME_FOLDERS = "folders";
    static final String TABLE_NAME_TODO = "todo";
    static final String PARAMETER_NOTIFY = "notify";
    static final String CLEAN_NOTES = "clean_notes";
    static final String NOTES_WORDS = "words";
    static final String TEXT_NOTE = "text_note";
    static final String NOTE_CHANGE = "change";
    static final String QUERY_NOTES = "query_notes_no_limit";

    // 6修改手写数据, 7闹铃数据,
    // 8去除手写和涂鸦将原数据转化为图片类型
    // 9 添加globalId, attachment_id, attachment_md5
    // 10 新增附件同步字段 attachment_sync_url, attachment_md5, sync_data1
    // 11 升级数据库用于清空旧版本同步状态
    // 12 纠正错误的OTA数据
    // 13 add account filed for clean globalId of other account data.
    // 14 rebuild notes table add (_id , description, attr_num),
    //recreate content_delete TRIGGER
    // 15 add attribute picture width and height.
    // 16 add notes' top
    // 17 remove 'thumb_filename' from words table and use union query -- not used!
    // 18 add folders table
    // 19 Add 'recycled_time' in 'notes' table which indicating the time of moving to 'recycle folder'
    // 20 add 'skin' in 'note'
    // 21 add 'skin_pre recycleTime_pre alarmTime_pre' in 'note'
    // 22 add 'encrypted' in 'folder'
    // 23 Add 'timestamp' column in 'notes' table
    static final int DATABASE_VERSION = 23;

    // columns of folders table
    public static final String COL_FOLDER_ID = "_id";       // INTEGER: PRIMARY KEY AUTOINCREMENT.
    public static final String COL_FOLDER_NAME = "name";    // TEXT: must filter some special characters.
    public static final String COL_FOLDER_GUID = "guid";    // TEXT:
    public static final String COL_FOLDER_STATE = "state";                  // INTEGER: see FolderInfo for detail.
    public static final String COL_FOLDER_CREATED_TIME = "created_time";    // INTEGER:
    public static final String COL_FOLDER_MODIFY_DEVICE = "modify_device";  // TEXT:
    public static final String COL_FOLDER_DATA1 = "data1";  // TEXT: reserved
    public static final String COL_FOLDER_DATA2 = "data2";  // TEXT: reserved
    public static final String COL_FOLDER_ENCRYPTED = "encrypted";  //INTEGER: 1 is encrypted note
    public static final String COL_FOLDER_MODIFY_TIME = "modify_time";
    public static final String COL_FOLDER_EXTRA = "extra";

    /*
     * cols
     */
    public static final String COL_ID = "_id";
    public static final String COL_GUID = "guid";
    @Deprecated
    public static final String COL_SORT = "sort";// 分类:0混合型、1纯手写、2纯涂鸦
    public static final String COL_CREATED_CONSOLE = "created_console";// 端口：手机/PC/WEB
    public static final String COL_UPDATED = "updated";
    public static final String COL_CREATED = "created"; // 创建时间
    public static final String COL_TOPPED = "topped"; // time of notes' top
    public static final String COL_STATE = "state";// 笔记状态
    public static final String COL_VERSION = "version";
    public static final String COL_THUMB_ATTR_GUID = "thumb_filename";
    public static final String COL_DESCRIPTION = "description";
    public static final String COL_ATTR_COUNT = "attr_count";
    public static final String COL_THUMB_TYPE = "thumb_type";
    public static final String COL_NOTE_OWNER = "uid";// 0为无主状态，1为有主状态
    public static final String COL_NOTE_FOLDER = "note_folder";
    public static final String COL_NOTE_FOLDER_GUID = "note_folder_guid";
    public static final String COL_RECYCLED_TIME = "recycled_time"; // The time of moving to 'recycle folder'
    public static final String COL_RECYCLED_TIME_PRE = "recycled_time_pre";
    public static final String COL_QUICK_MODE = "quick_mode";
    public static final String IS_SPECIFIED_FOLDER = "is_specified_folder";
    public static final String COL_DELETED = "deleted";
    public static final String COL_NOTE_GUID = "note_guid";
    public static final String COL_TYPE = "type";
    public static final String COL_FILENAME = "filename"; // attrGuid 或者文本内容
    public static final String COL_NOTE_ATTR_OWNER = "version";//
    public static final String COL_PARA = "para"; // 背景
    public static final String COL_ATTR_CREATED = "updated";// attr创建时间,仅attr表用

    public static final String COL_ALARM_TIME = "alarm_time"; // 闹铃时间,仅alarm_note表用
    public static final String COL_ALARM_TIME_PRE = "alarm_time_pre";

    public static final String COL_NOTE_SKIN = "note_skin"; // 便签皮肤
    public static final String COL_NOTE_SKIN_PRE = "note_skin_pre";
    public static final String COL_TIMESTAMP = "timestamp";

    public static final String QUERY_SKIN_COUNT = "query_skin_count";
    public static final String COUNT = "count";
    /*
     * 同步全局ID, TEXT类型，提供给云服务便签同步使用，用于保存服务端globalId.
     */
    public static final String COL_GLOBAL_ID = "globalId";

    /*
     * user name of account. while start sync, check whether it is different from current account.
     * If not, clean globalId, request full sync.
     */
    public static final String COL_ACCOUNT = "account";
    /*
     * 附件ID, TEXT类型，提供给云服务便签同步使用，用于保存附件上传后返回的信息，恢复时上传此信息下载附件。 一条笔记的对应一个附件，由保存在sd卡里的文件夹压缩成的附件。
     */
    public static final String COL_ATTACHMENT_ID = "attachment_id";
    /*
     * 附件MD5, TEXT类型，提供给云服务便签同步使用，用于保存附件同步时的通过本地压缩后的附件生成的MD5。 用于对比本地附件是否有修改，本地和服务端附件是否一致的判断。
     */
    public static final String COL_ATTACHMENT_MD5 = "attachment_md5";
    /*
     * 附件同步url，TEXT类型，用于保存在同步过程中，服务器下发的附件保存地址。
     */
    public static final String COL_ATTACHMENT_SYNC_URL = "attachment_sync_url";
    /*
     * picture width。
     */
    public static final String COL_WIDTH = "width";
    /*
     * picture height。
     */
    public static final String COL_HEIGHT = "height";

    /**
     * words table cols
     */
    public static final String COL_WORDS_NOTE_GUID = "note_guid";
    public static final String COL_WORDS_CONTENT = "content";
    public static final String COL_WORDS_UPDATED = "updated";
    public static final String COL_WORDS_STATE = "state";

    /*
     * for sync
     */
    public static final String SYNC_NOTES_ATTRIBUTES = "notes_attributes";
    public static final String REPEAT_TODO = "repeat/todo";
    /*
     * 附件同步备用字段
     */
    public static final String COL_SYNC_DATA1 = "sync_data1";
    public static final String COL_WORD_CONTENT = "content";
    public static final String WORD_UNION_SEARCH_KEY = "SearchUnionWord";
    public static final String LIST_UNION_QUERY_KEY = "UnionList";
    static final String NOTES_ATTR[] = {
            "_id", "note_guid", "type", "filename", "version", "updated", "para", "state"};

    /*
     * URI for tables
     */
    public static final Uri CONTENT_URI_NOTES_OLD = Uri.parse("content://" + AUTHORITY_NEW + "/" + TABLE_NAME_NOTES);
    public static final Uri CONTENT_URI_TEXT_NOTES = Uri.parse("content://" + AUTHORITY + "/" + TEXT_NOTE);
    public static final Uri CONTENT_URI_TEXT_NOTES_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + TEXT_NOTE);
    public static final Uri CONTENT_URI_NOTES_ATTRIBUTES = Uri.parse("content://" + AUTHORITY + "/" + TABLE_NAME_NOTES_ATTRIBUTES);
    public static final Uri CONTENT_URI_NOTES_ATTRIBUTES_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + TABLE_NAME_NOTES_ATTRIBUTES);
    public static final Uri CONTENT_URI_ALARM_TIME = Uri.parse("content://" + AUTHORITY + "/" + TABLE_NAME_ALARM_TIME);
    public static final Uri CONTENT_URI_NOTES_SEARCHWORD = Uri.parse("content://" + AUTHORITY + "/" + NOTES_WORDS);
    public static final Uri CONTENT_URI_NOTES_SEARCHWORD_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + NOTES_WORDS);
    public static final Uri CLEAN_NOTE_URI = Uri.parse("content://" + AUTHORITY + "/" + CLEAN_NOTES);
    public static final Uri DATA_CHANGE_URI = Uri.parse("content://" + AUTHORITY_NEW + "/" + NOTE_CHANGE);
    public static final Uri CONTENT_URI_FOLDERS = Uri.parse("content://" + AUTHORITY + "/" + TABLE_NAME_FOLDERS);
    public static final Uri CONTENT_URI_TODO = Uri.parse("content://" + AUTHORITY + "/" + TABLE_NAME_TODO);
    public static final Uri CONTENT_URI_TODO_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + TABLE_NAME_TODO);
    public static final Uri CONTENT_URI_REPEAT_TODO = Uri.parse("content://" + AUTHORITY + "/" + REPEAT_TODO);
    public static final Uri CONTENT_URI_REPEAT_TODO_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + REPEAT_TODO);
    public static final Uri DATA_CHANGE_WHEN_SYNC = Uri.parse("content://" + AUTHORITY_NEW + "/sync");
    public static final Uri NOTE_DATA_CHANGE_URI = Uri.parse("content://" + AUTHORITY + "/note_change");
    public static final Uri NOTE_DATA_CHANGE_URI_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/note_change");
    public static final Uri TODO_DATA_CHANGE_URI = Uri.parse("content://" + AUTHORITY + "/todo_change");
    public static final Uri TODO_DATA_CHANGE_URI_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/todo_change");
    public static final Uri QUERY_NOTES_NOT_LIMIT = Uri.parse("content://" + AUTHORITY + "/" + QUERY_NOTES);
    public static final Uri QUERY_NOTES_NOT_LIMIT_NEW = Uri.parse("content://" + AUTHORITY_NEW + "/" + QUERY_NOTES);

    private NotesProviderPresenter mNotesProviderPresenter;

    @Override
    public ContentProviderResult[] applyBatch(ArrayList<ContentProviderOperation> operations)
            throws OperationApplicationException {
        if (null == mNotesProviderPresenter) {
            return null;
        }
        return mNotesProviderPresenter.applyBatch(this, operations);
    }

    @Override
    public boolean onCreate() {
        if (!MigrateOldPackageManager.INSTANCE.shouldMigrate(getContext())) {
            MyApplication.getMyApplication().getMHasUpgradeDb().set(true);
        }

        mNotesProviderPresenter = new NotesProviderPresenter();
        mNotesProviderPresenter.onCreate(getContext());
        return true;
    }

    @Override
    public int bulkInsert(Uri uri, ContentValues[] values) {
        if (null == mNotesProviderPresenter
                || !mNotesProviderPresenter.checkPermission(getContext())) {
            return 0;
        }
        return mNotesProviderPresenter.bulkInsert(getContext(), uri, values);
    }

    @Override
    public String getType(Uri uri) {
        return (null == mNotesProviderPresenter) ? "" : mNotesProviderPresenter.getType(uri);
    }

    /**
     * insert
     * */
    @Override
    public Uri insert(Uri uri, ContentValues values) {
        String callingPkg = safeGetCallingPackage();
        DataStatisticsHelper.INSTANCE.providerOps("NotesProvider", "03010100", uri, values, null, null, callingPkg);
        AppLogger.BASIC.d("NotesProvider", "insert values:" + values.keySet());
        if (null == mNotesProviderPresenter
                || !mNotesProviderPresenter.checkPermission(getContext())) {
            return null;
        }
        return mNotesProviderPresenter.insert(getContext(), uri, values, callingPkg);
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        String callingPkg = safeGetCallingPackage();
        DataStatisticsHelper.INSTANCE.providerOps("NotesProvider", "03010200", uri, null, selection, selectionArgs, callingPkg);
        FeedbackLog.INSTANCE.getD().userLog("delete form provider");
        if (null == mNotesProviderPresenter || !mNotesProviderPresenter.checkPermission(getContext())) {
            return 0;
        }
        return mNotesProviderPresenter.delete(getContext(), uri, selection, selectionArgs, callingPkg);
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        String callingPkg = safeGetCallingPackage();
        DataStatisticsHelper.INSTANCE.providerOps("NotesProvider", "03010300", uri, values, selection, selectionArgs, callingPkg);
        FeedbackLog.INSTANCE.getD().userLog("update form provider");
        if (null == mNotesProviderPresenter || !mNotesProviderPresenter.checkPermission(getContext())) {
            return 0;
        }
        return mNotesProviderPresenter.update(getContext(), uri, values, selection, selectionArgs, callingPkg);
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
        if (null == mNotesProviderPresenter
                || !mNotesProviderPresenter.checkPermission(getContext())) {
            return null;
        }
        return mNotesProviderPresenter.query(getContext(), uri, projection, selection, selectionArgs, sortOrder, safeGetCallingPackage());
    }

    @Nullable
    @Override
    public Bundle call(@NonNull String method, @Nullable String arg, @Nullable Bundle extras) {
        if (null == mNotesProviderPresenter || TextUtils.isEmpty(method)) {
            return null;
        }
        return mNotesProviderPresenter.call(getContext(), method, arg, extras, safeGetCallingPackage());
    }

    private String safeGetCallingPackage() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                return getCallingPackageUnchecked();
            }
            return getCallingPackage();
        } catch (Exception ignore) {
            return null;
        }
    }

    public static boolean matchUrl(Uri uri, @NonNull String column) {
        AppLogger.BASIC.d("NotesProvider", "query invalid package uri:" + uri);
        if (column.equals(TEXT_NOTE)) {
            return uri.toString().startsWith(CONTENT_URI_TEXT_NOTES.toString())
                    || uri.toString().startsWith(CONTENT_URI_TEXT_NOTES_NEW.toString());
        }
        if (column.equals(TABLE_NAME_NOTES_ATTRIBUTES)) {
            return uri.toString().startsWith(CONTENT_URI_NOTES_ATTRIBUTES.toString())
                    || uri.toString().startsWith(CONTENT_URI_NOTES_ATTRIBUTES_NEW.toString());
        }
        if (column.equals(NOTES_WORDS)) {
            return uri.toString().startsWith(CONTENT_URI_NOTES_SEARCHWORD.toString())
                    || uri.toString().startsWith(CONTENT_URI_NOTES_SEARCHWORD_NEW.toString());
        }
        if (column.equals(TABLE_NAME_TODO)) {
            return uri.toString().startsWith(CONTENT_URI_TODO.toString())
                    || uri.toString().startsWith(CONTENT_URI_TODO_NEW.toString());
        }
        if (column.equals(REPEAT_TODO)) {
            return uri.toString().startsWith(CONTENT_URI_REPEAT_TODO.toString())
                    || uri.toString().startsWith(CONTENT_URI_REPEAT_TODO_NEW.toString());
        }
        if (column.equals(QUERY_NOTES)) {
            return uri.toString().startsWith(QUERY_NOTES_NOT_LIMIT.toString())
                    || uri.toString().startsWith(QUERY_NOTES_NOT_LIMIT_NEW.toString());
        }
        return false;
    }
}
