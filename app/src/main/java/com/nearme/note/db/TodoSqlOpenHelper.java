/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  TodoSqlOpenHelper.java
 * * Description: TodoSqlOpenHelper.java
 * * Version: 1.0
 * * Date : 2020/1/19
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/19      1.0    build this module
 ****************************************************************/
package com.nearme.note.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import androidx.annotation.Nullable;

import com.oplus.note.logger.AppLogger;
import com.nearme.note.MyApplication;

/**
 * This class is only used to move todo.db data to nearme_note.db
 */
public class TodoSqlOpenHelper extends SQLiteOpenHelper {

    private static final String TAG = "TodoSqlOpenHelper";

    private TodoSqlOpenHelper(@Nullable Context context, @Nullable String name, @Nullable SQLiteDatabase.CursorFactory factory, int version) {
        super(context, name, factory, version);
    }

    private static class InnerClass {
        private static final TodoSqlOpenHelper INSTANCE =
                new TodoSqlOpenHelper(MyApplication.getAppContext(), DBConstants.TODO.DB_NAME, null, DBConstants.TODO.VERSION_2);
    }

    public static TodoSqlOpenHelper getInstance() {
        return InnerClass.INSTANCE;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        AppLogger.BASIC.d(TAG, "onCreate");
        db.execSQL("CREATE TABLE IF NOT EXISTS `todo` (`local_id` TEXT NOT NULL, `global_id` TEXT, `content` TEXT, `alarm_time` INTEGER, `create_time` INTEGER, `update_time` INTEGER, `finish_time` INTEGER, `status` INTEGER, `is_delete` INTEGER, `timestamp` INTEGER, PRIMARY KEY(`local_id`))");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        AppLogger.BASIC.d(TAG, "onUpgrade");
        int upgradeVersion = oldVersion;
        if (upgradeVersion == DBConstants.TODO.VERSION_1) {
            db.execSQL("CREATE TABLE IF NOT EXISTS `todo` (`local_id` TEXT NOT NULL, `global_id` TEXT, `content` TEXT, `alarm_time` INTEGER, `create_time` INTEGER, `update_time` INTEGER, `finish_time` INTEGER, `status` INTEGER, `is_delete` INTEGER, PRIMARY KEY(`local_id`))");
        }
        if (upgradeVersion < DBConstants.TODO.VERSION_2) {
            db.execSQL("ALTER TABLE `todo` ADD COLUMN `timestamp` INTEGER");
            upgradeVersion = DBConstants.TODO.VERSION_2;
        }
    }
}
