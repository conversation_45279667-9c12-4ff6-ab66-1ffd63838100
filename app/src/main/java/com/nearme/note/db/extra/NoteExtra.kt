package com.nearme.note.db.extra

import android.os.Parcelable
import com.oplus.note.utils.ExtraJsonHelper
import kotlinx.android.parcel.Parcelize

@Parcelize
class NoteExtra() : Parcelable {

    companion object {
        fun create(extra: String?): NoteExtra {
            return if (extra.isNullOrBlank()) {
                NoteExtra()
            } else {
                ExtraJsonHelper.fromJson(extra, NoteExtra::class.java)?.apply {
                    this.extra = extra
                } ?: NoteExtra()
            }
        }
    }

    @Transient
    private var extra: String? = null
    var title: String? = null
    var skinId: String? = null

    override fun toString() = ExtraJsonHelper.updateExtraMapJson(extra, this)

    /**
     * 更新当前对象所有字段到原始extra字符串中，并返回结果
     */
    fun upToDate(): String {
        extra = ExtraJsonHelper.updateExtraMapJson(extra, this)
        return extra ?: ""
    }

    fun updateExtraInfo(updateExtra: NoteExtra): NoteExtra {
        return create(ExtraJsonHelper.updateExtraJson(this, updateExtra))
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NoteExtra

        if (extra != other.extra) return false
        if (title != other.title) return false
        if (skinId != other.skinId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = extra?.hashCode() ?: 0
        result = 31 * result + (title?.hashCode() ?: 0) + (skinId?.hashCode() ?: 0)
        return result
    }

}