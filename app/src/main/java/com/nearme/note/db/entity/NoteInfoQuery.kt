/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoQueryExt.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023/10/31
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>               2017/07/26     1.0        build this module
 * W9005794                        2023/10/31     2.0       Create this module
 **********************************************************************************/
package com.nearme.note.db.entity

import android.content.Context
import com.nearme.note.MyApplication.Companion.appContext
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.entities.Note
import com.nearme.note.db.entities.NotesAttribute
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import java.util.Date

class NoteInfoQuery {
    companion object {
        private const val TAG = "NoteInfoQuery"

        @JvmStatic
        fun getInstance(): NoteInfoQuery {
            return NoteInfoQueryHolder.instance
        }
    }

    private object NoteInfoQueryHolder {
        val instance = NoteInfoQuery()
    }

    fun queryNotesAttributes(attrGuid: String?): NotesAttribute {
        return AppDatabase.getInstance().noteAttributeDao().findByFilename(attrGuid)
    }

    fun queryNoteAttributes(
        ctx: Context?,
        info: NoteInfo,
        isSortByCreated: Boolean,
        isFilterDeleted: Boolean
    ) {
        info.guid?.let {
            queryNoteAttributes(ctx, it, info, isSortByCreated, isFilterDeleted)
        } ?: AppLogger.NOTE.w(TAG, "info guid is null!")
    }

    private fun queryNoteAttributes(
        ctx: Context?,
        srcGuid: String,
        dstInfo: NoteInfo?,
        isSortByCreated: Boolean,
        isFilterDeleted: Boolean
    ) {
        if (dstInfo == null) {
            AppLogger.NOTE.d(TAG, "[DBUtil]queryNoteAttributes, info is null!!! ")
            return
        }
        AppLogger.NOTE.d(TAG, "[DBUtil]queryNoteAttributes: $srcGuid")
        val attrs = if (isSortByCreated) {
            AppDatabase.getInstance().noteAttributeDao().findByNoteGuidOrderByUpdated(srcGuid)
        } else {
            AppDatabase.getInstance().noteAttributeDao().findByNoteGuid(srcGuid)
        }
        dstInfo.clearAttributes()
        for (attr in attrs) {
            val state = attr.state
            if (isFilterDeleted && state == NoteAttribute.STATE_DELETED) {
                AppLogger.NOTE.d(
                    TAG,
                    "[DBUtil]queryNoteAttributes, state==deleted. file name: " + attr.filename
                )
                continue
            }
            val type = attr.type
            val fileName = attr.filename
            val att = if (type == NoteAttribute.TYPE_TEXT_CONTENT) {
                val textAttribuite = NoteAttribute.newTextAttribute()
                textAttribuite.text = fileName
                dstInfo.wholeContentAttribute = textAttribuite
                textAttribuite
            } else {
                dstInfo.addAttribute(NoteAttribute.TYPE_ALBUM, fileName)
            }
            att.setOperation(NoteAttribute.OP_NONE)
            att.owner = attr.noteAttrOwner
            att.created = attr.attrCreated?.time ?: 0
            att.param = attr.para
            att.state = state
            att.attachmentSyncUrl = attr.attachmentSyncUrl
            att.attachmentMd5 = attr.attachmentMd5
            att.width = attr.width
            att.height = attr.height
        }
    }

    fun queryNoteAttributes(
        datas: ArrayList<NoteAttribute>,
        guid: String,
        isFilterDeleted: Boolean,
        isSortByCreated: Boolean
    ) {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryNoteAttributes: $guid")
        runCatching {
            val attrs = if (isSortByCreated) {
                AppDatabase.getInstance().noteAttributeDao().findByNoteGuidOrderByUpdated(guid)
            } else {
                AppDatabase.getInstance().noteAttributeDao().findByNoteGuid(guid)
            }
            datas.clear()
            for (attr in attrs) {
                val state = attr.state
                if (isFilterDeleted && state == NoteAttribute.STATE_DELETED) {
                    continue
                }
                val type = attr.type
                val fileName = attr.filename
                val att = NoteAttribute.newNoteAttribute(
                    type, fileName,
                    NoteAttribute.OP_NONE
                )
                val owner = attr.noteAttrOwner
                att.owner = owner
                val created = attr.attrCreated?.time ?: 0
                att.created = created
                val para = attr.para
                att.param = para
                val width = attr.width
                att.width = width
                val height = attr.height
                att.height = height
                att.state = state
                att.attachmentSyncUrl = attr.attachmentSyncUrl
                att.attachmentMd5 = attr.attachmentMd5
                datas.add(att)
            }
        }.onFailure {
            AppLogger.NOTE.e(TAG, "[DBUtil]queryNoteAttributes : error " + it.message)
        }
    }

    /**
     * query note list by time reverse order isFilterDeleted deleted: 0 normal 1 deleted
     */
    fun queryAllNoteInfo(
        ctx: Context?,
        allNoteInfo: ArrayList<NoteInfo>,
        isFilterDeleted: Boolean
    ) {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryAllNoteInfo: ")
        runCatching {
            if (isFilterDeleted) {
                AppDatabase.getInstance().noteDao()
                    .findNotesByDeletedOrderbyUpdated(NoteInfo.STATE_UNMARK_DELETED)
            } else {
                AppDatabase.getInstance().noteDao()
                    .findNotesByDeletedOrderbyUpdated(NoteInfo.STATE_MARK_DELETED)
            }?.let {
                parseCursorForAllNoteInfo(it, allNoteInfo)
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryAllNoteInfo failed:${it.message}")
        }
    }

    fun queryAlarmNoteInfo(
        ctx: Context?,
        allNoteInfo: ArrayList<NoteInfo>,
        isFilterDeleted: Boolean
    ) {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryAllNoteInfo: ")
        if (isFilterDeleted) {
            AppDatabase.getInstance().noteDao().findAlarmNoteInfos()
        } else {
            AppDatabase.getInstance().noteDao().findAlarmNoteInfosWithoutDeleted()
        }?.let {
            parseCursorForAllNoteInfo(it, allNoteInfo)
        }
    }

    fun getNextAlarm(): Long {
        AppLogger.NOTE.d(TAG, "[DBUtil]getNextAlarm: ")
        return runCatching {
            arrayListOf<NoteInfo>().also {
                val notes = AppDatabase.getInstance().noteDao()
                    .findNextAlarmNote(System.currentTimeMillis())
                parseCursorForAllNoteInfo(notes, it)
            }.firstOrNull()?.alarmTime ?: -1
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getNextAlarm failed:${it.message}")
        }.getOrDefault(-1)
    }

    /**
     * query note list by guid set
     */
    fun queryAllNoteInfoByGuidSet(
        ctx: Context?,
        allNoteInfo: ArrayList<NoteInfo>,
        guidSet: Set<String>
    ) {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryAllNoteInfoByGuidSet: ")
        runCatching {
            val notes = AppDatabase.getInstance().noteDao().findNotDeleteNoteByGuids(guidSet)
            parseCursorForAllNoteInfo(notes, allNoteInfo)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryAllNoteInfoByGuidSet failed:${it.message}")
        }
    }

    /**
     * query note info by mGuid
     */
    fun queryNoteInfoByGuid(guid: String): NoteInfo? {
        AppLogger.NOTE.d(TAG, "queryNoteInfoByGuid: $guid")
        var info: NoteInfo? = null
        runCatching {
            val note = AppDatabase.getInstance().noteDao().findByGuid(guid)
            if (note != null) {
                info = NoteInfo()
                updateNoteInfo(note, info)
            }
        }.onFailure {
            AppLogger.BASIC.d(TAG, "queryNoteInfoByGuid failed:${it.message}")
        }
        return info
    }

    /**
     * query note info by mGuid
     */
    fun queryAndUpdateNoteInfo(noteInfo: NoteInfo?): Boolean {
        return noteInfo?.let {
            runCatching {
                val note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.guid)
                note?.let {
                    updateNoteInfo(note, noteInfo)
                    true
                } ?: false
            }.onFailure {
                AppLogger.BASIC.d(TAG, "queryAndUpdateNoteInfo failed:${it.message}")
            }.getOrDefault(false)
        } ?: false
    }

    /**
     * update note info by cursor
     */
    private fun updateNoteInfo(note: Note?, info: NoteInfo?) {
        if (null == note || null == info) {
            return
        }
        info.guid = note.guid
        info.globalId = note.globalId
        info.version = note.version
        info.topped = note.topped.time()
        info.updated = note.updated.time()
        info.created = note.created.time()
        info.state = note.state
        info.createConsole = note.createdConsole
        info.thumbType = note.thumbType
        info.content = note.thumbFilename
        info.setBackgroundRes(note.para)
        info.folderGuid = note.noteFolderGuid.ifNullOrEmpty {
            FolderInfo.FOLDER_GUID_NO_GUID
        }
        info.folderName = note.noteFolder.ifNullOrEmpty {
            appContext.resources.getString(R.string.memo_all_notes)
        }
        info.recycled = note.recycledTime.time()
        info.alarmTime = note.alarmTime.time()
        info.noteSkin = note.noteSkin
        info.recycledPre = note.recycledTimePre.time()
        info.alarmTimePre = note.alarmTimePre.time()
        info.noteSkinPre = note.noteSkinPre
        info.timestamp = note.timeStamp.time()
        info.extra = note.extra
    }

    /**
     * query note info by guid
     */
    fun queryNoteInfoByGlobleId(globalId: String): NoteInfo? {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryNoteInfoByGlobleId: $globalId")
        return runCatching {
            AppDatabase.getInstance().noteDao().findNoteByGlobalGuid(globalId)?.toNoteInfo()
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryNoteInfoByGlobleId failed:${it.message}")
        }.getOrNull()
    }

    /**
     * return all dirty note info ，state!=BaseNoteInfo.STATE_UNCHANGE exclude note attr info
     */
    fun queryAllNoteInfoOfLocalDirtyNote(allNoteInfo: ArrayList<NoteInfo>?) {
        AppLogger.NOTE.d(TAG, "[DBUtil]queryAllNoteInfoOfLoacalDirtyNote")
        allNoteInfo?.let {
            val notes = getQueryLocalDirtyNoteCursor()
            parseCursorForAllNoteInfo(notes, allNoteInfo)
        }
    }

    /**
     * query the count of local dirty note's count
     *
     * @return
     */
    fun queryLocalDirtyNoteCount(): Int {
        return runCatching {
            getQueryLocalDirtyNoteCursor().size
        }.onFailure {
            AppLogger.NOTE.e(
                TAG,
                "[DBUtil]queryLocalDirtyNoteCount : error = " + it.message
            )
        }.getOrDefault(0)
    }

    private fun getQueryLocalDirtyNoteCursor(): List<Note> {
        return runCatching {
            AppDatabase.getInstance().noteDao().findAllDirtyNotesOrderbyUpdate()
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getQueryLocalDirtyNoteCursor failed:${it.message}")
        }.getOrDefault(listOf())
    }

    private fun parseCursorForAllNoteInfo(notes: List<Note>?, allNoteInfo: ArrayList<NoteInfo>) {
        notes?.let {
            parseCursorForAllNoteInfo(it, allNoteInfo, true)
        }
    }

    private fun Note.toNoteInfo(): NoteInfo {
        return NoteInfo().also {
            it.guid = this.guid
            it.version = this.version
            it.updated = this.updated.time()
            it.topped = this.topped.time()
            it.created = this.created.time()
            it.state = this.state
            it.createConsole = this.createdConsole
            it.thumbType = this.thumbType
            it.content = this.thumbFilename
            it.setBackgroundRes(this.para)
            it.noteSkin = this.noteSkin
            it.globalId = this.globalId
            it.delete = "${this.deleted}"
            it.folderGuid = this.noteFolderGuid
            it.folderName = this.noteFolder
            it.recycled = this.recycledTime.time()
            it.alarmTime = this.alarmTime.time()
            it.noteSkin = this.noteSkin
            it.recycledPre = this.recycledTimePre.time()
            it.alarmTimePre = this.alarmTime.time()
            it.noteSkinPre = this.noteSkinPre
            it.timestamp = this.timeStamp.time()
            it.extra = this.extra
            it.sysVersion = this.sysVersion
        }
    }

    private inline fun <C, R> C?.ifNullOrEmpty(defaultValue: () -> R): R where C : R, R : CharSequence =
        if (isNullOrEmpty()) defaultValue() else this

    private fun updateState(globalId: String, state: Int): Int {
        if (globalId.isNotEmpty() && state == NoteInfo.STATE_NEW) {
            return NoteInfo.STATE_MODIFIED
        }
        if (globalId.isEmpty() && state == NoteInfo.STATE_MODIFIED) {
            return NoteInfo.STATE_NEW
        }
        return state
    }

    private fun Date?.time(): Long {
        return this?.time ?: 0
    }

    private fun parseCursorForAllNoteInfo(
        notes: List<Note>,
        allNoteInfo: ArrayList<NoteInfo>,
        isFilterSync: Boolean
    ) {
        for (note in notes) {
            val info = note.toNoteInfo()
            info.state = updateState(info.globalId, info.state)
            info.folderGuid = info.folderGuid.ifNullOrEmpty {
                FolderInfo.FOLDER_GUID_NO_GUID
            }
            info.folderName = info.folderName.ifNullOrEmpty {
                appContext.resources.getString(R.string.memo_all_notes)
            }
            if (isFilterSync
                && info.globalId.isNullOrEmpty()
                && NoteInfo.STATE_MARK_DELETED == info.delete
            ) {
                AppLogger.NOTE.w(
                    TAG,
                    "[DBUtil]parseCursorForAllNoteInfo delete state not allow to sync"
                )
                continue
            }
            if (note.thumbFilename == null) {
                AppLogger.NOTE.d(
                    TAG,
                    "[DBUtil]parseCursorForAllNoteInfo null == info.thumbAttrGuid info.guid="
                            + info.guid
                )
                continue
            }
            allNoteInfo.add(info)
        }
    }

    fun queryAllNotesCount(): Int {
        return runCatching {
            AppDatabase.getInstance().noteDao().allCount
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryAllNotesCount failed:${it.message}")
        }.getOrDefault(0)
    }


    fun getAllNote(): List<Note> {
        return runCatching {
            AppDatabase.getInstance().noteDao().all
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getAllNote failed:${it.message}")
        }.getOrDefault(listOf())
    }

    fun queryAllRemindNotesCount(ctx: Context?): Int {
        return runCatching {
            AppDatabase.getInstance().noteDao().allRemindNoteCount
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryAllRemindNotesCount failed:${it.message}")
        }.getOrDefault(0)
    }

    fun queryAllKindsOfSkinCount(): HashMap<String, Int> {
        val countMap = HashMap<String, Int>()
        runCatching {
            val skinCounts = AppDatabase.getInstance().noteDao().allSkinCount
            for (entity in skinCounts) {
                countMap[entity.skin] = entity.count
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "queryAllKindsOfSkinCount failed:${it.message}")
        }
        return countMap
    }

    fun queryEncryptedNotesCount(): Int {
        return AppDatabase.getInstance().noteDao()
            .findEncrptedNoteCount(FolderInfo.FOLDER_GUID_ENCRYPTED)
    }
}