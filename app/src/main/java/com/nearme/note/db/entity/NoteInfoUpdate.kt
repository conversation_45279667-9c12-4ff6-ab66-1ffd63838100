/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - NoteInfoDelete.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2017/07/26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>       2017/07/26        build this module
 * W9005794                2023/12/18        change java to kotlin
 **********************************************************************************/
package com.nearme.note.db.entity

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NoteInfoDBUtil
import com.nearme.note.db.NotesProvider
import com.nearme.note.db.entities.Note
import com.nearme.note.db.extra.NoteExtra.Companion.create
import com.oplus.cloud.agent.note.NoteSyncAgent
import com.oplus.cloud.data.Packet
import com.oplus.cloud.protocol.ProtocolTag
import com.oplus.cloud.utils.MD5Utils
import com.oplus.note.logger.AppLogger
import java.io.IOException
import java.util.Date

class NoteInfoUpdate private constructor() {
    private object NoteInfoUpdateHolder {
        val instance = NoteInfoUpdate()
    }

    fun updateNote(noteInfo: NoteInfo) {
        NoteInfoAdd.getInstance()
            .insertOrUpdateNote(noteInfo, NoteInfoDBUtil.TYPE_UPDATE, false, null)
    }

    fun updateNote(noteInfo: NoteInfo?, userName: String?) {
        noteInfo?.let {
            NoteInfoAdd.getInstance()
                .insertOrUpdateNote(it, NoteInfoDBUtil.TYPE_UPDATE, true, userName)
        }
    }

    fun updateNotes(noteInfoList: List<NoteInfo>?, userName: String?) {
        NoteInfoAdd.getInstance()
            .insertOrUpdateNotes(noteInfoList, NoteInfoDBUtil.TYPE_UPDATE, true, userName)
    }

    fun updateNoteList(ctx: Context?, packets: List<Packet<*>>, userName: String?): Boolean {
        return updateNoteList(packets, userName, false)
    }

    @VisibleForTesting
    fun updateNoteList(
        packets: List<Packet<*>>,
        userName: String?,
        isModify: Boolean
    ): Boolean {
        val notes = ArrayList<Note>()
        for (packet in packets) {
            val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
            val guid = packet.getString(NoteSyncAgent.ITEM_ID)
            AppLogger.CLOUD.d(
                TAG,
                "updateNote globalId = $globalId userName = $userName isModify = $isModify"
            )
            val note = AppDatabase.getInstance().noteDao().findByGuid(guid) ?: continue
            note.state = if (isModify) NoteInfo.STATE_MODIFIED else NoteInfo.STATE_UNCHANGE
            globalId?.let {
                note.globalId = globalId
            }
            if (!userName.isNullOrEmpty()) {
                note.account = userName
            }
            note.recycledTimePre = note.recycledTime
            note.alarmTimePre = note.alarmTime
            var skinId = note.noteSkin
            if (!note.extra?.skinId.isNullOrEmpty()) {
                skinId = note.extra?.skinId
            }
            note.noteSkinPre = skinId
            notes.add(note)
        }
        return runCatching {
            AppDatabase.getInstance().noteDao().updateNotes(notes)
            notifyDataChanged()
            true
        }.onFailure {
            AppLogger.BASIC.e(TAG, "[Room] updateNoteList failed:${it.message}")
        }.getOrDefault(false)
    }

    @JvmOverloads
    fun updateNote(
        ctx: Context?,
        packet: Packet<*>,
        userName: String?,
        isModify: Boolean = false
    ): Boolean {
        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
        val guid = packet.getString(NoteSyncAgent.ITEM_ID)
        val recycleTime = packet.getString(NotesProvider.COL_RECYCLED_TIME)
        val recycleTimePre = packet.getString(NotesProvider.COL_RECYCLED_TIME_PRE)
        val alarmTime = packet.getString(NotesProvider.COL_ALARM_TIME)
        val alarmTimePre = packet.getString(NotesProvider.COL_ALARM_TIME_PRE)
        var skin = packet.getString(NotesProvider.COL_NOTE_SKIN)
        val skinPre = packet.getString(NotesProvider.COL_NOTE_SKIN_PRE)
        val noteExtra = packet.getString(ProtocolTag.CONTENT_NOTE_EXTRA)
        val extra = create(noteExtra)
        if (extra.skinId.isNullOrEmpty()) {
            skin = extra.skinId
        }
        AppLogger.CLOUD.d(
            TAG,
            "updateNote globalId = $globalId userName = $userName isModify = $isModify"
        )
        val note = AppDatabase.getInstance().noteDao().findByGuid(guid) ?: return false
        note.state = if (isModify) NoteInfo.STATE_MODIFIED else NoteInfo.STATE_UNCHANGE
        if (globalId != null) {
            note.globalId = globalId
        }
        if (!userName.isNullOrEmpty()) {
            note.account = userName
        }
        if (recycleTime != recycleTimePre) {
            note.recycledTimePre = Date(recycleTime.toLong())
        }
        if (alarmTime != alarmTimePre) {
            note.alarmTimePre = Date(alarmTime.toLong())
        }
        if (skin != skinPre) {
            note.noteSkinPre = skin
        }
        val count = AppDatabase.getInstance().noteDao().updateNote(note)
        notifyDataChanged()
        return count > 0
    }

    fun insertConflictNote(noteInfo: NoteInfo, userName: String?): Boolean {
        AppLogger.CLOUD.d(TAG, "insertConflictNote " + noteInfo.guid)
        val datas = ArrayList<NoteAttribute>()
        // step1 query local note attributes by download note guid
        NoteInfoQuery.getInstance().queryNoteAttributes(datas, noteInfo.guid, isFilterDeleted = false, false)
        val hasSameGuid = datas.size > 0
        // step2 calculate md5 for download note text data
        val newTextMd5 = calcMd5FromNoteAttributes(noteInfo)
        if (hasSameGuid) {
            // step3 calculate md5 for local note text data
            val textMd5 = calcMd5FromNoteAttributes(noteInfo)
            if (textMd5 != newTextMd5) {
                // step4 md5 code different mean data has modify, modify local note guid
                updateConflictNote(noteInfo, userName)
                return true
            } else {
                updateNoteInfo(noteInfo, userName)
            }
        } else {
            noteInfo.state = NoteInfo.STATE_UNCHANGE
            NoteInfoAdd.getInstance().insertNoteOfCloud(noteInfo, userName)
            notifyDataChanged()
            return true
        }
        return false
    }

    fun updateConflictNote(noteInfo: NoteInfo, userName: String?): Boolean {
        runCatching {
            modifyLocalNoteGuid(noteInfo)
        }.onFailure {
            AppLogger.CLOUD.e(TAG, "updateConflictNote error = " + it.message)
        }
        //insert download note info
        noteInfo.state = NoteInfo.STATE_UNCHANGE
        NoteInfoAdd.getInstance().insertNoteOfCloud(noteInfo, userName)
        notifyDataChanged()
        return true
    }

    fun reNewLocalNote(ctx: Context?, noteInfo: NoteInfo?): Boolean {
        return runCatching {
            noteInfo?.let {
                modifyLocalNoteGuid(noteInfo)
                true
            } ?: false
        }.onFailure {
            AppLogger.CLOUD.e(TAG, "reNewLocalNote error = " + it.message)
        }.getOrDefault(false)
    }

    @VisibleForTesting
    @Throws(IOException::class)
    fun modifyLocalNoteGuid(noteInfo: NoteInfo?) {
        AppDatabase.getInstance().commonDao().updateLocalNoteGuid(noteInfo)
    }

    @VisibleForTesting
    fun updateNoteInfo(noteInfo: NoteInfo, userName: String?) {
        noteInfo.timestamp = System.currentTimeMillis()
        val note = AppDatabase.getInstance().noteDao().findByGuid(noteInfo.guid)
        note?.let {
            NoteInfoAdd.copyUpdateNoteValues(note, noteInfo)
            if (!userName.isNullOrEmpty()) {
                note.account = userName
            }
            AppDatabase.getInstance().noteDao().updateNote(note)
            notifyDataChanged()
        }
    }

    @VisibleForTesting
    fun calcMd5FromNoteAttributes(noteInfo: NoteInfo): String {
        return MD5Utils.calcMd5(noteInfo.wholeContent)
    }

    @VisibleForTesting
    fun notifyDataChanged() {
        appContext.contentResolver.apply {
            notifyChange(NotesProvider.DATA_CHANGE_URI, null)
            notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI, null)
            notifyChange(NotesProvider.NOTE_DATA_CHANGE_URI_NEW, null)
        }
    }

    companion object {
        private const val TAG = "NoteInfoUpdate"

        @JvmStatic
        fun getInstance(): NoteInfoUpdate {
            return NoteInfoUpdateHolder.instance
        }

        @JvmStatic
        fun copyUpdateNoteValues(note: Note, noteInfo: NoteInfo) {
            note.updated = Date(noteInfo.updated)
            if (!noteInfo.content.isNullOrEmpty()) {
                note.thumbFilename = noteInfo.content
            }
            if (noteInfo.topped > -1) {
                note.topped = Date(noteInfo.topped)
            }
            note.para = noteInfo.backgroudRes
            note.thumbType = noteInfo.thumbType
            note.version = noteInfo.version
            note.state = noteInfo.state
            note.deleted = noteInfo.delete.toInt()
            note.attrCount = noteInfo.pictureAttributeSize
            note.description = noteInfo.description
            note.noteFolderGuid = noteInfo.folderGuid
            note.noteFolder = noteInfo.folderName
            note.alarmTime = Date(noteInfo.alarmTime)
            note.noteSkin = noteInfo.noteSkin
            noteInfo.timestamp = System.currentTimeMillis()
            note.timeStamp = Date(noteInfo.timestamp)
            note.extra = note.extra?.updateExtraInfo(noteInfo.extra)
        }
    }
}