package com.nearme.note.db.entities;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.oplus.note.repo.todo.entity.DateConverters;
import com.nearme.note.converter.NoteExtraConverters;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.nearme.note.db.extra.NoteExtra;

import java.util.Date;

@Entity(tableName = "notes")
public class Note {

    @NonNull
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    public int id;

    @ColumnInfo(name = "guid")
    public String guid;

    @Nullable
    @ColumnInfo(name = "version", defaultValue = "0")
    public int version;

    /**
     * time of notes' top
     */
    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "topped", defaultValue = "0")
    public Date topped;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "updated", defaultValue = "0")
    public Date updated;

    /**
     * The time of moving to 'recycle folder'
     */
    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "recycled_time", defaultValue = "0")
    public Date recycledTime;

    /**
     * 笔记状态
     */
    @Nullable
    @ColumnInfo(name = "state", defaultValue = "0")
    public int state;

    @Nullable
    @ColumnInfo(name = "description")
    public String description;

    @Nullable
    @ColumnInfo(name = "note_folder")
    public String noteFolder;

    @NonNull
    @ColumnInfo(name = "note_folder_guid", defaultValue = FolderInfo.FOLDER_GUID_NO_GUID)
    public String noteFolderGuid;

    @Nullable
    @ColumnInfo(name = "attr_count", defaultValue = "0")
    public int attrCount;

    /**
     * 分类:0混合型、1纯手写、2纯涂鸦
     */
    @Nullable
    @ColumnInfo(name = "sort", defaultValue = "0")
    public int sort;

    /**
     * 端口：手机/PC/WEB
     */
    @Nullable
    @ColumnInfo(name = "created_console", defaultValue = "0")
    public int createdConsole;

    @Nullable
    @ColumnInfo(name = "thumb_type", defaultValue = "0")
    public int thumbType;

    @Nullable
    @ColumnInfo(name = "thumb_filename")
    public String thumbFilename;

    /**
     * 0为无主状态，1为有主状态
     */
    @Nullable
    @ColumnInfo(name = "uid", defaultValue = "0")
    public int uid;

    @Nullable
    @ColumnInfo(name = "deleted", defaultValue = "0")
    public int deleted;

    /**
     * 背景
     */
    @Nullable
    @ColumnInfo(name = "para", defaultValue = "0")
    public int para;

    /**
     * 创建时间
     */
    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "created", defaultValue = "0")
    public Date created;

    /*
     * 同步全局ID, TEXT类型，提供给云服务便签同步使用，用于保存服务端globalId.
     */
    @Nullable
    @ColumnInfo(name = "globalId")
    public String globalId;

    /*
     * 附件ID, TEXT类型，提供给云服务便签同步使用，用于保存附件上传后返回的信息，恢复时上传此信息下载附件。 一条笔记的对应一个附件，由保存在sd卡里的文件夹压缩成的附件。
     */
    @Nullable
    @ColumnInfo(name = "attachment_id")
    public String attachmentId;

    /*
     * 附件MD5, TEXT类型，提供给云服务便签同步使用，用于保存附件同步时的通过本地压缩后的附件生成的MD5。 用于对比本地附件是否有修改，本地和服务端附件是否一致的判断。
     */
    @Nullable
    @ColumnInfo(name = "attachment_md5")
    public String attachmentMd5;

    /*
     * user name of account. while start sync, check whether it is different from current account.
     * If not, clean globalId, request full sync.
     */
    @Nullable
    @ColumnInfo(name = "account")
    public String account;

    /**
     * 闹铃时间,仅alarm_note表用
     */
    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "alarm_time", defaultValue = "0")
    public Date alarmTime;

    /**
     * 便签皮肤
     */
    @Nullable
    @ColumnInfo(name = "note_skin")
    public String noteSkin;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "recycled_time_pre", defaultValue = "0")
    public Date recycledTimePre;

    @Nullable
    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "alarm_time_pre", defaultValue = "0")
    public Date alarmTimePre;

    @Nullable
    @ColumnInfo(name = "note_skin_pre")
    public String noteSkinPre;

    @Nullable
    @TypeConverters(DateConverters.class)
    @ColumnInfo(name = "timestamp")
    public Date timeStamp;

    @Nullable
    @TypeConverters(NoteExtraConverters.class)
    @ColumnInfo(name = "extra")
    public NoteExtra extra;

    //sysVersion用于记录cloudkit同步数据的版本号，由云服务生成，本地只做记录
    @Nullable
    @ColumnInfo(name = "sysVersion", defaultValue = "0")
    public long sysVersion;

    public Note() {
    }
}
