package com.nearme.note.db.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.oplus.note.repo.todo.entity.DateConverters;

import java.util.Date;

@Entity(tableName = "words")
public class Word {

    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    public int id;

    @ColumnInfo(name = "note_guid")
    public String noteGuid;

    @ColumnInfo(name = "content")
    public String content;

    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "updated")
    public Date updated;

    @ColumnInfo(name = "state")
    public int state;
}
