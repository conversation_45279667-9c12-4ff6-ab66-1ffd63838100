package com.nearme.note.db.entities;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.oplus.note.repo.todo.entity.DateConverters;

import java.util.Date;

@Entity(tableName = "alarm_note")
public class AlarmNote {

    @NonNull
    @ColumnInfo(name = "guid")
    @PrimaryKey
    public String guid;

    @TypeConverters({DateConverters.class})
    @ColumnInfo(name = "alarm_time", defaultValue = "0")
    public Date alarmTime;

    public AlarmNote() {
    }
    
}
