/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  FolderDao.java
 * * Description: FolderDao.java
 * * Version: 1.0
 * * Date : 2020/1/9
 * * Author: <EMAIL>
 * *
 * * OPLUS Coding Static Checking Skip
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/9      1.0    build this module
 ****************************************************************/

package com.nearme.note.db.daos;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.RoomWarnings;
import androidx.room.Transaction;
import androidx.room.Update;
import androidx.sqlite.db.SimpleSQLiteQuery;

import com.nearme.note.data.NoteInfo;
import com.nearme.note.util.SqlUtils;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.repo.note.entity.Folder;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.oplus.note.repo.note.entity.FolderItem;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import kotlin.Pair;

@Dao
public abstract class FolderDao extends BaseDao {

    private static final String TAG = "FolderDao";

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insert(Folder folder);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    public abstract void insertAll(@NotNull List<Folder> folderList);

    @Query("DELETE FROM folders")
    public abstract int deleteAll();

    @Query("SELECT * FROM folders WHERE name = :folderName AND state != " + FolderInfo.FOLDER_STATE_DELETED+" ORDER BY created_time DESC ")
    public abstract List<Folder> findNotDeletedFolder(String folderName);

    public List<Folder> findNotDeletedFolderByName(String folderName) {
        List<Folder> notDeletedFolderByName;
        try {
            notDeletedFolderByName = findNotDeletedFolder(folderName);
        } catch (Exception e) {
            AppLogger.BASIC.e(TAG, e.getMessage());
            return null;
        }
        return notDeletedFolderByName;
    }

    @Query("SELECT * FROM folders WHERE guid != \'" + FolderInfo.FOLDER_GUID_NO_GUID + "\' AND encrypted != " + FolderInfo.FOLDER_ENCRYPTED)
    public abstract List<Folder> findCustomFolder();

    /**
     * 查询所有加密笔记本（不含原有的隐藏笔记）
     * @param encryptFolder  原有的隐藏笔记的guid
     * @return list
     */
    @Query("SELECT * FROM folders WHERE encrypted = \'" + FolderInfo.FOLDER_ENCRYPTED + "\' AND guid != :encryptFolder")
    public abstract List<Folder> findEncryptedFolder(String encryptFolder );

    @Query("SELECT count(*) FROM folders WHERE state != " + FolderInfo.FOLDER_STATE_DELETED)
    public abstract int getNotDeletedFolderCount();

    @Query("SELECT * FROM folders WHERE state != " + FolderInfo.FOLDER_STATE_UNCHANGE + " LIMIT 1")
    public abstract boolean existDirtyFolder();

    @Query("SELECT * FROM folders WHERE state != " + FolderInfo.FOLDER_STATE_DELETED + " ORDER BY created_time DESC")
    public abstract List<Folder> getNotDeletedFoldersOrderbyCreatedTime();


    @Query("SELECT * FROM folders WHERE state != " + FolderInfo.FOLDER_STATE_DELETED + " AND folders.guid != :excludedFolderGuid " + " ORDER BY created_time DESC")
    public abstract List<Folder> getNotDeletedFoldersExcludeOrderbyCreatedTime(String excludedFolderGuid);

    @Query("SELECT * FROM folders ORDER BY created_time DESC")
    public abstract List<Folder> getAllFoldersOrderByCreateTime();

    @Query("SELECT * FROM folders WHERE state != "+ FolderInfo.FOLDER_STATE_UNCHANGE +" ORDER BY created_time DESC")
    public abstract List<Folder> getDirtyFolders();

    @Query("SELECT * FROM folders WHERE guid =:folderGuid OR name =:folderName ORDER BY created_time DESC")
    public abstract List<Folder> getFoldersWithGuidOrName(String folderGuid, String folderName);

    @Query("SELECT * FROM folders where folders.state != " + Folder.FOLDER_STATE_DELETED)
    public abstract LiveData<List<Folder>> getViewableFolders();

    public LiveData<List<FolderItem>> getDrawerRichNoteFolderInfo() {
        return doGetDrawerRichNoteFolderInfo(FolderInfo.FOLDER_GUID_ENCRYPTED);
    }

    @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT count(rich_notes.folder_id) AS notes_count, folders.* FROM folders LEFT JOIN rich_notes "
            + " ON rich_notes.deleted != 1 AND rich_notes.recycle_time = 0 AND rich_notes.folder_id "
            + "in (select folders.guid from folders where folders.encrypted = 0) "
            + " GROUP BY folders.guid "
            + " HAVING folders.guid='" + FolderInfo.FOLDER_GUID_NO_GUID + "'"
            + " UNION SELECT count(rich_notes.folder_id) AS notes_count, folders.* FROM folders LEFT JOIN rich_notes "
            + " ON (folders.guid = rich_notes.folder_id AND rich_notes.deleted != 1) AND rich_notes.recycle_time = 0 "
            + " GROUP BY folders.guid "
            + " HAVING folders.guid != '" + FolderInfo.FOLDER_GUID_NO_GUID + "'" + " AND folders.guid != :excludedFolderGuid " + "  AND folders.state != " + FolderInfo.FOLDER_STATE_DELETED + " ORDER BY created_time DESC")
    protected abstract LiveData<List<FolderItem>> doGetDrawerRichNoteFolderInfo(String excludedFolderGuid);

    @SuppressWarnings(RoomWarnings.CURSOR_MISMATCH)
    @Query("SELECT count(rich_notes.folder_id) AS notes_count, folders.* FROM folders LEFT JOIN rich_notes "
            + " ON rich_notes.deleted != 1 AND rich_notes.recycle_time = 0 AND rich_notes.folder_id "
            + "in (select folders.guid from folders where folders.encrypted = 0) "
            + " GROUP BY folders.guid "
            + " HAVING folders.guid='" + FolderInfo.FOLDER_GUID_NO_GUID + "'"
            + " UNION SELECT count(rich_notes.folder_id) AS notes_count, folders.* FROM folders LEFT JOIN rich_notes "
            + " ON (folders.guid = rich_notes.folder_id AND rich_notes.deleted != 1) AND rich_notes.recycle_time = 0 "
            + " GROUP BY folders.guid "
            + " HAVING folders.guid != '" + FolderInfo.FOLDER_GUID_NO_GUID + "'" + " AND folders.guid != :excludedFolderGuid " + "  AND folders.state != " + FolderInfo.FOLDER_STATE_DELETED + " ORDER BY created_time DESC")
    protected abstract List<FolderItem> doGetDrawerRichNoteFolderInfoSync(String excludedFolderGuid);

    public List<FolderItem> getDrawerRichNoteFolderInfoSync() {
        return doGetDrawerRichNoteFolderInfoSync(FolderInfo.FOLDER_GUID_ENCRYPTED);
    }

    @Query("SELECT count(*) FROM (SELECT rich_notes.local_id FROM rich_notes  WHERE rich_notes.recycle_time > 0 AND rich_notes.deleted != " + NoteInfo.STATE_MARK_DELETED + " GROUP BY rich_notes.local_id)")
    public abstract LiveData<Integer> getDrawerDeletedCountForRichNote();

    @Nullable
    @Query("SELECT * FROM folders WHERE guid = :guid")
    public abstract Folder findByGuid(String guid);

    @Query("SELECT * FROM folders WHERE guid in (:guids)")
    public abstract List<Folder> findByGuids(List<String> guids);

    @Query("SELECT * FROM folders WHERE guid = :guid and encrypted == 0 and state != :state")
    public abstract Folder findByGuidNoEncryptedAndNoState(String guid, int state);

    @Update
    public abstract int updateFolder(Folder folder);

    @Query("select * from folders where state != :state")
    public abstract List<Folder> findFoldersByStateNotEquals(int state);

    @Transaction
    public int deleteFolderByGuid(List<String> folderGuids) {
        String idsStr = SqlUtils.joinIds(folderGuids);
        String sql = "delete from folders where guid in (" + idsStr + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        return executeSqlReturnInt(query);
    }

    @Transaction
    public void deleteNewStateFolderByGuid(List<String> deleteOnCloudOpenFolderList) {
        String ids = SqlUtils.joinIds(deleteOnCloudOpenFolderList);
        String sql = "delete from folders where state = " + FolderInfo.FOLDER_STATE_NEW + " and guid in (" + ids + ")";

        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        executeSqlReturnInt(query);
    }

    @Transaction
    public void updateNotNewStateFolderStateToDeletedByGuid(List<String> deleteOnCloudOpenFolderList) {
        String ids = SqlUtils.joinIds(deleteOnCloudOpenFolderList);
        String sql = "update folders set state = " + FolderInfo.FOLDER_STATE_DELETED
                + ", modify_time = " + System.currentTimeMillis()
                + " where guid in (" + ids + ") and state != " + FolderInfo.FOLDER_STATE_NEW;
        SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
        executeSqlReturnInt(query);
    }

    @Query("DELETE FROM folders WHERE state = :folderState")
    public abstract int deleteFolderByState(int folderState);

    @Update
    public abstract int updateFolders(List<Folder> folders);

    @Transaction
    public void updateFolderStateAndSysVersion(@NotNull List<Pair<String, Long>> guidAndSysVersions) {
        for (Pair<String, Long> guidAndSysVersion : guidAndSysVersions) {
            String sql = "update folders set state = " + FolderInfo.FOLDER_STATE_UNCHANGE + " , sysVersion = " + guidAndSysVersion.getSecond() +
                    " where guid = " + "'" + guidAndSysVersion.getFirst() + "'";
            SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
            executeSqlReturnInt(query);
        }
    }

    @Transaction
    public void updateFolderStateAndEncryptSysVersion(@NotNull List<Pair<String, Long>> guidAndSysVersions) {
        for (Pair<String, Long> guidAndSysVersion : guidAndSysVersions) {
            String sql = "update folders set state = " + FolderInfo.FOLDER_STATE_UNCHANGE + " , encryptSysVersion = " + guidAndSysVersion.getSecond() +
                    " where guid = " + "'" + guidAndSysVersion.getFirst() + "'";
            SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
            executeSqlReturnInt(query);
        }
    }

    @Transaction
    public void updateFoldersEncryptPreSysVersion(@NotNull List<Pair<String, Long>> guidAndSysVersions) {
        for (Pair<String, Long> guidAndSysVersion : guidAndSysVersions) {
            String sql = "update folders set encrypted_pre = encrypted , state = " + FolderInfo.FOLDER_STATE_UNCHANGE + " , sysVersion = " + guidAndSysVersion.getSecond() +
                    " where guid = " + "'" + guidAndSysVersion.getFirst() + "'";
            SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
            executeSqlReturnInt(query);
        }
    }

    @Transaction
    public void updateFoldersEncryptPreEncryptSysVersion(@NotNull List<Pair<String, Long>> guidAndSysVersions) {
        for (Pair<String, Long> guidAndSysVersion : guidAndSysVersions) {
            String sql = "update folders set encrypted_pre = encrypted , state = " + FolderInfo.FOLDER_STATE_UNCHANGE + " , encryptSysVersion = " + guidAndSysVersion.getSecond() +
                    " where guid = " + "'" + guidAndSysVersion.getFirst() + "'";
            SimpleSQLiteQuery query = new SimpleSQLiteQuery(sql);
            executeSqlReturnInt(query);
        }
    }

    @Query("UPDATE folders SET guid = :newGuid,  state = 0, sysVersion = 0, encryptSysVersion = 0 WHERE guid == :oldGuid")
    public abstract void reNewFolder(String oldGuid, String newGuid);

    @Query("SELECT count(*) FROM folders WHERE encrypted == 0 or guid =:includeGuid")
    public abstract int getAllNonEncrypt(String includeGuid);

    @Query("SELECT count(*) FROM folders WHERE state = :state")
    public abstract int getCountOf(int state);

    @Query("SELECT count(*) FROM folders WHERE (encrypted == 0 or guid =:includeGuid) and state = :state")
    public abstract int getNonEncryptCountOf(int state, String includeGuid);


    @Query("SELECT * FROM folders WHERE guid = :guid")
    public abstract LiveData<Folder> findFolderByGuid(@NotNull String guid);

    /**
     * 查询加密笔记本数
     * @param excludeGuid 排除的guid
     * @return 数量
     */
    @Query("SELECT count(*) FROM folders WHERE encrypted = 1 AND guid != :excludeGuid")
    public abstract int getEncryptCount(String excludeGuid);

    /**
     * 查询加密state为state的笔记本数
     * @param state 数据表中的state
     * @param excludeGuid 排除的guid
     * @return 数量
     */
    @Query("SELECT count(*) FROM folders WHERE state = :state AND encrypted = 1 AND guid != :excludeGuid")
    public abstract int getEncryptCountOf(int state, String excludeGuid);

    /**
     * 查询单独保存同步状态的folder，当前为通话摘要和速记
     * @param guids 查询的guid列表
     * @return folder集合
     */
    @Query("SELECT * FROM folders WHERE guid in (:guids)")
    public abstract List<Folder> getFoldersByIdList(List<String> guids);

    /**
     * 查询加密的笔记
     * @return guid集合
     */
    @Query("SELECT guid FROM folders WHERE encrypted = 1 ")
    public abstract List<String> getEncryptedFolders();

    @Transaction
    @Query("update folders set state = " + FolderInfo.FOLDER_STATE_MODIFIED + " where state = " + FolderInfo.FOLDER_STATE_UNCHANGE)
    public abstract int changeStateUnChangeToModify();
}
