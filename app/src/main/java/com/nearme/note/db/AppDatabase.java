/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  AppDatabase.java
 * * Description: AppDatabase.java
 * * Version: 1.0
 * * Date : 2019/12/18
 * * Author: <EMAIL>
 * *
 * * OPLUS Java File Skip Rule:NumberIntUse,LineLength
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2019/12/18      1.0    build this module
 ****************************************************************/

package com.nearme.note.db;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.VisibleForTesting;
import androidx.room.Database;
import androidx.room.OnConflictStrategy;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.nearme.note.MyApplication;
import com.nearme.note.db.daos.CollectPrivacyDao;
import com.nearme.note.db.entities.CollectPrivacy;
import com.oplus.note.repo.note.entity.FolderInfo;
import com.nearme.note.data.NoteAttribute;
import com.nearme.note.db.daos.AlarmNoteDao;
import com.nearme.note.db.daos.CommonDao;
import com.nearme.note.db.daos.FolderDao;
import com.nearme.note.db.daos.NoteAttributeDao;
import com.nearme.note.db.daos.NoteDao;
import com.nearme.note.db.daos.WordDao;
import com.nearme.note.db.entities.AlarmNote;
import com.oplus.note.repo.note.entity.Folder;
import com.nearme.note.db.entities.Note;
import com.nearme.note.db.entities.NotesAttribute;
import com.nearme.note.db.entities.Word;
import com.oplus.note.repo.note.entity.FolderExtra;
import com.nearme.note.model.RichNoteDao;
import com.oplus.note.repo.paragraph.bean.ParaStyleDao;
import com.oplus.note.repo.paragraph.bean.ParaStyleSummary;
import com.oplus.note.repo.skin.bean.SkinDao;
import com.oplus.note.repo.skin.bean.SkinSummary;
import com.oplus.note.repo.todo.entity.ToDo;
import com.nearme.note.model.ToDoDao;
import com.nearme.note.util.DataStatisticsHelper;
import com.nearme.note.util.SqlUtils;
import com.nearme.note.util.StatisticsUtils;
import com.oplus.cloud.agent.SyncAgentContants;
import com.oplus.cloud.status.Device;
import com.oplus.cloud.sync.note.AnchorManager;
import com.oplus.note.R;
import com.oplus.note.logger.AppLogger;
import com.oplus.note.repo.note.entity.Attachment;
import com.oplus.note.repo.note.entity.CloudSyncSubAttachmentItem;
import com.oplus.note.repo.note.entity.RichNote;
import com.oplus.note.repo.note.entity.RichNoteExtra;
import com.oplus.note.repo.note.entity.SpeechLogInfo;
import com.oplus.note.scenecard.todo.ui.controller.TodoResourceController;
import com.oplus.note.utils.SharedPreferencesUtil;
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * PLEASE NOTE:
 * when do backup, we read db records and serialization as json, when do restore, we read json and deserialization as data bean,
 * in order to make sure backup and restore can work normal between different version, we should follow below rules:
 * when our business need to alert db schema for table folders / rich_notes /attachments /note_skin / todo
 * we must NOT delete or rename column, we could only add column.
 * **/
@Database(entities = {Note.class, Folder.class, NotesAttribute.class, Word.class, AlarmNote.class, ToDo.class,
        SkinSummary.class, ParaStyleSummary.class, RichNote.class, Attachment.class, SpeechLogInfo.class, CollectPrivacy.class},
        version = AppDatabase.DB_VERSION, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    public static final int DB_VERSION = 42;

    private static final String TAG = "AppDatabase";

    private static final String SP_NAME_NOTE_DB_UPGRADE = "note_db_upgrade";

    private static final String SP_KEY_HAVE_UPGRADED_TO_23 = "have_upgraded_to_23";
    private static final String SP_KEY_OLD_DB_UPGRADE_23_FAILED = "old_db_upgrade_23_failed";
    private static final String SP_KEY_UPGRADE_TO_ROOM_DB_FAILED = "upgrade_to_room_db_failed";
    private static final String SP_KEY_MIGRATE_TODO_DATA_FAILED = "migrate_todo_data_failed";
    private static final String SP_KEY_SHOULD_SHOW_FAILED_DIALOG = "should_show_failed_dialog";

    private static final String SP_KEY_OLD_UPGRADE_23_EXCEPTION = "old_upgrade_23_exception";
    private static final String SP_KEY_UPGRADE_ROOM_EXCEPTION = "upgrade_room_exception";
    private static final String SP_KEY_MIGRATE_TODO_EXCEPTION = "migrate_todo_exception";
    private static final String SP_KEY_BACKUP_FAILED_EXCEPTION = "backup_failed_exception";

    private static final String DATABASE_OLD_BACKUP_PATH = "_backup_old";
    private static final String DATABASE_ROOM_BACKUP_PATH = "_backup_room";

    private static final Migration MIGRATION_34_35 = new Migration(34, 35) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_34_35 start: " + Looper.getMainLooper().isCurrentThread());
            addColorIndexColumnForTodoTable(database);
        }
    };

    private static final Migration MIGRATION_35_36 = new Migration(35, 36) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_35_36 start: " + Looper.getMainLooper().isCurrentThread());
            addSpeechLogInfoTable(database);
            addSpeechLogIndex(database);
            addEncryptedPreColumnForFolderTable(database);
            addEncryptedColumnsForRichNoteTable(database);
            updateDefaultSync(database);
        }
    };

    private static final Migration MIGRATION_36_37 = new Migration(36, 37) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_36_37 start: " + Looper.getMainLooper().isCurrentThread());
            deleteDuplicateFolder(database);
        }
    };

    private static final Migration MIGRATION_37_38 = new Migration(37, 38) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_37_38 start: " + Looper.getMainLooper().isCurrentThread());
            addSpeechMarkAndCard(database);
            addSpeechMark(database);
            addAttachmentExtraField(database);
            addFileInfosToAttachment(database);
            addSpeechLogExtraInfo(database);
        }
    };

    private static final Migration MIGRATION_38_39 = new Migration(38, 39) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_38_39 start: " + Looper.getMainLooper().isCurrentThread());
            addAudioInfoToAttachment(database);
        }
    };
    private static final Migration MIGRATION_39_40 = new Migration(39, 40) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_39_40 start: " + Looper.getMainLooper().isCurrentThread());
            addFeedBackTable(database);
        }
    };

    private static final Migration MIGRATION_40_41 = new Migration(40, 41) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_40_41 start: " + Looper.getMainLooper().isCurrentThread());
            try {
                database.execSQL("ALTER TABLE feed_back RENAME TO collect_privacy");
            } catch (SQLException e) {
                AppLogger.BASIC.e(TAG, "[Room] collect_privacy error : " + e.getMessage());
            }

        }
    };

    private static final Migration MIGRATION_41_42 = new Migration(41, 42) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_41_42 start: " + Looper.getMainLooper().isCurrentThread());
            addParaStyleTable(database);
        }
    };

    @VisibleForTesting
    public static volatile AppDatabase sInstance;

    public abstract NoteDao noteDao();

    public abstract NoteAttributeDao noteAttributeDao();

    public abstract ToDoDao toDoDao();

    public abstract WordDao wordsDao();

    public abstract FolderDao foldersDao();

    public abstract AlarmNoteDao alarmNoteDao();

    public abstract CommonDao commonDao();

    public abstract SkinDao skinDao();

    /**
     * 文本样式数据库操作
     * @return ParaStyleDao
     */
    public abstract ParaStyleDao paraStyleDao();

    public abstract RichNoteDao richNoteDao();
    /***
     * 收集数据DAO
     * @return CollectPrivacyDao
     */
    public abstract CollectPrivacyDao collectPrivacyDao();

    public static AppDatabase getInstance() {
        synchronized (AppDatabase.class) {
            if (sInstance == null) {
                AppLogger.BASIC.d(TAG, "AppDatabase getInstance");
                synchronized (AppDatabase.class) {
                    if (sInstance == null) {
                        RoomDatabase.Builder<AppDatabase> builder = Room.databaseBuilder(MyApplication.getAppContext(), AppDatabase.class, DBConstants.NOTE.DB_NAME);
                        builder.addCallback(new Callback() {
                            @Override
                            public void onCreate(@NonNull SupportSQLiteDatabase db) {
                                super.onCreate(db);
                                String info = MyApplication.getVersion(MyApplication.getApplication() ,true, true);
                                DataStatisticsHelper.INSTANCE.commonOps(TAG, "04010000", DB_VERSION + " app: " + info);
                                createDefaultFolder(db);
                            }
                        });
                        builder.addMigrations(MIGRATION_1_24);
                        builder.addMigrations(MIGRATION_2_24);
                        builder.addMigrations(MIGRATION_3_24);
                        builder.addMigrations(MIGRATION_4_24);
                        builder.addMigrations(MIGRATION_5_24);
                        builder.addMigrations(MIGRATION_6_24);
                        builder.addMigrations(MIGRATION_7_24);
                        builder.addMigrations(MIGRATION_8_24);
                        builder.addMigrations(MIGRATION_9_24);
                        builder.addMigrations(MIGRATION_10_24);
                        builder.addMigrations(MIGRATION_11_24);
                        builder.addMigrations(MIGRATION_12_24);
                        builder.addMigrations(MIGRATION_13_24);
                        builder.addMigrations(MIGRATION_14_24);
                        builder.addMigrations(MIGRATION_15_24);
                        builder.addMigrations(MIGRATION_16_24);
                        builder.addMigrations(MIGRATION_17_24);
                        builder.addMigrations(MIGRATION_18_24);
                        builder.addMigrations(MIGRATION_19_24);
                        builder.addMigrations(MIGRATION_20_24);
                        builder.addMigrations(MIGRATION_21_24);
                        builder.addMigrations(MIGRATION_22_24);
                        builder.addMigrations(MIGRATION_23_24);
                        builder.addMigrations(MIGRATION_24_25);
                        builder.addMigrations(MIGRATION_25_26);
                        builder.addMigrations(MIGRATION_26_27);
                        builder.addMigrations(MIGRATION_27_28);
                        builder.addMigrations(MIGRATION_28_29);
                        builder.addMigrations(MIGRATION_29_30);
                        builder.addMigrations(MIGRATION_30_31);
                        builder.addMigrations(MIGRATION_31_32);
                        builder.addMigrations(MIGRATION_32_33);
                        builder.addMigrations(MIGRATION_33_34);
                        builder.addMigrations(MIGRATION_34_35);
                        builder.addMigrations(MIGRATION_35_36);
                        builder.addMigrations(MIGRATION_36_37);
                        builder.addMigrations(MIGRATION_37_38);
                        builder.addMigrations(MIGRATION_38_39);
                        builder.addMigrations(MIGRATION_39_40);
                        builder.addMigrations(MIGRATION_40_41);
                        builder.addMigrations(MIGRATION_41_42);
                        sInstance = builder.build();
                    }
                }
            }
        }
        return sInstance;
    }

    private static final Migration MIGRATION_1_24 = new Migration(1, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_1_24 start");
            fullUpgradeToRoom(database);
        }
    };
    private static final Migration MIGRATION_2_24 = new Migration(2, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_2_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_3_24 = new Migration(3, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_3_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_4_24 = new Migration(4, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_4_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_5_24 = new Migration(5, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_5_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_6_24 = new Migration(6, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_6_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_7_24 = new Migration(7, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_7_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_8_24 = new Migration(8, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_8_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_9_24 = new Migration(9, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_9_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_10_24 = new Migration(10, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_10_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_11_24 = new Migration(11, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_11_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_12_24 = new Migration(12, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_12_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_13_24 = new Migration(13, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_13_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_14_24 = new Migration(14, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_14_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_15_24 = new Migration(15, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_15_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_16_24 = new Migration(16, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_16_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_17_24 = new Migration(17, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_17_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_18_24 = new Migration(18, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_18_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_19_24 = new Migration(19, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_19_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_20_24 = new Migration(20, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_20_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_21_24 = new Migration(21, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_21_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_22_24 = new Migration(22, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_22_24 start");
            fullUpgradeToRoom(database);
        }
    };

    private static final Migration MIGRATION_23_24 = new Migration(23, 24) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_23_24 start");
            upgradeTo24(database);
        }
    };

    private static void fullUpgradeToRoom(@NonNull SupportSQLiteDatabase database) {
        database.setTransactionSuccessful();
        database.endTransaction();
        upgradeOldDbTo23();
        upgradeTo24(database);
        database.beginTransaction();
    }

    private static void upgradeOldDbTo23() {
        try {
            long startTime = System.currentTimeMillis();
            doDbUpgrade(DATABASE_OLD_BACKUP_PATH);
            NotesProviderPresenter.DatabaseHelper.getInstance().getReadableDatabase();
            AppLogger.BASIC.d(TAG, "[Room] nearme_note.db upgrade verstion to 23 cost:" + (System.currentTimeMillis() - startTime));
        } catch (Exception e) {
            StatisticsUtils.setOldDbUpgradeFailed(MyApplication.getAppContext(), e.toString());
            AppLogger.BASIC.d(TAG, "[Room] old db upgrade failed:" + e.toString());
            setDbUpgradeFailed(SP_KEY_OLD_DB_UPGRADE_23_FAILED, true);
            setFailedException(SP_KEY_OLD_UPGRADE_23_EXCEPTION, e.toString());
        }
    }

    private static void upgradeTo24(@NonNull SupportSQLiteDatabase database) {
        AppLogger.BASIC.d(TAG, "[Room] MIGRATION_23_24 start");

        doDbUpgrade(DATABASE_ROOM_BACKUP_PATH);
        upgradeToRoomDb(database);
        migrateTodoData(database);
        if (isAnyUpgradeFailed()) {
            new AppDatabaseRecover().startRecover(database);
        }
    }

    private static final Migration MIGRATION_24_25 = new Migration(24, 25) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_24_25 start");
            addFolderTableExtraColumn(database);
            addNoteTableExtraColumn(database);
            addSkinTable(database);
        }
    };

    private static final Migration MIGRATION_25_26 = new Migration(25, 26) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_25_26 start");
            addRichNoteTable(database);
            addRichNoteIndex(database);
            addAttachmentTable(database);
            addAttachmentIndex(database);
            addFolderTableModifyTimeColumn(database);
        }
    };

    private static final Migration MIGRATION_26_27 = new Migration(26, 27) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_26_27 start: " + Looper.getMainLooper().isCurrentThread());
            addAttachmentAssociateColumn(database);
            addPaintAttachmentIfNeeded(database);
        }
    };

    private static final Migration MIGRATION_27_28 = new Migration(27, 28) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_27_28 start: " + Looper.getMainLooper().isCurrentThread());
            addTodoTableColumn(database);
        }
    };

    private static final Migration MIGRATION_28_29 = new Migration(28, 29) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_28_29 start: " + Looper.getMainLooper().isCurrentThread());
            addIsLocalColumnAndIsPreSetColumnForRichNotesTable(database);
        }
    };

    private static final Migration MIGRATION_29_30 = new Migration(29, 30) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_29_30 start: " + Looper.getMainLooper().isCurrentThread());
            addCallerPackageColumnForRichNotesTable(database);
        }
    };

    private static final Migration MIGRATION_30_31 = new Migration(30, 31) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_30_31 start: " + Looper.getMainLooper().isCurrentThread());
            addSysVersionOrCheckPayloadColumns(database);
        }
    };
    private static final Migration MIGRATION_31_32 = new Migration(31, 32) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_31_32 start: " + Looper.getMainLooper().isCurrentThread());
            addWebCardJsonColumnForRichNotesTable(database);
        }
    };

    private static final Migration MIGRATION_32_33 = new Migration(32, 33) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_32_33 start: " + Looper.getMainLooper().isCurrentThread());
            addSysVersionColumnForNotesTable(database);
        }
    };

    private static final Migration MIGRATION_33_34 = new Migration(33, 34) {
        @Override
        public void migrate(@NonNull SupportSQLiteDatabase database) {
            AppLogger.BASIC.d(TAG, "[Room] MIGRATION_33_34 start: " + Looper.getMainLooper().isCurrentThread());
            addHtmlTextColumnForRichNotesTable(database);
            transHtmlTextForRichNote(database);
        }
    };

    private static void addSysVersionOrCheckPayloadColumns(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `sysVersion` INTEGER NOT NULL DEFAULT 0");
            database.execSQL("ALTER TABLE `folders` ADD COLUMN `sysVersion` INTEGER NOT NULL DEFAULT 0");
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `sysVersion` INTEGER");
            database.execSQL("ALTER TABLE `attachments` ADD COLUMN `checkPayload` TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addIsLocalColumnForRichNotesTable error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addIsLocalColumnAndIsPreSetColumnForRichNotesTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `is_local` INTEGER NOT NULL default 0");
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `is_preset` INTEGER NOT NULL default 0");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addIsLocalColumnForRichNotesTable error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addCallerPackageColumnForRichNotesTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `from_package` TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addCallerPackageColumnForRichNotesTable error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addWebCardJsonColumnForRichNotesTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `web_notes` TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addCallerPackageColumnForRichNotesTable error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addSysVersionColumnForNotesTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `notes` ADD COLUMN `sysVersion` INTEGER NOT NULL DEFAULT 0");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addSysVersionColumnForNoteTable error : " + e.getMessage());
        }
    }

    private static void addHtmlTextColumnForRichNotesTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `html_text` TEXT NOT NULL DEFAULT ''");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addCallerPackageColumnForRichNotesTable error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addColorIndexColumnForTodoTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `color_index` INTEGER NOT NULL DEFAULT -1");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addColorIndexColumnForTodoTable error : " + e.getMessage());
        }
    }


    private static void addEncryptedPreColumnForFolderTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `folders` ADD COLUMN `encrypted_pre` INTEGER NOT NULL DEFAULT 0");
            database.execSQL("ALTER TABLE `folders` ADD COLUMN `encryptSysVersion` INTEGER NOT NULL DEFAULT 0");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addEncryptPreColumnForFolderTable error : " + e.getMessage());
        }
    }

    private static void addEncryptedColumnsForRichNoteTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `encryptSysVersion` INTEGER ");
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `encrypted_pre` INTEGER NOT NULL DEFAULT 0");
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `encrypted` INTEGER NOT NULL DEFAULT 0");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addEncryptedColumnsForRichNoteTable error : " + e.getMessage());
        }
    }

    private static void updateDefaultSync(SupportSQLiteDatabase database) {
        try {
            database.execSQL("UPDATE folders SET extra = " + "'" + getDefaultSync() + "'" + " WHERE guid = " + "'" + FolderInfo.FOLDER_GUID_ENCRYPTED + "'");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] updateDefaultSync error : " + e.getMessage());
        }
    }

    private static void addSpeechLogInfoTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `speech_log_info`(`speech_log_id` TEXT NOT NULL,`contact_cover` TEXT, `contact_number` TEXT, `contact_name` TEXT, `voice_id` TEXT,"
                    + "`voice_lrc_id` TEXT,`voice_url` TEXT,`pic_id` TEXT,`voice_lrc_url` TEXT,`flag` INTEGER NOT NULL default 0,`entity` TEXT, `speech_create_time` INTEGER NOT NULL default 0,"
                    + "`speech_type` INTEGER NOT NULL default 0,`rich_note_id` TEXT NOT NULL,`summary_id` TEXT NOT NULL PRIMARY KEY, "
                    + "FOREIGN KEY(`rich_note_id`) REFERENCES `rich_notes`(`local_id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addSpeechLogInfoTable error : " + e.getMessage());
        }
    }

    private static void addSpeechLogIndex(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_speech_log_info_rich_note_id` ON `speech_log_info` (`rich_note_id`)");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create speech_log_info index error : " + e.getMessage());
        }
    }

    private static void addSpeechMarkAndCard(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `speech_log_info` ADD COLUMN `speech_mark` TEXT ");
            database.execSQL("ALTER TABLE `speech_log_info` ADD COLUMN `combined_card` TEXT ");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addSpeechMarkAndCard error : " + e.getMessage());
        }
    }

    private static void addAttachmentExtraField(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `rich_notes` ADD COLUMN `attachment_extra` TEXT ");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addAttachmentExtraField error : " + e.getMessage());
        }
    }

    private static void addFileInfosToAttachment(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `attachments` ADD COLUMN `file_name` TEXT ");
            database.execSQL("ALTER TABLE `attachments` ADD COLUMN `cloud_metadata` TEXT ");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addFileInfosToAttachment error : " + e.getMessage());
        }
    }
    private static void addSpeechLogExtraInfo(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `speech_log_info` ADD COLUMN `extra_info` TEXT ");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addFileNameToAttachment error : " + e.getMessage());
        }
    }

    private static void deleteDuplicateFolder(SupportSQLiteDatabase database) {
        Cursor cursor = null;
        try {
            cursor = database.query("select _id,guid from folders where guid in (SELECT guid FROM folders GROUP BY guid HAVING COUNT(*) >= 2)");
            //遍历cursor
            HashMap<String, Integer> map = new HashMap<>();
            List<String> willDeleteFolderGuids = new ArrayList<>();
            if (cursor != null && cursor.moveToFirst()) {
                do {
                    int id = cursor.getInt(0);
                    String gid = cursor.getString(1);
                    AppLogger.BASIC.d(TAG, "id: " + id + ",gid:" + gid);
                    if (map.containsKey(gid)) {
                        willDeleteFolderGuids.add(String.valueOf(id));
                    } else {
                        map.put(gid, id);
                    }
                } while (cursor.moveToNext());
            }
            if (willDeleteFolderGuids.size() > 0) {
                String idsStr = SqlUtils.joinIds(willDeleteFolderGuids);
                AppLogger.BASIC.d(TAG, "idStr: " + idsStr);
                String sql = "delete from folders where _id in (" + idsStr + ")";
                database.execSQL(sql);
            }
            addUniqueConstraintGuid(database);
        } catch (Exception e) {
            StatisticsUtils.setOldDbUpgradeFailed(MyApplication.getAppContext(), e.toString());
            AppLogger.BASIC.e(TAG, "migration 36_37 err: " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    private static void addUniqueConstraintGuid(SupportSQLiteDatabase database) {
        database.execSQL("CREATE UNIQUE INDEX `index_folders_guid` ON `folders` (`guid`)");
    }

    private static void addSpeechMark(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'speech_log_info' ADD COLUMN 'speech_mark' TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create speech_log_info speech mark error : " + e.getMessage());
        }
    }

    private static void addTodoTableColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `parent_id` TEXT");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `extra` TEXT");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `alarm_next_time` INTEGER");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `is_reminded` INTEGER default 0");// 用于记录重复待办是否有在系统内提醒过： 默认0  表示未提醒过，则在待办完成时，需要改变结束规则内的次数;  1表示提醒过,在完成待办时，不需要改变结束次数
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `is_local` INTEGER default 0");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `force_reminder_pre` INTEGER default 0");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `repeat_rule_pre` TEXT");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `alarm_time_pre` INTEGER DEFAULT 0");
            database.execSQL("ALTER TABLE `todo` ADD COLUMN `from_package` TEXT");

            database.execSQL("UPDATE todo set alarm_next_time = todo.alarm_time");

        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addTodoTableExtraColumn error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void upgradeToRoomDb(SupportSQLiteDatabase database) {
        long startTime = System.currentTimeMillis();
        try {
            database.beginTransaction();
            createNewNameTables(database);
            moveDataToNewTables(database);
            renameNewTableToOldName(database);
            createTriggers(database);
        } catch (Exception e) {
            StatisticsUtils.setEventRoomDbUpgradeFailed(MyApplication.getAppContext(), e.toString());
            reCreateOldRoomDb(database);
            setDbUpgradeFailed(SP_KEY_UPGRADE_TO_ROOM_DB_FAILED, true);
            setFailedException(SP_KEY_UPGRADE_ROOM_EXCEPTION, e.toString());
            AppLogger.BASIC.e(TAG, "[Room] migration failed:" + e);
        } finally {
            database.setTransactionSuccessful();
            database.endTransaction();
        }
        AppLogger.BASIC.d(TAG, "[Room] upgrade nearme_note.db to Room cost:" + (System.currentTimeMillis() - startTime));
    }

    public static void doDbUpgrade(String suffix) {
        String dataPath = MyApplication.getAppContext().getFilesDir().getParent();
        dataPath += "/databases";
        String destPath = dataPath + suffix;
        backupDb(dataPath, destPath);
    }

    private static void backupDb(String sourcePath, String distPath) {
        try {
            copyFolder(new File(sourcePath), new File(distPath));
            AppLogger.BASIC.d(TAG, "[Room] copy file success:");
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] copy file failed:" + e);
            setFailedException(SP_KEY_BACKUP_FAILED_EXCEPTION, e.toString());
        }
    }

    private static void copyFolder(File src, File dest) throws IOException {
        if (src.isDirectory()) {
            if (!dest.exists()) {
                dest.mkdirs();
            }
            String[] files = src.list();
            if (files == null) {
                return;
            }
            for (String file : files) {
                File srcFile = new File(src, file);
                File destFile = new File(dest, file);
                copyFolder(srcFile, destFile);
            }
        } else {
            InputStream in = null;
            OutputStream out = null;
            try {
                in = new FileInputStream(src);
                out = new FileOutputStream(dest);

                byte[] buffer = new byte[4096];
                int length = 0;
                while ((length = in.read(buffer)) > 0) {
                    out.write(buffer, 0, length);
                }
                out.flush();
            } catch (Exception ignore) {
            } finally {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            }
        }
    }

    private static void createNewNameTables(SupportSQLiteDatabase database) {
        database.execSQL("CREATE TABLE IF NOT EXISTS `notes_new` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `guid` TEXT, `version` INTEGER NOT NULL DEFAULT 0, `topped` INTEGER DEFAULT 0, `updated` INTEGER DEFAULT 0, `recycled_time` INTEGER DEFAULT 0, `state` INTEGER NOT NULL DEFAULT 0, `description` TEXT, `note_folder` TEXT, `note_folder_guid` TEXT NOT NULL DEFAULT '00000000_0000_0000_0000_000000000000', `attr_count` INTEGER NOT NULL DEFAULT 0, `sort` INTEGER NOT NULL DEFAULT 0, `created_console` INTEGER NOT NULL DEFAULT 0, `thumb_type` INTEGER NOT NULL DEFAULT 0, `thumb_filename` TEXT, `uid` INTEGER NOT NULL DEFAULT 0, `deleted` INTEGER NOT NULL DEFAULT 0, `para` INTEGER NOT NULL DEFAULT 0, `created` INTEGER DEFAULT 0, `globalId` TEXT, `attachment_id` TEXT, `attachment_md5` TEXT, `account` TEXT, `alarm_time` INTEGER DEFAULT 0, `note_skin` TEXT, `recycled_time_pre` INTEGER DEFAULT 0, `alarm_time_pre` INTEGER DEFAULT 0, `note_skin_pre` TEXT, `timestamp` INTEGER)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `folders_new` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `guid` TEXT, `state` INTEGER NOT NULL, `created_time` INTEGER, `modify_device` TEXT, `data1` TEXT, `data2` TEXT, `encrypted` INTEGER NOT NULL DEFAULT 0)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `notes_attributes_new` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `note_guid` TEXT, `type` INTEGER NOT NULL DEFAULT 0, `filename` TEXT, `version` INTEGER NOT NULL DEFAULT 0, `updated` INTEGER DEFAULT 0, `para` TEXT, `state` INTEGER NOT NULL DEFAULT 1, `attachment_md5` TEXT, `attachment_sync_url` TEXT, `sync_data1` TEXT, `width` INTEGER NOT NULL DEFAULT 0, `height` INTEGER NOT NULL DEFAULT 0)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `words_new` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `note_guid` TEXT, `content` TEXT, `updated` INTEGER, `state` INTEGER NOT NULL)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `alarm_note_new` (`guid` TEXT NOT NULL, `alarm_time` INTEGER DEFAULT 0, PRIMARY KEY(`guid`))");
        database.execSQL("CREATE TABLE IF NOT EXISTS `todo` (`local_id` TEXT NOT NULL, `global_id` TEXT, `content` TEXT, `alarm_time` INTEGER, `create_time` INTEGER, `update_time` INTEGER, `finish_time` INTEGER, `status` INTEGER, `is_delete` INTEGER, `timestamp` INTEGER, PRIMARY KEY(`local_id`))");
    }

    private static void moveDataToNewTables(SupportSQLiteDatabase database) {
        database.execSQL("INSERT INTO folders_new (_id, name, guid, state, created_time, modify_device,data1, data2, encrypted) "
                + " SELECT _id, name, guid, state, created_time, modify_device, data1, data2, encrypted FROM folders");
        database.execSQL("INSERT INTO notes_new (_id, guid, version, topped, updated, recycled_time, state, description, note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, thumb_filename, uid, deleted, para, created, globalId, attachment_id, attachment_md5, account, alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, timestamp) "
                + " SELECT _id, guid, version, topped, updated, recycled_time, state, description, note_folder, note_folder_guid, attr_count, sort, created_console, thumb_type, thumb_filename, uid, deleted, para, created, globalId, attachment_id, attachment_md5, account, alarm_time, note_skin, recycled_time_pre, alarm_time_pre, note_skin_pre, timestamp FROM notes");
        database.execSQL("INSERT INTO notes_attributes_new (_id, note_guid, type, filename, version, updated, para, state, attachment_md5, attachment_sync_url, sync_data1, width, height) "
                + " SELECT _id, note_guid, type, filename, version, updated, para, state, attachment_md5, attachment_sync_url, sync_data1, width, height FROM notes_attributes");
        database.execSQL("INSERT INTO words_new (_id, note_guid, content, updated, state)"
                + " SELECT _id, note_guid, content, updated, state FROM words");
        database.execSQL("INSERT INTO alarm_note_new (guid, alarm_time)"
                + " SELECT guid, alarm_time FROM alarm_note");
    }

    private static void createTriggers(SupportSQLiteDatabase database) {
        database.execSQL("CREATE TRIGGER IF NOT EXISTS attribute_delete AFTER DELETE ON notes_attributes " + " BEGIN "
                + " DELETE FROM words WHERE note_guid = OLD.note_guid AND OLD.type=" + NoteAttribute.TYPE_TEXT_CONTENT + "; " + " END;");
        database.execSQL("CREATE TRIGGER IF NOT EXISTS attribute_update AFTER UPDATE ON notes_attributes " + " BEGIN "
                + " UPDATE words SET state = NEW.state WHERE (note_guid=NEW.note_guid); " + " END;");
    }

    private static void renameNewTableToOldName(SupportSQLiteDatabase database) {
        database.execSQL("DROP TABLE IF EXISTS folders");
        database.execSQL("ALTER TABLE folders_new RENAME TO folders");

        database.execSQL("DROP TABLE IF EXISTS notes");
        database.execSQL("ALTER TABLE notes_new RENAME TO notes");

        database.execSQL("DROP TABLE IF EXISTS notes_attributes");
        database.execSQL("ALTER TABLE notes_attributes_new RENAME TO notes_attributes");

        database.execSQL("DROP TABLE IF EXISTS words");
        database.execSQL("ALTER TABLE words_new RENAME TO words");

        database.execSQL("DROP TABLE IF EXISTS alarm_note");
        database.execSQL("ALTER TABLE alarm_note_new RENAME TO alarm_note");
    }

    private static void reCreateOldRoomDb(SupportSQLiteDatabase database) {
        dropAllNewTables(database);
        backupAllOldTables(database);
        createAllOldNameTables(database);
        createTriggers(database);

        AnchorManager manager = new AnchorManager(MyApplication.getAppContext());
        manager.clearAnchors(SyncAgentContants.DataType.RICH_NOTE);
        manager.clearAnchors(SyncAgentContants.DataType.NOTE);
        manager.clearAnchors(SyncAgentContants.DataType.TODO);
    }

    private static void dropAllNewTables(SupportSQLiteDatabase database) {
        database.execSQL("DROP TABLE IF EXISTS folders_new");
        database.execSQL("DROP TABLE IF EXISTS notes_new");
        database.execSQL("DROP TABLE IF EXISTS notes_attributes_new");
        database.execSQL("DROP TABLE IF EXISTS words_new");
        database.execSQL("DROP TABLE IF EXISTS alarm_note_new");
    }

    private static void backupAllOldTables(SupportSQLiteDatabase database) {
        database.execSQL("ALTER TABLE folders RENAME TO " + DBConstants.NOTE_BACK.TABLE_FOLDERS_BACK);
        database.execSQL("ALTER TABLE notes RENAME TO " + DBConstants.NOTE_BACK.TABLE_NOTES_BACK);
        database.execSQL("ALTER TABLE notes_attributes RENAME TO " + DBConstants.NOTE_BACK.TABLE_NOTES_ATTRIBUTES_BACK);
        database.execSQL("ALTER TABLE words RENAME TO " + DBConstants.NOTE_BACK.TABLE_WORDS_BACK);
        database.execSQL("ALTER TABLE alarm_note RENAME TO " + DBConstants.NOTE_BACK.TABLE_ALARM_NOTE_BACK);
    }

    private static void createAllOldNameTables(SupportSQLiteDatabase database) {
        database.execSQL("CREATE TABLE IF NOT EXISTS `notes` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `guid` TEXT, `version` INTEGER NOT NULL DEFAULT 0, `topped` INTEGER DEFAULT 0, `updated` INTEGER DEFAULT 0, `recycled_time` INTEGER DEFAULT 0, `state` INTEGER NOT NULL DEFAULT 0, `description` TEXT, `note_folder` TEXT, `note_folder_guid` TEXT NOT NULL DEFAULT '00000000_0000_0000_0000_000000000000', `attr_count` INTEGER NOT NULL DEFAULT 0, `sort` INTEGER NOT NULL DEFAULT 0, `created_console` INTEGER NOT NULL DEFAULT 0, `thumb_type` INTEGER NOT NULL DEFAULT 0, `thumb_filename` TEXT, `uid` INTEGER NOT NULL DEFAULT 0, `deleted` INTEGER NOT NULL DEFAULT 0, `para` INTEGER NOT NULL DEFAULT 0, `created` INTEGER DEFAULT 0, `globalId` TEXT, `attachment_id` TEXT, `attachment_md5` TEXT, `account` TEXT, `alarm_time` INTEGER DEFAULT 0, `note_skin` TEXT, `recycled_time_pre` INTEGER DEFAULT 0, `alarm_time_pre` INTEGER DEFAULT 0, `note_skin_pre` TEXT, `timestamp` INTEGER)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `folders` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `guid` TEXT, `state` INTEGER NOT NULL, `created_time` INTEGER, `modify_device` TEXT, `data1` TEXT, `data2` TEXT, `encrypted` INTEGER NOT NULL DEFAULT 0)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `notes_attributes` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `note_guid` TEXT, `type` INTEGER NOT NULL DEFAULT 0, `filename` TEXT, `version` INTEGER NOT NULL DEFAULT 0, `updated` INTEGER DEFAULT 0, `para` TEXT, `state` INTEGER NOT NULL DEFAULT 1, `attachment_md5` TEXT, `attachment_sync_url` TEXT, `sync_data1` TEXT, `width` INTEGER NOT NULL DEFAULT 0, `height` INTEGER NOT NULL DEFAULT 0)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `words` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `note_guid` TEXT, `content` TEXT, `updated` INTEGER, `state` INTEGER NOT NULL)");
        database.execSQL("CREATE TABLE IF NOT EXISTS `alarm_note` (`guid` TEXT NOT NULL, `alarm_time` INTEGER DEFAULT 0, PRIMARY KEY(`guid`))");
        database.execSQL("CREATE TABLE IF NOT EXISTS `todo` (`local_id` TEXT NOT NULL, `global_id` TEXT, `content` TEXT, `alarm_time` INTEGER, `create_time` INTEGER, `update_time` INTEGER, `finish_time` INTEGER, `status` INTEGER, `is_delete` INTEGER, `timestamp` INTEGER, PRIMARY KEY(`local_id`))");

        createDefaultFolder(database);
    }

    private static void createDefaultFolder(SupportSQLiteDatabase database) {
        database.execSQL("INSERT INTO " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                + NotesProvider.COL_FOLDER_NAME + ", "
                + NotesProvider.COL_FOLDER_GUID + ", "
                + NotesProvider.COL_FOLDER_STATE + ", "
                + NotesProvider.COL_FOLDER_CREATED_TIME + ", "
                + NotesProvider.COL_FOLDER_MODIFY_DEVICE + ", "
                + NotesProvider.COL_FOLDER_MODIFY_TIME
                + ") VALUES ("
                + "'" + MyApplication.getAppContext().getResources().getString(R.string.memo_all_notes) + "', "
                + "'" + FolderInfo.FOLDER_GUID_NO_GUID + "', "
                + FolderInfo.FOLDER_STATE_NEW + ", "
                + Long.MAX_VALUE + ", '"
                + Device.getDeviceIMEI(MyApplication.getAppContext()) + "', "
                + System.currentTimeMillis()
                + ");");

        database.execSQL("INSERT INTO " + NotesProvider.TABLE_NAME_FOLDERS + " ("
                + NotesProvider.COL_FOLDER_NAME + ", "
                + NotesProvider.COL_FOLDER_GUID + ", "
                + NotesProvider.COL_FOLDER_STATE + ", "
                + NotesProvider.COL_FOLDER_CREATED_TIME + ", "
                + NotesProvider.COL_FOLDER_MODIFY_DEVICE + ", "
                + NotesProvider.COL_FOLDER_ENCRYPTED + ", "
                + NotesProvider.COL_FOLDER_MODIFY_TIME + ","
                + NotesProvider.COL_FOLDER_EXTRA
                + ") VALUES ("
                + "'" + MyApplication.getAppContext().getResources().getString(com.oplus.note.baseres.R.string.encrypted_note) + "', "
                + "'" + FolderInfo.FOLDER_GUID_ENCRYPTED + "', "
                + FolderInfo.FOLDER_STATE_NEW + ", "
                + Long.MIN_VALUE + ", '"
                + Device.getDeviceIMEI(MyApplication.getAppContext()) + "', "
                + FolderInfo.FOLDER_ENCRYPTED + ","
                + System.currentTimeMillis() + ",'"
                + getDefaultSync() + "'"
                + ");");
    }

    @SuppressLint("Range")
    private static void migrateTodoData(SupportSQLiteDatabase database) {
        long startTime = System.currentTimeMillis();
        SQLiteDatabase todoDb = null;
        Cursor cursor = null;
        try {
            todoDb = TodoSqlOpenHelper.getInstance().getReadableDatabase();
            if (isTableExist(todoDb, DBConstants.TODO.TABLE_TODO)) {
                database.beginTransaction();
                cursor = todoDb.query(DBConstants.TODO.TABLE_TODO, null, null, null, null, null, null);
                if ((cursor != null) && cursor.moveToFirst()) {
                    ContentValues contentValues = new ContentValues();
                    do {
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.LOCAL_ID, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.LOCAL_ID)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.GLOBAL_ID, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.GLOBAL_ID)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.CONTENT, cursor.getString(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.CONTENT)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.ALARM_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.ALARM_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.CREATE_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.CREATE_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.UPDATE_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.UPDATE_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.FINISH_TIME, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.FINISH_TIME)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.STATUS, cursor.getInt(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.STATUS)));
                        contentValues.put(DBConstants.TODO.TODO_COLUMN.IS_DELETE, cursor.getInt(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.IS_DELETE)));
                        if (cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.TIMESTAMP) > 0) {
                            contentValues.put(DBConstants.TODO.TODO_COLUMN.TIMESTAMP, cursor.getLong(cursor.getColumnIndex(DBConstants.TODO.TODO_COLUMN.TIMESTAMP)));
                        } else {
                            contentValues.put(DBConstants.TODO.TODO_COLUMN.TIMESTAMP, 0);
                        }
                        database.insert(DBConstants.TODO.TABLE_TODO, OnConflictStrategy.REPLACE, contentValues);
                    } while (cursor.moveToNext());
                    todoDb.execSQL("DROP TABLE IF EXISTS " + DBConstants.TODO.TABLE_TODO);
                }
            }
        } catch (Exception e) {
            StatisticsUtils.setEventRoomTodoMigrationFailed(MyApplication.getAppContext(), e.toString());
            AppLogger.BASIC.e(TAG, "[Room] todo migration failed:" + e);
            new AnchorManager(MyApplication.getAppContext()).clearAnchors(SyncAgentContants.DataType.TODO);
            setDbUpgradeFailed(SP_KEY_MIGRATE_TODO_DATA_FAILED, true);
            setFailedException(SP_KEY_MIGRATE_TODO_EXCEPTION, e.toString());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            if (todoDb != null) {
                todoDb.close();
            }
            database.setTransactionSuccessful();
            database.endTransaction();
        }
        AppLogger.BASIC.d(TAG, "[Room] move todo.db data to nearme_note.db cost:" + (System.currentTimeMillis() - startTime));
    }

    public static boolean isTableExist(SQLiteDatabase db, String tableName) {
        boolean isExist = false;
        Cursor cursor = null;
        try {
            cursor = db.rawQuery("select count(*) from sqlite_master where type='table' and name='" + tableName + "'", null);
            if ((cursor != null) && cursor.moveToFirst()) {
                isExist = cursor.getInt(0) > 0;
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] Check SQLiteDatabase table exists failed: " + e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return isExist;
    }

    public static boolean isTableExist(SupportSQLiteDatabase db, String tableName) {
        boolean isExist = false;
        Cursor cursor = null;
        try {
            cursor = db.query("select count(*) from sqlite_master where type='table' and name='" + tableName + "'");
            if ((cursor != null) && cursor.moveToFirst()) {
                isExist = cursor.getInt(0) > 0;
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] Check SupportSQLiteDatabase table exists failed: " + e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return isExist;
    }

    public static void resetMigratedFlag() {
        synchronized (AppDatabase.class) {
            SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                    SP_NAME_NOTE_DB_UPGRADE, SP_KEY_HAVE_UPGRADED_TO_23, false);
        }
    }

    private static void setDbUpgradeFailed(String spKey, boolean isFialed) {
        SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, spKey, isFialed);
    }

    private static void setFailedException(String key, String exception) {
        SharedPreferencesUtil.getInstance().putString(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, key, exception);
    }

    public static boolean isUpgradeTo23Failed() {
        return SharedPreferencesUtil.getInstance().getBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_OLD_DB_UPGRADE_23_FAILED, false);
    }

    public static boolean isUpgradeToRoomDbFailed() {
        return SharedPreferencesUtil.getInstance().getBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_UPGRADE_TO_ROOM_DB_FAILED, false);
    }

    public static boolean isMigrateTodoDataFailed() {
        return SharedPreferencesUtil.getInstance().getBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_MIGRATE_TODO_DATA_FAILED, false);
    }

    public static boolean isAnyUpgradeFailed() {
        return isUpgradeTo23Failed() || isUpgradeToRoomDbFailed() || isMigrateTodoDataFailed();
    }

    public static void clearFailedFlags() {
        SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_OLD_DB_UPGRADE_23_FAILED, false);
        SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_UPGRADE_TO_ROOM_DB_FAILED, false);
        SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_MIGRATE_TODO_DATA_FAILED, false);
    }

    public static void setShouldShowFailedDialogFlag(boolean isShow) {
        AppLogger.BASIC.d(TAG, "[Room] setShouldShowFailedDialogFlag:" + isShow);
        SharedPreferencesUtil.getInstance().putBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_SHOULD_SHOW_FAILED_DIALOG, isShow);
    }

    public static boolean getShouldShowFailedDialog() {
        boolean show = SharedPreferencesUtil.getInstance().getBoolean(MyApplication.getAppContext(),
                SP_NAME_NOTE_DB_UPGRADE, SP_KEY_SHOULD_SHOW_FAILED_DIALOG, false);
        AppLogger.BASIC.d(TAG, "[Room] getShouldShowFailedDialog:" + show);
        return show;
    }

    private static void addFolderTableExtraColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'folders' ADD COLUMN 'extra' TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addNoteTableTitleColumn error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addFolderTableModifyTimeColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'folders' ADD COLUMN 'modify_time' INTEGER");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addFolderTableModifyTimeColumn error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addNoteTableTitleColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'notes' ADD COLUMN 'title' TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addNoteTableTitleColumn error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addNoteTableExtraColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'notes' ADD COLUMN 'extra' TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] addNoteTableExtraColumn error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addSkinTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `note_skin` (`aid` TEXT NOT NULL, `id` TEXT NOT NULL, `md5` TEXT NOT NULL, `name` TEXT NOT NULL, `preview` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `url` TEXT NOT NULL, `versionCode` INTEGER NOT NULL, `detail` TEXT, `condition` TEXT NOT NULL, `data1` TEXT NOT NULL, `data2` TEXT NOT NULL, PRIMARY KEY(`id`, `condition`))");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create table note_skin error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addRichNoteTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `rich_notes` (`local_id` TEXT NOT NULL, `global_id` TEXT, `text` TEXT NOT NULL, `raw_text` TEXT NOT NULL, `folder_id` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `create_time` INTEGER NOT NULL, `update_time` INTEGER NOT NULL, `top_time` INTEGER NOT NULL, `recycle_time` INTEGER NOT NULL, `alarm_time` INTEGER NOT NULL, `state` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `skin_id` TEXT NOT NULL, `title` TEXT, `raw_title` TEXT, `recycle_time_pre` INTEGER, `alarm_time_pre` INTEGER, `skin_id_pre` TEXT, `extra` TEXT, `version` INTEGER NOT NULL, PRIMARY KEY(`local_id`))");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create table rich_notes error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addRichNoteIndex(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_rich_notes_local_id` ON `rich_notes` (`local_id`)");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create rich_notes index error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addAttachmentTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `attachments` (`attachment_id` TEXT NOT NULL, `rich_note_id` TEXT NOT NULL, `type` INTEGER NOT NULL, `state` INTEGER NOT NULL, `md5` TEXT, `url` TEXT, `width` INTEGER, `height` INTEGER, PRIMARY KEY(`attachment_id`), FOREIGN KEY(`rich_note_id`) REFERENCES `rich_notes`(`local_id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create table attachments error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addFeedBackTable(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE TABLE IF NOT EXISTS `feed_back` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,`create_time` INTEGER NOT NULL, `type` TEXT, `content` TEXT)");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addFeedBackTable error : " + e.getMessage());
        }
    }

    private static void addParaStyleTable(SupportSQLiteDatabase database) {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS `note_paragraph_style` (`aid` TEXT NOT NULL, `id` TEXT NOT NULL, `md5` TEXT NOT NULL,"
                    + " `name` TEXT NOT NULL, `preview` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `thumbnailBlack` TEXT NOT NULL,"
                    + " `url` TEXT NOT NULL, `versionCode` INTEGER NOT NULL, `detail` TEXT, `condition` TEXT NOT NULL,"
                    + " `data1` TEXT NOT NULL, `data2` TEXT NOT NULL, `section` TEXT NOT NULL,"
                    + " `structVersion` TEXT NOT NULL, PRIMARY KEY(`id`, `condition`))";
            database.execSQL(sql);
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addParaStyleTable error : " + e.getMessage());
        }
    }

    private static void addAttachmentIndex(SupportSQLiteDatabase database) {
        try {
            database.execSQL("CREATE INDEX IF NOT EXISTS `index_attachments_rich_note_id` ON `attachments` (`rich_note_id`)");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create attachments index error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void addAttachmentAssociateColumn(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE 'attachments' ADD COLUMN 'associate_attachment_id' TEXT");
        } catch (SQLException e) {
            AppLogger.BASIC.d(TAG, "[Room] create attachments associate error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    // In case:the note with paints data sync to lower version with cloud, then upgrade the app
    // to 'paints' version, The paints info has be not parsed from 'extra'. So make the parse
    // process along with db upgrade.
    private static void addPaintAttachmentIfNeeded(SupportSQLiteDatabase database) {
        try (Cursor cursor = database.query("select local_id,extra from rich_notes where extra like '%paints%'")) {
            while (cursor.moveToNext()) {
                RichNoteExtra extra = RichNoteExtra.create(cursor.getString(1));
                if ((extra.getPaints() == null) || extra.getPaints().isEmpty()) {
                    continue;
                }
                String richNoteId = cursor.getString(0);

                ContentValues values = new ContentValues();
                for (CloudSyncSubAttachmentItem paint : extra.getPaints()) {
                    values.put("attachment_id", paint.getId());
                    values.put("type", Attachment.TYPE_PAINT);
                    values.put("url", paint.getUrl());
                    values.put("rich_note_id", richNoteId);
                    values.put("associate_attachment_id", paint.getRelateId());
                    values.put("state", Attachment.STATE_NEW);
                }
                try {
                    database.insert("attachments", SQLiteDatabase.CONFLICT_IGNORE, values);
                } catch (SQLException ignore) {
                }
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] addPaintAttachmentIfNeeded error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void transHtmlTextForRichNote(SupportSQLiteDatabase database) {
        try (Cursor cursor = database.query("select local_id,raw_text from rich_notes")) {
            while (cursor.moveToNext()) {
                String rawText = cursor.getString(1);
                if (rawText == null || rawText.isEmpty()) {
                    AppLogger.BASIC.w(TAG, "Add html text failed via raw text is null.");
                    continue;
                }

                String localId = cursor.getString(0);

                String htmlText =
                        HtmlTransformManagerFactory.gainHtmlTransformManager().toHtmlText(rawText);
                ContentValues contentValues = new ContentValues();
                contentValues.put("html_text", htmlText);
                database.update("rich_notes", SQLiteDatabase.CONFLICT_IGNORE, contentValues,
                        "local_id=?", new String[]{localId});
            }
        } catch (Exception e) {
            AppLogger.BASIC.d(TAG, "[Room] transHtmlTextForRichNote error : " + e.getMessage());
        }
    }

    /**
     * 升级数据库的时候，给未完成的代办绑定颜色index
     * @param database 数据库
     * @param sqlStr sql查询语句
     * @param params sql查询条件参数
     */
    private static void bindTodoColorIndex(SupportSQLiteDatabase database, String sqlStr, Object[] params) {
        Cursor cursor = null;
        try {
            if (params == null) {
                cursor = database.query(sqlStr);
            } else {
                cursor = database.query(sqlStr, params);
            }
            if (cursor == null) {
                return;
            }
            while (cursor.moveToNext()) {
                int colorIndex = cursor.getInt(1);
                String localId = cursor.getString(0);
                if (colorIndex < 0) {
                    colorIndex = TodoResourceController.INSTANCE.checkColorIndexLegal(colorIndex);
                } else {
                    continue;
                }
                ContentValues values = new ContentValues();
                values.put("color_index", colorIndex);
                database.update("todo", SQLiteDatabase.CONFLICT_IGNORE, values, "local_id=?", new String[]{localId});
            }
        } catch (Exception e) {
            AppLogger.BASIC.e(TAG, "[Room] bindTodoColorIndex error : " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }


    private static String getDefaultSync() {
        FolderExtra folderExtra = FolderExtra.Companion.create(null);
        if (com.oplus.note.BuildConfig.isExport) {
            folderExtra.setSync(com.oplus.cloudkit.util.Constants.FOLDER_SYNC_OFF);
        } else {
            folderExtra.setSync(com.oplus.cloudkit.util.Constants.FOLDER_SYNC_ON);
        }
        return folderExtra.upToDate();
    }

    private static void addAudioInfoToAttachment(SupportSQLiteDatabase database) {
        try {
            database.execSQL("ALTER TABLE `attachments` ADD COLUMN `extra` TEXT ");
        } catch (SQLException e) {
            AppLogger.BASIC.e(TAG, "[Room] addFileInfosToAttachment error : " + e.getMessage());
        }
    }
}
