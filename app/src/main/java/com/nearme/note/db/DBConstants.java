/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  DBConstants.java
 * * Description: DBConstants.java
 * * Version: 1.0
 * * Date : 2020/1/19
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * yixiaoming  2020/1/19      1.0    build this module
 ****************************************************************/
package com.nearme.note.db;

public final class DBConstants {

    public static class NOTE {
        public static final String DB_NAME = "nearme_note.db";

        public static final String TABLE_FOLDERS = "folders";
        public static final String TABLE_NOTES = "notes";
        public static final String TABLE_NOTES_ATTRIBUTES = "notes_attributes";
        public static final String TABLE_WORDS = "words";
        public static final String TABLE_ALARM_NOTE = "alarm_note";
    }

    public static class NOTE_BACK {
        public static final String TABLE_FOLDERS_BACK = "folders_backup";
        public static final String TABLE_NOTES_BACK = "notes_backup";
        public static final String TABLE_NOTES_ATTRIBUTES_BACK = "notes_attributes_backup";
        public static final String TABLE_WORDS_BACK = "words_backup";
        public static final String TABLE_ALARM_NOTE_BACK = "alarm_note_backup";
    }

    public static class TODO {
        public static final String DB_NAME = "todo.db";

        public static final int VERSION_1 = 1;
        public static final int VERSION_2 = 2;

        public static final String TABLE_TODO = "todo";

        public static class TODO_COLUMN {
            public static final String LOCAL_ID = "local_id";
            public static final String GLOBAL_ID = "global_id";
            public static final String CONTENT = "content";
            public static final String ALARM_TIME = "alarm_time";
            public static final String CREATE_TIME = "create_time";
            public static final String UPDATE_TIME = "update_time";
            public static final String FINISH_TIME = "finish_time";
            public static final String STATUS = "status";
            public static final String IS_DELETE = "is_delete";
            public static final String TIMESTAMP = "timestamp";
        }
    }
}
