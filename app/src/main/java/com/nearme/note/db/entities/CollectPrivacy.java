package com.nearme.note.db.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "collect_privacy")
public class CollectPrivacy {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id")
    public int id;

    @ColumnInfo(name = "content")
    public String content;

    @ColumnInfo(name = "create_time")
    public long createTime = System.currentTimeMillis();

    @ColumnInfo(name = "type")
    public String type;
}
