/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: TodoQueryHelper.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/7/13
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.nearme.note.db

import android.annotation.SuppressLint
import android.content.ContentProviderOperation
import android.content.ContentProviderResult
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.OperationApplicationException
import android.content.pm.PackageManager
import android.database.Cursor
import android.database.MatrixCursor
import android.database.SQLException
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.RemoteException
import android.text.SpannableStringBuilder
import android.text.TextUtils
import androidx.core.net.toUri
import androidx.sqlite.db.SupportSQLiteQueryBuilder
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.appwidget.todowidget.ToDoWidgetProvider
import com.nearme.note.appwidget.todowidget.TodoOfflineInfoActivity
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.ToDoRepository
import com.nearme.note.model.toRichNoteWithAttachments
import com.nearme.note.remind.RepeatDataHelper
import com.nearme.note.remind.RepeatManage
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DeviceInfoUtils
import com.nearme.note.util.FileUtil
import com.nearme.note.util.StatisticsUtils
import com.oplus.cloud.utils.PrefUtils
import com.oplus.note.R
import com.oplus.note.common.PkgConstants
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.DateConverters
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.todo.ToDoExtra
import com.oplus.note.repo.todo.entity.StatusEnumConverters
import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.repo.todo.entity.TodoTableColumns
import com.oplus.note.utils.NoteStatusProviderUtil
import com.oplus.note.utils.generateNumericCode
import com.oplus.note.utils.isPackageInstall
import com.oplus.richtext.core.parser.HtmlFormats
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import org.jsoup.internal.StringUtil
import java.time.ZoneId
import java.util.Date
import java.util.UUID

object TodoProviderHelper {
    private const val TAG = "TodoProviderHelper"

    const val CALENDAR_TODO_AUTHORITY: String = "com.oplus.task"
    const val CALENDAR_TABLE_TASKS: String = "tasks"
    val CALENDAR_CONTENT_URI: Uri =
        "content://$CALENDAR_TODO_AUTHORITY/$CALENDAR_TABLE_TASKS".toUri()

    const val METHOD_TODO_TRANSFER = "todo_transfer"
    const val PACKAGE_CALENDAR_DOMESTIC = "com.coloros.calendar" // 日历内销
    const val PACKAGE_CALENDAR_EXPORT = "com.oplus.calendar" // 日历外销
    const val TODO_CALENDAR_LINK_STR = "calendar/open-todo"
    const val TODO_TRANSFER_CALENDAR_LINK = "nativeapp://$TODO_CALENDAR_LINK_STR"
    const val TODO_TRANSFER_RESULT_CODE = "result_code"
    const val TODO_TRANSFER_NOT_SUPPORT_CODE = -2 // 当前不支持待办下线（如在OS16以下）
    const val TODO_TRANSFERRED_CODE = -1 // 已经进行过待办迁移
    const val TODO_TRANSFERRING_CODE = 0 // 已经在进行待办迁移
    const val TODO_TRANSFER_CODE = 1 // 正常进行待办迁移
    const val BATCH_LIMIT_COUNT = 50
    const val COUNT_LIMIT: Int = 100

    @Volatile
    private var isTodoTransferring = false // 是否正在进行待办迁移
    private var todoTransferFailedCount = 0 // 执行插入失败数据

    private val AVAILABLE_COLUMNS = arrayOf(
        TodoTableColumns.LOCAL_ID,
        TodoTableColumns.CONTENT,
        TodoTableColumns.ALARM_TIME,
        TodoTableColumns.FINISH_TIME
    )

    // 日历待办provider
    private const val META_DATA_CALENDAR_TODO_NAME: String = "isSupportTask"
    const val PACKAGE_NAME_CALENDAR_PROVIDER = "com.android.providers.calendar"

    // 日历待办列表页action
    const val HOME_PAGE = "oplus.intent.calendar.HOME_PAGE"
    const val PAGE_TYPE = "page_type"
    const val PAGE_TYPE_VALUE = 4

    // 日历待办表字段名
    private const val CALENDAR_TODO_LOCAL_ID = "local_id"
    private const val CALENDAR_TODO_PARENT_ID = "original_id"
    private const val CALENDAR_TODO_GLOBAL_ID = "_sync_id"
    private const val CALENDAR_TODO_CONTENT = "content"
    private const val CALENDAR_TODO_ALARM_TIME = "dtstart"
    private const val CALENDAR_TODO_ALL_DAY = "allDay"
    private const val CALENDAR_TODO_REMINDERS = "reminders"
    private const val CALENDAR_TODO_TIMEZONE = "timezone"
    private const val CALENDAR_TODO_CREATE_TIME = "create_time"
    private const val CALENDAR_TODO_UPDATE_TIME = "update_time"
    private const val CALENDAR_TODO_FINISH_TIME = "finish_time"
    private const val CALENDAR_TODO_STATUS = "status"
    private const val CALENDAR_TODO_IS_DELETED = "deleted"
    private const val CALENDAR_TODO_FORCE_REMINDER = "force_reminder"
    private const val CALENDAR_TODO_REPEAT_RULE = "rrule"
    private const val CALENDAR_TODO_SORT_TIME = "sort_time"
    private const val CALENDAR_TODO_FROM_PACKAGE = "mutators"
    private const val CALENDAR_TODO_SYS_VERSION = "sys_version"
    private const val CALENDAR_TODO_COLOR_INDEX = "color"

    private const val CALENDAR_TODO_TIMEZONE_DEFAULT = "UTC"

    private const val CALENDAR_REMINDERS_MINUTES = "minutes"
    private const val CALENDAR_REMINDERS_METHOD = "method"

    fun handleableUri(uri: Uri): Boolean {
        return NotesProvider.matchUrl(uri, NotesProvider.TABLE_NAME_TODO)
    }

    @SuppressLint("Range", "getLastPathSegmentRisk")
    fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {
        if (ConfigUtils.isToDoDeprecated) {
            AppLogger.BASIC.i(TAG, "query error: Todo is deprecated.")
            return null
        }
        return AppDatabase.getInstance().openHelper.readableDatabase.let { db ->
            var cursor: Cursor? = null
            try {
                val table = uri.lastPathSegment
                if (table.isNullOrEmpty()) {
                    return@let null
                }

                val builder = SupportSQLiteQueryBuilder.builder(table)
                val resultColumns: Array<String>
                if (projection != null) {
                    //查询时需要 projection 中的 repeat_rule 改成 extra；但返回数据时，还是得返回 repeat_rule
                    val newProjection = replaceTarget(projection, TodoTableColumns.REPEAT_RULE, TodoTableColumns.EXTRA)
                    val queryColumns = newProjection.filter { it in AVAILABLE_COLUMNS.plus(TodoTableColumns.EXTRA) }.toTypedArray()
                    builder.columns(queryColumns)
                    resultColumns = replaceTarget(queryColumns, TodoTableColumns.EXTRA, TodoTableColumns.REPEAT_RULE)
                } else {
                    resultColumns = AVAILABLE_COLUMNS
                    builder.columns(AVAILABLE_COLUMNS)
                }

                val where = "${TodoTableColumns.IS_DELETE} != 1"
                val sel =
                    if (!selection.isNullOrEmpty()) "$selection and $where" else where
                builder.selection(sel, selectionArgs)

                if (sortOrder != null) {
                    builder.orderBy(sortOrder)
                }
                val sql = builder.create()

                AppLogger.BASIC.d(TAG, "query: ${sql.sql}")
                cursor = db.query(sql)
                //将 cursor 中数据解析出来，填充到 matrixCursor 中
                val matrixCursor = MatrixCursor(resultColumns)
                if (cursor != null && cursor.count > 0) {
                    while (cursor.moveToNext()) {
                        val rowData = arrayOfNulls<Any>(resultColumns.size)
                        for (index in resultColumns.indices) {
                            if (TextUtils.equals(resultColumns[index], TodoTableColumns.REPEAT_RULE)) {
                                val extraInfo = cursor.getString(cursor.getColumnIndex(TodoTableColumns.EXTRA))
                                if (TextUtils.isEmpty(extraInfo) || "null" == extraInfo) {
                                    rowData[index] = ""
                                } else {
                                    val repeatRule =
                                        Gson().fromJson<ToDoExtra>(
                                            extraInfo,
                                            object : TypeToken<ToDoExtra>() {}.type
                                        ).repeatRule
                                    rowData[index] = repeatRule
                                }
                            } else {
                                rowData[index] = cursor.getString(cursor.getColumnIndex(resultColumns[index]))
                            }
                        }
                        matrixCursor.addRow(rowData)
                    }
                }
                matrixCursor
            } catch (e: Exception) {
                AppLogger.BASIC.e(TAG, "${e.message}")
                null
            } finally {
                cursor?.close()
            }
        }
    }

    @SuppressLint("Range", "Recycle")
    fun queryCalendarTodoTask(context: Context): MutableList<ToDo> {
        val resolver = context.contentResolver
        val taskProj = arrayOf(
            CALENDAR_TODO_LOCAL_ID, CALENDAR_TODO_PARENT_ID, CALENDAR_TODO_GLOBAL_ID,
            CALENDAR_TODO_CONTENT, CALENDAR_TODO_ALARM_TIME, CALENDAR_TODO_CREATE_TIME,
            CALENDAR_TODO_UPDATE_TIME, CALENDAR_TODO_FINISH_TIME, CALENDAR_TODO_STATUS,
            CALENDAR_TODO_IS_DELETED, CALENDAR_TODO_FORCE_REMINDER, CALENDAR_TODO_REPEAT_RULE,
            CALENDAR_TODO_SORT_TIME, CALENDAR_TODO_FROM_PACKAGE, CALENDAR_TODO_SYS_VERSION,
            CALENDAR_TODO_COLOR_INDEX
        )
        var cursor: Cursor? = null
        val list: MutableList<ToDo> = mutableListOf()
        kotlin.runCatching {
            cursor = resolver.query(CALENDAR_CONTENT_URI, taskProj, null, null, null)?.apply {
                if (count > 0) {
                    while (moveToNext()) {
                        val todo = ToDo()
                        todo.localId = UUID.fromString(getString(getColumnIndex(CALENDAR_TODO_LOCAL_ID)))
                        todo.parentId = getString(getColumnIndex(CALENDAR_TODO_PARENT_ID))
                        todo.globalId = UUID.fromString(getString(getColumnIndex(CALENDAR_TODO_GLOBAL_ID)))
                        todo.content = getString(getColumnIndex(CALENDAR_TODO_CONTENT))
                        todo.alarmTime = DateConverters.timestampToDate(
                            getInt(getColumnIndex(CALENDAR_TODO_ALARM_TIME)).toLong()
                        )
                        todo.createTime = DateConverters.timestampToDate(
                            getInt(getColumnIndex(CALENDAR_TODO_CREATE_TIME)).toLong()
                        )
                        todo.updateTime = DateConverters.timestampToDate(
                            getInt(getColumnIndex(CALENDAR_TODO_UPDATE_TIME)).toLong()
                        )
                        todo.finishTime = DateConverters.timestampToDate(
                            getInt(getColumnIndex(CALENDAR_TODO_FINISH_TIME)).toLong()
                        )
                        todo.status = StatusEnumConverters.intToEnum(
                            getInt(getColumnIndex(CALENDAR_TODO_STATUS))
                        )
                        todo.setIsDelete(getInt(getColumnIndex(CALENDAR_TODO_IS_DELETED)) == 1)
                        todo.forceReminder = getInt(getColumnIndex(CALENDAR_TODO_FORCE_REMINDER)) == 1
                        todo.repeatRule = getString(getColumnIndex(CALENDAR_TODO_REPEAT_RULE))
                        todo.sortTime = getInt(getColumnIndex(CALENDAR_TODO_SORT_TIME)).toLong()
                        val todoExtra = ToDoExtra()
                        todoExtra.forceReminder = todo.forceReminder
                        todoExtra.repeatRule = todo.repeatRule
                        todoExtra.sortTime = todo.sortTime
                        todo.extra = todoExtra
                        todo.fromPackage = getString(getColumnIndex(CALENDAR_TODO_FROM_PACKAGE))
                        todo.sysVersion = getInt(getColumnIndex(CALENDAR_TODO_SYS_VERSION)).toLong()
                        todo.colorIndex = getInt(getColumnIndex(CALENDAR_TODO_COLOR_INDEX))
                        list.add(todo)
                    }
                }
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "${it.message}")
        }
        cursor?.close()
        return list
    }

    /** 将 array 中的 target 替换成 to 并返回新的 array */
    private fun replaceTarget(projection: Array<String>, target: String, to: String): Array<String> {
        val tempArray = projection.clone()
        val index = tempArray.indexOf(target)
        if (index > -1) {
            tempArray[index] = to
        }
        return tempArray
    }

    fun update(
        uri: Uri,
        values: ContentValues?,
        callingPackage: String? = null
    ): Int {
        try {
            //必要参数，外部必须提供
            val packageName = uri.getQueryParameter("caller_package")
            if (NoteProviderHelper.isInValidPackageName(packageName, callingPackage)) {
                AppLogger.BASIC.d(TAG, "query invalid package $packageName")
                return 0
            }

            val localId = uri.getQueryParameter(TodoTableColumns.LOCAL_ID)
            val todo = AppDatabase.getInstance().toDoDao().getByLocalIdSync(localId)
            if (todo == null) {
                AppLogger.BASIC.d(TAG, "no such todo data")
                return 0
            }

            //检查参数
            var finishTime: Date? = null
            if (values?.containsKey(TodoTableColumns.CONTENT) == true) {
                val content = values.get(TodoTableColumns.CONTENT).toString().trim()
                if (TextUtils.isEmpty(content)) {
                    AppLogger.BASIC.d(TAG, "update todo param invalid")
                    return 0
                } else {
                    todo.content = content
                }
            }
            if (values?.containsKey(TodoTableColumns.FINISH_TIME) == true) {
                val time = values.getAsString(TodoTableColumns.FINISH_TIME)
                if (!StringUtil.isNumeric(time)) {
                    AppLogger.BASIC.d(TAG, "update todo param invalid")
                    return 0
                } else {
                    finishTime = Date(time.toLong())
                }
            }
            AppLogger.BASIC.d(TAG, "update todo $todo")
            return if (finishTime == null) {
                AppDatabase.getInstance().toDoDao().update(todo)
            } else {
                updateTodo(todo, finishTime)
            }
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "${e.message}")
        }
        return 0
    }

    private fun updateTodo(todo: ToDo, finishTime: Date): Int {
        var result = 0
        val repository = ToDoRepository.getInstance()
        if (RepeatDataHelper.isRepeat(todo)) {
            val nextTime = RepeatManage.nextAlarmTimeByRepeat(RepeatDataHelper.getRepeatData(todo), todo.alarmTime.time)
            if (todo.hasSyncedToCloud()) {
                todo.status = ToDo.StatusEnum.MODIFIED
            }
            if (nextTime > 0 || todo.reminded) {
                result = repository.syncUpdateFinishTime(todo, nextTime)
            } else {
                todo.finishTime = finishTime
                result = repository.syncUpdate(todo)
            }
        } else {
            if (todo.hasSyncedToCloud()) {
                todo.status = ToDo.StatusEnum.MODIFIED
            }
            todo.finishTime = finishTime
            result = repository.syncUpdate(todo)
        }
        return result
    }

    fun deleteToDoByLocalId(context: Context, localIds: Array<String>?): Int {
        var result = 0
        try {
            if (localIds.isNullOrEmpty()) {
                return result
            }
            val len = localIds.size
            if (len <= 0) {
                return result
            }
            val list = localIds.toList()
            var subList: List<String>?
            val times = len / COUNT_LIMIT
            val mod = len % COUNT_LIMIT
            var index = 0
            var count = 0
            if (len >= COUNT_LIMIT) {
                for (i in 0 until times) {
                    subList = list.subList(index, COUNT_LIMIT * (i + 1))
                    index = index + COUNT_LIMIT
                    count = AppDatabase.getInstance().toDoDao().deleteByLocalIds(subList)
                    result = result + count
                }
            }
            subList = list.subList(index, index + mod)
            result = result + AppDatabase.getInstance().toDoDao().deleteByLocalIds(subList)
            if (result > 0) {
                NotesProviderPresenter.sendNotify(context, NotesProvider.DATA_CHANGE_URI)
                NotesProviderPresenter.sendNotify(context, NotesProvider.TODO_DATA_CHANGE_URI)
                NotesProviderPresenter.sendNotify(context, NotesProvider.TODO_DATA_CHANGE_URI_NEW)
                NoteCardWidgetProvider.instance.postUIToCard(false)
            }
        } catch (e: SQLException) {
            AppLogger.BASIC.e(NotesProviderPresenter.TAG, "deleteToDoByLocalId " + e.message)
        }
        return result
    }

    fun call(
        context: Context,
        method: String,
        arg: String?,
        extras: Bundle?,
        callingPackage: String?
    ): Bundle? {
        return when (method) {
            METHOD_TODO_TRANSFER -> {
                if (callingPackage == PACKAGE_CALENDAR_DOMESTIC
                    || callingPackage == PACKAGE_CALENDAR_EXPORT
                    || callingPackage == context.packageName
                ) {
                    val isNoteCreated = PrefUtils.getBoolean(
                        context,
                        PrefUtils.TODO_NOTE_CREATED_KEY, false
                    )
                    val randomId = generateNumericCode()
                    AppLogger.THIRDLOG.d(TAG, "[$randomId]54010101, todoNoteCreated: $isNoteCreated")
                    // os16+会创建待办笔记
                    if (!isNoteCreated && ConfigUtils.isToDoDeprecated) {
                        // 创建待办笔记
                        todoTransferNote(context, randomId)
                    }
                    todoTransfer(context, callingPackage, randomId)
                } else null
            }

            else -> null
        }
    }

    /**
     * 待办数据迁移
     */
    private fun todoTransfer(context: Context, callingPackage: String?, randomId: String): Bundle {
        val todoTransferState = PrefUtils.getBoolean(
            context,
            PrefUtils.TODO_TRANSFER_STATE_KEY, false
        )
        val isNoteCreated = PrefUtils.getBoolean(
            context,
            PrefUtils.TODO_NOTE_CREATED_KEY, false
        )
        val resultCode = if (isTodoTransferring) { // 正在进行待办迁移
            TODO_TRANSFERRING_CODE
        } else if (todoTransferState) { // 已经进行过待办迁移
            TODO_TRANSFERRED_CODE
        } else if (!ConfigUtils.isToDoDeprecated) { // 不支持待办下线
            TODO_TRANSFER_NOT_SUPPORT_CODE
        } else {
            TODO_TRANSFER_CODE
        }
        AppLogger.THIRDLOG.d(TAG, "[$randomId]54010201, launchFrom: $callingPackage, currentStatus: $resultCode")
        AppLogger.THIRDLOG.d(
            TAG,
            "[$randomId]54010201, calendarVersion: ${DeviceInfoUtils.getPkgVersionCode(context, PkgConstants.PACKAGE_CALENDAR)}"
        )
        AppLogger.THIRDLOG.d(
            TAG,
            "[$randomId]54010201, calendarStorageVersion: ${DeviceInfoUtils.getPkgVersionCode(context, PACKAGE_NAME_CALENDAR_PROVIDER)}"
        )
        // 迁移待办数据
        if (callingPackage == context.packageName && isNoteCreated && resultCode == TODO_TRANSFER_CODE) {
            // 查询待办数据
            val allTodos = AppDatabase.getInstance().toDoDao().allData
            if (allTodos.isNotEmpty()) {
                AppLogger.THIRDLOG.d(TAG, "[$randomId]54010201, todoCount: ${allTodos.size}")
                isTodoTransferring = true
                val startTime = System.currentTimeMillis()
                AppLogger.BASIC.d(TAG, "todoTransfer startTime: $startTime")
                // 批量插入数据
                batchInsertTodos(context, allTodos, false, randomId)
                isTodoTransferring = false
            }
        }
        return Bundle().apply {
            putInt(TODO_TRANSFER_RESULT_CODE, resultCode)
        }
    }

    fun batchInsertTodos(context: Context, allTodos: List<ToDo>, isMover: Boolean, randomId: String = "") {
        val operationsBuffer = ArrayList<ContentProviderOperation>()
        val localIds = ArrayList<String>()
        var operationsCount = 0
        allTodos.forEach {
            val values = ContentValues().apply {
                put(CALENDAR_TODO_LOCAL_ID, it.localId.toString())
                put(CALENDAR_TODO_GLOBAL_ID, it.globalId.toString())
                put(CALENDAR_TODO_CONTENT, it.content)
                put(CALENDAR_TODO_ALARM_TIME, it.alarmTime?.time) //dtstart没值传null
                put(CALENDAR_TODO_ALL_DAY, if (it.alarmTime?.time == null) 1 else 0) //没时间就是全天待办
                val defaultZone = ZoneId.systemDefault().id
                put(CALENDAR_TODO_TIMEZONE, if (it.alarmTime?.time == null) CALENDAR_TODO_TIMEZONE_DEFAULT else defaultZone) // 无日期时区传UTC，否则传系统默认
                put(CALENDAR_TODO_CREATE_TIME, it.createTime?.time ?: 0)
                put(CALENDAR_TODO_UPDATE_TIME, it.updateTime?.time ?: 0)
                put(CALENDAR_TODO_FINISH_TIME, it.finishTime?.time ?: 0)
                put(CALENDAR_TODO_STATUS, it.status?.ordinal)
                put(CALENDAR_TODO_IS_DELETED, if (it.isDelete == true) 1 else 0)
                put(CALENDAR_TODO_FORCE_REMINDER, if (it.extra?.forceReminder == true) 1 else 0)
                put(CALENDAR_TODO_REPEAT_RULE, it.extra?.repeatRule ?: "")
                put(CALENDAR_TODO_SORT_TIME, it.extra?.sortTime ?: 0)
                put(CALENDAR_TODO_FROM_PACKAGE, it.fromPackage ?: "")
                put(CALENDAR_TODO_SYS_VERSION, it.sysVersion.toString())
                put(CALENDAR_TODO_COLOR_INDEX, it.colorIndex)
            }
            localIds.add(it.localId.toString())
            if (it.alarmTime?.time == null) {
                ContentProviderOperation.newInsert(CALENDAR_CONTENT_URI)
                    .withValues(values)
                    .build().apply { operationsBuffer.add(this) }
            } else {
                val contentValues = ContentValues().apply {
                    put(CALENDAR_REMINDERS_MINUTES, 0)
                    put(CALENDAR_REMINDERS_METHOD, 1)
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    ContentProviderOperation.newInsert(CALENDAR_CONTENT_URI)
                        .withValues(values)
                        .withExtra(CALENDAR_TODO_REMINDERS, arrayListOf(contentValues))
                        .build().apply { operationsBuffer.add(this) }
                }
            }
            // 每满50个操作就批量插入一次，避免数据量太大，一次性插入失败
            if (operationsBuffer.size == BATCH_LIMIT_COUNT) {
                try {
                    val results = context.contentResolver.applyBatch(
                        CALENDAR_TODO_AUTHORITY,
                        operationsBuffer
                    )
                    operationsCount++
                    val isFinish = allTodos.size % BATCH_LIMIT_COUNT == 0
                            && operationsCount == allTodos.size / BATCH_LIMIT_COUNT
                    dealResult(context, results, operationsBuffer, localIds, isFinish, randomId, isMover)
                } catch (e: OperationApplicationException) {
                    AppLogger.THIRDLOG.d(TAG, "[$randomId]54020101, BatchInsert 50 todos operationApp error: ${e.message}")
                } catch (e: RemoteException) {
                    AppLogger.THIRDLOG.d(TAG, "[$randomId]54020101, BatchInsert 50 todos remote error: ${e.message}")
                }
            }
        }
        if (operationsBuffer.size > 0) {
            try {
                val results =
                    context.contentResolver.applyBatch(CALENDAR_TODO_AUTHORITY, operationsBuffer)
                dealResult(context, results, operationsBuffer, localIds, true, randomId, isMover)
            } catch (e: OperationApplicationException) {
                AppLogger.THIRDLOG.d(TAG, "[$randomId]54020101, BatchInsert less than 50 todos operationApp error: ${e.message}")
            } catch (e: RemoteException) {
                AppLogger.THIRDLOG.d(TAG, "[$randomId]54020101, BatchInsert less than 50 todos remote error: ${e.message}")
            }
        }
    }

    private fun dealResult(
        context: Context,
        results: Array<ContentProviderResult>?,
        operations: ArrayList<ContentProviderOperation>,
        localIds: ArrayList<String>,
        isFinish: Boolean = false,
        randomId: String,
        isMover: Boolean
    ) {
        AppLogger.THIRDLOG.d(
            TAG,
            "[$randomId]54020102, transferTodoCount: ${operations.size}, transferSuccessTodoCount: ${results?.size}"
        )
        if (results != null) {
            if (!isMover) {
                // 删除成功的数据
                val delCount = AppDatabase.getInstance().toDoDao().deleteByLocalIds(localIds)
                AppLogger.THIRDLOG.d(
                    TAG,
                    "[$randomId]54020102, BatchInsert success, localDelCount: ${localIds.size}, localDelSuccessCount: $delCount"
                )
            }
            operations.clear()
            localIds.clear()
        } else {
            todoTransferFailedCount += localIds.size
            localIds.clear()
        }
        // 如果是搬家则更新迁移状态
        if (isFinish && !isMover) {
            val endTime = System.currentTimeMillis()
            AppLogger.BASIC.d(TAG, "todoTransfer endTime: $endTime")
            val allTodos = AppDatabase.getInstance().toDoDao().allData
            // 所有数据都迁移成功则更新迁移标记
            if (allTodos.isEmpty()) {
                // 更新迁移状态
                PrefUtils.putBoolean(
                    context,
                    PrefUtils.TODO_TRANSFER_STATE_KEY, true
                )
                NoteStatusProviderUtil.setStatus(
                    context,
                    NoteStatusProviderUtil.FLAG_TODO_TRANSFER_STATUS,
                    true
                )
            }
            StatisticsUtils.setTodoTransferFail(context, allTodos.size, todoTransferFailedCount)
            AppLogger.THIRDLOG.d(TAG, "[$randomId]54030101, remainTodoCount: ${allTodos.size}, todoTransferFailedCount: $todoTransferFailedCount")
            todoTransferFailedCount = 0
        }
    }

    fun todoTransferNote(context: Context, randomId: String) {
        var allTodos = AppDatabase.getInstance().toDoDao().allData
        AppLogger.BASIC.d(TAG, "todoTransferNote allTodos: ${allTodos.isEmpty()}")
        if (allTodos.isEmpty()) {
            PrefUtils.putBoolean(context, PrefUtils.TODO_NOTE_TIPS_SHOW_KEY, true)
            return
        }
        val (uncheckTodos, checkTodos) = allTodos.partition { it.isComplete == false }
        val todoNoteIds = HashSet<String>()
        var currentContent = SpannableStringBuilder()
        var newTodoTipsText = context.getResources().getString(com.oplus.note.baseres.R.string.todo_new_experience_tips)
        val startPos = 0
        val pos = newTodoTipsText.lastIndexOf("，") + 1
        val newTodoTipsLinkText = newTodoTipsText.substring(pos)
        newTodoTipsText = newTodoTipsText.substring(startPos, pos)
        val todoUndo = context.getResources().getString(com.oplus.note.baseres.R.string.todo_undo_group)
        val todoNoteTitle = context.getResources().getString(com.oplus.note.baseres.R.string.todo_list_title_text)
        val todoDone = context.getResources().getString(com.oplus.note.baseres.R.string.todo_done_group)
        var fixedContentLen = newTodoTipsText.length + newTodoTipsLinkText.length + todoNoteTitle.length
        if (uncheckTodos.size > 0) {
            fixedContentLen += todoUndo.length
        }
        if (checkTodos.size > 0) {
            fixedContentLen += todoDone.length
        }
        val isExceed = isExceedContentLimit(fixedContentLen, allTodos)
        if (isExceed) {
            allTodos = uncheckTodos.sortedBy { it.createTime }.plus(checkTodos.sortedBy { it.createTime })
        } else {
            allTodos = uncheckTodos.sortedByDescending { it.createTime }.plus(checkTodos.sortedByDescending { it.createTime })
        }
        var contentLen = fixedContentLen
        var noteIndex = 0
        var guid = UUID.randomUUID().toString()
        val webItems: MutableList<Data> = mutableListOf()
        allTodos.forEachIndexed { index, todo ->
            val isChecked = todo.isComplete
            val liClass = if (isChecked) HtmlFormats.START_LI_CHECKED else HtmlFormats.START_LI_UNCHECK
            val todoContent = "$liClass${todo.content}${HtmlFormats.BR_TAG}${HtmlFormats.END_LI}"
            contentLen += todo.content.length
            if (index == 0) {
                currentContent = appendNoteTopContent(context, isChecked, guid, webItems)
            }
            if (contentLen > RichNoteSplitHelper.CONTENT_LIMIT) {
                AppLogger.BASIC.d(TAG, "todoTransferNote contentLen: $contentLen")
                currentContent.append("${HtmlFormats.END_UL}${HtmlFormats.END_DIV}")
                todoNoteIds.add(guid)
                val noteTitle = if (noteIndex == 0) todoNoteTitle else "$todoNoteTitle（$noteIndex）"
                createNote(
                    context,
                    currentContent.toString(),
                    noteTitle,
                    guid,
                    todoNoteIds,
                    webItems = webItems,
                    todoCount = allTodos.size,
                    randomId = randomId
                )
                // create new next note
                guid = UUID.randomUUID().toString()
                noteIndex++
                contentLen = todo.content.length + fixedContentLen + noteIndex.toString().length + 2
                currentContent = appendNoteTopContent(context, isChecked, guid, webItems)
                currentContent.append(todoContent)
            } else {
                if (!isChecked) {
                    currentContent.append(todoContent)
                } else {
                    if (uncheckTodos.size > 0 && index == uncheckTodos.size) {
                        currentContent.append(HtmlFormats.END_UL)
                        currentContent.append(HtmlFormats.DIV_HR_SOLID)
                        currentContent.append(
                            "${HtmlFormats.START_SPAN_TEXT_SIZE}${HtmlFormats.START_SPAN_TEXT_BOLD}$todoDone" +
                                    "${HtmlFormats.END_SPAN}${HtmlFormats.END_SPAN}${HtmlFormats.BR_TAG}"
                        )
                        currentContent.append(HtmlFormats.START_UL)
                    }
                    currentContent.append(todoContent)
                }
            }
        }
        currentContent.append("${HtmlFormats.END_UL}${HtmlFormats.END_DIV}")
        todoNoteIds.add(guid)
        val noteTitle = if (noteIndex == 0) todoNoteTitle else "$todoNoteTitle（$noteIndex）"
        createNote(
            context,
            currentContent.toString(),
            noteTitle,
            guid,
            todoNoteIds,
            true,
            webItems = webItems,
            todoCount = allTodos.size,
            randomId = randomId
        )
    }

    /**
     * 是否超过单条笔记字数限制
     */
    private fun isExceedContentLimit(fixedContent: Int, todos: List<ToDo>): Boolean {
        var count = fixedContent
        todos.forEach {
            count += it?.content?.length ?: 0
        }
        return if (count > RichNoteSplitHelper.CONTENT_LIMIT) true else false
    }

    private fun appendNoteTopContent(context: Context, isChecked: Boolean, guid: String, webItems: MutableList<Data>): SpannableStringBuilder {
        var newTodoTipsText = context.resources.getString(com.oplus.note.baseres.R.string.todo_new_experience_tips)
        val startPos = 0
        val pos = newTodoTipsText.lastIndexOf("，") + 1
        val newTodoTipsLinkText = newTodoTipsText.substring(pos)
        newTodoTipsText = newTodoTipsText.substring(startPos, pos)
        val todoUndo = context.resources.getString(com.oplus.note.baseres.R.string.todo_undo_group)
        val todoDone = context.resources.getString(com.oplus.note.baseres.R.string.todo_done_group)
        val attachment = createAndSaveAttachment(context, guid)
        webItems.clear()
        val data = Data(type = Data.TYPE_ATTACHMENT, attachment = attachment)
        webItems.add(data)
        val currentContent = SpannableStringBuilder(HtmlFormats.START_DIV)
        val imgHtml = "<img src=\"${attachment.attachmentId}\" width=\"${attachment.picture?.width}\" height=\"${attachment.picture?.height}\"/>"
        currentContent.append(imgHtml)
        currentContent.append(
            "${HtmlFormats.START_DIV}$newTodoTipsText<a href=$TODO_TRANSFER_CALENDAR_LINK>$newTodoTipsLinkText</a>" +
                    "${HtmlFormats.BR_TAG}${HtmlFormats.END_DIV}"
        )

        val isTodoDone = if (!isChecked) todoUndo else todoDone
        currentContent.append(HtmlFormats.DIV_HR_SOLID)
        currentContent.append(
            "${HtmlFormats.START_SPAN_TEXT_SIZE}${HtmlFormats.START_SPAN_TEXT_BOLD}$isTodoDone" +
                    "${HtmlFormats.END_SPAN}${HtmlFormats.END_SPAN}${HtmlFormats.BR_TAG}"
        )
        currentContent.append(HtmlFormats.START_UL)
        return currentContent
    }

    fun createNote(
        context: Context,
        content: String,
        title: String,
        guid: String,
        todoNoteIds: HashSet<String>,
        isLatestNote: Boolean = false,
        webItems: MutableList<Data>,
        todoCount: Int,
        randomId: String
    ) {
        val curTime = System.currentTimeMillis()
        val richData = RichData(
            metadata = RichNote(
                localId = guid,
                createTime = curTime,
                updateTime = curTime,
                text = context.getResources().getString(com.oplus.note.baseres.R.string.todo_new_experience_tips),
                topTime = System.currentTimeMillis(),
                htmlText = content,
                title = title
            ),
            title = Data(type = Data.TYPE_TEXT, SpannableStringBuilder(title)),
            items = mutableListOf(),
            webItems = webItems,
            coverPictureAttachment = null
        )
        richData.metadata.rawText =
            HtmlTransformManagerFactory.gainHtmlTransformManager().toRawText(content)
        val richNote = richData.toRichNoteWithAttachments()
        RichNoteRepository.insert(richNote)

        if (isLatestNote) {
            // 更新笔记创建标记
            PrefUtils.putBoolean(
                context,
                PrefUtils.TODO_NOTE_CREATED_KEY, true
            )
            AppLogger.THIRDLOG.d(TAG, "[$randomId]54010101, todoCount: $todoCount, todoNoteCount: ${todoNoteIds.size}")
            // 保存最新笔记id
            PrefUtils.putStringSet(
                context,
                PrefUtils.TODO_NOTE_ID_LIST_KEY, todoNoteIds
            )
            // 更新新用户引导弹窗弹出标记
            PrefUtils.putBoolean(
                context,
                PrefUtils.TODO_NOTE_TIPS_SHOW_KEY, false
            )
        }
    }

    private fun createAndSaveAttachment(context: Context, guid: String): Attachment {
        val resId: Int = R.drawable.todo_note_guide_bg
        val imageUri = (("android.resource://" + context.packageName) + "/" + resId).toUri()
        val picGuid = UUID.randomUUID().toString()
        val picAttachment = Attachment(attachmentId = picGuid, type = Attachment.TYPE_PICTURE, richNoteId = guid)
        FileUtil.copyFileFromUri(
            MyApplication.appContext,
            imageUri,
            picAttachment.absolutePath(MyApplication.appContext)
        )
        val bitmap = MediaUtils.getThumbBitmapFromUri(imageUri)
        bitmap?.let {
            picAttachment.picture = Picture(it.width, it.height)
        }
        return picAttachment
    }

    /**
     * 判断日历待办相关功能是否支持
     */
    fun isCalendarSupportTodo(context: Context): Boolean {
        try {
            val appInfo = context.packageManager.getApplicationInfo(
                PACKAGE_NAME_CALENDAR_PROVIDER,
                PackageManager.GET_META_DATA
            )
            val bundle = appInfo.metaData
            val isSupport = bundle != null && bundle.getBoolean(META_DATA_CALENDAR_TODO_NAME, false)
            return (isPackageInstall(context, PACKAGE_CALENDAR_DOMESTIC)
                    || isPackageInstall(context, PACKAGE_CALENDAR_EXPORT))
                    && isSupport
        } catch (e: PackageManager.NameNotFoundException) {
            AppLogger.BASIC.e(TAG, "isCalendarSupportTodo error: NameNotFoundException")
            return false
        }
    }

    /**
     * 判断日历待办TaskProvider是否存在
     */
    fun isTaskProviderExists(context: Context): Boolean {
        try {
            val isAvailable = context.packageManager.resolveContentProvider(CALENDAR_TODO_AUTHORITY, 0) != null
            return isAvailable
        } catch (e: PackageManager.NameNotFoundException) {
            AppLogger.BASIC.e(TAG, "isTaskProviderExists error: NameNotFoundException")
            return false
        }
    }

    /**
     * 尝试打开日历的待办，如果不能打开则显示待办下线公告页面
     */
    fun openCalendarTodoList(context: Context) {
        kotlin.runCatching {
            val intent = Intent(HOME_PAGE).apply {
                putExtra(PAGE_TYPE, PAGE_TYPE_VALUE)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "start openTodoOfflineDetail error=${it.message}")
            openTodoOfflineDetail(context)
        }
    }

    /**
     * 打开待办下线公告详情页
     */
    fun openTodoOfflineDetail(context: Context, isFromWidget: Boolean = true) {
        kotlin.runCatching {
            val intent = Intent(context, TodoOfflineInfoActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            intent.putExtra(ToDoWidgetProvider.KEY_IS_FROM_TODO_WIDGET, isFromWidget)
            context.startActivity(intent)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "openTodoOfflineDetail error=${it.message}")
        }
    }
}