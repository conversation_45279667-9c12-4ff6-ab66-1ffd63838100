/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          ConvertUtils.kt
 * * Description:   ConvertUtils
 * * Version:       1.0
 * * Date :         2024/5/8
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/5/8     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.nearme.note.util.createFile
import com.nearme.note.util.filesDirAbsolutePath
import com.nearme.note.view.NoteEditImageView.Companion.MAX_BITMAP_SIZE
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.UUID
import kotlin.math.ceil
import kotlin.math.sqrt

object ConvertUtils {
    private const val TAG = "ConvertUtils"
    private const val ARGB_8888_BIT = 4
    private const val MIME_TYPE_PNG = "image/png"
    private const val BITMAP_COMPRESS_QUALITY = 100

    private const val COLOR_GRAY = "color_gray"
    private const val COLOR_RED = "color_red"
    private const val COLOR_ORANGE = "color_orange"
    private const val COLOR_YELLOW = "color_yellow"
    private const val COLOR_GREEN = "color_green"
    private const val COLOR_BLUE = "color_blue"
    //文字颜色映射到oppo支持的几种颜色类型
    private val COLOR_TYPE_MAP = mapOf(
//        "" to COLOR_GRAY,
        "FA2A2D" to COLOR_RED, //华为/荣耀红色
        "FF3636" to COLOR_RED, //三星红色
        "FFCB30" to COLOR_ORANGE, //三星橙色
        "FFBF00" to COLOR_YELLOW, //华为/荣耀黄色
        "F7EB00" to COLOR_YELLOW, //三星黄色
        "47CC47" to COLOR_GREEN, //华为/荣耀绿色
        "2FEBD2" to COLOR_GREEN, //三星绿色
        "00AAEE" to COLOR_BLUE, //华为/荣耀浅蓝
        "3396FF" to COLOR_BLUE, //三星浅蓝
        "3F56EA" to COLOR_BLUE, //华为/荣耀深蓝
        "1447FF" to COLOR_BLUE, //三星深蓝
    )

    @JvmStatic
    fun handleText(text: String, noteData: NoteData, textItem: TextItem, handleData: (NoteData) -> Unit): NoteData {
        var data = noteData
        val length = text.length

        val part = (data.size + length) / NoteData.TEXT_MAX_SIZE
        val size = (data.size + length) % NoteData.TEXT_MAX_SIZE
        var start = 0
        var end = NoteData.TEXT_MAX_SIZE - data.size
        AppLogger.BASIC.d(TAG, "handleText start, ${data.getName()}, size=${data.size}, text length=$length, part=$part")
        // 如果文本超过了最大长度，分段处理
        for (i in 0 until part) {
            AppLogger.BASIC.d(TAG, "handleText, ${data.getName()}, size=${data.size}, i=$i, text remain=${length - end}")
            data.addItem(textItem.copy(text = text.substring(start, end)))
            handleData(data)
            data = data.new()

            start = end
            end += NoteData.TEXT_MAX_SIZE
        }
        if (start < length) {
            data.addItem(textItem.copy(text = text.substring(start)))
        }
        AppLogger.BASIC.d(TAG, "handleText end, ${data.getName()}, size=${data.size}, size=$size")
        return data
    }

    @JvmStatic
    fun saveImagePng(data: ByteArray, noteId: String): ImageItem? {
        return kotlin.runCatching {
            val imageId = UUID.randomUUID().toString()
            val absolutePath = "${filesDirAbsolutePath()}${File.separator}$noteId${File.separator}${imageId}_thumb.png"
            AppLogger.BASIC.d(TAG, "saveImagePng, noteId=$noteId, absolutePath=$absolutePath")
            val file = File(absolutePath).createFile() ?: return null

            val opts = BitmapFactory.Options()
            opts.inJustDecodeBounds = true // 只读信息
            BitmapFactory.decodeByteArray(data, 0, data.size, opts)
            val imageSize = opts.outWidth * opts.outHeight * ARGB_8888_BIT
            AppLogger.BASIC.d(TAG, "saveImagePng, outMimeType=${opts.outMimeType}, imageSize=$imageSize")
            if (MIME_TYPE_PNG == opts.outMimeType && imageSize <= MAX_BITMAP_SIZE) {
                file.writeBytes(data)
                return ImageItem(imageId, opts.outWidth, opts.outHeight)
            }
            AppLogger.BASIC.d(TAG, "saveImagePng, $imageId width=${opts.outWidth}, height=${opts.outHeight}, imageSize=$imageSize")

            if (imageSize > MAX_BITMAP_SIZE) {
                // 整数采样，实际大小平方缩小。优点一次性，缺点缩小尺寸偏大
                opts.inSampleSize = ceil(sqrt(imageSize.toDouble() / MAX_BITMAP_SIZE)).toInt()
            }
            opts.inJustDecodeBounds = false // 读图片数据
            val bitmap = BitmapFactory.decodeByteArray(data, 0, data.size, opts)
            val imageItem = ImageItem(imageId, bitmap.width, bitmap.height)
            AppLogger.BASIC.d(TAG, "saveImagePng, $imageId width=${bitmap.width}, height=${bitmap.height}, size=${bitmap.allocationByteCount}")
            GlobalScope.launch(Dispatchers.IO) {
                // compress 保存图片耗时，图片过多时会导致搬家过程2分钟无响应，引起超时异常
                bitmap.compress(Bitmap.CompressFormat.PNG, BITMAP_COMPRESS_QUALITY, file.outputStream())
                bitmap.recycle()
                AppLogger.BASIC.d(TAG, "saveImagePng, $imageId compress end")
            }
            return imageItem
        }.onFailure {
            AppLogger.BASIC.d(TAG, "saveImagePng, noteId=$noteId e:$it")
        }.getOrNull()
    }

    @JvmStatic
    fun convertTextColorToColorType(textColor: String?): String? {
        if (textColor.isNullOrEmpty()) {
            return null
        }
        val color = if (textColor.startsWith("#")) textColor.substring(1) else textColor
        return COLOR_TYPE_MAP[color.uppercase()]
    }
}