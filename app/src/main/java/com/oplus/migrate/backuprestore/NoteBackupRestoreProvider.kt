/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - NoteBackupRestoreProvider
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import androidx.sqlite.db.SupportSQLiteQueryBuilder
import com.nearme.note.db.AppDatabase
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.retailmode.RetailMode

class NoteBackupRestoreProvider : ContentProvider() {
    override fun onCreate(): Boolean {
        return true
    }

    override fun query(uri: Uri, projection: Array<String>?, selection: String?, selectionArgs: Array<String>?, sortOrder: String?): Cursor? {
        if (!NoteProviderAccessChecker.checkIfCanAccessProvider(context, callingPackage)) {
            AppLogger.RED_MIGRATION.w(TAG, "forbidden to get access to provider, no query result")
            return null
        }

        val db = AppDatabase.getInstance().openHelper.readableDatabase
        val supportSQLiteQueryBuilder = SupportSQLiteQueryBuilder.builder("rich_notes")
        supportSQLiteQueryBuilder.selection(selection, selectionArgs)
        supportSQLiteQueryBuilder.columns(projection)
        val c = db.query(supportSQLiteQueryBuilder.create())
        if (c != null ) {
            AppLogger.RED_MIGRATION.d(TAG, "query cursor count = " + c.count)
        } else {
            AppLogger.RED_MIGRATION.e(TAG, "query cursor null")
        }
        return c
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        return 0
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<String>?): Int {
        return 0
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        if (!NoteProviderAccessChecker.checkIfCanAccessProvider(context, callingPackage)) {
            AppLogger.RED_MIGRATION.w(TAG, "forbidden to get access to provider")
            return null
        }
        AppLogger.RED_MIGRATION.d(TAG, "call method = $method")
        when (method) {
            BackupRestoreConstant.METHOD_COPY_FOLDER -> {
                val src = extras!!.getString(BackupRestoreConstant.KEY_COPY_FOLDER_SRC)
                val des = extras.getString(BackupRestoreConstant.KEY_COPY_FOLDER_DES)
                return BackupRestoreUtil.copyFolder(src, des)
            }
            BackupRestoreConstant.METHOD_BACKUP_COUNT -> {
                return BackupRestoreUtil.backupCount
            }
            BackupRestoreConstant.METHOD_BACKUP -> {
                val backupFileName = extras!!.getString(BackupRestoreConstant.KEY_BACKUP_FILENAME)
                return backupFileName?.let { BackupRestoreUtil.backupNoteRecord(it) }
            }
            BackupRestoreConstant.METHOD_RESTORE_COUNT -> {
                val restoreFileName = extras!!.getString(BackupRestoreConstant.KEY_RESTORE_FILENAME)
                return restoreFileName?.let { BackupRestoreUtil.getRestoreCount(it) }
            }
            BackupRestoreConstant.METHOD_RESTORE -> {
                val restoreFileName = extras!!.getString(BackupRestoreConstant.KEY_RESTORE_FILENAME)
                return restoreFileName?.let { BackupRestoreUtil.restoreNoteRecord(it) }
            }
            BackupRestoreConstant.METHOD_BACKUP_SIZE -> {
                return BackupRestoreUtil.backupSize
            }
            BackupRestoreConstant.KEY_RETAIL_MODEL_INSERT -> {
                return RetailMode.retailModeInsert(context)
            }
            else -> return super.call(method, arg, extras)
        }
    }

    companion object {
        const val TAG = "NoteBackupRestoreProvider"
    }
}