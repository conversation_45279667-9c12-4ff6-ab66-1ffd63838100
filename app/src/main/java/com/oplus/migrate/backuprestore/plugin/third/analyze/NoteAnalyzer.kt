/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: NoteAnalyzer.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze

import com.nearme.note.model.RichNoteRepository
import com.nearme.note.skin.SkinData
import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.align
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.bold
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.br
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.check
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.div
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.highlight
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.img
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.italic
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.list
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.listEnd
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.strikeThrough
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.table
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.td
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.textColor
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.textSize
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.tr
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteStyle.underline
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

abstract class NoteAnalyzer : Analyzer {
    companion object {
        private const val TAG = "NoteAnalyzer"
        private const val TITLE_MAX_LENGTH = 50
    }

    protected open fun isNameLong(name: String): Boolean = name.length >= TITLE_MAX_LENGTH

    protected fun insertData(data: NoteData) {
        AppLogger.BASIC.d(TAG, "insertRichNote, ${data.getName()}, size=${data.size}")
        val textBuilder = StringBuilder()
        val rawTextBuilder = StringBuilder()
        val imgNameList = mutableListOf<ImageItem>()
        val listGroupBuilder = StringBuilder()
        val groupBuilder = StringBuilder()
        val itemBuilder = StringBuilder()
        val imageBuilder = StringBuilder()

        val endIndex = data.groups.lastIndex
        for ((i, group) in data.groups.withIndex()) {
            for (item in group.getItems()) {
                when (item) {
                    is TextItem -> {
                        // 处理文本
                        val text = item.text
                        textBuilder.append(text)

                        // 增加文本样式
                        if (text.isNotEmpty()) {
                            itemBuilder.append(text)
                                .textSize(item.fontLevel)
                                .bold(item.isBold)
                                .italic(item.isItalic)
                                .strikeThrough(item.isStrikeThrough)
                                .underline(item.underline)
                                .textColor(ConvertUtils.convertTextColorToColorType(item.textColor))
                                .highlight(item.textHighlight || item.isTextColorHighlight)
                                .td(item.isTd)
                                .tr(item.isTr)
                        }
                        groupBuilder.append(itemBuilder)
                        itemBuilder.clear()
                    }

                    is ImageItem -> {
                        // 处理图片
                        imgNameList.add(item)
                        itemBuilder.img(item.id, item.width.toString(), item.height.toString())
                        if (!group.isCheckBox) {
                            groupBuilder.append(itemBuilder)
                        } else {
                            imageBuilder.append(itemBuilder)
                        }
                        itemBuilder.clear()
                    }

                    is WrapItem -> {
                        textBuilder.append("\n")
                        groupBuilder.br()
                    }
                }
            }
            if (group.isList) {
                AppLogger.BASIC.d(TAG, "list, $i: group=${group.groupId}")
                groupBuilder.list(group.type)
                listGroupBuilder.append(groupBuilder)
                if (i == endIndex || group.groupId != data.groups[i + 1].groupId) {
                    // 最后一段 或者 下一段的列表状态与当前不同时，结束列表组
                    listGroupBuilder.listEnd(group.type)
                    rawTextBuilder.append(listGroupBuilder)
                    listGroupBuilder.clear()
                }
            } else if (group.isTable()) {
                AppLogger.BASIC.d(TAG, "table, $i: group=${group.groupId}")
                groupBuilder.table(group.type)
                rawTextBuilder.append(groupBuilder)
            } else {
                // 处理非列表段落
                groupBuilder.check(group.type, group.lvlText)
                groupBuilder.align(group.alignment)
                rawTextBuilder.append(groupBuilder)
                if (imageBuilder.isNotEmpty()) {
                    rawTextBuilder.append(imageBuilder)
                    imageBuilder.clear()
                }
                listGroupBuilder.clear()
            }
            groupBuilder.clear()
        }
        val title = if (data.isAddName) "" else data.getName()
        insertRichNote(data.id, title, textBuilder.toString(), rawTextBuilder.div().toString(), imgNameList)
    }

    protected fun insertRichNote(id: String, title: String, text: String, rawText: String, images: List<ImageItem>) {
        val rawTitle = if (title.isNotEmpty()) StringBuilder(title).div().toString() else ""
        val time = System.currentTimeMillis()
        AppLogger.BASIC.d(TAG, "insertRichNote, title=$title, text.length=${text.length}, imgNameList=$images")

        val richNote = RichNote(
            localId = id,
            createTime = time,
            timestamp = time,
            updateTime = time,
            title = title,
            rawTitle = rawTitle,
            text = text,
            rawText = rawText,
            state = RichNote.STATE_NEW,
            skinId = SkinData.COLOR_SKIN_WHITE
        )
        val attachments = images.map {
            Attachment(it.id, id, Attachment.TYPE_PICTURE).apply {
                picture = Picture(it.width, it.height)
            }
        }
        RichNoteRepository.insert(RichNoteWithAttachments(richNote, attachments))
        NoteSearchManagerWrapper.notifyDataChange()
    }
}