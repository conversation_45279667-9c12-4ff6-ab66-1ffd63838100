/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: AnalyzerFactory.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze

import android.util.Base64
import com.oplus.migrate.backuprestore.plugin.third.analyze.hr.HrNoteAnalyzer
import com.oplus.migrate.backuprestore.plugin.third.analyze.hw.HwNoteAnalyzer
import com.oplus.migrate.backuprestore.plugin.third.analyze.sam.SamNoteAnalyzer
import com.oplus.note.logger.AppLogger

object AnalyzerFactory {
    private const val TAG = "AnalyzerFactory"
    private val TYPE_HW = arrayOf("aHVhd2Vp", "cHRhYw==") // hua_wei
    private val TYPE_HR = arrayOf("aG9ub3I=") // honor
    private val TYPE_THREE_STAR = arrayOf("c2Ftc3VuZw==") // samsung

    fun createAnalyzer(type: String): Analyzer? {
        val brand = encode(type)
        AppLogger.BASIC.d(TAG, "createAnalyzer brand:$brand")
        return when {
            TYPE_HW.contains(brand) -> HwNoteAnalyzer()
            TYPE_HR.contains(brand) -> HrNoteAnalyzer()
            TYPE_THREE_STAR.contains(brand) -> SamNoteAnalyzer()
            else -> null
        }
    }

    private fun encode(brand: String): String {
        val bytes = Base64.encode(brand.lowercase().toByteArray(), Base64.DEFAULT)
        return String(bytes).trim()
    }
}