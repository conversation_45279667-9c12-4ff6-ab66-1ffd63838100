/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_CREATE
 * * File: IOSFileAttr.kt
 * * Description: OuterHtml
 * * Version: 1.0
 * * Date: 2024/02/17
 * * Author: wangxiong
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
import android.app.Application
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import java.security.MessageDigest

object SignatureChecker {

    private const val HIGH_HEX = 0xF0
    private const val LOW_HEX = 0x0F
    private const val SHR_NUM = 4
    private const val TAG = "SignatureChecker"

    fun getAppSignatures(application: Application, pkgName: String): String {
        val signatureList = mutableListOf<String>()

        try {
            val packageInfo = application.packageManager.getPackageInfo(
                pkgName,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    PackageManager.GET_SIGNING_CERTIFICATES
                } else {
                    PackageManager.GET_SIGNATURES
                }
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners?.forEach { signer ->
                    signatureList.add(toHexString(getSha256(signer.toByteArray())))
                }
            } else {
                packageInfo.signatures?.forEach { signature ->
                    signatureList.add(toHexString(getSha256(signature.toByteArray())))
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "Package not found: $pkgName", e)
        }
        return signatureList.joinToString("\n")
    }

    private fun getSha256(byteArray: ByteArray): ByteArray {
        return MessageDigest.getInstance("SHA-256").digest(byteArray)
    }

    private fun byteToHex(b: Byte): String {
        val hexChars = "0123456789ABCDEF"
        val high = (b.toInt() and HIGH_HEX) shr SHR_NUM
        val low = (b.toInt() and LOW_HEX)
        return "${hexChars[high]}${hexChars[low]}"
    }

    private fun toHexString(bytes: ByteArray): String {
        return bytes.joinToString(":") { byteToHex(it) }
    }
}