/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          SamNoteAnalyzer.kt
 * * Description:   SamNoteAnalyzer
 * * Version:       1.0
 * * Date :         2024/5/8
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/5/8     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.sam

import android.content.Context
import com.nearme.note.util.subName
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteAnalyzer
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteData
import com.oplus.migrate.backuprestore.plugin.third.analyze.hr.HtmlConverter
import com.oplus.note.logger.AppLogger
import java.io.InputStream

class SamNoteAnalyzer : NoteAnalyzer() {
    companion object {
        private const val TAG = "SamNoteAnalyzer"
        private const val TITLE_DEFAULT = "Notes_"
    }

    private val converter by lazy { HtmlConverter() }

    override fun analyze(context: Context, inputStream: InputStream, fileName: String) {
        AppLogger.BASIC.d(TAG, "analyze, $fileName start")
        val name = if (fileName.startsWith(TITLE_DEFAULT)) "" else fileName.subName()
        val noteData = NoteData(name = name, isAddName = isNameLong(name)).init()
        kotlin.runCatching {
            converter.convertToData(inputStream, noteData) {
                insertData(it)
                converter.statisticUnSupportFeature(context, "samsung")
            }
        }.onFailure {
            AppLogger.BASIC.d(TAG, "analyze $fileName error. $it")
        }
        AppLogger.BASIC.d(TAG, "analyze, $fileName end")
    }
}