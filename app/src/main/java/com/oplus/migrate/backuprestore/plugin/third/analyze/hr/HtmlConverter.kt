/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          HtmlConverter.kt
 * * Description:   HtmlConverter
 * * Version:       1.0
 * * Date :         2024/5/16
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/5/16     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hr

import android.content.Context
import android.text.TextUtils
import com.oplus.migrate.backuprestore.plugin.third.analyze.ConvertUtils
import com.oplus.migrate.backuprestore.plugin.third.analyze.DataGroup
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteData
import com.oplus.migrate.backuprestore.plugin.third.analyze.TextItem
import com.oplus.migrate.backuprestore.plugin.third.analyze.WrapItem
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.editor.utils.RichStatisticsUtils
import org.jsoup.Jsoup
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import java.io.InputStream
import java.util.Base64
import java.util.UUID

class HtmlConverter {
    companion object {
        private const val TAG = "HtmlConverter"
        private const val NODE_DIV = "div"
        private const val NODE_P = "p"
        private const val NODE_H1 = "h1"
        private const val NODE_H2 = "h2"
        private const val NODE_OL = "ol"
        private const val NODE_UL = "ul"
        private const val NODE_LI = "li"
        private const val NODE_INPUT = "input"
        private const val NODE_TABLE = "table"
        private const val NODE_TR = "tr"
        private const val NODE_TD = "td"
        private const val NODE_BR = "br"
        private const val NODE_HR = "hr"
        private const val NODE_IMG = "img"
        private const val NODE_B = "b"
        private const val NODE_I = "i"
        private const val NODE_U = "u"
        private const val NODE_S = "s"
        private const val NODE_FONT = "font"
        private const val NODE_SPAN = "span"
        private const val NODE_TEXT = "#text"

        private const val ATTR_CLASS = "class"
        private const val ATTR_CLASS_TITLE = "title"
        private const val ATTR_ALIGN = "align"
        private const val ATTR_ALIGN_CENTER = "center"
        private const val ATTR_ALIGN_RIGHT = "right"
        private const val ATTR_CHECKED = "checked"
        private const val ATTR_LIST_TYPE = "type"
        private const val ATTR_LIST_VALUE = "value"
        private const val ATTR_LIST_TYPE_1 = "1"
        private const val ATTR_LIST_TYPE_A = "a"
        private const val ATTR_LIST_TYPE_DISC = "disc"
        private const val ATTR_LIST_TYPE_SQUARE = "square"
        private const val ATTR_STYLE = "style"
        private const val ATTR_FONT_COLOR = "color"
        private const val ATTR_FONT_SIZE = "font-size"
        private const val ATTR_BACKGROUND_COLOR = "background-color"
        private const val ATTR_TEXT_ALIGN = "text-align"
        private const val ATTR_LIST_NUM_FMT = "numFmt"
        private const val ATTR_LIST_LVL_TEXT = "lvlText"
        private const val ATTR_LIST_LVL_START = "start"
        private const val ATTR_LIST_LVL_FONT = "font"
        private const val LIST_BULLET = "bullet"
        private const val CHECK_BOX_FONT = "Wingdings"
        private const val UNCHECKED_CHAR = "o"
        private const val CHECKED_CHAR = "þ"
        private const val ATTR_FONT_WEIGHT = "font-weight"
        private const val ATTR_TEXT_BOLD = "bold"
        private const val ATTR_FONT_STYLE = "font-style"
        private const val ATTR_TEXT_ITALIC = "italic"
        private const val ATTR_TEXT_DECORATION = "text-decoration"
        private const val ATTR_TEXT_UNDERLINE = "underline"
        private const val ATTR_TEXT_LINE_THROUGH = "line-through"
        private const val ATTR_IMG_SRC = "src"

        private const val FONT_SIZE_SMALL = "14"
        private const val FONT_SIZE_MIDDLE = "18"
        private const val FONT_SIZE_BIG = "21"
        private const val FONT_SIZE_LARGE = "23"
        private const val FONT_SIZE_PERCENT_SMALL = "85.0%"
        private const val FONT_SIZE_PERCENT_MIDDLE = "115.0%"
        private const val FONT_SIZE_PERCENT_BIG = "130.0%"
        private const val FONT_SIZE_PERCENT_LARGE = "145.0%"
        private const val FONT_SIZE_UNIT_PT = "pt"
        private const val WORD_TEXT_SIZE_FOR_SAM_12 = 21.5
        private const val WORD_TEXT_SIZE_FOR_SAM_26 = 48.5
        private const val WORD_TEXT_SIZE_FOR_SAM_40 = 75
        private const val WORD_TEXT_SIZE_FOR_SAM_52 = 92

        private const val SPACE = "&nbsp;"
        private const val IMG_HEAD = "data:image/png;base64,"

        private const val COLOR_TRANSPARENT = "transparent"
    }

    private val unSupportNodeStr = StringBuilder()
    private lateinit var data: NoteData
    private var handleData: (NoteData) -> Unit = {}

    fun convertToData(inputStream: InputStream, noteData: NoteData, handleData: (NoteData) -> Unit) {
        AppLogger.BASIC.d(TAG, "convertHtmlToData")
        data = noteData
        this.handleData = handleData
        unSupportNodeStr.clear()
        val document = Jsoup.parse(inputStream, "UTF-8", "")
        convertNode(document.body())
        if (data.isContent()) {
            handleData(data)
        }
    }

    fun statisticUnSupportFeature(context: Context, brand: String) {
        AppLogger.BASIC.d(TAG, "statisticUnSupportFeature brand=$brand unSupportNodeStr=$unSupportNodeStr")
        if (!TextUtils.isEmpty(unSupportNodeStr) && !TextUtils.isEmpty(brand)) {
            RichStatisticsUtils.setEventMoverUnSupportFeature(context, brand, unSupportNodeStr.toString())
        }
    }

    private fun convertNode(node: Node, group: DataGroup? = null, item: TextItem? = null) {
        val nodeName = node.nodeName()
        AppLogger.BASIC.d(TAG, "--- nodeName=$nodeName")
        when (nodeName) {
            NODE_H1, NODE_H2 -> handH1H2(node, group, item)
            NODE_DIV -> handDiv(node, group, item)
            NODE_P -> handP(node, item)
            NODE_UL, NODE_OL -> handOlAndUl(node)
            NODE_LI -> handLi(node, group)
            NODE_INPUT -> handInput(node, group)
            NODE_TABLE -> handTable(node)
            NODE_TR -> handTr(node, group)
            NODE_TD -> handTd(node, group, item)
            NODE_B, NODE_I, NODE_U, NODE_S -> handBIUS(node, group, item)
            NODE_SPAN, NODE_FONT -> handSpanAndFont(node, group, item)
            NODE_TEXT -> handleText(node, group, item)
            NODE_BR, NODE_HR -> handleBr(group)
            NODE_IMG -> handleImg(node, group)
            else -> {
                val childNodes = node.childNodes()
                if (childNodes.isNotEmpty()) {
                    for (childNode in childNodes) {
                        convertNode(childNode, group, item)
                    }
                } else {
                    if (TextUtils.isEmpty(unSupportNodeStr)) {
                        unSupportNodeStr.append(nodeName)
                    } else {
                        unSupportNodeStr.append(",").append(nodeName)
                    }
                }
            }
        }
    }

    /**新版本荣耀笔记数据标题用 h2 标记，这里加上对 h2的处理 <h2 class="title">标题</h2> */
    private fun handH1H2(node: Node, group: DataGroup?, item: TextItem?) {
        if (node.attr(ATTR_CLASS) == ATTR_CLASS_TITLE) {
            // 以文件名为标题，内容的标题丢弃
            return
        }
        for (childNode in node.childNodes()) {
            convertNode(childNode, group, item)
        }
    }

    private fun handDiv(node: Node, group: DataGroup?, item: TextItem?) {
        val align = node.attr(ATTR_ALIGN)
        if (align.isNotEmpty()) {
            val dataGroup = data.addNewGroup()
            dataGroup.alignment = when (align) {
                ATTR_ALIGN_CENTER -> DataGroup.ALIGN_CENTER
                ATTR_ALIGN_RIGHT -> DataGroup.ALIGN_END
                else -> ""
            }
            for (childNode in node.childNodes()) {
                convertNode(childNode, dataGroup)
            }
            if (dataGroup.isTextGroup()) {
                // 文本段落需要增加换行
                val wrapItem = WrapItem()
                if (data.addItem(wrapItem)) {
                    handleData(data)
                    data = data.new()
                }
            }
        } else if ((node is Element) && NODE_INPUT == node.firstElementChild()?.nodeName()) {
            // input 为 div(input + text)格式
            val dataGroup = data.addNewGroup()
            dataGroup.type = DataGroup.TYPE_CHECK_BOX
            for (childNode in node.childNodes()) {
                convertNode(childNode, dataGroup)
            }
        } else {
            for (childNode in node.childNodes()) {
                convertNode(childNode, group, item)
            }
        }
    }

    private fun handP(node: Node, item: TextItem?) {
        val previousNode = node.previousSibling()
        if (previousNode?.nodeName() == NODE_H1 && previousNode.attr(ATTR_CLASS) == ATTR_CLASS_TITLE
            && node is Element && node.text().isEmpty()
        ) {
            // <h1 class="title"><p></p> 标题标签后的空段落标签丢弃
            return
        }
        val styleMap = mutableMapOf<String, String>()
        node.attr(ATTR_STYLE).split(";")
            .filter { it.isNotEmpty() }
            .map { it.split(":") }
            .filter { it.size >= 2 }
            .forEach {
                styleMap[it[0]] = it[1]
            }

        // 三星docx转html，段落属性在 p 标签
        val dataGroup = DataGroup()
        val align = styleMap[ATTR_TEXT_ALIGN]
        dataGroup.alignment = when (align) {
            ATTR_ALIGN_CENTER -> DataGroup.ALIGN_CENTER
            ATTR_ALIGN_RIGHT -> DataGroup.ALIGN_END
            else -> ""
        }
        // <p style="numFmt:bullet;lvlText:o;start:1;font:Wingdings;">
        val numFmt = styleMap[ATTR_LIST_NUM_FMT]
        val lvlText = styleMap[ATTR_LIST_LVL_TEXT] ?: ""
        val start = styleMap[ATTR_LIST_LVL_START] ?: ""
        val isWingdingsFont = styleMap[ATTR_LIST_LVL_FONT] == CHECK_BOX_FONT
        dataGroup.type = when {
            numFmt.isNullOrEmpty() -> ""
            numFmt != LIST_BULLET -> DataGroup.TYPE_ORDERED_LIST
            isWingdingsFont && (lvlText == UNCHECKED_CHAR || lvlText == CHECKED_CHAR) -> DataGroup.TYPE_CHECK_BOX
            else -> DataGroup.TYPE_DISORDERED_LIST
        }
        dataGroup.groupId = if (dataGroup.isList) UUID.randomUUID().toString() else ""
        dataGroup.lvlText = lvlText
        dataGroup.index = start
        // 处理列表段落，没有标志关联成组，故以上下文同类型成组
        if (dataGroup.isList) {
            data.lastOrNull()?.run {
                if (isSameList(dataGroup)) {
                    dataGroup.groupId = groupId
                }
            }
        }
        data.addGroup(dataGroup)
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup, item)
        }
        if (dataGroup.isEmpty() || dataGroup.isTextGroup()) {
            // 空段落 或者 文本段落需要增加换行
            val wrapItem = WrapItem()
            if (data.addItem(wrapItem)) {
                handleData(data)
                data = data.new()
            }
        }
    }

    private fun handOlAndUl(node: Node) {
        // 不加载只传递属性, 统一id
        val dataGroup = DataGroup(groupId = UUID.randomUUID().toString())
        dataGroup.type = when (node.nodeName()) {
            NODE_OL -> DataGroup.TYPE_ORDERED_LIST
            else -> DataGroup.TYPE_DISORDERED_LIST
        }
        val list = if (node is Element) node.children() else emptyList()
        for (childNode in list) {
            convertNode(childNode, dataGroup)
        }
    }

    private fun handLi(node: Node, group: DataGroup?) {
        val lvlText = node.attr(ATTR_LIST_TYPE)
        val value = node.attr(ATTR_LIST_VALUE)
        val dataGroup = DataGroup()
        dataGroup.groupId = group?.groupId ?: UUID.randomUUID().toString()
        dataGroup.type = group?.type ?: when (lvlText) {
            ATTR_LIST_TYPE_DISC, ATTR_LIST_TYPE_SQUARE -> DataGroup.TYPE_DISORDERED_LIST
            ATTR_LIST_TYPE_1, ATTR_LIST_TYPE_A -> DataGroup.TYPE_ORDERED_LIST
            else -> DataGroup.TYPE_DISORDERED_LIST
        }
        dataGroup.lvlText = lvlText
        dataGroup.index = value

        // 列表升序，则为同组
        if (dataGroup.isList) {
            data.lastOrNull()?.let {
                if ((dataGroup.groupId != it.groupId) && dataGroup.isIndexIncrease(it)) {
                    dataGroup.groupId = it.groupId
                }
            }
        }
        data.addGroup(dataGroup)
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup)
        }
    }

    private fun handInput(node: Node, group: DataGroup?) {
        val dataGroup = group ?: data.addNewGroup()
        dataGroup.apply {
            type = DataGroup.TYPE_CHECK_BOX
            lvlText = if (node.hasAttr(ATTR_CHECKED)) {
                DataGroup.CHAR_CHECKED
            } else DataGroup.CHAR_UNCHECKED
        }
        // input 为 input(text)格式时执行
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup)
        }
    }

    private fun handTable(node: Node) {
        val dataGroup = data.addNewGroup()
        dataGroup.type = DataGroup.TYPE_TABLE
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup)
        }
        //找到最后一个 td 元素，增加 tr 结束的标记
        dataGroup.lastTdItem()?.isTr = TextItem.TR_END
    }

    private var firstTdInTr = false
    private fun handTr(node: Node, group: DataGroup?) {
        if (node !is Element) {
            return
        }
        val dataGroup = group ?: data.addNewGroup()
        val elements = node.children()
        firstTdInTr = true
        for (childNode in elements) {
            convertNode(childNode, dataGroup)
        }
        // <tr> 完成后换行
        if (data.addItem(WrapItem())) {
            handleData(data)
            data = data.new()
        }
    }

    private fun handTd(node: Node, group: DataGroup?, item: TextItem?) {
        if (node !is Element) {
            return
        }
        if (firstTdInTr) {
            //找到上一个 td 元素，增加 tr 结束的标记
            group?.lastTdItem()?.isTr = TextItem.TR_END
        }
        if (node.text().trim().isNotBlank()) {
            group ?: data.addNewGroup()
            // <td> 文本后添加双空格
            val text = node.text().replace("\n", "").replace(SPACE, " ")
            val textItem = item ?: TextItem()
            textItem.isTd = true
            if (firstTdInTr) {
                textItem.isTr = TextItem.TR_START
            }
            data = ConvertUtils.handleText(text, data, textItem, handleData)
        }
        firstTdInTr = false
    }

    private fun handBIUS(node: Node, group: DataGroup?, item: TextItem?) {
        val dataGroup = group ?: data.addNewGroup()
        val textItem = when (node.nodeName()) {
            NODE_B -> item?.copy(isBold = true) ?: TextItem(isBold = true)
            NODE_I -> item?.copy(isItalic = true) ?: TextItem(isItalic = true)
            NODE_U -> item?.copy(underline = true) ?: TextItem(underline = true)
            NODE_S -> item?.copy(isStrikeThrough = true) ?: TextItem(isStrikeThrough = true)
            else -> TextItem()
        }
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup, textItem)
        }
    }

    private fun handSpanAndFont(node: Node, group: DataGroup?, item: TextItem?) {
        // <span style="color:#8A2BE2;background-color:#FFBF0061;font-size:16"><font style ="font-size:145.0%;"><font color ="#47cc47">
        var fontLevel = TextItem.TEXT_SIZE_LEVEL_DEFAULT
        var color: String? = null
        var textHighlight = false
        var isBold = item?.isBold ?: false
        var isItalic = item?.isItalic ?: false
        var isUnderline = item?.underline ?: false
        var isStrikeThrough = item?.isStrikeThrough ?: false
        node.attr(ATTR_STYLE).split(";")
            .filter { it.isNotEmpty() }
            .map { it.split(":") }
            .filter { it.size >= 2 }
            .forEach {
                val key = it[0]
                val value = it[1]

                when (key) {
                    ATTR_FONT_SIZE -> {
                        fontLevel = if (value.endsWith(FONT_SIZE_UNIT_PT)) {
                            /*
                            * <span style="font-size:21.0pt;">
                            * 三星字号从6到64，默认为17
                            * 分5档区间标示小、默认、中、大、超大：6、12、26、40、52、64
                            * 对应docx文档字号为：10.5（五号）、21.5、48.5、75、92、115
                            * */
                            val textSize = value.substring(0, value.length - FONT_SIZE_UNIT_PT.length).toFloatOrNull() ?: 0f
                            when {
                                textSize < 1 -> TextItem.TEXT_SIZE_LEVEL_DEFAULT
                                textSize < WORD_TEXT_SIZE_FOR_SAM_12 -> TextItem.TEXT_SIZE_LEVEL_SMALL
                                textSize < WORD_TEXT_SIZE_FOR_SAM_26 -> TextItem.TEXT_SIZE_LEVEL_DEFAULT
                                textSize < WORD_TEXT_SIZE_FOR_SAM_40 -> TextItem.TEXT_SIZE_LEVEL_MIDDLE
                                textSize < WORD_TEXT_SIZE_FOR_SAM_52 -> TextItem.TEXT_SIZE_LEVEL_BIG
                                else -> TextItem.TEXT_SIZE_LEVEL_LARGE
                            }
                        } else {
                            when (value) {
                                FONT_SIZE_SMALL, FONT_SIZE_PERCENT_SMALL -> TextItem.TEXT_SIZE_LEVEL_SMALL
                                FONT_SIZE_MIDDLE, FONT_SIZE_PERCENT_MIDDLE -> TextItem.TEXT_SIZE_LEVEL_MIDDLE
                                FONT_SIZE_BIG, FONT_SIZE_PERCENT_BIG -> TextItem.TEXT_SIZE_LEVEL_BIG
                                FONT_SIZE_LARGE, FONT_SIZE_PERCENT_LARGE -> TextItem.TEXT_SIZE_LEVEL_LARGE
                                else -> TextItem.TEXT_SIZE_LEVEL_DEFAULT  // 默认 16、100.0% 或者 无值
                            }
                        }
                    }

                    ATTR_FONT_COLOR -> color = value

                    ATTR_BACKGROUND_COLOR -> textHighlight = value.isNotEmpty() && (value != COLOR_TRANSPARENT)

                    ATTR_FONT_WEIGHT -> {
                        // 三星docx转换的html，span 需要判断 BIUS
                        isBold = value == ATTR_TEXT_BOLD
                    }

                    ATTR_FONT_STYLE -> {
                        // 三星docx转换的html，span 需要判断 BIUS
                        isItalic = value == ATTR_TEXT_ITALIC
                    }

                    ATTR_TEXT_DECORATION -> {
                        // 三星docx转换的html，span 需要判断 BIUS
                        if (value == ATTR_TEXT_UNDERLINE) {
                            isUnderline = true
                        } else if (value == ATTR_TEXT_LINE_THROUGH) {
                            isStrikeThrough = true
                        }
                    }
                }
            }
        if (color == null) {
            color = node.attr(ATTR_FONT_COLOR)
        }
        val dataGroup = group ?: data.addNewGroup()
        val textItem = item?.copy(
            isBold = isBold, isItalic = isItalic, underline = isUnderline, isStrikeThrough = isStrikeThrough,
            fontLevel = fontLevel, textColor = color ?: "", textHighlight = textHighlight
        ) ?: TextItem(
            isBold = isBold, isItalic = isItalic, underline = isUnderline, isStrikeThrough = isStrikeThrough,
            fontLevel = fontLevel, textColor = color ?: "", textHighlight = textHighlight
        )
        for (childNode in node.childNodes()) {
            convertNode(childNode, dataGroup, textItem)
        }
    }

    private fun handleText(node: Node, group: DataGroup?, item: TextItem?) {
        if (node.toString().trim().isNotBlank()) {
            group ?: data.addNewGroup()
            val text = node.toString().replace("\n", "").replace(SPACE, " ")
            data = ConvertUtils.handleText(text, data, item ?: TextItem(), handleData)
        }
    }

    private fun handleBr(group: DataGroup?) {
        group ?: data.lastNotListGroupOrNull() ?: data.addNewGroup()
        val wrapItem = WrapItem()
        if (data.addItem(wrapItem)) {
            handleData(data)
            data = data.new()
        }
    }

    private fun handleImg(node: Node, group: DataGroup?) {
        group ?: data.addNewGroup()
        val src = node.attr(ATTR_IMG_SRC)
        val imgData = if (src.startsWith(IMG_HEAD)) src.substring(IMG_HEAD.length) else src
        val decodeData = Base64.getMimeDecoder().decode(imgData)
        ConvertUtils.saveImagePng(decodeData, data.id)?.let {
            if (data.addItem(it)) {
                handleData(data)
                data = data.new()
            }
            AppLogger.BASIC.d(TAG, "picture=$it")
        }
    }
}