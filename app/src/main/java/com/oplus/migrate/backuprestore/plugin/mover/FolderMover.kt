package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil
import com.oplus.note.repo.note.entity.Folder
import com.nearme.note.migration.MigrationConstants
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.util.FileUtil
import com.nearme.note.util.ResourceUtils
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.cloud.status.Device
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import com.oplus.note.notebook.internal.NoteBookData
import com.oplus.note.utils.base64
import java.io.File
import java.lang.reflect.Type
import java.util.Date

open class FolderMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {
    private val TAG = "FolderMover"
    open val tag = "Folder"

    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup folder table")
        val list: List<Folder> = AppDatabase.getInstance().foldersDao().findCustomFolder()
        val listStr =
                if (list.isNullOrEmpty()) {
                    AppLogger.BR.w(TAG, "onBackup ${tag}List.isNullOrEmpty()")
                    "[]"
                } else {
                    Gson().toJson(list)
                }
        val backupPath: String = backupFilePath + File.separator + MigrationConstants.FILE_FOLDER
        AppLogger.BR.d(TAG, "onBackup saveToFile, path = ${MigrationConstants.FILE_FOLDER}, content = ${list.size}")
        val isSuccess = FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        if (!isSuccess) {
            AppLogger.BR.e(TAG, "onBackup FoldrMover saveToFile fail")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteBackupPlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_FOLDER_MOVER_BACKUP_FAIL
            )
        }
    }

    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }

        val filePath: String = backupFilePath + File.separator + MigrationConstants.FILE_FOLDER
        AppLogger.BR.d(TAG, "onRestore $tag list: ${filePath.base64()}")

        runCatching {
            FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
                val listType: Type = object : TypeToken<List<Folder>>() {}.type
                var folderList: List<Folder>? = null
                kotlin.runCatching {
                    val gsonBuilder = getDateGsonBuilder()
                    val gson = gsonBuilder.create()
                    folderList = gson.fromJson(this, listType)
                }.onFailure {
                    AppLogger.BR.d(TAG, "onRestore ${tag}List error", it)
                    com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                        NoteRestorePlugin,
                        com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_FOLDER_MOVER_FAILD_TO_RESOLVE_GSON
                    )
                }
                if (folderList.isNullOrEmpty()) {
                    AppLogger.BR.w(TAG, "onRestore ${tag}List.isNullOrEmpty()")
                } else {
                    AppLogger.BR.w(TAG, "onRestore ${tag}List size:${folderList?.size}")
                    mergeData(context, folderList ?: emptyList())
                }
            } ?: run {
                AppLogger.BR.w(TAG, "onRestore FloderMover getContentFromFile is null")
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                    NoteRestorePlugin,
                    com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_FOLDER_MOVER_GETCONTENTFROMFILE_FAIL
                )
            }
        }.onFailure {
            AppLogger.BR.e(TAG, "onRestore $tag error :${it.message}}")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteRestorePlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_FOLDER_MOVER_RESTORE_FAIL
            )
        }
    }

    @Suppress("LongMethod")
    internal fun mergeData(context: Context, remoteFolderList: List<Folder>) {
        val addFolderList = mutableListOf<Folder>()
        val updateFolderList = mutableListOf<Folder>()

        for (remoteFolder in remoteFolderList) {
            remoteFolder.id = 0
            remoteFolder.state = FolderInfo.FOLDER_STATE_NEW
            val cover = remoteFolder.extra?.getCover()

            if (!TextUtils.isEmpty(cover)) {
                val idInt = ResourceUtils.getResIdByResName(MyApplication.appContext, cover)
                if (idInt == 0) {
                    AppLogger.BASIC.d(TAG, "在搬家后可能会有笔记本封面资源id加载不到的情况，均设置为默认封面")
                    val skinCover = NoteBookData.getDefaultCover()
                    remoteFolder.extra?.setCover(skinCover, NoteBookData.isOldCover(skinCover))
                } else if (!NoteBookData.imageList.contains(cover)) {
                    val skinCover = NoteBookData.getAllNoteDefaultCover()
                    remoteFolder.extra?.setCover(skinCover, NoteBookData.isOldCover(skinCover))
                }
            }

            val folderList = AppDatabase.getInstance().foldersDao().getFoldersWithGuidOrName(remoteFolder.guid, remoteFolder.name)
            val folderWithSameGuid: Folder? = findLocalFolderWithSameGuid(folderList, remoteFolder.guid)

            if (null != folderWithSameGuid) { //guid相同，同一文件夹
                val localFolder = folderWithSameGuid
                localFolder.name = remoteFolder.name
                localFolder.modifyDevice = remoteFolder.modifyDevice
                localFolder.extra = remoteFolder.extra
                localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
                localFolder.createTime = remoteFolder.createTime
                localFolder.modifyTime = remoteFolder.modifyTime
                if (localFolder.encryptedPre != localFolder.encrypted) {
                    //本地加解密状态已改变，已本地状态为准
                } else {
                    //本地加解密状态未改变，以云端为准
                    localFolder.encrypted = remoteFolder.encrypted
                }
                updateFolderList.add(localFolder)
            } else {
                val folderWithSameName: Folder? = findLocalFolderWithSameName(folderList, remoteFolder.name)
                if (null != folderWithSameName) { //guid不同，名字相同，合并文件夹
                    if (handleFolderCaseEncrypted(context, remoteFolder, folderWithSameName)) {
                        continue
                    }

                    if (handleFolderQuick(context, remoteFolder, folderWithSameName)) {
                        continue
                    }

                    if (handlerFolderCaseCallSummary(context, remoteFolder, folderWithSameName)) {
                        continue
                    }

                    if (handlerFolderCaseArticleSummary(context, remoteFolder, folderWithSameName)) {
                        continue
                    }

                    if (handlerFolderCaseAudioSummary(context, remoteFolder, folderWithSameName)) {
                        continue
                    }

                    val localFolder = folderWithSameName
                    AppLogger.BR.d(TAG, "mergeData(), remoteFolder: $remoteFolder, localFolder: $localFolder")
                    localFolder.modifyDevice = remoteFolder.modifyDevice
                    if (remoteFolder.createTime != null) {
                        localFolder.createTime = remoteFolder.createTime
                    }
                    if (remoteFolder.modifyTime != null) {
                        localFolder.modifyTime = remoteFolder.modifyTime
                    }
                    localFolder.state = FolderInfo.FOLDER_STATE_MODIFIED
                    localFolder.extra = remoteFolder.extra
                    if (localFolder.encryptedPre != localFolder.encrypted) {
                        //本地加解密状态已改变，已本地状态为准
                    } else {
                        //本地加解密状态未改变，以云端为准
                        localFolder.encrypted = remoteFolder.encrypted
                    }
                    updateFolderList.add(localFolder)
                    updateFolderGuidForRichNote(remoteFolder.guid, localFolder.guid)
                } else {
                    addFolderList.add(remoteFolder)
                }
            }

            if (addFolderList.size >= com.oplus.migrate.backuprestore.plugin.MigrationConstants.MERGE_COUNT_LIMIT) {
                addFolderList.forEach {
                    FolderUtil.insertFolderRecovery(it)
                }
                addFolderList.clear()
            }

            if (updateFolderList.size >= com.oplus.migrate.backuprestore.plugin.MigrationConstants.MERGE_COUNT_LIMIT) {
                AppDatabase.getInstance().foldersDao().updateFolders(updateFolderList)
                updateFolderList.clear()
            }
        }

        if (addFolderList.size > 0) {
            AppDatabase.getInstance().foldersDao().insertAll(addFolderList)
        }

        if (updateFolderList.size > 0) {
            AppDatabase.getInstance().foldersDao().updateFolders(updateFolderList)
        }
    }

    /**
     * 处理不支持加密功能版本上的创建的普通“加密笔记”，同步到支持加密版本的冲突
     * 低版本同步上来的普通加密笔记，在高版本上会新建为一个带后缀的普通“加密笔记”文件夹，如“加密笔记1”，“加密笔记2”
     * 以此类推
     *
     * 通话摘要后新增的加密笔记本不会走此逻辑
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    private fun handleFolderCaseEncrypted(context: Context, cloudFolder: Folder, localFolder: Folder): Boolean {
        AppLogger.BASIC.d(TAG, "handleFolderCaseEncrypted(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        if (FolderInfo.FOLDER_GUID_ENCRYPTED == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handleFolderCaseEncrypted(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handleFolderCaseEncrypted(), newFolderName: ${newFolderName.base64()}")
            FolderUtil.insertFolderNameSync(
                context,
                newFolderName,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                null
            )
            return true
        }

        return false
    }

    private fun handleFolderQuick(context: Context, cloudFolder: Folder, localFolder: Folder): Boolean {
        AppLogger.BASIC.d(TAG, "handleFolderQuick(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        if (FolderInfo.FOLDER_GUID_QUICK == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handleFolderQuick(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handleFolderQuick(), newFolderName: ${newFolderName.base64()}")
            FolderUtil.insertFolderNameSync(
                context,
                newFolderName,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                cloudFolder.extra?.getCover()
            )
            return true
        }

        return false
    }

    @Suppress("LongMethod")
    private fun handlerFolderCaseCallSummary(
        context: Context,
        cloudFolder: Folder,
        localFolder: Folder
    ): Boolean {
        AppLogger.BASIC.d(TAG, "handlerFolderCaseCallSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        /**
         * 本地有真“通话摘要"笔记本,则将搬过来的假“通话摘要”笔记本命名为“通话摘要X”
         * 本地有假“通话摘要”笔记本，则将搬过来的真”通话摘要“笔记本直接插入，并将本地假“通话摘要”笔记本命名为“通话摘要X”
         */
        if (FolderInfo.FOLDER_GUID_CALL_SUMMARY == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseCallSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseCallSummary(), newFolderName: ${newFolderName.base64()}")
            FolderUtil.insertFolderNameSync(
                context,
                newFolderName,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                null
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_CALL_SUMMARY == cloudFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseCallSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseCallSummary2, newFolderName: ${newFolderName.base64()}")
            localFolder.name = newFolderName
            localFolder.modifyTime = Date(System.currentTimeMillis())
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            FolderUtil.insertFolderNameSync(
                context,
                cloudFolder.name,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                cloudFolder.extra?.getCover()
            )
            return true
        }

        return false
    }

    @Suppress("LongMethod")
    private fun handlerFolderCaseArticleSummary(
        context: Context,
        cloudFolder: Folder,
        localFolder: Folder
    ): Boolean {
        AppLogger.BASIC.d(TAG, "handlerFolderCaseArticleSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        /**
         * 本地有真“通话摘要"笔记本,则将搬过来的假“通话摘要”笔记本命名为“通话摘要X”
         * 本地有假“通话摘要”笔记本，则将搬过来的真”通话摘要“笔记本直接插入，并将本地假“通话摘要”笔记本命名为“通话摘要X”
         */
        if (FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseArticleSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseArticleSummary(), newFolderName: ${newFolderName.base64()}")
            FolderUtil.insertFolderNameSync(
                context,
                newFolderName,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                null
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY == cloudFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseArticleSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseArticleSummary2, newFolderName: ${newFolderName.base64()}")
            localFolder.name = newFolderName
            localFolder.modifyTime = Date(System.currentTimeMillis())
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            FolderUtil.insertFolderNameSync(
                context,
                cloudFolder.name,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                cloudFolder.extra?.getCover()
            )
            return true
        }

        return false
    }


    @Suppress("LongMethod")
    private fun handlerFolderCaseAudioSummary(
        context: Context,
        cloudFolder: Folder,
        localFolder: Folder
    ): Boolean {
        AppLogger.BASIC.d(TAG, "handlerFolderCaseAudioSummary(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        /**
         * 本地有真“语音摘要"笔记本,则将搬过来的假“语音摘要”笔记本命名为“语音摘要X”
         * 本地有假“语音摘要”笔记本，则将搬过来的真”语音摘要“笔记本直接插入，并将本地假“语音摘要”笔记本命名为“语音摘要X”
         */
        if (FolderInfo.FOLDER_GUID_AUDIO_SUMMARY == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseAudioSummary(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseAudioSummary(), newFolderName: ${newFolderName.base64()}")
            FolderUtil.insertFolderNameSync(
                context,
                newFolderName,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                null
            )
            return true
        } else if (FolderInfo.FOLDER_GUID_AUDIO_SUMMARY == cloudFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseAudioSummary2, localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()

            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }

            var index = 0

            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }

            val newFolderName = cloudFolder.name + (index + 1)
            AppLogger.BASIC.d(TAG, "handlerFolderCaseAudioSummary2, newFolderName: ${newFolderName.base64()}")
            localFolder.name = newFolderName
            localFolder.modifyTime = Date(System.currentTimeMillis())
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
            FolderUtil.insertFolderNameSync(
                context,
                cloudFolder.name,
                cloudFolder.guid,
                Device.getDeviceIMEI(context),
                cloudFolder.createTime?.time ?: 0,
                cloudFolder.state,
                cloudFolder.encrypted,
                cloudFolder.extra?.getCover()
            )
            return true
        }

        return false
    }

    private fun findLocalFolderWithSameGuid(folderList: List<Folder>, targetGuid: String): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.guid, targetGuid)) {
                return folder
            }
        }
        return null
    }

    private fun findLocalFolderWithSameName(folderList: List<Folder>, targetName: String): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.name, targetName)) {
                return folder
            }
        }
        return null
    }

    @VisibleForTesting
    fun updateFolderGuidForRichNote(oldFolderGuid: String, newFolderGuid: String) {
        try {
            val notes = AppDatabase.getInstance().richNoteDao().findByFolderGuids(listOf(oldFolderGuid))
            for (note in notes) {
                note.folderGuid = newFolderGuid
                note.state = RichNote.STATE_MODIFIED
                note.updateTime = System.currentTimeMillis()
            }

            RichNoteRepository.updateNotes(notes, false)
        } catch (e: Exception) {
            AppLogger.BASIC.e(TAG, "updateFolderGuidForRichNote error.", e)
        }
    }
}