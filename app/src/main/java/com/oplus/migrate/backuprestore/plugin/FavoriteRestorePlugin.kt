/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9060       2023/10/21      1.0     create file
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.nearme.note.activity.edit.MediaUtils
import com.nearme.note.activity.richedit.RichAdapter
import com.nearme.note.activity.richedit.entity.Data
import com.nearme.note.activity.richedit.entity.RichData
import com.nearme.note.activity.richedit.entity.isEmpty
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil.createCollectionNoteByFolderGuid
import com.nearme.note.logic.ThumbFileConstants
import com.nearme.note.logic.ThumbFileManager
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.speech.utils.PresetNoteSpeechUtils
import com.nearme.note.util.FileUtil
import com.nearme.note.util.filesDirAbsolutePath
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.RestorePlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.note.repo.note.entity.PageResult
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.ResultEntity
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.entity.SourcePackage
import com.oplus.note.repo.note.entity.SubAttachment
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.io.File
import java.lang.reflect.Type
import java.util.UUID

class FavoriteRestorePlugin : RestorePlugin() {

    @VisibleForTesting
    private lateinit var mRestoreResultPath: String
    @VisibleForTesting
    private lateinit var mRestoreFileRootPath: String
    private var mMaxCount = 0
    private var mCompleteCount = 0
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val repository = RichNoteRepository
    private val mPauseLock = Object()
    private var mIsCancel: Boolean = false
    private var mIsPause: Boolean = false
    private var mCheckCollectionFolderFlag = false

    companion object {
        const val TAG = "FavoriteRestorePlugin"
        const val RESULT_FOLDER = "ColorFavoriteResult"
        const val RESULTS = "results.json"
        const val FILES = "files"
        private const val SHAREIMAGE = "share_image"
        private const val HTTP = "http://"
        private const val HTTPS = "https://"
        private const val NOTE_TYPE_IMAGE = "image"
        private const val NOTE_TYPE_FILE = "file"
        private const val NOTE_TYPE_URL = "url"
    }

    /**  doc: https://hio.oppo.com/app/ozone/team/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=157883906_157808373
    恢复业务流程需继承并实现RestorePlugin，主要流程：
    数据恢复：onCreate --> onPrepare --> onRestore--> onDestroy
    暂停恢复：onPause
    继续恢复：onContinue
    停止恢复：onCancel
     */
    override fun onCreate(
        context: Context,
        brPluginHandler: BRPluginHandler,
        config: BREngineConfig
    ) {
        super.onCreate(context, brPluginHandler, config)
        AppLogger.BR.d(TAG, "onCreate:$config, ${Thread.currentThread().name}")
        mRestoreResultPath = getResultFolder(config)
        mRestoreFileRootPath = getFilesFolder(config)
        val resultPathName = FileUtil.getLastPathComponent(mRestoreResultPath)
        AppLogger.BR.d(
            TAG,
            "backupResultPathName: $resultPathName"
        )
    }

    override fun onPreview(bundle: Bundle): Bundle? {
        AppLogger.BR.d(TAG, "onPreview")
        return null
    }

    override fun onPrepare(bundle: Bundle): Bundle {
        val prepareBundle = Bundle()
        prepareBundle.putInt(ProgressHelper.MAX_COUNT, mMaxCount)
        AppLogger.BR.d(TAG, "onPrepare:$prepareBundle, ${Thread.currentThread().name}")
        return prepareBundle
    }

    @Suppress("LongMethod")
    override fun onRestore(bundle: Bundle) {
        runCatching {
            AppLogger.BR.d(TAG, "onRestore:, ${Thread.currentThread().name}")
            FileUtil.getContentFromFile(getFileDescriptor(mRestoreResultPath))
                ?.apply {
                    val listType: Type = object : TypeToken<List<ResultEntity>>() {}.type
                    val resultsList = kotlin.runCatching {
                        Gson().fromJson<List<ResultEntity>?>(this, listType)
                    }.onFailure {
                        //这次this可能为空字符串，造成json解析出错，跳出循环导搬家失败
                        AppLogger.BR.e(TAG, "from json error:${it.message}")
                    }.getOrNull()
                    if (resultsList.isNullOrEmpty()) {
                        AppLogger.BR.d(TAG, "resultsList is empty")
                        mMaxCount = 0
                    } else {
                        AppLogger.BR.d(TAG, "resultsList size ${resultsList.size}")
                        mMaxCount = resultsList.size

                        resultsList.takeIf { it.isNotEmpty() }?.run {
                            runCatching {
                                while (!mIsCancel && mMaxCount > mCompleteCount) {
                                    synchronized(mPauseLock) {
                                        while (mIsPause) {
                                            runCatching {
                                                AppLogger.BR.d(TAG, "onRestore on pause wait lock here")
                                                mPauseLock.wait()
                                            }
                                        }
                                    }

                                    val resultEntity = resultsList[mCompleteCount]
                                    mCompleteCount++
                                    restoreFileIfNeed(resultEntity)
                                    val progress = Bundle()
                                    ProgressHelper.putMaxCount(progress, mMaxCount)
                                    ProgressHelper.putCompletedCount(progress, mCompleteCount)
                                    pluginHandler.updateProgress(progress)
                                }
                            }.onFailure {
                                AppLogger.BR.e(TAG, "onRestore error", it)
                            }
                        }
                    }
                }
        }.onFailure {
            AppLogger.BR.e(TAG, "Favorite restore error :${it.message}}")
        }
    }


    override fun onPause(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onPause, ${Thread.currentThread().name}")
        mIsPause = true
    }

    override fun onContinue(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onContinue, ${Thread.currentThread().name}")
        mIsPause = false
        synchronized(mPauseLock) {
            mPauseLock.notifyAll()
            AppLogger.BR.d(TAG, "onContinue mPauseLock.notifyAll()")
        }
    }

    override fun onCancel(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onCancel, ${Thread.currentThread().name}")
        mIsCancel = true
        mIsPause = false
        synchronized(mPauseLock) {
            mPauseLock.notifyAll()
            AppLogger.BR.d(TAG, "onCancel mLock.notifyAll()")
        }
    }

    override fun onDestroy(bundle: Bundle): Bundle {
        val resultBundle = Bundle()
        ProgressHelper.putBRResult(
            resultBundle,
            if (mIsCancel) ProgressHelper.BR_RESULT_CANCEL else ProgressHelper.BR_RESULT_SUCCESS
        )
        ProgressHelper.putCompletedCount(resultBundle, mCompleteCount)
        ProgressHelper.putMaxCount(resultBundle, mMaxCount)
        AppLogger.BR.d(
            TAG,
            "onDestroy resultBundle = $resultBundle, ${Thread.currentThread().name}"
        )
        return resultBundle
    }


    private fun restoreFileIfNeed(resultEntity: ResultEntity) {
        val richData = constructWithoutAttachments(resultEntity.title)
        val picAtts: MutableList<Attachment> = mutableListOf()
        saveNotesAsync(picAtts, richData, resultEntity)
        AppLogger.BR.d(TAG, "extra note items size :${richData.items?.size} ")
    }

    private fun saveNotesAsync(
        picAtts: MutableList<Attachment>,
        richData: RichData,
        data: ResultEntity
    ) {
        if (data.type == SHAREIMAGE) {
            data.noteType = NOTE_TYPE_IMAGE
        } else if (!TextUtils.isEmpty(data.filePath)) {
            data.noteType = NOTE_TYPE_FILE
        } else if ((data.url?.lowercase()?.startsWith(HTTP) == true) ||
            (data.url?.lowercase()?.startsWith(HTTPS) == true) || (!TextUtils.isEmpty(data.deepLink))) {
            data.noteType = NOTE_TYPE_URL
        } else {
            AppLogger.BR.d(TAG,
                "save not support type:${data.type} filePath length:${data.filePath?.length} " +
                        "url length:${data.url?.length} deepLink length:${data.deepLink?.length}")
            return
        }
        if (data.noteType == NOTE_TYPE_IMAGE) {
            AppLogger.BR.d(TAG, "save type as image")
            data.filePath?.let { filePath ->
                val insertImageResult = doInsertImagePre(richData, filePath, data)
                if (insertImageResult != null) {
                    picAtts.add(insertImageResult)
                } else {
                    AppLogger.BR.d(TAG, "image fileResult is null")
                    return
                }
            } ?: kotlin.run {
                AppLogger.BR.e(TAG, "image filePath is null")
                return
            }
        } else if (data.noteType == NOTE_TYPE_FILE) {
            AppLogger.BR.d(TAG, "save type as file")
            data.filePath?.let { filePath ->
                val doInsertFilePre = doInsertFilePre(richData, filePath, data)
                doInsertFilePre?.let {
                    picAtts.add(doInsertFilePre.first)
                    picAtts.add(doInsertFilePre.second)
                } ?: kotlin.run {
                    AppLogger.BR.e(TAG, "file fileResult is null")
                    return
                }
            } ?: kotlin.run {
                AppLogger.BR.e(TAG, "file filePath is null")
                return
            }
        }

        addToNoteStep(richData, picAtts, data)

        if (richData.isEmpty()) {
            AppLogger.BR.d(TAG, "richData is empty")
            return
        }

        coroutineScope.launch {
            val reNewRichNoteWithAtt: RichNoteWithAttachments? =
                repository.convert(
                    richData,
                    sourcePackage = SourcePackage(
                        data.packageName,
                        data.packageLabel
                    )
                )
            if (data.noteType == NOTE_TYPE_FILE) {
                picAtts.let { attachments ->
                    reNewRichNoteWithAtt?.attachments = attachments
                }
            }
            reNewRichNoteWithAtt?.apply {
                repository.insertBySuspend(this)
            } ?: kotlin.run {
                AppLogger.BR.e(TAG, "reNewRichNoteWithAtt is null")
            }
        }
    }

    @VisibleForTesting
    fun addToNoteStep(
        richData: RichData,
        picAtts: MutableList<Attachment>,
        data: ResultEntity
    ) {
        richData.apply {
            var size = items.size
            val beforeSize = items.size
            if (data.noteType == NOTE_TYPE_IMAGE) {
                picAtts.forEach {
                    addItem(
                        size,
                        Data(Data.TYPE_ATTACHMENT, null, it)
                    )
                    size++
                }
            } else if (data.noteType == NOTE_TYPE_FILE) {
                picAtts.forEach {
                    if (it.type == Attachment.TYPE_PICTURE) {
                        addItem(
                            size,
                            Data(Data.TYPE_ATTACHMENT, null, it)
                        )
                    }
                    size++
                }
            } else if (data.noteType == NOTE_TYPE_URL) {
                addItem(
                    size, Data(
                        Data.TYPE_CARD,
                        card = PageResult(
                            url = (data.url ?: ""),
                            cover = (data.coverImage ?: "")
                        ).apply {
                            title = (data.title ?: "")
                        }
                    )
                )
                size++
            }
            AppLogger.BR.d(TAG, "size:$size, before:$beforeSize")
        }
    }

    @VisibleForTesting
    fun doInsertFilePre(
        richData: RichData,
        filePath: String,
        data: ResultEntity
    ): Pair<Attachment, Attachment>? = runBlocking {
        val noteGuid = richData.getNoteGuid()
        if (noteGuid == null) {
            AppLogger.BR.d(TAG, "doInsertFilePre noteGuid is null")
            return@runBlocking null
        }
        val sourceFileName = FileUtil.getLastPathComponent(filePath)
        val picAttachment = UUID.randomUUID().toString().let {
            Attachment(
                attachmentId = it,
                type = Attachment.TYPE_PICTURE,
                richNoteId = noteGuid,
                fileName = data.title
            )
        }
        val attType = Attachment.TYPE_FILE_CARD
        val spaceImage = PresetNoteSpeechUtils.PRESET_NOTE_FILE_CARD_ATTACHMENT
        val fileAttachment = Attachment(
            attachmentId = UUID.randomUUID().toString(),
            richNoteId = noteGuid,
            type = attType,
            fileName = data.title
        )
        fileAttachment.subAttachment =
            SubAttachment(associateAttachmentId = picAttachment.attachmentId)
        runCatching {
            val bitmap = MyApplication.appContext.assets.open(spaceImage)
                .let { MediaUtils.getThumbBitmapFromInputStream(it) }
            bitmap?.apply {
                picAttachment.picture = Picture(bitmap.width, bitmap.height)
            }

            FileUtil.saveBmpToFile(
                bitmap, picAttachment.absolutePath(MyApplication.appContext)
            )

            val destFilePath = fileAttachment.absolutePath(MyApplication.appContext)
            val destFile = File(destFilePath)
            AppLogger.BR.d(TAG, "doInsertFilePre destFile path name ${destFile.name}")
            val backupPath = mRestoreFileRootPath + File.separator + sourceFileName
            AppLogger.BR.d(TAG, "doInsertFilePre backupPath name $sourceFileName")
            saveRichDataTitle(richData, sourceFileName)
            val fd = getFileDescriptor(backupPath)
            com.oplus.migrate.utils.FileUtil.createFile(destFile)
            com.oplus.migrate.utils.FileUtil.saveFile(fd, destFile)
        }.onFailure {
            AppLogger.BR.e(TAG, "doInsertFilePre error:${it.message}")
        }.onSuccess {
            AppLogger.BR.d(TAG, "doInsertFilePre create success")
        }
        return@runBlocking Pair(picAttachment, fileAttachment)
    }

    @VisibleForTesting
    fun doInsertImagePre(richData: RichData, filePath: String, data: ResultEntity): Attachment? = runBlocking {
        val noteGuid = richData.getNoteGuid()
        if (noteGuid == null) {
            AppLogger.BR.d(TAG, "doInsertPicPre noteGuid is null")
            return@runBlocking null
        }
        val sourceFileName = FileUtil.getLastPathComponent(filePath)
        val fileAttachment = Attachment(type = Attachment.TYPE_PICTURE, richNoteId = noteGuid, fileName = data.title)
        runCatching {
            val destFilePathTemp = fileAttachment.run {
                "${MyApplication.appContext.filesDirAbsolutePath()}/$richNoteId/$attachmentId${ThumbFileConstants.THUMB_TEMP}"
            }
            val destFile = File(destFilePathTemp)
            AppLogger.BR.d(TAG, "doInsertPicPre destFile temp path name ${destFile.name}")
            val backupPath = mRestoreFileRootPath + File.separator + sourceFileName
            AppLogger.BR.d(TAG, "doInsertPicPre backupPath name $sourceFileName")
            saveRichDataTitle(richData, sourceFileName)
            val fd = getFileDescriptor(backupPath)
            com.oplus.migrate.utils.FileUtil.createFile(destFile)
            com.oplus.migrate.utils.FileUtil.saveFile(fd, destFile)
            val bitmap = BitmapFactory.decodeFile(destFile.absolutePath)
            val width = bitmap.width
            val height = bitmap.height
            fileAttachment.picture = Picture(width, height)
            //压缩并存入文件
            val path = fileAttachment.absolutePath(MyApplication.appContext)
            val result = FileUtil.saveBmpToFile(bitmap, path)
            FileUtil.deleteFile(destFile)
            AppLogger.BR.d(TAG, "doInsertPicPre destFile result $result")
        }.onFailure {
            AppLogger.BR.e(TAG, "doInsertPicPre error:${it.message}")
        }.onSuccess {
            AppLogger.BR.d(TAG, "doInsertPicPre create success")
        }
        return@runBlocking fileAttachment
    }

    private fun saveRichDataTitle(richData: RichData, subject: String?) {
        richData.title?.let {
            if (subject != null) {
                it.text = SpannableStringBuilder(subject)
            }
        } ?: kotlin.run {
            AppLogger.BR.e(TAG, "richData not create")
        }
    }

    private fun constructWithoutAttachments(subject: String?): RichData {
        val items = mutableListOf(Data.emptyInstance())
        var subjectSub: String? = subject
        val titleTxt = if (subject != null) {
            subjectSub = if (subject.length >= RichAdapter.TITLE_MAX_LENGTH) {
                subject.substring(0, RichAdapter.TITLE_MAX_LENGTH)
            } else {
                subject
            }
            Data(Data.TYPE_TEXT, SpannableStringBuilder(subjectSub))
        } else {
            Data.emptyInstance()
        }
        checkCollectionIsExistAndCreate()
        val richData = RichData(
            metadata = RichNote(folderGuid = FolderFactory.FOLDER_GUID_COLLECTION).apply {
                title = subjectSub
                updateTime = System.currentTimeMillis()
                coroutineScope.launch {
                    ThumbFileManager.ensureRichNoteFolderExist(localId)
                }
            },
            title = titleTxt,
            items = items,
            coverPictureAttachment = null
        )
        return richData
    }

    private fun checkCollectionIsExistAndCreate() {
        if (!mCheckCollectionFolderFlag) {
            val folder =
                AppDatabase.getInstance().foldersDao()
                    .findByGuid(FolderFactory.FOLDER_GUID_COLLECTION)
            AppLogger.BR.d(TAG, "checkCollectionIsExistAndCreate folder is null ${folder == null}")
            if (folder == null) {
                createCollectionNoteByFolderGuid()
            }
            mCheckCollectionFolderFlag = true
        }
    }

    private fun getResultFolder(config: BREngineConfig) =
        config.restoreRootPath + File.separator + RESULT_FOLDER + File.separator + RESULTS

    private fun getFilesFolder(config: BREngineConfig) =
        config.restoreRootPath + File.separator + RESULT_FOLDER + File.separator + FILES
}