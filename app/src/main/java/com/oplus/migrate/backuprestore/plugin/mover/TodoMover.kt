package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import androidx.core.net.toUri
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.TodoProviderHelper
import com.nearme.note.model.ToDoRepository
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.DateUtil
import com.nearme.note.util.FileUtil
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.entity.ToDo
import java.io.File
import java.lang.reflect.Type

class TodoMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {
    companion object {
        const val TAG = "TodoMover"
    }
    private val mTodoCache = ArrayList<ToDo>()
    private val mUpdateTodoCache = ArrayList<ToDo>()
    //private val toDoRepository = MyApplication.getMyApplication().appComponent.toDoRepository()
    private val toDoRepository = ToDoRepository.getInstance()

    // 备份目录的待办属性文件路径
    private val todoAttrFilePath: String = backupFilePath + File.separator + MigrationConstants.FILE_TODO_ATTR

    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup todo table")
        var list: MutableList<ToDo> = mutableListOf()
        if (ConfigUtils.isToDoDeprecated) {
            // 向备份目录写入待办属性文件
            val attr = Gson().toJson(TodoMoveAttr())
            FileUtil.saveToFile(plugin.getFileDescriptor(todoAttrFilePath), attr)
            if (TodoProviderHelper.isCalendarSupportTodo(context) && TodoProviderHelper.isTaskProviderExists(context)) {
                list = TodoProviderHelper.queryCalendarTodoTask(context)
            }
        } else {
            list = AppDatabase.getInstance().toDoDao().allData
        }
        val listStr =
            if (list.isEmpty()) {
                AppLogger.RED_MIGRATION.w(TAG, "onBackup todoList.isEmpty()")
                "[]"
            } else {
                Gson().toJson(list)
            }
        val backupPath: String = backupFilePath + File.separator + MigrationConstants.FILE_TODO
        AppLogger.BR.d(TAG, "onBackup saveToFile, path = ${MigrationConstants.FILE_TODO}, content = ${list.size}")
        val isSuccess = FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        if (!isSuccess) {
            AppLogger.BR.e(TAG, "TodoMover [FileUtil]saveToFile failed")
            MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_TODO_MOVER_IO_EXCEPTION)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }
        // 判断数据是否来源于OS16以前的版本
        val fromOldVersion =
            (!FileUtil.isFileExist(todoAttrFilePath))
                    || (FileUtil.isFileExist(todoAttrFilePath) && TodoMoveAttr().createOsSdkVersion <= Build.VERSION_CODES.VANILLA_ICE_CREAM)
        // 注意：外销不管待办来源是哪个OS版本，新机便签都要搬入待办。如果待办是下线状态，就搬入日历存储，否则搬入便签。
        if (ConfigUtils.isToDoDeprecated) {
            var shouldRestore = ConfigUtils.isExport
            if (!shouldRestore) {
                shouldRestore = fromOldVersion || !TodoProviderHelper.isCalendarSupportTodo(context)
            }
            if (shouldRestore) {
                restoreToCalendar()
            }
        } else {
            // 走原来的逻辑，写入便签的存储中
            restoreToDb()
        }
    }

    /**
     * 恢复数据，写入便签的存储（OS16以下）
     */
    private fun restoreToDb() {
        val filePath: String = backupFilePath + File.separator + MigrationConstants.FILE_TODO
        AppLogger.BR.d(TAG, "restoreToDb todo table: ${MigrationConstants.FILE_TODO}")

        FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
            val listType: Type = object : TypeToken<List<ToDo>>(){}.type
            var todoList: List<ToDo>? = null

            try {
                val gsonBuilder = getDateGsonBuilder()
                val gson = gsonBuilder.create()
                todoList = gson.fromJson(this, listType)
            } catch (e: Exception) {
                AppLogger.BR.e(TAG, "restoreToDb todolist error", e)
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_TODO_MOVER_FAILD_TO_RESOLVE_GSON)
            }
            if (todoList.isNullOrEmpty()) {
                AppLogger.BR.w(TAG, "restoreToDb todoList.isNullOrEmpty()")
            } else {
                AppLogger.BR.w(TAG, "restoreToDb todoList size:${todoList.size}")
                for (remoteTodo in todoList) {
                    remoteTodo.resetToNewState()

                    val localData : ToDo? = AppDatabase.getInstance().toDoDao().getByLocalIdSync(remoteTodo.localId)
                    mergeData(remoteTodo, localData)
                }

                insertMergeDataToDb(true)
                updateMergeDataToDb(true)
                AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO)
            }
        } ?: run {
            AppLogger.BR.w(TAG, "TodoMover restoreToDb todoList getContentFromFile is null:")
            MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_TODO_MOVER_GETCONTENTFROMFILE_FAIL)
        }
    }

    /**
     * 恢复数据，写入日历的存储（OS16以上）
     */
    private fun restoreToCalendar() {
        val filePath: String = backupFilePath + File.separator + MigrationConstants.FILE_TODO
        AppLogger.BR.d(TAG, "restoreToCalendar todo table: ${MigrationConstants.FILE_TODO}")

        FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
            val listType: Type = object : TypeToken<List<ToDo>>() {}.type
            var todoList: List<ToDo>? = null

            kotlin.runCatching {
                val gsonBuilder = getDateGsonBuilder()
                val gson = gsonBuilder.create()
                todoList = gson.fromJson(this, listType)
            }.onFailure {
                AppLogger.BR.e(TAG, "restoreToCalendar todolist error: ${it.message}")
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_TODO_MOVER_FAILD_TO_RESOLVE_GSON)
            }
            if (todoList.isNullOrEmpty()) {
                AppLogger.BR.w(TAG, "restoreToCalendar todoList.isNullOrEmpty()")
            } else {
                AppLogger.BR.w(TAG, "restoreToCalendar todoList size: ${todoList!!.size}")
                // 将需要恢复的待办数据插入日历
                TodoProviderHelper.batchInsertTodos(context, todoList!!, true, "")
            }
        } ?: run {
            AppLogger.BR.w(TAG, "TodoMover restoreToCalendar todoList getContentFromFile is null:")
            MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_TODO_MOVER_GETCONTENTFROMFILE_FAIL)
        }
    }

    private fun mergeData(remoteData: ToDo?, relatedData: ToDo?) {
        //本地没有相同id待办数据，直接作为新建待办插入
        if (relatedData == null) {
            AppLogger.RED_MIGRATION.d(TAG, "relateData is null, insert new data")
            remoteData!!.resetToNewState()
            mTodoCache.add(remoteData)
            insertMergeDataToDb()
        } else {
            AppLogger.RED_MIGRATION.d(TAG, "relateData is not null, merge data")
            val sameContent = TextUtils.equals(relatedData.content, remoteData!!.content)
            val sameAlarmTime = DateUtil.areDateEquals(relatedData.alarmTime, remoteData.alarmTime)
            var sameFinishState = false
            if (relatedData.finishTime != null && remoteData.finishTime != null
                    || relatedData.finishTime == null && remoteData.finishTime == null) {
                sameFinishState = true
            }

            if (sameAlarmTime && sameContent && sameFinishState) {
                //数据相同：1、旧手机待办没有删除，新手机待办标记删除，再次搬家后，新手机恢复删除的待办
                //2、其他情况，不做处理，直接使用新手机中的数据。
                AppLogger.RED_MIGRATION.d(TAG, "sameContent ,check delete ${remoteData.isDelete} - ${relatedData.isDelete}")
                if (!remoteData.isDelete && relatedData.isDelete) {
                    relatedData.setIsDelete(false)
                }
                relatedData.sortTime = remoteData.sortTime
                relatedData.status = ToDo.StatusEnum.MODIFIED
                mUpdateTodoCache.add(relatedData)
                updateMergeDataToDb()
            } else {
                AppLogger.RED_MIGRATION.d(TAG, "not sameContent")
                remoteData.resetToNewState()
                mTodoCache.add(remoteData)
                insertMergeDataToDb()
            }
        }
    }

    private fun insertMergeDataToDb(immediate: Boolean = false) {
        val size = mTodoCache.size

        if ((size >= MigrationConstants.MERGE_COUNT_LIMIT) || (immediate && size > 0)) {
            toDoRepository.insertListSync(mTodoCache)
            mTodoCache.clear()
        }
    }

    private fun updateMergeDataToDb(immediate: Boolean = false) {
        val size = mUpdateTodoCache.size

        if ((size >= MigrationConstants.MERGE_COUNT_LIMIT) || (immediate && size > 0)) {
            toDoRepository.updateListSync(mUpdateTodoCache)
            mUpdateTodoCache.clear()
        }
    }

    class TodoMoveAttr {
        val createOsSdkVersion: Int = Build.VERSION.SDK_INT
    }
}