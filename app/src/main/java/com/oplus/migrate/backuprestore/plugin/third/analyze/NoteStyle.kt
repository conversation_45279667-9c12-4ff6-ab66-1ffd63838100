/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          NoteStyle.kt
 * * Description:   NoteStyle
 * * Version:       1.0
 * * Date :         2024/5/8
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/5/8     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze

import com.oplus.migrate.backuprestore.plugin.third.analyze.DataGroup.Companion.TYPE_CHECK_BOX
import com.oplus.migrate.backuprestore.plugin.third.analyze.DataGroup.Companion.TYPE_DISORDERED_LIST
import com.oplus.migrate.backuprestore.plugin.third.analyze.DataGroup.Companion.TYPE_ORDERED_LIST

object NoteStyle {

    private const val DIV = "<div>"
    private const val DIV_END = "</div>"
    private const val BR = "<br>"
    private const val ALIGN_CENTER = "<div class=\"align-center\">"
    private const val ALIGN_END = "<div class=\"align-end\">"
    private const val LIST_START = "<li>"
    private const val LIST_END = "</li>"
    private const val CHECK_BOX_START = "<li class=\"checked\">"
    private const val UNCHECK_BOX_START = "<li class=\"unchecked\">"
    private const val LIST_BULLET_START = "<ul>"
    private const val LIST_BULLET_END = "</ul>"
    private const val LIST_ORDERED_START = "<ol>"
    private const val LIST_ORDERED_END = "</ol>"

    private const val TEXT_SIZE_SMALL = "<span class=\"text-size-0.875\">"
    private const val TEXT_SIZE_MIDDLE = "<span class=\"text-size-1.0625\">"
    private const val TEXT_SIZE_BIG = "<span class=\"text-size-1.125\">"
    private const val TEXT_SIZE_LARGE = "<span class=\"text-size-1.25\">"
    private const val TEXT_BOLD = "<span class=\"text-weight-bold\">"
    private const val TEXT_ITALIC = "<span class=\"text-italic\">"
    private const val TEXT_UNDERLINE = "<span class=\"text-decoration-underline\">"
    private const val TEXT_HIGHLIGHT = "<span class=\"text-highlight-active\">"
    private const val TEXT_LINE_THROUGH = "<span class=\"text-line-through\">"

    private const val TEXT_COLOR_PREFIX = "<span class=\""
    private const val TEXT_COLOR_SUFFIX = "\">"

    private const val SPAN_END = "</span>"

    private const val IMG = "<img src=\""
    private const val WIDTH = "\" width=\""
    private const val HEIGHT = "\" height=\""
    private const val IMG_END = "\">"

    private const val TABLE_START = "<table>"
    private const val TABLE_END = "</table>"
    private const val TR_START = "<tr>"
    private const val TR_END = "</tr>"
    private const val TD_START = "<td>"
    private const val TD_END = "</td>"

    fun StringBuilder.div(): StringBuilder = insert(0, DIV).append(DIV_END)

    fun StringBuilder.br(): StringBuilder = append(BR)

    fun StringBuilder.align(alignment: String): StringBuilder = when (alignment) {
        DataGroup.ALIGN_CENTER -> insert(0, ALIGN_CENTER).append(DIV_END)
        DataGroup.ALIGN_END -> insert(0, ALIGN_END).append(DIV_END)
        else -> this
    }

    fun StringBuilder.check(type: String, text: String): StringBuilder = when {
        type == TYPE_CHECK_BOX && text == DataGroup.CHAR_CHECKED ->
            insert(0, CHECK_BOX_START).append(LIST_END).insert(0, LIST_BULLET_START).append(LIST_BULLET_END)

        type == TYPE_CHECK_BOX && text == DataGroup.CHAR_UNCHECKED ->
            insert(0, UNCHECK_BOX_START).append(LIST_END).insert(0, LIST_BULLET_START).append(LIST_BULLET_END)

        else -> this
    }

    fun StringBuilder.list(type: String): StringBuilder = when (type) {
        TYPE_ORDERED_LIST, TYPE_DISORDERED_LIST -> insert(0, LIST_START).append(LIST_END)
        else -> this
    }

    fun StringBuilder.listEnd(type: String): StringBuilder = when (type) {
        TYPE_DISORDERED_LIST -> insert(0, LIST_BULLET_START).append(LIST_BULLET_END)
        TYPE_ORDERED_LIST -> insert(0, LIST_ORDERED_START).append(LIST_ORDERED_END)
        else -> this
    }

    fun StringBuilder.textSize(size: Int): StringBuilder = when (size) {
        TextItem.TEXT_SIZE_LEVEL_SMALL -> insert(0, TEXT_SIZE_SMALL).append(SPAN_END)
        TextItem.TEXT_SIZE_LEVEL_MIDDLE -> insert(0, TEXT_SIZE_MIDDLE).append(SPAN_END)
        TextItem.TEXT_SIZE_LEVEL_BIG -> insert(0, TEXT_SIZE_BIG).append(SPAN_END)
        TextItem.TEXT_SIZE_LEVEL_LARGE -> insert(0, TEXT_SIZE_LARGE).append(SPAN_END)
        else -> this
    }

    fun StringBuilder.bold(isShow: Boolean): StringBuilder = if (isShow) {
        insert(0, TEXT_BOLD).append(SPAN_END)
    } else {
        this
    }

    fun StringBuilder.italic(isShow: Boolean): StringBuilder = if (isShow) {
        insert(0, TEXT_ITALIC).append(SPAN_END)
    } else {
        this
    }

    fun StringBuilder.underline(isShow: Boolean): StringBuilder = if (isShow) {
        insert(0, TEXT_UNDERLINE).append(SPAN_END)
    } else {
        this
    }

    fun StringBuilder.strikeThrough(isShow: Boolean): StringBuilder = if (isShow) {
        insert(0, TEXT_LINE_THROUGH).append(SPAN_END)
    } else {
        this
    }

    fun StringBuilder.highlight(isShow: Boolean): StringBuilder = if (isShow) {
        insert(0, TEXT_HIGHLIGHT).append(SPAN_END)
    } else {
        this
    }

    fun StringBuilder.img(name: String, width: String, height: String): StringBuilder {
        return append(IMG).append(name).append(WIDTH).append(width).append(HEIGHT).append(height).append(IMG_END)
    }

    fun StringBuilder.textColor(colorType: String?): StringBuilder {
        return if (colorType == null) {
            this
        } else {
            insert(0, "$TEXT_COLOR_PREFIX$colorType$TEXT_COLOR_SUFFIX").append(SPAN_END)
        }
    }

    fun StringBuilder.table(type: String): StringBuilder = if (type == DataGroup.TYPE_TABLE) {
        insert(0, TABLE_START).append(TABLE_END)
    } else {
        this
    }

    fun StringBuilder.tr(isTr: Int): StringBuilder = when (isTr) {
        TextItem.TR_START -> insert(0, TR_START)
        TextItem.TR_END -> append(TR_END)
        else -> this
    }

    fun StringBuilder.td(isTd: Boolean): StringBuilder = if (isTd) {
        insert(0, TD_START).append(TD_END)
    } else {
        this
    }
}