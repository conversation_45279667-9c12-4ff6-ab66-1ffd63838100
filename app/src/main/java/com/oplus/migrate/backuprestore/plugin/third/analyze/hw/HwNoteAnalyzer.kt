/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: HwNoteAnalyzer.kt
 * * Description:hua_wei解析器
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hw

import android.content.Context
import com.nearme.note.util.subName
import com.oplus.migrate.backuprestore.plugin.third.analyze.NoteAnalyzer
import com.oplus.note.logger.AppLogger
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.util.UUID

class HwNoteAnalyzer : NoteAnalyzer() {
    companion object {
        private const val TAG = "HwNoteAnalyzer"
        private const val TITLE_HUAWEI_DEFAULT = "notepad"
        private const val BUF_SIZE = 1024
    }

    private val analyzer by lazy { HwHtmlAnalyzer() }

    override fun analyze(context: Context, inputStream: InputStream, fileName: String) {
        val data = readFile2String(inputStream)
        if (isHtml(data)) {
            val name = fileName.subName()
            analyzer.setTitle(analyzeTitle(name))
            val noteId = UUID.randomUUID().toString()
            val title = if (isNameLong(name) || name.startsWith(TITLE_HUAWEI_DEFAULT)) "" else name
            analyzer.analyze(context, data, noteId)
            val text = analyzer.text
            val rawText = analyzer.rawText
            val images = analyzer.images
            insertRichNote(noteId, title, text.toString(), rawText.toString(), images)
        }
    }

    /**
     * 产品定的标题逻辑：标题未超过便签标题上限时正常显示，标题超过便签标题的上限时，将标题放在正文的顶部
     */
    private fun analyzeTitle(titleReplace: String): Pair<String, String> {
        var resultTitle = ""
        var resultTitleRaw = ""
        val isOutMax = isNameLong(titleReplace)
        if (isOutMax || titleReplace.startsWith(TITLE_HUAWEI_DEFAULT)) {
            AppLogger.BASIC.d(TAG, "getTitle default $titleReplace,isOutMax:$isOutMax")
            if (isOutMax) {
                resultTitle = titleReplace
                resultTitleRaw = "${getRawTitle(titleReplace)}${HwHtmlFormats.START_BR}"
            }
        }
        return Pair(resultTitle, resultTitleRaw)
    }

    private fun getRawTitle(title: String): String {
        return "${HwHtmlFormats.START_DIV}$title${HwHtmlFormats.END_DIV}"
    }

    private fun readFile2String(inputStream: InputStream): String {
        val baos = ByteArrayOutputStream()
        inputStream.use { reader ->
            val buffer = ByteArray(BUF_SIZE)
            var len: Int
            while (reader.read(buffer).also { len = it } >= 0) {
                baos.write(buffer, 0, len)
            }
        }
        return baos.toString()
    }

    private fun isHtml(data: String): Boolean {
        val start = data.startsWith("<!DOCTYPE html>") || data.startsWith("<html>")
        val end = data.endsWith("</html>")
        return if (start && end) {
            true
        } else {
            AppLogger.BASIC.d(TAG, "data is not html:$data")
            false
        }
    }
}