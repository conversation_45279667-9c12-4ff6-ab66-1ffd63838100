package com.oplus.migrate.backuprestore.plugin

import com.oplus.note.logger.AppLogger

object MigrationConstants {
    const val NOTE_FOLDER: String = "Note"
    const val FILE_TODO: String = "todo"
    const val FILE_NOTE: String = "note"
    const val FILE_FOLDER: String = "folder"
    const val FILE_NOTE_ATTRIBUTE: String = "note_attribute"
    const val FILE_RICH_NOTE: String = "rich_note"
    const val FILE_ATTACHMENT: String = "attachment"
    const val FILE_WORD: String = "word"
    const val FILE_SKIN: String = "skin"
    const val FILE_FILE_BACKUP: String = "files/file_backup.zip"
    const val FILE_SHARE_PREFERENCE: String = "share_preference"
    const val FILE_DATA_SOURCE_INFO: String = "data_source_info"
    const val FILE_SPEECH_LOG = "speech_log"
    const val FILE_ENCRYPT_FOLDER = "encrypt_folder"
    const val FILE_ENCRYPT_RICH_NOTE = "encrypt_rich_note"
    const val FILE_NO_FOLDER_RICH_NOTE = "no_folder_rich_note"
    // 待办属性文件名定义
    const val FILE_TODO_ATTR: String = "todo_attr"

    const val SP_NAME_MIGRATION = "note_migration"
    const val SP_KEY_PENDING_UPDATE_SKIN = "note_migration_key_update_skin"
    const val SP_KEY_PENDING_DOWNLOAD_SKIN = "note_migration_key_download_skin"

    const val MERGE_COUNT_LIMIT: Int = 500

    //返回的错误码
    const val BR_ERROR_CODE = "fail_reason"
    const val BR_ERROR_DESCRIPTION = "fail_description"
    const val ERROR_CODE_UNKNOWN = -1//未知错误
    const val ERROR_CODE_FAILD_TO_ENSURE_DIRECTORY = 100 //解压缩文件时目录不存在且创建失败
    const val ERROR_CODE_FAILD_TO_UNZIP_FILE = 101 //文件解压缩失败
    const val ERROR_CODE_FAILD_TO_ZIP_FILE = 102 //文件压缩失败
    const val ERROR_CODE_TODO_MOVER_IO_EXCEPTION = 103 //待办事项备份失败
    const val ERROR_CODE_TODO_MOVER_GETCONTENTFROMFILE_FAIL = 104//代办事项恢复异常，获取内容为空
    const val ERROR_CODE_TODO_MOVER_FAILD_TO_RESOLVE_GSON = 105 //代办事项解析gson字符串失败
    const val ERROR_CODE_FOLDER_MOVER_FAILD_TO_RESOLVE_GSON = 106//文件夹解析gson字符串失败
    const val ERROR_CODE_FOLDER_MOVER_GETCONTENTFROMFILE_FAIL = 107 //文件夹恢复异常，获取内容为空
    const val ERROR_CODE_FOLDER_MOVER_BACKUP_FAIL = 108 //文件夹备份失败
    const val ERROR_CODE_FOLDER_MOVER_RESTORE_FAIL = 109 //文件夹恢复失败
    const val ERROR_CODE_SKIN_MOVER_BACKUP_FAIL = 110 //皮肤备份失败
    const val ERROR_CODE_SKIN_MOVER_GETCONTENTFROMFILE_FAIL = 111 //皮肤恢复异常，获取内容为空
    const val ERROR_CODE_SKIN_MOVER_FAILD_TO_RESOLVE_GSON = 112 //皮肤解析gson字符串失败
    const val ERROR_CODE_SP_MOVER_BACKUP_FAILD_TO_FOUND_FILE = 113 //SpMover备份失败，找不到文件
    const val ERROR_CODE_SP_MOVER_BACKUP_IO_EXCEPTION = 114 //SpMover备份失败，读写异常
    const val ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_FILE = 115 //SpMover恢复失败，找不到文件
    const val ERROR_CODE_SP_MOVER_RESTORE_IO_EXCEPTION = 116 //SpMover恢复失败，读写异常
    const val ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_CLASS = 117 //SpMover恢复异常，找不到类
    const val ERROR_CODE_DATASOURCEINFOMOVER_BACKUP_FAIL = 118 //DataSourceInfoMover备份失败，读写异常
    const val ERROR_CODE_ENCRYPT_FOLDER_MOVER_RESTORE_FAIL = 119//EncryptFolderMover恢复失败
    const val ERROR_CODE_ENCRYPT_FOLDER_MOVER_BACKUP_FAIL = 120 //EncryptFolderMover备份失败
    const val ERROR_CODE_RICH_NOTE_MOVER_BACKUP_FAIL = 121 //RichNoteMover备份失败
    const val ERROR_CODE_FAILD_TO_BACKUP_CONTACT = 122 //RichNoteMover备份联系人卡片失败
    const val ERROR_CODE_FAILD_TO_BACKUP_SCHEDULE = 123 //RichNoteMover备份日程卡片失败
    const val ERROR_CODE_RICH_NOTE_MOVER_RESTORE_RESOLVE_JSON = 124 //RichNoteMover恢复解析gson字符串失败
    const val ERROR_CODE_RICH_NOTE_MOVER_RESTORE_SPEECH_LOG = 125 //RichNoteMover恢复speechLogExtra失败
    const val ERROR_CODE_PARA_STYLE_MOVER_BACKUP_FAIL = 126 //段落样式备份失败
    const val ERROR_CODE_PARA_STYLE_MOVER_GETCONTENTFROMFILE_FAIL = 127 //段落样式恢复异常，获取内容为空
    const val ERROR_CODE_PARA_STYLE_MOVER_FAILD_TO_RESOLVE_GSON = 128 //段落样式解析gson字符串失败

    private val errorCodes = mapOf(
        ERROR_CODE_UNKNOWN to "未知错误",
        ERROR_CODE_FAILD_TO_ENSURE_DIRECTORY to "解压缩文件时目录不存在且创建失败",
        ERROR_CODE_FAILD_TO_UNZIP_FILE to "文件解压缩失败",
        ERROR_CODE_FAILD_TO_ZIP_FILE to "文件压缩失败",
        ERROR_CODE_TODO_MOVER_IO_EXCEPTION to "待办事项备份失败",
        ERROR_CODE_TODO_MOVER_GETCONTENTFROMFILE_FAIL to "代办事项恢复异常，获取内容为空",
        ERROR_CODE_TODO_MOVER_FAILD_TO_RESOLVE_GSON to "代办事项解析gson字符串失败",
        ERROR_CODE_FOLDER_MOVER_FAILD_TO_RESOLVE_GSON to "文件夹解析gson字符串失败",
        ERROR_CODE_FOLDER_MOVER_GETCONTENTFROMFILE_FAIL to "文件夹恢复异常，获取内容为空",
        ERROR_CODE_FOLDER_MOVER_BACKUP_FAIL to "文件夹备份失败",
        ERROR_CODE_FOLDER_MOVER_RESTORE_FAIL to "文件夹恢复失败",
        ERROR_CODE_SKIN_MOVER_BACKUP_FAIL to "皮肤备份失败",
        ERROR_CODE_SKIN_MOVER_GETCONTENTFROMFILE_FAIL to "皮肤恢复异常，获取内容为空",
        ERROR_CODE_SKIN_MOVER_FAILD_TO_RESOLVE_GSON to "皮肤解析gson字符串失败",
        ERROR_CODE_SP_MOVER_BACKUP_FAILD_TO_FOUND_FILE to "SpMover备份失败，找不到文件",
        ERROR_CODE_SP_MOVER_BACKUP_IO_EXCEPTION to "SpMover备份失败，读写异常",
        ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_FILE to "SpMover恢复失败，找不到文件",
        ERROR_CODE_SP_MOVER_RESTORE_IO_EXCEPTION to "SpMover恢复失败，读写异常",
        ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_CLASS to "SpMover恢复异常，找不到类",
        ERROR_CODE_DATASOURCEINFOMOVER_BACKUP_FAIL to "DataSourceInfoMover备份失败，读写异常",
        ERROR_CODE_ENCRYPT_FOLDER_MOVER_RESTORE_FAIL to "EncryptFolderMover恢复失败",
        ERROR_CODE_ENCRYPT_FOLDER_MOVER_BACKUP_FAIL to "EncryptFolderMover备份失败",
        ERROR_CODE_RICH_NOTE_MOVER_BACKUP_FAIL to "RichNoteMover备份失败",
        ERROR_CODE_FAILD_TO_BACKUP_CONTACT to "RichNoteMover备份联系人卡片失败",
        ERROR_CODE_FAILD_TO_BACKUP_SCHEDULE to "RichNoteMover备份日程卡片失败",
        ERROR_CODE_RICH_NOTE_MOVER_RESTORE_RESOLVE_JSON to "RichNoteMover恢复解析gson字符串失败",
        ERROR_CODE_RICH_NOTE_MOVER_RESTORE_SPEECH_LOG to "RichNoteMover恢复speechLogExtra失败",
        ERROR_CODE_PARA_STYLE_MOVER_BACKUP_FAIL to "段落样式备份失败",
        ERROR_CODE_PARA_STYLE_MOVER_GETCONTENTFROMFILE_FAIL to "段落样式恢复异常，获取内容为空",
        ERROR_CODE_PARA_STYLE_MOVER_FAILD_TO_RESOLVE_GSON to "段落样式解析gson字符串失败",
    )

    fun getFailCodeAndDescription(plugin: Any, code: Int) {
        when (plugin) {
            NoteBackupPlugin -> {
                NoteBackupPlugin.isSuccess = false
                NoteBackupPlugin.resultCode = code
                NoteBackupPlugin.resultDescription = errorCodes[code] ?: "未知错误"
            }

            NoteRestorePlugin -> {
                NoteRestorePlugin.isSuccess = false
                NoteRestorePlugin.resultCode = code
                NoteRestorePlugin.resultDescription = errorCodes[code] ?: "未知错误"
            }

            else -> AppLogger.BR.e("MigrationConstants", "error plugin type")
        }
    }


    // 备份数据的数量，这里没有算todo和note的具体数量
    // 备份data/file, 数据库表todo, note, rich_note, folder, note_attribute, attachment, words, note_skin, shared_preference
    const val BACKUP_MAX_COUNT: Int = 9

    const val MOVER_RICH_NOTE = "MOVER_RICH_NOTE"
    const val MOVER_TODO = "MOVER_TODO"
    const val MOVER_FOLDER = "MOVER_FOLDER"
    const val MOVER_SKIN = "MOVER_SKIN"
    const val MOVER_FILE = "MOVER_FILE"
    const val MOVER_SP = "MOVER_SP"
    const val MOVER_DATA_SOURCE_INFO = "MOVER_DATA_SOURCE_INFO"
    const val MOVER_ENCRYPT_FOLDER = "MOVER_ENCRYPT_FOLDER"
    const val MOVER_ENCRYPT_RICH_NOTE = "MOVER_ENCRYPT_RICH_NOTE"
    const val MOVE_NO_FOLDER_RICH_NOTE = "MOVER_NO_FOLDER_RICH_NOTE"
    const val MOVE_PARA_STYLE = "MOVER_PARA_STYLE"

    //控制需要备份的模块，后期需要扩展时需要增加新类型到数组中。
    val MOVER_LIST: Array<String> = arrayOf(
        MOVER_SP,
        MOVER_FILE,
        MOVER_RICH_NOTE,
        MOVER_TODO,
        MOVER_FOLDER,
        MOVER_SKIN,
        MOVER_DATA_SOURCE_INFO,
        MOVER_ENCRYPT_FOLDER,
        MOVER_ENCRYPT_RICH_NOTE,
        MOVE_NO_FOLDER_RICH_NOTE,
        MOVE_PARA_STYLE
    )
}