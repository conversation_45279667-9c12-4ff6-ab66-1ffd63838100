/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - NoteXMLParser
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import android.text.TextUtils
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNoteExtra
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.skin.SkinData
import com.oplus.note.logger.AppLogger
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserException
import org.xmlpull.v1.XmlPullParserFactory
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.IOException
import java.lang.Boolean
import java.util.*

object NoteXmlParser {
    fun parseRichNoteContent(richnoteBackupXmlFile: File?): List<RichNoteWithAttachments> {
        val noteList: MutableList<RichNoteWithAttachments> = ArrayList()
        var `is`: FileInputStream? = null
        try {
            `is` = FileInputStream(richnoteBackupXmlFile)
            val parser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(`is`, null)
            var eventType = parser.eventType
            var richNote: RichNote? = null
            var attachments: MutableList<Attachment?>? = null
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        val tag = parser.name
                        if (NoteXmlTagConstant.NOTE_TAG == tag) {
                            attachments = ArrayList()
                            richNote = fillRichNote(parser)
                        } else if (NoteXmlTagConstant.ATTACHMENT_TAG == tag) {
                            val attachment = fillAttachmentInfo(parser)
                            attachments!!.add(attachment)
                        }
                    }
                    XmlPullParser.END_TAG -> {
                        val tag = parser.name
                        if (NoteXmlTagConstant.NOTE_TAG == tag) {
                            val richNoteWithAttachments = RichNoteWithAttachments(richNote!!, attachments as List<Attachment>)
                            noteList.add(richNoteWithAttachments)
                        }
                    }
                    else -> {
                    }
                }
                eventType = parser.next()
            }
        } catch (e: FileNotFoundException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRichNoteContent -> ${e.message}")
        } catch (e: XmlPullParserException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRichNoteContent -> ${e.message}")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRichNoteContent -> ${e.message}")
        } finally {
            if (`is` != null) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    AppLogger.RED_MIGRATION.e(TAG, "parseRichNoteContent -> ${e.message}")
                }
            }
        }
        return noteList
    }

    private fun fillRichNote(parser: XmlPullParser): RichNote {
        var localId = ""
        var globalId: String? = UUID.randomUUID().toString()
        var text = ""
        var rawText = ""
        var folderGuid: String = FolderInfo.FOLDER_GUID_NO_GUID
        var timestamp = 0L
        var createTime = 0L
        var updateTime: Long = 0
        var topTime: Long = 0
        var recycleTime: Long = 0
        var alarmTime: Long = 0
        var state: Int = RichNote.STATE_NEW
        var deleted = false
        var skinId: String = SkinData.COLOR_SKIN_WHITE
        var title: String? = null
        var rawTitle: String? = null
        var recycleTimePre: Long? = null
        var alarmTimePre: Long? = null
        var skinIdPre: String? = null
        var extra: RichNoteExtra? = null
        var version = 0
        var packageName: String? = null

        val attrNum = parser.attributeCount
        var i = 0
        while (i < attrNum) {
            val key = parser.getAttributeName(i)
            val value = parser.getAttributeValue(i)
            when (key) {
                "local_id" -> localId = value
                "global_id" -> globalId = value
                "text" -> text = value
                "raw_text" -> rawText = value
                "folder_id" -> folderGuid = value
                "timestamp" -> timestamp = value.toLong()
                "create_time" -> createTime = value.toLong()
                "update_time" -> updateTime = value.toLong()
                "top_time" -> topTime = value.toLong()
                "recycle_time" -> recycleTime = value.toLong()
                "alarm_time" -> alarmTime = java.lang.Long.valueOf(value)
                "state" -> state = value.toInt()
                "deleted" -> deleted = Boolean.parseBoolean(value)
                "title" -> title = value
                "raw_title" -> rawTitle = value
                "recycle_time_pre" -> recycleTimePre = java.lang.Long.valueOf(value)
                "alarm_time_pre" -> alarmTimePre = java.lang.Long.valueOf(value)
                //"extra" -> richNote.extra = value
                "version" -> version = value.toInt()
                "from_package" -> packageName = value
            }
            i++
        }
        return RichNote(
            localId, globalId, text, rawText,"", folderGuid, timestamp, createTime, updateTime, topTime, recycleTime,
            alarmTime, state, deleted, skinId, title, rawTitle, recycleTimePre, alarmTimePre, skinIdPre, extra, version,
            packageName = packageName
        )
    }

    private fun fillAttachmentInfo(parser: XmlPullParser): Attachment {
        var attachmentId = ""
        var richNoteId = ""
        var type = 0
        var state: Int = Attachment.STATE_NEW
        var md5: String? = null
        var url: String? = null

        var width = 0
        var height = 0
        val attachmentAttrNum = parser.attributeCount
        var j = 0
        var hasPicture = false
        while (j < attachmentAttrNum) {
            val key = parser.getAttributeName(j)
            val value = parser.getAttributeValue(j)
            when (key) {
                "attachment_id" -> attachmentId = value
                "rich_note_id" -> richNoteId = value
                "type" -> type = value.toInt()
                "state" -> state = value.toInt()
                "md5" -> md5 = value
                "url" -> url = value
                "width" -> if (!TextUtils.isEmpty(value)) {
                    hasPicture = true
                    width = value.toInt()
                }
                "height" -> if (!TextUtils.isEmpty(value)) {
                    hasPicture = true
                    height = value.toInt()
                }
            }
            j++
        }
        val attachment = Attachment(attachmentId, richNoteId, type, state, md5, url)
        if (hasPicture) {
            val picture = Picture(width, height)
            attachment.picture = picture
        }
        return attachment
    }

    fun parseRestoreCount(file: File?): Int {
        var count = 0
        var `is`: FileInputStream? = null
        try {
            `is` = FileInputStream(file)
            val parser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(`is`, null)
            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        val tag = parser.name
                        if (NoteXmlTagConstant.NOTE_COUNT_TAG == tag) {
                            count = parser.getAttributeValue(0).toInt()
                        }
                    }
                    XmlPullParser.END_TAG -> {
                    }
                    else -> {
                    }
                }
                if (0 != count) {
                    //LogUtils.d(TAG, "count read success,break! count = " + count);
                    break
                }
                eventType = parser.next()
            }
        } catch (e: FileNotFoundException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount -> ${e.message}")
        } catch (e: XmlPullParserException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount -> ${e.message}")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount -> ${e.message}")
        } finally {
            if (null != `is`) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount -> ${e.message}")
                }
            }
        }
        return count
    }

    const val TAG = "NoteXmlParser"
}