package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import android.os.Build
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.nearme.note.util.FileUtil
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.note.BuildConfig
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import java.io.File

class DataSourceInfoMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) :
    Mover(context, backupFilePath, plugin) {

    private val TAG = "DataSourceInfoMover"

    @Suppress("TooGenericExceptionCaught")
    override fun onBackup() {
        val backupPath: String = backupFilePath + File.separator + MigrationConstants.FILE_DATA_SOURCE_INFO
        val sourceInfo = createSourceInfo()
        AppLogger.BR.d(TAG, "onBackup sourceInfo=$sourceInfo")
        kotlin.runCatching {
            FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), sourceInfo)
        }.onFailure {
            AppLogger.BR.e(TAG, "onBackup DataSourceInfoMover failed", it)
            MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_DATASOURCEINFOMOVER_BACKUP_FAIL)
        }
    }

    @VisibleForTesting
    fun createSourceInfo(): String {
        val pkg = BuildConfig.APPLICATION_ID
        val versionName = BuildConfig.VERSION_NAME
        val versionCode = BuildConfig.VERSION_CODE
        val androidVersion = Build.VERSION.SDK_INT
        val oplusRomVersion = OplusBuildProxy.getOsVersion()
        return Gson().toJson(DataSourceInfo(pkg, versionName, versionCode, androidVersion, oplusRomVersion))
    }

    override fun onRestore(isNotBrSdkGenerateBackupData: Boolean) {
        AppLogger.BR.d(TAG, "onRestore: $isNotBrSdkGenerateBackupData")
    }
}