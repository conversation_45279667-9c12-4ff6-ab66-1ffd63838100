/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: HwHtmlFormats.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hw

object HwHtmlFormats {
    const val BODY = "body"
    const val DIV = "div"
    const val BR = "br"
    const val UL = "ul"
    const val LI = "li"
    const val B = "b"
    const val I = "i"
    const val U = "u"
    const val FONT = "font"
    const val OL = "ol"
    const val IMG = "img"
    const val TEXT = "#text"
    const val INPUT = "input"

    const val CHECKED = "checked"
    const val UNCHECKED = "unchecked"
    const val ALIGN = "align"
    const val RIGHT = "right"
    const val CENTER = "center"
    const val WIDTH = "width"
    const val HEIGHT = "height"

    const val END_SPAN = "</span></div>"
    const val END_INPUT = "</li></ul>"
    const val END_LI = "</li>"
    const val END_OL = "</ol>"
    const val END_UL = "</ul>"
    const val END_DIV = "</div>"
    const val END_IMG = "/>"
    const val START_SPAN = "<span class=\""
    const val START_INPUT = "<ul><li class="
    const val START_LI = "<li>"
    const val START_OL = "<ol>"
    const val START_UL = "<ul>"
    const val START_DIV = "<div>"
    const val START_DIV_START = "<div class=\"align-start\">"
    const val START_DIV_END = "<div class=\"align-end\">"
    const val START_DIV_CENTER = "<div class=\"align-center\">"
    const val START_BR = "<br>"

    const val HTML = ".html"
    const val FIND_DIV = "<div"
    const val END = ">"
    const val SRC = "src=\""
    const val BRACKET = "\">"
    const val HTML_IMG_START = "<img src="
    const val TEXT_SIZE_PREFIX = "text-size-"
    const val TEXT_WEIGHT_BOLD = "text-weight-bold"
    const val TEXT_ITALIC = "text-italic"
    const val TEXT_DECORATION_UNDERLINE = "text-decoration-underline"
    const val TEXT_HIGHLIGHT_ACTIVE = "text-highlight-active"
}