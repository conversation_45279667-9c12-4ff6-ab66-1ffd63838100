/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - NoteXmlConstant
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

object NoteXmlTagConstant {
    const val NOTE_ROOT_TAG = "note"
    const val NOTE_COUNT_TAG = "noteCount"
    const val NOTE_COUNT = "count"
    const val NOTE_TAG = "richNoteRecord"
    const val NOTE_APP_VERSION_TAG = "noteAppInfo"
    const val APP_VERSION_NAME = "versionName"
    const val APP_VERSION_CODE = "versionCode"
    const val ATTACHMENT_TAG = "noteAttachment"
}