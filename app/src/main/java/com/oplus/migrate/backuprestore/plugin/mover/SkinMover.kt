package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.nearme.note.db.AppDatabase
import com.nearme.note.migration.MigrationConstants
import com.nearme.note.migration.MigrationConstants.SP_KEY_PENDING_DOWNLOAD_SKIN
import com.nearme.note.migration.MigrationConstants.SP_KEY_PENDING_UPDATE_SKIN
import com.nearme.note.migration.MigrationConstants.SP_NAME_MIGRATION
import com.nearme.note.util.FileUtil
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.downloader.util.DeviceUtil
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.skin.api.SkinContent
import com.oplus.note.repo.skin.bean.SkinSummary
import com.oplus.note.utils.SharedPreferencesUtil
import java.io.File
import java.lang.reflect.Modifier
import java.lang.reflect.Type

class SkinMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {

    companion object {
        const val TAG = "SkinMover"
    }

    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup Skin table")
        val list: List<SkinSummary> = AppDatabase.getInstance().skinDao().getAllData()
        // 只备份已下载的皮肤
        val downloadedSkinList = filterDownloadedSkin(list)
        val listStr =
                if (downloadedSkinList.isNullOrEmpty()) {
                    AppLogger.BR.w(TAG, "onBackup Skin.isNullOrEmpty()")
                    "[]"
                } else {
                    GsonBuilder().excludeFieldsWithModifiers(Modifier.STATIC)
                            .create().toJson(downloadedSkinList)
                }
        val backupPath: String = backupFilePath + File.separator + MigrationConstants.FILE_SKIN
        AppLogger.BR.d(TAG, "onBackup saveToFile, path = $backupPath, content = $listStr")
        val isSuccess = FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        if (!isSuccess) {
            AppLogger.BR.e(TAG, "[FileUtil]saveToFile failed")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteBackupPlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_SKIN_MOVER_BACKUP_FAIL
            )
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }
        val filePath: String = backupFilePath + File.separator + MigrationConstants.FILE_SKIN
        AppLogger.BR.d(TAG, "onRestore skin list: ${MigrationConstants.FILE_SKIN}")
        FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
            val listType: Type = object : TypeToken<List<SkinSummary>>(){}.type
            var skinList: List<SkinSummary>? = null
            kotlin.runCatching {
                skinList = GsonBuilder()
                    .excludeFieldsWithModifiers(Modifier.STATIC)
                    .create().fromJson(this, listType)
            }.onFailure {
                AppLogger.BR.e(TAG, "onRestore skinList error", it)
                NoteRestorePlugin.isSuccess = false
                NoteRestorePlugin.resultCode =
                    com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_SKIN_MOVER_FAILD_TO_RESOLVE_GSON
            }
            if (skinList.isNullOrEmpty()) {
                AppLogger.BR.w(TAG, "onRestore skinList.isNullOrEmpty()")
            } else {
                AppLogger.BR.w(TAG, "onRestore skinList.size ${skinList?.size}")
                saveSkinsToLocal(skinList ?: emptyList())
            }
        } ?: run {
            AppLogger.BR.w(TAG, "onRestore SkinMover getContentFromFile is null")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteRestorePlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_SKIN_MOVER_GETCONTENTFROMFILE_FAIL
            )
        }
    }

    private fun filterDownloadedSkin(list: List<SkinSummary>) : List<SkinSummary> {
        val result = mutableListOf<SkinSummary>()
        list.forEach { skin ->
            if (skin.isDownloaded()) {
                result.add(skin)
            }
        }
        return result
    }

    private fun saveSkinsToLocal(skinList: List<SkinSummary>) {
        val pendingUpdateSkins = mutableListOf<SkinSummary>()
        val pendingDownloadSkins = mutableListOf<SkinSummary>()
        skinList.forEach { skin ->
            val condition = DeviceUtil.getUsingCondition()
            AppLogger.BR.d(TAG, "saveSkinsToLocal skin data1 : ${skin.data1}, condition : ${skin.condition}, " +
                "deviceUtil condition : $condition")
            if (skin.data1.isNotEmpty() && skin.data1 == SkinContent.SKIN_AID_VERSION) {
                if (skin.condition == condition) {
                    // 如果旧手机data1、condition和新手机一样, 只更新皮肤
                    pendingUpdateSkins.add(skin)

                    //将相同分辨率的皮肤表插入本机数据表中
                    AppDatabase.getInstance().skinDao().insertSkinSummary(skin)
                } else {
                    // 如果旧手机data1、condition和新手机不一样, 需要下载对应data1、condition的皮肤
                    skin.condition = condition
                    pendingDownloadSkins.add(skin)
                }
            }
        }

        SharedPreferencesUtil.getInstance().apply {
            val gson = GsonBuilder()
                    .excludeFieldsWithModifiers(Modifier.STATIC)
                    .create()
            putString(MyApplication.appContext, SP_NAME_MIGRATION, SP_KEY_PENDING_UPDATE_SKIN, gson.toJson(pendingUpdateSkins))
            putString(MyApplication.appContext, SP_NAME_MIGRATION, SP_KEY_PENDING_DOWNLOAD_SKIN, gson.toJson(pendingDownloadSkins))
        }
    }
}