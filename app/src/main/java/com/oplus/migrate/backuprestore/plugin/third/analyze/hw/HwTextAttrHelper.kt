/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: TextAttrHelper.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hw

import com.oplus.migrate.backuprestore.plugin.third.analyze.ConvertUtils
import org.jsoup.nodes.Attribute
import org.jsoup.nodes.Attributes
import org.jsoup.nodes.Node

object HwTextAttrHelper {
    private const val HUAWEI_FONT_SIZE = "font-size"
    private const val HUAWEI_FONT_COLOR = "color"
    private const val HUAWEI_SIZE_SMALL = "$HUAWEI_FONT_SIZE:85.0%;"
    private const val HUAWEI_SIZE_BIG = "$HUAWEI_FONT_SIZE:115.0%;"
    private const val HUAWEI_SIZE_OVER_BIG = "$HUAWEI_FONT_SIZE:130.0%;"
    private const val HUAWEI_SIZE_MAX_BIG = "$HUAWEI_FONT_SIZE:145.0%;"
    private const val SIZE_MAX_BIG = "1.25"
    private const val SIZE_OVER_BIG = "1.125"
    private const val SIZE_BIG = "1.0625"
    private const val SIZE_NORMAL = "1.0"
    private const val SIZE_SMALL = "0.875"

    fun getTextColorOrHighlight(node: Node): String? {
        val colorAttr = getAttribute(node.attributes(), HUAWEI_FONT_COLOR)
        if (colorAttr != null) {
            val color = colorAttr.value
            return ConvertUtils.convertTextColorToColorType(color) ?: HwHtmlFormats.TEXT_HIGHLIGHT_ACTIVE
        }
        return null
    }

    /**
     * 转化Huawei的textSize
     */
    fun getSizeFormHuawei(node: Node): String? {
        val size = getSizeFromAttr(node.attributes(), HUAWEI_FONT_SIZE)
        return size?.let {
            when {
                it.contains(HUAWEI_SIZE_SMALL) -> SIZE_SMALL
                it.contains(HUAWEI_SIZE_BIG) -> SIZE_BIG
                it.contains(HUAWEI_SIZE_OVER_BIG) -> SIZE_OVER_BIG
                it.contains(HUAWEI_SIZE_MAX_BIG) -> SIZE_MAX_BIG
                else -> SIZE_NORMAL
            }
        }
    }

    private fun haveColor(node: Node): Boolean {
        val attribute = getAttribute(node.attributes(), HUAWEI_FONT_COLOR)
        return attribute != null
    }

    private fun getAttribute(attrs: Attributes, name: String): Attribute? {
        return attrs.find { it.key == name }
    }

    private fun getSizeFromAttr(attrs: Attributes, name: String): String? {
        for (attr in attrs) {
            val value = attr.value
            if (value.startsWith(name)) {
                return value
            }
        }
        return null
    }
}