/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - BackupRestoreUtil
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import android.os.Bundle
import android.text.TextUtils
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.RichNoteExtra
import com.oplus.note.repo.note.entity.Attachment
import com.nearme.note.model.RichNoteRepository.findAllUserNote
import com.nearme.note.model.RichNoteRepository.insertList
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.utils.FileUtil
import com.oplus.migrate.utils.OldDataRestoreUtil
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

object BackupRestoreUtil {
    const val TAG = "BackupRestoreUtil"

    /**
     * create backup file in data/data/com.xxx.note/BackupRestore
     * xml:        data/data/com.xxx.note/BackupRestore/Richnote.xml
     * attachment: data/data/com.xxx.note/BackupRestore/attachments/
     */
    /*static String BACKUPRESTORE_FOLDER = BaseApp.Companion.getBaseApp().getFilesDir().getParentFile() + File.separator + "BackupRestore";
    static String RICHNOTE_BACKUP_XML_FILE = BACKUPRESTORE_FOLDER + File.separator +"Richnote.xml";
    static String RICHNOTE_ATTACHMENT_FOLDER = BACKUPRESTORE_FOLDER + File.separator + "attachments";*/
    lateinit var RICHNOTE_BACKUP_XML_FILE: String
    lateinit var RICHNOTE_ATTACHMENT_FOLDER: String
    private fun backup() {
        val list = findAllUserNote()
        if (list.isEmpty()) {
            AppLogger.RED_MIGRATION.w(TAG, "backup RichNoteWithAttachments list empty")
            return
        }
        backupRichNoteContent(list)
        backupRichNoteAttachments()
    }

    private fun restore(): Bundle? {
        val isOldBackupData = OldDataRestoreUtil.isOldBackupData(RICHNOTE_BACKUP_XML_FILE)
        AppLogger.RED_MIGRATION.d(TAG, "isOldBackupData = $isOldBackupData")
        if (isOldBackupData) {
            return OldDataRestoreUtil.restore(RICHNOTE_BACKUP_XML_FILE)
        } else {
            val bundle = Bundle()
            bundle.putBoolean(BackupRestoreConstant.IS_OLD_NOTE_BACKUP_DATA, false)
            restoreRichNoteContent()
            restoreRichNoteAttachments()
            return bundle
        }
    }

    private fun restoreRichNoteAttachments() {
        val srcFile = File(RICHNOTE_ATTACHMENT_FOLDER)
        val destFile = MyApplication.appContext.filesDir
        if (!destFile.exists()) {
            destFile.mkdir()
        }
        FileUtil.copyFiles(srcFile, destFile)
        AppLogger.RED_MIGRATION.d(TAG, "restoreRichNoteAttachments done")
    }

    private fun restoreRichNoteContent() {
        val notes = NoteXmlParser.parseRichNoteContent(File(RICHNOTE_BACKUP_XML_FILE))
        insertList(notes)
        AppLogger.RED_MIGRATION.d(TAG, "restoreRichNoteContent done")
    }

    private fun backupRichNoteAttachments() {
        val srcFile = MyApplication.appContext.filesDir
        val destFile = File(RICHNOTE_ATTACHMENT_FOLDER)
        if (!destFile.exists()) {
            destFile.mkdir()
        }
        FileUtil.copyFiles(srcFile, destFile)
        AppLogger.RED_MIGRATION.d(TAG, "backupRichNoteAttachments done")
    }

    private fun backupRichNoteContent(list: List<RichNoteWithAttachments>) {
        val noteXML = NoteXmlComposer()
        noteXML.startCompose()
        noteXML.addNoteAppInfo()
        noteXML.addNoteCount(list.size)
        for (i in list.indices) {
            val richNoteWithAttachments = list[i]

            richNoteWithAttachments.attachments?.let {

                var paints: ArrayList<Attachment> =
                    mutableListOf<Attachment>() as ArrayList<Attachment>
                var voices: ArrayList<Attachment> =
                    mutableListOf<Attachment>() as ArrayList<Attachment>
                var coverPaints: ArrayList<Attachment> =
                    mutableListOf<Attachment>() as ArrayList<Attachment>
//                var coverPictures: ArrayList<Attachment> =
//                    mutableListOf<Attachment>() as ArrayList<Attachment>
//                var pageResults: ArrayList<PageResult> = mutableListOf<PageResult>() as ArrayList<PageResult>
                var subAttachments = richNoteWithAttachments.attachments
                if (subAttachments != null) {
                    for (attachment in subAttachments) {
                        if (attachment.type == Attachment.TYPE_VOICE) {
                            voices.add(attachment)
                            subAttachments -= attachment
                        } else if (attachment.type == Attachment.TYPE_PAINT) {
                            paints.add(attachment)
                            subAttachments -= attachment
                        } else if (attachment.type == Attachment.TYPE_COVER_PAINT) {
                            coverPaints.add(attachment)
                            subAttachments -= attachment
                        }
//                        else if (attachment.type == Attachment.TYPE_COVER_PICTURE) {
//                            coverPictures.add(attachment)
//                            subAttachments -= attachment
//                        }
                    }
                }
//                pageResults = richNoteWithAttachments.richNote.extra?.pageResults as ArrayList<PageResult>
                val newExtra = RichNoteExtra.updateAttachmentBackup(
                    richNoteWithAttachments.richNote.extra,
                    paints,
                    voices,
                    coverPaints,
//                    coverPictures,
//                    pageResults = pageResults

                )
                richNoteWithAttachments.richNote.extra = newExtra
                if (!subAttachments.isNullOrEmpty()) {
                    subAttachments.also { richNoteWithAttachments.attachments = it }
                }
                AppLogger.RED_MIGRATION.w(TAG, "backupRichNote newExtra = $newExtra")
            }
            AppLogger.RED_MIGRATION.w(TAG, "richNoteWithAttachments = ${richNoteWithAttachments.attachments}")
            noteXML.addOneNoteRecord(richNoteWithAttachments)
        }
        noteXML.endCompose()
        val noteXmlInfo = noteXML.xmlInfo
        if (noteXmlInfo != null && !TextUtils.isEmpty(RICHNOTE_BACKUP_XML_FILE)) {
            try {
                writeToFile(RICHNOTE_BACKUP_XML_FILE, noteXmlInfo.toByteArray())
            } catch (e: IOException) {
                AppLogger.RED_MIGRATION.e(TAG, "backupRichNoteContent -> ${e.message}")
            }
        }
        AppLogger.RED_MIGRATION.d(TAG, "backupRichNoteContent done")
    }

    @Throws(IOException::class)
    private fun writeToFile(fileName: String?, bytes: ByteArray) {
        val file = File(fileName)
        if (!file.parentFile.exists()) {
            file.parentFile.mkdirs()
            if (!file.exists()) {
                file.createNewFile()
            }
        }
        var outStream: FileOutputStream? = null
        try {
            outStream = FileOutputStream(fileName)
            outStream.write(bytes, 0, bytes.size)
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "writeToFile error: $e")
        } finally {
            if (null != outStream) {
                outStream.flush()
                outStream.close()
            }
        }
    }

    // backuprestore begin
    fun copyFolder(src: String?, dest: String?): Bundle {
        AppLogger.RED_MIGRATION.d(TAG, "copyFolder src = $src")
        AppLogger.RED_MIGRATION.d(TAG, "copyFolder dest = $dest")
        var isSuccess = true
        try {
            val files = File(src).listFiles()
            files?.let {
                for (f in files) {
                    if (f.isFile) {
                        f.copyRecursively(File(dest))
                        AppLogger.RED_MIGRATION.d(TAG, "copyFile ${f.absolutePath} -> $dest")
                    }
                }
            }
            //FileUtils.copyDirectoryToDirectory(File(src), File(dest))
        } catch (e: IOException) {
            isSuccess = false
            AppLogger.RED_MIGRATION.e(TAG, "copyFolder error: $e")
        }
        val bundle = Bundle()
        bundle.putBoolean(BackupRestoreConstant.KEY_COPY_RESULT, isSuccess)
        return bundle
    }

    val backupCount: Bundle
        get() {
            val count = Bundle()
            val list = findAllUserNote()
            if (list.isNotEmpty()) {
                count.putInt(BackupRestoreConstant.KEY_BACKUP_COUNT, list.size)
            }
            return count
        }

    fun backupNoteRecord(filePath: String): Bundle? {
        AppLogger.RED_MIGRATION.d(TAG, "backupNoteRecord filePath = $filePath")
        initPath(filePath)
        backup()
        return null
    }

    fun getRestoreCount(filePath: String): Bundle {
        AppLogger.RED_MIGRATION.d(TAG, "getRestoreCount filePath = $filePath")
        initPath(filePath)
        val backupMaxCount = NoteXmlParser.parseRestoreCount(File(filePath))
        val bundle = Bundle()
        bundle.putInt(BackupRestoreConstant.KEY_RESTORE_COUNT, backupMaxCount)
        return bundle
    }

    fun restoreNoteRecord(filePath: String): Bundle? {
        AppLogger.RED_MIGRATION.d(TAG, "restoreNoteRecord filePath = $filePath")
        initPath(filePath)
        return restore()
    }

    private fun initPath(filePath: String) {
        val backupFile = File(filePath)
        RICHNOTE_BACKUP_XML_FILE = filePath
        RICHNOTE_ATTACHMENT_FOLDER = backupFile.parent + File.separator + "picture"
        AppLogger.RED_MIGRATION.d(TAG, "initPath RICHNOTE_BACKUP_XML_FILE = $RICHNOTE_BACKUP_XML_FILE")
        AppLogger.RED_MIGRATION.d(TAG, "initPath RICHNOTE_ATTACHMENT_FOLDER = $RICHNOTE_ATTACHMENT_FOLDER")
    }

    // backuprestore end
    val backupSize: Bundle?
        get() {
            val totalSize = kotlin.runCatching {
                MyApplication.application.filesDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
            }.getOrDefault(0L)
            val list = findAllUserNote()
            var count = 0
            if (list.isNotEmpty()) {
                count = list.size
            }
            if (count != 0) {
                val size = Bundle()
                return if (totalSize == 0L) {
                    size.putLong(BackupRestoreConstant.KEY_BACKUP_TOTAL_SIZE, count * 1024.toLong())
                    size
                } else {
                    val noteSize = totalSize + count * 1024
                    size.putLong(BackupRestoreConstant.KEY_BACKUP_TOTAL_SIZE, noteSize)
                    size
                }
            }
            return null
        }
}
