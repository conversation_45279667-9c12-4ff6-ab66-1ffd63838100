package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.nearme.note.migration.MigrationConstants.FILE_FILE_BACKUP
import com.nearme.note.util.FileUtil
import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import java.io.File

class FileMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {

    private val TAG = "FileMover"

    override fun onBackup() {
        val srcDirPath = context.filesDir.absolutePath
        val zipFilePath = backupFilePath + File.separator + FILE_FILE_BACKUP
        AppLogger.BR.d(TAG, "onBackup data/files to $FILE_FILE_BACKUP")
        try {
            val isSuccess = FileUtil.zipFilesAtPath(
                context.filesDir.absolutePath,
                plugin.getFileDescriptor(zipFilePath)
            )
            if (!isSuccess) {
                AppLogger.BR.d(TAG, "onBackup zip file failed")
                MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_FAILD_TO_ZIP_FILE)
            }
        } catch (e: Exception) {
            AppLogger.BR.w("onBackup zip file failed.", e.message)
            MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_FAILD_TO_ZIP_FILE)
        }
    }

    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }
        val zipFilePath = backupFilePath + File.separator + FILE_FILE_BACKUP
        val desPath = context.filesDir.parent
        AppLogger.BR.d(TAG, "onRestore Files unzip: from $FILE_FILE_BACKUP to filesDir.parent")
        try {
            val isSuccess = FileUtil.unzipFileToPath(plugin.getFileDescriptor(zipFilePath), desPath)
            if (!isSuccess) {
                AppLogger.BR.d(TAG, "onRestore FileMover fail:unzip file fail")
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_FAILD_TO_UNZIP_FILE)
            }
        } catch (e: Exception) {
            AppLogger.BR.w("Fileutil unzip file failed.", e.message)
            MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_FAILD_TO_ENSURE_DIRECTORY)
        }
        NoteSearchManagerWrapper.notifyDataChange()
    }
}