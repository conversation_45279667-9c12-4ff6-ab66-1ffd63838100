/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/10/21
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9060       2023/10/21      1.0     create file
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin

import android.content.Context
import android.os.Bundle
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.BackupPlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.note.logger.AppLogger

class FavoriteBackupPlugin : BackupPlugin() {

    companion object {
        private const val TAG = "FavoriteBackupPlugin"
    }

    /**doc: https://hio.oppo.com/app/ozone/team/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=157883906_157808373
        备份业务流程需继承并实现BackupPlugin，主要流程：
        数据扫描：onCreate --> onPreview --> onDestroy
        数据备份：onCreate --> onPrepare --> onBackup --> onDestroy
        暂停备份：onPause
        继续备份：onContinue
        停止备份：onCancel
     */
    override fun onCreate(
        context: Context,
        brPluginHandler: BRPluginHandler,
        config: BREngineConfig
    ) {
        super.onCreate(context, brPluginHandler, config)
        AppLogger.BR.d(TAG, "onCreate, ${Thread.currentThread().name}")
    }


    override fun onPreview(bundle: Bundle): Bundle {
        AppLogger.BR.d(TAG, "onPreview")
        return Bundle().apply {
            // 预览时，由插件返回值来决定是否显示在数据项列表中，1表示显示，0表示不显示
            putInt(ProgressHelper.PREVIEW_LIST_SHOW_PLUGIN_ITEM, 0)
        }
    }

    override fun onPrepare(bundle: Bundle): Bundle {
        AppLogger.BR.d(TAG, "onPrepare.  ${Thread.currentThread().name}")
        return Bundle()
    }

    override fun onBackup(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onBackup. ${Thread.currentThread().name}")
    }

    override fun onPause(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onPause. ${Thread.currentThread().name}")
    }

    override fun onContinue(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onContinue. ${Thread.currentThread().name}")
    }

    override fun onCancel(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onCancel. ${Thread.currentThread().name}")
    }

    override fun onDestroy(bundle: Bundle): Bundle {
        // 结束的时候，要把备份结果放到bundle中，结果是成功、失败，或是取消
        AppLogger.BR.d(TAG, "onDestroy. ${Thread.currentThread().name}")
        return Bundle()
    }
}

