/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: HtmlAnalyzer.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hw

import android.content.Context
import android.graphics.BitmapFactory
import android.text.TextUtils
import android.util.Base64
import com.nearme.note.util.FileUtil
import com.nearme.note.util.filesDirAbsolutePath
import com.nearme.note.view.NoteEditImageView
import com.oplus.migrate.backuprestore.plugin.third.analyze.ImageItem
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.editor.utils.RichStatisticsUtils
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Node
import java.io.File
import java.util.*

class HwHtmlAnalyzer {
    companion object {
        private const val TAG = "HwHtmlAnalyzer"
        private const val SPACE = "&nbsp;"
        private const val IMG_HUAWEI = "data:image/png;base64,"
    }

    var noteId = ""
    var text = StringBuilder()
    var rawText = StringBuilder()
    var images: ArrayList<ImageItem> = ArrayList()
    private var title = ""
    private var titleRaw = ""
    private var isInput = false
    private var divStyle = ""
    private var textStyle = ""
    private var endTag = ""
    private var endDivTag = ""
    private var endListTag = ""
    private var tempText = ""

    private val unSupportNodeStr = StringBuilder()

    fun setTitle(titlePair: Pair<String, String>) {
        title = titlePair.first
        titleRaw = titlePair.second
    }

    fun analyze(context: Context, data: String, noteId: String): String {
        clear()
        this.noteId = noteId
        analyzeTitle()
        val document = Jsoup.parse(data)
        analyzeDocument(context, document)
        if (!TextUtils.isEmpty(unSupportNodeStr)) {
            val brand = StringBuilder().append("hua").append("wei")
            RichStatisticsUtils.setEventMoverUnSupportFeature(context, brand.toString(), unSupportNodeStr.toString())
        }
        return rawText.toString()
    }

    private fun analyzeTitle() {
        if (titleRaw.isNotBlank()) {
            rawText.append(titleRaw)
            text.append(title)
        }
    }

    private fun analyzeDocument(context: Context, document: Document) {
        val elements = document.select(HwHtmlFormats.BODY)
        if (elements.isNotEmpty()) {
            val body = elements[0]
            analyzeNodeList(context, body.childNodes())
        }
        AppLogger.BASIC.d(TAG, " mRawText : $rawText")
    }

    private fun analyzeNodeList(context: Context, nodeList: List<Node>) {
        for (i in nodeList.indices) {
            clearTag()
            val sbContent = StringBuilder()
            val node = nodeList[i]
            val recurseString = recurse(context, node, sbContent, false)
            if (recurseString.trim().isEmpty()) continue
            var tagInfo = recurseString + endListTag + HwHtmlFormats.END_DIV
            tagInfo = removeDivIfNeed(tagInfo, false)
            rawText.append(tagInfo)
        }
    }

    private fun recurse(context: Context, node: Node, sbContent: StringBuilder, isList: Boolean): String {
        parseContent(context, node, sbContent, isList)
        parseChild(context, node, sbContent, isList)
        var result = sbContent.toString().trim()
        if (!TextUtils.isEmpty(result)) {
            result = "${HwHtmlFormats.START_DIV}$result"
        }
        return result
    }

    /**
     * Huawei的列表样式为：<ol><li type ="1" value="1"><font>文本</font><br></li></ol>
     *                  <ol><li type ="1" value="2"><font>文本</font><br></li></ol>
     * 直接解析会生成多个列表并且序号都为1，因此华为的列表要单独处理不能挨个直接解析
     */
    private fun parseChild(context: Context, node: Node, sbContent: StringBuilder, isList: Boolean) {
        val nodeList = parseChildList(node)
        if (nodeList.isNotEmpty()) {
            var i = 0
            while (i < nodeList.size) {
                val analyzePosition = analyzeOlAndUl(context, nodeList, i, sbContent)
                if (analyzePosition >= nodeList.size) return
                if (analyzePosition == i) {
                    recurse(context, nodeList[i], sbContent, isList)
                    i++
                } else {
                    i = analyzePosition
                }
            }
        }
    }

    /**
     * node.childNodes()可能会生成很多nodeName为#text内容为空的Node，会影响后续对列表的处理，因此先过滤掉
     */
    private fun parseChildList(node: Node): MutableList<Node> {
        val nodeList = arrayListOf<Node>()
        nodeList.addAll(node.childNodes())
        if (nodeList.isNotEmpty()) {
            nodeList.removeIf { (it.nodeName() == HwHtmlFormats.TEXT) && TextUtils.isEmpty(it.toString()) }
        }
        return nodeList
    }

    private fun parseContent(context: Context, node: Node, sbNode: StringBuilder, isList: Boolean = false) {
        when (node.nodeName()) {
            HwHtmlFormats.B -> {
                if (!isInput) {
                    textStyle = HwHtmlConvert.replaceToSpan(HwHtmlFormats.B, textStyle, divStyle)
                    endTag = HwHtmlFormats.END_SPAN
                }
            }

            HwHtmlFormats.I -> {
                if (!isInput) {
                    textStyle = HwHtmlConvert.replaceToSpan(HwHtmlFormats.I, textStyle, divStyle)
                    endTag = HwHtmlFormats.END_SPAN
                }
            }

            HwHtmlFormats.U -> {
                if (!isInput) {
                    textStyle = HwHtmlConvert.replaceToSpan(HwHtmlFormats.U, textStyle, divStyle)
                    endTag = HwHtmlFormats.END_SPAN
                }
            }

            HwHtmlFormats.INPUT -> {
                isInput = true
                textStyle = HwHtmlConvert.replaceToInput(node)
                endTag = HwHtmlFormats.END_INPUT
            }

            HwHtmlFormats.FONT -> {
                textStyle = HwHtmlConvert.replaceToSpan(HwHtmlFormats.FONT, textStyle, divStyle, node)
                endTag = HwHtmlFormats.END_SPAN
            }

            HwHtmlFormats.TEXT -> parseText(node, sbNode, isList)
            HwHtmlFormats.DIV -> {
                if (!isList) {
                    divStyle = HwHtmlConvert.analyzeDivAlign(node)
                }
                endDivTag = HwHtmlFormats.END_DIV
            }
            HwHtmlFormats.BR -> if (!isList) sbNode.append(HwHtmlFormats.START_BR)
            HwHtmlFormats.IMG -> {
                val img = parseImg(context, node)
                img?.let { sbNode.append(it) }
            }

            else -> {
                if (node.nodeName() != HwHtmlFormats.LI && node.nodeName() != HwHtmlFormats.OL && node.nodeName() != HwHtmlFormats.UL) {
                    //列表相关tag有单独处理，这里不上报列表相关的埋点
                    if (TextUtils.isEmpty(unSupportNodeStr)) {
                        unSupportNodeStr.append(node.nodeName())
                    } else {
                        unSupportNodeStr.append(",").append(node.nodeName())
                    }
                }
            }
        }
    }

    private fun parseText(node: Node, sbNode: StringBuilder, isList: Boolean) {
        val content = node.toString()
        if (content.isEmpty()) {
            return
        }
        if (isIndentation(content)) {
            tempText = content
        } else {
            val tempSb = StringBuilder()
            parseTag()
            tempSb.append(textStyle)
            tempSb.append(tempText + content)
            text.append(content.replace(SPACE, ""))
            tempSb.append(endTag)
            val removeDiv = removeDivIfNeed(tempSb.toString(), (isList || isInput))
            sbNode.append(removeDiv)
            endDivTag = HwHtmlFormats.END_DIV
            tempText = ""
        }
        clearTag()
    }

    /**
     * 判断文本是否全是空格
     */
    private fun isIndentation(text: String): Boolean {
        return text.trim().isBlank()
    }

    private fun clear() {
        noteId = ""
        text.clear()
        rawText.clear()
        images.clear()
        unSupportNodeStr.clear()
    }

    private fun clearTag() {
        isInput = false
        divStyle = ""
        textStyle = ""
        endTag = ""
    }

    private fun parseTag() {
        if (textStyle.isEmpty()) {
            textStyle = HwHtmlConvert.replaceToSpan(HwHtmlFormats.TEXT, textStyle, divStyle)
        } else {
            if (!textStyle.startsWith(HwHtmlFormats.FIND_DIV) && !isInput) {
                textStyle = "${HwHtmlFormats.START_DIV}$textStyle"
            }
        }
        if (endTag.isEmpty()) {
            endTag = HwHtmlFormats.END_SPAN
        }
    }

    private fun parseImg(context: Context, node: Node): String? {
        val data = HwHtmlConvert.getImgData(node)
        val imageItem = processImage(data, noteId)
        return if (imageItem == null) {
            null
        } else {
            images.add(imageItem)
            val width = imageItem.width
            val height = imageItem.height
            val start = "${HwHtmlFormats.HTML_IMG_START}\"${imageItem.id}\" "
            val size = "${HwHtmlFormats.WIDTH}=\"$width\" ${HwHtmlFormats.HEIGHT}=\"$height\""
            val end = HwHtmlFormats.END_IMG
            "$start$size$end"
        }
    }

    private fun processImage(data: String, noteId: String): ImageItem? {
        AppLogger.BASIC.i(TAG, "processImg, noteId=$noteId")
        return kotlin.runCatching {
            val imgData = if (data.startsWith(IMG_HUAWEI)) data.substring(IMG_HUAWEI.length) else data
            val decodeData = Base64.decode(imgData, Base64.DEFAULT)
            val bitmap = BitmapFactory.decodeByteArray(decodeData, 0, decodeData.size)
            if (bitmap == null) {
                AppLogger.BASIC.d(TAG, "processImg, create bitmap error")
                return null
            }
            if (bitmap.allocationByteCount >= NoteEditImageView.MAX_BITMAP_SIZE) {
                AppLogger.BASIC.d(TAG, "processImg, bitmap is to big:${bitmap.byteCount}")
                return null
            }
            val imageId = UUID.randomUUID().toString()
            val absolutePath = "${filesDirAbsolutePath()}${File.separator}$noteId${File.separator}${imageId}_thumb.png"
            if (!FileUtil.saveBmpToFile(bitmap, absolutePath)) {
                AppLogger.BASIC.d(TAG, "processImg, save image error. path=$absolutePath")
                return null
            }
            ImageItem(imageId, bitmap.width, bitmap.height)
        }.onFailure {
            AppLogger.BASIC.d(TAG, "processImg, noteId=$noteId e:$it")
        }.getOrNull()
    }

    /**
     * 连续的<ol>或<ul>需要整合在一起
     */
    private fun analyzeOlAndUl(context: Context, list: MutableList<Node>, i: Int, sbNode: StringBuilder): Int {
        val sameList = arrayListOf<Node>()
        val type = list[i].nodeName()
        if (type == HwHtmlFormats.OL || type == HwHtmlFormats.UL) {
            getList(list, i, sameList, type)
        }
        if (sameList.isNotEmpty()) {
            val start = if (type == HwHtmlFormats.OL) HwHtmlFormats.START_OL else HwHtmlFormats.START_UL
            val end = if (type == HwHtmlFormats.OL) HwHtmlFormats.END_OL else HwHtmlFormats.END_UL
            sbNode.append(start)
            sameList.forEach {
                it.childNodes().forEach { nodeChild ->
                    sbNode.append(HwHtmlFormats.START_LI)
                    recurse(context, nodeChild, sbNode, true)
                    sbNode.append(HwHtmlFormats.END_LI)
                }
            }
            sbNode.append(end)
        }
        return sameList.size + i
    }

    private fun getList(parentList: MutableList<Node>?, i: Int, list: MutableList<Node>, type: String) {
        if (parentList.isNullOrEmpty() || (i >= parentList.size)) {
            return
        }
        val positionNode = parentList[i]
        if (positionNode.nodeName() == type) {
            list.add(positionNode)
            val nextPosition = i + 1
            if (nextPosition < parentList.size) {
                getList(parentList, nextPosition, list, type)
            }
        }
    }

    /**
     * <div><ul><li></li></ul></div>
     * <li><div></div></li>这些格式本身并无问题
     * 但是在目前版本的便签内，li中不能使用div否则在内部格式转换的时候会出现格式异常
     */
    private fun removeDivIfNeed(str: String, enforceRemove: Boolean): String {
        return if (enforceRemove || str.contains(HwHtmlFormats.START_INPUT)) {
            removeDiv(str, null)
        } else {
            str
        }
    }

    private fun removeDiv(str: String, result: StringBuilder?): String {
        val temp = result ?: StringBuilder(str)
        val start = str.indexOf(HwHtmlFormats.FIND_DIV)
        if (start >= 0) {
            val end = str.indexOf(HwHtmlFormats.END, start)
            if (end < 0) {
                return str
            }
            val next = end + 1
            temp.clear()
            temp.append(str.removeRange(start, next))
            if (next < str.length) {
                removeDiv(temp.toString(), temp)
            }
        }
        return temp.toString().replace(HwHtmlFormats.END_DIV, "")
    }
}