/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptRichNoteMover
 ** Description:
 **         v1.0:  mover of encrypt rich notes.
 **
 ** Version: 1.0
 ** Date: 2023/10/30
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/10/30   1.0      Create this module
 ********************************************************************************/
package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.nearme.note.db.AppDatabase
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.FILE_ENCRYPT_RICH_NOTE

class EncryptRichNoteMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) :
    RichNoteMover(context, backupFilePath, plugin) {
    override val tag: String
        get() = "encryptRichNote"

    override fun getMigrationPath(): String {
        return FILE_ENCRYPT_RICH_NOTE
    }

    override fun getBackUpRichNote(): List<RichNoteWithAttachments> {
        return AppDatabase.getInstance().richNoteDao()
            .getAllEncryptRichNoteWithAttachmentsToBackup()
    }

    override fun onBackup() {
        super.onBackup()
    }

    override fun onRestore(isNotBrSdkGenerateBackupData: Boolean) {
        super.onRestore(isNotBrSdkGenerateBackupData)
    }
}