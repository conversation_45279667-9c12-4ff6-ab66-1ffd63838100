/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - BackupRestoreXmlComposer
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.logger.AppLogger
import org.xmlpull.v1.XmlSerializer
import java.io.IOException
import java.io.StringWriter

class NoteXmlComposer {
    private var mSerializer: XmlSerializer? = null
    private var mStringWriter: StringWriter? = null
    fun startCompose(): Boolean {
        var result = false
        mSerializer = NoteXmlSerializer()
        mStringWriter = StringWriter()
        try {
            (mSerializer as NoteXmlSerializer).setOutput(mStringWriter!!)
            (mSerializer as NoteXmlSerializer).startDocument("UTF-8", true)
            (mSerializer as NoteXmlSerializer).startTag("", NoteXmlTagConstant.NOTE_ROOT_TAG)
            result = true
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "startCompose -> ${e.message}")
        } catch (e: IllegalArgumentException) {
            AppLogger.RED_MIGRATION.e(TAG, "startCompose -> ${e.message}")
        } catch (e: IllegalStateException) {
            AppLogger.RED_MIGRATION.e(TAG, "startCompose -> ${e.message}")
        }
        return result
    }

    fun addNoteCount(size: Int) {
        try {
            mSerializer!!.startTag("", NoteXmlTagConstant.NOTE_COUNT_TAG)
            mSerializer!!.attribute("", NoteXmlTagConstant.NOTE_COUNT, Integer.toString(size))
            mSerializer!!.endTag("", NoteXmlTagConstant.NOTE_COUNT_TAG)
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteCount -> ${e.message}")
        } catch (e: IllegalArgumentException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteCount -> ${e.message}")
        } catch (e: IllegalStateException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteCount -> ${e.message}")
        }
    }

    fun addNoteAppInfo() {
        try {
            mSerializer!!.startTag("", NoteXmlTagConstant.NOTE_APP_VERSION_TAG)
            mSerializer!!.attribute("", NoteXmlTagConstant.APP_VERSION_NAME, appVersionName)
            mSerializer!!.attribute("", NoteXmlTagConstant.APP_VERSION_CODE, appVersionCode.toString())
            mSerializer!!.endTag("", NoteXmlTagConstant.NOTE_APP_VERSION_TAG)
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteAppInfo -> ${e.message}")
        } catch (e: IllegalArgumentException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteAppInfo -> ${e.message}")
        } catch (e: IllegalStateException) {
            AppLogger.RED_MIGRATION.e(TAG, "addNoteAppInfo -> ${e.message}")
        }
    }

    private val appVersionCode: Int
        get() {
            val packageManager = MyApplication.appContext.packageManager
            val packageInfo: PackageInfo?
            var versionCode = 0
            try {
                packageInfo = packageManager.getPackageInfo(MyApplication.appContext.packageName, 0)
                if (packageInfo != null) {
                    versionCode = packageInfo.versionCode
                }
            } catch (e: PackageManager.NameNotFoundException) {
                AppLogger.RED_MIGRATION.e(TAG, "appVersionCode.get() -> ${e.message}")
            }
            return versionCode
        }
    private val appVersionName: String
        get() {
            val packageManager = MyApplication.appContext.packageManager
            val packageInfo: PackageInfo?
            var versionName = ""
            try {
                packageInfo = packageManager.getPackageInfo(MyApplication.appContext.packageName, 0)
                if (packageInfo != null) {
                    versionName = packageInfo.versionName ?: ""
                }
            } catch (e: PackageManager.NameNotFoundException) {
                AppLogger.RED_MIGRATION.e(TAG, "appVersionName.get() -> ${e.message}")
            }
            return versionName
        }

    fun endCompose() {
        try {
            mSerializer!!.endTag("", NoteXmlTagConstant.NOTE_ROOT_TAG)
            mSerializer!!.endDocument()
        } catch (e: IllegalArgumentException) {
            AppLogger.RED_MIGRATION.e(TAG, "endCompose -> ${e.message}")
        } catch (e: IllegalStateException) {
            AppLogger.RED_MIGRATION.e(TAG, "endCompose -> ${e.message}")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "endCompose -> ${e.message}")
        }
    }

    val xmlInfo: String?
        get() = if (mStringWriter != null) {
            mStringWriter.toString()
        } else null

    fun addOneNoteRecord(richNoteWithAttachments: RichNoteWithAttachments) {
        try {
            mSerializer!!.startTag("", NoteXmlTagConstant.NOTE_TAG)
            addContent(richNoteWithAttachments)
            val attachmentList = richNoteWithAttachments.attachments
            if (attachmentList != null && attachmentList.isNotEmpty()) {
                for (j in attachmentList.indices) {
                    val attachment = attachmentList[j]
                    addAttachments(attachment)
                }
            }
            mSerializer!!.endTag("", NoteXmlTagConstant.NOTE_TAG)
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "addOneNoteRecord -> ${e.message}")
        }
    }

    @Throws(IOException::class)
    private fun addAttachments(attachment: Attachment) {
        mSerializer!!.startTag("", NoteXmlTagConstant.ATTACHMENT_TAG)
        mSerializer!!.attribute("", "attachment_id", attachment.attachmentId)
        mSerializer!!.attribute("", "rich_note_id", attachment.richNoteId)
        mSerializer!!.attribute("", "type", attachment.type.toString())
        mSerializer!!.attribute("", "state", attachment.state.toString())
        attachment.md5?.let { mSerializer!!.attribute("", "md5", attachment.md5) }
        attachment.url?.let { mSerializer!!.attribute("", "url", attachment.url) }
        attachment.picture?.let {
            mSerializer!!.attribute("", "width", attachment.picture!!.width.toString())
            mSerializer!!.attribute("", "height", attachment.picture!!.height.toString())
        }
        mSerializer!!.endTag("", NoteXmlTagConstant.ATTACHMENT_TAG)
    }

    @Throws(IOException::class)
    private fun addContent(richNoteWithAttachments: RichNoteWithAttachments) {
        mSerializer!!.attribute("", "local_id", richNoteWithAttachments.richNote.localId)
        richNoteWithAttachments.richNote.globalId?.let { mSerializer!!.attribute("", "global_id", richNoteWithAttachments.richNote.globalId) }
        mSerializer!!.attribute("", "text", richNoteWithAttachments.richNote.text)
        mSerializer!!.attribute("", "raw_text", richNoteWithAttachments.richNote.rawText)
        mSerializer!!.attribute("", "folder_id", richNoteWithAttachments.richNote.folderGuid)
        mSerializer!!.attribute("", "timestamp", richNoteWithAttachments.richNote.timestamp.toString())
        mSerializer!!.attribute("", "create_time", richNoteWithAttachments.richNote.createTime.toString())
        mSerializer!!.attribute("", "update_time", richNoteWithAttachments.richNote.updateTime.toString())
        mSerializer!!.attribute("", "top_time", richNoteWithAttachments.richNote.topTime.toString())
        mSerializer!!.attribute("", "recycle_time", richNoteWithAttachments.richNote.recycleTime.toString())
        mSerializer!!.attribute("", "alarm_time", richNoteWithAttachments.richNote.alarmTime.toString())
        mSerializer!!.attribute("", "state", richNoteWithAttachments.richNote.state.toString())
        mSerializer!!.attribute("", "deleted", richNoteWithAttachments.richNote.deleted.toString())
        richNoteWithAttachments.richNote.title?.let { mSerializer!!.attribute("", "title", richNoteWithAttachments.richNote.title) }
        richNoteWithAttachments.richNote.rawTitle?.let { mSerializer!!.attribute("", "raw_title", richNoteWithAttachments.richNote.rawTitle) }
        richNoteWithAttachments.richNote.recycleTimePre?.let { mSerializer!!.attribute("", "recycle_time_pre", richNoteWithAttachments.richNote.recycleTimePre.toString()) }
        richNoteWithAttachments.richNote.alarmTimePre?.let { mSerializer!!.attribute("", "alarm_time_pre", richNoteWithAttachments.richNote.alarmTimePre.toString()) }
        richNoteWithAttachments.richNote.extra?.let { mSerializer!!.attribute("", "extra",
            richNoteWithAttachments.richNote.extra.toString()
        ) }
        richNoteWithAttachments.richNote.packageName?.let { mSerializer!!.attribute("", "from_package", richNoteWithAttachments.richNote.packageName) }
        mSerializer!!.attribute("", "version", richNoteWithAttachments.richNote.version.toString())
    }

    companion object {
        const val TAG = "NoteXmlComposer"
    }
}