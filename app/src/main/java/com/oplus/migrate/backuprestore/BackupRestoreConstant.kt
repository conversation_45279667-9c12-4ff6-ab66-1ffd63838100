/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - BackupRestoreConstant
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import android.net.Uri
import com.oplus.note.common.PkgConstants

object BackupRestoreConstant {
    const val METHOD_COPY_FOLDER = "METHOD_COPY_FOLDER"
    const val KEY_COPY_FOLDER_SRC = "KEY_COPY_FOLDER_SRC"
    const val KEY_COPY_FOLDER_DES = "KEY_COPY_FOLDER_DES"
    const val KEY_COPY_RESULT = "KEY_COPY_RESULT"
    const val METHOD_BACKUP = "METHOD_BACKUP"
    const val KEY_BACKUP_FILENAME = "KEY_BACKUP_FILENAME"
    const val KEY_IS_USE_SDK = "KEY_IS_USE_SDK"
    const val METHOD_BACKUP_COUNT = "METHOD_BACKUP_COUNT"
    const val KEY_BACKUP_COUNT = "KEY_BACKUP_COUNT"
    const val METHOD_BACKUP_SIZE = "METHOD_RESTORE_SIZE"
    const val KEY_BACKUP_TOTAL_SIZE = "KEY_RESTORE_TOTAL_SIZE"
    const val METHOD_RESTORE = "METHOD_RESTORE"
    const val METHOD_RESTORE_COUNT = "METHOD_RESTORE_COUNT"
    const val KEY_RESTORE_FILENAME = "KEY_RESTORE_FILENAME"
    const val KEY_RESTORE_COUNT = "KEY_RESTORE_COUNT"
    // for both two pkgname note app will need to support this backup file, so this string will be defined in both two pkgname note app code
    const val NOTE_BR_FILE = "T25lUGx1c05vdGUueG1s"
    const val NOTE_FOLDER = "Note"
    const val NOTE_ATTACHMENTS = "noteAttachments"
    const val IS_OLD_NOTE_BACKUP_DATA = "IS_OLD_NOTE_BACKUP_DATA"
    const val NOTE_ATTACHMENTS_SPLIT = "@"
    const val THUMB_END_FIX = "_thumb.png"
    const val FILE_FILE_BACKUP: String = "files/file_backup.zip"
    const val OLD_PICTURE_FILE_BACKUP: String = "picture"
    //retail mode begin
    const val KEY_RETAIL_MODEL_INSERT = "KEY_RETAIL_MODEL_INSERT"
    const val KEY_RETAIL_MODEL_DATA = "KEY_RETAIL_MODEL_DATA"
    const val KEY_RETAIL_MODEL_DATA_IMAGE = "KEY_RETAIL_MODEL_DATA_IMAGE"
    //retail mode end
}