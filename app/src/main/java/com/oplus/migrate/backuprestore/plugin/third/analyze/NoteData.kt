/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File:          NoteData.kt
 * * Description:   NoteData
 * * Version:       1.0
 * * Date :         2024/5/8
 * * Author:        LiDongHang
 * * ---------------------Revision History: ---------------------
 * *  <author>     <date>       <version >   <desc>
 * *  LiDongHang   2024/5/8     1.0          build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze

import com.oplus.note.logger.AppLogger
import java.util.UUID

data class NoteData(
    val id: String = UUID.randomUUID().toString(),
    private val name: String,
    val isAddName: Boolean,
    var size: Int = 0,
    var imageSize: Int = 0,
    val groups: MutableList<DataGroup> = mutableListOf()
) {
    companion object {
        private const val TAG = "NoteData"
        const val TEXT_MAX_SIZE = 30000
        const val IMAGE_MAX_SIZE = 50
    }

    private var partNumber = 0

    fun getName(): String {
        return if ((partNumber == 0) || name.isEmpty()) {
            name
        } else {
            "$name($partNumber)"
        }
    }

    fun init(): NoteData {
        if (isAddName) {
            addItem(TextItem(text = getName()))
            addItem(WrapItem())
        }
        return this
    }

    fun new(): NoteData {
        val new = NoteData(name = name, isAddName = isAddName)
        new.partNumber = partNumber + 1
        new.init()
        val dataGroup = last()
        dataGroup.clear()
        new.addGroup(dataGroup)
        return new
    }

    fun lastOrNull(): DataGroup? = if (groups.isNotEmpty()) groups.last() else null

    fun lastNotListGroupOrNull(): DataGroup? = groups.lastOrNull()?.takeIf {
        (it.type != DataGroup.TYPE_ORDERED_LIST) && (it.type != DataGroup.TYPE_DISORDERED_LIST) && (it.type != DataGroup.TYPE_CHECK_BOX)
                && it.type != DataGroup.TYPE_TABLE
    }

    fun last(): DataGroup = lastOrNull() ?: addNewGroup()

    fun addGroup(group: DataGroup) {
        groups.add(group)
    }

    fun addNewGroup(): DataGroup = DataGroup().also { groups.add(it) }

    fun addItem(item: DataItem): Boolean {
        last().addItem(item)
        size += item.getItemSize()
        if (item is ImageItem) {
            imageSize++
        }
        AppLogger.BASIC.d(TAG, "addItem, size=$size, imageSize=$imageSize")
        return size >= TEXT_MAX_SIZE || imageSize >= IMAGE_MAX_SIZE
    }

    fun isContent(): Boolean {
        val start = if (isAddName) 1 else 0
        val end = groups.size - 1
        for (i in start..end) {
            if (groups[i].isContentGroup()) {
                return true
            }
        }
        if (partNumber == 0) {
            // 空笔记不保存
            AppLogger.BASIC.d(TAG, "${getName()} is empty")
        }
        return false
    }

    override fun toString(): String {
        val sb = StringBuilder("name=${getName()}").append("\n")
            .append("size=$size").append("\n")
        for (group in groups) {
            sb.append(group).append("\n")
        }
        return sb.toString()
    }
}

data class DataGroup(
    var groupId: String = "",
    var alignment: String = "",
    var type: String = "",
    var lvlText: String = "",
    var index: String = "",
    private val items: MutableList<DataItem> = mutableListOf()
) {
    companion object {
        const val ALIGN_CENTER = "center"
        const val ALIGN_END = "end"

        const val TYPE_ORDERED_LIST = "ordered_list"
        const val TYPE_DISORDERED_LIST = "disordered_list"
        const val TYPE_CHECK_BOX = "check_box"
        const val TYPE_TABLE = "table"

        const val CHAR_UNCHECKED = "o"
        const val CHAR_CHECKED = "þ"
    }

    val isList
        get() = type.isNotEmpty() && !isCheckBox && !isTable()

    val isCheckBox
        get() = type == TYPE_CHECK_BOX

    fun isTable(): Boolean = type == TYPE_TABLE

    fun getItems(): List<DataItem> = items.toList()

    fun clear() {
        items.clear()
    }

    fun addItem(item: DataItem) {
        items.add(item)
    }

    fun isSameList(group: DataGroup): Boolean {
        return isList && (type == group.type) && (lvlText == group.lvlText)
    }

    fun isIndexIncrease(group: DataGroup): Boolean {
        return isSameList(group) && index.isNotEmpty() && group.index.isNotEmpty() && (index[0] == group.index[0] + 1)
    }

    fun isEmpty(): Boolean {
        return items.isEmpty()
    }

    fun isTextGroup(): Boolean {
        return items.any { it is TextItem }
    }

    fun isContentGroup(): Boolean {
        return items.any { ((it is TextItem) && it.isNotEmpty()) || (it is ImageItem) }
    }

    fun lastTdItem(): TextItem? {
        return items.lastOrNull { it is TextItem && it.isTd } as? TextItem
    }
}

sealed interface DataItem {
    fun getItemSize(): Int = 0
}

data class TextItem(
    var isBold: Boolean = false,
    var isItalic: Boolean = false,
    var underline: Boolean = false,
    var isStrikeThrough: Boolean = false,
    val fontLevel: Int = TEXT_SIZE_LEVEL_DEFAULT,
    val fontSize: String = "",
    var textSize: Double = 0.0,
    val textColor: String = "",
    val textHighlight: Boolean = false,
    val text: String = "",
    /** 标记是否是 tr 元素*/
    var isTr: Int = NOT_TR,
    /** 标记是 td 元素*/
    var isTd: Boolean = false
) : DataItem {
    companion object {
        const val TEXT_SIZE_LEVEL_SMALL = 0
        const val TEXT_SIZE_LEVEL_DEFAULT = 1
        const val TEXT_SIZE_LEVEL_MIDDLE = 2
        const val TEXT_SIZE_LEVEL_BIG = 3
        const val TEXT_SIZE_LEVEL_LARGE = 4

        const val NOT_TR = 0
        const val TR_START = 1
        const val TR_END = 2

        private val COLORFUL_COLLECTION = listOf(
            "9933FF",
            "EF806C", "D94D3C", "123644", "3F8881", "6088B1", "50479A", "A56133", "CEB8A1",
            "FBE156", "F05268", "6CDD8F", "7289FF", "DADADA", "676767", "FFC000", "37D366",
            "6CA9E8", "92B0E2", "B0D29F", "BA9500", "EB7461", "743AA2", "C767A6", "C27F65",
            "E91E63", "FF7043", "FFC107", "CDDC39", "00BCD4", "2B61FB", "795548", "9D9D9D",
            "B3C871", "9AD2C9", "F1C27D", "EF8862", "5C69AE", "8B7BC9", "C27DC3", "D2634E",
            "F9DAAE", "FFBFA6", "FE9A9C", "FF85AC", "687AB3", "8781AD", "AE8EBD", "D488B7",
            "FF5E32", "FF9002", "FECA93", "B3B242", "545691", "5A7C7D", "73BAC0", "68B231",
            "E9E9E9", "D9D9D9", "BFBFBF", "A6A6A6", "919191", "6A6A6A", "4E4E4E",
            "91E2C2", "04878F", "BCB5E9", "6F55B8", "5CDEE6", "699BD8", "00377A",
            "439AF6", "A3F178", "7A5DE9", "D7559B", "2E1EEF", "7DFAA7", "EFFD6D", "E86536",
            "E54D62", "FF8F00", "24BCEB", "786AE4", "FFDA28", "8FD82F", "01BC8E", "EB5CD9",
            "FF0C84", "FF2400", "0DFB05", "0D0DFF", "FF00FF", "FFFF00", "21FFFA", "A82AFF",
            "FFD8E2", "FFF1CD", "C0EDFF", "DDDFFE", "FFFAC3", "EBF4C6", "BDEFE3", "F9DBFC",
            "FF7B8B", "FF95AE", "D6EA74", "AFEAB9", "FFB3A3", "FFEB62", "BAE4E3", "C1D2EF",
            "FB95D3", "FFBBE2", "9D97EF", "A68FC4", "DB9BF1", "DBD5F9", "78ACE8", "EEEEB0",
            "BA0F1B", "D74F4C", "FFDB5E", "A6B653", "E87D8D", "FFB74F", "C9D87B", "26998A",
            "B65858", "DFA798", "6395A0", "D1E2E9", "EA8F74", "FFF4DB", "7DC7D2", "9994B4",
            "431F0E", "71391C", "37582D", "6E7D46", "7A5F4B", "B08E7D", "C3C06D", "C9D9B2",
            "915D6E", "C4A0AE", "F5E5E5", "55687E", "BD7E93", "F0CED1", "AABED3", "3A4959",
            "494640", "686763", "BBB0A2", "C5CDCD", "978E8F", "CEC4C5", "D6D7D2", "F0F0F0",
            "8A2BE2"  // 华为/荣耀紫色
        )
    }

    override fun getItemSize(): Int = text.length

    fun isNotEmpty(): Boolean = text.isNotEmpty()

    val isTextColorHighlight: Boolean
        get() {
            if (textColor.isEmpty()) {
                return false
            }
            val color = if (textColor.startsWith("#")) textColor.substring(1) else textColor
            return COLORFUL_COLLECTION.contains(color.uppercase())
        }
}

data class ImageItem(
    val id: String = UUID.randomUUID().toString(),
    var width: Int = 0,
    var height: Int = 0
) : DataItem

data class WrapItem(
    val item: String = "B"  // 换行，一个字符长度
) : DataItem {
    override fun getItemSize(): Int = 1
}