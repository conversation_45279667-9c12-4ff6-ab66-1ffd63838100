/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: HtmlAnalyzeService.kt
 * * Description: OuterHtml
 * * Version: 1.0
 * * Date: 2022/11/17
 * * Author: xushuya
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third

import SignatureChecker
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import com.nearme.note.MyApplication.Companion.appContext
import com.oplus.migrate.backuprestore.plugin.third.analyze.ios.FileAnalyze
import com.oplus.note.logger.AppLogger
import com.oplus.note.R
import com.oplus.note.osdk.adapter.OplusIntentAdapter
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import okio.IOException
import org.koin.core.component.getScopeName

class HtmlAnalyzeService : Service() {
    companion object {
        private const val TAG = "HtmlAnalyzeService"
        const val NOTIFICATION_CHANNEL_ID = "NoteReceiveProcess"
        const val CALL_PACKAGE_NAME = "com.pangu.itools"
        //硬编码已与工具确认该签名不会变更，且debug与release 都为同一签名
        const val CALL_PACKAGE_SIGNATURE = "59:4B:C8:9A:A3:45:87:3E:A6:EA:C3:3D:E4:CC:09:7C:39:AD:10:5A:E9:6F:FF:5F:1B:64:65:15:90:9D:AD:96"
    }

    private var serviceScope: CoroutineScope? = null

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        AppLogger.BASIC.d(TAG, "onCreate#")
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager: NotificationManager =
                appContext.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID, appContext.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManager.createNotificationChannel(channel)
            val notification =
                Notification.Builder(applicationContext, NOTIFICATION_CHANNEL_ID).build()
            startForeground(1, notification)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val callingPkg = findCallingPackage(intent)
        if (callingPkg == null || CALL_PACKAGE_SIGNATURE != SignatureChecker.getAppSignatures(application, callingPkg)) {
            AppLogger.BASIC.e(TAG, "Error in onStartCommand Signature validate fail. calling from $callingPkg")
            stopSelf(startId)
            return START_NOT_STICKY
        }
        serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob() + CoroutineExceptionHandler { coroutineContext, throwable ->
            AppLogger.BASIC.e(TAG, coroutineContext.getScopeName().toString(), throwable)
        })
        serviceScope?.launch {
            try {
                intent?.data?.let { uri ->
                    AppLogger.BASIC.d(TAG, "onStartCommand# uri = $uri")
                    val fileAnalyze = FileAnalyze()
                    val files = fileAnalyze.loadData(appContext, uri)
                    val jobs = files?.map { file -> launch(CoroutineName(file)) { fileAnalyze.analyzeHtmlAndSave(file) } }
                    jobs?.forEach { it.join() }
                }
            } catch (e: IOException) {
                AppLogger.BASIC.e(TAG, "Error in onStartCommand", e)
            } finally {
                AppLogger.BASIC.d(TAG, "end HtmlAnalyzeService ")
                stopSelf(startId)
            }
        }

        return START_NOT_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope?.cancel()  // 取消所有协程任务
    }

    private fun findCallingPackage(intent: Intent?): String? {
        if (intent == null) {
            return null
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            kotlin.runCatching {
                val callingUid = OplusIntentAdapter.getCallingUid(intent) ?: return null
                val callingPkgs = packageManager.getPackagesForUid(callingUid) ?: return null
                val target = callingPkgs.find { it == CALL_PACKAGE_NAME } ?: callingPkgs.getOrNull(0)
                AppLogger.BASIC.d(TAG, "findCallingPackage: $target")
                return target
            }.onFailure {
                return null
            }
        } else {
            return null
        }
        return null
    }
}













