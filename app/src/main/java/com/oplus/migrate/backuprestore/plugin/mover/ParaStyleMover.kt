/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * File: ParaStyleMover
 * * Description: ParaStyleMover
 * * Version: 1.0
 * * Date: 2025/6/9
 * * Author: 80303972
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * 80303972 2025/6/9 1.0 build this module
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.nearme.note.db.AppDatabase
import com.nearme.note.migration.MigrationConstants
import com.nearme.note.util.FileUtil
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.paragraph.bean.ParaStyleSummary
import java.io.File
import java.lang.reflect.Modifier
import java.lang.reflect.Type

class ParaStyleMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) :
    Mover(context, backupFilePath, plugin) {

    companion object {
        const val TAG = "ParaStyleMover"
    }

    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup ParaStyle table")
        val list: List<ParaStyleSummary> = AppDatabase.getInstance().paraStyleDao().getAllData()
        // 只备份已下载的皮肤
        val downloadedParaStyleList = filterDownloadedParaStyle(list)
        val listStr =
            if (downloadedParaStyleList.isEmpty()) {
                AppLogger.BR.w(TAG, "onBackup ParaStyle.isNullOrEmpty()")
                "[]"
            } else {
                GsonBuilder().excludeFieldsWithModifiers(Modifier.STATIC)
                    .create().toJson(downloadedParaStyleList)
            }
        val backupPath: String =
            backupFilePath + File.separator + MigrationConstants.FILE_PARA_STYLE
        AppLogger.BR.d(TAG, "onBackup saveToFile, path = $backupPath, content = $listStr")
        val isSuccess = FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        if (!isSuccess) {
            AppLogger.BR.e(TAG, "[FileUtil]saveToFile failed")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteBackupPlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_PARA_STYLE_MOVER_BACKUP_FAIL
            )
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }
        val filePath: String = backupFilePath + File.separator + MigrationConstants.FILE_PARA_STYLE
        AppLogger.BR.d(TAG, "onRestore paraStyle list: ${MigrationConstants.FILE_PARA_STYLE}")
        FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
            val listType: Type = object : TypeToken<List<ParaStyleSummary>>() {}.type
            var paraStyleList: List<ParaStyleSummary>? = null
            kotlin.runCatching {
                paraStyleList = GsonBuilder()
                    .excludeFieldsWithModifiers(Modifier.STATIC)
                    .create().fromJson(this, listType)
            }.onFailure {
                AppLogger.BR.e(TAG, "onRestore paraStyleList error", it)
                NoteRestorePlugin.isSuccess = false
                NoteRestorePlugin.resultCode =
                    com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_PARA_STYLE_MOVER_FAILD_TO_RESOLVE_GSON
            }
            if (paraStyleList.isNullOrEmpty()) {
                AppLogger.BR.w(TAG, "onRestore paraStyleList.isNullOrEmpty()")
            } else {
                AppLogger.BR.w(TAG, "onRestore paraStyleList.size ${paraStyleList?.size}")
                saveParaStylesToLocal(paraStyleList ?: emptyList())
            }
        } ?: run {
            AppLogger.BR.w(TAG, "onRestore ParaStyleMover getContentFromFile is null")
            com.oplus.migrate.backuprestore.plugin.MigrationConstants.getFailCodeAndDescription(
                NoteRestorePlugin,
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_PARA_STYLE_MOVER_GETCONTENTFROMFILE_FAIL
            )
        }
    }

    private fun filterDownloadedParaStyle(list: List<ParaStyleSummary>): List<ParaStyleSummary> {
        val result = mutableListOf<ParaStyleSummary>()
        list.forEach { paraStyle ->
            if (paraStyle.isDownloaded()) {
                result.add(paraStyle)
            }
        }
        return result
    }

    private fun saveParaStylesToLocal(paraStyleList: List<ParaStyleSummary>) {
        AppDatabase.getInstance().paraStyleDao().insertStyleSummaryList(paraStyleList)
    }
}