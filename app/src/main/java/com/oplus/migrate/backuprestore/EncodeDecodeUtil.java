/*****************************************************************
 * * Copyright (C), 2020-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - EncodeUtil
 * * Description:
 * * Version: 1.0
 * * Date : 2020/11/21
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2020/11/21 1.0 create
 ******************************************************************/
package com.oplus.migrate.backuprestore;

import android.util.Base64;

import java.nio.charset.StandardCharsets;

public class EncodeDecodeUtil {

    public static final String NEW_CLOUD_PKG = "Y29tLmhleXRhcC5jbG91ZA";

    public static final String CLOUD_MAIN = "Y29tLmhleXRhcC5pbnRlbnQuYWN0aW9uLkNMT1VEX01BSU4";

    public static final String NOTE_SETTING = "Y29tLmhleXRhcC5jbG91ZC5hY3Rpb24uTk9URV9TRVRUSU5H";

    public static final String RECORD_SETTING = "Y29tLmhleXRhcC5jbG91ZC5hY3Rpb24uUkVDT1JEX1NFVFRJTkc";

    public static final String GALLERY_SETTING = "Y29tLmhleXRhcC5jbG91ZC5hY3Rpb24uR0FMTEVSWV9TRVRUSU5H";

    public static final String NEW_CLOUD_DATA_CHANGED = "Y29tLmhleXRhcC5jbG91ZC5hY3Rpb24uREFUQV9DSEFOR0VE";

    public static final String OLD_CLOUD_PKG = "Y29tLmNvbG9yb3MuY2xvdWQ";

    public static final String OLD_CLOUD_DATA_CHANGED = "Y29tLmNvbG9yb3MuY2xvdWQuYWN0aW9uLkRBVEFfQ0hBTkdFRA";

    public static final String ACTION_ACCOUNT_LOGOUT = "Y29tLm9wcG8udXNlcmNlbnRlci5hY2NvdW50X2xvZ291dA";

    public static final String EXTRA_ACCOUNT_LOGOUT_KEY_CLEAN_DATA = "Y29tLm9wcG8udXNlcmNlbnRlci5jbGVhbl9kYXRh";

    public static final String PROTOCOL_TAG_ROM_VERSION = "T0NMT1VELUNPTE9ST1M";

    public static final String SWITCH_PKG = "Y29tLm9uZXBsdXMuYmFja3VwcmVzdG9yZQ";
    public static final String SWITCH_SIGN = "0d2bb493d4c258eb105fa6e0d59ac47b";

    public static final String ZENMODE_PKG = "Y29tLm9uZXBsdXMuYnJpY2ttb2Rl";
    public static final String ZENMODE_SIGN = "0d2bb493d4c258eb105fa6e0d59ac47b";

    public static String decode(String text) {
        return new String(Base64.decode(text.getBytes(StandardCharsets.UTF_8), Base64.NO_WRAP | Base64.NO_PADDING), StandardCharsets.UTF_8);
    }

    public static String encode(String text) {
        return Base64.encodeToString(text.getBytes(StandardCharsets.UTF_8), Base64.NO_WRAP | Base64.NO_PADDING);
    }
}
