package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonSyntaxException
import com.google.gson.internal.JavaVersion
import com.google.gson.internal.PreJava9DateFormatProvider
import com.google.gson.internal.bind.util.ISO8601Utils
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.note.logger.AppLogger
import java.lang.reflect.Type
import java.text.DateFormat
import java.text.ParseException
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

abstract class Mover(var context: Context, var backupFilePath: String, var plugin: AbstractPlugin) {
    companion object {
        private const val TAG = "Mover"
    }

    abstract fun onBackup()

    abstract fun onRestore(isNotBrSdkGenerateBackupData: Boolean)

    fun getDateGsonBuilder(): GsonBuilder {
        return GsonBuilder().registerTypeAdapter(Date::class.java, object :
            JsonDeserializer<Date> {
            override fun deserialize(
                json: JsonElement,
                typeOfT: Type?,
                context: JsonDeserializationContext?
            ): Date {
                val dateFormats = getDateFormat()
                synchronized(dateFormats) {
                    for (dateFormat in dateFormats) {
                        try {
                            if (dateFormat is SimpleDateFormat) {
                                val formatPattern = dateFormat.toPattern()
                                val dateString = json.asString

                                if ((dateString.contains("AM") || dateString.contains("PM"))
                                    && !formatPattern.endsWith("a")) { //数据是12小时制，formatPattern不是12小时制
                                    continue
                                }

                                if ((!dateString.contains("AM") && !dateString.contains("PM"))
                                    && formatPattern.endsWith("a")) { //数据是24小时制，formatPattern不是24小时制
                                    continue
                                }
                            }

                            val date = dateFormat.parse(json.asString)
                            AppLogger.RED_MIGRATION.d(TAG, "date=$date, dateFormats.size=${dateFormats.size}")
                            if (date != null) {
                                return date
                            }
                        } catch (ignored: ParseException) {
                        }
                    }

                    return try {
                        ISO8601Utils.parse(
                            json.asString,
                            ParsePosition(0)
                        )
                    } catch (e: ParseException) {
                        throw JsonSyntaxException(json.asString, e)
                    }
                }
            }
        })
    }

    private fun getDateFormat(): MutableList<DateFormat> {
        val dateFormats: MutableList<DateFormat> = mutableListOf()
        dateFormats.add(
            DateFormat.getDateTimeInstance(
                DateFormat.DEFAULT,
                DateFormat.DEFAULT,
                Locale.US
            )
        )

        if (Locale.getDefault() != Locale.US) {
            dateFormats.add(DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT))
        }

        if (JavaVersion.isJava9OrLater()) {
            dateFormats.add(
                PreJava9DateFormatProvider.getUSDateTimeFormat(
                    DateFormat.DEFAULT, DateFormat.DEFAULT
                )
            )
        }

        //like this: Mar 8, 2022 7:18:00 PM
        val datePattern12Hour = "MMM d, y h:mm:ss a"
        //like this: Mar 8, 2022 19:18:00
        val datePattern24Hour = "MMM d, y HH:mm:ss"
        val dateFormat12Hour = SimpleDateFormat(datePattern12Hour, Locale.ENGLISH)
        val dateFormat24Hour = SimpleDateFormat(datePattern24Hour, Locale.ENGLISH)
        dateFormats.add(dateFormat12Hour)
        dateFormats.add(dateFormat24Hour)
        return dateFormats
    }
}