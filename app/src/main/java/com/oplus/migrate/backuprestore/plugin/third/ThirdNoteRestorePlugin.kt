/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: ThirdNoteRestorePlugin.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third

import android.content.Context
import android.os.Bundle
import android.os.ParcelFileDescriptor
import com.google.gson.Gson
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.RestorePlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.migrate.backuprestore.plugin.third.analyze.AnalyzerFactory
import com.oplus.note.logger.AppLogger
import java.io.*

class ThirdNoteRestorePlugin : RestorePlugin() {

    companion object {
        private const val TAG = "ThirdNoteRestorePlugin"
        private const val NOTE_BACKUP_FOLDER = "ThirdNote"
        private const val META_INFO_FOLDER = "MetaInfo"
        private const val INDEX_FILE = "fileList.txt"
        private const val META_INFO_FILE = "meta_info.txt"
        private const val BUF_SIZE = 1024
        var isRunning = false
    }

    private var isCancel = false
    private var maxCount = 0
    private var completeCount = 0

    override fun onCreate(
        context: Context?,
        brPluginHandler: BRPluginHandler?,
        config: BREngineConfig?
    ) {
        super.onCreate(context, brPluginHandler, config)
        isRunning = true
    }

    override fun onPreview(bundle: Bundle): Bundle {
        AppLogger.BASIC.d(TAG, "onPreview")
        return Bundle()
    }

    override fun onPrepare(bundle: Bundle): Bundle {
        AppLogger.BASIC.d(TAG, "onPrepare")
        completeCount = 0
        isCancel = false
        return Bundle()
    }

    /**
     * 搬家路径：/storage/emulated/0/Android/data/com.coloros.backuprestore/PhoneClone/Data/<时间戳>/ThirdNote
     * ThirdNote文件夹里面有MetaInfo文件夹和若干html文件
     * MetaInfo包含两个文件：meta_info.txt->NoteMetaInfo和fileList.txt->html文件列表文件
     */
    override fun onRestore(bundle: Bundle) {
        AppLogger.BASIC.d(TAG, "onRestore")
        runCatching {
            val rootPath = "${brEngineConfig.restoreRootPath}${File.separator}$NOTE_BACKUP_FOLDER${File.separator}"
            val pair = getMetaInfo(rootPath) ?: return
            readHtml(rootPath, pair)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "onRestore e $it")
        }
    }

    /**
     * 暂停逻辑搬家暂未启用，待后续搬家有更新再做修改
     */
    override fun onPause(bundle: Bundle) {
        AppLogger.BASIC.d(TAG, "onPause")
    }

    override fun onContinue(bundle: Bundle) {
        AppLogger.BASIC.d(TAG, "onContinue")
    }

    override fun onCancel(bundle: Bundle) {
        AppLogger.BASIC.d(TAG, "onCancel")
        isCancel = true
    }

    override fun onDestroy(bundle: Bundle): Bundle {
        val result = progress(true)
        AppLogger.BASIC.d(TAG, "onDestroy completeCount:$completeCount maxCount:$maxCount ,isCancel:$isCancel")
        isRunning = false
        return result
    }

    private fun readHtml(rootPath: String, pair: Pair<NoteMetaInfo, String>) {
        val indexFileFd = getFileDescriptor("${pair.second}$INDEX_FILE")
        val bufferedReader = BufferedReader(FileReader(indexFileFd))
        bufferedReader.use {
            while (true) {
                if (isCancel) return
                val name = it.readLine() ?: break
                if (name.isNotBlank()) {
                    val noteFile = "$rootPath$name"
                    val fileFd = getFileDescriptor(noteFile)
                    if (fileFd == null) {
                        AppLogger.BASIC.d(TAG, "onRestore fd is null")
                    } else {
                        AppLogger.BASIC.d(TAG, "onRestore file $noteFile")
                        parseNote(pair.first, fileFd, name)
                    }
                    completeCount++
                    progress(false)
                }
            }
        }
    }

    private fun getMetaInfo(rootPath: String): Pair<NoteMetaInfo, String>? {
        val metaInfoPath = "$rootPath$META_INFO_FOLDER${File.separator}"
        val json = getContent("$metaInfoPath$META_INFO_FILE")
        AppLogger.BASIC.d(TAG, "onRestore metaInfo json $json")
        val metaInfo = Gson().fromJson(json, NoteMetaInfo::class.java)
        if (metaInfo == null) {
            AppLogger.BASIC.d(TAG, "onRestore ,metaInfo is null")
            return null
        } else {
            AppLogger.BASIC.d(TAG, "onRestore metaInfo $metaInfo")
            maxCount = metaInfo.maxCount
        }
        return Pair(metaInfo, metaInfoPath)
    }

    private fun getContent(filePath: String): String {
        var result = ""
        var inputStream: FileInputStream?
        kotlin.runCatching {
            val fd = getFileDescriptor(filePath, ParcelFileDescriptor.MODE_READ_ONLY)
            /**
             * 存储空间满等异常场景下，fd会返回null
             */
            if (fd == null) {
                AppLogger.BASIC.e(TAG, "getContent fd is null!")
            } else {
                inputStream = FileInputStream(fd)
                val baos = ByteArrayOutputStream()
                var len: Int
                val buffer = ByteArray(BUF_SIZE)
                inputStream?.use {
                    while (it.read(buffer, 0, BUF_SIZE).also { read -> len = read } != -1) {
                        baos.write(buffer, 0, len)
                    }
                }
                result = baos.toString()
            }
        }.onFailure {
            AppLogger.BASIC.e(TAG, "getContent err $it")
        }
        return result
    }

    /**
     * 推送进度给搬家
     */
    private fun progress(isEnd: Boolean): Bundle {
        val bundle = Bundle()
        ProgressHelper.putCompletedCount(bundle, completeCount)
        ProgressHelper.putMaxCount(bundle, maxCount)
        if (isEnd) {
            val success = completeCount == maxCount
            val result = if (success) ProgressHelper.BR_RESULT_SUCCESS else ProgressHelper.BR_RESULT_FAILED
            ProgressHelper.putBRResult(bundle, result)
        } else {
            pluginHandler.updateProgress(bundle)
        }
        return bundle
    }

    /**
     * 解析数据并插入数据库
     */
    private fun parseNote(metaInfo: NoteMetaInfo, file: FileDescriptor, name: String) {
        val analyzer = AnalyzerFactory.createAnalyzer(metaInfo.brand.lowercase())
        if (analyzer == null) {
            AppLogger.BASIC.d(TAG, "parseNote else $metaInfo")
        } else {
            analyzer.analyze(context, FileInputStream(file), name)
        }
    }
}