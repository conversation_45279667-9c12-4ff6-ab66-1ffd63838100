/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - NoteXmlSerializer
 * * Description:
 * * Version: 1.0
 * * Date : 2021/4/26
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/4/26 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.backuprestore

import org.xmlpull.v1.XmlSerializer
import java.io.IOException
import java.io.OutputStream
import java.io.OutputStreamWriter
import java.io.Writer

class NoteXmlSerializer : XmlSerializer {
    private var writer: Writer? = null
    private var pending = false
    private var auto = 0
    private var depth = 0
    private var elementStack = arrayOfNulls<String>(12)

    //nsp/prefix/name
    private var nspCounts = IntArray(4)
    private var nspStack = arrayOfNulls<String>(8)

    //prefix/nsp; both empty are ""
    private var indent = BooleanArray(4)
    private var unicode = false
    private var encoding: String? = null
    @Throws(IOException::class)
    private fun check(close: Boolean) {
        if (!pending) return
        depth++
        pending = false
        if (indent.size <= depth) {
            val hlp = BooleanArray(depth + 4)
            System.arraycopy(indent, 0, hlp, 0, depth)
            indent = hlp
        }
        indent[depth] = indent[depth - 1]
        for (i in nspCounts[depth - 1] until nspCounts[depth]) {
            writer!!.write(' '.toInt())
            writer!!.write("xmlns")
            if ("" != nspStack[i * 2]) {
                writer!!.write(':'.toInt())
                writer!!.write(nspStack[i * 2])
            } else check(!("" == namespace && "" != nspStack[i * 2 + 1])) { "Cannot set default namespace for elements in no namespace" }
            writer!!.write("=\"")
            writeEscaped(nspStack[i * 2 + 1], '"'.toInt())
            writer!!.write('"'.toInt())
        }
        if (nspCounts.size <= depth + 1) {
            val hlp = IntArray(depth + 8)
            System.arraycopy(nspCounts, 0, hlp, 0, depth + 1)
            nspCounts = hlp
        }
        nspCounts[depth + 1] = nspCounts[depth]
        //   nspCounts[depth + 2] = nspCounts[depth];
        writer!!.write(if (close) " />" else ">")
    }

    @Throws(IOException::class)
    private fun writeEscaped(s: String?, quot: Int) {
        for (element in s!!) {
            when (element) {
                '\n', '\r', '\t' -> if (quot == -1) writer!!.write(element.toInt()) else writer!!.write("&#" + element.toInt() + ';')
                '&' -> writer!!.write("&amp;")
                '>' -> writer!!.write("&gt;")
                '<' -> writer!!.write("&lt;")
                '"' -> writer!!.write("&quot;")
                '\'' -> writer!!.write("&apos;")
                else -> if (element >= ' ' && element != '@' && (element.toInt() < 127 || unicode)) writer!!.write(element.toInt()) else writer!!.write(
                    "&#" + element.toInt() + ";"
                )
            }
        }
    }

    /*
        private final void writeIndent() throws IOException {
            writer.write("\r\n");
            for (int i = 0; i < depth; i++)
                writer.write(' ');
        }*/
    @Throws(IOException::class)
    override fun docdecl(dd: String) {
        writer!!.write("<!DOCTYPE")
        writer!!.write(dd)
        writer!!.write(">")
    }

    @Throws(IOException::class)
    override fun endDocument() {
        while (depth > 0) {
            endTag(
                    elementStack[depth * 3 - 3]!!,
                    elementStack[depth * 3 - 1]!!)
        }
        flush()
    }

    @Throws(IOException::class)
    override fun entityRef(name: String) {
        check(false)
        writer!!.write('&'.toInt())
        writer!!.write(name)
        writer!!.write(';'.toInt())
    }

    override fun getFeature(name: String): Boolean {
        //return false;
        return if ("http://xmlpull.org/v1/doc/features.html#indent-output"
                ==
                name) indent[depth] else false
    }

    override fun getPrefix(namespace: String, create: Boolean): String {
        return try {
            getPrefix(namespace, false, create)!!
        } catch (e: IOException) {
            throw RuntimeException(e.toString())
        }
    }

    @Throws(IOException::class)
    private fun getPrefix(
            namespace: String,
            includeDefault: Boolean,
            create: Boolean): String? {
        var i = nspCounts[depth + 1] * 2 - 2
        while (i >= 0) {
            if (nspStack[i + 1] == namespace && (includeDefault || nspStack[i] != "")) {
                var cand = nspStack[i]
                for (j in i + 2 until nspCounts[depth + 1] * 2) {
                    if (nspStack[j] == cand) {
                        cand = null
                        break
                    }
                }
                if (cand != null) return cand
            }
            i -= 2
        }
        if (!create) return null
        var prefix: String?
        if ("" == namespace) prefix = "" else {
            do {
                prefix = "n" + auto++
                var i = nspCounts[depth + 1] * 2 - 2
                while (i >= 0) {
                    if (prefix == nspStack[i]) {
                        prefix = null
                        break
                    }
                    i -= 2
                }
            } while (prefix == null)
        }
        pending = false
        val p = pending
        setPrefix(prefix, namespace)
        pending = p
        return prefix
    }

    override fun getProperty(name: String): Any {
        throw RuntimeException("Unsupported property")
    }

    @Throws(IOException::class)
    override fun ignorableWhitespace(s: String) {
        text(s)
    }

    override fun setFeature(name: String, value: Boolean) {
        if ("http://xmlpull.org/v1/doc/features.html#indent-output"
                == name) {
            indent[depth] = value
        } else throw RuntimeException("Unsupported Feature")
    }

    override fun setProperty(name: String, value: Any) {
        throw RuntimeException(
                "Unsupported Property:$value")
    }

    @Throws(IOException::class)
    override fun setPrefix(prefix: String, namespace: String) {
        var prefix: String? = prefix
        var namespace: String? = namespace
        check(false)
        if (prefix == null) prefix = ""
        if (namespace == null) namespace = ""
        val defined = getPrefix(namespace, true, false)

        // boil out if already defined
        if (prefix == defined) return
        var pos = nspCounts[depth + 1]++ shl 1
        if (nspStack.size < pos + 1) {
            val hlp = arrayOfNulls<String>(nspStack.size + 16)
            System.arraycopy(nspStack, 0, hlp, 0, pos)
            nspStack = hlp
        }
        nspStack[pos++] = prefix
        nspStack[pos] = namespace
    }

    override fun setOutput(writer: Writer) {
        this.writer = writer

        // elementStack = new String[12]; //nsp/prefix/name
        //nspCounts = new int[4];
        //nspStack = new String[8]; //prefix/nsp
        //indent = new boolean[4];
        nspCounts[0] = 2
        nspCounts[1] = 2
        nspStack[0] = ""
        nspStack[1] = ""
        nspStack[2] = "xml"
        nspStack[3] = "http://www.w3.org/XML/1998/namespace"
        pending = false
        auto = 0
        depth = 0
        unicode = false
    }

    @Throws(IOException::class)
    override fun setOutput(os: OutputStream, encoding: String) {
        requireNotNull(os)
        setOutput(
                if (encoding == null) OutputStreamWriter(os) else OutputStreamWriter(os, encoding))
        this.encoding = encoding
        if (encoding != null
                && encoding.lowercase().startsWith("utf")) unicode = true
    }

    @Throws(IOException::class)
    override fun startDocument(
            encoding: String,
            standalone: Boolean) {
        writer!!.write("<?xml version='1.0' ")
        if (encoding != null) {
            this.encoding = encoding
            if (encoding.lowercase().startsWith("utf")) unicode = true
        }
        if (this.encoding != null) {
            writer!!.write("encoding='")
            writer!!.write(this.encoding)
            writer!!.write("' ")
        }
        if (standalone != null) {
            writer!!.write("standalone='")
            writer!!.write(
                    if (standalone) "yes" else "no")
            writer!!.write("' ")
        }
        writer!!.write("?>")
    }

    @Throws(IOException::class)
    override fun startTag(namespace: String, name: String): XmlSerializer {
        check(false)

        //        if (namespace == null)
        //            namespace = "";
        if (indent[depth]) {
            writer!!.write("\r\n")
            for (i in 0 until depth) writer!!.write("  ")
        }
        var esp = depth * 3
        if (elementStack.size < esp + 3) {
            val hlp = arrayOfNulls<String>(elementStack.size + 12)
            System.arraycopy(elementStack, 0, hlp, 0, esp)
            elementStack = hlp
        }
        val prefix = if (namespace == null) "" else getPrefix(namespace, true, true)!!
        if ("" == namespace) {
            for (i in nspCounts[depth] until nspCounts[depth + 1]) {
                check(!("" == nspStack[i * 2] && "" != nspStack[i * 2 + 1])) { "Cannot set default namespace for elements in no namespace" }
            }
        }
        elementStack[esp++] = namespace
        elementStack[esp++] = prefix
        elementStack[esp] = name
        writer!!.write('<'.toInt())
        if ("" != prefix) {
            writer!!.write(prefix)
            writer!!.write(':'.toInt())
        }
        writer!!.write(name)
        pending = true
        return this
    }

    @Throws(IOException::class)
    override fun attribute(
            namespace: String,
            name: String,
            value: String): XmlSerializer {
        var namespace: String? = namespace
        var value: String? = value
        check(pending) { "illegal position for attribute" }

        //        int cnt = nspCounts[depth];
        if (namespace == null) namespace = ""

        //      depth--;
        //      pending = false;

        //add 2021-04-26
        if (value == null) {
            value = ""
        }
        //add 2021-04-26
        val prefix = if ("" == namespace) "" else getPrefix(namespace, false, true)!!

        //      pending = true;
        //      depth++;

        /*        if (cnt != nspCounts[depth]) {
                    writer.write(' ');
                    writer.write("xmlns");
                    if (nspStack[cnt * 2] != null) {
                        writer.write(':');
                        writer.write(nspStack[cnt * 2]);
                    }
                    writer.write("=\"");
                    writeEscaped(nspStack[cnt * 2 + 1], '"');
                    writer.write('"');
                }
                */writer!!.write(' '.toInt())
        if ("" != prefix) {
            writer!!.write(prefix)
            writer!!.write(':'.toInt())
        }
        writer!!.write(name)
        writer!!.write('='.toInt())
        val q = if (value.indexOf('"') == -1) '"' else '\''
        writer!!.write(q.toInt())
        writeEscaped(value, q.toInt())
        writer!!.write(q.toInt())
        return this
    }

    @Throws(IOException::class)
    override fun flush() {
        check(false)
        writer!!.flush()
    }

    /*
        public void close() throws IOException {
            check();
            writer.close();
        }
    */
    @Throws(IOException::class)
    override fun endTag(namespace: String, name: String): XmlSerializer {
        if (!pending) depth--
        //        if (namespace == null)
        //          namespace = "";
        require(!((namespace == null
                && elementStack[depth * 3] != null)
                || (namespace != null
                && namespace != elementStack[depth * 3])
                || elementStack[depth * 3 + 2] != name)) { "</{$namespace}$name> does not match start" }
        if (pending) {
            check(true)
            depth--
        } else {
            if (indent[depth + 1]) {
                writer!!.write("\r\n")
                for (i in 0 until depth) writer!!.write("  ")
            }
            writer!!.write("</")
            val prefix = elementStack[depth * 3 + 1]
            if ("" != prefix) {
                writer!!.write(prefix)
                writer!!.write(':'.toInt())
            }
            writer!!.write(name)
            writer!!.write('>'.toInt())
        }
        nspCounts[depth + 1] = nspCounts[depth]
        return this
    }

    override fun getNamespace(): String? {
        return if (getDepth() == 0) null else elementStack[getDepth() * 3 - 3]!!
    }

    override fun getName(): String? {
        return if (getDepth() == 0) null else elementStack[getDepth() * 3 - 1]!!
    }

    override fun getDepth(): Int {
        return if (pending) depth + 1 else depth
    }

    @Throws(IOException::class)
    override fun text(text: String): XmlSerializer {
        check(false)
        indent[depth] = false
        writeEscaped(text, -1)
        return this
    }

    @Throws(IOException::class)
    override fun text(text: CharArray, start: Int, len: Int): XmlSerializer {
        text(String(text, start, len))
        return this
    }

    @Throws(IOException::class)
    override fun cdsect(data: String) {
        check(false)
        writer!!.write("<![CDATA[")
        writer!!.write(data)
        writer!!.write("]]>")
    }

    @Throws(IOException::class)
    override fun comment(comment: String) {
        check(false)
        writer!!.write("<!--")
        writer!!.write(comment)
        writer!!.write("-->")
    }

    @Throws(IOException::class)
    override fun processingInstruction(pi: String) {
        check(false)
        writer!!.write("<?")
        writer!!.write(pi)
        writer!!.write("?>")
    }
}