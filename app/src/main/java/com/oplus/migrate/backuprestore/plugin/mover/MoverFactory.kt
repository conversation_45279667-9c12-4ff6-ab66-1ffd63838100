package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_DATA_SOURCE_INFO
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_ENCRYPT_FOLDER
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_ENCRYPT_RICH_NOTE
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_FILE
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_FOLDER
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_RICH_NOTE
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_SKIN
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_SP
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_TODO
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVE_NO_FOLDER_RICH_NOTE
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVE_PARA_STYLE

object MoverFactory {
    fun getMover(
        context: Context,
        moverName: String,
        path: String,
        plugin: AbstractPlugin
    ): Mover? {
        var ret: Mover? = null

        when (moverName) {
            MOVER_RICH_NOTE -> ret = RichNoteMover(context, path, plugin)
            MOVER_FILE -> ret = FileMover(context, path, plugin)
            MOVER_TODO -> ret = TodoMover(context, path, plugin)
            MOVER_FOLDER -> ret = FolderMover(context, path, plugin)
            MOVER_SKIN -> ret = SkinMover(context, path, plugin)
            MOVE_PARA_STYLE -> ret = ParaStyleMover(context, path, plugin)
            MOVER_SP -> ret = SpMover(context, path, plugin)
            MOVER_DATA_SOURCE_INFO -> ret = DataSourceInfoMover(context, path, plugin)
            MOVER_ENCRYPT_FOLDER -> ret = EncryptFolderMover(context, path, plugin)
            MOVER_ENCRYPT_RICH_NOTE -> ret = EncryptRichNoteMover(context, path, plugin)
            MOVE_NO_FOLDER_RICH_NOTE -> ret = NoFolderRichNoteMover(context, path, plugin)
        }

        return ret
    }
}