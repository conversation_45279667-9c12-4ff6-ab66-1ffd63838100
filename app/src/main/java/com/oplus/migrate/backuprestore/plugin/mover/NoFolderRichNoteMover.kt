/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - NoFolderRichNoteMover
 ** Description:
 **         v1.0:   Notebook non-existent data
 **
 ** Version: 1.0
 ** Date: 2024/09/18
 ** Author: Jiep<PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.Yan              2024/9/18        1.0      Create this module
 ********************************************************************************/
package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.nearme.note.db.AppDatabase
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.FILE_NO_FOLDER_RICH_NOTE
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class NoFolderRichNoteMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : RichNoteMover(context, backupFilePath, plugin) {

    companion object {
        private const val TAG = "NoFolderRichNoteMover"
    }

    override val tag: String
        get() = "NoFolderRichNote"

    override fun getMigrationPath(): String {
        return FILE_NO_FOLDER_RICH_NOTE
    }

    override fun getBackUpRichNote(): List<RichNoteWithAttachments> {
        return AppDatabase.getInstance().richNoteDao().queryNoFolderRichNotes()
    }

    override fun onBackup() {
        super.onBackup()
    }

    override fun onRestore(isNotBrSdkGenerateBackupData: Boolean) {
        super.onRestore(isNotBrSdkGenerateBackupData)
    }
}