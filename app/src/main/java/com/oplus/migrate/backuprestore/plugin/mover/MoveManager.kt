package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.MOVER_LIST
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.note.logger.AppLogger
import kotlin.properties.Delegates

class MoveManager(
    var context: Context,
    var backupFilePath: String,
    var plugin: AbstractPlugin
) {

    private var completeCount: Int = 0
    private var isCancel: Boolean = false
    private var handler: Handler = Handler(Looper.getMainLooper())
    private var runnable: Runnable? = null
    fun getMoverCount(): Int = MOVER_LIST.size

    fun getCompleteCount(): Int = completeCount

    private var startTimestamp: Long by Delegates.observable(0L) { _, _, _ ->
        onTimestampChanged()
    }

    /**
     * 增加一个文件开始备份的时间戳的监听，当它的值发生变化时，会触发一个监听时间：在110秒后判断当前的已完成备份的数量是否发生了改变，若没有改变则代表当前的文件还在备份中，此时
     * 向搬家方发送一次进度跟新的消息，同时更新时间戳，继续下一次监听
     */
    private fun onTimestampChanged() {
        val currentCompleteCount = completeCount
        runnable?.let { handler.removeCallbacks(it) }
        runnable = Runnable {
            if (currentCompleteCount == completeCount) {
                val progress = Bundle()
                ProgressHelper.putCompletedCount(progress, completeCount)
                ProgressHelper.putMaxCount(progress, getMoverCount())
                plugin.pluginHandler.updateProgress(progress)
                startTimestamp = System.currentTimeMillis()
            }
        }
        handler.postDelayed(runnable!!, TIMEOUT_RESETTIMESTAMP)
    }

    fun onBackup() {
        for (moverName in MOVER_LIST) {
            if (isCancel) {
                isCancel = false
                break
            }
            startTimestamp = System.currentTimeMillis()
            MoverFactory.getMover(context, moverName, backupFilePath, plugin)?.apply {
                onBackup()
            } ?: run {
                AppLogger.BR.w("MoveManager", "getMover[$moverName] is null")
            }
            AppLogger.BR.d("MoveManager", "onBackup--[$moverName],completeCount=$completeCount")
            completeCount++
            //每完成一个文件备份就同步跟新一次进度，同时对于需要处理两分钟以上的文件，在快满两分钟但是仍旧没有完成备份也需要同步一次更新数据，避免引发超时错误
            val progress = Bundle()
            ProgressHelper.putCompletedCount(progress, completeCount)
            ProgressHelper.putMaxCount(progress, getMoverCount())
            plugin.pluginHandler.updateProgress(progress)
            AppLogger.BR.d("MoverManager", "${plugin.pluginHandler}")
        }
    }

    fun onCancel() {
        isCancel = true
    }

    fun onRestore(isNotBrSdkGenerateBackupData: Boolean) {
        for (moverName in MOVER_LIST) {
            if (isCancel) {
                isCancel = false
                break
            }
            startTimestamp = System.currentTimeMillis()
            MoverFactory.getMover(context, moverName, backupFilePath, plugin)?.apply {
                onRestore(isNotBrSdkGenerateBackupData)
            } ?: run {
                AppLogger.BR.w("MoveManager", "getMover[$moverName] is null")
            }
            AppLogger.BR.d("MoveManager", "onRestore--[$moverName],completeCount=$completeCount")
            completeCount++
            //每完成一个文件恢复就同步跟新一次进度，同时对于需要处理两分钟以上的文件，在快满两分钟但是仍旧没有完成恢复也需要同步一次更新数据，避免引发超时错误
            val progress = Bundle()
            ProgressHelper.putCompletedCount(progress, completeCount)
            ProgressHelper.putMaxCount(progress, getMoverCount())
            plugin.pluginHandler.updateProgress(progress)
        }
    }

    companion object {
        private const val TIMEOUT_RESETTIMESTAMP = 110000L
    }
}