/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptFolderMover
 ** Description:
 **         v1.0:  mover of encrypt folder
 **
 ** Version: 1.0
 ** Date: 2023/10/30
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote       2023/10/30   1.0      Create this module
 ********************************************************************************/
package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.db.AppDatabase
import com.oplus.note.repo.note.entity.Folder
import com.nearme.note.util.FileUtil
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import java.io.File
import java.lang.reflect.Type

class EncryptFolderMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) :
    FolderMover(context, backupFilePath, plugin) {
    override val tag: String
        get() = "encryptFolder"

    @Suppress("TooGenericExceptionCaught")
    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup folder table")
        val list: List<Folder> = AppDatabase.getInstance().foldersDao()
            .findEncryptedFolder(FolderInfo.FOLDER_GUID_ENCRYPTED)
        val listStr =
            if (list.isNullOrEmpty()) {
                AppLogger.BR.w(TAG, "onBackup ${tag}List.isNullOrEmpty()")
                "[]"
            } else {
                Gson().toJson(list)
            }
        val backupPath: String =
            backupFilePath + File.separator + MigrationConstants.FILE_ENCRYPT_FOLDER
        AppLogger.BR.d(TAG, "onBackup $tag saveToFile, path = ${MigrationConstants.FILE_ENCRYPT_FOLDER}, contentSize = ${list.size}")
        kotlin.runCatching {
            FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        }.onFailure {
            AppLogger.BR.e(TAG, "onBackup EncryptFolderMover saveToFile fail ", it)
            MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_ENCRYPT_FOLDER_MOVER_BACKUP_FAIL)
        }
    }

    override fun onRestore(isRestoreOldNoteData: Boolean) {
        if (isRestoreOldNoteData) {
            return
        }

        val filePath: String =
            backupFilePath + File.separator + MigrationConstants.FILE_ENCRYPT_FOLDER
        AppLogger.BR.d(TAG, "onRestore $tag list: ${MigrationConstants.FILE_ENCRYPT_FOLDER}")

        kotlin.runCatching {
            val fd = plugin.getFileDescriptor(filePath)
            FileUtil.getContentFromFile(fd)?.apply {
                val listType: Type = object : TypeToken<List<Folder>>() {}.type

                val gsonBuilder = getDateGsonBuilder()

                val gson = gsonBuilder.create()
                val folderList = mutableListOf<Folder>()
                if (this.isNotEmpty()) {
                    folderList.addAll(gson.fromJson(this, listType))
                }
                if (folderList.isEmpty()) {
                    AppLogger.BR.w(TAG, "onRestore ${tag}List.isNullOrEmpty()")
                } else {
                    AppLogger.BR.w(TAG, "onRestore ${tag}List size:${folderList.size}")
                    mergeData(context, folderList)
                }
            }
        }.onFailure {
            AppLogger.BR.e(TAG, "onRestore $tag error :${it.message}")
            MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_ENCRYPT_FOLDER_MOVER_RESTORE_FAIL)
        }
    }

    companion object {
        private const val TAG = "EncryptFolderMover"
    }
}