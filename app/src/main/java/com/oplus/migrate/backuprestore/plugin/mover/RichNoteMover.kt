package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.TextUtils
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.resetToNewState
import com.nearme.note.skin.api.SkinManager
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.FileUtil
import com.nearme.note.util.NoteSearchManagerWrapper
import com.nearme.note.util.StringEncodeDecode
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.migrate.backuprestore.BackupRestoreConstant
import com.oplus.migrate.backuprestore.BackupRestoreConstant.NOTE_BR_FILE
import com.oplus.migrate.backuprestore.BackupRestoreUtil
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.MigrationConstants.FILE_RICH_NOTE
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.util.JsonToRichNoteConverter
import com.oplus.note.repo.note.util.NoteFeatureUtil
import com.oplus.note.repo.note.util.RichNoteJsonConverter
import com.oplus.richtext.core.entity.ImageFormat
import com.oplus.richtext.core.node.MediaNode
import com.oplus.richtext.core.node.SpannedNode
import com.oplus.richtext.core.parser.HtmlParser
import com.oplus.richtext.core.parser.HtmlStandardParser
import com.oplus.richtext.core.utils.Constants.SPECIAL_CHARS
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import java.io.File

open class RichNoteMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {

    companion object {
        private const val TAG = "RichNoteMover"
    }

    private val mRichNoteCache = ArrayList<RichNoteWithAttachments>()
    private val mUpdateRichNoteCache = ArrayList<RichNoteWithAttachments>()
    open val tag: String = "RichNote"

    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup rich note table")
        val data = getBackUpRichNote()

        //备份时去除已经标记为永久删除的数据。
        val list = data.filter { !it.richNote.deleted }
        val listStr = RichNoteJsonConverter.richNoteListToJson(context, list) { errorCode ->
            AppLogger.BR.d(TAG, "onBackup $tag error, code = $errorCode")
            when (errorCode) {
                RichNoteJsonConverter.ERROR_FAILD_TO_BACKUP_CONTACT -> {
                    MigrationConstants.getFailCodeAndDescription(
                        NoteBackupPlugin,
                        MigrationConstants.ERROR_CODE_FAILD_TO_BACKUP_CONTACT
                    )
                }
                RichNoteJsonConverter.ERROR_FAILD_TO_BACKUP_SCHEDULE -> {
                    MigrationConstants.getFailCodeAndDescription(
                        NoteBackupPlugin,
                        MigrationConstants.ERROR_CODE_FAILD_TO_BACKUP_SCHEDULE
                    )
                }
            }
        }
        val backupPath: String = backupFilePath + File.separator + getMigrationPath()
        AppLogger.BR.d(TAG, "onBackup migrationPath = $tag saveToFile, path = ${getMigrationPath()}, contentSize = ${list.size}")
        val isSuccess = FileUtil.saveToFile(plugin.getFileDescriptor(backupPath), listStr)
        if (!isSuccess) {
            AppLogger.BR.e(TAG, "TodoMover [FileUtil]saveToFile failed")
            MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_RICH_NOTE_MOVER_BACKUP_FAIL)
        }
    }

    override fun onRestore(isNotBrSdkGenerateBackupData: Boolean) {
        val filePath: String = backupFilePath + File.separator + getMigrationPath()
        AppLogger.BR.d(
            TAG, "onRestore $tag backupFilePath = ${getMigrationPath()}," +
                    "isNotBrSdkGenerateBackupData=$isNotBrSdkGenerateBackupData"
        )
        if (isNotBrSdkGenerateBackupData) {
            copyOldNoteAttachments()
            restoreOldNoteData()
            clearTempFiles()
        } else {
            FileUtil.getContentFromFile(plugin.getFileDescriptor(filePath))?.apply {
                val richNoteList = JsonToRichNoteConverter.jsonToRichNoteList(this, { errorCode ->
                    AppLogger.BR.d(TAG, "onRestore $tag error, code = $errorCode")
                    when (errorCode) {
                        JsonToRichNoteConverter.ERROR_MOVER_RESTORE_RESOLVE_JSON -> {
                            MigrationConstants.getFailCodeAndDescription(
                                NoteRestorePlugin,
                                MigrationConstants.ERROR_CODE_RICH_NOTE_MOVER_RESTORE_RESOLVE_JSON
                            )
                        }

                        JsonToRichNoteConverter.ERROR_MOVER_RESTORE_SPEECH_LOG -> {
                            MigrationConstants.getFailCodeAndDescription(
                                NoteRestorePlugin,
                                MigrationConstants.ERROR_CODE_RICH_NOTE_MOVER_RESTORE_SPEECH_LOG
                            )
                        }
                    }
                }, { remoteRichNote ->
                    AppLogger.BR.d(TAG, "onRestore $tag merge richtext")
                    val localData = AppDatabase.getInstance().richNoteDao().getByLocalId(remoteRichNote.richNote.localId)
                    mergeRichText(remoteRichNote, localData)
                })

                if (!richNoteList.isNullOrEmpty()) {
                    insertMergeDataToDb(true)
                    updateMergeDataToDb(true)
                    AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
                }
            }
        }
        NoteSearchManagerWrapper.notifyDataChange()
    }

    open fun getMigrationPath(): String {
        AppLogger.RED_MIGRATION.d(TAG, "getMigrationPath$FILE_RICH_NOTE")
        return FILE_RICH_NOTE
    }

    open fun getBackUpRichNote(): List<RichNoteWithAttachments> {
        return AppDatabase.getInstance().richNoteDao()
            .getAllNonEncryptRichNoteWithAttachmentsToBackup()
    }

    private fun clearTempFiles() {
        val tempPath = Checker.getSwitchedTargetPath(context, backupFilePath)
        FileUtil.deleteFile(File(tempPath))
        AppLogger.RED_MIGRATION.v(TAG, "clearTempFiles tempPath =$tempPath")
    }

    private fun restoreOldNoteData() {
        //restore数据
        val restoreFilePath = Checker.getSwitchedTargetPath(context, backupFilePath + File.separator + StringEncodeDecode.decode(NOTE_BR_FILE))
        AppLogger.RED_MIGRATION.v(TAG, "restoreOldNoteData restoreFilePath=$restoreFilePath")

        val bundle = BackupRestoreUtil.restoreNoteRecord(restoreFilePath)
        if (bundle != null && bundle.getBoolean(BackupRestoreConstant.IS_OLD_NOTE_BACKUP_DATA)) {
            restoreOldBackupAttachment(bundle)
        }
    }

    private fun copyOldNoteAttachments() {
        //copy sdcard/Android/data/com.xxx.backuprestore/Backup/Data/xxxx-xx-xx-xxxxxx/Note/picture
        //to sdcard/Android/data/com.xxx.note/Backup/Data/xxxx-xx-xx-xxxxxx/Note/picture
        val src = backupFilePath + File.separator + BackupRestoreConstant.OLD_PICTURE_FILE_BACKUP
        val dest = Checker.getSwitchedTargetPath(context, src)
        val result = plugin.restoreAppData(src, dest)
        AppLogger.RED_MIGRATION.d(TAG, "copyOldNoteAttachments restoreAppData src=$src, dest=$dest, result=$result")

        //copy sdcard/Android/data/com.xxx.note/Backup/Data/xxxx-xx-xx-xxxxxx/Note/picture
        //to data/data/com.xxx.note/files
        FileUtil.copyFiles(File(dest), context.filesDir)
        AppLogger.RED_MIGRATION.d(TAG, "copyOldNoteAttachments copy $dest to ${context.filesDir}")
    }

    private fun restoreOldBackupAttachment(bundle: Bundle) {
        val dataFilesDir = context.filesDir.absolutePath
        val attachments: List<String>? = bundle.getStringArrayList(BackupRestoreConstant.NOTE_ATTACHMENTS)
        if (attachments != null && attachments.isNotEmpty()) {
            for (i in attachments.indices) {
                val info = attachments[i]
                val guid = info.split(BackupRestoreConstant.NOTE_ATTACHMENTS_SPLIT).toTypedArray()[0]
                val attachmentId = info.split(BackupRestoreConstant.NOTE_ATTACHMENTS_SPLIT).toTypedArray()[1]
                val fd = plugin.getFileDescriptor("$backupFilePath/picture/$attachmentId")
                val destFile = File(dataFilesDir + File.separator + guid + File.separator + attachmentId + BackupRestoreConstant.THUMB_END_FIX)
                com.oplus.migrate.utils.FileUtil.createFile(destFile)
                com.oplus.migrate.utils.FileUtil.saveFile(fd, destFile)
                val bitmap = BitmapFactory.decodeFile(destFile.absolutePath)
                val width = bitmap.width
                val height = bitmap.height
                RichNoteRepository.updateAttachWidthAndHeight(attachmentId, width, height)
            }
        }
    }

    @Suppress("ComplexCondition")
    private fun mergeRichText(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?) {
        //如果本地没有相同id便签，直接作为新建便签插入。
        AppLogger.BR.d(TAG, "mergeRichText ${moveOpsRecord(remoteData)}")
        if (relatedData == null) {
            AppLogger.RED_MIGRATION.d(RichNoteStrategy.TAG, "migration merge richtext, remoteData = ${remoteData?.richNote?.globalId}")
            remoteData!!.resetToNewState()
            updateText(remoteData)
            SkinManager.downSkin(remoteData.richNote.skinId)
            mRichNoteCache.add(remoteData)
            insertMergeDataToDb()

            //如果local id相同，比较便签内容及相应字段，判断是否符合相同便签条件。
        } else {
            val sameRawTitle = (remoteData!!.richNote.rawTitle.hashCode() == relatedData!!.richNote.rawTitle.hashCode())
                    && TextUtils.equals(remoteData!!.richNote.rawTitle, relatedData.richNote.rawTitle)
            val sameRawContent = NoteFeatureUtil.isRawTextSame(relatedData, remoteData)
            val sameReminder = (remoteData.richNote.alarmTime == relatedData.richNote.alarmTime)
            val sameSkin = (remoteData.richNote.skinId == relatedData.richNote.skinId)
            val sameFolder = (remoteData.richNote.folderGuid == relatedData.richNote.folderGuid)
            val sameTopStatus = (remoteData.richNote.topTime == 0L &&  relatedData.richNote.topTime == 0L) ||
                    (remoteData.richNote.topTime > 0 &&  relatedData.richNote.topTime > 0)


            val remoteCoverPic =
                remoteData.attachments?.find { it.type == Attachment.TYPE_COVER_PAINT }
            val relateCoverPic =
                relatedData.attachments?.find { it.type == Attachment.TYPE_COVER_PAINT }
            val sameDoodle =
                ((remoteCoverPic?.attachmentId ?: -1) == (relateCoverPic?.attachmentId ?: -1))
            val isEncrypt = FolderUtil.getFolderEncrypt(relatedData.richNote.folderGuid) == FolderInfo.FOLDER_ENCRYPTED &&
                relatedData.richNote.folderGuid != FolderInfo.FOLDER_GUID_ENCRYPTED


            //符合相同便签条件 需要增加涂鸦
            AppLogger.RED_MIGRATION.d(TAG, "sameDoodle" + sameDoodle)
            if (sameRawTitle && sameRawContent && sameReminder && sameSkin && sameFolder && sameTopStatus && sameDoodle) {
                /**数据相同：1、旧手机笔记在全部笔记，新手机在最近删除，再次搬家，新手机的笔记恢复到全部笔记
                 * 2、其他情况，不做处理，直接使用新手机中的数据。
                 * 加密笔记本遵守这个条件(加密笔记不能恢复到全部笔记，会有隐私泄露风险)
                 * */
                var restoreDeletedData = false
                if (!remoteData.richNote.deleted && remoteData.richNote.recycleTime == 0L &&
                    (relatedData.richNote.recycleTime > 0 || relatedData.richNote.deleted) &&
                    !isEncrypt) {
                    relatedData.richNote.state = RichNote.STATE_MODIFIED
                    relatedData.richNote.recycleTime = 0
                    relatedData.richNote.deleted = false
                    relatedData.richNote.encrypted = FolderInfo.FOLDER_UNENCRYPTED
                    relatedData.richNote.encryptedPre = FolderInfo.FOLDER_UNENCRYPTED
                    mUpdateRichNoteCache.add(relatedData)

                    updateMergeDataToDb()
                    restoreDeletedData = true
                }
                /*相同笔记判定 保留最后修改时间*/
                if ((remoteData.richNote?.updateTime ?: 0) > (relatedData?.richNote?.updateTime ?: 0)) {
                    relatedData.speechLogInfo?.speechMark = remoteData?.richNote?.extra?.speechLogExtra?.markList
                    relatedData.speechLogInfo?.combinedCard = remoteData?.richNote?.extra?.speechLogExtra?.entityGroup
                }
                updateLrcAttachment(remoteData, relatedData)
                updateAsrLrcAttachment(remoteData, relatedData)
                mUpdateRichNoteCache.add(relatedData)
                updateMergeDataToDb()
                AppLogger.RED_MIGRATION.d(TAG, "mergeRichText same and use local data, restoreDeletedData $restoreDeletedData")
            } else {
                AppLogger.RED_MIGRATION.d(TAG, "mergeRichText reNewRichNote from remote data")
                remoteData.resetToNewState()
                updateText(remoteData)
                SkinManager.downSkin(remoteData.richNote.skinId)

                //重新生成富文本数据并插入数据库(如果新旧手机笔记的localId相同，重新生成富文本数据时，需要保留原数据的附件，bug:3629824)
                val shouldDeleteOriginalAttachment = (remoteData.richNote.localId != relatedData.richNote.localId)
                mRichNoteCache.add(RichNoteRepository.reNewRichNote(remoteData, shouldDeleteOriginalAttachment))
                insertMergeDataToDb()
            }
        }
    }

    private fun updateText(data: RichNoteWithAttachments) {
        val textOut = StringBuilder()
        val htmlText = HtmlTransformManagerFactory.gainHtmlTransformManager().toHtmlText(data.richNote.rawText)
        val itemNodeList = HtmlStandardParser.fromHtml(source = htmlText, shouldSkipTidying = true)
        itemNodeList.forEach { iItemNode ->
            when (iItemNode) {
                is SpannedNode -> {
                    if (iItemNode.type == SpannedNode.TYPE_TEXT) {
                        textOut.append( SpannableStringBuilder(iItemNode.data))
                    } else if (iItemNode.type == SpannedNode.TYPE_CARD) {
                    } else {
                        textOut.append( SpannableStringBuilder(iItemNode.data))
                    }
                }
                is MediaNode -> {}
            }
        }
        if (textOut.isNullOrEmpty()){
            data.richNote.text = HtmlParser.parse(data.richNote.rawText).text.filterNot { it == ImageFormat.PLACE_HOLDER_CHARACTER }
        } else {
            data.richNote.text = textOut.toString()
        }
        data.richNote.text = data.richNote.text.filterNot { it in SPECIAL_CHARS }
        if (data.richNote.rawTitle != null) {
            data.richNote.title = HtmlParser.parse(data.richNote.rawTitle!!).text
        }
    }

    private fun insertMergeDataToDb (immediate : Boolean = false) {
        val repository = RichNoteRepository
        val size = mRichNoteCache.size

        if ((size >= MigrationConstants.MERGE_COUNT_LIMIT) || (immediate && size > 0)) {
            repository.insertList(mRichNoteCache)
            mRichNoteCache.clear()
            NoteSearchManagerWrapper.notifyDataChange()
        }
    }

    private fun updateMergeDataToDb(immediate: Boolean = false) {
        val size = mUpdateRichNoteCache.size

        if ((size >= MigrationConstants.MERGE_COUNT_LIMIT) || (immediate && size > 0)) {
            RichNoteRepository.updateList(mUpdateRichNoteCache)
            mUpdateRichNoteCache.clear()
            NoteSearchManagerWrapper.notifyDataChange()
        }
    }

    private fun updateLrcAttachment(
        remoteData: RichNoteWithAttachments,
        localData: RichNoteWithAttachments
    ) {
        val remoteLrcAttachment = remoteData.getLrcAttachment()
        val localLrcAttachment = localData.getLrcAttachment()
        if (remoteData.richNote.updateTime >= localData.richNote.updateTime) {
            /**
             * 远端数据更新时间更晚，替换LRC附件
             */
            val newAttachmentList = localData.attachments?.toMutableList()
            newAttachmentList?.remove(localLrcAttachment)
            if (remoteLrcAttachment != null) {
                newAttachmentList?.add(remoteLrcAttachment)
            }
            localData.attachments = newAttachmentList
            AppLogger.CLOUDKIT.d(TAG, "replace lrc attachment with:${remoteLrcAttachment?.attachmentId}")
        } else {
            AppLogger.CLOUDKIT.d(TAG, "not replace lrc attachment")
        }
    }

    private fun updateAsrLrcAttachment(
        remoteData: RichNoteWithAttachments,
        localData: RichNoteWithAttachments
    ) {
        var changed = false
        val needUpdateFromRemote = remoteData.richNote.updateTime >= localData.richNote.updateTime
        if (needUpdateFromRemote) {
            /**
             * 远端数据更新时间更晚，替换LRC附件
             */
            val newAttachmentList = localData.attachments?.toMutableList()

            val remoteVoiceAttachments = remoteData.getIdentifyVoiceAttachments()
            val remoteAsrLrcAttachments = remoteData.getASRLrcAttachments()
            val localVoiceAttachments = localData.getIdentifyVoiceAttachments()
            val localAsrLrcAttachments = localData.getASRLrcAttachments()

            localVoiceAttachments?.forEach { localVoice ->
                val remoteVoice = remoteVoiceAttachments?.find { it.attachmentId == localVoice.attachmentId }
                val localLrcAttachment = localAsrLrcAttachments?.find { it.attachmentId == localVoice.extra?.asrAttachId }
                val remoteLrcAttachment = remoteAsrLrcAttachments?.find { it.attachmentId == remoteVoice?.extra?.asrAttachId }
                if (remoteVoice != null && remoteLrcAttachment != null) {
                    newAttachmentList?.remove(localVoice)
                    newAttachmentList?.add(remoteVoice)
                    newAttachmentList?.remove(localLrcAttachment)
                    newAttachmentList?.add(remoteLrcAttachment)
                    changed = true
                    //这里需要更新一下 attachment，因为后续整体更新的时候，attachment id 一样的不会更新
                    RichNoteRepository.updateAttachment(remoteVoice)
                    AppLogger.RED_MIGRATION.d(TAG, "updateAsrLrcAttachment remove lrc ${localLrcAttachment?.attachmentId} " +
                                "then add ${remoteLrcAttachment.attachmentId}"
                    )
                }
            }
            if (changed) {
                localData.attachments = newAttachmentList
            }
        }
    }

    private fun moveOpsRecord(remoteData: RichNoteWithAttachments?): String {
        return "remoteData = id: ${remoteData?.richNote?.localId?.take(com.oplus.note.common.Constants.INT_8)} " +
                "titleLength:${remoteData?.richNote?.title?.length} " +
                "textCount:${remoteData?.richNote?.text?.length} attachmentSize:${remoteData?.attachments?.size}"
    }
}