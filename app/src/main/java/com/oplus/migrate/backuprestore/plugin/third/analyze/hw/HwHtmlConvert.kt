/************************************************************
 * * Copyright 2020-2030 OPLUS Mobile Comm Corp., Ltd.
 * * File: HtmlConvert.kt
 * * Description:
 * * Version: 1.0
 * * Date: 2023/7/14
 * * Author: niexiaokang
 * *---------------------Revision History:---------------------
 * *  <author>           <date>      <version>   <desc>
 * *  niexiaokang       2023/7/14    1.0       build this module
 ************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.hw

import com.oplus.note.logger.AppLogger
import org.jsoup.nodes.Node

object HwHtmlConvert {
    private const val TAG = "HwHtmlConvert"

    /**
     * 目前input未添加富文本相关属性
     */
    fun replaceToInput(node: Node): String {
        var checked = node.attr(HwHtmlFormats.CHECKED)
        if (checked.isEmpty()) {
            checked = HwHtmlFormats.UNCHECKED
        }
        return "${HwHtmlFormats.START_INPUT}\"$checked\">"
    }

    fun replaceToSpan(tags: String, format: String, divStyle: String, obj: Any? = null): String {
        var format = format
        if (!format.contains(HwHtmlFormats.FIND_DIV)) {
            format += divStyle.ifEmpty { HwHtmlFormats.START_DIV }
        }
        if (!format.contains(HwHtmlFormats.START_SPAN)) {
            format += HwHtmlFormats.START_SPAN
        }
        if (format.endsWith(HwHtmlFormats.BRACKET)) {
            format = format.substring(0, format.length - 2) + " "
        }
        if (tags.contains(HwHtmlFormats.B) && !format.contains(HwHtmlFormats.TEXT_WEIGHT_BOLD)) {
            format += HwHtmlFormats.TEXT_WEIGHT_BOLD + " "
        } else if (tags.contains(HwHtmlFormats.U) && !format.contains(HwHtmlFormats.TEXT_DECORATION_UNDERLINE)) {
            format += HwHtmlFormats.TEXT_DECORATION_UNDERLINE + " "
        } else if (tags.contains(HwHtmlFormats.I) && !format.contains(HwHtmlFormats.TEXT_ITALIC)) {
            format += HwHtmlFormats.TEXT_ITALIC + " "
        } else if (tags.contains(HwHtmlFormats.FONT)) {
            if (obj != null && obj is Node) {
                val size = HwTextAttrHelper.getSizeFormHuawei(obj)
                size?.let { format += HwHtmlFormats.TEXT_SIZE_PREFIX + it + " " }
                val colorOrHighlight = HwTextAttrHelper.getTextColorOrHighlight(obj)
                colorOrHighlight?.let { format += "$colorOrHighlight " }
            }
        } else {
            AppLogger.BASIC.d(TAG, "replaceToSpan else:$tags")
        }
        format = format.trim() + HwHtmlFormats.BRACKET
        return format
    }

    fun analyzeDivAlign(node: Node): String {
        return when (node.attr(HwHtmlFormats.ALIGN)) {
            HwHtmlFormats.RIGHT -> HwHtmlFormats.START_DIV_END
            HwHtmlFormats.CENTER -> HwHtmlFormats.START_DIV_CENTER
            else -> HwHtmlFormats.START_DIV_START
        }
    }

    fun getImgData(node: Node): String {
        var imgName = ""
        val content = node.toString()
        if (content.contains(HwHtmlFormats.SRC) && content.contains(HwHtmlFormats.BRACKET)) {
            val begin = content.indexOf(HwHtmlFormats.SRC) + HwHtmlFormats.SRC.length
            val temp = content.substring(begin)
            val end = temp.indexOf(HwHtmlFormats.BRACKET)
            imgName = temp.substring(0, end)
        }
        return imgName
    }
}