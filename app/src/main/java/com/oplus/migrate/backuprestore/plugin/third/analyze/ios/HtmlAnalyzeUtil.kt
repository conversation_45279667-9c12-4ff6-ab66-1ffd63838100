/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: HtmlAnalyzeUtil.kt
 * * Description: HtmlAnalyze
 * * Version: 1.0
 * * Date: 2022/11/17
 * * Author: xushuya
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.ios

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageDecoder
import com.google.gson.Gson
import com.nearme.note.logic.ThumbFileConstants
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.richtext.core.parser.HtmlConvert
import com.oplus.richtext.core.parser.HtmlFormats
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStreamReader
import java.util.Objects
import java.util.UUID

class HtmlAnalyzeUtil {

    companion object {
        private const val TAG = "HtmlAnalyzeUtil"
        private const val HTML_TYPE = "html"
        private const val SIZE = 2048
        private const val LIMIT_RAW_TITLE = 100
        private const val TEXT_DECORATION_UNDERLINE = "text-decoration-underline"
        private const val TEXT_DECORATION_LINE_THROUGH = "text-line-through"
        private const val TEXT_BOLD_STYLE = "text-weight-bold"
        private const val TEXT_ITALIC = "text-italic"
        private const val BRACKET = "\">"
    }

    private var notePath = ""
    private var mRawTitle = ""
    private var mTitle = ""
    private var mText = ""

    private var mRawText = StringBuilder()

    private var mNoteId = ""
    private var attachList: ArrayList<Attachment> = ArrayList()
    private var mNoteInfo: HashMap<String, Objects> = HashMap()
    private var textStyles = mutableMapOf<String, String>()
    private var spanStr = ""
    private var brInSpan = 0
    var endTag = ""
    var h1TextList = mutableListOf<String>()

    fun analyze(path: String, noteId: String) {
        clear()
        val file = File(path)
        if (!file.isFile || !isHtmlFile(file)) {
            return
        }
        mNoteId = noteId
        notePath = file.parent?.toString() ?: ""
        val mHtml = readHtml(path)
        val cleanedHtml = mHtml.replace("[\\u0000-\\u001F]+".toRegex(), "")
        val document = Jsoup.parse(cleanedHtml)
        document.outputSettings(Document.OutputSettings().prettyPrint(false));
        analyzeDocument(document)
    }

    private fun reduceLineBreaks(htmlString: StringBuilder): String {
        return htmlString.replace(Regex("[\r\n]"), "")
            .replace(Regex("(<br\\s*/?>\\s*){2,}", RegexOption.IGNORE_CASE)) { matchResult ->
                val count = matchResult.value.count { it == '<' }
                if (count < 2) {
                    matchResult.value
                } else {
                    "<br>".repeat(count - 1)
                }
            }
    }

    fun getTitle(): String {
        return mTitle
    }

    fun getText(): String {
        var text = mText
        if (mText.contains(mTitle)) {
            val index = mText.indexOf(mTitle)
            if (index == 0) {
                text = mText.substring(mTitle.length)
            }
        }
        return text.trim()
    }

    fun getRawTitle(): String {
        return mRawTitle
    }

    fun getRawText(): String {
        return mRawText.toString()
    }

    fun getAttachmentList(): ArrayList<Attachment> {
        return attachList
    }


    private fun clear() {
        mNoteId = ""
        mRawTitle = ""
        mTitle = ""
        mText = ""
        mRawText = StringBuilder()

        if (attachList.isNotEmpty()) {
            attachList.clear()
        }
        if (mNoteInfo.isNotEmpty()) {
            mNoteInfo.clear()
        }
    }

    private fun analyzeDocument(document: Document) {
        val elements = document.select(HtmlFormats.BODY)
        elements.select(HtmlFormats.VIDEO).remove()
        mText = elements.text()
        for (body in elements) {
            val nodeList = body.childNodes()
            analyzeNodeList(nodeList)
        }
    }

    private fun analyzeNodeList(nodeList: List<Node>) {
        for (i in nodeList.indices) {
            clearTag()
            val sbContent = StringBuilder()
            val node = nodeList[i]
            recurse(node, sbContent)
            val tagInfo = sbContent.toString().trim()
            if (i == 0 && node.normalName() == HtmlFormats.H1) {
                val titleTextList = recurseTitle(node)
                val tempTitle = titleTextList.firstOrNull()
                if ((tempTitle?.length ?: 0) <= LIMIT_RAW_TITLE) {
                    mRawTitle = "<div>${tempTitle}</div>"
                } else {
                    tempTitle?.substring(0, LIMIT_RAW_TITLE).let {
                        mRawTitle = "<div>$it</div>"
                    }
                    mRawText.append(
                        "<div class=${HtmlFormats.H1}>${tempTitle?.substring(LIMIT_RAW_TITLE)}</div>"
                    )
                }
                for (index in 1 until titleTextList.size) {
                    mRawText.append("<div class=${HtmlFormats.H1}>${titleTextList[index]}</div>")
                }
                h1TextList.clear()
            } else {
                mRawText.append(tagInfo)
            }
        }
    }

    private fun recurse(node: Node, sbContent: StringBuilder) {
        //无内容
        if (node is Element && node.nodeName() == HtmlFormats.B && node.text().isNullOrEmpty()) {
            return
        }
        val endTag = parseContent(node, sbContent)
        val nodeList = node.childNodes()
        if (nodeList.size > 0) {
            for (n in nodeList) {
                recurse(n, sbContent)
            }
        }
        if (node.nodeName() == HtmlFormats.B || node.nodeName() == HtmlFormats.U || node.nodeName() == HtmlFormats.DEL || node.nodeName() == HtmlFormats.I) {
            return
        }
        sbContent.append(endTag)
    }

    /**
     * H1标签内有br换行，第一个br前的作为标题，后面所有text作为内容
     */
    private fun recurseTitle(node: Node): List<String> {
        if (node.childNodes().isNotEmpty()) {
            for (child in node.childNodes()) {
                if (child.nodeName() == HtmlFormats.TEXT) {
                    h1TextList.add(child.toString())
                } else {
                    recurseTitle(child)
                }
            }
        }
        return h1TextList
    }
    @Suppress("LongMethod")
    private fun parseContent(node: Node, sbNode: StringBuilder): String {
        val nodeName = node.nodeName()
        when (nodeName) {
            HtmlFormats.HTML, HtmlFormats.BODY -> sbNode.append("")
            HtmlFormats.H1, HtmlFormats.H2, HtmlFormats.H3, HtmlFormats.H4, HtmlFormats.H5, HtmlFormats.H6 -> {
                sbNode.append("<div class=$nodeName>")
                endTag = HtmlFormats.END_DIV
            }

            HtmlFormats.I -> {
                if (!textStyles.contains(TEXT_ITALIC)) {
                    textStyles[TEXT_ITALIC] = "${HtmlFormats.START_SPAN}$TEXT_ITALIC$BRACKET"
                }
                endTag = HtmlFormats.END_SPAN
            }

            HtmlFormats.B -> {
                if (!textStyles.contains(TEXT_BOLD_STYLE)) {
                    textStyles[TEXT_BOLD_STYLE] = "${HtmlFormats.START_SPAN}$TEXT_BOLD_STYLE$BRACKET"
                }
                endTag = HtmlFormats.END_SPAN
            }

            HtmlFormats.U -> {
                if (!textStyles.contains(TEXT_DECORATION_UNDERLINE)) {
                    textStyles[TEXT_DECORATION_UNDERLINE] = "${HtmlFormats.START_SPAN}$TEXT_DECORATION_UNDERLINE$BRACKET"
                }
                endTag = HtmlFormats.END_SPAN
            }

            HtmlFormats.DEL -> {
                if (!textStyles.contains(TEXT_DECORATION_LINE_THROUGH)) {
                    textStyles[TEXT_DECORATION_LINE_THROUGH] = "${HtmlFormats.START_SPAN}${TEXT_DECORATION_LINE_THROUGH}$BRACKET"
                }
                endTag = HtmlFormats.END_SPAN
            }

            HtmlFormats.TEXT -> {
                recurseBr(node)
                parseText(node, sbNode)
            }

            HtmlFormats.TABLE -> {
                sbNode.append("<table attachid=${UUID.randomUUID()}>")
                sbNode.append("<tbody>")
                endTag = "</tbody></table>"
            }

            HtmlFormats.BR -> {
                if (node.previousSibling()?.nodeName() == HtmlFormats.IMG ||
                    node.previousSibling()?.nodeName() == HtmlFormats.TABLE
                ) {
                    return endTag
                } else if ((node.parentNode()?.nodeName() == HtmlFormats.DEL ||
                            node.parentNode()?.nodeName() == HtmlFormats.B ||
                            node.parentNode()?.nodeName() == HtmlFormats.U ||
                            node.parentNode()?.nodeName() == HtmlFormats.I) && (
                            node.previousSibling()?.nodeName() == HtmlFormats.BR ||
                                    node.previousSibling()?.nodeName() == HtmlFormats.TEXT)
                ) {
                    return endTag
                } else if (node.parentNode()?.nodeName() == HtmlFormats.H1 ||
                    node.parentNode()?.nodeName() == HtmlFormats.H2 ||
                    node.parentNode()?.nodeName() == HtmlFormats.H3 ||
                    node.parentNode()?.nodeName() == HtmlFormats.H4 ||
                    node.parentNode()?.nodeName() == HtmlFormats.H5 ||
                    node.parentNode()?.nodeName() == HtmlFormats.H6
                ) {
                    return endTag
                } else {
                    sbNode.append(HtmlFormats.START_BR)
                }
            }

            HtmlFormats.UL -> {
                val listStyle = if (node.attr("class")
                        .contains("list-style-line")
                ) HtmlFormats.START_UL_LINE else HtmlFormats.START_UL
                sbNode.append(listStyle)
                endTag = HtmlFormats.END_UL
            }

            HtmlFormats.OL -> {
                sbNode.append(HtmlFormats.START_OL)
                endTag = HtmlFormats.END_OL
            }

            HtmlFormats.LI -> {
                val className = node.attr("class")
                val listItemStyle = when {
                    className.contains("unchecked") -> HtmlFormats.START_LI_UNCHECK
                    className.contains("checked") -> HtmlFormats.START_LI_CHECKED
                    else -> HtmlFormats.START_LI
                }
                sbNode.append(listItemStyle)
                endTag = HtmlFormats.END_LI
            }
            HtmlFormats.TR -> {
                sbNode.append(HtmlFormats.START_TR)
                endTag = HtmlFormats.END_TR
            }
            HtmlFormats.TD -> {
                sbNode.append(HtmlFormats.START_TD)
                endTag = HtmlFormats.END_TD
            }
            HtmlFormats.IMG -> sbNode.append(parseImg(node))
            else -> {}
        }
        return endTag
    }

    private fun parseImg(node: Node): String {
        val fileName = HtmlConvert.getImgName(node)
        val imageInfo = renameFile(fileName)
        AppLogger.BASIC.d(
            TAG,
            "parseImg  mNoteId: $mNoteId imageId: ${imageInfo.first}  width: ${imageInfo.second}  height: ${imageInfo.third}"
        )

        val attachment = Attachment(imageInfo.first, mNoteId, Attachment.TYPE_PICTURE)
        attachList.add(attachment)
        return "<img src=\"${imageInfo.first}\"  width=\"${imageInfo.second}\" height=\"${imageInfo.third}\"/>"
    }

    private fun renameFile(name: String): Triple<String, Int, Int> {
        val startTime = System.currentTimeMillis()
        val id = UUID.randomUUID().toString()
        val file = File(notePath, name)
        val newName = "$id${ThumbFileConstants.THUMB}"
        val newFile = File(notePath, newName)
        if (file.exists() && !newFile.exists()) {
            if (!isPngFile(file)) {
                convertToPng(file, newFile)
                file.delete()
            } else {
                file.renameTo(newFile)
            }
        }
        val imageDimensions = getImageDimensions(newFile.path)
        AppLogger.DEBUG.d(TAG, "renameFile# start-end = ${System.currentTimeMillis() - startTime}")
        return Triple(id, imageDimensions.first, imageDimensions.second)
    }

    private fun isPngFile(file: File): Boolean {
        val startTime = System.currentTimeMillis()
        val pngSignature = byteArrayOf(-119, 80, 78, 71, 13, 10, 26, 10)  // PNG文件的魔数
        if (file.length() < pngSignature.size) {
            return false
        }
        FileInputStream(file).use { fis ->
            val header = ByteArray(pngSignature.size)
            fis.read(header, 0, header.size)
            AppLogger.DEBUG.d(
                TAG,
                "isPngFile# start-end = ${System.currentTimeMillis() - startTime}"
            )
            return header.contentEquals(pngSignature)
        }
    }

    @Throws(IOException::class)
    fun convertToPng(sourceFile: File, outputFilePath: File) {
        val startTime = System.currentTimeMillis()
        val source = ImageDecoder.createSource(sourceFile)
        val bitmap: Bitmap = ImageDecoder.decodeBitmap(source)
        // 将 Bitmap 保存为 PNG 文件
        FileOutputStream(outputFilePath).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        }
        AppLogger.DEBUG.d(
            TAG,
            "convertToPng# start-end = ${System.currentTimeMillis() - startTime}"
        )
    }

    private fun getImageDimensions(filePath: String): Pair<Int, Int> {

        val startTime = System.currentTimeMillis()
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true // 只获取图片边界信息
        }

        // 加载图片
        BitmapFactory.decodeFile(filePath, options)

        // 获取宽高
        val width = options.outWidth
        val height = options.outHeight

        AppLogger.DEBUG.d(
            TAG,
            "getImageDimensions# start-end = ${System.currentTimeMillis() - startTime}"
        )
        return Pair(width, height)
    }

    private fun readHtml(fileName: String): String {
        return try {
            FileInputStream(fileName).use { fis ->
                InputStreamReader(fis, "utf-8").use { reader ->
                    BufferedReader(reader).use { br ->
                        br.readText()
                    }
                }
            }
        } catch (e: FileNotFoundException) {
            AppLogger.BASIC.e(TAG, "FileNotFoundException: ${e.message}")
            ""
        } catch (e: IOException) {
            AppLogger.BASIC.e(TAG, "IOException: ${e.message}")
            ""
        }
    }

    fun getIOSFileAttr(filePath: String): IOSFileAttr {
        val gson = Gson()
        val jsonFile = File(filePath)
        val jsonContent = jsonFile.readText()
        val attr = gson.fromJson(jsonContent, IOSFileAttr::class.java)
        return attr
    }

    private fun isHtmlFile(temp: File): Boolean {
        val fileName = temp.name
        if (fileName.isNotEmpty() && fileName.endsWith(HTML_TYPE)) {
            return true
        }
        return false
    }

    private var tempText = ""
    private fun parseText(node: Node, sbNode: StringBuilder) {
        val content = node.toString()
        if (content.isEmpty()) {
            return
        }
        if (isIndentation(content)) {
            tempText = content
        }

        val tempSb = StringBuilder()
        parseTextStyles()
        tempSb.append(spanStr)
        tempSb.append(content)
        tempSb.append(endTag)
        tempText = tempSb.replace("[\r\n]".toRegex(), "")
        sbNode.append(tempText)
        for (i in 0 until brInSpan) {
            sbNode.append(HtmlFormats.START_BR)
        }
        //若Text节点下个节点是若干BR然后接一个Text,则Span要保留
        val hasNextText = hasNextTextSameSpan(node)
        clearTag(hasNextText)
    }

    private fun parseTextStyles() {
        if (textStyles.isEmpty()) {
            spanStr = ""
        } else {
            /**
             * 处理textStyles并排序
             */
            if (textStyles.contains(TEXT_DECORATION_LINE_THROUGH)) {
                spanStr += textStyles[TEXT_DECORATION_LINE_THROUGH]
            }
            if (textStyles.contains(TEXT_DECORATION_UNDERLINE)) {
                spanStr += textStyles[TEXT_DECORATION_UNDERLINE]
            }
            if (textStyles.contains(TEXT_BOLD_STYLE)) {
                spanStr += textStyles[TEXT_BOLD_STYLE]
            }
            if (textStyles.contains(TEXT_ITALIC)) {
                spanStr += textStyles[TEXT_ITALIC]
            }
        }

        if (endTag.isEmpty()) {
            endTag = HtmlFormats.END_SPAN
        } else if (endTag == HtmlFormats.END_SPAN) {
            val builder = StringBuilder()
            for (i in 0 until textStyles.size) {
                builder.append(HtmlFormats.END_SPAN)
            }
            endTag = builder.toString().trim()
        }
    }
    /**
     * 判断文本是否全是空格
     */
    private fun isIndentation(text: String): Boolean {
        return text.trim().isBlank()
    }

    private fun clearTag(hasNextText: Boolean = false) {
        endTag = ""
        if (!hasNextText) {
            textStyles.clear()
        }
        spanStr = ""
        brInSpan = 0
    }


    private fun recurseBr(node: Node) {
        if (node.nextSibling()?.nodeName() == HtmlFormats.BR
            && (node.parentNode()?.nodeName() == HtmlFormats.DEL ||
                    node.parentNode()?.nodeName() == HtmlFormats.B ||
                    node.parentNode()?.nodeName() == HtmlFormats.U ||
                    node.parentNode()?.nodeName() == HtmlFormats.I
                    )
        ) {
            //span内文本后有BR，需要去掉BR，把BR放到span后
            brInSpan++
            node.nextSibling()?.let {
                recurseBr(it)
            }
        }
    }

    private fun hasNextTextSameSpan(node: Node): Boolean {
        if (node.nextSibling()?.nodeName() == "br") {
            val next = node.nextSibling()
            return next?.let { hasNextTextSameSpan(it) } ?: false
        } else if (node.nextSibling()?.nodeName() == "#text") {
            return true
        } else {
            return false
        }
    }
}