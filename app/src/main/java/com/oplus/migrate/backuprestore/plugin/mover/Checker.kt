package com.oplus.migrate.backuprestore.plugin.mover

import android.content.Context
import com.nearme.note.util.StringEncodeDecode
import com.oplus.migrate.backuprestore.BackupRestoreConstant.NOTE_BR_FILE
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.migrate.utils.FileUtil
import com.oplus.note.logger.AppLogger
import java.io.File
import java.io.FileDescriptor
import java.io.FileInputStream
import java.io.IOException
import java.util.regex.Matcher
import java.util.regex.Pattern


/*****************************************************************

 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd

 * * VENDOR_EDIT

 * * File: - Checker

 * * Description:

 * * Version: 1.0

 * * Date : 2021/7/4

 * * Author: XXX

 * *

 * * ---------------------- Revision History:----------------------

 * * <author> <date> <version> <desc>

 * * XXX 2021/7/4 1.0 create

 ******************************************************************/
object Checker {

    const val TAG = "Checker"

    fun isNotBrSdkGenerateBackupData(plugin: NoteRestorePlugin, backupPath: String): Boolean {
        val oldDataPath = backupPath + File.separator + "rich_note"
        val fd = plugin.getFileDescriptor(oldDataPath)
        return getSize(fd) == 0L
    }

    @Throws(IOException::class)
    fun getSize(fd: FileDescriptor?): Long {
        FileInputStream(fd).use { fis -> return fis.channel.size() }
    }

    fun prepareOldData(context: Context, plugin: NoteRestorePlugin, backupPath: File) {
        var backupFileName = backupPath.absolutePath + File.separator + StringEncodeDecode.decode(NOTE_BR_FILE)
        AppLogger.RED_MIGRATION.d(TAG, "prepareOldData backupFileName =$backupFileName")

        val fd = plugin.getFileDescriptor(backupFileName)
        val xmlContent = FileUtil.readFromFd(fd)
        //AppLogger.RED_MIGRATION.d(TAG, "prepareOldData copy to xmlContent =$xmlContent")

        backupFileName = getSwitchedTargetPath(context, backupFileName)
        AppLogger.RED_MIGRATION.d(TAG, "prepareOldData copy to backupFileName =$backupFileName")

        val file = File(backupFileName)
        FileUtil.createFile(file)
        FileUtil.saveToFile(file, xmlContent)
    }

    fun getSwitchedTargetPath(context: Context, backupFileName: String): String {
        val result = if (backupFileName.contains("opbackup")) { //非融合搬家备份数据
            backupFileName.replace("opbackup", "Android/data/" + context.packageName)
        } else if (backupFileName.contains("com.coloros.backuprestore")) {
            backupFileName.replace("com.coloros.backuprestore", context.packageName)
        } else if (backupFileName.contains("com.oneplus.backuprestore")) {
            backupFileName.replace("com.oneplus.backuprestore", context.packageName)
        } else {
            val re = getSwitchFromBackupFile(backupFileName)
            backupFileName.replace(re, context.packageName)
        }
        AppLogger.RED_MIGRATION.d(TAG, "getSwitchPkg: from $backupFileName to $result")
        return result
    }

    private fun getSwitchFromBackupFile(backupFileName: String): String {
        val begin = "com."
        val end = ".backuprestore"
        val pattern: Pattern = Pattern.compile("$begin*$end")
        val matcher: Matcher = pattern.matcher(backupFileName)
        if (matcher.find()) {
            return matcher.group(0)
        }
        return ""
    }
}