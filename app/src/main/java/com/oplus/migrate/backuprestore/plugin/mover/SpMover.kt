package com.oplus.migrate.backuprestore.plugin.mover

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import com.nearme.note.MyApplication
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.PrivacyPolicyTool
import com.oplus.backup.sdk.component.plugin.AbstractPlugin
import com.oplus.migrate.backuprestore.plugin.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.NoteBackupPlugin
import com.oplus.migrate.backuprestore.plugin.NoteRestorePlugin
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.search.NoteSearchManager
import com.oplus.note.utils.NoteStatusProviderUtil
import com.oplus.note.utils.NoteStatusProviderUtil.FLAG_RECENT_DEL_FOLDER_ENCRYPT_STATUS
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.todo.search.TodoSearchManager
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.ObjectInputStream
import java.io.ObjectOutputStream

class SpMover(context: Context, backupFilePath: String, plugin: AbstractPlugin) : Mover(context, backupFilePath, plugin) {

    private val TAG = "SpMover"

    private val spList = arrayOf(
        "privacy_policy_alert",     //隐私政策的授权
        "sp_todo_plugin_setting_config", //隐藏、显示已完成的待办
        "note_mode",     //宫格模式、排序方式
        "notebook_ignore_state" //笔记本提示语“忽略”状态
    )


    @SuppressLint("SuspiciousIndentation")
    override fun onBackup() {
        AppLogger.BR.d(TAG, "onBackup sp ")
        spList.forEach {
            val recentDelFolderEncryptStatus =
                NoteStatusProviderUtil.getStatus(context, FLAG_RECENT_DEL_FOLDER_ENCRYPT_STATUS)
                SharedPreferencesUtil.getInstance().putInt(
                    MyApplication.appContext,
                    SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.RECENT_DELETE_ENCRYPTED_KEY,
                    if (recentDelFolderEncryptStatus) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
                )
            val sharePreferencePath = backupFilePath + File.separator + MigrationConstants.FILE_SHARE_PREFERENCE + File.separator + it
            val fileDescriptor = plugin.getFileDescriptor(sharePreferencePath)
            var output: ObjectOutputStream? = null
            try {
                output = ObjectOutputStream(FileOutputStream(fileDescriptor))
                val pref: SharedPreferences = context.getSharedPreferences(it, Context.MODE_PRIVATE)
                output.writeObject(pref.all)
            } catch (e: FileNotFoundException) {
                AppLogger.BR.e(TAG, "onBackup sp FileNotFoundException${e.message}")
                MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_SP_MOVER_BACKUP_FAILD_TO_FOUND_FILE)
            } catch (e: IOException) {
                AppLogger.BR.e(TAG, "onBackup sp IOException${e.message}")
                MigrationConstants.getFailCodeAndDescription(NoteBackupPlugin, MigrationConstants.ERROR_CODE_SP_MOVER_BACKUP_IO_EXCEPTION)
            } finally {
                try {
                    if (output != null) {
                        output.flush()
                        output.close()
                    }
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }

        }
    }

    override fun onRestore(isRestoreOldNoteData: Boolean) {
        AppLogger.BR.d(TAG, "onRestore sp 1.$isRestoreOldNoteData")
        if (isRestoreOldNoteData) {
            return
        }
        var extraStatus: Any? = null
        spList.forEach { spName ->
            val sharePreferencePath = backupFilePath + File.separator + MigrationConstants.FILE_SHARE_PREFERENCE + File.separator + spName
            val fileDescriptor = plugin.getFileDescriptor(sharePreferencePath)
            var input: ObjectInputStream? = null
            try {
                val fis = FileInputStream(fileDescriptor)
                //搬家不存在该sp文件路径会导致ObjectInputStream初始化报IO异常，通过fis.available()>0判断文件存在
                val length = fis.available()
                if (length > 0) {
                    input = ObjectInputStream(fis)
                    val prefEdit: SharedPreferences.Editor = context.getSharedPreferences(spName, Context.MODE_PRIVATE).edit()
                    val entries: Map<String, *> = input.readObject() as Map<String, *>
                    if (entries.isEmpty()) {
                        AppLogger.BR.d(TAG, "onRestore sp 2. entries.isEmpty()")
                    }
                    entries.forEach {
                        val v = it.value
                        val key = it.key
                        AppLogger.BR.d(TAG, "onRestore sp 2. key = $key , v = $v")
                        val isBreak = checkBreakIfNeed(spName, key)
                        if (isBreak) {
                            if (key == PrivacyPolicyTool.KEY_EXTRA_AGREE_STATUS) {
                                extraStatus = v
                            }
                            AppLogger.BR.d(TAG, "onRestore continue")
                        } else {
                            when (v) {
                                is Boolean -> prefEdit.putBoolean(key, v)
                                is Float -> prefEdit.putFloat(key, v)
                                is Int -> {
                                    prefEdit.putInt(key, v)
                                    if (key == SharedPreferencesUtil.RECENT_DELETE_ENCRYPTED_KEY) {
                                        SharedPreferencesUtil.getInstance().remove(
                                            context,
                                            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                                            SharedPreferencesUtil.RECENT_DELETE_ENCRYPTED_KEY
                                        )
                                        NoteStatusProviderUtil.setStatus(
                                            MyApplication.appContext,
                                            NoteStatusProviderUtil.FLAG_RECENT_DEL_FOLDER_ENCRYPT_STATUS,
                                            v == Folder.FOLDER_ENCRYPTED
                                        )
                                    }
                                }

                                is Long -> prefEdit.putLong(key, v)
                                is String -> prefEdit.putString(key, v)
                            }
                        }
                    }
                    prefEdit.apply()
                }
            } catch (e: FileNotFoundException) {
                AppLogger.BR.e(TAG, "onRestore sp FileNotFoundException${e.message}", e)
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_FILE)
            } catch (e: IOException) {
                AppLogger.BR.e(TAG, "onRestore sp IOException${e.message}", e)
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_SP_MOVER_RESTORE_IO_EXCEPTION)
            } catch (e: ClassNotFoundException) {
                AppLogger.BR.e(TAG, "onRestore sp ClassNotFoundException${e.message}", e)
                MigrationConstants.getFailCodeAndDescription(NoteRestorePlugin, MigrationConstants.ERROR_CODE_SP_MOVER_RESTORE_FAILD_TO_FOUND_CLASS)
            } finally {
                try {
                    if (input != null) {
                        input.close()
                    }
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }
        handlePrivacyPolicyStatus(extraStatus)
        initDmpIfNeed()
        val modeFlag: Int = SharedPreferencesUtil.getInstance()
            .getInt(context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SharedPreferencesUtil.HOME_PAGE_MODE_KEY)
        val intent = Intent()
        intent.action = NoteListHelper.ACTION_MODE_FLAG_CHANGE
        intent.putExtra(NoteListHelper.KEY_MODE_FLAG_CHANGE, modeFlag)
        intent.setPackage(context.getPackageName())
        context.sendBroadcast(intent)
    }

    /**
     * 一些操作需要在循环外单独处理,例如：用户须知同意状态和附加功能同意状态
     */
    private fun checkBreakIfNeed(spName: String, key: String): Boolean {
        val isPrivacyPolicySp = spName == PrivacyPolicyTool.SP_NAME
        val isUserOrExtraStatus =
            key == PrivacyPolicyTool.KEY_USER_AGREE_STATUS || key == PrivacyPolicyTool.KEY_EXTRA_AGREE_STATUS
        return isPrivacyPolicySp && isUserOrExtraStatus
    }

    /**
     * 处理隐私政策同意状态（用户须知+附加功能），单独处理
     */
    private fun handlePrivacyPolicyStatus(extraStatus: Any?) {
        val statusMap = handleExtraStatus(extraStatus)
        PrivacyPolicyHelper.setExtraAgreeStatus(context, statusMap)
    }

    /**
     * 处理附加功能同意状态
     */
    private fun handleExtraStatus(extraStatus: Any?): MutableMap<String, Boolean>? {
        return if (extraStatus is Int) {
            val statusMap = mutableMapOf<String, Boolean>()
            val localExtraStatus = PrivacyPolicyHelper.getExtraStatus(context)
            PrivacyPolicyTool.extraStatusMap.forEach { (key, value) ->
                statusMap[key] = checkIsAgree(extraStatus, localExtraStatus, value)
            }
            statusMap
        } else {
            AppLogger.BASIC.d(TAG, "handleExtraStatus value is not int")
            null
        }
    }

    private fun checkIsAgree(extraStatus: Int, localExtraStatus: Int, agreeStatus: Int): Boolean {
        return (extraStatus and agreeStatus == agreeStatus) || (localExtraStatus and agreeStatus == agreeStatus)
    }

    private fun initDmpIfNeed() {
        val isAgreeDmpSearch = PrivacyPolicyHelper.isAgreeDmpSearch()
        AppLogger.BASIC.d(TAG, "initDmpIfNeed isAgreeDmpSearch:$isAgreeDmpSearch")
        if (isAgreeDmpSearch) {
            NoteSearchManager.init(true)
            TodoSearchManager.init(true)
        }
    }
}