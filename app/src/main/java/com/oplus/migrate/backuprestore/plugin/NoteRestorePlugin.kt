package com.oplus.migrate.backuprestore.plugin

import android.content.Context
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.nearme.note.migration.MigrationConstants
import com.oplus.migrate.backuprestore.plugin.mover.MoveManager
import com.oplus.migrate.backuprestore.plugin.mover.Checker
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.RestorePlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.note.logger.AppLogger
import java.io.*

class NoteRestorePlugin : RestorePlugin() {

    private var isCancel: Boolean = false
    @VisibleForTesting
    lateinit var backupPath: String

    private var moveManager: MoveManager? = null
    private var isNotBrSdkGenerateBackupData: Boolean = false

    /*  doc: https://hio.oppo.com/app/ozone/team/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=157883906_157808373
    *   恢复业务流程需继承并实现RestorePlugin，主要流程：
        数据恢复：onCreate --> onPrepare --> onRestore--> onDestroy
        暂停恢复：onPause
        继续恢复：onContinue
        停止恢复：onCancel
    * */
    override fun onCreate(context: Context, brPluginHandler: BRPluginHandler, config: BREngineConfig) {
        super.onCreate(context, brPluginHandler, config)

        isRunning = true
        AppLogger.BR.d(TAG, "onCreate:$config, ${Thread.currentThread().name}")
        backupPath = config.restoreRootPath + File.separator + MigrationConstants.NOTE_FOLDER
        AppLogger.BR.d(TAG, "backupPath:$backupPath")
        moveManager = MoveManager(context, backupPath, this)
    }
    
    override fun onPreview(bundle: Bundle): Bundle? {
        AppLogger.BR.d(TAG, "onPreview")
        return null
    }

    override fun onPrepare(bundle: Bundle): Bundle {
        val prepareBundle = Bundle()
        val backupFolder = File(backupPath)
        AppLogger.BR.d(TAG, "backupFolder:$backupFolder")
        isNotBrSdkGenerateBackupData = Checker.isNotBrSdkGenerateBackupData(this, backupPath)
        AppLogger.BR.d(TAG, "isNotBrSdkGenerateBackupData:$isNotBrSdkGenerateBackupData")
        if (isNotBrSdkGenerateBackupData) {
            Checker.prepareOldData(context,this, backupFolder);
        }

        prepareBundle.putInt(ProgressHelper.MAX_COUNT, moveManager!!.getMoverCount())
        AppLogger.BR.d(TAG, "onPrepare:$prepareBundle, ${Thread.currentThread().name}")
        return prepareBundle
    }

    override fun onRestore(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onRestore, ${Thread.currentThread().name}")
        runCatching {
            moveManager?.onRestore(isNotBrSdkGenerateBackupData)
        }.onFailure {
            AppLogger.BR.e(TAG, "onRestore error:${it.message}")
            isSuccess = false
            resultCode =
                com.oplus.migrate.backuprestore.plugin.MigrationConstants.ERROR_CODE_UNKNOWN
        }
    }


    override fun onPause(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onPause, ${Thread.currentThread().name}")
    }

    override fun onContinue(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onContinue, ${Thread.currentThread().name}")
        isCancel = false
    }

    override fun onCancel(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onCancel, ${Thread.currentThread().name}")
        isCancel = true
        moveManager?.onCancel()
    }

    override fun onDestroy(bundle: Bundle): Bundle {
        val resultBundle = Bundle()
        if (isCancel) {
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_CANCEL)
        } else if (isSuccess) {
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_SUCCESS)
        } else {
            resultBundle.putInt(com.oplus.migrate.backuprestore.plugin.MigrationConstants.BR_ERROR_CODE, resultCode)
            resultBundle.putString(com.oplus.migrate.backuprestore.plugin.MigrationConstants.BR_ERROR_DESCRIPTION, resultDescription)
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_FAILED)
        }
        ProgressHelper.putCompletedCount(resultBundle, moveManager!!.getCompleteCount())
        ProgressHelper.putMaxCount(resultBundle, moveManager!!.getMoverCount())
        AppLogger.BR.d(TAG, "onDestroy resultBundle = $resultBundle, ${Thread.currentThread().name}")
        isRunning = false
        return resultBundle
    }

    companion object {
        var isRunning = false
        private const val TAG = "NoteRestorePlugin"
        var resultCode: Int = 0
        var isSuccess: Boolean = true
        var resultDescription: String = ""
    }
}