package com.oplus.migrate.backuprestore.plugin

import android.content.Context
import android.os.Bundle
import com.nearme.note.migration.MigrationConstants.NOTE_FOLDER
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.BackupPlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.backuprestore.plugin.mover.MoveManager
import java.io.File


class NoteBackupPlugin : BackupPlugin() {
//    private var completeCount: Int = 0
//    private var maxCount: Int = BACKUP_MAX_COUNT
    private var isCancel: Boolean = false
    private lateinit var backupPath: String
    private var moveManager: MoveManager? = null


    /*  doc: https://hio.oppo.com/app/ozone/team/gotoOzoneCircleKbItemDetail?page=ozone&source=index&enc_kbi_id=157883906_157808373
        备份业务流程需继承并实现BackupPlugin，主要流程：
        数据扫描：onCreate --> onPreview --> onDestroy
        数据备份：onCreate --> onPrepare --> onBackup --> onDestroy
        暂停备份：onPause
        继续备份：onContinue
        停止备份：onCancel
    * */
    override fun onCreate(context: Context, brPluginHandler: BRPluginHandler, config: BREngineConfig) {
        super.onCreate(context, brPluginHandler, config)

        isRunning = true
        AppLogger.BR.d(TAG,"onCreate, ${Thread.currentThread().name}")
        backupPath = config.backupRootPath + File.separator + NOTE_FOLDER
        moveManager = MoveManager(context, backupPath, this)
    }



    override fun onPreview(bundle: Bundle): Bundle {
        val previewBundle = Bundle()
        ProgressHelper.putMaxCount(previewBundle, moveManager!!.getMoverCount()) // 可备份的最大数据条数
        ProgressHelper.putPreviewDataSize(previewBundle, computeBackupSize())  // 预估备份的文件大小,单位是B
        AppLogger.BR.d(TAG,"onPreview:$previewBundle")
        return previewBundle
    }

    override fun onPrepare(bundle: Bundle): Bundle {
        //准备阶段,需要把数据的数据条数最大值放到bundle中返回
        val prepareBundle = Bundle()
        ProgressHelper.putMaxCount(prepareBundle, moveManager!!.getMoverCount())
        AppLogger.BR.d(TAG,"onPrepare:$prepareBundle")
        return prepareBundle
    }

    override fun onBackup(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onBackup. ${Thread.currentThread().name}")
        moveManager?.let {
            it.onBackup()
        } ?: run {
            AppLogger.BR.d(TAG, "moveManager is null")
            isSuccess = false
            resultCode = MigrationConstants.ERROR_CODE_UNKNOWN
        }
    }


    override fun onPause(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onPause. ${Thread.currentThread().name}")
    }

    override fun onContinue(bundle: Bundle) {
        AppLogger.BR.d(TAG, "onContinue. ${Thread.currentThread().name}")
        isCancel = false
    }

    override fun onCancel(bundle: Bundle) {
        AppLogger.BR.d(TAG,"onCancel. ${Thread.currentThread().name}")
        isCancel = true
        moveManager?.onCancel()
    }

    override fun onDestroy(bundle: Bundle): Bundle {
        // 结束的时候，要把备份结果放到bundle中，结果是成功、失败，或是取消
        val resultBundle = Bundle()
        if (isCancel) {
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_CANCEL)
        } else if (isSuccess) {
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_SUCCESS)
        } else {
            resultBundle.putInt(MigrationConstants.BR_ERROR_CODE, resultCode)
            resultBundle.putString(MigrationConstants.BR_ERROR_DESCRIPTION, resultDescription)
            ProgressHelper.putBRResult(resultBundle, ProgressHelper.BR_RESULT_FAILED)
        }
        ProgressHelper.putMaxCount(resultBundle, moveManager!!.getMoverCount())
        ProgressHelper.putCompletedCount(resultBundle, moveManager!!.getCompleteCount())
        AppLogger.BR.d(TAG, "onDestroy resultBundle = $resultBundle. ${Thread.currentThread().name}")
        isRunning = false
        return resultBundle
    }

    private fun computeBackupSize(): Long {
        return kotlin.runCatching {
            AppLogger.BR.d(TAG, "computeBackupSize")
            File(context.applicationInfo.dataDir).walkTopDown().filter { it.isFile }.map { it.length() }.sum()
        }.getOrDefault(0L)
    }

    companion object {
        var isRunning = false
        private const val TAG = "NoteBackupPlugin"
        var resultCode: Int = 0
        var isSuccess: Boolean = true
        var resultDescription = ""
    }
}

