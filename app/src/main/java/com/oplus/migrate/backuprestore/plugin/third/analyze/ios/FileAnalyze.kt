/****************************************************************
 * * Copyright (C), 2019-2027, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: FileAnalyze.kt
 * * Description: OuterHtml
 * * Version: 1.0
 * * Date: 2022/11/17
 * * Author: xushuya
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 ****************************************************************/
package com.oplus.migrate.backuprestore.plugin.third.analyze.ios

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.FileUtils
import androidx.annotation.RequiresApi
import androidx.documentfile.provider.DocumentFile
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.skin.SkinData
import com.nearme.note.util.FileUtil
import com.oplus.cloud.status.Device
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.richnote.RichNoteFactory
import com.oplus.note.R
import com.oplus.note.notebook.internal.NoteBookData
import com.oplus.note.repo.note.NoteRepoFactory
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderFactory
import com.oplus.richtext.core.parser.HtmlFormats
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import java.io.*
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Paths
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

class FileAnalyze {
    companion object {
        private const val TAG = "FileAnalyze"
        private const val SIZE = 1024
    }

    private var mAppPath = ""
    private val repository = RichNoteRepository
    private val mutex = Mutex()
    private var mContext: Context? = null
    private fun init() {
        mAppPath = FileUtil.getNotePath()
    }

    @SuppressLint("NewApi")
    fun loadData(context: Context, uri: Uri?): List<String>? {
        init()
        val zipFile = uri?.let { readUri(context, it) }
        val folderList = zipFile?.let { unZipFile(it, mAppPath) }
        AppLogger.BASIC.d(TAG, "loadData# folderList= ${folderList?.size} ")
        mContext = context
        return folderList
    }

     suspend fun analyzeHtmlAndSave(folder: String) {
        val guid = UUID.randomUUID().toString()
        val htmlAndJsonFilePath = getHtmlAndJsonFilePath(folder, guid)
        htmlAndJsonFilePath?.let {
            val htmlAnalyzeUtil = HtmlAnalyzeUtil()
            val iosFileAttr = htmlAnalyzeUtil.getIOSFileAttr(it.second)
            AppLogger.BASIC.d(TAG, "analyzeHtmlAndSave# ${iosFileAttr.id} ")
            htmlAnalyzeUtil.analyze(it.first, guid)
            //便签不支持空笔记过滤掉text为空且没有附件的笔记
            if (htmlAnalyzeUtil.getText().isNotBlank() || htmlAnalyzeUtil.getAttachmentList().isNotEmpty()) {
                insertData(guid, htmlAnalyzeUtil, iosFileAttr)
            }else {
                AppLogger.BASIC.d(TAG, "analyzeHtmlAndSave# skip empty  ${iosFileAttr.id} ")
            }
        }
    }

    private fun getHtmlAndJsonFilePath(folderPath: String, guid: String): Pair<String, String>? {
        val guidPath = "$mAppPath${File.separator}$guid"
        try {
            Files.move(Paths.get(folderPath), Paths.get(guidPath))
        } catch (e: IOException) {
            AppLogger.BASIC.e(TAG, "Failed to rename $folderPath to $guidPath", e)
            return null
        }
        val guidFile = File(guidPath)
        val htmlFile =
            guidFile.walk().firstOrNull { it.isFile && it.name.endsWith(HtmlFormats.HTML) }
        val jsonFile = guidFile.walk().firstOrNull { it.isFile && it.name.endsWith(".json") }

        val htmlPath = htmlFile?.absolutePath
        val jsonPath = jsonFile?.absolutePath

        if (htmlPath == null) {
            AppLogger.BASIC.d(TAG, "No HTML file found in $guid")
            return null
        }

        if (jsonPath == null) {
            AppLogger.BASIC.d(TAG, "No JSON file found in $guid")
            return null
        }

        return Pair(htmlPath, jsonPath)
    }

    private suspend fun insertData(
        guid: String,
        htmlAnalyzeUtil: HtmlAnalyzeUtil,
        fileAttr: IOSFileAttr
    ) {
        val richNote: RichNote = RichNoteFactory.createRichNote(guid, fileAttr.createdTime)
        val folderGuid = getOrCreateFolderGuidByIosAttrName(fileAttr.folder)
        richNote.rawText = htmlAnalyzeUtil.getRawText().ifEmpty { "<br>" }
        richNote.rawTitle = htmlAnalyzeUtil.getRawTitle()
        richNote.text = htmlAnalyzeUtil.getText()
        richNote.updateTime = fileAttr.modifiedTime
        richNote.state = RichNote.STATE_NEW
        richNote.skinId = SkinData.COLOR_SKIN_WHITE
        richNote.folderGuid = folderGuid
        if (folderGuid == FolderInfo.FOLDER_GUID_RECENT_DELETE) {
            richNote.recycleTime = System.currentTimeMillis()
        }
        val note = RichNoteWithAttachments(richNote, htmlAnalyzeUtil.getAttachmentList())
        richNote.title = if (note.isPicturesNote()) {
            mContext?.getString(R.string.memo_picture)
        } else {
            fileAttr.text
        }
        repository.insert(note)
    }


    private suspend fun getOrCreateFolderGuidByIosAttrName(name: String): String = withContext(
        Dispatchers.IO
    ) {
        //防止重复创建 串行处理
        mutex.withLock {
            when (name) {
                IOSFileAttr.ATTR_ALL -> FolderInfo.FOLDER_GUID_NO_GUID
                IOSFileAttr.ATTR_RECENT_DELETE -> FolderInfo.FOLDER_GUID_RECENT_DELETE
                else -> {
                    val folderRepo = NoteRepoFactory.getFolderRepo()
                    val folders = folderRepo?.findNotDeletedFolderByName(name)
                    if (!folders.isNullOrEmpty()) {
                        return@withContext folders[0].guid
                    } else {
                        val folder =
                            FolderFactory.createEmptyFolder(Device.getDeviceIMEI(MyApplication.appContext))
                        folder.state = Folder.FOLDER_STATE_NEW
                        folder.guid = UUID.randomUUID().toString()
                        folder.name = name
                        folder.extra?.setPureCover(NoteBookData.getDefaultPureCover())
                        folderRepo?.insert(folder)
                        return@withContext folder.guid
                    }
                }
            }
        }
    }

    @Throws(IOException::class)
    private fun unZipFile(zipFile: File, descDir: String): List<String> {
        val nameList = mutableListOf<String>()
        val zip = ZipFile(zipFile, StandardCharsets.UTF_8)
        // 使用 use 语句来自动关闭 ZipFile
        zip.use {
            val entries = it.entries()
            while (entries.hasMoreElements()) {
                val entry = entries.nextElement() as ZipEntry
                val entryName = entry.name
                if (entry.isDirectory) {
                    // 处理目录
                    val dirPath = File(descDir, entryName.trimEnd('/'))
                    if (!dirPath.exists() && !dirPath.mkdirs()) {
                        throw IOException("Failed to create directory: $entryName")
                    }
                } else {
                    handleUnzipFile(descDir, entryName, nameList, entry, zip)
                }
            }
        }

        if (!zipFile.delete()) {
            AppLogger.BASIC.e(TAG, "Failed to delete zip file")
        }

        return nameList
    }

    @Throws(IOException::class)
    private fun handleUnzipFile(
        descDir: String,
        entryName: String,
        nameList: MutableList<String>,
        entry: ZipEntry,
        zip: ZipFile
    ) {
        val outPath = File(descDir, entryName)
        AppLogger.BASIC.d(TAG, "unZipFile# $entryName")
        // 创建父目录
        outPath.parentFile?.let { parentDir ->
            if (!parentDir.exists() && !parentDir.mkdirs()) {
                throw IOException("Failed to create parent directory s")
            }
            if (!nameList.contains(parentDir.absolutePath)) {
                nameList.add(parentDir.absolutePath)
            }
        }
        // 解压文件
        BufferedInputStream(zip.getInputStream(entry)).use { input ->
            FileOutputStream(outPath).use { output ->
                val buffer = ByteArray(SIZE)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } > 0) {
                    output.write(buffer, 0, bytesRead)
                }
            }
        }
    }


    @RequiresApi(Build.VERSION_CODES.Q)
    private fun readUri(context: Context, uri: Uri): File? {

        var inputStream: InputStream? = null
        var file: File? = null
        try {
            inputStream = context.contentResolver.openInputStream(uri)
            if (inputStream != null) {
                val documentFile = DocumentFile.fromSingleUri(context, uri) ?: return null
                val name = documentFile.name
                val path = mAppPath + File.separator + name

                val cache = File(path)
                val fos = FileOutputStream(cache)
                FileUtils.copy(inputStream, fos)
                fos.close()
                file = cache
            }
        } catch (e: IOException) {
            AppLogger.BASIC.e(TAG, "IOException when reading uri $e")
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    AppLogger.BASIC.e(TAG, "IOException when closing stream $e")
                }
            }
        }
        return file
    }
}