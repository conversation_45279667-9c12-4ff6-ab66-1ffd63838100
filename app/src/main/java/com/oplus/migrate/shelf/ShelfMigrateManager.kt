package com.oplus.migrate.shelf

import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import android.text.SpannableStringBuilder
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.parser.HtmlParser.serialize
import java.io.*
import java.util.*

class ShelfMigrateManager private constructor(private val mContext: Context) {
    fun doShelfMigrate() {
        copyRedLauncherDataIfNeeded()
        val database = getLauncherDb(mContext)
        if (database == null) {
            AppLogger.RED_MIGRATION.d(TAG, "doShelfMigrate getLauncherDb is null")
            return
        }
        migrateShelfData(database)
    }

    private fun migrateShelfData(database: SQLiteDatabase) {
        var cursor: Cursor? = null
        val selection = "$SHELF_COL_TYPE =?"
        val args = arrayOf(SHELF_TYPE)
        try {
            cursor = database.query(SHELF_TABLE_NAME, null, selection, args, null, null, null)
            if (cursor != null && cursor.count > 0) {
                for (i in 0 until cursor.count) {
                    cursor.moveToPosition(i)
                    val content = cursor.getString(cursor.getColumnIndexOrThrow(SHELF_COL_CONTENT));
                    if (content.isNullOrBlank()) {
                        continue
                    }
                    val remindTime = cursor.getLong(cursor.getColumnIndexOrThrow(
                        SHELF_COL_REMIND_TIME
                    ))
                    val modifyTime = cursor.getLong(cursor.getColumnIndexOrThrow(
                        SHELF_COL_MODIFY_TIME
                    ))
                    //AppLogger.RED_MIGRATION.d(TAG, "$content, $remindTime, $modifyTime")

                    RichNoteRepository.insert(RichNote().copy(
                        localId = UUID.randomUUID().toString(),
                        text = content,
                        rawText = serialize(SpannableStringBuilder(content)),
                        timestamp = modifyTime,
                        createTime = modifyTime,
                        updateTime = modifyTime,
                        alarmTime = if (remindTime > 0L) remindTime else 0L
                    ))
                }
            }
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "migrateShelfData Exception " + e.message)
        } finally {
            database.close()
            if (cursor != null) {
                cursor.close()
            }
        }
    }

    private fun getLauncherDb(context: Context): SQLiteDatabase? {
        val oldDbPath = context.dataDir.absolutePath + File.separator + RED_LAUNCHER_DB_FILE_PATH
        val oldDbFile = File(oldDbPath)
        return if (!oldDbFile.exists() || !oldDbFile.canRead()) {
            null
        } else {
            SQLiteDatabase.openOrCreateDatabase(oldDbFile, null)
        }
    }

    private fun copyRedLauncherDataIfNeeded(): Boolean {
        if (!shouldGetOldData(mContext)) {
            AppLogger.RED_MIGRATION.d(
                TAG,
                "copyRedLauncherDataIfNeeded should not get Old Data"
            )
            return false
        }
        if (!getOldData(mContext)) {
            AppLogger.RED_MIGRATION.d(TAG, "copyRedLauncherDataIfNeeded get oldData fail")
            return false
        }
        return false
    }

    private fun shouldGetOldData(context: Context): Boolean {
        val cr = context.contentResolver
        try {
            val bundle = cr.call(IS_BACKUPED_CONTENT_URI, IS_BACKUPED, null, null)
            if (bundle != null) {
                return !bundle.getBoolean(IS_BACKUPED, false)
            } else {
                AppLogger.RED_MIGRATION.d(TAG, "back up bundle is null")
            }
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.d(
                TAG,
                "OPLauncher BackupProvider not exit, skip backup msg = " + e.message
            )
        }
        return false
    }

    private fun getOldData(context: Context): Boolean {
        val cr = context.contentResolver
        val bundle = cr.call(BACKUP_CONTENT_URI, BACKUP_ENABLED, null, null)
        if (bundle != null) {
            for (key in bundle.keySet()) {
                val uri = Uri.parse(bundle.getString(key))
                val filePath = context.dataDir.path + File.separator + DATA_FOLDER_NAME + key
                AppLogger.RED_MIGRATION.d(TAG, "key: $key, uri: $uri, filePath: $filePath")
                saveFile(context, uri, filePath)
            }
            return bundle.keySet().size > 0
        } else {
            AppLogger.RED_MIGRATION.d(TAG, "back up bundle is null")
        }
        return false
    }

    private fun saveFile(context: Context, uri: Uri, destinationFilename: String) {
        var bis: InputStream? = null
        var bos: OutputStream? = null
        try {
            val file = File(destinationFilename)
            if (!file.parentFile.parentFile.exists()) {
                file.parentFile.parentFile.mkdir()
            }
            if (!file.parentFile.exists()) {
                file.parentFile.mkdir()
            }
            bis = context.contentResolver.openInputStream(uri)
            if (bis == null) {
                return
            }
            bos = FileOutputStream(destinationFilename, false)
            val buf = ByteArray(1024)
            var read = bis.read(buf)
            while (read != -1) {
                bos.write(buf, 0, read)
                read = bis.read(buf)
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            try {
                bis?.close()
                bos?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    companion object {
        const val TAG = "ShelfOtaManager"
        const val AUTHORITY = "net.oneplus.launcher.backup.BackUpProvider"
        const val BACKUP_ENABLED = "BACKUP_ENABLED"
        const val IS_BACKUPED = "IS_BACKUPED"
        val BACKUP_CONTENT_URI = Uri.parse("content://$AUTHORITY/$BACKUP_ENABLED")
        val IS_BACKUPED_CONTENT_URI = Uri.parse("content://$AUTHORITY/$IS_BACKUPED")
        const val DATA_FOLDER_NAME = "old_red_launcher_data"
        val RED_LAUNCHER_DB_FILE_PATH =
            DATA_FOLDER_NAME + File.separator + "databases" + File.separator + "launcher.db"
        const val SHELF_TABLE_NAME = "quickPage"
        const val SHELF_COL_TYPE = "type"
        const val SHELF_COL_CONTENT = "content"
        const val SHELF_COL_REMIND_TIME = "reminderTime"
        const val SHELF_COL_MODIFY_TIME = "modified"
        const val SHELF_TYPE = "4"

        @Volatile
        private var sInstance: ShelfMigrateManager? = null
        fun getInstance(context: Context): ShelfMigrateManager? {
            if (sInstance == null) {
                synchronized(ShelfMigrateManager::class.java) {
                    if (sInstance == null) {
                        sInstance = ShelfMigrateManager(context.applicationContext)
                    }
                }
            }
            return sInstance
        }
    }
}