object PageUtil {

    fun split(totalSize: Int, pageSize: Int): MutableList<PageInfo> {
        val pageInfos: MutableList<PageInfo> = ArrayList()
        val requestCount = totalSize / pageSize
        for (i in 0..requestCount) {
            val fromIndex = i * pageSize
            val toIndex = totalSize.coerceAtMost((i + 1) * pageSize)
            pageInfos.add(PageInfo(fromIndex, toIndex, toIndex - fromIndex))
            if (toIndex == totalSize) {
                break
            }
        }
        println(pageInfos)
        return pageInfos
    }

    class PageInfo(var start: Int, var end: Int, var size: Int) {
        override fun toString(): String {
            return "PageInfo(start=$start, end=$end, size=$size)"
        }
    }
}