/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - FileUtil
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/23
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/23 1.0 create
 ******************************************************************/
package com.oplus.migrate.utils;

import android.annotation.SuppressLint;

import com.oplus.note.logger.AppLogger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

public class FileUtil {

    public static final String TAG = "FileUtil";

    public static void copyFile(String srcPath, String destPath) {
        InputStream bis = null;
        OutputStream bos = null;
        try {
            bis = new BufferedInputStream(new FileInputStream(srcPath));
            bos = new BufferedOutputStream(new FileOutputStream(destPath));
            final byte[] buff = new byte[1024];
            int len = -1;
            while ((len = bis.read(buff)) != -1) {
                bos.write(buff, 0, len);
            }
            bos.flush();
        } catch (final FileNotFoundException e) {
            AppLogger.RED_MIGRATION.e(TAG, "copyFile e:" + e);
        } catch (final IOException e) {
            AppLogger.RED_MIGRATION.e(TAG, "copyFile e:" + e);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (final IOException e) {
                    AppLogger.RED_MIGRATION.d(TAG, "copyFile e:" + e);
                }
            }
            if (bis != null) {
                try {
                    bis.close();
                } catch (final IOException e) {
                    AppLogger.RED_MIGRATION.e(TAG, "copyFile e:" + e);
                }
            }
        }
    }

    @SuppressLint("NewApi")
    public static void copyFiles(File source, File dest) {
        if ((null == source) || (null == dest)) {
            return;
        }
        if (!source.exists()) {
            return;
        }
        if (source.isFile()) {
            dest = new File(dest, source.getName());
            try {
                Files.copy(source.toPath(), dest.toPath());
            } catch (IOException e) {
                AppLogger.RED_MIGRATION.e(TAG, "copyFiles -> " + e.getMessage());
            }
        } else {
            final File[] files = source.listFiles();
            if ((files == null) || (files.length == 0)) {
                return;
            }
            for (File file : files) {
                if (!file.isFile()) {
                    File destTemp = new File(dest, file.getName());
                    destTemp.mkdirs();
                    copyFiles(file, destTemp);
                } else {
                    copyFiles(file, dest);
                }
            }
        }
    }

    public static String readFromFd(FileDescriptor fd) {
        InputStream is = null;
        try {
            is = new FileInputStream(fd);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            int len = -1;
            byte[] buffer = new byte[1024];
            while ((len = is.read(buffer, 0, 1024)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toString("utf-8");
        } catch (Exception e) {
            AppLogger.RED_MIGRATION.e(TAG, "[FileUtil]getContentFromFile failed." + e.getMessage());
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    AppLogger.RED_MIGRATION.e(TAG, "[FileUtil]getContentFromFile failed." + e.getMessage());
                }
            }
        }
        return null;
    }

    public static void saveToFile(File file, String content) {
        BufferedWriter bw = null;
        try {
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8));
            bw.write(content);
            AppLogger.RED_MIGRATION.d(TAG, "saveToFile done");
        } catch (IOException e) {
            AppLogger.RED_MIGRATION.e(TAG, "saveToFile -> " + e.getMessage());
        } finally {
            if (bw != null) {
                try {
                    bw.close();
                } catch (IOException e) {
                    AppLogger.RED_MIGRATION.e(TAG, "saveToFile bw -> " + e.getMessage());
                }
            }
        }
    }

    public static void saveFile(FileDescriptor srcFd, File destFile) {
        FileInputStream bis = new FileInputStream(srcFd);
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(destFile);
            byte[] temp = new byte[1024];
            int length;
            while ((length = bis.read(temp)) != -1) {
                fos.write(temp, 0, length);
            }

        } catch (IOException e) {
            AppLogger.RED_MIGRATION.e(TAG, "saveFile -> " + e.getMessage());
        } finally {
            try {
                bis.close();
            } catch (IOException e) {
                AppLogger.RED_MIGRATION.e(TAG, "saveFile bis -> " + e.getMessage());
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    AppLogger.RED_MIGRATION.e(TAG, "saveFile fos -> " + e.getMessage());
                }
            }
        }
    }

    public static void createFile(File file) {
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        if (file.exists()) {
            file.delete();
        }
        try {
            file.createNewFile();
        } catch (IOException e) {
            AppLogger.RED_MIGRATION.e(TAG, "createFile -> e " + e.getMessage());
        }
    }

    public static boolean deleteFile(String path) {
        boolean isDeleted = false;
        final File file = new File(path);

        if (file.exists() && file.isFile()) {
            isDeleted = file.delete();
        }

        return isDeleted;
    }
}
