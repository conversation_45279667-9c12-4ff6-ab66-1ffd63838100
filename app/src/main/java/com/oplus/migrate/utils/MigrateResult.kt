/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - Constant
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/22
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/22 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.utils

import com.oplus.note.logger.AppLogger

object MigrateResult {
    const val TAG = "MigrateResult"
    const val MIGRATION_UNDONE = 0
    const val MIGRATION_IN_PROGRESS = 1
    const val MIGRATION_END_OLD_DB_NULL = 2
    const val MIGRATION_END_EXCEPTION = 3
    const val MIGRATION_END_OLD_DB_EMPTY_RECORDS = 4
    const val MIGRATION_END_SUCCESS = 5
    const val MIGRATION_END_ALREADY_DONE = 6

    fun isMigrateEnd(status: Int): Boolean {
        val result = status > MIGRATION_IN_PROGRESS
        AppLogger.RED_MIGRATION.d(TAG, "status = $status, isMigrateEnd = $result ")
        return result
    }


    fun isMigrateEndFail(status: Int): Boolean {
        val result = status == MIGRATION_END_OLD_DB_NULL
        AppLogger.RED_MIGRATION.d(TAG, "status = $status, isMigrateEndFail = $result ")
        return result
    }

    fun isMigrateEndSuccess(status: Int): Boolean {
        return status == MIGRATION_END_OLD_DB_EMPTY_RECORDS || status == MIGRATION_END_SUCCESS || status == MIGRATION_END_ALREADY_DONE
    }
}