/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - SharedPreferenceUtil
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/22
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/22 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.utils

import android.content.Context

object SharedPreferenceUtil {

    const val MIGRATE_PREF_NAME = "migrate_pref"
    const val KEY_IS_MIGRATE_DONE = "is_migrate_done"
    const val KEY_IS_MIGRATE_MARK_INVALID_DATA_DONE = "is_migrate_mark_invalid_data_done"
    const val KEY_IS_RETAIL_MODE_HAS_INSERT = "is_retail_mode_has_insert"
    private const val DEFAULT_VALUE_INTEGER = 0

    fun getInt(context: Context, preferenceName: String?, key: String?): Int {
        return getInt(context, preferenceName, key, DEFAULT_VALUE_INTEGER)
    }

    fun getInt(context: Context, preferenceName: String?, key: String?, defaultValue: Int): Int {
        val sharedPreferences = context.getSharedPreferences(preferenceName, Context.MODE_PRIVATE)
        return sharedPreferences.getInt(key, defaultValue)
    }

    fun getBoolean(context: Context, preferenceName: String?, key: String?): Boolean {
        val sharedPreferences = context.getSharedPreferences(preferenceName, Context.MODE_PRIVATE)
        return sharedPreferences.getBoolean(key, false)
    }

    fun putBoolean(context: Context, preferenceName: String?, key: String?, value: Boolean) {
        val sharedPreferences = context.getSharedPreferences(preferenceName, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putBoolean(key, value)
        editor.commit()
    }

    fun putInt(context: Context, preferenceName: String, key: String, value: Int) {
        val sharedPreferences = context.getSharedPreferences(preferenceName, Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putInt(key, value)
        editor.commit()
    }
}