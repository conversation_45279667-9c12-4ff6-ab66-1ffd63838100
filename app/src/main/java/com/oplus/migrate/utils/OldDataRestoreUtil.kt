/*****************************************************************

 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd

 * * VENDOR_EDIT

 * * File: - OldDataRestoreUtil

 * * Description:

 * * Version: 1.0

 * * Date : 2021/6/23

 * * Author: XXX

 * *

 * * ---------------------- Revision History:----------------------

 * * <author> <date> <version> <desc>

 * * XXX 2021/6/23 1.0 create

 ******************************************************************/
package com.oplus.migrate.utils

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.model.*
import com.nearme.note.model.RichNoteRepository.insert
import com.nearme.note.skin.SkinData
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.MigrateManager.Companion.THUMB
import com.oplus.migrate.MigrateStatusCallback
import com.oplus.migrate.backuprestore.BackupRestoreConstant
import com.oplus.migrate.backuprestore.NoteXmlTagConstant
import com.oplus.migrate.old.OldNoteAttachmentInfo
import com.oplus.migrate.old.OldNoteContract
import com.oplus.migrate.old.OldNoteInfo
import com.oplus.migrate.old.OldNoteXMLParser.parseOldNoteInfo
import com.oplus.migrate.old.Transformer
import com.oplus.migrate.old.Transformer.convertRichNoteContent
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.richtext.editor.utils.RichEditorUtil
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserException
import org.xmlpull.v1.XmlPullParserFactory
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.util.ArrayList
import java.util.regex.Matcher
import java.util.regex.Pattern
import java.util.UUID

object OldDataRestoreUtil {

    const val TAG = "OldDataRestoreUtil"
    private const val MAX_SPLIT = 3     //最大拆分次数
    private const val Attachments_Str_Length = 36       //图片附件字符串长度

    fun isOldBackupData(backupFilePath: String): Boolean {
        var isOldData = false
        try {
            // old backup data has no noteAppInfo node
            isOldData = !checkIfNoteAppVersionNodeExists(backupFilePath)
            AppLogger.RED_MIGRATION.d(TAG, "isOldBackupData = $isOldData")
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "isOldBackupData -> ${e.message}")
        }
        return isOldData
    }

    @JvmStatic
    fun getAttachmentsFromRichContent(richContent: String): MutableList<String> {
        val attachments: MutableList<String> = ArrayList()
        val uuidPattern: Pattern = Pattern.compile("[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}")
        val matcher = uuidPattern.matcher(richContent)
        while (matcher.find()) {
            attachments.add(matcher.group())
        }
        AppLogger.BASIC.d(TAG, "attachments:$attachments")
        return attachments
    }

    @JvmStatic
    fun convertOldNoteToRichNote(info: OldNoteInfo, isOta: Boolean): MutableList<RichNoteWithAttachments>? {
        if (info.richContent.isNullOrBlank() && info.title.isNullOrBlank()) {
            return null
        }

        val richNoteWithAttachmentsList: MutableList<RichNoteWithAttachments> = mutableListOf()
        if (info.richContent.isNullOrBlank() && !info.title.isNullOrBlank()) { //仅有标题，无内容
            AppLogger.BASIC.d(TAG, "convertOldNoteToRichNote just has title")
            val richNote = convertOldNoteData(info)
            richNote?.let {
                richNoteWithAttachmentsList.add(RichNoteWithAttachments(it, null))
            }

            return richNoteWithAttachmentsList
        }

        val richContent: String = info.richContent!!
        val attachments: MutableList<String> = getAttachmentsFromRichContent(richContent)
        val indexes = arrayOfNulls<Int>(attachments.size)

        for (index in 0 until attachments.size) {
            indexes[index] = richContent.indexOf(attachments[index])
        }

        AppLogger.BASIC.d(TAG, "indexes.size:${indexes.size}")
        var lastIndex = 0   //上次被拆分的位置
        var maxCount = MAX_SPLIT    //最大拆分次数
        val richContextTempList: ArrayList<String> = arrayListOf()  //被拆分后的内容列表
        for (index in richContent.indices) {
            if (index > 0 && (index % (RichEditorUtil.CONTENT_TEXT_LENGTH_MAX - 1) == 0) && maxCount > 0) {
                var isInPicture = false   //标记分割处是否在图片文件字符串区域
                for (temp in indexes) {
                    //判断分割笔记处是否在图片文件字符串区域
                    if (index > temp!! && (index - temp) < Attachments_Str_Length) {
                        val strValue = richContent.substring(lastIndex, temp)
                        richContextTempList.add(strValue)
                        lastIndex = temp
                        isInPicture = true
                        break
                    }
                }
                AppLogger.BASIC.d(TAG, "isPicture:$isInPicture")
                if (!isInPicture) {
                    val strValue = richContent.substring(lastIndex, index + 1)
                    richContextTempList.add(strValue)
                    lastIndex = index + 1
                }
                maxCount--
            }
        }
        if (richContent.length > lastIndex) {
            richContextTempList.add(richContent.substring(lastIndex))
        }
        if (richContextTempList.size != 0) {
            for (index in 0 until richContextTempList.size) {
                val titleTemp = info.title
                val itemId = if (index != 0 ) UUID.randomUUID().toString() else info.itemId

                if (index != 0) {
                    info.title = titleTemp + index
                }

                info.richContent = richContextTempList[index]
                val attachmentsList: MutableList<Attachment> = ArrayList()
                convertOldNoteAttachment(info, attachmentsList, isOta, itemId)
                val richNote = convertOldNoteData(info, itemId)
                val richNoteWithAttachments: RichNoteWithAttachments? =
                    richNote?.let { RichNoteWithAttachments(it, attachmentsList) }
                richNoteWithAttachmentsList.add(richNoteWithAttachments!!)
                info.title = titleTemp
            }
        } else {
            val attachmentList: MutableList<Attachment> = ArrayList()
            val richNote = convertOldNoteData(info)
            convertOldNoteAttachment(info, attachmentList, isOta)
            richNote?.let {
                richNoteWithAttachmentsList.add(RichNoteWithAttachments(it, attachmentList))
            }
        }
        return richNoteWithAttachmentsList
    }

    private fun convertOldNoteData(info: OldNoteInfo, newNoteId: String? = null): RichNote? {
        val isSplitNote = newNoteId != null && !TextUtils.equals(newNoteId, info.itemId)
        checkIfOldNoteAttachmentFileExist(info, isSplitNote)
        if (info.richContent.isNullOrBlank() && info.title.isNullOrBlank()) {
            return null
        }
        val localId = newNoteId ?: info.itemId!!
        val globalId = info.globalId
        val text = richNoteTextConvert(info)
        val rawText = convertRichNoteContent((if (info.richContent != null) info.richContent!! else ""))
        val folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
        val timestamp = info.modified
        val createTime = info.created
        val updateTime = info.modified
        val topTime = if (info.top == 1) info.setTopTime else 0L
        val recycleTime = 0L
        val alarmTime = info.remindTime
        val state = statusConvert(info.status)
        val deleted = info.status == OldNoteContract.Notes.NOTE_STATUS_DELETE
        val skinId = SkinData.COLOR_SKIN_WHITE
        val title = if (info.hasEditTitle == 1 && info.title != null) info.title!! else ""
        val rawTitle = if (info.hasEditTitle == 1 && info.title != null) Transformer.DIV_TAG_START + info.title!! + Transformer.DIV_TAG_END else ""
        val recycleTimePre = 0L
        val alarmTimePre = 0L
        val skinIdPre = null
        val extra = null
        val version = 0
        return RichNote(localId, globalId, text, rawText,"", folderGuid, timestamp, createTime, updateTime, topTime, recycleTime, alarmTime, state, deleted, skinId, title, rawTitle, recycleTimePre, alarmTimePre, skinIdPre, extra, version)
    }

    private fun checkIfOldNoteAttachmentFileExist(info: OldNoteInfo, isSplitNote: Boolean = false) {
        var oldRichContent = info.richContent
        if (oldRichContent.isNullOrBlank()) {
            return
        }
        val uuidPattern: Pattern = Pattern.compile("[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}")
        val matcher = uuidPattern.matcher(oldRichContent)
        if (matcher.find()) {
            for (i in 0 until matcher.groupCount()) {
                val uuid = matcher.group(i)
                if (uuid.isNullOrBlank()) {
                    continue
                }
                val isHasFile = checkIfHasAttachmentFile(uuid)
                if (isHasFile) {
                    if (oldRichContent?.contains('\u0004' + uuid + '\u0005') == true) {
                        // do nothing
                    } else {
                        oldRichContent = oldRichContent?.replace(uuid, '\u0004' + uuid + '\u0005')
                    }
                    addToAttachmentListIfNeed(info, uuid, isSplitNote)
                } else {
                    oldRichContent = oldRichContent?.replace(uuid, "")
                }
            }
        }
        info.richContent = oldRichContent
    }

    @VisibleForTesting
    fun checkIfHasAttachmentFile(uuid: String?): Boolean {
        val file = File(MyApplication.appContext.filesDir.absolutePath + File.separator + uuid)
        val result = file.exists() && file.length() > 0
        AppLogger.RED_MIGRATION.i(TAG, "checkIfHasAttachmentFile " + file.absolutePath + ", result = $result")
        return result
    }

    @VisibleForTesting
    fun addToAttachmentListIfNeed(info: OldNoteInfo, uuid: String, isSplitNote: Boolean = false) {
        var isInAttachmentList = false
        for (i in info.noteAttachments.indices) {
            val oldNoteAttachmentInfo = info.noteAttachments[i]
            if (oldNoteAttachmentInfo != null) {
                val oldAttach = oldNoteAttachmentInfo.itemId
                if (uuid == oldAttach) {
                    // already in attachment list
                    isInAttachmentList = true
                }
            }
        }

        if (!isInAttachmentList) {
            val oldNoteAttachmentInfo = OldNoteAttachmentInfo()
            oldNoteAttachmentInfo.itemId = uuid
            oldNoteAttachmentInfo.attachment_name = uuid
            val newArray = info.noteAttachments.copyOf(info.noteAttachments.size + 1)
            newArray[info.noteAttachments.size] = oldNoteAttachmentInfo
            info.noteAttachments = newArray
            //copyFile(info.itemId, uuid)
        }

        if (!isSplitNote) {
            copyFile(info.itemId, uuid)
        }
    }

    private fun copyFile(itemId: String?, uuid: String) {
        val noteAttachmentDir = File(MyApplication.appContext.filesDir.toString() + File.separator + itemId)
        if (!noteAttachmentDir.exists()) {
            noteAttachmentDir.mkdir()
        }
        val dataFilesPath = MyApplication.appContext.filesDir.absolutePath
        val fileSrcPath = dataFilesPath + File.separator + uuid
        val fileDestPath = dataFilesPath + File.separator + itemId + File.separator + uuid + THUMB
        FileUtil.copyFile(fileSrcPath, fileDestPath)
    }

    private fun convertOldNoteAttachment(info: OldNoteInfo, attachments: MutableList<Attachment>, isOta: Boolean, newNoteId: String? = null) {
        val oldNoteAttachmentInfo = info.noteAttachments
        if (oldNoteAttachmentInfo.isNotEmpty()) {
            for (oldAttachment in oldNoteAttachmentInfo) {
                AppLogger.RED_MIGRATION.i(TAG, "convertOldNoteAttachment " + oldAttachment.toString())
                if (isOta && !isAttachmentFileExist(info, oldAttachment)) {
                    continue
                }
                //新增判断拆分出的新笔记是否包含该图片资源，新拆分的笔记根据内容来关联内容包含的图片附件
                if (oldAttachment?.itemId?.let { info.richContent?.contains(it) } == true) {
                    oldAttachment?.let {
                        val attachmentId = oldAttachment.itemId!!
                        val richNoteId = newNoteId ?: info.itemId!!
                        val type = Attachment.TYPE_PICTURE
                        val state = Attachment.STATE_NEW
                        val md5 = null
                        val url = if (oldAttachment.file_id != null) {
                            oldAttachment.file_id!!.trim()
                        } else {
                            ""
                        }
                        val picture = Picture(100, 100)
                        val attachment = Attachment(attachmentId, richNoteId, type, state, md5, url, picture)
                        attachments.add(attachment)

                        //如果是拆分后的笔记，需要拷贝附件到新笔记的路径下
                        val isSplitNote = newNoteId != null && !TextUtils.equals(newNoteId, info.itemId)
                        if (isOta && isSplitNote) {
                            moveAttachmentByNoteId(info.itemId, newNoteId, oldAttachment.attachment_name)
                        }
                        AppLogger.RED_MIGRATION.i(TAG, "convertOldNoteAttachment add ${attachment.attachmentId},isSplitNote=$isSplitNote")
                    }
                }
            }
        }
    }

    private fun isAttachmentFileExist(oldNote: OldNoteInfo, oldAttachment: OldNoteAttachmentInfo?): Boolean {
        if (oldAttachment == null) {
            return false
        }
        val path = MyApplication.appContext.filesDir.absolutePath + File.separator + oldNote.itemId + File.separator + oldAttachment.attachment_name + THUMB
        val result = File(path).exists()
        AppLogger.RED_MIGRATION.d(TAG, "isAttachmentFileExist PATH = $path, isExist = $result")
        return result
    }

    fun richNoteTextConvert(info: OldNoteInfo): String {
        var richText = info.richContent
        if (richText.isNullOrBlank()) {
            return ""
        }
        var matcher: Matcher = Transformer.ITEM_SYMBOL_STRING_PATTERN.matcher(richText)
        if (matcher.find()) {
            richText = matcher.replaceAll("")
        }

        if (richText.isNullOrBlank()) {
            return ""
        }
        matcher = Transformer.TO_DO_UNCHECK_STRING_PATTERN.matcher(richText)
        if (matcher.find()) {
            richText = matcher.replaceAll("")
        }

        if (richText.isNullOrBlank()) {
            return ""
        }
        matcher = Transformer.TO_DO_CHECKED_STRING_PATTERN.matcher(richText)
        if (matcher.find()) {
            richText = matcher.replaceAll("")
        }

        if (richText.isNullOrBlank()) {
            return ""
        }
        matcher = Transformer.PHOTO_PATTERN.matcher(richText)
        if (matcher.find()) {
            richText = matcher.replaceAll("")
        }

        return if (richText.isNullOrBlank()) {
            ""
        } else {
            richText.trim()
        }
    }

    fun statusConvert(oldStatus: Int): Int {
        return when (oldStatus) {
            OldNoteContract.Notes.NOTE_STATUS_NOMODIFY -> RichNote.STATE_UNCHANGE
            OldNoteContract.Notes.NOTE_STATUS_NEW -> RichNote.STATE_NEW
            OldNoteContract.Notes.NOTE_STATUS_MODIFY, OldNoteContract.Notes.NOTE_STATUS_DELETE -> RichNote.STATE_MODIFIED
            else -> throw IllegalStateException("Unexpected value: $oldStatus")
        }
    }

    fun migrateAttachments(context: Context, oldNoteInfoList: List<OldNoteInfo>) {
        for (i in oldNoteInfoList.indices) {
            val oldNoteInfo = oldNoteInfoList[i]
            val itemId = oldNoteInfo.itemId
            val attachmentInfos = oldNoteInfo.noteAttachments
            if (attachmentInfos.isNotEmpty()) {
                for (oldNoteAttachmentInfo in attachmentInfos) {
                    copyAttachment(context, oldNoteAttachmentInfo, itemId)
                }
            }
        }
    }

    fun copyAttachment(context: Context, oldNoteAttachmentInfo: OldNoteAttachmentInfo?, itemId: String?) {
        mkdirForNoteItemIdIfNeed(context, itemId)
        val dataFilesPath = context.filesDir.absolutePath
        val attachmentName = oldNoteAttachmentInfo!!.attachment_name
        val fileSrcPath = dataFilesPath + File.separator + attachmentName
        val fileDestPath = dataFilesPath + File.separator + itemId + File.separator + attachmentName + THUMB
        FileUtil.copyFile(fileSrcPath, fileDestPath)
    }

    fun mkdirForNoteItemIdIfNeed(context: Context, itemId: String?) {
        val noteAttachmentDir = File(context.filesDir.absolutePath + File.separator + itemId)
        if (!noteAttachmentDir.exists()) {
            noteAttachmentDir.mkdir()
        }
    }

    fun migrateDatas(context: Context, oldNoteInfoList: List<OldNoteInfo>, callback: MigrateStatusCallback?) {
        val addRichNoteWithAttachmentsList: MutableList<RichNoteWithAttachments> = ArrayList()
        val updateRichNoteWithAttachmentsList: MutableList<RichNoteWithAttachments> = ArrayList()
        for (i in oldNoteInfoList.indices) {
            val oldNoteInfo = oldNoteInfoList[i]
            var tempList: MutableList<RichNoteWithAttachments>? = null

            try {
                tempList = convertOldNoteToRichNote(oldNoteInfo, true)
            } catch (e: java.lang.Exception) {
                AppLogger.BASIC.e(TAG, "convertOldNoteToRichNote error.", e)
            }

            tempList?.forEach { richNoteWithAttachments ->
                adjustAttachmentSizeProperties(context, richNoteWithAttachments)
                val sameLocalIdData: RichNoteWithAttachments? = addRichNoteWithAttachmentsList.find {
                    it.richNote.localId == richNoteWithAttachments.richNote.localId
                }
                val localData = if (sameLocalIdData == null) {
                    RichNoteRepository.getRichNoteWithAttachments(richNoteWithAttachments.richNote.localId)
                } else {
                    null
                }

                if (sameLocalIdData != null) {
                    addRichNoteWithAttachmentsList.add(RichNoteRepository.reNewRichNote(richNoteWithAttachments, false))
                } else if (localData != null) {
                    val sameRawTitle =
                        (richNoteWithAttachments.richNote.rawTitle.hashCode() == localData.richNote.rawTitle.hashCode())
                                && TextUtils.equals(richNoteWithAttachments.richNote.rawTitle, localData.richNote.rawTitle)
                    val sameRawContent =
                        (richNoteWithAttachments.richNote.rawText.hashCode() == localData.richNote.rawText.hashCode())
                            && TextUtils.equals(richNoteWithAttachments.richNote.rawText, localData.richNote.rawText)
                    val sameReminder =
                        (richNoteWithAttachments.richNote.alarmTime <= 0 && localData.richNote.alarmTime <= 0)
                            || (richNoteWithAttachments.richNote.alarmTime == localData.richNote.alarmTime)
                    val sameFolder = (richNoteWithAttachments.richNote.folderGuid == localData.richNote.folderGuid)

                    if (sameRawTitle && sameRawContent && sameReminder && sameFolder) {
                        // 数据相同，不做处理
                    } else if (sameRawTitle && sameReminder && sameFolder) { // rawText不同
                        when {
                            richNoteWithAttachments.richNote.rawText.contains(localData.richNote.rawText.removeSuffix("</div>")) -> {
                                // 包含关系，当前插入的数据包含数据库已存在的数据
                                localData.richNote.rawText = richNoteWithAttachments.richNote.rawText
                                updateRichNoteWithAttachmentsList.add(localData)
                            }
                            localData.richNote.rawText.contains(richNoteWithAttachments.richNote.rawText.removeSuffix("</div>")) -> {
                                // 数据库已经存在的数据包含当前数据,不做处理
                            }
                            else -> {
                                addRichNoteWithAttachmentsList.add(RichNoteRepository.reNewRichNote(richNoteWithAttachments, false))
                            }
                        }
                    } else {
                        addRichNoteWithAttachmentsList.add(RichNoteRepository.reNewRichNote(richNoteWithAttachments, false))
                    }
                } else {
                    addRichNoteWithAttachmentsList.add(richNoteWithAttachments)
                }
            }
            callback?.publishProgress(i + 1, oldNoteInfoList.size)
        }

        if (updateRichNoteWithAttachmentsList.isNotEmpty()) {
            RichNoteRepository.updateList(updateRichNoteWithAttachmentsList)
        }

        if (addRichNoteWithAttachmentsList.isNotEmpty()) {
            RichNoteRepository.insertList(addRichNoteWithAttachmentsList)
        }
    }

    fun adjustAttachmentSizeProperties(context: Context, richNoteWithAttachments: RichNoteWithAttachments) {
        val attachments = richNoteWithAttachments.attachments
        if (attachments != null && attachments.isNotEmpty()) {
            for (i in attachments.indices) {
                val attachment = attachments[i]
                val noteId = richNoteWithAttachments.richNote.localId
                val attachmentId = attachment.attachmentId
                val attachmentPath = context.filesDir.absolutePath + File.separator + noteId + File.separator + attachmentId + THUMB
                val bitmap = BitmapFactory.decodeFile(attachmentPath)
                if (bitmap != null) {
                    attachment.picture = Picture(bitmap.width, bitmap.height)
                }
            }
        }
    }

    private fun checkIfNoteAppVersionNodeExists(fileName: String): Boolean {
        var matches = false
        var `is`: FileInputStream? = null
        try {
            `is` = FileInputStream(fileName)
            val parser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(`is`, null)
            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                if (eventType == XmlPullParser.START_TAG) {
                    val tag = parser.name
                    if (NoteXmlTagConstant.NOTE_APP_VERSION_TAG == tag) {
                        matches = true
                        break
                    }
                }
                eventType = parser.next()
            }
        } catch (e: XmlPullParserException) {
            AppLogger.RED_MIGRATION.e(TAG, "checkIfNoteAppVersionNodeExists -> ${e.message}")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "checkIfNoteAppVersionNodeExists -> ${e.message}")
        } finally {
            if (`is` != null) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    AppLogger.RED_MIGRATION.e(TAG, "checkIfNoteAppVersionNodeExists -> ${e.message}")
                }
            }
        }
        return matches
    }

    fun restore(backupFilePath: String?): Bundle {
        val infoList = parseOldNoteInfo(backupFilePath)
        val richNoteWithAttachmentsList: MutableList<RichNoteWithAttachments> = ArrayList()
        //restore data
        for (i in infoList.indices) {
            val info = infoList[i]
            //LogUtil.d(TAG, info.toString());
            val tempList = convertOldNoteToRichNote(info, false)
            if (tempList != null && tempList.size != 0) {
                tempList.forEach { richNoteWithAttachments ->
                    if (RichNoteRepository.findSameContentRichNote(richNoteWithAttachments.richNote)
                            .isEmpty()
                    ) {
                        insert(richNoteWithAttachments)
                        richNoteWithAttachmentsList.add(richNoteWithAttachments)
                    }
                }
            }
        }
        //insertList(richNoteWithAttachmentsList)
        //copy attachment files
        val bundle = Bundle()
        val attachs = ArrayList<String>()
        for (i in richNoteWithAttachmentsList.indices) {
            val (_, attachments) = richNoteWithAttachmentsList[i]
            if (attachments != null) {
                for ((attachmentId, richNoteId) in attachments) {
                    attachs.add(richNoteId + BackupRestoreConstant.NOTE_ATTACHMENTS_SPLIT + attachmentId)
                }
            }
        }
        bundle.putBoolean(BackupRestoreConstant.IS_OLD_NOTE_BACKUP_DATA, true)
        bundle.putStringArrayList(BackupRestoreConstant.NOTE_ATTACHMENTS, attachs)
        return bundle
    }

    private fun moveAttachmentByNoteId(oldNoteId: String?, newNoteId: String?, attachmentName: String?) {
        if (oldNoteId == null || newNoteId == null || attachmentName == null) {
            return
        }

        mkdirForNoteItemIdIfNeed(MyApplication.appContext, newNoteId)
        val dataFilesPath = MyApplication.appContext.filesDir.absolutePath
        val oldFilePath = dataFilesPath + File.separator + oldNoteId + File.separator + attachmentName + THUMB
        val newFilePath = dataFilesPath + File.separator + newNoteId + File.separator + attachmentName + THUMB
        FileUtil.copyFile(oldFilePath, newFilePath)
        FileUtil.deleteFile(oldFilePath)
    }
}