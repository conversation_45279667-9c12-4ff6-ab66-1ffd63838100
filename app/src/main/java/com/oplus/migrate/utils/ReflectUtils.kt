package com.oplus.migrate.utils

import com.oplus.note.logger.AppLogger
import java.lang.reflect.InvocationTargetException


object ReflectUtils {
    private const val TAG = "ReflectUtils"

    @JvmStatic
    fun invoke(obj: Any?, objClass: Class<*>, methodName: String, agrs: Array<Class<*>?>,
               agrsObj: Array<Any?>): Any? {
        var result: Any? = null
        try {
            val method = objClass.getMethod(methodName, *agrs)
            result = method.invoke(obj, *agrsObj)
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            //AppLogger.RED_MIGRATION.w(TAG, "invoke InvocationTargetException:${targetException?.message}, objClass: $objClass, method: $methodName, ${AppLogger.RED_MIGRATION.getCallerTrace(TAG, targetException?.stackTrace)}")
            AppLogger.RED_MIGRATION.w(TAG, "invoke InvocationTargetException:${targetException?.message}, objClass: $objClass, method: $methodName")
        } catch (t: Throwable) {
            //AppLogger.RED_MIGRATION.w(TAG, "invoke exception: $t, objClass:$objClass, method: $methodName, ${AppLogger.RED_MIGRATION.getCallerTrace(TAG, t.stackTrace)}")
            AppLogger.RED_MIGRATION.w(TAG, "invoke exception: $t, objClass:$objClass, method: $methodName")
        }
        return result
    }

    @JvmStatic
    fun <T> readField(obj: Any, name: String, tClass: Class<T>?): T? {
        try {
            val field = obj.javaClass.getDeclaredField(name)
            field.isAccessible = true
            return field[obj] as T
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "readField InvocationTargetException:${targetException?.message}, objClass: $obj")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "readField Exception:$t, object:$obj, name:$name")
        }
        return null
    }

    @JvmStatic
    fun invokeMethodWithParams(obj: Any?, classFullName: String?, methodName: String?, paramsClass: Array<Class<*>?>,
                               params: Array<Any?>): Any? {
        var result: Any? = null
        try {
            val destClass = Class.forName(classFullName)
            val method = destClass.getMethod(methodName, *paramsClass)
            method.isAccessible = true
            result = method.invoke(obj, *params)
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "invokeMethodWithParams InvocationTargetException.  ${classFullName}, $methodName, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "invokeMethodWithParams exception. ${classFullName}, $methodName, $t")
        }
        return result
    }

    @JvmStatic
    fun invokeMethodWithNoParams(obj: Any?, classFullName: String?, methodName: String?): Any? {
        var result: Any? = null
        try {
            val destClass = Class.forName(classFullName)
            val method = destClass.getMethod(methodName)
            method.isAccessible = true
            result = method.invoke(obj)
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "invokeMethodWithNoParams InvocationTargetException.  ${classFullName}, $methodName, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "invokeMethodWithNoParams exception. ${classFullName}, $methodName, $t")
        }
        return result
    }

    @JvmStatic
    fun invokeConstructorWithParams(classFullName: String?, paramsClass: Array<Class<*>?>, params: Array<Any?>): Any? {
        var result: Any? = null
        try {
            val destClass = Class.forName(classFullName)
            val constructor = destClass.getConstructor(*paramsClass)
            constructor.isAccessible = true
            result = constructor.newInstance(*params)
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "invokeConstructorWithParams InvocationTargetException.  ${classFullName}, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "invokeConstructorWithParams exception. ${classFullName}, $t")
        }
        return result
    }

    @JvmStatic
    fun invokeConstructorWithNoParams(classFullName: String?): Any? {
        var result: Any? = null
        try {
            val destClass = Class.forName(classFullName)
            result = destClass.newInstance()
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "invokeConstructorWithNoParams InvocationTargetException.  ${classFullName}, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "invokeConstructorWithNoParams exception. ${classFullName}, $t")
        }
        return result
    }

    @JvmStatic
    fun getField(obj: Any?, classFullName: String?, fieldName: String?): Any? {
        try {
            val destClass = Class.forName(classFullName)
            val field = destClass.getDeclaredField(fieldName)
            field.isAccessible = true
            return field[obj]
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "getField InvocationTargetException.  ${classFullName}, $fieldName, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "getField exception. $classFullName, $fieldName, $t")
        }
        return null
    }

    @JvmStatic
    fun setField(obj: Any?, classFullName: String?, fieldName: String?, value: Any?) {
        try {
            val destClass = Class.forName(classFullName)
            val field = destClass!!.getDeclaredField(fieldName)
            field.isAccessible = true
            field[obj] = value
        } catch (e: InvocationTargetException) {
            val targetException = e.targetException
            AppLogger.RED_MIGRATION.w(TAG, "setField InvocationTargetException.  ${classFullName}, $fieldName, ${targetException?.message}")
        } catch (t: Throwable) {
            AppLogger.RED_MIGRATION.w(TAG, "setField exception. $classFullName, $fieldName, $t")
        }
    }
}
