/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - DbUtil
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/22
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/22 1.0 create
</desc></version></date></author> */
package com.oplus.migrate.utils

import android.annotation.SuppressLint
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import androidx.annotation.VisibleForTesting
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.old.OldNoteAttachmentContract
import com.oplus.migrate.old.OldNoteAttachmentInfo
import com.oplus.migrate.old.OldNoteContract
import com.oplus.migrate.old.OldNoteInfo
import java.io.File
import java.util.ArrayList

object DbUtil {
    private const val TAG = "DbUtil"
    private const val DB_NAME = "note.db"
    private const val NOTES_TABLE_NAME = "notes"
    private const val ATTACHMENT_TABLE_NAME = "note_attachment"
    private const val COL_BACKUP_STATUS = "backup_status"
    private const val COL_STATUS = "status"
    private const val DELETE_STATUS = "3"
    private const val INVALID_DATA_SYMBOL = "-1"


    fun getOldDatabase(context: Context): SQLiteDatabase? {
        val oldDbPath = context.dataDir.absolutePath + File.separator + "databases" + File.separator + DB_NAME
        val oldDbFile = File(oldDbPath)
        return if (!oldDbFile.exists() || !oldDbFile.canRead()) {
            null
        } else {
            SQLiteDatabase.openOrCreateDatabase(oldDbFile, null)
        }
    }

    fun markInvalidData(db: SQLiteDatabase): Boolean {
        try {
            db.execSQL("update notes set backup_status = -1 where (title, rich_content) in (select title,rich_content from notes group by title,rich_content having count(*) > 1)  and _id not in (select min(_id) from notes group by title,rich_content having count(*)>1)")
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "markInvalidData Exception " + e.message)
            return false
        }
        return true
    }

    fun getOldNoteInfoFromOldDb(database: SQLiteDatabase, pageInfo: PageUtil.PageInfo): List<OldNoteInfo>? {
        var oldNoteInfoList: MutableList<OldNoteInfo>? = null
        var cursor: Cursor? = null
        try {
            val selection = "$COL_BACKUP_STATUS !=? and $COL_STATUS !=?";
            val args = arrayOf(INVALID_DATA_SYMBOL, DELETE_STATUS)
            //val limit = "limit ${pageInfo.size} offset ${pageInfo.start}"
            val limit = "${pageInfo.start}, ${pageInfo.size}"
            AppLogger.RED_MIGRATION.e(TAG, "getOldNoteInfoFromOldDb limit -> $limit")
            //cursor = database.query(NOTES_TABLE_NAME, null, selection, args, null, null, null)
            cursor = database.query(NOTES_TABLE_NAME, null, selection, args, null, null, null, limit)
            if (cursor != null && cursor.count > 0) {
                oldNoteInfoList = ArrayList()
                for (i in 0 until cursor.count) {
                    cursor.moveToPosition(i)
                    val oldNoteInfo = getOldNoteInfoFromCursor(cursor)
                    val attachments = getAttachments(oldNoteInfo.id, database)
                    AppLogger.RED_MIGRATION.w(TAG, "getOldNoteInfoFromOldDb id -> ${oldNoteInfo.id}")
                    if (attachments != null) {
                        oldNoteInfo.noteAttachments = attachments
                    }
                    oldNoteInfoList.add(oldNoteInfo)
                }
            }
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "getOldNoteInfoFromOldDb -> ${e.message}")
        } finally {
            closeCursor(cursor)
        }
        return oldNoteInfoList
    }

    fun getOldNoteCount(database: SQLiteDatabase): Int {
        var count = 0
        var cursor: Cursor? = null
        try {
            val selection = "$COL_BACKUP_STATUS !=? and $COL_STATUS !=?";
            val args = arrayOf(INVALID_DATA_SYMBOL, DELETE_STATUS)
            cursor = database.query(NOTES_TABLE_NAME, arrayOf("_id"), selection, args, null, null, null)
            if (cursor != null) {
                count = cursor.count
            }
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "getOldNoteCount -> ${e.message}")
        } finally {
            closeCursor(cursor)
        }
        AppLogger.RED_MIGRATION.d(TAG, "getOldNoteCount -> $count")
        return count
    }

    fun closeDb(database: SQLiteDatabase?) {
        database?.close()
    }

    private fun closeCursor(cursor: Cursor?) {
        cursor?.close()
    }

    @JvmStatic
    @SuppressLint("Range")
    @VisibleForTesting
    fun getOldNoteInfoFromCursor(cursor: Cursor): OldNoteInfo {
        val oldNoteInfo = OldNoteInfo()
        oldNoteInfo.id = cursor.getInt(cursor.getColumnIndex("_id"))
        oldNoteInfo.title = cursor.getString(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_TITLE))
        oldNoteInfo.summary = cursor.getString(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_SUMMARY))
        oldNoteInfo.richContent = cursor.getString(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_RICH_CONTENT))
        oldNoteInfo.content = cursor.getString(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_CONTENT))
        oldNoteInfo.type = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_TYPE))
        oldNoteInfo.top = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_TOP))
        oldNoteInfo.setTopTime = cursor.getLong(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_SET_TOP_TIME))
        oldNoteInfo.hasPhoto = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_HAS_PHOTO))
        oldNoteInfo.hasItem = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_HAS_ITEM))
        oldNoteInfo.hasTodo = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_HAS_TODO))
        oldNoteInfo.created = cursor.getLong(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_CREATE_DATE))
        oldNoteInfo.modified = cursor.getLong(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_MODIFICATION_DATE))
        oldNoteInfo.hasEditTitle = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_HAS_EDIT_TITLE))
        oldNoteInfo.remindTime = cursor.getLong(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_REMIND_TIME))
        oldNoteInfo.itemId = cursor.getString(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_ITEM_ID))
        oldNoteInfo.status = cursor.getInt(cursor.getColumnIndex(OldNoteContract.Notes.COLUMN_NAME_STATUS))
        return oldNoteInfo
    }

    @JvmStatic
    @VisibleForTesting
    fun getAttachments(id: Int, database: SQLiteDatabase): Array<OldNoteAttachmentInfo?>? {
        var oldNoteAttachmentInfoList: Array<OldNoteAttachmentInfo?>? = null
        var cursor: Cursor? = null
        try {
            val where = OldNoteAttachmentContract.COLUMN_NAME_NOTE_ID + "=?"
            val whereArgs = arrayOf(id.toString())
            cursor = database.query(ATTACHMENT_TABLE_NAME, null, where, whereArgs, null, null, null)
            if (cursor != null && cursor.count > 0) {
                oldNoteAttachmentInfoList = arrayOfNulls(cursor.count)
                for (i in 0 until cursor.count) {
                    cursor.moveToPosition(i)
                    val attachmentInfo = getOldNoteAttachmentInfoFromCursor(cursor)
                    oldNoteAttachmentInfoList[i] = attachmentInfo
                }
            }
        } catch (e: Exception) {
            AppLogger.RED_MIGRATION.e(TAG, "getAttachments -> ${e.message}")
        } finally {
            closeCursor(cursor)
        }
        return oldNoteAttachmentInfoList
    }

    @JvmStatic
    @SuppressLint("Range")
    @VisibleForTesting
    fun getOldNoteAttachmentInfoFromCursor(cursor: Cursor): OldNoteAttachmentInfo {
        val oldNoteAttachmentInfo = OldNoteAttachmentInfo()
        oldNoteAttachmentInfo.itemId = cursor.getString(cursor.getColumnIndex(OldNoteAttachmentContract.COLUMN_NAME_ITEM_ID))
        oldNoteAttachmentInfo.attachment_name = cursor.getString(cursor.getColumnIndex(OldNoteAttachmentContract.COLUMN_NAME_ATTACHMENT_NAME))
        oldNoteAttachmentInfo.file_id = cursor.getString(cursor.getColumnIndex(OldNoteAttachmentContract.COLUMN_NAME_GLOBAL_ID))
        return oldNoteAttachmentInfo
    }
}