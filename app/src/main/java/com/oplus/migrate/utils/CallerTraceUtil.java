package com.oplus.migrate.utils;

public class CallerTraceUtil {
    private static final int NUM_5 = 5;
    public static String getCallerTrace(String tag, StackTraceElement[] ste) {
        return getCaller(tag, ste, NUM_5);
    }

    private static String getCaller(String tag, StackTraceElement[] ste, int depth) {
        if (ste == null) {
            return "null";
        }
        StringBuilder builder = new StringBuilder(tag);
        builder.append("\n");
        try {
            int count = Math.min(ste.length, depth);
            for (int i = 0; i < count; i++) {
                builder.append(ste[i]).append("\n");
            }
        } catch (Exception e) {
        }
        builder.append("\n");
        return builder.toString();
    }
}
