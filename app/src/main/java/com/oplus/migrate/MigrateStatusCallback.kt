/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - MigrateListener
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/22
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/22 1.0 create
</desc></version></date></author> */
package com.oplus.migrate

interface MigrateStatusCallback {
    fun start()
    fun publishProgress(complete: Int, total: Int)
    fun end(status: Int)
}