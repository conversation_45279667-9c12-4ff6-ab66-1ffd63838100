package com.oplus.migrate.retailmode

import android.content.Context
import android.graphics.BitmapFactory
import android.os.Bundle
import android.text.SpannableStringBuilder
import com.nearme.note.model.*
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.utils.SharedPreferenceUtil
import com.oplus.migrate.utils.SharedPreferenceUtil.KEY_IS_RETAIL_MODE_HAS_INSERT
import com.oplus.migrate.utils.SharedPreferenceUtil.MIGRATE_PREF_NAME
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.richtext.core.parser.HtmlParser
import java.io.*
import java.util.*

object RetailMode {

    const val TAG = "RetailMode"

    private const val RETAIL_MODE_NOTE_COUNT = 9
    private val RETAIL_MODE_NOTE_1_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_2_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_3_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_4_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_5_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_6_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_7_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_8_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_9_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_7_ATTACHMENT_1_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_1_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_2_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_3_GUID = UUID.randomUUID().toString()
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_4_GUID = UUID.randomUUID().toString()
    private const val RETAIL_MODE_ATTACHMENT_FOLDER = "retail_mode_files"
    private val RETAIL_MODE_NOTE_7_ATTACHMENT_1_ASSERT_NAME =
        RETAIL_MODE_ATTACHMENT_FOLDER + File.separator + "note_7_attachment_1.png"
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_1_ASSERT_NAME =
        RETAIL_MODE_ATTACHMENT_FOLDER + File.separator + "note_8_attachment_1.png"
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_2_ASSERT_NAME =
        RETAIL_MODE_ATTACHMENT_FOLDER + File.separator + "note_8_attachment_2.png"
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_3_ASSERT_NAME =
        RETAIL_MODE_ATTACHMENT_FOLDER + File.separator + "note_8_attachment_3.png"
    private val RETAIL_MODE_NOTE_8_ATTACHMENT_4_ASSERT_NAME =
        RETAIL_MODE_ATTACHMENT_FOLDER + File.separator + "note_8_attachment_4.png"


    fun retailModeInsert(context: Context?): Bundle? {
        if (context == null) {
            AppLogger.MIGRATION.d(TAG, "retailModeInsert context is null, so return")
            return null
        }

        if (SharedPreferenceUtil.getBoolean(
                context,
                MIGRATE_PREF_NAME,
                KEY_IS_RETAIL_MODE_HAS_INSERT
            )
        ) {
            AppLogger.MIGRATION.d(TAG, "retailModeInsert has already inserted, so return")
            return null
        }

        val runnable = Runnable {
            AppLogger.MIGRATION.d(TAG, "retailModeInsert begin")
            val presetNotes: MutableList<RichNoteWithAttachments> =
                ArrayList(RETAIL_MODE_NOTE_COUNT)
            val presetNote9 = buildPreset(context, RETAIL_MODE_NOTE_9_GUID)
            val presetNote8 = buildPreset(context, RETAIL_MODE_NOTE_8_GUID)
            val presetNote7 = buildPreset(context, RETAIL_MODE_NOTE_7_GUID)
            val presetNote6 = buildPreset(context, RETAIL_MODE_NOTE_6_GUID)
            val presetNote5 = buildPreset(context, RETAIL_MODE_NOTE_5_GUID)
            val presetNote4 = buildPreset(context, RETAIL_MODE_NOTE_4_GUID)
            val presetNote3 = buildPreset(context, RETAIL_MODE_NOTE_3_GUID)
            val presetNote2 = buildPreset(context, RETAIL_MODE_NOTE_2_GUID)
            val presetNote1 = buildPreset(context, RETAIL_MODE_NOTE_1_GUID)
            presetNotes.add(presetNote9)
            presetNotes.add(presetNote8)
            presetNotes.add(presetNote7)
            presetNotes.add(presetNote6)
            presetNotes.add(presetNote5)
            presetNotes.add(presetNote4)
            presetNotes.add(presetNote3)
            presetNotes.add(presetNote2)
            presetNotes.add(presetNote1)
            for (data in presetNotes) {
                AppLogger.MIGRATION.d(TAG, "retailModeInsert " + data.richNote.title)
            }
            RichNoteRepository.insertList(presetNotes)
            SharedPreferenceUtil.putBoolean(
                context,
                MIGRATE_PREF_NAME,
                KEY_IS_RETAIL_MODE_HAS_INSERT,
                true
            )
            AppLogger.MIGRATION.d(TAG, "retailModeInsert end")
        }
        Thread(runnable).start()
        return Bundle()
    }

    private fun buildPreset(context: Context, noteGuid: String): RichNoteWithAttachments {
        val textArray = genPresetNoteText(noteGuid)
        val text = if (textArray[0] == null) "" else textArray[0]
        val rawText = if (textArray[1] == null) "" else textArray[1]
        val t = System.currentTimeMillis()
        val richNote = RichNote().copy(
            localId = noteGuid,
            text = text!!,
            rawText = rawText!!,
            timestamp = t,
            createTime = t,
            updateTime = t,
            title = textArray[2],
            rawTitle = textArray[3],
        )

        val attachments: MutableList<Attachment> = ArrayList()
        buildAttachments(context, noteGuid, attachments)
        Thread.sleep(5)
        return RichNoteWithAttachments(richNote, attachments)
    }

    private fun genPresetNoteText(noteGuid: String): Array<String?> {
        //text-rawText-title-rawTitle
        val result = arrayOfNulls<String>(4)
        when {
            RETAIL_MODE_NOTE_1_GUID == noteGuid -> {
                result[0] = "\u200DBuy a Christmas tree, about 1.2-1.5 meters\n" +
                        "\u200D2.Colored balls-5 of big, 5 of middle, 4 of small\n" +
                        "\u200D10 of gift boxes, 2 pieces 12cmX12cm, 3 pieces 10cmX10cm, 5 pieces 5cmX5cm\n" +
                        "\u200D4.Christmas wrapping paper x10\n" +
                        "\u200D5.Ribbon x2\n" +
                        "\u200DLantern, warm yellow x1, multicolor x1"
                result[1] = "<div><ul><li>Buy a Christmas tree, about 1.2-1.5 meters<br></li><li>2.Colored balls-5 of big, 5 of middle, 4 of small<br></li><li>10 of gift boxes, 2 pieces <span class=\"text-decoration-underline\">12cmX12cm, 3 pieces 10cmX10cm, 5 pieces 5cmX5cm</span><br></li><li>4.Christmas wrapping paper x10<br></li><li>5.Ribbon x2<br></li><li>Lantern, warm yellow x1, multicolor x1</li></ul></div>"
                result[2] = "Before Christmas"
            }
            RETAIL_MODE_NOTE_2_GUID == noteGuid -> {
                result[0] = "\u200DPassport (preferably have a copy)\n" +
                        "\u200DBank card (preferably a multi-currency card, such as a VISA card)\n" +
                        "\u200DPhone + charger\n" +
                        "\u200DCash (RMB, tourist destination cash)\n" +
                        "\u200DActivate a roaming SIM card (in case of emergency you need to be contacted)\n" +
                        "\u200DContact information for important, emergency contacts"
                result[1] = "<div><ul><li><span class=\"text-weight-bold\">Passport (preferably have a copy)</span><br></li><li>Bank card (preferably a multi-currency card, such as a VISA card)<br></li><li>Phone + charger<br></li><li>Cash (RMB, tourist destination cash)<br></li><li>Activate a roaming SIM card (in case of emergency you need to be contacted)<br></li><li>Contact information for important, emergency contacts</li></ul></div>"
                result[2] = "Travel must carry classes"
            }
            RETAIL_MODE_NOTE_3_GUID == noteGuid -> {
                result[0] = "\u200DAttend the OnePlus new product Conference\n" +
                        "\u200DPurchase the latest bend studio games\n" +
                        "\u200DTravel to Japan\n" +
                        "\u200DWrite tourism strategy of Japan"
                result[1] = "<div><ul><li>Attend the OnePlus new product Conference<br></li><li>Purchase the latest bend studio games<br></li><li>Travel to Japan<br></li><li>Write tourism strategy of Japan</li></ul></div>"
                result[2] = "Need to complete this month"
            }
            RETAIL_MODE_NOTE_4_GUID == noteGuid -> {
                result[0] = "\u200DBuy a Switch game console\n" +
                        "\u200DWatch a Jay Chou concert\n" +
                        "\u200DWant to own James' signature jersey and sneakers\n" +
                        "\u200DLearn Mandarin Chinese"
                result[1] = "<div><ul><li>Buy a Switch game console<br></li><li>Watch a Jay Chou concert<br></li><li>Want to own James' signature jersey and sneakers<br></li><li>Learn Mandarin Chinese</li></ul></div>"
                result[2] = "Plans for the second half of the year"
            }
            RETAIL_MODE_NOTE_5_GUID == noteGuid -> {
                result[0] = "Chest: Dumbbell bench press 10x3, Smith bench press 10x3, Butterfly clip chest 10x3\n" +
                            "Legs:  dumbbell squat 15x3, single dumbbell squat 15x3, Smith barbell squat 15x3\n" +
                            "Stretch for 10 minutes"
                result[1] = "<div>Chest: Dumbbell bench press 10x3, Smith bench press 10x3, Butterfly clip chest 10x3<br>Legs:  dumbbell squat 15x3, single dumbbell squat 15x3, Smith barbell squat 15x3<br>Stretch for 10 minutes</div>"
                result[2] = "Fitness exercises & quantity"
            }
            RETAIL_MODE_NOTE_6_GUID == noteGuid -> {
                result[0] = "Terracotta Warriors Tips: Be sure to understand the history, be sure to ask your tour guide to \n" +
                            "explain, otherwise you will only see pits and pottery figurines. The parking lot costs 20rmb, but the road to the parking lot during the holidays is very congested and can be parked at remote points. Buy your tickets online in advance: Qin Shihuang Mausoleum Museum or official website. Enter the park directly with your ID card, but you can't exchange paper tickets for online tickets during holidays. Unless you buy tickets on the spot, you can’t bring a lighter, and there will be two security checks. After the security check, you can take a sightseeing car to watch or walk, about 15 minutes or so. After the ticket is checked, it is the first pit. It is recommended to visit the first and third museums first and finally the second museum. You can learn about the information on the official website in advance. Pit No. 1 has the largest terracotta warriors and horses; Pit No. 2 has very few terracotta warriors and horses, mainly showing unexcavated states, mostly soil slopes, and some display windows, which display several types of typical terracotta warriors, such as senior military officers, kneeling shooting maids, and juniors. Officers and servants allow visitors to watch from a close distance; Pit No. 3 is the smallest, and there are higher-ranking officers and servants, as well as some war horses and weapons. There are several large exhibition areas in the museum, which display the pottery figurines from different periods of the Qin and Han Dynasties. It is worth a look."
                result[1] = "<div>Terracotta Warriors Tips: Be sure to understand the history, be sure to ask your tour guide to explain, otherwise you will only see pits and pottery figurines. <span class=\"text-highlight-active\">The parking lot costs 20rmb,</span> but the road to the parking lot during the holidays is very congested and can be parked at remote points. Buy your tickets online in advance: Qin Shihuang Mausoleum Museum or official website. Enter the park directly with your ID card, but you can't exchange paper tickets for online tickets during holidays. Unless you buy tickets on the spot, <span class=\"text-highlight-active\">you can’t bring a lighter, and there will be two security checks.</span> After the security check, you can take a sightseeing car to watch or walk, about 15 minutes or so. After the ticket is checked, it is the first pit. <span class=\"text-highlight-active\">It is recommended to visit the first and third museums first and finally the second museum.</span> You can learn about the information on the official website in advance. <span class=\"text-highlight-active\">Pit No. 1 has the largest terracotta warriors and horses;</span> Pit No. 2 has very few terracotta warriors and horses, mainly showing unexcavated states, mostly soil slopes, and some display windows, which display several types of typical terracotta warriors, such as senior military officers, kneeling shooting maids, and juniors. Officers and servants allow visitors to watch from a close distance; Pit No. 3 is the smallest, and there are higher-ranking officers and servants, as well as some war horses and weapons. There are several large exhibition areas in the museum, which display the pottery figurines from different periods of the Qin and Han Dynasties. It is worth a look.</div>"
                result[2] = "Xi An Terracotta army"
            }
            RETAIL_MODE_NOTE_7_GUID == noteGuid -> {
                result[0] = "First Stop: Kimono Shop-1h\n" +
                        "At the shop near Kiyomizu-dera, the overall dress is more than two hundred RMB, which is very cost-effective! It feels like a good orange torii to be photographed in a while.\n" +
                        "Second stop: Kiyomizu-dera Temple-1h\n" +
                        "People from the kimono shop arrive at Kiyomizu-dera Temple in 15 minutes. Wash your hands and rinse your mouth in the clean pool in front of the temple to show your respect for the gods. Buying by yourself or for family and friends is a good gift.\n" +
                        "Third stop: two years board. Three years board-0.5h\n" +
                        "Ninenzaka outside Kiyomizu Temple. Sannenzaka is the most unique slope in Kyoto. These two slopes are listed in Japan's \"protection area of important traditional buildings\". Look for Japanese classic postcards near Ninenzaka Angle, in fact, there is no standard shooting point here. The main point is that the photo should include the distant temple of Difa Temple. People are on the left side of the photo, and wait until the surrounding tourists take pictures.\n" +
                        "Transportation: Kiyomizu Temple is about three kilometers from here, there are many transportation options to choose from, and buses and trams can be reachedTickets: Fushimi Inari Shrine does not require tickets and is open 24 hours. Although the lights are dark at night, it is really different"
                result[1] = "<div><span class=\"text-highlight-active\">First Stop: Kimono Shop-1h</span><br>At the shop near Kiyomizu-dera, the overall dress is more than two hundred RMB, which is very cost-effective! It feels like a good orange torii to be photographed in a while.<br><br><span class=\"text-highlight-active\">Second stop: Kiyomizu-dera Temple-1h</span><br>People from the kimono shop arrive at Kiyomizu-dera Temple in 15 minutes. Wash your hands and rinse your mouth in the clean pool in front of the temple to show your respect for the gods. Buying by yourself or for family and friends is a good gift.<br><br><span class=\"text-highlight-active\">Third stop: two years board. Three years board-0.5h</span><br>Ninenzaka outside Kiyomizu Temple. Sannenzaka is the most unique slope in Kyoto. These two slopes are listed in Japan's \"protection area of important traditional buildings\". Look for Japanese classic postcards near Ninenzaka Angle, in fact, there is no standard shooting point here. The main point is that the photo should include the distant temple of Difa Temple. People are on the left side of the photo, and wait until the surrounding tourists take pictures.<br><br>Transportation: Kiyomizu Temple is about three kilometers from here, there are many transportation options to choose from, and buses and trams can be reached</div><img src=\"$RETAIL_MODE_NOTE_7_ATTACHMENT_1_GUID\"><div>Tickets: Fushimi Inari Shrine does not require tickets and is open 24 hours. Although the lights are dark at night, it is really different</div>"
                result[2] = "One day tour to Kyoto, Japan"
            }
            RETAIL_MODE_NOTE_8_GUID == noteGuid -> {
                result[0] = "Travel preparations: Umbrella: sun and rain\n" +
                        "Medicine: Island medicine is not particularly convenient.\n" +
                        "The key point here is that seafood is a cold thing, and seafood is not often eaten. A few people will have diarrhea. In addition, some foods are taboo when eating seafood. Therefore, everyone must not eat casually. For example, if you eat seafood and crab, most people may not care, but some people will have diarrhea, so be careful. Amatory drugs and cold medicine are necessary to go out;Clothing: Needless to say, swimwear goes to the island, it must be brought. As for clothing, it is recommended to bring a long sleeve after the beginning of autumn, the temperature will be slightly cooler in the morning and evening.Buying tickets guide\n" +
                        "From May to July 10, there are many flight schedules, with about four flights a day, and ferry tickets are relatively easy to buy.\n" +
                        "From July 10th to July 25th, the voyage will be increased to 10 voyages per day. The early departure ticket is not very good, so it is recommended to book in advance to be better.\n" +
                        "From July 25th to August 25th, you must book your tickets in advance, otherwise it will be particularly inconvenient and you will often fail to buy tickets\n" +
                        "After August 25th, it returned to the usual time, and the ferry tickets were not so nervous"
                result[1] = "<div>Travel preparations: Umbrella: sun and rain <br>Medicine: Island medicine is not particularly convenient. <br>The key point here is that seafood is a cold thing, and seafood is not often eaten. A few people will have diarrhea. In addition, some foods are taboo when eating seafood. Therefore, everyone must not eat casually. For example, if you eat seafood and crab, most people may not care, but some people will have diarrhea, so be careful. Amatory drugs and cold medicine are necessary to go out;<br></div><img src=\"$RETAIL_MODE_NOTE_8_ATTACHMENT_1_GUID\"><div></div><img src=\"$RETAIL_MODE_NOTE_8_ATTACHMENT_2_GUID\"><div></div><img src=\"$RETAIL_MODE_NOTE_8_ATTACHMENT_3_GUID\"><div>Clothing: Needless to say, swimwear goes to the island, it must be brought. As for clothing, it is recommended to bring a long sleeve after the beginning of autumn, the temperature will be slightly cooler in the morning and evening.<br></div><img src=\"$RETAIL_MODE_NOTE_8_ATTACHMENT_4_GUID\"><div><br>Buying tickets guide <br>From May to July 10, there are many flight schedules, with about four flights a day, and ferry tickets are relatively easy to buy. <br>From July 10th to July 25th, <span class=\"text-highlight-active\">the voyage will be increased to 10 voyages per day.</span> The early departure ticket is not very good, so it is recommended to book in advance to be better. <br>From July 25th to August 25th, you must book your tickets in advance, otherwise it will be particularly inconvenient and you will often fail to buy tickets <br><span class=\"text-highlight-active\">After August 25th, it returned to the usual time, and the ferry tickets were not so nervous</span></div>"
                result[2] = "Haxian Island Travel Preparation Guide"
            }
            RETAIL_MODE_NOTE_9_GUID == noteGuid -> {
                result[0] = "Itinerary: Jiefangbei Pedestrian Street → Chaotianmen → Yangtze River Cableway → Three Gorges Museum → Hongya Cave → Qianyemen Bridge → Sun Moonlight Night Market\n" +
                            "\n" +
                            "First stop：Jiefangbei Walking Street-3-4h\n" +
                            "Play Guide: Experience the interlaced scene of the old town style and modernism. There are many hidden attractions in Jiefangbei, enough for visitors to spend a full morning, and lunch can be at Bayi Snack Street.\n" +
                            "\n" +
                            "Second stop：Chaotianmen-2h\n" +
                            "Play Guide: In the afternoon, you can come to Chaotianmen Square and Chaotianmen Wharf to enjoy the pier culture of Chongqing, or take a group of punk style photos at Raffles City.\n" +
                            "\n" +
                            "Third Stop：Yangtze River Cableway-0.5h\n" +
                            "Play Guide: Located in Shangqing Temple, adjacent to the People's Auditorium, you can visit them together\n" +
                            "\n" +
                            "Fourth stop : Three Gorges Museum-1-2h\n" +
                            "Play Guide: Located in Shangqing Temple, adjacent to the Great Hall of the People, you can visit together\n" +
                            "\n" +
                            "Fifth Stop : Hongya Cave-2h\n" +
                            "Play Guide: Hongya Cave is best known for its night view, but the commercial street inside is also very unique. You can go inside to visit various theme stores before sunset. There are a total of 11 floors here, and each floor has a large road. You can also stand on the square at the top of Hongya Cave to see the different scenery.\n" +
                            "\n" +
                            "The sixth stop : Qianqianmen Bridge-1h\n" +
                            "Play Guide: A great place to watch the night view! Not only can you see Hongya Cave, you can also enjoy the gorgeous river view on the bridge\n" +
                            "\n" +
                            "The Seventh Stop： Sun Moonlight Night Market\n" +
                            "Play Guide: Friends who like to go to the night market or have a supper can go to Sun Moonlight Plaza to experience the night life of Chongqing"
                result[1] = "<div>Itinerary: Jiefangbei Pedestrian Street → Chaotianmen → Yangtze River Cableway → Three Gorges Museum → Hongya Cave → Qianyemen Bridge → Sun Moonlight Night Market<br><br>First stop：Jiefangbei Walking Street-3-4h<br>Play Guide: Experience the interlaced scene of the old town style and modernism. There are many hidden attractions in Jiefangbei, enough for visitors to spend a full morning, and lunch can be at Bayi Snack Street.<br><br>Second stop：Chaotianmen-2h<br>Play Guide: In the afternoon, you can come to Chaotianmen Square and Chaotianmen Wharf to enjoy the pier culture of Chongqing, or take a group of punk style photos at Raffles City.<br><br>Third Stop：Yangtze River Cableway-0.5h<br>Play Guide: Located in Shangqing Temple, adjacent to the People's Auditorium, you can visit them together<br><br>Fourth stop : Three Gorges Museum-1-2h<br>Play Guide: Located in Shangqing Temple, adjacent to the Great Hall of the People, you can visit together<br><br>Fifth Stop : Hongya Cave-2h<br>Play Guide: Hongya Cave is best known for its night view, but the commercial street inside is also very unique. You can go inside to visit various theme stores before sunset. There are a total of 11 floors here, and each floor has a large road. You can also stand on the square at the top of Hongya Cave to see the different scenery.<br><br>The sixth stop : Qianqianmen Bridge-1h<br>Play Guide: A great place to watch the night view! Not only can you see Hongya Cave, you can also enjoy the gorgeous river view on the bridge<br><br>The Seventh Stop： Sun Moonlight Night Market<br>Play Guide: Friends who like to go to the night market or have a supper can go to Sun Moonlight Plaza to experience the night life of Chongqing</div>"
                result[2] = "Chongqing Travel Guide"
            }
        }
        result[3] = HtmlParser.serialize(SpannableStringBuilder(result[2]))
        return result
    }

    private fun buildAttachments(
        context: Context,
        localId: String,
        attachments: MutableList<Attachment>
    ) {
        when {
            RETAIL_MODE_NOTE_7_GUID == localId -> {
                val attachmentId = RETAIL_MODE_NOTE_7_ATTACHMENT_1_GUID
                val whs = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId,
                    RETAIL_MODE_NOTE_7_ATTACHMENT_1_ASSERT_NAME
                )
                val attachment1 = Attachment(
                    attachmentId, localId, 0, 1, null, null, Picture(
                        whs[0], whs[1]
                    ), null
                )
                attachments.add(attachment1)
            }
            RETAIL_MODE_NOTE_8_GUID == localId -> {
                val attachmentId1 = RETAIL_MODE_NOTE_8_ATTACHMENT_1_GUID
                val attachmentId2 = RETAIL_MODE_NOTE_8_ATTACHMENT_2_GUID
                val attachmentId3 = RETAIL_MODE_NOTE_8_ATTACHMENT_3_GUID
                val attachmentId4 = RETAIL_MODE_NOTE_8_ATTACHMENT_4_GUID
                val whs1 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId1,
                    RETAIL_MODE_NOTE_8_ATTACHMENT_1_ASSERT_NAME
                )
                val whs2 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId2,
                    RETAIL_MODE_NOTE_8_ATTACHMENT_2_ASSERT_NAME
                )
                val whs3 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId3,
                    RETAIL_MODE_NOTE_8_ATTACHMENT_3_ASSERT_NAME
                )
                val whs4 = copyAttachmentsAndReturnWHs(
                    context,
                    localId,
                    attachmentId4,
                    RETAIL_MODE_NOTE_8_ATTACHMENT_4_ASSERT_NAME
                )
                val attachment1 = Attachment(
                    attachmentId1, localId, 0, 1, null, null, Picture(
                        whs1[0], whs1[1]
                    ), null
                )
                val attachment2 = Attachment(
                    attachmentId2, localId, 0, 1, null, null, Picture(
                        whs2[0], whs2[1]
                    ), null
                )
                val attachment3 = Attachment(
                    attachmentId3, localId, 0, 1, null, null, Picture(
                        whs3[0], whs3[1]
                    ), null
                )
                val attachment4 = Attachment(
                    attachmentId4, localId, 0, 1, null, null, Picture(
                        whs4[0], whs4[1]
                    ), null
                )
                attachments.add(attachment1)
                attachments.add(attachment2)
                attachments.add(attachment3)
                attachments.add(attachment4)
            }
        }
    }

    private fun copyAttachmentsAndReturnWHs(
        context: Context,
        noteGuid: String,
        attachmentName: String,
        assertName: String
    ): IntArray {
        val whs = IntArray(2)
        val destPath = createAttachmentFiles(context, noteGuid, attachmentName)
        copyFromAssert(context, destPath, assertName)
        val bitmap = BitmapFactory.decodeFile(destPath)
        if (bitmap != null) {
            whs[0] = bitmap.width
            whs[1] = bitmap.height
            bitmap.recycle()
        }
        return whs
    }

    private fun createAttachmentFiles(
        context: Context,
        noteGuid: String,
        attachmentName: String
    ): String {
        val path = context.filesDir.absolutePath + File.separator + noteGuid
        val folder = File(path)
        if (!folder.exists()) {
            folder.mkdir()
        }
        val destPath = path + File.separator + attachmentName + "_thumb.png"
        val destFile = File(destPath)
        if (!destFile.exists()) {
            try {
                destFile.createNewFile()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return destPath
    }

    private fun copyFromAssert(context: Context, destPath: String, assertName: String) {
        var `in`: InputStream? = null
        var out: OutputStream? = null
        val assetManager = context.assets
        try {
            `in` = assetManager.open(assertName)
            File(destPath).createNewFile()
            out = FileOutputStream(destPath)
            copyFile(`in`, out)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            try {
                `in`?.close()
                if (out != null) {
                    out.flush()
                    out.close()
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    @Throws(IOException::class)
    private fun copyFile(`in`: InputStream, out: OutputStream) {
        val buffer = ByteArray(1024)
        var read: Int
        while (`in`.read(buffer).also { read = it } != -1) {
            out.write(buffer, 0, read)
        }
    }
}