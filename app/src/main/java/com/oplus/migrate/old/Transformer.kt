package com.oplus.migrate.old

import com.oplus.note.logger.AppLogger
import java.util.*
import java.util.regex.Pattern

import kotlin.math.max
import kotlin.math.min

/*****************************************************************

 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd

 * * VENDOR_EDIT

 * * File: - Transformer

 * * Description:

 * * Version: 1.0

 * * Date : 2021/5/8

 * * Author: XXX

 * *

 * * ---------------------- Revision History:----------------------

 * * <author> <date> <version> <desc>

 * * XXX 2021/5/8 1.0 create

 ******************************************************************/
object Transformer {

    private const val TAG = "Transformer"

    private const val ITEM_SYMBOL_STRING = "&#1;"
    private const val TO_DO_UNCHECK_STRING = "&#2;"
    private const val TO_DO_CHECKED_STRING = "&#3;"
    private const val PHOTO_START_STRING = "&#4;"
    private const val PHOTO_END_STRING = "&#5;"
    const val DIV_TAG_START = "<div>"
    const val DIV_TAG_END = "</div>"

    val ITEM_SYMBOL_STRING_PATTERN: Pattern = Pattern.compile(ITEM_SYMBOL_STRING, Pattern.DOTALL)
    val TO_DO_UNCHECK_STRING_PATTERN: Pattern = Pattern.compile(TO_DO_UNCHECK_STRING, Pattern.DOTALL)
    val TO_DO_CHECKED_STRING_PATTERN: Pattern = Pattern.compile(TO_DO_CHECKED_STRING, Pattern.DOTALL)
    //val PHOTO_PATTERN: Pattern = Pattern.compile("$PHOTO_START_STRING[\\S]+$PHOTO_END_STRING", Pattern.DOTALL)
    val PHOTO_PATTERN: Pattern = Pattern.compile("\u0004([^\u0005]*)\u0005", Pattern.DOTALL)
    val NEW_LINE_PATTERN: Pattern = Pattern.compile("\n", Pattern.DOTALL)
    val BLANK_SPACE_PATTERN: Pattern = Pattern.compile(" ", Pattern.DOTALL) //空格匹配


    fun convertRichNoteContent(text: String): String {
        AppLogger.RED_MIGRATION.d(TAG, "convertRichNoteContent RichContent =$text")
        val outRawText = StringBuilder()
        //1.特殊字符转义
        //TODO   换行字符是否需求处理 (\r)
        text.forEach {
            when (it) {
                '\n' -> outRawText.append(it)
                in Char.MIN_VALUE..0x1F.toChar() -> outRawText.append("&#").append(it.toInt()).append(";")
                '<' -> outRawText.append("&lt;")
                '>' -> outRawText.append("&gt;")
                '&' -> outRawText.append("&amp;")
                0x7F.toChar() -> outRawText.append("&#").append(it.toInt()).append(";")
                '\u200B', '\u200C' -> {
                    /**去掉此字符*/
                }
                else -> outRawText.append(it)
            }
        }
        val outText = StringBuilder(outRawText)
        // 2.图片添加标签
        val attachment = parseAttachmentFromRichContent(text)
        attachment.forEach {
            val imageString = PHOTO_START_STRING + it + PHOTO_END_STRING
            val outIndex = outText.indexOf(imageString)
            if (outIndex != -1) {
                outText.delete(outIndex, outIndex + imageString.length)
            }
            val start = outRawText.indexOf(imageString)
            if (start != -1) {
                //image标签
                outRawText.replace(start, start + imageString.length, createImageSource(it))
            }
        }
        removeCheckBoxChar(outText)

        //3.计算图片、待办、纯文本区间
        //3.1. 计算图片标签区间
        val imageRangeList = calculationImageRange(outRawText)
        //3.2 添加div标签
        addDivTag(outRawText, imageRangeList)

        val checkRangeList = mutableListOf<CheckRange>()
        //3.3.1. 计算未完成待办 区间
        calculationCheckRange(outRawText, false, checkRangeList)
        //3.3.2. 计算已完成待办 区间
        calculationCheckRange(outRawText, true, checkRangeList)
        //3.3.3. 完成待办替换
        if (checkRangeList.size > 0) {
            checkRangeList.sortByDescending {
                it.start
            }
            calculationAndReplaceCheckRange(outRawText, checkRangeList)
        }


        val itemRangeList = mutableListOf<ItemRange>()
        //3.3.1. 计算未完成待办 区间
        calculationItemRange(outRawText, itemRangeList)
        //3.3.3. 完成待办替换
        if (itemRangeList.size > 0) {
            itemRangeList.sortByDescending {
                it.start
            }
            calculationAndReplaceItemRange(outRawText, itemRangeList)
        }

        var result = outRawText.toString().replace("\n", "<br>")
        result = result.replace("<br></li></ul></div>", "</li></ul></div>")// add for fix 1518891
        result = result.replace("<br></div>", "</div>")
        AppLogger.RED_MIGRATION.d(TAG, "convertRichNoteContent result =$result")
        return result
    }

    private fun calculationAndReplaceItemRange(source: StringBuilder, itemRangeList: MutableList<ItemRange>) {
        val tempList = mutableListOf<ItemRange>()
        itemRangeList.forEach {
            if (tempList.size > 0 && tempList.last().start != it.end) {
                replaceItemRangeList(source, tempList)
            }
            tempList.add(it)
        }
        if (tempList.size > 0) {
            replaceItemRangeList(source, tempList)
        }
    }

    private fun calculationItemRange(sourceText: StringBuilder, itemRangeList: MutableList<ItemRange>) {
        val checkChar = ITEM_SYMBOL_STRING
        var start = sourceText.indexOf(checkChar)
        while (start != -1) {
            val charCR = sourceText.indexOf('\n', start)
            val charLT = sourceText.indexOf('<', start)
            val end = if ((charCR == -1) || (charLT == -1)) {
                max(charCR, charLT)
            } else {
                min(charCR, charLT)
            }
            val char = sourceText[end]
            start = if (char == '\n') {
                val text = sourceText.substring(start + ITEM_SYMBOL_STRING.length, end + 1).replace("\n", "<br>")
                itemRangeList.add(ItemRange("<li>${text}</li>", start, end + 1))
                sourceText.indexOf(checkChar, end + 1)
            } else {
                val text = sourceText.substring(start + ITEM_SYMBOL_STRING.length, end)
                itemRangeList.add(ItemRange("<li>${text}</li>", start, end))
                sourceText.indexOf(checkChar, end)
            }
        }
    }

    private fun removeCheckBoxChar(outText: StringBuilder): String {
        var index = outText.indexOf(TO_DO_CHECKED_STRING)
        while (index != -1) {
            outText.deleteCharAt(index)
            index = outText.indexOf(TO_DO_CHECKED_STRING, index)
        }
        index = outText.indexOf(TO_DO_UNCHECK_STRING)
        while (index != -1) {
            outText.deleteCharAt(index)
            index = outText.indexOf(TO_DO_UNCHECK_STRING, index)
        }
        return outText.toString()
    }

    private fun createImageSource(src: String): String {
        return "<img src=\"$src\">"
    }

    private fun parseAttachmentFromRichContent(richContent: String): List<String> {
        val attachments: MutableList<String> = ArrayList()
        val regex = "\u0004([^\u0005]*)\u0005"
        val pattern = Pattern.compile(regex)
        val matcher = pattern.matcher(richContent)
        while (matcher.find()) {
            attachments.add(matcher.group(1))
        }
        return attachments
    }

    private fun addDivTag(outRawText: StringBuilder, rangeList: MutableList<ImageRange>) {
        outRawText.append(DIV_TAG_END)
        rangeList.forEach {
            outRawText.insert(it.end, DIV_TAG_START)
            outRawText.insert(it.start, DIV_TAG_END)
        }
        outRawText.insert(0, DIV_TAG_START)
    }

    private fun calculationImageRange(sourceText: StringBuilder): MutableList<ImageRange> {
        val rangeList = mutableListOf<ImageRange>()
        var start = sourceText.indexOf('<')
        while (start != -1) {
            val end = sourceText.indexOf('>', start)
            require(end != -1)
            rangeList.add(ImageRange(sourceText.substring(start, end + 1), start, end + 1))
            start = sourceText.indexOf('<', end + 1)
        }
        rangeList.reverse()
        return rangeList
    }

    /**
     * 计算待办区间
     */
    private fun calculationCheckRange(sourceText: StringBuilder, isCheck: Boolean, rangeList: MutableList<CheckRange>) {
        val checkChar = if (isCheck) TO_DO_CHECKED_STRING else TO_DO_UNCHECK_STRING
        var start = sourceText.indexOf(checkChar)
        while (start != -1) {
            val charCR = sourceText.indexOf('\n', start)
            val charLT = sourceText.indexOf('<', start)
            val end = if ((charCR == -1) || (charLT == -1)) {
                max(charCR, charLT)
            } else {
                min(charCR, charLT)
            }
            val char = sourceText[end]
            start = if (char == '\n') {
                val text = sourceText.substring(start + TO_DO_UNCHECK_STRING.length, end + 1).replace("\n", "")
                rangeList.add(CheckRange("<li class=\"${if (isCheck) "checked" else "unchecked"}\">${text}</li>", isCheck, start, end + 1))
                sourceText.indexOf(checkChar, end + 1)
            } else {
                val text = sourceText.substring(start + TO_DO_UNCHECK_STRING.length, end)
                rangeList.add(CheckRange("<li class=\"${if (isCheck) "checked" else "unchecked"}\">${text}</li>", isCheck, start, end))
                sourceText.indexOf(checkChar, end)
            }
        }
    }

    /**
     * 计算并替换连续待办
     * */
    private fun calculationAndReplaceCheckRange(source: StringBuilder, checkRangeList: MutableList<CheckRange>) {
        val tempList = mutableListOf<CheckRange>()
        checkRangeList.forEach {
            if (tempList.size > 0 && tempList.last().start != it.end) {
                replaceCheckRangeList(source, tempList)
            }
            tempList.add(it)
        }
        if (tempList.size > 0) {
            replaceCheckRangeList(source, tempList)
        }
    }

    /**
     * 替换待办并将连续待办合并
     * */
    private fun replaceCheckRangeList(source: StringBuilder, checkBoxRangeList: MutableList<CheckRange>) {
        checkBoxRangeList.forEachIndexed { index, checkRange ->
            val str = "${if (index == checkBoxRangeList.lastIndex) "<ul>" else ""}${checkRange.text}${if (index == 0) "</ul>" else ""}"
            source.replace(checkRange.start, checkRange.end, str)
        }
        checkBoxRangeList.clear()
    }

    private fun replaceItemRangeList(source: StringBuilder, itemRangeList: MutableList<ItemRange>) {
        itemRangeList.forEachIndexed { index, checkRange ->
            val str = "${if (index == itemRangeList.lastIndex) "<ul>" else ""}${checkRange.text}${if (index == 0) "</ul>" else ""}"
            source.replace(checkRange.start, checkRange.end, str)
        }
        itemRangeList.clear()
    }
}

sealed class Range(open val start: Int, open val end: Int)
data class CheckRange(val text: String, val isCheck: Boolean, override val start: Int, override val end: Int) : Range(start, end)
data class ImageRange(val text: String, override val start: Int, override val end: Int) : Range(start, end)
data class ItemRange(val text: String, override val start: Int, override val end: Int) : Range(start, end)