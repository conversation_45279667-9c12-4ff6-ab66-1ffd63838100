package com.oplus.migrate.old

import android.provider.BaseColumns

object OldNoteContract {

    /**内容摘要的最大长度 */
    const val SUMMARY_MAX_COUNT = 100

    //call method
    const val METHOD_GET_NEED_SYNC_ATTACHMENTS = "METHOD_GET_NEED_SYNC_ATTACHMENTS"
    const val KEY_NEED_SYNC_ATTACHMENTS = "KEY_NEED_SYNC_ATTACHMENTS"
    const val METHOD_UPDATE_ATTACHMENTS = "METHOD_UPDATE_ATTACHMENTS"
    const val KEY_ATTACHMENT_NAME = "KEY_ATTACHMENT_NAME"
    const val KEY_ATTACHMENT_FILEID = "KEY_ATTACHMENT_FILEID"
    const val METHOD_GET_NEED_DOWNLOAD_ATTACHMENTS = "METHOD_GET_NEED_DOWNLOAD_ATTACHMENTS"
    const val KEY_NEED_DOWNLOAD_ATTACHMENTS = "KEY_NEED_DOWNLOAD_ATTACHMENTS"
    const val METHOD_GET_DOWNLOAD_ATTACHMENTS = "METHOD_GET_DOWNLOAD_ATTACHMENTS"
    const val KEY_DOWNLOAD_ATTACHMENT_NAME = "KEY_DOWNLOAD_ATTACHMENT_NAME"
    const val KEY_DOWNLOAD_FILEPATH = "KEY_DOWNLOAD_FILEPATH"
    const val METHOD_GET_NEED_SYNC_NOTES = "METHOD_GET_NEED_SYNC_NOTES"
    const val KEY_NEED_SYNC_NOTES = "KEY_NEED_SYNC_NOTES"
    const val METHOD_GET_NEED_SYNC_NOTES_ATTACHMENTS = "METHOD_GET_NEED_SYNC_NOTES_ATTACHMENTS"
    const val KEY_NEED_SYNC_NOTES_ID = "KEY_NEED_SYNC_NOTES_ID"
    const val KEY_NEED_SYNC_NOTES_ATTACHMENTS = "KEY_NEED_SYNC_NOTES_ATTACHMENTS"
    const val METHOD_UPDATE_NOTES_GLOBALID = "METHOD_UPDATE_NOTES_FLOBALID"
    const val KEY_NOTES_ITEM_ID = "KEY_NOTES_ITEM_ID"
    const val KEY_NOTES_GLOBALID = "KEY_NOTES_GLOBALID"
    const val METHOD_UPDATE_NOTES_STATUS = "METHOD_UPDATE_NOTES_STATUS"
    const val KEY_UPDATE_GLOBAL_ID = "KEY_UPDATE_GLOBAL_ID"
    const val METHOD_DELETE_NOTES = "METHOD_DELETE_NOTES"
    const val KEY_DELETE_GLOBAL_ID = "KEY_DELETE_GLOBAL_ID"
    const val METHOD_GET_RECOVERY_ADD_NOTES = "METHOD_GET_RECOVERY_ADD_NOTES"
    const val KEY_RECOVERY_ADD_NOTES = "KEY_RECOVERY_ADD_NOTES"
    const val KEY_RECOVERY_ADD_NOTES_ATTACHMENTS = "KEY_RECOVERY_ADD_NOTES_ATTACHMENTS"
    const val KEY_RECOVERY_ADD_NOTES_RESULT = "KEY_RECOVERY_ADD_NOTES_RESULT"
    const val METHOD_GET_RECOVERY_ADD_NOTES_ITEMIDS = "METHOD_GET_RECOVERY_ADD_NOTES_ITEMIDS"
    const val KEY_RECOVERY_ALL_NOTES_ITEMIDS = "KEY_RECOVERY_ALL_NOTES_ITEMIDS"
    const val METHOD_GET_RECOVERY_UPDATE_NOTES = "METHOD_GET_RECOVERY_UPDATE_NOTES"
    const val KEY_RECOVERY_UPDATE_NOTES = "KEY_RECOVERY_UPDATE_NOTES"
    const val KEY_RECOVERY_UPDATE_NOTES_ATTACHMENT = "KEY_RECOVERY_UPDATE_NOTES_ATTACHMENT"
    const val KEY_RECOVERY_UPDATE_NOTES_RESULT = "KEY_RECOVERY_UPDATE_NOTES_RESULT"
    const val METHOD_GET_RECOVERY_UPDATE_NOTES_ATTACHMENTS = "METHOD_GET_RECOVERY_UPDATE_NOTES_ATTACHMENTS"
    const val KEY_RECOVERY_UPDATE_NOTES_ID = "KEY_RECOVERY_UPDATE_NOTES_ID"
    const val KEY_RECOVERY_UPDATE_NOTES_ATTACHMENTS = "KEY_RECOVERY_UPDATE_NOTES_ATTACHMENTS"
    const val METHOD_GET_RECOVERY_DELETE_NOTES = "METHOD_GET_RECOVERY_DELETE_NOTES"
    const val KEY_RECOVERY_DELETE_NOTES = "KEY_RECOVERY_DELETE_NOTES"
    const val METHOD_HAS_DIRTY_DATA = "METHOD_HAS_DIRTY_DATA"
    const val KEY_HAS_DIRTY_DATA = "KEY_HAS_DIRTY_DATA"
    const val METHOD_CLEAR_SYNC_INFO = "METHOD_CLEAR_SYNC_INFO"
    const val METHOD_GET_UPLOAD_FILE_BYTES = "METHOD_GET_UPLOAD_FILE_BYTES"
    const val KEY_GET_UPLOAD_FILE_BYTES_ATTACHMENTNAME = "KEY_GET_UPLOAD_FILE_BYTES_ATTACHMENTNAME"
    const val KEY_GET_UPLOAD_FILE_BYTES = "KEY_GET_UPLOAD_FILE_BYTES"
    const val METHOD_CLEAR_NOTES = "METHOD_CLEAR_NOTES"
    const val METHOD_GET_DETAIL_WIDGET_INFO = "METHOD_GET_DETAIL_WIDGET_INFO"
    const val KEY_DETAIL_WIDGET_ID = "KEY_DETAIL_WIDGET_ID"

    /**
     * backup restore start
     */
    const val METHOD_COPY_FOLDER = "METHOD_COPY_FOLDER"
    const val KEY_COPY_FOLDER_SRC = "KEY_COPY_FOLDER_SRC"
    const val KEY_COPY_FOLDER_DES = "KEY_COPY_FOLDER_DES"
    const val KEY_COPY_RESULT = "KEY_COPY_RESULT"
    const val METHOD_BACKUP = "METHOD_BACKUP"
    const val KEY_BACKUP_FILENAME = "KEY_BACKUP_FILENAME"
    const val METHOD_BACKUP_COUNT = "METHOD_BACKUP_COUNT"
    const val KEY_BACKUP_COUNT = "KEY_BACKUP_COUNT"
    const val METHOD_BACKUP_SIZE = "METHOD_RESTORE_SIZE"
    const val KEY_BACKUP_TOTAL_SIZE = "KEY_RESTORE_TOTAL_SIZE"
    const val METHOD_RESTORE = "METHOD_RESTORE"
    const val METHOD_RESTORE_COUNT = "METHOD_RESTORE_COUNT"
    const val KEY_RESTORE_FILENAME = "KEY_RESTORE_FILENAME"
    const val KEY_RESTORE_COUNT = "KEY_RESTORE_COUNT"

    /**
     * backup restore end
     */
    const val KEY_RETAIL_MODEL_INSERT = "KEY_RETAIL_MODEL_INSERT"
    const val KEY_RETAIL_MODEL_DATA = "KEY_RETAIL_MODEL_DATA"
    const val KEY_RETAIL_MODEL_DATA_IMAGE = "KEY_RETAIL_MODEL_DATA_IMAGE"

    /**
     * Notes table contract
     */
    object Notes : BaseColumns {
        /**
         * 标题
         */
        const val COLUMN_NAME_TITLE = "title"
        const val COLUMN_NAME_HAS_EDIT_TITLE = "edit_has_title"

        /**
         * 富文本内容（包含图片等信息）
         */
        const val COLUMN_NAME_RICH_CONTENT = "rich_content"

        /**
         * 普通内容（不包含图片等信息）
         */
        const val COLUMN_NAME_CONTENT = "content"

        /**
         * 内容摘要（content的前面NoteContract.SUMMARY_MAX_COUNT个字符）
         */
        const val COLUMN_NAME_SUMMARY = "summary"

        /**
         * 创建时间
         */
        const val COLUMN_NAME_CREATE_DATE = "created"

        /**
         * 修改时间
         */
        const val COLUMN_NAME_MODIFICATION_DATE = "modified"

        /**
         * 内容摘要 0:普通便签  1:通话便签
         */
        const val COLUMN_NAME_TYPE = "type"

        /**普通便签  */
        const val TYPE_NORMAL = 0

        /**通话便签  */
        const val TYPE_CALL = 1

        /**
         * 是否置顶  1:置顶 0:未置顶
         */
        const val COLUMN_NAME_TOP = "top"

        /**
         * 置顶时间 只有便签被置顶的时候该时间才是有效的
         */
        const val COLUMN_NAME_SET_TOP_TIME = "set_top_time"

        /**
         * 是否有图片  1:有 0:没有
         */
        const val COLUMN_NAME_HAS_PHOTO = "has_photo"

        /**
         * 是否有项目  1:有 0:没有
         */
        const val COLUMN_NAME_HAS_ITEM = "has_item"

        /**
         * 是否有代办事项  1:有 0:没有
         */
        const val COLUMN_NAME_HAS_TODO = "has_todo"
        const val COLUMN_NAME_IS_USER = "is_user"

        /**
         * 列表界面的缩略图
         */
        const val COLUMN_NAME_THUMBNAIL = "thumbnail"

        /**
         * sync server file_id (String)
         */
        const val COLUMN_NAME_GLOBAL_ID = "global_id"

        /**
         * note location itemId UUID(String)
         */
        const val COLUMN_NAME_ITEM_ID = "item_id"

        /**
         * 1 has_attachment 0 no_attachment，目前附件只有图片
         */
        const val COLUMN_NAME_HAS_ATTACHMENT = "has_attachment"

        /**
         * note status(0 nomodify 1 new 2 modify 3 delete)
         */
        const val COLUMN_NAME_STATUS = "status"
        const val NOTE_STATUS_NOMODIFY = 0
        const val NOTE_STATUS_NEW = 1
        const val NOTE_STATUS_MODIFY = 2
        const val NOTE_STATUS_DELETE = 3
        const val NOTE_STATUS_BACKUPED = 4

        /**
         * remind time
         */
        const val COLUMN_NAME_HAS_REMIND_TIME = "has_remind_time"
        const val COLUMN_NAME_REMIND_TIME = "remind_time"

        //其中1是已经通知过，0是还未通知
        const val COLUMN_NAME_HAVA_INFORMED_TIME = "have_informed"
        const val COLUMN_NAME_HAVA_BACKUPED = "backup_status"
        const val COLUMN_NAME_ATTACHMENT_COUNT = "attachment_count"

        /**
         * The table name offered by this provider
         */
        const val TABLE_NAME = "notes"
        /*
         * URI definitions
         */
        /**
         * The scheme part for this provider's URI
         */
        private const val SCHEME = "content://"
        /**
         * Path parts for the URIs
         */
        /**
         * Path part for the Notes URI
         */
        private const val PATH_NOTES = "/notes"

        /**
         * Path part for the Note ID URI
         */
        private const val PATH_NOTE_ID = "/notes/"

        /**
         * 0-relative position of a note ID segment in the path part of a note ID URI
         */
        const val NOTE_ID_PATH_POSITION = 1

        /**
         * The default sort order for this table
         */
        const val DEFAULT_SORT_ORDER = (COLUMN_NAME_TOP + " DESC, "
                + COLUMN_NAME_MODIFICATION_DATE + " DESC")
    }
}