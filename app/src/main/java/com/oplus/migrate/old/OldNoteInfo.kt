package com.oplus.migrate.old

class OldNoteInfo {
    var id = 0
    var title: String? = null
    var hasEditTitle // 0 false 1 true
            = 0
    var content: String? = null
    var summary: String? = null
    var richContent: String? = null
    var created: Long = 0
    var modified: Long = 0
    var type // 0 normal 1 call
            = 0
    var top // 0 top 1 no-top
            = 0
    var setTopTime: Long = 0
    var hasItem // 0 false 1 true
            = 0
    var hasTodo // 0 false 1 true
            = 0
    var hasPhoto // 0 false 1 true
            = 0
    var isUser // 0 not-user 1 user
            = 0
    var thumbNail: String? = null
    var globalId: String? = null
    var itemId: String? = null
    var hasAttachment // 0 false 1 true
            = 0
    var status //0 nomodify 1 new 2 modify 3 delete
            = 0
    var noteAttachments: Array<OldNoteAttachmentInfo?> = arrayOf()
    var hasRemindTime // 0 false 1 true
            = 0
    var remindTime: Long = 0
    var haveInformed // 0 false 1 true
            = 0
    var isSendShelf // 0 false 1 true
            = 0
    var isBackuped //0 false,1 true,2 full recover from cloud
            = 0
    var attachmentCount = 0
    /*fun getTitle(): String {
        return if (title == null) " " else title!!
    }

    fun setTitle(title: String?) {
        this.title = title
    }

    fun getContent(): String {
        return if (content == null) " " else content!!
    }

    fun setContent(content: String?) {
        this.content = content
    }

    fun getSummary(): String {
        return if (summary == null) " " else summary!!
    }

    fun setSummary(summary: String?) {
        this.summary = summary
    }

    fun getRichContent(): String {
        return if (richContent == null) " " else richContent!!
    }

    fun setRichContent(richContent: String?) {
        this.richContent = richContent
    }

    fun getThumbNail(): String {
        return if (thumbNail == null) " " else thumbNail!!
    }

    fun setThumbNail(thumbNail: String?) {
        this.thumbNail = thumbNail
    }

    fun getGlobalId(): String {
        return if (globalId == null) " " else globalId!!
    }

    fun setGlobalId(globalId: String?) {
        this.globalId = globalId
    }

    fun getItemId(): String {
        return if (itemId == null) " " else itemId!!
    }

    fun setItemId(itemId: String?) {
        this.itemId = itemId
    }*/

    object NoteXml {
        const val NOTE_COUNT_TAG = "noteCount"
        const val NOTE_COUNT = "count"
        const val NOTE_TAG = "noteRecord"
        const val NOTE_ID = /*OldNoteContract.Notes._ID*/"_id";
        const val NOTE_TITLE = OldNoteContract.Notes.COLUMN_NAME_TITLE
        const val NOTE_HAS_EDIT_TITLE = OldNoteContract.Notes.COLUMN_NAME_HAS_EDIT_TITLE
        const val NOTE_RICH_CONTENT = OldNoteContract.Notes.COLUMN_NAME_RICH_CONTENT
        const val NOTE_CONTENT = OldNoteContract.Notes.COLUMN_NAME_CONTENT
        const val NOTE_SUMMARY = OldNoteContract.Notes.COLUMN_NAME_SUMMARY
        const val NOTE_CREATE = OldNoteContract.Notes.COLUMN_NAME_CREATE_DATE
        const val NOTE_MODIFIED = OldNoteContract.Notes.COLUMN_NAME_MODIFICATION_DATE
        const val NOTE_TYPE = OldNoteContract.Notes.COLUMN_NAME_TYPE
        const val NOTE_TOP = OldNoteContract.Notes.COLUMN_NAME_TOP
        const val NOTE_SET_TOP_TIME = OldNoteContract.Notes.COLUMN_NAME_SET_TOP_TIME
        const val NOTE_HAS_PHOTO = OldNoteContract.Notes.COLUMN_NAME_HAS_PHOTO
        const val NOTE_HAS_ITEM = OldNoteContract.Notes.COLUMN_NAME_HAS_ITEM
        const val NOTE_HAS_TODO = OldNoteContract.Notes.COLUMN_NAME_HAS_TODO
        const val NOTE_IS_USER = OldNoteContract.Notes.COLUMN_NAME_IS_USER
        const val NOTE_THUMBNAIL = OldNoteContract.Notes.COLUMN_NAME_THUMBNAIL
        const val NOTE_GLOBAL_ID = OldNoteContract.Notes.COLUMN_NAME_GLOBAL_ID
        const val NOTE_ITEM_ID = OldNoteContract.Notes.COLUMN_NAME_ITEM_ID
        const val NOTE_HAS_ATTACHMENT = OldNoteContract.Notes.COLUMN_NAME_HAS_ATTACHMENT
        const val NOTE_STATUS = OldNoteContract.Notes.COLUMN_NAME_STATUS
        const val NOTE_HAS_REMIND_TIME = OldNoteContract.Notes.COLUMN_NAME_HAS_REMIND_TIME
        const val NOTE_REMIND_TIME = OldNoteContract.Notes.COLUMN_NAME_REMIND_TIME
        const val NOTE_HAVA_INFORMED_TIME = OldNoteContract.Notes.COLUMN_NAME_HAVA_INFORMED_TIME
        const val NOTE_HAVA_BACKUPED = OldNoteContract.Notes.COLUMN_NAME_HAVA_BACKUPED
        const val NOTE_ATTACHMENT_COUNT = OldNoteContract.Notes.COLUMN_NAME_ATTACHMENT_COUNT
    }

    override fun toString(): String {
        return "OldNoteInfo{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", hasEditTitle=" + hasEditTitle +
                ", content='" + content + '\'' +
                ", summary='" + summary + '\'' +
                ", richContent='" + richContent + '\'' +
                ", created=" + created +
                ", modified=" + modified +
                ", type=" + type +
                ", top=" + top +
                ", setTopTime=" + setTopTime +
                ", hasItem=" + hasItem +
                ", hasTodo=" + hasTodo +
                ", hasPhoto=" + hasPhoto +
                ", isUser=" + isUser +
                ", thumbNail='" + thumbNail + '\'' +
                ", globalId='" + globalId + '\'' +
                ", itemId='" + itemId + '\'' +
                ", hasAttachment=" + hasAttachment +
                ", status=" + status +
                ", noteAttachments=" + noteAttachments.contentToString() +
                ", hasRemindTime=" + hasRemindTime +
                ", remindTime=" + remindTime +
                ", haveInformed=" + haveInformed +
                ", isSendShelf=" + isSendShelf +
                ", isBackuped=" + isBackuped +
                ", attachmentCount=" + attachmentCount +
                '}'
    }
}