package com.oplus.migrate.old

import com.oplus.note.logger.AppLogger
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserException
import org.xmlpull.v1.XmlPullParserFactory
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.IOException
import java.util.*

object OldNoteXMLParser {
    private const val TAG = "NoteXMLParser"
    fun parseRestoreCount(fileName: String?): Int {
        var count = 0
        var `is`: FileInputStream? = null
        try {
            `is` = FileInputStream(fileName)
            val parser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(`is`, null)
            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        val tag = parser.name
                        if (OldNoteInfo.NoteXml.NOTE_COUNT_TAG == tag) {
                            count = parser.getAttributeValue(0).toInt()
                        }
                    }
                    XmlPullParser.END_TAG -> {
                    }
                    else -> {
                    }
                }
                if (0 != count) {
                    //MyLogs.d(TAG, "count read success,break! count = " + count);
                    break
                }
                eventType = parser.next()
            }
        } catch (e: FileNotFoundException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount file not found error: $e")
        } catch (e: XmlPullParserException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount xml pull parser error: $e")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount IO error: $e")
        } finally {
            if (null != `is`) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    AppLogger.RED_MIGRATION.e(TAG, "parseRestoreCount close error: $e")
                }
            }
        }
        return count
    }

    @JvmStatic
    fun parseOldNoteInfo(fileName: String?): List<OldNoteInfo> {
        val oldNoteInfo: MutableList<OldNoteInfo> = ArrayList()
        var `is`: FileInputStream? = null
        try {
            `is` = FileInputStream(fileName)
            val parser = XmlPullParserFactory.newInstance().newPullParser()
            parser.setInput(`is`, null)
            var note: OldNoteInfo? = null
            var eventType = parser.eventType
            var attachmentInfoList: MutableList<OldNoteAttachmentInfo?>? = null
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> {
                    }
                    XmlPullParser.START_TAG -> {
                        val tag = parser.name
                        if (OldNoteInfo.NoteXml.NOTE_TAG == tag) {
                            note = OldNoteInfo()
                            attachmentInfoList = ArrayList()
                            val attrNum = parser.attributeCount
                            var i = 0
                            while (i < attrNum) {
                                val name = parser.getAttributeName(i)
                                val value = parser.getAttributeValue(i)
                                fillOldNoteInfo(note, name, value)
                                i++
                            }
                        } else if (OldNoteAttachmentInfo.NoteAttachmentXml.ATTCHMENT_TAG == tag) {
                            val noteAttachment = OldNoteAttachmentInfo()
                            val attachmentAttrNum = parser.attributeCount
                            var i = 0
                            while (i < attachmentAttrNum) {
                                val attchmentName = parser.getAttributeName(i)
                                val attchmentValue = parser.getAttributeValue(i)
                                fillOldNoteAttachmentInfo(noteAttachment, attchmentName, attchmentValue)
                                i++
                            }
                            attachmentInfoList?.add(noteAttachment)
                        }
                    }
                    XmlPullParser.END_TAG -> {
                        val tag = parser.name
                        if (OldNoteInfo.NoteXml.NOTE_TAG == tag && null != note) {
                            if (attachmentInfoList!!.size > 0) {
                                val attachmentInfo = arrayOfNulls<OldNoteAttachmentInfo>(attachmentInfoList.size)
                                var i = 0
                                while (i < attachmentInfoList.size) {
                                    attachmentInfo[i] = attachmentInfoList[i]
                                    i++
                                }
                                note.noteAttachments = attachmentInfo
                            }
                            oldNoteInfo.add(note)
                        }
                    }
                    else -> {
                    }
                }
                eventType = parser.next()
            }
        } catch (e: FileNotFoundException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseOldNoteInfo file not found error: $e")
        } catch (e: XmlPullParserException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseOldNoteInfo xml pull parser error: $e")
        } catch (e: IOException) {
            AppLogger.RED_MIGRATION.e(TAG, "parseOldNoteInfo IO error: $e")
        } finally {
            if (null != `is`) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    AppLogger.RED_MIGRATION.e(TAG, "parseOldNoteInfo close error: $e")
                }
            }
        }
        return oldNoteInfo
    }

    private fun fillOldNoteInfo(note: OldNoteInfo, name: String, value: String) {
        if (OldNoteInfo.NoteXml.NOTE_ID == name) {
            note.id = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_TITLE == name) {
            note.title = value
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_EDIT_TITLE == name) {
            note.hasEditTitle = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_RICH_CONTENT == name) {
            note.richContent = value
        } else if (OldNoteInfo.NoteXml.NOTE_CONTENT == name) {
            note.content = value
        } else if (OldNoteInfo.NoteXml.NOTE_SUMMARY == name) {
            note.summary = value
        } else if (OldNoteInfo.NoteXml.NOTE_CREATE == name) {
            note.created = value.toLong()
        } else if (OldNoteInfo.NoteXml.NOTE_MODIFIED == name) {
            note.modified = value.toLong()
        } else if (OldNoteInfo.NoteXml.NOTE_TYPE == name) {
            note.type = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_TOP == name) {
            note.top = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_SET_TOP_TIME == name) {
            note.setTopTime = value.toLong()
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_PHOTO == name) {
            note.hasPhoto = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_ITEM == name) {
            note.hasItem = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_TODO == name) {
            note.hasTodo = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_IS_USER == name) {
            note.isUser = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_THUMBNAIL == name) {
            note.thumbNail = value
        } else if (OldNoteInfo.NoteXml.NOTE_GLOBAL_ID == name) {
            note.globalId = value
        } else if (OldNoteInfo.NoteXml.NOTE_ITEM_ID == name) {
            note.itemId = value
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_ATTACHMENT == name) {
            note.hasAttachment = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_STATUS == name) {
            note.status = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_HAS_REMIND_TIME == name) {
            note.hasRemindTime = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_REMIND_TIME == name) {
            note.remindTime = value.toLong()
        } else if (OldNoteInfo.NoteXml.NOTE_HAVA_INFORMED_TIME == name) {
            note.haveInformed = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_HAVA_BACKUPED == name) {
            note.isBackuped = value.toInt()
        } else if (OldNoteInfo.NoteXml.NOTE_ATTACHMENT_COUNT == name) {
            note.attachmentCount = value.toInt()
        }
    }

    private fun fillOldNoteAttachmentInfo(noteAttachment: OldNoteAttachmentInfo, name: String, value: String) {
        if (OldNoteAttachmentInfo.NoteAttachmentXml.NOTE_ATTACHMENT_NOTEID == name) {
            noteAttachment.noteId = value.toInt()
        } else if (OldNoteAttachmentInfo.NoteAttachmentXml.NOTE_ATTACHMENT_GLOBALID == name) {
            noteAttachment.file_id = value
        } else if (OldNoteAttachmentInfo.NoteAttachmentXml.NOTE_ATTACHMENT_ITEMID == name) {
            noteAttachment.itemId = value
        } else if (OldNoteAttachmentInfo.NoteAttachmentXml.NOTE_ATTACHMENT_NAME == name) {
            noteAttachment.attachment_name = value
        } else if (OldNoteAttachmentInfo.NoteAttachmentXml.NOTE_ATTACHMENT_STATUS == name) {
            noteAttachment.status = value.toInt()
        }
    }
}