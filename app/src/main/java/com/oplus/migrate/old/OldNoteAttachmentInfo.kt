package com.oplus.migrate.old

class OldNoteAttachmentInfo {
    var file_id: String? = null
        get() = if (field == null) " " else field
    var itemId: String? = null
        get() = if (field == null) " " else field
    var attachment_name: String? = null
        get() = if (field == null) " " else field
    var status = 0
    var noteId = 0

    object NoteAttachmentXml {
        const val ATTCHMENT_TAG = "noteAttachment"
        const val NOTE_ATTACHMENT_NOTEID = OldNoteAttachmentContract.COLUMN_NAME_NOTE_ID
        const val NOTE_ATTACHMENT_GLOBALID = OldNoteAttachmentContract.COLUMN_NAME_GLOBAL_ID
        const val NOTE_ATTACHMENT_ITEMID = OldNoteAttachmentContract.COLUMN_NAME_ITEM_ID
        const val NOTE_ATTACHMENT_NAME = OldNoteAttachmentContract.COLUMN_NAME_ATTACHMENT_NAME
        const val NOTE_ATTACHMENT_STATUS = OldNoteAttachmentContract.COLUMN_NAME_STATUS
    }

    override fun toString(): String {
        return "OldNoteAttachmentInfo(file_id=$file_id, itemId=$itemId, attachment_name=$attachment_name, status=$status, noteId=$noteId)"
    }


}