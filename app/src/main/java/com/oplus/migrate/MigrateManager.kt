/*****************************************************************
 * * Copyright (C), 2021-2029, OPlus Technology (Shenzhen) Co., Ltd
 * * VENDOR_EDIT
 * * File: - MigrateManager
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/22
 * * Author: XXX
 * *
 * * ---------------------- Revision History:----------------------
 * * <author> <date> <version> <desc>
 * * XXX 2021/6/22 1.0 create
</desc></version></date></author> */
package com.oplus.migrate

import android.annotation.SuppressLint
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import com.nearme.note.util.StatisticsUtils
import com.oplus.migrate.utils.FilePermissionUtil
import com.oplus.note.logger.AppLogger
import com.oplus.migrate.old.OldNoteInfo
import com.oplus.migrate.shelf.ShelfMigrateManager
import com.oplus.migrate.utils.CallerTraceUtil
import com.oplus.migrate.utils.MigrateResult.MIGRATION_END_ALREADY_DONE
import com.oplus.migrate.utils.MigrateResult.MIGRATION_END_OLD_DB_EMPTY_RECORDS
import com.oplus.migrate.utils.MigrateResult.MIGRATION_END_OLD_DB_NULL
import com.oplus.migrate.utils.MigrateResult.MIGRATION_END_SUCCESS
import com.oplus.migrate.utils.MigrateResult.MIGRATION_IN_PROGRESS
import com.oplus.migrate.utils.MigrateResult.MIGRATION_UNDONE
import com.oplus.migrate.utils.DbUtil
import com.oplus.migrate.utils.DbUtil.closeDb
import com.oplus.migrate.utils.DbUtil.getOldDatabase
import com.oplus.migrate.utils.DbUtil.markInvalidData
import com.oplus.migrate.utils.MigrateResult.MIGRATION_END_EXCEPTION
import com.oplus.migrate.utils.OldDataRestoreUtil.migrateAttachments
import com.oplus.migrate.utils.OldDataRestoreUtil.migrateDatas
import com.oplus.migrate.utils.SharedPreferenceUtil.KEY_IS_MIGRATE_DONE
import com.oplus.migrate.utils.SharedPreferenceUtil.KEY_IS_MIGRATE_MARK_INVALID_DATA_DONE
import com.oplus.migrate.utils.SharedPreferenceUtil.MIGRATE_PREF_NAME
import com.oplus.migrate.utils.SharedPreferenceUtil.getBoolean
import com.oplus.migrate.utils.SharedPreferenceUtil.getInt
import com.oplus.migrate.utils.SharedPreferenceUtil.putBoolean
import com.oplus.migrate.utils.SharedPreferenceUtil.putInt
import com.oplus.note.BuildConfig
import java.io.File

/**
 * <AUTHOR>
 */
class MigrateManager private constructor(private val context: Context, private val callback: MigrateStatusCallback?) {
    fun executeMigrate() {
        var status: Int
        var database: SQLiteDatabase? = null

        // should fix file permission manually
        FilePermissionUtil.fixPermission(context, File.separator + "files")

        try {
            val t = System.currentTimeMillis()
            val isNeedToMigrate = isNeedToMigrate(context)
            if (!isNeedToMigrate) {
                AppLogger.RED_MIGRATION.d(TAG, "note.db not exist, no need to do migrate")
                saveMigrateStatus(MIGRATION_END_SUCCESS)
                return
            }

            val isHasMigrated = checkIfHasMigrated()
            AppLogger.RED_MIGRATION.d(TAG, "isHasMigrated = $isHasMigrated")
            if (isHasMigrated) {
                AppLogger.RED_MIGRATION.d(TAG, "Migration end because it's already migrated")
                publishMigrateEnd(MIGRATION_END_ALREADY_DONE)
                return
            }

            publishMigrateStart()
            status = MIGRATION_IN_PROGRESS
            if (!BuildConfig.isExport && BuildConfig.isOnePlus) {
                //domestic Shelf Migrate
                ShelfMigrateManager.getInstance(context)?.doShelfMigrate()
                // should fix file permission manually
                FilePermissionUtil.fixPermission(context, File.separator + "files")
            }
            migrateSharedPreference()
            saveMigrateStatus(status)
            database = getOldDatabase(context)
            if (database == null) {
                AppLogger.RED_MIGRATION.d(TAG, "Migration end because old db is null")
                status = MIGRATION_END_OLD_DB_NULL
                saveMigrateStatus(status)
                publishMigrateEnd(MIGRATION_END_OLD_DB_NULL)
                StatisticsUtils.setRedNoteMigrateResult(0, "MIGRATION_END_OLD_DB_NULL")
                return
            }

            // check if mark invalid data is done or not
            val isMarkInvalidDataDone = getBoolean(context, MIGRATE_PREF_NAME, KEY_IS_MIGRATE_MARK_INVALID_DATA_DONE)
            AppLogger.RED_MIGRATION.d(TAG, "isMarkInvalidDataDone = $isMarkInvalidDataDone")
            if (!isMarkInvalidDataDone) {
                val isDone = markInvalidData(database)
                putBoolean(context, MIGRATE_PREF_NAME, KEY_IS_MIGRATE_MARK_INVALID_DATA_DONE, isDone)
                AppLogger.RED_MIGRATION.d(TAG, "markInvalidData result = $isDone")
            }

            var pagedOldNoteInfoList: List<OldNoteInfo>?
            val size = DbUtil.getOldNoteCount(database)
            if (size == 0) {
                AppLogger.RED_MIGRATION.d(TAG, "Migration end because no old note record in old db")
                status = MIGRATION_END_OLD_DB_EMPTY_RECORDS
            } else {
                val pages = PageUtil.split(size, PAGE_SIZE)
                for (pageInfo in pages) {
                    AppLogger.RED_MIGRATION.v(TAG, "page: $pageInfo")
                    // step2.1 get old notes from old db
                    pagedOldNoteInfoList = DbUtil.getOldNoteInfoFromOldDb(database, pageInfo)
                    AppLogger.RED_MIGRATION.d(TAG, "getOldNoteInfoFromOldDb pagedOldNoteInfoList " + if (pagedOldNoteInfoList != null) "size is " + pagedOldNoteInfoList.size else "is null")
                    if (pagedOldNoteInfoList != null && pagedOldNoteInfoList.isNotEmpty()) {
                        // step3.1 copy attachment
                        migrateAttachments(context, pagedOldNoteInfoList)
                        // step3.2 convert to new notes and insert to new db
                        migrateDatas(context, pagedOldNoteInfoList, callback)
                    }
                }
                status = MIGRATION_END_SUCCESS
            }

            /*// step2.1 get old notes from old db
            oldNoteInfoList = DbUtil.getOldNoteInfoFromOldDb(database)
            AppLogger.RED_MIGRATION.d(TAG, "getOldNoteInfoFromOldDb oldNoteInfoList " + if (oldNoteInfoList != null) "size is " + oldNoteInfoList.size else "is null")
            status = if (oldNoteInfoList != null && oldNoteInfoList.isNotEmpty()) {
                // step3.1 copy attachment
                migrateAttachments(context, oldNoteInfoList)
                // step3.2 convert to new notes and insert to new db
                migrateDatas(context, oldNoteInfoList, callback)
                MIGRATION_END_SUCCESS
            } else {
                AppLogger.RED_MIGRATION.d(TAG, "Migration end because no old note record in old db")
                MIGRATION_END_OLD_DB_EMPTY_RECORDS
            }*/
            AppLogger.RED_MIGRATION.d(TAG, "Migration end success, cost time = " + (System.currentTimeMillis() - t))
            StatisticsUtils.setRedNoteMigrateResult(1, "success")

        } catch (e: Exception) {
            val stackTrace = CallerTraceUtil.getCallerTrace(TAG, e.stackTrace)
            AppLogger.RED_MIGRATION.d(TAG, "Migration exception : " + e.message + ", stackTrace : $stackTrace")
            status = MIGRATION_END_EXCEPTION
            StatisticsUtils.setRedNoteMigrateResult(0, stackTrace)
        } finally {
            if (database != null) {
                closeDb(database)
            }
        }
        saveMigrateStatus(status)
        publishMigrateEnd(status)
    }

    private fun isNeedToMigrate(context: Context): Boolean {
        val oldDbFile = File(context.dataDir.absolutePath + File.separator + "databases" + File.separator + "note.db")
        return oldDbFile.exists() && oldDbFile.canRead()
    }

    private fun migrateSharedPreference() {
        val isListLayout = context.getSharedPreferences("com.oneplus.note", Context.MODE_PRIVATE).getBoolean("is_list_layout", true)
        putInt(context, "note_mode", "home_page_mode", if (isListLayout) 0 else 1)
        AppLogger.RED_MIGRATION.d(TAG, "migrateSharedPreference isListLayout = $isListLayout")
    }

    private fun checkIfHasMigrated(): Boolean {
        val status = getInt(context, MIGRATE_PREF_NAME, KEY_IS_MIGRATE_DONE, MIGRATION_UNDONE)
        AppLogger.RED_MIGRATION.d(TAG, "checkIfHasMigrated status = $status")
        return status > MIGRATION_IN_PROGRESS
    }

    private fun publishMigrateStart() {
        if (callback != null) {
            callback.start()
            AppLogger.RED_MIGRATION.d(TAG, "executeMigrate start")
        }
    }

    private fun publishMigrateEnd(status: Int) {
        if (callback != null) {
            callback.end(status)
            AppLogger.RED_MIGRATION.d(TAG, "executeMigrate end")
        }
    }

    private fun saveMigrateStatus(status: Int) {
        putInt(context, MIGRATE_PREF_NAME, KEY_IS_MIGRATE_DONE, status)
    }

    companion object {
        const val TAG = "MigrateManager"
        const val THUMB = "_thumb.png"
        const val PAGE_SIZE = 100;

        @SuppressLint("StaticFieldLeak")
        @Volatile private var instance: MigrateManager? = null
        fun getInstance(context: Context, callback: MigrateStatusCallback?) =
            instance ?: synchronized(this) {
                instance ?: MigrateManager(context.applicationContext, callback).also { instance = it }
            }
    }
}