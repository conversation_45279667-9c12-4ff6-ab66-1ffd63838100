/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: FolderSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/15
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.isEncryptLocal
import com.nearme.note.db.isNormalFolder
import com.nearme.note.db.isPrivateFolder
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudDataTypeProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.FolderTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger

open class FolderSyncManager(
    zone: String = Constants.ZONE_FOLDER,
    recordTypeVersion: Int = Constants.RECORD_TYPE_VERSION_FOLDER
) : AbsDataSyncManager(Constants.MODULE_NOTE, zone, recordTypeVersion) {

    companion object {
        private const val TAG = "FolderSyncManager"
    }

    private val transformer = FolderTransformer()
    private val localEncryptRecordIds = mutableSetOf<String>()
    private var mergerHelper: FolderMerger? = null
    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(TAG, "onStartRecovery")
        mergerHelper?.initEncryptDecryptFolderManager()
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryStart")

        // do nothing.
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd:${data?.size}")

        // use legacy folder merge strategy.
        data?.mapNotNull { record ->
            transformer.convertToFolderBeanFrom(record)
        }?.let { cloudFolders ->
            mergerHelper?.onRecoveryFolders(cloudFolders)
        }
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        AppLogger.BASIC.d(TAG, "onRecoveryEnd")
        mergerHelper?.onRecoveryEnd(backUp)
    }

    override fun onRecoverError(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(TAG, "onRecoverError")
        onRecoveryEnd(backUp)
    }

    override fun afterFetchData() {
        AppLogger.BASIC.d(TAG, "afterFetchData")
        mergerHelper?.forceMerge()
    }

    override fun getMetaDataCount(): Int {
        return AppDatabase.getInstance().foldersDao()
            .getAllNonEncrypt(FolderInfo.FOLDER_GUID_ENCRYPTED) -
                AppDatabase.getInstance().foldersDao().getNonEncryptCountOf(
                    FolderInfo.FOLDER_STATE_NEW,
                    FolderInfo.FOLDER_GUID_ENCRYPTED)
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(TAG, "onStartBackup")
        localEncryptRecordIds.clear()
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {
        // query all folder include mark delete.
        val folders = AppDatabase.getInstance().foldersDao().dirtyFolders.filter {
            /**
             * 非加密笔记本 ————————  原有策略备份
             * 原有隐私笔记本 ———————— 原有策略备份
             * 本地进行过加密的笔记本 ———————— 从云端Folder表删除
             */
                it.isNormalFolder() ||
                it.isPrivateFolder() ||
                it.isEncryptLocal()
        }
        AppLogger.CLOUDKIT.d(TAG, "onQueryDirtyData: ${folders.size}")
        return folders.map {
            if (it.isEncryptLocal()) {
                localEncryptRecordIds.add(it.guid)
                AppLogger.CLOUDKIT.d(TAG, "localEncryptRecordIds add: ${it.guid}")
            }
            transformer.convertFolderToRecordFrom(it)
        }
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupStart")

        // do nothing.
    }

    override fun onPagingBackupEnd(
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupEnd")
        val deletes = mutableListOf<String>()
        val updates = mutableListOf<Pair<String, Long>>()
        val encrypt = mutableListOf<Triple<Int, String, Long>>()
        successData?.forEach {
            AppLogger.CLOUDKIT.d(TAG, "successData:${it.operatorType} === ${it.sysRecordId}")
            if (it.operatorType == Constants.OPERATOR_TYPE_DELETE || it.operatorType == Constants.OPERATOR_TYPE_RECYCLE) {
                if (localEncryptRecordIds.contains(it.sysRecordId)) {
                    AppLogger.CLOUDKIT.d(TAG, "encrypt folder ,modify pre")
                    encrypt.add(Triple(FolderInfo.FOLDER_ENCRYPTED, it.sysRecordId, it.sysVersion))
                    return@forEach
                }
                deletes.add(it.sysRecordId)
            } else {
                updates.add(Pair(it.sysRecordId, it.sysVersion))
            }
        }
        AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deletes)
        AppDatabase.getInstance().foldersDao().updateFolderStateAndSysVersion(updates)
        mergerHelper?.setFolderEncryptedChanged(changedData = encrypt)
    }

    override fun getBackUpFinishErrorResult(
        cloudDataTypeProxy: CloudDataTypeProxy,
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ): CloudBackupResponseErrorProxy? {

        /**
         * bug 6030963，速记笔记本的 1104 错误，清除锚点，下次全量更新
         * 固定Id笔记本云端删除后，本地再新建，本地为NEW但云端需Resume.
         */
        val fixedIdFolders = listOf(
            FolderInfo.FOLDER_GUID_QUICK,
            FolderInfo.FOLDER_GUID_CALL_SUMMARY,
            FolderInfo.FOLDER_GUID_ARTICLE_SUMMARY,
            FolderInfo.FOLDER_GUID_AUDIO_SUMMARY,
            FolderInfo.FOLDER_GUID_PAINT
        )
        val quickNoteFoldError = errorData?.filter {
            it.subServerErrorCode == ERROR_CODE_SYS_VERSION_ISNULL
                    && it.sysRecordId in fixedIdFolders
        }
        if (!quickNoteFoldError.isNullOrEmpty()) {
            clearClearSystemVersion(cloudDataTypeProxy)
            return quickNoteFoldError[0]
        }
        return super.getBackUpFinishErrorResult(cloudDataTypeProxy, successData, errorData)
    }

    open fun setFolderMerger(mergerHelper: FolderMerger) {
        this.mergerHelper = mergerHelper
    }
}