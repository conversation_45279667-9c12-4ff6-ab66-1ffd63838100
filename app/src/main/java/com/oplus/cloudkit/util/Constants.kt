package com.oplus.cloudkit.util

import com.oplus.note.repo.note.entity.Folder

class Constants {
    companion object {
        const val MODULE_NOTE = ConstantsWrapper.MODULE_NOTE
        const val MODULE_TODO = ConstantsWrapper.MODULE_TODO

        const val ZONE_FOLDER = "note_folder_info"
        const val ZONE_SETTING = "note_setting_info"
        const val ZONE_NOTE = "note_item_info"
        const val ZONE_RICH_NOTE = "hypertext_item_info"
        const val ZONE_ENCRYPT_FOLDER = "note_encrypt_folder_info"
        const val ZONE_ENCRYPT_RICH_NOTE = "note_encrypt_hypertext_item_info"

        const val RECORD_TYPE_FOLDER = "folder_info"
        const val RECORD_TYPE_SETTING = "setting_info"
        const val RECORD_TYPE_NOTE_ITEM = "note_item_info"
        const val RECORD_TYPE_RICH_NOTE_ITEM = "hypertext_item_info"
        const val RECORD_TYPE_TODO_ITEM = "todo_item_info"
        const val RECORD_TYPE_ENCRYPT_RICH_NOTE_ITEM = "encrypt_hypertext_item_info"
        const val RECORD_TYPE_ENCRYPT_FOLDER = "encrypt_folder_info"

        const val RECORD_TYPE_VERSION_FOLDER = 0
        const val RECORD_TYPE_VERSION_SETTING = 1   //通话摘要升级为1
        const val RECORD_TYPE_VERSION_NOTE_ITEM = 0
        const val RECORD_TYPE_VERSION_RICH_NOTE_ITEM = 3 //通话摘要升级为3
        const val RECORD_TYPE_VERSION_TODO_ITEM = 0
        const val RECORD_TYPE_VERSION_ENCRYPT_FOLDER = 1 //通话摘要升级为1
        const val RECORD_TYPE_VERSION_ENCRYPT_RICH_NOTE_ITEM = 4//通话摘要升级为4

        const val OPERATOR_TYPE_CREATE = "create"   //新建元数据
        const val OPERATOR_TYPE_CREATE_AND_RECYCLE = "createAndRecycle"   //新建元数据在回收站
        const val OPERATOR_TYPE_DELETE = "deleteAndReplace" //彻底删除元数据
        const val OPERATOR_TYPE_DELETE_BY_OLD_NOTE = "delete" //彻底删除元数据(针对旧笔记无field字段)
        const val OPERATOR_TYPE_REPLACE = "replace" //替换整个元数据（确保元数据是新的）
        const val OPERATOR_TYPE_RECYCLE = "recycleAndReplace"   //把元数据挪到回收站
        const val OPERATOR_TYPE_RESUME = "resumeAndReplace" //把元数据从回收站 恢复出来
        const val OPERATOR_TYPE_RESTORE = "restoreAndReplace" //恢复删除的数据

        const val RECORD_STATUS_NORMAL = 0
        const val RECORD_STATUS_DELETED = 1
        const val RECORD_STATUS_RECYCLED = 2

        const val DATA_TYPE_NORMAL = 1
        const val DATA_TYPE_IO = 2


        const val FOLDER_SYNC_ON = Folder.FOLDER_SYNC_ON
        const val FOLDER_SYNC_OFF = Folder.FOLDER_SYNC_OFF
        const val FOLDER_SYNC_UNDEFINED = Folder.FOLDER_SYNC_UNDEFINED
    }
}