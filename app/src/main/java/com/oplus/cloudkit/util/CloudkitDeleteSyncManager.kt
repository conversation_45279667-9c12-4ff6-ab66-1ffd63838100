/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudkitDeleteSyncManager.kt
** Description:
** Version: 1.0
** Date : 2024/3/11
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  yanglinlong       2024/3/11      1.0     create file
****************************************************************/
package com.oplus.cloudkit.util

import android.content.Context
import androidx.annotation.WorkerThread
import com.nearme.note.db.AppDatabase
import com.nearme.note.util.PreferencesUtils
import com.oplus.note.logger.AppLogger

object CloudkitDeleteSyncManager {

    private const val TAG = "CloudkitDeleteSyncManager"

    @WorkerThread
    @JvmStatic
    fun checkAndChangeStatus(context: Context) {
        if (PreferencesUtils.getNeedCheckSyncSwitch(context)) {
            PreferencesUtils.setNeedCheckSyncSwitch(context, false)
            if (SyncSwitchStateRepository.isCloudClose(context)) {
                changeDataStatus()
            }
        }
    }

    /** 将笔记、笔记本、待办 status 为 unchange 的数据，状态改为 modify*/
    @WorkerThread
    @JvmStatic
    fun changeDataStatus() {
        AppDatabase.getInstance().apply {
            val noteCount = richNoteDao().changeStateUnChangeToModify()
            val todoCount = toDoDao().changeStatusUnChangeToArchived()
            val folderCount = foldersDao().changeStateUnChangeToModify()
            AppLogger.CLOUDKIT.d(TAG, "changeDataStatus noteCount $noteCount todoCount $todoCount folderCount $folderCount")
        }
    }
}