/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudKitSyncStatus.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2022/6/10
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/6/10     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.util

import java.lang.IllegalArgumentException

/**
 * CloudKit 云同步状态
 * @param priority 状态code，结束状态时表示优先级，数字越大优先级越高
 * @param isFinishStatus 是否是结束状态
 * */
@Suppress("MagicNumber")
enum class CloudKitSyncStatus(val priority: Int, val isFinishStatus: Boolean) {

    //同步中
    SYNC_CODE_SYNCING(11, false),

    // NOTE: 结束状态的优先级（权重）按取值大小定义
    SYNC_CODE_FINISHED_SUCCESS(100, true), //成功
    SYNC_CODE_FINISHED_FAIL(101, true), //失败
    SYNC_CODE_FINISHED_CLOUD_SPACE_NOT_ENOUGH(102, true), //云空间不足
    SYNC_CODE_FINISHED_INTERRUPTED(103, true), //请求中断
    SYNC_CODE_FINISHED_ATTACHMENT_UPLOAD_FAILED(104, true), // 附件上传失败
    SYNC_CODE_FINISHED_CLOUD_STEAM_LIMIT(105, true), //云端限流
    SYNC_CODE_FINISHED_CLOUD_COLD_STANDBY(106, true), //用户数据被迁移或者归档变成冷存储
    SYNC_CODE_FINISHED_LOCAL_SPACE_NOT_ENOUGH(107, true), //本地空间不足
    SYNC_CODE_FINISHED_NO_GLOBAL_STATE(107, true); //未获取到全球一体化信息

    companion object {
        @JvmStatic
        fun getSyncStatus(priorityCode: Int) : CloudKitSyncStatus {
            for (syncStatus in values()) {
                if (syncStatus.priority == priorityCode) {
                    return syncStatus
                }
            }
            throw IllegalArgumentException("priority code is inValid, please check")
        }
    }
}