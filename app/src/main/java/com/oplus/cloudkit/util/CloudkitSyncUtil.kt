package com.oplus.cloudkit.util

import android.content.Context
import com.nearme.note.MyApplication
import com.nearme.note.logic.AccountManager
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.tips.CloudSyncErrorType
import com.nearme.note.util.AppExecutors
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.NetworkUtils
import com.oplus.note.utils.isPackageDisabled
import com.oplus.cloudkit.CloudKitSdkManager
import java.util.concurrent.TimeUnit

object CloudkitSyncUtil {

    private const val GET_STATE_TIMEOUT = 150L

    @JvmStatic
    fun getSyncFailReason(context: Context, state: Int): CloudSyncErrorType {
        when (state) {
            CloudKitSdkManager.OPEN_ONLY_WIFI_CODE -> {
                if (!NetworkUtils.isWifiConnected(context)) {
                    return CloudSyncErrorType.WLANConnectionExceptionType
                }
            }
            CloudKitSdkManager.OPEN_ALL_CODE -> {
                if (!NetworkUtils.isNetworkConnected(context)) {
                    return CloudSyncErrorType.NetWorkConnectionErrorType
                }
            }
        }
        return CloudSyncErrorType.CloudkitSyncErrorType
    }

    @JvmStatic
    fun isCloudSyncEnable(context: Context): Boolean {
        var enable = false
        kotlin.runCatching {
            val feature = AppExecutors.getInstance().submitInDiskIO {
                val isCanCloud = AccountManager.isLogin(context) && !NoteSyncProcess.isCloudSyncSwitchClose(context)
                val isCloudEnable = !isPackageDisabled(MbaUtils.PACKAGER_CLOUD, context)
                isCanCloud && isCloudEnable
            }
            enable = feature.get(GET_STATE_TIMEOUT, TimeUnit.MILLISECONDS)
        }
        return enable
    }
}

val isCloudSyncEnableFun: () -> Boolean = { CloudkitSyncUtil.isCloudSyncEnable(MyApplication.appContext) }