/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/28
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import com.nearme.note.MyApplication
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NoteInfoDBUtil
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.note.NoteSyncViewModel
import com.oplus.cloud.sync.note.strategy.*
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.AttachmentTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.transformer.NoteTransformer

class NoteSyncManager(private val viewModel: NoteSyncViewModel) : AbsDataSyncManager(Constants.MODULE_NOTE, Constants.ZONE_NOTE, Constants.RECORD_TYPE_VERSION_NOTE_ITEM) {

    companion object {
        private const val TAG = "NoteSyncManager"
    }

    private val transformer = NoteTransformer()
    private val mergerHelper = MergerHelper()

    private val asm by lazy {
        AttachmentSyncManager(AttachmentTransformer(MyApplication.application))
    }

    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(TAG, "onStartRecovery")
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd: ${data?.size}")

        val folders = AppDatabase.getInstance().foldersDao().allFoldersOrderByCreateTime

        data?.forEach { record ->
            if (record.sysStatus == 1) {
                // 彻底删除
                val remote = NoteInfoDBUtil.queryNoteInfoByGlobleId(record.sysRecordId)
                if (remote != null) {
                    val relate = viewModel.getRelatedData(remote)
                    mergerHelper.mergeWhenDelete(remote, relate)
                }
            } else {
                // 新增或修改（包括删除到回收站）
                val remote = transformer.convertToNoteFrom(record, folders)
                if (remote != null) {
                    val relate = viewModel.getRelatedData(remote)
                    mergerHelper.mergeWhenUpdate(remote, relate)
                }
            }
        }
        mergerHelper.flush()
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        AppLogger.BASIC.d(TAG, "onRecoveryEnd")
        backUp.invoke()
    }

    override fun getMetaDataCount(): Int {
        // No data in new state
        return NoteInfoDBUtil.queryAllNotesCount()
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(TAG, "onStartBackup")
        asm.sync()
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {
        AppLogger.CLOUDKIT.d(TAG, "onQueryDirtyData")
        return viewModel.dirtyDataList.mapNotNull { transformer.convertToRecordFrom(it) }
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupStart")
    }

    override fun onPagingBackupEnd(successData: List<CloudBackupResponseRecordProxy>?, errorData: List<CloudBackupResponseErrorProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupEnd")
    }

    class MergerHelper {

        private val updateMergers by lazy {
            mutableListOf(
                    NoteConditionJudgeStrategy(),
                    NoteNewStrategy(),
                    NoteSameContentStrategy(),
                    NoteConflictStrategy(),
                    NoteContentUpdateStrategy(),
            )
        }

        private val deleteMergers by lazy {
            mutableListOf(
                    NoteDelConditionStrategy(),
                    NoteDelEditedStrategy(),
                    NoteDirDeleteStrategy()
            )
        }

        /**
         * @return true means the given remote data handle by a valid merge strategy.
         */
        fun mergeWhenUpdate(remote: NoteInfo, relate: NoteInfo?): Boolean {
            updateMergers.forEach { strategy ->
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
            return false
        }

        /**
         * @return true means the given remote data handle by a valid merge strategy.
         */
        fun mergeWhenDelete(remote: NoteInfo, relate: NoteInfo?): Boolean {
            deleteMergers.forEach { strategy ->
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
            return false
        }

        fun flush() {
            updateMergers.forEach { it.mergeDataListBuffer() }
            deleteMergers.forEach { it.mergeDataListBuffer() }
        }
    }
}