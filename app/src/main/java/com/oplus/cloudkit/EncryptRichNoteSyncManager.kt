/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptRichNoteSyncManager
 ** Description:
 **         v1.0:   Encrypted rich note cloud synchronization
 **
 ** Version: 1.0
 ** Date: 2023/10/25
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote       2023/10/25   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import android.text.TextUtils
import com.nearme.note.MyApplication
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.isDecryptLocal
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.isEncryptLocal
import com.nearme.note.model.isEncryptNote
import com.nearme.note.model.isSync
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.AttachmentTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger

class EncryptRichNoteSyncManager(
    zone: String = Constants.ZONE_ENCRYPT_RICH_NOTE,
    recordTypeVersion: Int = Constants.RECORD_TYPE_VERSION_ENCRYPT_RICH_NOTE_ITEM
) : RichNoteSyncManager(zone, recordTypeVersion) {

    private var mergerHelper: RichNoteMerger? = null
    private var startBackupTimestamp = 0L
    private val localDecryptRecordIds = mutableSetOf<String>()
    private val localEncryptRecordIds = mutableSetOf<String>()

    override fun setRichNoteMerger(mergerHelper: RichNoteMerger) {
        this.mergerHelper = mergerHelper
    }
    override fun tag(): String = "EncryptRichNoteSyncManager"
    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(tag(), "onStartRecovery")
        mergerHelper?.initEncryptDecryptRichNoteHelper()
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(tag(), "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(tag(), "onPagingRecoveryEnd:${data?.size}")
        mergerHelper?.recoverRichNotes(data)
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(tag(), "onRecoveryEnd")
        mergerHelper?.onRecoveryEnd(backUp)
    }

    override fun onRecoverError(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(tag(), "onRecoverError")
        onRecoveryEnd(backUp)
    }

    override fun getMetaDataCount(): Int {
        return RichNoteRepository.queryAllEncryptedNotesCount() - RichNoteRepository.getEncryptCountOf(
                RichNote.STATE_NEW
        )
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(tag(), "onStartBackup")

        localDecryptRecordIds.clear()
        localEncryptRecordIds.clear()
        if (mergerHelper?.hasNewData == true) {
            mergerHelper?.hasNewData = false
            WidgetUtils.sendNoteDataChangedBroadcast(MyApplication.appContext)
            NoteCardWidgetProvider.instance.postUIToCard(false)
        }

        dirtyRichNotes = null
        startBackupTimestamp = 0L
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {

        dirtyRichNotes = RichNoteRepository.queryDirtyData().filter {
            /**
             * 筛选出开启同步，
             * 加密的笔记和本地解密的笔记
             * 加密（1，1）修改的笔记修改加密表
             * 本地解密（0，1）的笔记从加密表删除
             */
            it.isSync() && ((it.isDecryptLocal() || it.isEncryptNote()))
        }
        AppLogger.CLOUDKIT.d(tag(), "filtered dirty size:${dirtyRichNotes?.size}")
        correctLegacyGlobalIds(dirtyRichNotes)
        correctEncryptChanges(dirtyRichNotes)
        return dirtyRichNotes?.mapNotNull {
            if (it.isDecryptLocal())  {
                localDecryptRecordIds.add(it.richNote.localId)
                AppLogger.CLOUDKIT.d(tag(), "localDecryptRecordIds add: ${it.richNote.localId}")
            } else if (it.isEncryptLocal()) {
                localEncryptRecordIds.add(it.richNote.localId)
                AppLogger.CLOUDKIT.d(tag(), "localEncryptRecordIds add: ${it.richNote.localId}")
            }
            transformer.convertEncryptedRichNoteToRecordFrom(it)
        } ?: emptyList()
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(tag(), "onPagingBackupStart:${data?.size}")
        startBackupTimestamp = System.currentTimeMillis()
    }

    override fun onPagingBackupEnd(
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ) {
        /** 监听同步过程中备份数据是否有修改方案：
         * 1、备份开始时记录当前时间：startBackupTimestamp,
         * 2、备份结束时，查询数据库中 updateTime大于 startBackupTimestamp的脏数据：changedDirtyData，
         * 3、备份结束更新数据状态时，过滤掉changedDirtyData
         * 4、加密的数据，需要从云端主表删除，因此删除的数据要区分实际删除or加密，加密的数据不要从本地删除而是更新其version
         **/
        val changedDirtyData = RichNoteRepository.queryChangedDirtyData(startBackupTimestamp)

        val decrypt = mutableListOf<Triple<Int, String, Long>>()
        val encrypt = mutableListOf<Triple<Int, String, Long>>()

        successData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                record.operatorType == Constants.OPERATOR_TYPE_DELETE
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId }?.apply {
                if (!localDecryptRecordIds.contains(this.richNote.localId)) {
                    RichNoteRepository.delete(this)
                } else {
                    decrypt.add(Triple(RichNoteMerger.ENCRYPT_CLOUD_DECRYPT, this.richNote.localId, record.sysVersion))
                }
            }
        }

        mergerHelper?.setRichNoteEncryptedChanged(changedData = decrypt)

        //单独处理，本地已经彻底删除的笔记数据第一次同步到云端的场景, 服务端返回 1104 SYS_VERSION_ISNULL
        val deletedNoteList = arrayListOf<String>()
        errorData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                record.subServerErrorCode == ERROR_1104
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId }?.apply {
                if (richNote.deleted) {
                    if (!TextUtils.isEmpty(richNote.globalId)) {
                        deletedNoteList.add(richNote.globalId!!)
                    }
                    RichNoteRepository.delete(this)
                }
            }
        }

        //处理 1200 exist 报错，云端已存在但本地数据是 new 状态, 将状态改成 modify 状态
        errorData?.filter { record ->
            record.subServerErrorCode == ERROR_CODE_1200_EXIST
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId && it.richNote.state == RichNote.STATE_NEW }?.let {
                it.richNote.state = RichNote.STATE_MODIFIED
                RichNoteRepository.updateWithNoTimestamp(it.richNote)
            }
        }

        successData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                record.operatorType != Constants.OPERATOR_TYPE_DELETE
        }?.mapNotNull { record ->
            dirtyRichNotes?.find {
                record.sysRecordId == it.richNote.globalId
                    && !deletedNoteList.contains(it.richNote.globalId)
            }?.apply {
                if (localEncryptRecordIds.contains(this.richNote.localId)) {
                    encrypt.add(Triple(RichNoteMerger.ENCRYPT_CLOUD_ENCRYPT, this.richNote.localId, record.sysVersion))
                    richNote.alarmTimePre = richNote.alarmTime
                    richNote.recycleTimePre = richNote.recycleTime
                    richNote.skinIdPre = richNote.skinId
                } else {
                    richNote.encryptSysVersion = record.sysVersion
                    richNote.state = RichNote.STATE_UNCHANGE

                    richNote.alarmTimePre = richNote.alarmTime
                    richNote.recycleTimePre = richNote.recycleTime
                    richNote.skinIdPre = richNote.skinId
                }
            }
        }?.apply {
            RichNoteRepository.updateList(this, false)
        }
        mergerHelper?.setRichNoteEncryptedChanged(changedData = encrypt)
    }
    companion object {
        private const val ERROR_1104 = 1104
    }
}