/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudKitCardController.kt
 ** Description: 处理内销 CloudKit tips
 ** Version: 1.0
 ** Date : 2022/5/23
 ** Author: Yang<PERSON><PERSON><PERSON>@Apps.Notes
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/23     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.nearme.note.MyApplication
import com.nearme.note.setting.SettingsSyncSwitchActivity
import com.nearme.note.tips.UpgradeCloudSpaceUtil
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.OperationSPUtil
import com.oplus.cloud.CloudOperationResponseData
import com.oplus.note.logger.AppLogger
import com.oplus.note.market.MarketManager
import com.oplus.note.repo.note.util.NoteFeatureUtil
import com.oplus.note.utils.SharedPreferencesUtil
import java.util.concurrent.TimeUnit
import kotlin.math.abs

class CloudKitInfoController(builder: Builder, val fragment: Fragment?) :
        HeadTipsBaseController(builder), CloudSyncCardView.ClickCardButtonListener {

    interface OnSyncSwitchChangeListener{
        fun onCKSyncSwitchChange()
    }

    companion object {

        /**卡片三种状态 隐藏、显示云同步提示、显示通知权限提示*/
        const val CARD_VIEW_STATUS_HIDE = 0
        const val CARD_VIEW_STATUS_CLOUD_TIP = 1
        const val CARD_VIEW_STATUS_NOTIFY_TIP = 2
        const val CARD_VIEW_STATUS_ALARM_TIP = 3
        const val CARD_VIEW_STATUS_SCREEN_ON_TIP = 4
        const val CARD_VIEW_STATUS_OVERLAY_TIP = 5

        /*高低版本提示*/
        const val CARD_VIEW_STATUS_DIFF_VERSION_TIP = 7
        /**
         * 云同步运营View
         */
        const val CARD_VIEW_STATUS_OPERATION_TIP = 6

        private const val TAG = "CloudKitCardController"

        /**点击忽略后，三个月后再提醒*/
        private const val SHOW_INTERVAL = 1000 * 60 * 60 * 24 * 90L
        private const val IGNORE_TIME_KEY = "ignore_time_key"

        /**
         * 30天
         */
        private const val OPERATION_SHOW_INTERVAL = 30L

        private const val STATUS_IGNORE = 1
        private const val STATUS_OPEN_SWITCH = 2

        private var mShouldHideCard: MutableLiveData<Int>? = null

        private var mSyncEnable = true

        var isNotifyIgnore = false
        var isAlarmIgnore = false
        var isScreenOnIgnore = false
        var isOverlayIgnore = false
        var isDiffIgnore = false


        fun resetPermissionGuideState() {
            isNotifyIgnore = false
            isAlarmIgnore = false
            isScreenOnIgnore = false
            isOverlayIgnore = false
            isDiffIgnore = false
        }
    }

    private var mCardViewStatus: MutableLiveData<Int>? = null

    private var data: CloudOperationResponseData? = null
    private val mCloudSyncCardView: CloudSyncCardView by lazy {
        CloudSyncCardView(mRecyclerView?.context!!).apply {
            setOnClickButtonListener(this@CloudKitInfoController)
        }
    }

    private var mSyncSwitchChangeListener: OnSyncSwitchChangeListener? = null

    init {
        //笔记或待办列表，一个隐藏后，另一个也要隐藏
        fragment?.apply {
            if (mShouldHideCard == null) {
                mShouldHideCard = MutableLiveData<Int>(0)
            }
            mShouldHideCard?.observe(fragment.viewLifecycleOwner, object : Observer<Int> {
                override fun onChanged(it: Int) {
                    AppLogger.BASIC.d(TAG, "mShouldHideCard observe $it mCardViewStatus is null? ${mCardViewStatus == null}")
                    when (it) {
                        STATUS_IGNORE, STATUS_OPEN_SWITCH -> {
                            mCardViewStatus?.value = CARD_VIEW_STATUS_HIDE
                        }
                    }
                }
            })
            if (mCardViewStatus == null) {
                mCardViewStatus = MutableLiveData<Int>(CARD_VIEW_STATUS_HIDE)
            }
            mCardViewStatus?.observe(fragment.viewLifecycleOwner) {
                when (it) {
                    CARD_VIEW_STATUS_HIDE -> resetHeadTipsView()
                    CARD_VIEW_STATUS_CLOUD_TIP -> showHeadTipsView(true)
                    CARD_VIEW_STATUS_NOTIFY_TIP -> showNotifyTipsView()
                    CARD_VIEW_STATUS_ALARM_TIP -> showAlarmTipsView()
                    CARD_VIEW_STATUS_SCREEN_ON_TIP -> showScreenOnTipsView()
                    CARD_VIEW_STATUS_OVERLAY_TIP -> showOverlayTipsView()
                    CARD_VIEW_STATUS_OPERATION_TIP -> showOperationTipsView()
                    CARD_VIEW_STATUS_DIFF_VERSION_TIP -> showDiffVersionTipsView()
                }
            }
        }
    }

    fun isDiffTipsShowing(): Boolean {
        AppLogger.BASIC.e(
            TAG,
            "isCurrentDiffTips:${mCloudSyncCardView.isCurrentDiffTips()}  isHeadTipsShowing${isHeadTipsShowing()}"
        )
        return mCloudSyncCardView.isCurrentDiffTips() && isHeadTipsShowing()
    }

    private fun showDiffVersionTipsView() {
        AppLogger.BASIC.e(TAG, "isDiffIgnore:$isDiffIgnore")
        if (!isDiffIgnore) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_DIFF_VERSION_TIP)
            super.showHeadTipsView(false)
        }
    }

    private fun showOperationTipsView() {
        data?.dataList?.first()?.let {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_OPERATION_TIP, data)
            super.showHeadTipsView(false)
        }
    }

    fun updateCardStatus(data: CloudOperationResponseData?, realShow: ((show: Boolean) -> Unit)? = null) {
        var showSuccess = false
        data?.dataList?.first()?.let {
            if (showOperationViewIfNeed(it)) {
                val isSameData = data.isSame(this.data)
                val currentCardType = mCardViewStatus?.value == CARD_VIEW_STATUS_OPERATION_TIP
                if (!isSameData || !currentCardType) {
                    AppLogger.BASIC.d(TAG, "real show operation data")
                    this.data = data
                    mCardViewStatus?.postValue(CARD_VIEW_STATUS_OPERATION_TIP)
                }
                showSuccess = true
                realShow?.invoke(true)
            }
        }
        if (!showSuccess) {
            realShow?.invoke(false)
        }
    }

    /**
     * 当前展示的卡片
     */
    fun getCardStatus(): Int? {
        return mCardViewStatus?.value
    }

    /**
     * @return 是否显示了卡片
     * */
    fun updateCardStatus(cardStatus: Int): Boolean {
        when (cardStatus) {
            CARD_VIEW_STATUS_DIFF_VERSION_TIP -> {
                val isIgnoreDiffVersion = NoteFeatureUtil.getIsIgnoreDiffVersion()
                AppLogger.BASIC.d(TAG, "isIgnoreDiffVersion  :$isIgnoreDiffVersion")
                if (!isIgnoreDiffVersion) {
                    mCardViewStatus?.postValue(CARD_VIEW_STATUS_DIFF_VERSION_TIP)
                    return true
                }
                return false
            }
            CARD_VIEW_STATUS_CLOUD_TIP -> {
                val shouldShowCloudSyncTip = shouldShowCloudSyncTip()
                AppLogger.BASIC.d(TAG, "updateCardStatus: cloud $cardStatus,shouldShowHeader tip :$shouldShowCloudSyncTip")
                if (shouldShowCloudSyncTip) {
                    mCardViewStatus?.postValue(CARD_VIEW_STATUS_CLOUD_TIP)
                    return true
                }
                return false
            }
            else -> {
                AppLogger.BASIC.d(TAG, "updateCardStatus:$cardStatus")
                mCardViewStatus?.postValue(cardStatus)
                return true
            }
        }
    }

    fun updateSyncEnable(enable: Boolean) {
        mSyncEnable = enable
    }

    fun setSyncSwitchChangeListener(listener: OnSyncSwitchChangeListener?) {
        mSyncSwitchChangeListener = listener
    }

    fun getHeadTipsLayout(): View {
        return mCloudSyncCardView
    }

    override fun showHeadTipsView(isCloudTip: Boolean) {
        if (shouldShowCloudSyncTip()) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_CLOUD_TIP)
            super.showHeadTipsView(true)
        } else {
            resetHeadTipsView()
        }
    }

    /**
     * 显示通知权限开启提示
     */
    private fun showNotifyTipsView() {
        AppLogger.BASIC.e(TAG, "isNotifyIgnore:$isNotifyIgnore")
        if (!isNotifyIgnore) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_NOTIFY_TIP)
            super.showHeadTipsView(false)
        }
    }

    private fun showAlarmTipsView() {
        AppLogger.BASIC.e(TAG, "isAlarmIgnore:$isAlarmIgnore")
        if (!isAlarmIgnore) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_ALARM_TIP)
            super.showHeadTipsView(false)
        }
    }

    private fun showScreenOnTipsView() {
        AppLogger.BASIC.e(TAG, "isScreenOnIgnore:$isScreenOnIgnore")
        if (!isScreenOnIgnore) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_SCREEN_ON_TIP)
            super.showHeadTipsView(false)
        }
    }

    /**
     * 悬浮窗权限
     */
    private fun showOverlayTipsView() {
        AppLogger.BASIC.e(TAG, "isOverylayIgnore:$isOverlayIgnore")
        if (!isOverlayIgnore) {
            mCloudSyncCardView.checkIfViewInflate(CARD_VIEW_STATUS_OVERLAY_TIP)
            super.showHeadTipsView(false)
        }
    }


    /**切换到编辑模式，卡片不能点击*/
    fun setSyncGuideViewState(enable: Boolean) {
        mCloudSyncCardView.updateButtonEnable(enable)
    }

    override fun onClickIgnore(isCloudSync: Boolean, isDiffVersion: Boolean) {
        AppLogger.BASIC.d(
            TAG,
            "onClickIgnore isCloudSync=$isCloudSync mShouldHideCard is null? ${mShouldHideCard == null}"
        )
        mShouldHideCard?.value = STATUS_IGNORE
        if (isCloudSync) {
            SharedPreferencesUtil.getInstance().putLong(
                MyApplication.appContext,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                IGNORE_TIME_KEY,
                System.currentTimeMillis()
            )
        } else if (isDiffVersion) {
            isDiffIgnore = true
            if (isDiffVersion) {
                SharedPreferencesUtil.getInstance().putBoolean(
                    MyApplication.appContext, SharedPreferencesUtil.DIFF_VERSION_TIPS,
                    SharedPreferencesUtil.IS_DIFF_VERSION_IGNORE, true
                )
            }
        } else {
            fragment?.context?.let {
                if (!CheckNextAlarmUtils.getNotificationsEnabled(it)) {
                    isNotifyIgnore = true
                }
                if (!CommonPermissionUtils.getScheduleAlarmEnabled(it)) {
                    isAlarmIgnore = true
                }
                if (!CommonPermissionUtils.getScreenOnEnabled(it)) {
                    isScreenOnIgnore = true
                }
                if (!CommonPermissionUtils.getOverlayEnabled(it)) {
                    isOverlayIgnore = true
                }
            }
        }
    }

    override fun onOperationSetting() {
        super.onOperationSetting()
        //运营位按钮点击后就关闭运营位
        mShouldHideCard?.value = STATUS_OPEN_SWITCH
        fragment?.activity?.let {
            SettingsSyncSwitchActivity.start(it)
        }
    }

    override fun onOperationClick() {
        mCloudSyncCardView.showOpenSwitchDialog()
    }

    override fun onOperationIgnore(data: CloudOperationResponseData.ConfigData) {
        super.onOperationIgnore(data)
        mShouldHideCard?.value = STATUS_IGNORE
        saveOperationIgnoreAction(data)
    }

    /**
     * 记录ignore 的时间、id、有效期
     */
    private fun saveOperationIgnoreAction(data: CloudOperationResponseData.ConfigData) {
        val ignoreTime = System.currentTimeMillis()
        val validTime = data.endTime
        OperationSPUtil.saveIgnoreTime(data.remindConfigId, ignoreTime, validTime)
    }

    /**
     * 判断运营信息是否需要显示
     */
    private fun showOperationViewIfNeed(data: CloudOperationResponseData.ConfigData): Boolean {
        val currentTimeMillis = System.currentTimeMillis()
        if (currentTimeMillis in data.startTime..data.endTime) {
            /*是否点击过忽略，且在30天内***/
            val onceIgnore = OperationSPUtil.isIgnoreAction(data.remindConfigId)
            val ignoreActionTime = OperationSPUtil.getIgnoreActionTime(data.remindConfigId)
            val inThirtyDays = abs(currentTimeMillis - ignoreActionTime) <= TimeUnit.DAYS.toMillis(
                OPERATION_SHOW_INTERVAL
            )
            AppLogger.BASIC.d(TAG, "onceIgnore:$onceIgnore,inThirtyDays:$inThirtyDays")
            return !(onceIgnore && inThirtyDays)
        }
        return false
    }


    override fun onOperationPayUpgrade() {
        super.onOperationPayUpgrade()
        mShouldHideCard?.value = STATUS_OPEN_SWITCH
        fragment?.activity.let {
            UpgradeCloudSpaceUtil.instance().doUpgradeCloudSpace(it as AppCompatActivity)
        }
    }

    override fun onClickUpdate() {
        hideHeaderViewNoteTips()
        //跳转到商店
        MarketManager.checkJumpToDownloadNote(MyApplication.appContext)
    }

    override fun onClickAndOpenSwitch() {
        mShouldHideCard?.value = STATUS_OPEN_SWITCH
        mSyncSwitchChangeListener?.onCKSyncSwitchChange()
    }

    /**点击忽略后，云同步提醒三个月后再提醒*/
    private fun shouldShowCloudSyncTip(): Boolean {
        val lastTime = SharedPreferencesUtil.getInstance().getLong(
                MyApplication.appContext,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME, IGNORE_TIME_KEY, -1
        )
        return (lastTime < 0) || ((System.currentTimeMillis() - lastTime) > SHOW_INTERVAL)
    }

    fun clearIgnoreTime() {
        SharedPreferencesUtil.getInstance().putLong(
                MyApplication.appContext,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                IGNORE_TIME_KEY,
                -1
        )
    }

    fun getOperationData(): CloudOperationResponseData? {
        return data
    }

    fun onDestroy() {
        mCloudSyncCardView.onDestory()
        mShouldHideCard = null
        mCardViewStatus = null
        data = null
    }
}