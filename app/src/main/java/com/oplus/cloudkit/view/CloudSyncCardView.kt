/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudSyncTpisView.kt
 ** Description: 笔记、待办列表顶部云同步提示的view
 ** Version: 1.0
 ** Date : 2022/5/22
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/22     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.Manifest
import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.animation.Animation
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.coui.appcompat.tips.def.COUIDefaultTopTipsView
import com.coui.appcompat.tips.def.IDefaultTopTips
import com.nearme.note.DialogFactory
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.main.note.NoteListViewModel
import com.nearme.note.util.CheckNextAlarmUtils.initNotificationPermission
import com.nearme.note.util.CheckNextAlarmUtils.toNotificationSetting
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils.isSupportNewVerificationSDK
import com.nearme.note.util.MbaUtils
import com.nearme.note.util.MbaUtils.showMbaCloudDialog
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.StatisticsUtils
import com.oplus.note.utils.isPackageDisabled
import com.oplus.cloud.CloudOperationResponseData
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_ALARM_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_CLOUD_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_DIFF_VERSION_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_NOTIFY_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_OPERATION_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_OVERLAY_TIP
import com.oplus.cloudkit.view.CloudKitInfoController.Companion.CARD_VIEW_STATUS_SCREEN_ON_TIP
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.market.MarketManager
import com.oplus.note.osdk.proxy.OplusBuildProxy
import com.oplus.note.repo.note.util.NoteFeatureUtil

class CloudSyncCardView : COUIDefaultTopTips {

    companion object {
        private const val TAG = "CloudSyncCardView"
        private const val CLOUD_BUTTON_TYPE_LEFT = "0"
        private const val CLOUD_BUTTON_TYPE_RIGHT = "1"
        //255 * 0.3
        private const val ALPHA_30 = 76
    }

    interface ClickCardButtonListener {
        //点击忽略按钮
        fun onClickIgnore(isCloudSync: Boolean, isDiffVersion: Boolean = false)
        //点击开启按钮并且打开了云同步
        fun onClickAndOpenSwitch()

        fun onOperationIgnore(data: CloudOperationResponseData.ConfigData) {
            //do nothing
        }

        fun onOperationClick() {
            //do nothing
        }
        fun onOperationSetting() {
            //do nothing
        }

        fun onOperationPayUpgrade() {
            //do nothing
        }

        //点击更新按钮
        fun onClickUpdate()
    }

    private var mClickCardButtonListener: ClickCardButtonListener? = null
    private var mIgnore: TextView? = null
    private var mAction: TextView? = null
    private var dialogFactory: DialogFactory? = null
    private var dialogClickListener: DialogFactory.DialogOnClickListener? = null
    private var defaultTopTipsView: COUIDefaultTopTipsView? = null
    private var currentTipsIsDiff: Boolean = false

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    override fun generateView(): IDefaultTopTips {
        val defaultTopTipsView = COUIDefaultTopTipsView(context)
        mAction = defaultTopTipsView.findViewById(R.id.action)
        mIgnore = defaultTopTipsView.findViewById(R.id.ignore)
        mIgnore?.text = resources.getString(R.string.ignore)
        //注意此处不要指定LayoutParams，在具体使用场景处会有指定，避免出现类型不匹配的异常
        contentView = defaultTopTipsView
        this.defaultTopTipsView = defaultTopTipsView
        return defaultTopTipsView
    }

    fun checkIfViewInflate(type: Int) {
        setButtonColor()
        val currentFolderHasHigherOnlyFirst = NoteFeatureUtil.getCurrentFolderHasHigherOnlyFirst()
        val isIgnoreDiffVersion = NoteFeatureUtil.getIsIgnoreDiffVersion()
        AppLogger.BASIC.d(
            TAG,
            "currentFolderHasHigherOnlyFirst$currentFolderHasHigherOnlyFirst isIgnoreDiffVersion:$isIgnoreDiffVersion"
        )
        if (currentFolderHasHigherOnlyFirst && !isIgnoreDiffVersion) {
            showDiffInfo()
            return
        }
        when (type) {
            CARD_VIEW_STATUS_CLOUD_TIP -> showCloudSyncInfo(true)

            CARD_VIEW_STATUS_DIFF_VERSION_TIP -> showDiffInfo()

            CARD_VIEW_STATUS_NOTIFY_TIP -> showNotifyInfo()

            CARD_VIEW_STATUS_ALARM_TIP -> showScheduleAlarmInfo()

            CARD_VIEW_STATUS_SCREEN_ON_TIP -> showScreenOnInfo()

            CARD_VIEW_STATUS_OVERLAY_TIP -> showOverlayInfo()
        }
    }

    fun checkIfViewInflate(type: Int, data: CloudOperationResponseData?) {
        setButtonColor()
        when (type) {
            CARD_VIEW_STATUS_OPERATION_TIP -> showOperationData(data)
        }
    }

    private fun showOperationData(data: CloudOperationResponseData?) {
        data?.dataList?.first()?.let { configData ->
            StatisticsUtils.operationCardExposure(configData.remindConfigId)
            setTipsText(configData.content)
            setStartIcon(ContextCompat.getDrawable(context, R.drawable.icon_cloud))
            configData.buttons?.forEach { buttonInfo ->
                if (buttonInfo.index == 0) {
                    mIgnore?.text = buttonInfo.content
                    mIgnore?.setOnClickListener {
                        if (buttonInfo.linkInfo.linkType == CloudOperationResponseData.ConfigData.LinkType.NATIVE) {
                            handleButtonAction(configData, buttonInfo)
                        }
                    }
                }
                if (buttonInfo.index == 1) {
                    mAction?.text = buttonInfo.content
                    mAction?.setOnClickListener {
                        if (buttonInfo.linkInfo.linkType == CloudOperationResponseData.ConfigData.LinkType.NATIVE) {
                            if (configData.remindCategory == CloudOperationResponseData.ConfigData.RemindCategory.PAY) {
                                if (buttonInfo.getButtonAction() == CloudOperationResponseData.ButtonAction.PAY_UPGRADE) {
                                    mClickCardButtonListener?.onOperationPayUpgrade()
                                    StatisticsUtils.operationCardClick(
                                        configData.remindConfigId,
                                        buttonInfo.content,
                                        buttonInfo.index.toString(),
                                        configData.content
                                    )
                                }
                            } else {
                                handleButtonAction(configData, buttonInfo)
                            }
                        }
                    }
                }
            }
        }
    }

    private fun handleButtonAction(
        data: CloudOperationResponseData.ConfigData,
        buttonInfo: CloudOperationResponseData.ConfigData.ButtonInfo
    ) {
        buttonInfo.getButtonAction()?.let {
            StatisticsUtils.operationCardClick(
                data.remindConfigId,
                buttonInfo.content,
                buttonInfo.index.toString(),
                data.content
            )
        }
        when (buttonInfo.getButtonAction()) {
            CloudOperationResponseData.ButtonAction.IGNORE -> mClickCardButtonListener?.onOperationIgnore(data)
            CloudOperationResponseData.ButtonAction.PAGE_CLOUD_SETTING -> mClickCardButtonListener?.onOperationSetting()
            CloudOperationResponseData.ButtonAction.OPEN_CLOUD_SYNC -> mClickCardButtonListener?.onOperationClick()
            CloudOperationResponseData.ButtonAction.PAY_UPGRADE -> mClickCardButtonListener?.onOperationPayUpgrade()
            null -> {}
        }
    }
    override fun setTipsText(tipsContent: CharSequence) {
        super.setTipsText(tipsContent)
        // 检查 tipsContent 是否为空或包含特定字符串
        if (tipsContent == resources.getString(R.string.update_note_card_tips)) {
            AppLogger.BASIC.d(TAG, "setTipsText  currentTipsIsDiff")
            currentTipsIsDiff = true
        }else{
            currentTipsIsDiff = false
        }
    }

    fun isCurrentDiffTips(): Boolean {
        return currentTipsIsDiff
    }


    private fun showDiffInfo() {
        AppLogger.BASIC.d(TAG, "showDiffInfo  mIgnore is null? ${mIgnore == null}")
        val tipsContent = resources.getString(R.string.update_note_card_tips)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.icon_diff_version))
        if (MarketManager.isNoteAppCanUpgrade(context)) {
            //更新可用
            mAction?.text = resources.getString(R.string.update_note_upgrade)
            mAction?.setOnClickListener {
                AppLogger.BASIC.d(TAG, "showDiffInfo mAction click")
                mClickCardButtonListener?.onClickUpdate()
                //点击升级按钮埋点
                StatisticsUtils.addClickUpgradeNoteButtonPoint(StatisticsUtils.KEY_SHOW_FROM_LIST)
            }
            mIgnore?.text = resources.getString(R.string.ignore)
            mIgnore?.setOnClickListener {
                mClickCardButtonListener?.onClickIgnore(false, true)
            }
            //显示升级按钮埋点
            StatisticsUtils.addShowUpgradeNoteButtonPoint(StatisticsUtils.KEY_SHOW_FROM_LIST)
        } else {
            //更新不可用
            mAction?.text = resources.getString(R.string.got_it_button_text)
            mAction?.setOnClickListener {
                mClickCardButtonListener?.onClickIgnore(false, true)
            }
            mIgnore?.text = ""
            mIgnore?.setOnClickListener {}
        }
        //高低版本兼容-出现不兼容提示tips埋点
        StatisticsUtils.addShowUpgradeNoteTipPoint(StatisticsUtils.KEY_SHOW_FROM_LIST)
    }

    private fun showCloudSyncInfo(isCloudSync: Boolean) {
        AppLogger.BASIC.d(TAG, "showCloudSyncInfo  mIgnore is null? ${mIgnore == null}")
        val tipsContent = resources.getString(R.string.open_cloud_sync)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.icon_cloud))
        mAction?.text = resources.getString(R.string.permission_open)
        mIgnore?.setOnClickListener {
            AppLogger.BASIC.d(TAG, "mIgnore click mClickCardButtonListener is null=${mClickCardButtonListener == null}")
            mClickCardButtonListener?.onClickIgnore(isCloudSync)
        }
        mAction?.setOnClickListener {
            if (dialogFactory == null) {
                initDialogFactory()
            }
            showOpenSwitchDialog()
        }
    }

    fun showOpenSwitchDialog() {
        if (!PrivacyPolicyHelper.isAgreeUserNotice(context)) {
            val dialogFactory = DialogFactory(context as Activity, null)
            dialogFactory.checkDeclareRequestDialog(DialogFactory.GET_NETWORK_FOR_SYNC)
            return
        }
        if (isPackageDisabled(MbaUtils.PACKAGER_CLOUD, context)) {
            showMbaCloudDialog(context)
        } else {
            dialogFactory?.showDialog(DialogFactory.DIALOG_TYPE_START_CLOUD_DATA_MERGE, null)
            AppLogger.CLOUDKIT.d(TAG, "main card show showOpenSyncSwitchDialog")
        }
    }
    private fun initDialogFactory() {
        dialogClickListener = object : DialogFactory.DialogOnClickListener {
            override fun onDialogClickButton(type: Int, index: Int) {
            }

            override fun onDialogClickPositive(type: Int) {
                AppLogger.BASIC.d(TAG, "onDialogClickPositive")
                SyncSwitchStateRepository.showOpenSyncSwitchDialog(context as FragmentActivity,
                    isSupportNewVerificationSDK,
                    object : SyncSwitchStateRepository.ChangeSyncSwitchResultListener {
                        override fun changeSyncSwitchResult(
                            isSuccess: Boolean,
                            errorMessage: String?
                        ) {
                            AppLogger.CLOUDKIT.d(TAG, "main card open switch only wifi success $isSuccess")
                            if (isSuccess) {
                                // 若开启成功，立即触发一次同步
                                NoteListHelper.startSynchronizeByCloudkit(true)
                                mClickCardButtonListener?.onClickAndOpenSwitch()
                            }
                        }

                        override fun noSupportCloudKitSwitch() {
                            NoteSyncProcess.startCloudSettingActivity(context)
                        }
                    })
            }

            override fun onDialogClickNegative(type: Int) {
            }

            override fun onDialogDismiss(type: Int) {
            }
        }
        dialogFactory = DialogFactory(context as Activity, dialogClickListener)
    }

    private fun showNotifyInfo() {
        val tipsContent = resources.getString(R.string.notification_dialog_msg)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.alarm_icon_new))
        if (!(context as Activity).shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
            mAction?.text = resources.getString(com.oplus.note.baseres.R.string.setting)
        } else {
            mAction?.text = resources.getString(R.string.permission_open)
        }
        mIgnore?.setOnClickListener {
            mClickCardButtonListener?.onClickIgnore(false)
        }
        mAction?.setOnClickListener {
            if (!(context as Activity).shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                toNotificationSetting(context as Activity, CommonPermissionUtils.TIPS_PERMISSIONS_NOTIFY_CODE)
            } else {
                initNotificationPermission(context as Activity)
            }
        }
        NoteListViewModel.isShowPermissionsTips.postValue(true)
    }

    /**
     * 精确闹钟view
     */
    private fun showScheduleAlarmInfo() {
        val tipsContent = resources.getString(R.string.schedule_alarm_dialog_msg)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.alarm_icon_new))
        mAction?.text = resources.getString(R.string.permission_open)
        mIgnore?.setOnClickListener {
            mClickCardButtonListener?.onClickIgnore(false)
        }
        mAction?.setOnClickListener {
            CommonPermissionUtils.toScheduleAlarmSetting(context as Activity,
                CommonPermissionUtils.TIPS_PERMISSIONS_ALARM_CODE)
        }
        NoteListViewModel.isShowPermissionsTips.postValue(true)
    }

    /**
     * 屏幕唤醒view
     */
    private fun showScreenOnInfo() {
        val tipsContent = resources.getString(com.oplus.note.baseres.R.string.screen_on_dialog_msg_new)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.screen_on_icon))
        mAction?.text = resources.getString(R.string.permission_open)
        mIgnore?.setOnClickListener {
            mClickCardButtonListener?.onClickIgnore(false)
        }
        mAction?.setOnClickListener {
            CommonPermissionUtils.toScreenOnSetting(context as Activity,
                CommonPermissionUtils.TIPS_PERMISSIONS_SCREEN_ON_CODE)
        }
        NoteListViewModel.isShowPermissionsTips.postValue(true)
    }

    /**
     * 悬浮窗权限
     */
    private fun showOverlayInfo() {
        val tipsContent =
            resources.getString(com.oplus.note.baseres.R.string.permission_floating_window_des)
        setTipsText(tipsContent)
        setStartIcon(ContextCompat.getDrawable(context, R.drawable.overlay_icon))
        mAction?.text = resources.getString(R.string.permission_open)
        mIgnore?.setOnClickListener {
            mClickCardButtonListener?.onClickIgnore(false)
        }
        mAction?.setOnClickListener {
            CommonPermissionUtils.toOverlaySetting(
                context as Activity,
                CommonPermissionUtils.TIPS_PERMISSIONS_OVERLAY_CODE
            )
        }
        NoteListViewModel.isShowPermissionsTips.postValue(true)
    }

    private fun setButtonColor() {
        /** （1）OS12，关于云服务的高亮色，统一用蓝色，不随便签主题色或系统主题色改变。
         * （2）OS13，关于云服务的高亮色，统一跟随便签当前的主题色。*/
        if (OplusBuildProxy.isAboveOS130()) {
            val enable = mIgnore?.isEnabled ?: true
            var themeColor = COUIContextUtil.getAttrColor(context, com.coui.appcompat.R.attr.couiColorPrimaryText)
            if (!enable) {
                themeColor = ColorUtils.setAlphaComponent(themeColor, ALPHA_30)
            }
            mIgnore?.setTextColor(themeColor)
            mAction?.setTextColor(themeColor)
        }
    }

    fun updateButtonEnable(enable: Boolean) {
        mIgnore?.isEnabled = enable
        mAction?.isEnabled = enable
        setButtonColor()
    }

    override fun setAnimation(animation: Animation?) {
        //不执行动画
    }

    fun setOnClickButtonListener(listener: ClickCardButtonListener?) {
        mClickCardButtonListener = listener
    }

    fun onDestory() {
        dialogFactory?.onDestory()
        dialogFactory = null
    }

    fun changeTextTypeImpl() {
        AppLogger.BASIC.d(TAG, "changeTextTypeImpl")
        this.defaultTopTipsView?.changeType(COUIDefaultTopTipsView.TEXT_BTN_TYPE)
    }
}