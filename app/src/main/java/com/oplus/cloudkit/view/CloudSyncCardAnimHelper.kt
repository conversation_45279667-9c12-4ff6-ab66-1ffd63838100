/*********************************************************************************
 ** Copyright (C), 2020-2030, Oplus, All rights reserved.
 **
 ** File: - CardViewAnimHelper
 ** Description: CardView show and hide anim helper class
 **
 ** Version: 1.0
 ** Date: 2025/4/29
 ** Author: W9099175
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** W9099175              2025/4/29        1.0      Create this module
 ********************************************************************************/

package com.oplus.cloudkit.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.animation.COUISpringInterpolator
import com.oplus.note.logger.AppLogger

object CloudSyncCardAnimHelper {

    private const val TAG = "CloudSyncCardAnimHelper"
    private const val ANIM_SHOW_DURATION = 650L
    private const val ANIM_SHOW_SPRING_RESPONSE = 0.25
    private const val SPRING_BOUNCE = 0.0

    private const val ANIM_HIDE_DURATION = 850L
    private const val ANIM_HIDE_SPRING_RESPONSE = 0.4

    @JvmStatic
    fun showAnim(view: View, bottomMargin: Int, animEnd: Runnable?) {
        showAnim(view, bottomMargin, animEnd?.let { { it.run() } })
    }

    @JvmStatic
    @JvmOverloads
    fun showAnim(view: View, bottomMargin: Int = 0, animEnd: (() -> Unit)? = null) {
        // 测量 view 的高度
        view.measure(
            View.MeasureSpec.makeMeasureSpec(view.width, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        AppLogger.BASIC.d(TAG, "showAnim, height: ${view.measuredHeight}")
        val totalHeight = view.measuredHeight + bottomMargin
        // 动画
        val animator = ValueAnimator.ofInt(0, totalHeight)
        animator.duration = ANIM_SHOW_DURATION
        animator.interpolator = COUISpringInterpolator(ANIM_SHOW_SPRING_RESPONSE, SPRING_BOUNCE)

        animator.addUpdateListener { valueAnimator ->
            val animatedValue = valueAnimator.animatedValue as Int
            val layoutParams = view.layoutParams
            layoutParams.height = animatedValue
            view.layoutParams = layoutParams
        }

        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                animEnd?.invoke()
            }

            override fun onAnimationCancel(animation: Animator) {
                val layoutParams = view.layoutParams
                layoutParams.height = totalHeight
                view.layoutParams = layoutParams
                animEnd?.invoke()
            }
        })

        animator.start()
    }

    @JvmStatic
    fun hideAnim(view: View, bottomMargin: Int, isNote: Boolean, animEnd: Runnable?) {
        hideAnim(view, bottomMargin, isNote, animEnd?.let { { it.run() } })
    }

    @JvmStatic
    @JvmOverloads
    fun hideAnim(
        view: View,
        bottomMargin: Int = 0,
        isNote: Boolean,
        animEnd: (() -> Unit)? = null
    ) {
        // 测量 view 的高度
        view.measure(
            View.MeasureSpec.makeMeasureSpec(view.width, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        AppLogger.BASIC.d(TAG, "hideAnim, height: ${view.measuredHeight}")
        // 动画
        val totalHeight = view.measuredHeight + bottomMargin
        val animator = ValueAnimator.ofInt(totalHeight, 0)

        animator.duration = ANIM_HIDE_DURATION
        animator.interpolator = COUISpringInterpolator(ANIM_HIDE_SPRING_RESPONSE, SPRING_BOUNCE)
        animator.addUpdateListener { valueAnimator ->
            val animatedValue = valueAnimator.animatedValue as Int
            val layoutParams = view.layoutParams as ViewGroup.MarginLayoutParams
            // 以下动效的最终效果是：height先渐渐变成0，然后才是bottomMargin渐渐变成0
            if (isNote) {
                // 笔记：整个view的总高度实际包含bottomMargin
                layoutParams.height = animatedValue
            } else {
                // 待办：整个view的总高度不包含bottomMargin，所以height中要减去
                layoutParams.height = maxOf(0, animatedValue - bottomMargin)
            }
            layoutParams.bottomMargin = minOf(bottomMargin, animatedValue)
            view.layoutParams = layoutParams
        }

        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                animEnd?.invoke()
            }

            override fun onAnimationCancel(animation: Animator) {
                animEnd?.invoke()
            }
        })

        animator.start()
    }
}