/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SubViewMovementMethod
 ** Description:
 **         v1.0:   solve onClick() called when clicking empty space
 **
 ** Version: 1.0
 ** Date: 2024/06/18
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2024/6/18   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit.view

import android.text.Layout
import android.text.Spannable
import android.text.method.LinkMovementMethod
import android.view.MotionEvent
import android.widget.TextView

class SubViewMovementMethod : LinkMovementMethod() {

    override fun onTouchEvent(
        widget: TextView,
        buffer: Spannable?,
        event: MotionEvent
    ): Boolean {
        val action = event.action

        if (action == MotionEvent.ACTION_UP || action == MotionEvent.ACTION_DOWN) {
            var x = event.x.toInt()
            var y = event.y.toInt()

            x -= widget.totalPaddingLeft
            y -= widget.totalPaddingTop

            x += widget.scrollX
            y += widget.scrollY

            val layout: Layout = widget.layout
            val line: Int = layout.getLineForVertical(y)
            val off: Int = layout.getOffsetForHorizontal(line, x.toFloat())

            if (off >= widget.text.length) {
                // Return true so click won't be triggered in the leftover empty space
                return true
            }
        }

        return super.onTouchEvent(widget, buffer, event)
    }


    companion object {

        private val sInstance: SubViewMovementMethod? by lazy {
            SubViewMovementMethod()
        }

        @JvmStatic
        fun getInstance(): SubViewMovementMethod? {
            return sInstance
        }
    }
}