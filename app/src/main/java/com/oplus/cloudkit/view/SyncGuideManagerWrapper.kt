/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudSyncGuideWrapper.kt
 ** Description: 处理云同步内外销差异代码
 ** Version: 1.0
 ** Date : 2022/5/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/23     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.app.Activity
import android.content.Context
import androidx.annotation.WorkerThread
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import com.google.gson.Gson
import com.nearme.note.MyApplication
import com.nearme.note.guide.SyncGuideManager
import com.nearme.note.logic.AccountManager
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.NoteSyncProcessProxy
import com.nearme.note.setting.SettingPresenter
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.NetworkUtils
import com.nearme.note.util.OperationSPUtil
import com.nearme.note.util.PrivacyPolicyHelper
import com.nearme.note.util.StorageUtil
import com.nearme.note.viewmodel.ToDoViewModel
import com.oplus.cloud.CloudOperationRequestData
import com.oplus.cloud.CloudOperationResponseData
import com.oplus.cloudkit.BaseSyncManager
import com.oplus.cloudkit.CloudConfigManager
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.util.GetSyncSwitchListener
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import com.oplus.note.BuildConfig

/** 内销使用新版 CloudKit (CloudKitSyncGuidManager + CloudKitInfoController) 同步；
 * 一加外销使用原来的云同步逻辑(SyncGuideManager + InfoNotifyController)*/
class SyncGuideManagerWrapper {

    companion object {
        private const val NOTE_MODE = "note"
        private const val NOTE_NODE_PATH = "note_top"
        private const val TAG = "SyncGuideManagerWrapper"
    }

    private var mSyncGuidManager: SyncGuideManager? = null
    private var mCloudKitGuideManager: CloudKitSyncGuidManager? = null

    private val mUseCloudKit = ConfigUtils.isUseCloudKit
    private var cloudConfig: CloudOperationResponseData? = null

    constructor(
        fragment: Fragment, wrapper: InfoNotifyControllerWrapper?, isNote: Boolean = false,
        todoViewModel: ToDoViewModel? = null,
        onSyncFinishCallback: CloudKitSyncGuidManager.OnSyncFinishCallback? = null
    ) {
        if (mUseCloudKit) {
            mCloudKitGuideManager =
                CloudKitSyncGuidManager(fragment, wrapper?.getCloudKitInfoController(), onSyncFinishCallback)
        } else {
            mSyncGuidManager = if (todoViewModel != null) {
                SyncGuideManager(
                    fragment.context,
                    wrapper?.getInfoNotifyController(),
                    todoViewModel
                )
            } else {
                SyncGuideManager(fragment.context, wrapper?.getInfoNotifyController(), isNote)
            }
        }
    }
    fun isShowingDiffVersion(): Boolean? {
        return  mCloudKitGuideManager?.isDiffTipsShowing()
    }
    /**
     * 封装 CloudKit 和非 CloudKit 下首次获取云同步开关的逻辑
     * 注意两种情况下，回调的 state 定义不一样；CloudKit 下参考 SwitchState; 非CloudKit下 参考 NoteSyncProcess
     * */
    fun firstQueryNoteSyncCloudStateCompact(
        activity: Activity?,
        lifecycleOwner: LifecycleOwner,
        callback: NoteSyncProcess.CloudSyncStateCallback?
    ): NoteSyncProcessProxy? {
        return if (mUseCloudKit) {
            mCloudKitGuideManager?.firstQueryCloudKitSyncState(activity, lifecycleOwner, callback)
            null
        } else {
            NoteSyncProcessProxy(NoteSyncProcess(activity, callback)).apply {
                checkSyncSwitchStateTask()
                registerStateReceiver()
            }
        }
    }

    /**
     * 封装 CloudKit 和非 CloudKit 下云同步开关状态变化后，重新获取云同步开关的逻辑
     * 注意两种情况下，回调的 state 定义不一样；CloudKit 下参考 SwitchState; 非CloudKit下 参考 NoteSyncProcess
     * */
    fun queryNoteSyncCloudStateCompact(
        context: Context?,
        callback: NoteSyncProcess.CloudSyncStateCallback?
    ) {
        if (mUseCloudKit) {
            queryCloudKitSyncCloudState(context, callback)
        } else {
            SettingPresenter.queryNoteSyncCloudStateTask(context, callback)
        }
    }

    /**查询 cloudkit 下云同步开关状态*/
    fun queryCloudKitSyncCloudState(
        context: Context?,
        callback: NoteSyncProcess.CloudSyncStateCallback? = null
    ) {
        if (mUseCloudKit) {
            mCloudKitGuideManager?.queryCloudKitSyncCloudState(context, callback)
        }
    }

    fun release() {
        if (!mUseCloudKit) {
            mSyncGuidManager?.release()
        } else {
            CloudKitSyncGuidManager.mLastSyncStatus = null
        }
    }

    fun updateSyncSwitchState(settingState: Int) {
        if (!mUseCloudKit) {
            mSyncGuidManager?.updateSyncSwitchState(settingState)
        } else {
            mCloudKitGuideManager?.mSwitchState = settingState
        }
    }

    fun hideSyncGuideView() {
        if (!mUseCloudKit) {
            mSyncGuidManager?.hideSyncGuideView()
        } else {
            mCloudKitGuideManager?.hideSyncGuideView()
        }
    }

    fun getSyncSwitchState(): Int {
        if (!mUseCloudKit) {
            return mSyncGuidManager!!.syncSwitchState
        }
        return mCloudKitGuideManager!!.mSwitchState
    }

    fun syncStateCheck(activity: Activity?, isNote: Boolean) {
        if (!mUseCloudKit) {
            mSyncGuidManager?.syncStateCheck(activity, isNote)
        } else {
            //不检查数据，直接隐藏提示卡片
            mCloudKitGuideManager?.hideSyncGuideView()
        }
    }

    fun showCloudSyncTipView(hasNote: Boolean, showCallBack: ((show: Boolean) -> Unit)? = null) {
        if (!mUseCloudKit) {
            mSyncGuidManager?.showSyncGuidView(hasNote)
        } else {
            mCloudKitGuideManager?.showCloudSyncTipView(true, showCallBack)
        }
    }

    fun updateSubTitle(text: String, visibility: Int) {
        mCloudKitGuideManager?.updateSubTitle(text, visibility)
    }

    fun showNotifyGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        mCloudKitGuideManager?.showNotifyGuideView(activity, hasData, syncEnable)
    }

    fun showNotifyDiffVersionView() {
        mCloudKitGuideManager?.showDiffVersionGuideView()
    }

    fun showAlarmGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        mCloudKitGuideManager?.showAlarmGuideView(activity, hasData, syncEnable)
    }

    fun showScreenOnGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        mCloudKitGuideManager?.showScreenOnGuideView(activity, hasData, syncEnable)
    }

    fun showOverlayGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        mCloudKitGuideManager?.showOverlayGuideView(activity, hasData, syncEnable)
    }
    fun showNoteBookSyncTipView(guid: String, syncEnable: Boolean?) {
        mCloudKitGuideManager?.showNoteBookSyncTipView(guid, syncEnable)
    }

    /**
     * 拉取云控运营信息
     * 用户每次进入业务首页时拉取一次（有网络时，对有登录态用户进行拉取）
     */
    fun getCloudOperation(
        context: Context?,
        syncEnable: Boolean?,
        lifecycleScope: LifecycleCoroutineScope,
        showCallBack: ((show: Boolean) -> Unit)? = null
    ) {
        if (context == null) {
            showCallBack?.invoke(false)
            return
        }
        if (!PrivacyPolicyHelper.isAgreeUserNotice(context)) {
            showCallBack?.invoke(false)
            return
        }
        if (BuildConfig.isExport) {
            showCallBack?.invoke(false)
            return
        }
        if (!BaseSyncManager.syncInit) {
            // 调用 getOperationData 方法之前必须先init
            showCallBack?.invoke(false)
            return
        }
        if (!CloudKitGlobalStateManager.cloudEnable()) {
            showCallBack?.invoke(false)
            return
        }
        if (mCloudKitGuideManager?.getCardStatus() == CloudKitInfoController.CARD_VIEW_STATUS_OPERATION_TIP) {
            val data = mCloudKitGuideManager?.getOperationData()
            if ((syncEnable == true && data?.isSwitchType() == true)
                || (syncEnable == false && data?.isPayType() == true)) {
                showCallBack?.invoke(false)
                //显示时候，云同步开关发生变化，需要隐藏已经不正确的运营位信息
                return
            }
            //已经显示了
            showCallBack?.invoke(true)
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            val hasAvailableNetwork = NetworkUtils.isNetworkConnected(context)
            val isAccountLogin = AccountManager.isLogin(context)
            /*当天是否点击过ignore**/
            val hasClickIgnoreCurrent = OperationSPUtil.isClickIgnoreToday()
            AppLogger.BASIC.d(
                TAG, "getCloudOperation hasAvailableNetwork=$hasAvailableNetwork isLogin=$isAccountLogin " +
                        "clickIgnore=$hasClickIgnoreCurrent"
            )
            if (hasAvailableNetwork && isAccountLogin && !hasClickIgnoreCurrent) {
                getOperationData(lifecycleScope) { success ->
                    if (!success) {
                        showCallBack?.invoke(false)
                        return@getOperationData
                    }
                    lifecycleScope.launch {
                        showOperationGuideView() { showSuccess ->
                            showCallBack?.invoke(showSuccess)
                        }
                    }
                }
            } else {
                showCallBack?.invoke(false)
            }
        }
    }

    /**
     * 获取云同步运营数据，拉取条件：
     * 1）用户每次进入业务首页时拉取一次（有网络时，对有登录态用户进行拉取）
     * 2）若用户已点击过忽略，则当天不再进行文案拉取
     */
    private var loadedCloudOperation = false
    @WorkerThread
    fun getOperationData(scope: LifecycleCoroutineScope, callback: ((success: Boolean) -> Unit)? = null) {
        if (loadedCloudOperation) {
            callback?.invoke(false)
            return
        }
        loadedCloudOperation = true
        SyncSwitchStateRepository.querySwitchState(MyApplication.appContext, object : GetSyncSwitchListener {
            override fun onGetSyncSwitch(switchStateCode: Int) {
                val isAutoSync = switchStateCode != CloudKitSdkManager.CLOSE_CODE
                val isMobileDataAllow = switchStateCode == CloudKitSdkManager.OPEN_ALL_CODE
                val cloudOperationRequestData =
                    CloudOperationRequestData(isAutoSync, isMobileDataAllow, StorageUtil.getFreeDiskSpace())
                val requestData = Gson().toJson(cloudOperationRequestData)
                scope.launch(Dispatchers.IO) {
                    CloudConfigManager.getOperationData(NOTE_MODE, NOTE_NODE_PATH, requestData = requestData,
                        object : CloudConfigManager.CloudConfigResult {
                            override fun onFail() {
                                cloudConfig = null
                                callback?.invoke(false)
                            }

                            override fun success(data: CloudOperationResponseData) {
                                cloudConfig = data
                                callback?.invoke(true)
                                AppLogger.BASIC.d(TAG, "fetch operation data success $data")
                            }
                        })
                }
            }
        })
    }

    /**
     * cloud kit 下发的运营数据，展示到header view 中
     */
    private fun showOperationGuideView(
        showCallBack: ((show: Boolean) -> Unit)? = null
    ) {
        AppLogger.BASIC.d(TAG, "handle showOperationGuideView")
        if (cloudConfig == null) {
            AppLogger.BASIC.d(TAG, "handle showOperationGuideView return cloudConfig is null")
            showCallBack?.invoke(false)
            return
        }
        mCloudKitGuideManager?.showOperationGuideView(cloudConfig, showCallBack)
    }
}