/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudKitSyncGuidManager.kt
 ** Description: 处理 cloudKit 同步状态变化后的逻辑
 ** Version: 1.0
 ** Date : 2022/5/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/24     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.app.Activity
import android.content.Context
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.nearme.note.MyApplication
import com.nearme.note.db.AppDatabase
import com.nearme.note.logic.AccountManager
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.main.note.NoteListFragment
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.ToDoRepository
import com.nearme.note.util.CheckNextAlarmUtils
import com.nearme.note.util.CommonPermissionUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.NetworkUtils
import com.nearme.note.util.RSAProjectHelper
import com.nearme.note.view.helper.UiHelper
import com.oplus.cloud.CloudOperationResponseData
import com.oplus.cloudkit.CloudKitSdkManager
import com.oplus.cloudkit.SyncManager
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.util.GetSyncSwitchListener
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.utils.SingleRunner
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CloudKitSyncGuidManager(
    val fragment: Fragment,
    controller: CloudKitInfoController?,
    private val mSyncFinishCallback: OnSyncFinishCallback? = null
) : CloudKitInfoController.OnSyncSwitchChangeListener {

    interface OnSyncFinishCallback{
        /**开始同步*/
        fun onSyncing()
        /**同步结束*/
        fun onSyncFinish(syncStatus: CloudKitSyncStatus)
        /**同步结束，延迟一段时间后副标题才切回显示笔记/待办条数,这里新增一个回调 */
        fun onSyncFinishSubtitleChange()
    }

    companion object {
        private const val TAG = "CloudKitSyncGuidManager"
        var mLastSyncStatus: CloudKitSyncStatus? = null
        var editFolderGuids: List<String?> = listOf(FolderInfo.FOLDER_GUID_NO_GUID) /**在笔记本面板中修改笔记本属性*/
        var isAllSyncSwitchClosed = false
    }

    private val mCloudKitCardController: CloudKitInfoController? = controller
    private var mCallback: NoteSyncProcess.CloudSyncStateCallback? = null
    /** @link SwitchState 云同步开关状态变化 */
    var mSwitchState: Int = -1
    /**记录当前开关状态值，其实和上面mSwitchState值一样，为了不干扰另外写一个*/
    private var mCKSwitchState: Int = CloudKitSdkManager.CLOSE_CODE

    private val mSubTitleView: CloudSyncSubTitleView? by lazy<CloudSyncSubTitleView?> {
        fragment.view?.findViewById(R.id.sub_title_view)
    }

    init {
        mCloudKitCardController?.setSyncSwitchChangeListener(this)
        fragment.context?.apply {
            SyncManager.observeSyncTracking(fragment.viewLifecycleOwner) { syncStatus ->
                AppLogger.BASIC.d(TAG, "syncStatus change: $syncStatus SwitchState: $mCKSwitchState")
                if (syncStatus.isFinishStatus.not()) {
                    mSyncFinishCallback?.onSyncing()
                }
                mLastSyncStatus = syncStatus
                if (mCKSwitchState > CloudKitSdkManager.CLOSE_CODE) {
                    fragment.lifecycleScope.launch {
                        updateTipsRunner.afterPrevious {
                            updateTip(syncStatus)
                        }
                    }
                }
            }
        }
        mSubTitleView?.syncFinishCallback = mSyncFinishCallback
    }

    private val updateTipsRunner = SingleRunner()

    private suspend fun updateTip(syncStatus: CloudKitSyncStatus) {
        val ctx = fragment.context ?: return
        val syncTipData = getSyncItemData(ctx, syncStatus)
        if (fragment is NoteListFragment) {
            if (fragment.isEditMode()) {
                return
            }
            fragment.updateSyncStatus(syncStatus == CloudKitSyncStatus.SYNC_CODE_SYNCING)
            //【全部笔记】目录下，把所有可以关闭同步的笔记本开关关了，全部笔记的云同步提示不触发，其余需要触发
            if (fragment.getCurrentFolderInfo()?.guid == FolderInfo.FOLDER_GUID_ALL) {
                if (isAllSyncSwitchClosed) {
                    mSubTitleView?.switchToSubTitle()
                    return
                }
            } else {
                if (fragment.getFolderSyncState() == Constants.FOLDER_SYNC_OFF) {
                    mSubTitleView?.switchToSubTitle()
                    return
                } else {
                    val targetGuid = fragment.getCurrentFolderInfo()?.guid
                    if (!editFolderGuids.contains(targetGuid)) {
                        mSubTitleView?.switchToSubTitle()
                        return
                    }
                }
            }
        }
        mSubTitleView?.let {
            it.updateNextCloudTip(syncTipData)
        }
    }


    fun isDiffTipsShowing(): Boolean? {
       return mCloudKitCardController?.isDiffTipsShowing()
    }

    private suspend fun getSyncItemData(context: Context, syncStatus: CloudKitSyncStatus): SyncTipData {

        if (syncStatus.isFinishStatus) {
            mSyncFinishCallback?.onSyncFinish(syncStatus)
        }
        val status = when (syncStatus) {
            CloudKitSyncStatus.SYNC_CODE_SYNCING -> {
                SyncTipData(
                    R.string.data_is_syncing, null, syncStatus
                )
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS -> {
                SyncTipData(
                    R.string.data_sync_completed, context.getString(R.string.check), syncStatus
                )
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL,
            CloudKitSyncStatus.SYNC_CODE_FINISHED_INTERRUPTED,
            CloudKitSyncStatus.SYNC_CODE_FINISHED_NO_GLOBAL_STATE -> {
                syncTipDataFailedWithNetStatus(context, syncStatus)
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_SPACE_NOT_ENOUGH -> {
                if (BuildConfig.isExport) {
                    syncTipDataByCloudSpaceNotEnoughInExport(context, syncStatus)
                } else {
                    SyncTipData(
                        R.string.cloud_storage_full, context.getString(R.string.update_cloud_storage), syncStatus
                    )
                }
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_ATTACHMENT_UPLOAD_FAILED -> {
                if (BuildConfig.isExport) {
                    syncTipDataByCloudSpaceNotEnoughInExport(context, syncStatus)
                } else {
                    syncTipDataByFailed(context, syncStatus)
                }
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_STEAM_LIMIT -> {
                SyncTipData(
                    com.oplus.note.baseres.R.string.cloud_service_exception_sync_stop_new,
                    context.getString(com.oplus.note.baseres.R.string.retry_now),
                    syncStatus,
                )
            }

            CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_COLD_STANDBY -> {
                SyncTipData(
                    com.oplus.note.baseres.R.string.cloud_service_restore, null, syncStatus
                )
            }

            CloudKitSyncStatus.SYNC_CODE_FINISHED_LOCAL_SPACE_NOT_ENOUGH -> {
                val content = if (UiHelper.isDevicePad()) {
                    com.oplus.note.baseres.R.string.pad_insufficient_storage_sync_stop_new
                } else {
                    com.oplus.note.baseres.R.string.insufficient_storage_sync_stop_new
                }
                val checkText = context.getString(com.oplus.note.baseres.R.string.frer_up_storage)
                SyncTipData(content, checkText, syncStatus)
            }
        }
        return status
    }

    private fun syncTipDataFailedWithNetStatus(
        context: Context,
        syncStatus: CloudKitSyncStatus
    ): SyncTipData {
        val wifiConnected = NetworkUtils.isWifiConnected(context)
        val networkConnected = NetworkUtils.isNetworkConnected(context)
        if (mCKSwitchState == CloudKitSdkManager.OPEN_ONLY_WIFI_CODE && !wifiConnected) {
            return SyncTipData(
                com.oplus.note.baseres.R.string.no_wifi_sync_stop_new,
                context.getString(com.oplus.note.baseres.R.string.connect_wifi),
                syncStatus
            )
        } else if (mCKSwitchState == CloudKitSdkManager.OPEN_ALL_CODE && !networkConnected) {
            return SyncTipData(
                com.oplus.note.baseres.R.string.no_network_sync_stop_new,
                context.getString(com.oplus.note.baseres.R.string.net_settting),
                syncStatus
            )
        }
        return syncTipDataByFailed(context, syncStatus)
    }

    private fun syncTipDataByFailed(context: Context, syncStatus: CloudKitSyncStatus): SyncTipData {
        return SyncTipData(
            R.string.data_sync_paused, context.getString(R.string.check), syncStatus
        )
    }

    private suspend fun syncTipDataByCloudSpaceNotEnoughInExport(
        context: Context,
        syncStatus: CloudKitSyncStatus
    ) = withContext(Dispatchers.IO) {
        val count = RichNoteRepository.countOfRichNoteContainsAttachments()
        val checkText = context.getString(com.oplus.note.baseres.R.string.data_sync_check)
        val text = context.resources.getQuantityString(com.oplus.note.baseres.R.plurals.data_sync_attach_fail, count, count, checkText)
        return@withContext SyncTipData(
            textResId = 0,
            checkText = checkText,
            syncStatus = syncStatus,
            text = text,
        )
    }

    fun firstQueryCloudKitSyncState(
        activity: Activity?,
        lifecycleOwner: LifecycleOwner,
        callback: NoteSyncProcess.CloudSyncStateCallback?
    ) {
        mCallback = callback
        if (activity == null) {
            return
        }
        SyncSwitchStateRepository.querySwitchState(activity).observe(lifecycleOwner) { switchState ->
            mCKSwitchState = switchState
            mCallback?.refreshViewState(switchState)
            if (switchState == CloudKitSdkManager.CLOSE_CODE) {
                mSubTitleView?.switchToSubTitle()
            } else {
                mCloudKitCardController?.clearIgnoreTime()
            }
        }
    }

    /**查询 cloudkit 下云同步开关状态*/
    fun queryCloudKitSyncCloudState(
        context: Context?,
        callback: NoteSyncProcess.CloudSyncStateCallback? = null
    ) {
        context?.apply {
            SyncSwitchStateRepository.querySwitchState(context, object : GetSyncSwitchListener {
                override fun onGetSyncSwitch(switchStateCode: Int) {
                    if (callback != null) {
                        callback.refreshViewState(switchStateCode)
                    } else {
                        mCallback?.refreshViewState(switchStateCode)
                    }
                }
            })
        }
    }

    fun hideSyncGuideView() {
        mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_HIDE)
    }

    fun showCloudSyncTipView(isShow: Boolean, showCallBack: ((show: Boolean) -> Unit)? = null) {
        fragment.lifecycleScope.launch(Dispatchers.Main) {
            withContext(Dispatchers.Default) {
                if ((isShow && !RSAProjectHelper.sIsRSA4AndExport)
                    || (RSAProjectHelper.sIsRSA4AndExport && isShow && AccountManager.isLogin(MyApplication.appContext))) {
                    withContext(Dispatchers.Main) {
                        val show = mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_CLOUD_TIP)
                        showCallBack?.invoke(show ?: false)
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        showCallBack?.invoke(false)
                    }
                }
            }
        }
    }

    fun updateSubTitle(text: String, visibility: Int) {
        mSubTitleView?.findViewById<TextView>(R.id.sub_title_content)
            ?.let { mSubTitleView?.updateShowCountTip(text, visibility, it) }
    }

    override fun onCKSyncSwitchChange() {
        //点击开启云同步开关后，重新查询开关状态，触发刷新
        queryCloudKitSyncCloudState(fragment.context)
    }

    fun showNotifyGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        GlobalScope.launch(Dispatchers.Main) {
            val haveNextTime = withContext(Dispatchers.IO) {
                val todo = ToDoRepository.getInstance().nextAlarm
                val richNote = RichNoteRepository.getNextAlarm(System.currentTimeMillis())
                return@withContext (todo != null || richNote != null)
            }
            AppLogger.BASIC.e(TAG,"haveNextTime:$haveNextTime syncEnable: $syncEnable")
            if (haveNextTime) {
                if (ConfigUtils.isNotAllowSyncEncryptNoteToCloud && activity != null
                    && !CheckNextAlarmUtils.getNotificationsEnabled(activity)) {
                    mCloudKitCardController?.hideHeaderViewNoteTips()
                }
                mCloudKitCardController?.updateSyncEnable(syncEnable == true)
                mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_NOTIFY_TIP)
            } else {
                hideSyncGuideView()
            }
        }
    }

    fun showDiffVersionGuideView() {
        GlobalScope.launch(Dispatchers.Main) {
            mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_DIFF_VERSION_TIP)
        }
    }

    fun showAlarmGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        GlobalScope.launch(Dispatchers.Main) {
            val haveNextTime = withContext(Dispatchers.IO) {
                val todo = ToDoRepository.getInstance().nextAlarm
                val richNote = RichNoteRepository.getNextAlarm(System.currentTimeMillis())
                return@withContext (todo != null || richNote != null)
            }
            AppLogger.BASIC.e(TAG, "showAlarmGuideView haveNextTime:$haveNextTime syncEnable: $syncEnable")
            if (haveNextTime) {
                if (ConfigUtils.isNotAllowSyncEncryptNoteToCloud && activity != null
                    && !CommonPermissionUtils.getScheduleAlarmEnabled(activity)) {
                    mCloudKitCardController?.hideHeaderViewNoteTips()
                }
                mCloudKitCardController?.updateSyncEnable(syncEnable == true)
                mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_ALARM_TIP)
            } else {
                hideSyncGuideView()
            }
        }
    }

    fun showScreenOnGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        GlobalScope.launch(Dispatchers.Main) {
            val haveNextTime = withContext(Dispatchers.IO) {
                val todo = ToDoRepository.getInstance().nextAlarm
                val richNote = RichNoteRepository.getNextAlarm(System.currentTimeMillis())
                return@withContext (todo != null || richNote != null)
            }
            AppLogger.BASIC.e(TAG, "showScreenOnGuideView haveNextTime:$haveNextTime syncEnable: $syncEnable")
            if (haveNextTime) {
                if (ConfigUtils.isNotAllowSyncEncryptNoteToCloud && activity != null
                        && !CommonPermissionUtils.getScreenOnEnabled(activity)) {
                    mCloudKitCardController?.hideHeaderViewNoteTips()
                }
                mCloudKitCardController?.updateSyncEnable(syncEnable == true)
                mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_SCREEN_ON_TIP)
            } else {
                hideSyncGuideView()
            }
        }
    }

    fun showOverlayGuideView(activity: Activity?, hasData: Boolean?, syncEnable: Boolean?) {
        fragment.lifecycleScope.launch(Dispatchers.Main) {
            val haveNextTime = withContext(Dispatchers.IO) {
                val todos = ToDoRepository.getInstance().featureAlarmRemind
                val firstOrNull = todos.firstOrNull {
                    it.forceReminder
                }
                return@withContext firstOrNull != null
            }
            AppLogger.BASIC.e(TAG, "showOverlayGuideView haveNextTime:$haveNextTime syncEnable: $syncEnable")
            if (haveNextTime) {
                if (ConfigUtils.isNotAllowSyncEncryptNoteToCloud && activity != null
                    && !CommonPermissionUtils.getOverlayEnabled(activity)
                ) {
                    mCloudKitCardController?.hideHeaderViewNoteTips()
                }
                mCloudKitCardController?.updateSyncEnable(syncEnable == true)
                mCloudKitCardController?.updateCardStatus(CloudKitInfoController.CARD_VIEW_STATUS_OVERLAY_TIP)
            } else {
                hideSyncGuideView()
            }
        }
    }

    fun showNoteBookSyncTipView(folderGuid: String, syncEnable: Boolean?) {
        val isRecentFolder = folderGuid == FolderInfo.FOLDER_GUID_RECENT_DELETE
        if ((syncEnable != true && !isRecentFolder) || folderGuid == FolderInfo.FOLDER_GUID_ALL) {
            mCloudKitCardController?.hideHeaderViewNoteTips()
            return
        }
        GlobalScope.launch(Dispatchers.Main) {
            val noteBookSyncState = withContext(Dispatchers.IO) {
                val folder = AppDatabase.getInstance().foldersDao().findByGuid(folderGuid)
                return@withContext folder?.extra?.getSyncState()
            }
            mCloudKitCardController?.showHeaderViewNoteTips(noteBookSyncState)
        }
    }

    /**
     * 运营数据的优先级低于本地云同步显示，同步开关未开启、正在同步中。都不拉取运营配置
     */
    fun showOperationGuideView(
        data: CloudOperationResponseData?,
        showCallBack: ((show: Boolean) -> Unit)? = null
    ) {
        mCloudKitCardController?.updateCardStatus(data, showCallBack)
    }

    fun getCardStatus(): Int? {
        return mCloudKitCardController?.getCardStatus()
    }

    fun getOperationData(): CloudOperationResponseData? {
        return mCloudKitCardController?.getOperationData()
    }

    /**副标题正在显示云同步状态*/
    fun subtitleShowSyncing(): Boolean? {
        return mSubTitleView?.showSyncing()
    }
}