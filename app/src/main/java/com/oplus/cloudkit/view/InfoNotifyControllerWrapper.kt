/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - InfoNotifyControllerWrapper.kt
 ** Description: 处理笔记、待办列表云同步 header 的差异逻辑
 ** Version: 1.0
 ** Date : 2022/5/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/23     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.view.View
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.COUIRecyclerView
import com.nearme.note.guide.InfoNotifyController
import com.nearme.note.util.ConfigUtils

class InfoNotifyControllerWrapper {

    /**区分使用 CloudKit 新逻辑还是原有逻辑*/
    private val mUseCloudKit = ConfigUtils.isUseCloudKit
    private var mInfoNotifyController: InfoNotifyController? = null
    private var mCloudKitInfoController: CloudKitInfoController? = null

    constructor(builder: Builder) {
        if (mUseCloudKit) {
            mCloudKitInfoController = CloudKitInfoController(
                HeadTipsBaseController.Builder()
                    .setRecyclerView(builder.mRecyclerView),
                builder.mFragment
            )
        } else {
            mInfoNotifyController = InfoNotifyController(
                HeadTipsBaseController.Builder()
                    .setRecyclerView(builder.mRecyclerView)
            )
        }
    }

    fun getInfoNotifyController(): InfoNotifyController? {
        return mInfoNotifyController
    }

    fun getCloudKitInfoController(): CloudKitInfoController? {
        return mCloudKitInfoController
    }

    fun getHeadTipsLayout(): View {
        if (!mUseCloudKit) {
            return mInfoNotifyController!!.headTipsLayout
        }
        return mCloudKitInfoController!!.getHeadTipsLayout()
    }

    fun setSyncGuideViewState(enable: Boolean, isAutoSyncState: Boolean) {
        if (!mUseCloudKit) {
            mInfoNotifyController?.setSyncGuideViewState(enable, isAutoSyncState)
        } else {
            mCloudKitInfoController?.setSyncGuideViewState(enable)
        }
    }

    fun onDestroy() {
        mCloudKitInfoController?.onDestroy()
    }

    class Builder {
        var mRecyclerView: COUIRecyclerView? = null
        var mFragment: Fragment? = null

        fun setRecyclerView(recyclerView: COUIRecyclerView?): Builder {
            mRecyclerView = recyclerView
            return this
        }

        fun setFragment(fragment: Fragment): Builder {
            mFragment = fragment
            return this
        }

        fun build(): InfoNotifyControllerWrapper {
            return InfoNotifyControllerWrapper(this)
        }
    }
}