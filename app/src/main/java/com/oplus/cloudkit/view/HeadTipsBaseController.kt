/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - HeadTipsBaseController.kt
 ** Description: 处理笔记、待办列表 header 的基类
 ** Version: 1.0
 ** Date : 2022/5/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/23     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import androidx.recyclerview.widget.COUIRecyclerView
import com.nearme.note.activity.list.TodoAdapter
import com.nearme.note.activity.richlist.NoteAdapterInterface
import com.nearme.note.activity.richlist.RichNoteListAdapter
import com.oplus.note.logger.AppLogger

open class HeadTipsBaseController {

    protected var mRecyclerView: COUIRecyclerView? = null
    private var mShowTipsState = CLOUD_TIP_HEADER_STATUS_UNDEFINED

    companion object {
        private const val TAG = "HeadTipsBaseController"
        /**云服务提示 header 的显示状态 未定义、显示、隐藏 */
        const val CLOUD_TIP_HEADER_STATUS_UNDEFINED = 0
        const val CLOUD_TIP_HEADER_STATUS_SHOW = 1
        const val CLOUD_TIP_HEADER_STATUS_HIDE = 2
    }

    protected constructor(builder: Builder) {
        mRecyclerView = builder.mRecyclerView
    }

    open fun showHeadTipsView(isCloudTip: Boolean = true) {
        AppLogger.BASIC.d(TAG, "showHeadTipsView showState: $mShowTipsState")
        if (mShowTipsState != CLOUD_TIP_HEADER_STATUS_SHOW) {
            mShowTipsState = CLOUD_TIP_HEADER_STATUS_SHOW
            updateHeadView(true)
        }
    }

    open fun isHeadTipsShowing(): Boolean {
        AppLogger.BASIC.d(TAG, "isHeadTipsShowing showState: $mShowTipsState")
        return mShowTipsState == CLOUD_TIP_HEADER_STATUS_SHOW
    }

    fun resetHeadTipsView(): Boolean {
        AppLogger.BASIC.d(TAG, "resetHeadTipsView showState: $mShowTipsState")
        if (mShowTipsState != CLOUD_TIP_HEADER_STATUS_HIDE) {
            mShowTipsState = CLOUD_TIP_HEADER_STATUS_HIDE
            updateHeadView(false)
            return true
        }
        return false
    }

    private fun updateHeadView(show: Boolean) {
        val adapter = mRecyclerView?.adapter
        if (adapter != null) {
            if (show) {
                if (adapter is NoteAdapterInterface<*>) {
                    (adapter as NoteAdapterInterface<*>).showInfoBoard()
                } else if (adapter is TodoAdapter) {
                    adapter.addCloudHeader()
                }
            } else {
                if (adapter is NoteAdapterInterface<*>) {
                    (adapter as NoteAdapterInterface<*>).hideInfoBoard()
                } else if (adapter is TodoAdapter) {
                    adapter.removeCloudHeader()
                }
            }
        }
    }

    fun hideHeaderViewNoteTips() {
        val adapter = mRecyclerView?.adapter
        if (adapter is RichNoteListAdapter) {
            adapter.hideHeaderViewNoteTips()
        }
    }

    fun showHeaderViewNoteTips(noteBookSyncState: Int?) {
        val adapter = mRecyclerView?.adapter
        if (adapter is RichNoteListAdapter) {
            adapter.showHeaderViewNoteTips(noteBookSyncState)
        }
    }

    class Builder {
        var mRecyclerView: COUIRecyclerView? = null
        fun setRecyclerView(recyclerView: COUIRecyclerView?): Builder {
            mRecyclerView = recyclerView
            return this
        }

        fun build(): HeadTipsBaseController {
            return HeadTipsBaseController(this)
        }
    }
}