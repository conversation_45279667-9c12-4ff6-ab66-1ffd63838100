/****************************************************************
 ** Copyright (C), 2010-2022, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:  - CloudSyncSubTitleView.kt
 ** Description: 笔记、待办列表页副标题 view
 *  动画参数： http://cod.adc.com/front/component/ColorOS?category=%E5%8A%A8%E7%94%BB&version=V%2012&id=5799
 ** Version: 1.0
 ** Date : 2022/5/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: -----------------------
 **  <author>    <data>       <version >     <desc>
 **  YangLinlong   2022/5/23     1.0            add file
 ****************************************************************/
package com.oplus.cloudkit.view

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.TextUtils
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.View
import android.view.animation.Animation
import android.widget.TextView
import android.widget.ViewFlipper
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.main.MainActivity
import com.nearme.note.tips.CloudSyncErrorDialog
import com.nearme.note.tips.UpgradeCloudSpaceUtil
import com.oplus.anim.EffectiveAnimationView
import com.oplus.cloudkit.CloudKitGlobalStateManager
import com.oplus.cloudkit.JudgeGlobalStateCallback
import com.oplus.cloudkit.SyncManager
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.util.CloudkitSyncUtil
import com.oplus.cloudkit.util.GetSyncSwitchListener
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.note.R
import com.oplus.note.BuildConfig
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.proxy.OplusBuildProxy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

data class SyncTipData(
    var textResId: Int,
    var checkText: String?,
    var syncStatus: CloudKitSyncStatus,
    var text: String? = null,
)

class CloudSyncSubTitleView : ViewFlipper {

    companion object {
        private const val TAG = "CloudSyncSubTitleView"
        const val SHOW_COUNT_TIP_DELAY = 6000L
    }

    private var mViewInit = false
    private var mCheckTextColor: Int = 0
    private var mNowShowingTipData: SyncTipData? = null
    private lateinit var syncTipViewFlipper: ViewFlipper
    var syncFinishCallback: CloudKitSyncGuidManager.OnSyncFinishCallback? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    private fun checkInitViews() {
        if (mViewInit) {
            return
        }
        mViewInit = true
        syncTipViewFlipper = findViewById(R.id.sync_tip_vf)
        mCheckTextColor =
            if (OplusBuildProxy.isAboveOS130()) {
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            } else {
                context.getColor(R.color.expand_doodle_more_textcolor)
            }
        val animationListener = object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation?) {
                val animationView = syncTipViewFlipper.currentView?.findViewById<EffectiveAnimationView>(R.id.icon)
                if (mNowShowingTipData != null && animationView?.isVisible == true) {
                    animationView.playAnimation()
                }
            }

            override fun onAnimationEnd(animation: Animation?) {
                if (mNowShowingTipData == null) {
                    stopAllLottieAnimation()
                }
            }

            override fun onAnimationRepeat(animation: Animation?) {

            }
        }
        inAnimation?.setAnimationListener(animationListener)
        syncTipViewFlipper.inAnimation?.setAnimationListener(animationListener)
    }

    /**更新笔记或待办条数的提示，当前正在显示条数时立刻更新。当前显示云同步提示时，在云同步结束时更新*/
    fun updateShowCountTip(text: String, visibility: Int, textView: TextView) {
        if (TextUtils.isEmpty(text)) {
            return
        }
        textView.text = if (visibility == View.VISIBLE) text else ""
    }

    /**更新 云同步相关的提示，立刻更新*/
    fun updateNextCloudTip(syncTipData: SyncTipData) {
        checkInitViews()
        if (mNowShowingTipData == null) {
            //显示笔记条数切换到显示云同步状态
            mNowShowingTipData = syncTipData
            updateCurrentIconAndText(syncTipData)
            showNext()
        } else {
            //云同步状态间切换
            mNowShowingTipData = syncTipData
            updateNextIconAndText(syncTipData)
            syncTipViewFlipper.showNext()
        }
        //成功或失败 4s 后显示原来的笔记或待办数量
        if (syncTipData.syncStatus.isFinishStatus) {
            removeCallbacks(mShowCountTipTask)
            postDelayed(mShowCountTipTask, SHOW_COUNT_TIP_DELAY)
        }
    }

    /**隐藏云同步提示,显示笔记或待办条数*/
    fun switchToSubTitle() {
        if (mNowShowingTipData?.syncStatus?.isFinishStatus == false) {
            post(mShowCountTipTask)
        }
    }

    private fun updateCurrentIconAndText(syncTipData: SyncTipData) {
        updateTargetIcon(syncTipViewFlipper.displayedChild, syncTipData)
        updateTargetText(syncTipViewFlipper.displayedChild, syncTipData)
    }

    private fun updateNextIconAndText(syncTipData: SyncTipData) {
        var nextChildIndex = syncTipViewFlipper.displayedChild + 1
        if (nextChildIndex >= syncTipViewFlipper.childCount) {
            nextChildIndex = 0
        } else if (nextChildIndex < 0) {
            nextChildIndex = syncTipViewFlipper.childCount - 1
        }
        updateTargetIcon(nextChildIndex, syncTipData)
        updateTargetText(nextChildIndex, syncTipData)
    }

    /**修改指定子 lottieView 的图标内容*/
    private fun updateTargetIcon(index: Int, syncTipData: SyncTipData) {
        (syncTipViewFlipper.getChildAt(index)?.findViewById<EffectiveAnimationView>(R.id.icon))?.let {
            if (syncTipData.syncStatus == CloudKitSyncStatus.SYNC_CODE_SYNCING) {
                if (syncTipData.syncStatus == CloudKitSyncStatus.SYNC_CODE_SYNCING) {
                    it.setAnimation(R.raw.cloud_sync_loading)
                } else {
                    it.setAnimation(-1)
                }
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }
    }

    /**修改指定子 View 文字内容 和点击按钮内容*/
    private fun updateTargetText(index: Int, syncTipData: SyncTipData) {
        (syncTipViewFlipper.getChildAt(index).findViewById<TextView>(R.id.text)).let {
            setTipSequence(syncTipData, it)
        }
    }

    private fun stopAllLottieAnimation() {
        AppLogger.BASIC.d(TAG, "stopAllLottieAnimation")
        syncTipViewFlipper.getChildAt(0).findViewById<EffectiveAnimationView>(R.id.icon).cancelAnimation()
        syncTipViewFlipper.getChildAt(1).findViewById<EffectiveAnimationView>(R.id.icon).cancelAnimation()
    }

    private fun setTipSequence(syncTipData: SyncTipData, textView: TextView) {
        if (TextUtils.isEmpty(syncTipData.checkText)) {
            textView.text = context.getString(syncTipData.textResId)
            textView.ellipsize = TextUtils.TruncateAt.END
        } else {
            textView.ellipsize = null
            val checkString = syncTipData.checkText
            var wholeText = if (syncTipData.text.isNullOrEmpty()) {
                context.getString(syncTipData.textResId, checkString)
            } else {
                syncTipData.text ?: ""
            }
            //step 1 处理文字超长，中间打点，查看文字需要显示出来
            val viewWidth = textView.measuredWidth
            val textWith = textView.paint.measureText(wholeText).toInt()
            if (textWith > viewWidth) {
                val ellipse = "..."
                val checkStart = wholeText.lastIndexOf(checkString!!) - 1
                var startIndex = checkStart
                while (startIndex > 0) {
                    startIndex--
                    if (textView.paint.measureText(wholeText.replaceRange(startIndex, checkStart, ellipse)) < viewWidth) {
                        break
                    }
                }
                // IndexOutOfBoundsException: start 0, end -1
                if (checkStart >= startIndex) {
                    wholeText = wholeText.replaceRange(startIndex, checkStart, ellipse)
                }
            }

            //step 2 处理查看按钮文字高亮
            val spannableString = SpannableString(wholeText)
            //高亮文本的开始下标
            val start = wholeText.lastIndexOf(checkString!!)
            //高亮文本结束下标
            val end = start + checkString.length

            spannableString.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    onClickCheck(syncTipData.syncStatus)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.color = mCheckTextColor
                }
            }, start, end, Spanned.SPAN_MARK_MARK)

            textView.text = spannableString
            textView.movementMethod = SubViewMovementMethod.getInstance()
            //rtl 语言 textView 某些情况会设置一个很大的scrollX导致文字看不见
            textView.scrollX = 0
        }
    }

    /**点击查看*/
    private fun onClickCheck(syncStatus: CloudKitSyncStatus) {
        AppLogger.BASIC.d(TAG, "onClickCheck $syncStatus")
        when (syncStatus) {
            CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL,
            CloudKitSyncStatus.SYNC_CODE_FINISHED_INTERRUPTED,
            CloudKitSyncStatus.SYNC_CODE_FINISHED_NO_GLOBAL_STATE -> showCloudSyncErrorDialog(syncStatus)

            CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_SPACE_NOT_ENOUGH -> {
                if (BuildConfig.isExport) {
                    NoteSyncProcess.startCloudNoteListActivity(context)
                } else {
                    UpgradeCloudSpaceUtil.instance().doUpgradeCloudSpace(context as AppCompatActivity)
                }
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_ATTACHMENT_UPLOAD_FAILED -> {
                if (BuildConfig.isExport) {
                    NoteSyncProcess.startCloudNoteListActivity(context)
                } else {
                    showCloudSyncErrorDialog(syncStatus)
                }
            }
            CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS -> NoteSyncProcess.startCloudNoteListActivity(context)
            CloudKitSyncStatus.SYNC_CODE_FINISHED_LOCAL_SPACE_NOT_ENOUGH -> {
                /*跳转存储设置**/
                val settingIntent = Intent(Settings.ACTION_INTERNAL_STORAGE_SETTINGS)
                settingIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(settingIntent)
            }

            CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_STEAM_LIMIT -> {
                /* 重试,触发一次同步**/
                NoteListHelper.startSynchronizeByCloudkit(true)
            }
            else -> {}
        }
    }

    private fun showCloudSyncErrorDialog(syncStatus: CloudKitSyncStatus) {
        SyncSwitchStateRepository.querySwitchState(context, object : GetSyncSwitchListener {
            override fun onGetSyncSwitch(switchStateCode: Int) {
                if (context == null || (context as Activity).isFinishing) {
                    return
                }
                CloudSyncErrorDialog(context, CloudkitSyncUtil.getSyncFailReason(context, switchStateCode)).apply {
                    mRetryStartSyncListener = object : CloudSyncErrorDialog.RetryStartSyncListener {
                        override fun retryStartSync(dialog: CloudSyncErrorDialog) {
                            if (syncStatus == CloudKitSyncStatus.SYNC_CODE_FINISHED_NO_GLOBAL_STATE) {
                                //重新获取全球一体化信息
                                fetchCloudGlobalState()
                            } else {
                                //重新开始同步
                                NoteListHelper.startSynchronizeByCloudkit(false)
                            }
                            postDelayed({ dialog.dismiss() }, CloudSyncErrorDialog.DIALOG_DELAY_TIME)
                        }
                    }
                    show()
                }
            }
        })
    }

    /**恢复成显示笔记/待办条数*/
    private val mShowCountTipTask = Runnable {
        mNowShowingTipData = null
        showNext()
        syncFinishCallback?.onSyncFinishSubtitleChange()
    }

    /**显示同步状态中*/
    fun showSyncing(): Boolean {
        return mNowShowingTipData != null
    }

    /**重新获取全球一体化信息*/
    private fun fetchCloudGlobalState() {
        CloudKitGlobalStateManager.judgeOCloudState(object : JudgeGlobalStateCallback {
            override fun onStart() {
            }

            override fun onSuccess(globalEnable: Boolean, cloudEnable: Boolean) {
                //获取成功，执行同步流程
                NoteListHelper.startSynchronizeByCloudkit(false)
            }

            override fun onError(errorType: Int) {
                when (errorType) {
                    CloudKitGlobalStateManager.TYPE_ACCOUNT_DISABLED,
                    CloudKitGlobalStateManager.TYPE_DEVICE_DISABLED -> {
                        (context as? MainActivity)?.let {
                            it.lifecycleScope.launch(Dispatchers.Main) {
                                SyncSwitchStateRepository.showGlobalStateErrorDialog(it, errorType)
                                it.refreshCloudSyncTips()
                            }
                        }
                    }
                    CloudKitGlobalStateManager.TYPE_NET_ERROR,
                    CloudKitGlobalStateManager.TYPE_SERVER_ERROR -> {
                        SyncManager.updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_FINISHED_NO_GLOBAL_STATE)
                    }
                }
            }
        })
    }
}