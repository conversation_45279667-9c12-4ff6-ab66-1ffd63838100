/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SettingTransformer.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit.transformer

import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.metadata.SettingMetaData

class SettingTransformer {

    @get:VisibleForTesting
    val gson = Gson()

    fun convertToSettingsFrom(record: CloudMetaDataRecordProxy): SettingMetaData {
        return gson.fromJson(record.fields, SettingMetaData::class.java)
    }

    fun convertToRecordFrom(
        recordId: String?,
        sysVersion: Long,
        modeFlag: Int,
        localFolderSyncSwitch: String
    ): CloudMetaDataRecordProxy? {
        if (recordId.isNullOrEmpty()) {
            return null
        }
        val record = CloudMetaDataRecordProxy()
        record.sysRecordType = Constants.RECORD_TYPE_SETTING
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.sysRecordId = recordId
        record.sysVersion = sysVersion
        record.operatorType =
            if (sysVersion == 0L) Constants.OPERATOR_TYPE_CREATE else Constants.OPERATOR_TYPE_REPLACE
        record.fields = gson.toJson(SettingMetaData(modeFlag, localFolderSyncSwitch))

        return record
    }

}

data class FolderSyncData(
    val folderId: String,
    val sync: Int,
)