/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteTransformer.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/7
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
@file:Suppress("WhenExpressionFormattingRule")
package com.oplus.cloudkit.transformer

import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.SpeechLogInfo
import com.oplus.note.repo.note.entity.RichNoteExtra
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.getFileFormat
import com.nearme.note.model.isDecryptLocal
import com.nearme.note.model.isEncryptLocal
import com.nearme.note.model.isEncrypted
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.entity.SubAttachment
import com.nearme.note.util.ConfigUtils
import com.oplus.note.repo.note.entity.CloudSyncSubAttachmentItem
import com.oplus.cloud.sync.richnote.RichNoteConstants
import com.oplus.cloud.sync.richnote.RichNoteFactory.Companion.createAttachment
import com.oplus.cloudkit.lib.CloudMetaDataFileInfoProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.metadata.AttachmentMetaData
import com.oplus.cloudkit.metadata.RichNoteMetaData
import com.oplus.cloudkit.util.Constants
import com.oplus.note.BuildConfig
import com.oplus.note.data.CardContact
import com.oplus.note.data.CardSchedule
import com.oplus.note.data.CombinedCard
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.AttachmentExtra
import com.oplus.note.repo.note.entity.AttachmentExtraItem
import com.oplus.note.repo.note.entity.AttachmentFileMetaData
import com.oplus.note.repo.note.entity.CommonExtra
import com.oplus.note.repo.note.entity.ExtraInfo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.SpeechAppInfo
import com.oplus.note.repo.note.entity.SpeechAudioInfo
import com.oplus.note.repo.note.entity.SpeechKeyPointInfo
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Node

class RichNoteTransformer {

    companion object {
        private const val TAG = "RichNoteTransformer"
        val fileInfos = mutableListOf<CloudMetaDataFileInfoProxy>()
        fun convertRichNote(
            note: RichNoteWithAttachments,
            instance: RichNoteTransformer
        ): RichNoteMetaData {
            val mPContext = initializeContext()
            convertNoteToMetaData(mPContext.metaData, note, instance.gson)
            if (BuildConfig.DEBUG) {
                AppLogger.BASIC.d(TAG, "rawText ${mPContext.metaData.rawText}")
            }
            mPContext.metaData.encrypted = note.isEncryptLocal() && !note.richNote.deleted
            if (!note.richNote.deleted) {
                note.attachments?.forEach { attachment ->
                    //联系人和日程这2类的附件类型本身就没有url，不应该过滤
                    if (attachment.url.isNullOrEmpty() && attachment.type !in AttachmentTransformer.noFileAttachmentTypeList) {
                        return@forEach
                    }
                    fileInfos.add(
                        CloudMetaDataFileInfoProxy(
                            attachment.url, attachment.checkPayload
                        )
                    )
                    processAttachmentType(
                        mPContext,
                        instance,
                        attachment,
                    )
                }
            }
            mPContext.metaData.attachments = mPContext.attachments
            AppLogger.BASIC.d(TAG, "attachments is :$mPContext.attachments")
            note.richNote.extra?.paints = mPContext.paints
            note.richNote.extra?.voices = mPContext.voices
            note.richNote.extra?.coverPaints = mPContext.coverPaints
            note.richNote.extra?.coverPictures = mPContext.coverPics
            note.richNote.extra?.packageName = note.richNote.packageName
            note.richNote.extra?.setSpeechMarkInfo(note.speechLogInfo?.speechMark)
            note.richNote.extra?.setAudioInfo(note.speechLogInfo?.extraInfo?.audioInfo)
            note.richNote.extra?.setAppInfo(note.speechLogInfo?.extraInfo?.appInfo)
            note.richNote.extra?.updateCombineCards(
                mPContext.cardContacts, mPContext.cardSchedules, mPContext.nlpHandled
            )
            note.richNote.extra?.encryptStatus =
                if (note.isEncrypted()) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
            mPContext.metaData.extra = note.richNote.extra?.upToDate()
            note.richNote.attachmentExtra?.attachmentExtra =
                ArrayList(mPContext.attachmentsTempExtra)
            mPContext.metaData.attachmentExtra =
                note.richNote.attachmentExtra?.upToDate(Attachment.SUPPORT_TYPE_MAX)
            return mPContext.metaData
        }

        private fun initializeContext(): AttachmentProcessingContext {
            val metaData = RichNoteMetaData()
            val attachments = mutableListOf<AttachmentMetaData>()
            val paints = mutableListOf<CloudSyncSubAttachmentItem>()
            val voices = mutableListOf<CloudSyncSubAttachmentItem>()
            val coverPaints = mutableListOf<CloudSyncSubAttachmentItem>()
            val coverPics = mutableListOf<CloudSyncSubAttachmentItem>()
            val cardContacts = mutableListOf<CardContact>()
            val cardSchedules = mutableListOf<CardSchedule>()
            val attachmentsTempExtra = mutableListOf<AttachmentExtraItem>()
            val nlpHandled = false

            return AttachmentProcessingContext(
                metaData = metaData,
                attachments = attachments,
                voices = voices,
                paints = paints,
                coverPaints = coverPaints,
                coverPics = coverPics,
                cardSchedules = cardSchedules,
                cardContacts = cardContacts,
                attachmentsTempExtra = attachmentsTempExtra,
                nlpHandled = nlpHandled
            )
        }

        private fun convertNoteToMetaData(
            metaData: RichNoteMetaData,
            note: RichNoteWithAttachments,
            gson: Gson
        ) {
            metaData.localId = note.richNote.localId
            metaData.rawText = note.richNote.rawText
            metaData.rawTitle = note.richNote.rawTitle
            metaData.folderGuid = note.richNote.folderGuid
            metaData.createTime = note.richNote.createTime
            metaData.updateTime = note.richNote.updateTime
            metaData.topTime = note.richNote.topTime
            metaData.recycleTime = note.richNote.recycleTime
            metaData.alarmTime = note.richNote.alarmTime
            metaData.skinId = note.richNote.skinId
            metaData.version = note.richNote.version
            metaData.category = RichNoteConstants.RICH_NOTE_CATEGORY_TYPE
            metaData.dataVersion = RichNoteConstants.RICH_NOTE_DATA_VERSION
            metaData.itemId = note.richNote.localId
            metaData.speechLog = note.speechLogInfo?.let {
                gson.toJson(it)
            } ?: ""
        }

        private fun processAttachmentType(
            context: AttachmentProcessingContext,
            instance: RichNoteTransformer,
            attachment: Attachment,
        ) {
            when (attachment.type) {
                Attachment.TYPE_VOICE -> {
                    context.voices.add(
                        getCloudSyncSubAttachmentItem(attachment)
                    )
                }

                Attachment.TYPE_PAINT -> {
                    context.paints.add(
                        getCloudSyncSubAttachmentItem(attachment)
                    )
                }

                Attachment.TYPE_COVER_PAINT -> {
                    context.coverPaints.add(
                        getCloudSyncSubAttachmentItem(attachment)
                    )
                }

                Attachment.TYPE_COVER_PICTURE -> {
                    context.coverPics.add(
                        getCloudSyncSubAttachmentItem(attachment)
                    )
                }

                Attachment.TYPE_SPEECH_AUDIO -> {
                    //do nothing,just update speechLogInfo
                    addSpeechLog(instance.gson, context.metaData, attachment)
                    /**
                     * non-MP3 format file has been added to the recording summary.
                     * You need to transfer a copy of this type of attachment to AttachmentExtra.
                     */
                    if (attachment.getFileFormat().isNotEmpty() && attachment.getFileFormat()
                            .equals("mp3", ignoreCase = true).not()
                    ) {
                        context.attachmentsTempExtra.add(
                            instance.buildAttachmentExtraItem(
                                attachment
                            )
                        )
                    }
                }

                Attachment.TYPE_SPEECH_LRC -> {
                    //do nothing,just update speechLogInfo
                    addSpeechLog(instance.gson, context.metaData, attachment)
                }

                Attachment.TYPE_SPEECH_CONTACT -> {
                    val cardObj: CardContact? =
                        attachment.extra?.getCardDataObject(CardContact::class.java)
                    cardObj?.let {
                        //当extra中存有复制粘贴的extra信息的时候 需要添加到attachmentsTempExtra
                        AppLogger.BASIC.d(TAG, "attach Extra combined card contract not upload attachment")
                        context.attachmentsTempExtra.add(instance.buildAttachmentExtraItem(attachment))
                    } ?: addCardContacts(instance.gson, attachment, context)
                }

                Attachment.TYPE_SPEECH_SCHEDULE -> {
                    val cardObj: CardSchedule? =
                        attachment.extra?.getCardDataObject(CardSchedule::class.java)
                    cardObj?.let {
                        //当extra中存有复制粘贴的extra信息的时候 需要添加到attachmentsTempExtra
                        AppLogger.BASIC.d(TAG, "attach Extra combined card schedule not upload attachment")
                        context.attachmentsTempExtra.add(instance.buildAttachmentExtraItem(attachment))
                    } ?: addCardSchedule(instance.gson, attachment, context)
                }

                Attachment.TYPE_LRC_PICTURE,
                Attachment.TYPE_FILE_CARD,
                Attachment.TYPE_IDENTIFY_VOICE,
                Attachment.TYPE_IDENTIFY_VOICE_LRC,
                Attachment.TYPE_VIDEO, Attachment.TYPE_EXTERNAL_AUDIO -> {
                    context.attachmentsTempExtra.add(instance.buildAttachmentExtraItem(attachment))
                }

                Attachment.TYPE_PICTURE -> {
                    context.attachmentsTempExtra.add(instance.buildAttachmentExtraItem(attachment))
                    context.attachments.add(
                        getAttachmentMetaData(attachment)
                    )
                }

                else -> {
                    context.attachments.add(
                        getAttachmentMetaData(attachment)
                    )
                }
            }
        }

        private fun getAttachmentMetaData(attachment: Attachment): AttachmentMetaData {
            return AttachmentMetaData(
                type = attachment.type,
                id = attachment.attachmentId,
                url = attachment.url!!  // TYPE_SPEECH_CONTACT和TYPE_SPEECH_SCHEDULE 无Url
            )
        }

        private fun addCardContacts(
            gson: Gson,
            attachment: Attachment,
            context: AttachmentProcessingContext,
        ) {
            AppLogger.BASIC.d(TAG, "combined card contract not upload attachment")
            val speechLogInfo = gson.fromJson(context.metaData.speechLog, SpeechLogInfo::class.java)
            gson.fromJson(speechLogInfo.combinedCard, CombinedCard::class.java)?.apply {
                val card = this.cardContacts?.find { it.attId == attachment.attachmentId }
                if (card != null) {
                    context.cardContacts.add(card)
                }
                context.nlpHandled = this.nlpHandled
            }
        }

        private fun addCardSchedule(
            gson: Gson,
            attachment: Attachment,
            context: AttachmentProcessingContext,
        ) {
            AppLogger.BASIC.d(TAG, "combined card schedule not upload attachment")
            val speechLogInfo =
                gson.fromJson(context.metaData.speechLog, SpeechLogInfo::class.java)

            gson.fromJson(speechLogInfo.combinedCard, CombinedCard::class.java)?.apply {
                val card =
                    this.cardSchedules?.find { it.attId == attachment.attachmentId }
                if (card != null) {
                    context.cardSchedules.add(card)
                }
                context.nlpHandled = this.nlpHandled
            }
        }

        private fun getCloudSyncSubAttachmentItem(attachment: Attachment): CloudSyncSubAttachmentItem {
            return CloudSyncSubAttachmentItem(
                id = attachment.attachmentId,
                url = attachment.url!!,
                relateId = attachment.subAttachment?.associateAttachmentId
            )
        }

        private fun addSpeechLog(gson: Gson, metaData: RichNoteMetaData, attachment: Attachment) {
            runCatching {
                val speechLogInfo = gson.fromJson(metaData.speechLog, SpeechLogInfo::class.java)
                if (attachment.type ==  Attachment.TYPE_SPEECH_AUDIO) {
                    speechLogInfo.voiceAttUrl = attachment.url
                    speechLogInfo.voiceAttId = attachment.attachmentId
                } else if (attachment.type == Attachment.TYPE_SPEECH_LRC) {
                    speechLogInfo.voiceLrcUrl = attachment.url
                    speechLogInfo.voiceLrcId = attachment.attachmentId
                } else {
                    AppLogger.BASIC.e(TAG, "addSpeechLog error with type = ${attachment.type}")
                }
                metaData.speechLog = gson.toJson(speechLogInfo)
            }.onFailure {
                AppLogger.CLOUDKIT.e(TAG, "convert speech lrc error")
            }
        }
    }

    @VisibleForTesting
    val gson = Gson()

    fun convertToRichNoteFrom(record: CloudMetaDataRecordProxy): RichNoteWithAttachments? {
        if (record.fields == null) {
            AppLogger.CLOUDKIT.d(
                TAG, "invalid data when convert to note: ${record.sysRecordId}, ${record.sysStatus}"
            )
            return null
        }

        val metaData = gson.fromJson(record.fields, RichNoteMetaData::class.java)
        val metaDataType = record.sysRecordType
        // check valid
        val isLocalIdValid = metaData.localId != null
        val isContentValid =
            metaData.rawTitle?.isNotEmpty() == true || metaData.rawText?.isNotEmpty() == true
        val isSysVersionValid = record.sysVersion > 0
        if (!isLocalIdValid || !isContentValid || !isSysVersionValid) {
            AppLogger.CLOUDKIT.d(
                TAG,
                "invalid data when convert to note:${record.sysRecordId}, $isLocalIdValid, $isContentValid, $isSysVersionValid"
            )
            return null
        }

        val richNote = if (record.sysRecordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
            RichNote(
                localId = metaData.localId!!,
                createTime = if (metaData.createTime > 0) metaData.createTime else System.currentTimeMillis(),
                state = RichNote.STATE_UNCHANGE,
                globalId = record.sysRecordId,
                rawText = checkBase64ImageTag(metaData.rawText, record.sysRecordId),
                updateTime = if (metaData.updateTime > 0) metaData.updateTime else System.currentTimeMillis(),
                topTime = metaData.topTime,
                recycleTime = metaData.recycleTime,
                alarmTime = metaData.alarmTime,
                skinId = metaData.skinId,
                rawTitle = metaData.rawTitle,
                version = metaData.version,
                folderGuid = metaData.folderGuid ?: FolderInfo.FOLDER_GUID_NO_GUID,
                encrypted = if (metaData.encrypted) 1 else 0,

                sysVersion = record.sysVersion
            )
        } else {
            RichNote(
                localId = metaData.localId!!,
                createTime = if (metaData.createTime > 0) metaData.createTime else System.currentTimeMillis(),
                state = RichNote.STATE_UNCHANGE,
                globalId = record.sysRecordId,
                rawText = checkBase64ImageTag(metaData.rawText, record.sysRecordId),
                updateTime = if (metaData.updateTime > 0) metaData.updateTime else System.currentTimeMillis(),
                topTime = metaData.topTime,
                recycleTime = metaData.recycleTime,
                alarmTime = metaData.alarmTime,
                skinId = metaData.skinId,
                rawTitle = metaData.rawTitle,
                version = metaData.version,
                folderGuid = metaData.folderGuid ?: FolderInfo.FOLDER_GUID_NO_GUID,
                encrypted = if (metaData.encrypted) 1 else 0,

                encryptSysVersion = record.sysVersion
            )
        }

        if (BuildConfig.DEBUG) {
            AppLogger.BASIC.d(TAG, "download rawText:${richNote.rawText}")
        }
        val attachments = mutableListOf<Attachment>()

        metaData.attachments?.map {
            Attachment(
                attachmentId = it.id, type = it.type, url = it.url, richNoteId = richNote.localId
            )
        }?.let { attachments.addAll(it) }

        val extra = RichNoteExtra.create(metaData.extra)
        if (extra.paints != null) {
            extra.paints = RichNoteExtra.filterInvalid(attachments, extra.paints!!)
            for ((id, url, relateId) in extra.paints!!) {
                val attachment = createAttachment(id)
                attachment.type = Attachment.TYPE_PAINT
                attachment.url = url
                attachment.richNoteId = richNote.localId
                attachment.subAttachment = SubAttachment(relateId!!)
                attachments.add(attachment)
            }
        }
        if (extra.voices != null) {
            extra.voices = RichNoteExtra.filterInvalid(attachments, extra.voices!!)
            for ((id, url, relateId) in extra.voices!!) {
                val attachment = createAttachment(id)
                attachment.type = Attachment.TYPE_VOICE
                attachment.url = url
                attachment.richNoteId = richNote.localId
                attachment.subAttachment = SubAttachment(relateId!!)
                attachments.add(attachment)
            }
        }

        if (extra.coverPictures != null) {
            for ((id, url) in extra.coverPictures!!) {
                val attachment = createAttachment(id)
                attachment.type = Attachment.TYPE_COVER_PICTURE
                attachment.url = url
                attachment.richNoteId = richNote.localId
                attachments.add(attachment)
            }
        }

        if (extra.coverPaints != null) {
            for ((id, url, relateId) in extra.coverPaints!!) {
                val attachment = createAttachment(id)
                attachment.type = Attachment.TYPE_COVER_PAINT
                attachment.url = url
                attachment.richNoteId = richNote.localId
                attachment.subAttachment = SubAttachment(relateId!!)
                attachments.add(attachment)
            }
        }
        if (extra.speechLogExtra?.entityGroup != null) {
            gson.fromJson(extra.speechLogExtra?.entityGroup, CombinedCard::class.java)?.apply {
                cardSchedules?.forEach { cardSchedule ->
                    cardSchedule.attId?.apply {
                        val attachment = createAttachment(this)
                        attachment.type = Attachment.TYPE_SPEECH_SCHEDULE
                        attachment.url = ""
                        attachment.richNoteId = richNote.localId
                        attachment.subAttachment =
                            cardSchedule.associateId?.let { SubAttachment(it) }
                        attachments.add(attachment)
                    }
                }
                cardContacts?.forEach { cardContact ->
                    cardContact.attId?.apply {
                        val attachment = createAttachment(this)
                        attachment.type = Attachment.TYPE_SPEECH_CONTACT
                        attachment.url = ""
                        attachment.richNoteId = richNote.localId
                        attachment.subAttachment =
                            cardContact.associateId?.let { SubAttachment(it) }
                        attachments.add(attachment)
                    }
                }
            }
        }

        richNote.packageName = extra.packageName
        if (extra.encryptStatus == -1) {
            extra.encryptStatus = if (richNote.isEncrypted()) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
        }
        richNote.extra = extra

        val speechLog = Gson().fromJson(metaData.speechLog, SpeechLogInfo::class.java)
        speechLog?.speechMark = extra.speechLogExtra?.markList
        speechLog?.combinedCard = extra.speechLogExtra?.entityGroup
        val appInfo = SpeechAppInfo.create(extra.speechLogExtra?.appInfo)
        val audioInfo = SpeechAudioInfo.create(extra.speechLogExtra?.audioInfo)
        val keyPintInfo = SpeechKeyPointInfo.create(extra.speechLogExtra?.keyPintInfo)
        val extraInfo = ExtraInfo(appInfo = appInfo, audioInfo = audioInfo, keyPointInfo = keyPintInfo)
        speechLog?.extraInfo = ExtraInfo.create(extraInfo.toString())
        val speechLogAttachments = speechLog?.transformToAttachments()
        if (speechLogAttachments?.isNotEmpty() == true) {
            attachments.addAll(speechLogAttachments)
        }

        /**
         * 处理AttachmentExtra,若AttachmentExtra中和attachments有重复数据，则将重复数据的扩展信息写回对应本地attachments的fileName
         * 和cloudMetaData中
         * 没有则直接添加到本地数据库；
         */

        val attachmentExtra = AttachmentExtra.create(metaData.attachmentExtra)
        var finalAttachmentExtra = ""
        val iterator = attachmentExtra.attachmentExtra?.iterator()
        while (iterator?.hasNext() == true) {
            val attExtra = iterator.next()
            val cloudMetadata = AttachmentFileMetaData.create(attExtra.fileMetaData?.source)
            //需要针对于将AI摘要的卡片复制粘贴到非AI摘要的笔记中 检测数据是否跳过空检测 因为此时url为空
            var extraSkipEmptyCheck = false
            if (!cloudMetadata.cardData.isNullOrEmpty()) {
                var cardContact: CardContact? = null
                var cardSchedule: CardSchedule? = null
                if (cloudMetadata.type == Attachment.TYPE_SPEECH_CONTACT) {
                    cardContact = runCatching {
                        Gson().fromJson(cloudMetadata.cardData, CardContact::class.java)
                    }.getOrNull()
                }
                if (cloudMetadata.type == Attachment.TYPE_SPEECH_SCHEDULE) {
                    cardSchedule = runCatching {
                        Gson().fromJson(cloudMetadata.cardData, CardSchedule::class.java)
                    }.getOrNull()
                }
                extraSkipEmptyCheck =
                    !cloudMetadata.id.isNullOrEmpty() && (cardContact != null || cardSchedule != null)
                AppLogger.CLOUDKIT.d(
                    TAG, "extraSkipEmptyCheck value=${extraSkipEmptyCheck}"
                )
            }

            if (!extraSkipEmptyCheck && (attExtra.url.isNullOrEmpty() || cloudMetadata.id.isNullOrEmpty())) {
                AppLogger.CLOUDKIT.e(
                    TAG,
                    "invalid data when convert attachmentExtra url :${attExtra.url.isNullOrEmpty()}"
                )
                continue
            }

            /**
             * 当前支持的转为attachment,转为attachment，其余保留在attachmentExtra内
             */
            if (cloudMetadata.type <= Attachment.SUPPORT_TYPE_MAX) {
                val attachment = createAttachment(cloudMetadata.id!!)
                attachment.type = cloudMetadata.type
                attachment.url = attExtra.url
                attachment.richNoteId = richNote.localId
                cloudMetadata.relatedId?.let { associateId ->
                    attachment.subAttachment = SubAttachment(associateId)
                }
                attachment.fileName = cloudMetadata.fileName
                val commonExtra = CommonExtra(
                    asrAttachId = cloudMetadata.asrAttachId,
                    audioDuration = cloudMetadata.audioDuration,
                    createTime = cloudMetadata.addToNoteTime,
                    cardData = cloudMetadata.cardData
                )
                attachment.extra = commonExtra
                attachment.cloudMetaData = cloudMetadata
                attachments.find {
                    it.attachmentId == attachment.attachmentId
                }?.apply {
                    val index = attachments.indexOf(this)
                    attachments[index] = attachment
                } ?: run {
                    attachments.add(attachment)
                }
                iterator.remove()
            }
        }
        finalAttachmentExtra = attachmentExtra.upToDate(Attachment.SUPPORT_TYPE_MAX, true)
        AppLogger.CLOUDKIT.d(TAG, "finalAttachmentExtra:${finalAttachmentExtra.isNullOrEmpty()}")

        richNote.attachmentExtra = AttachmentExtra.create(finalAttachmentExtra)
        /**
         * recycleTime!=0 但SysStatus = 0的数据 恢复成非删除状态
         */
        if (record.sysStatus == 0 && richNote.recycleTime != 0L) {
            richNote.apply {
                recycleTime = 0L
                recycleTimePre = 0L
            }
        }

        return RichNoteWithAttachments(richNote, attachments, speechLog)
    }

    @Suppress("LongMethod")
    fun convertRichNoteToRecordFrom(note: RichNoteWithAttachments): CloudMetaDataRecordProxy? {
        // check valid
        val hasUnSyncAttachment = hasUnSyncAttachmentAndFileValid(note.attachments)
        if (hasUnSyncAttachment && !note.richNote.deleted) {
            AppLogger.CLOUDKIT.d(TAG, "invalid data when convert to record: $hasUnSyncAttachment id= ${note.richNote.localId}")
            return null
        }
        if (!ConfigUtils.isNeedToSyncPrivateNote && FolderInfo.FOLDER_GUID_ENCRYPTED == note.richNote.folderGuid) {
            AppLogger.CLOUDKIT.d(TAG, "private not support sync to cloud")
            return null
        }
        val metaData = convertRichNote(note, this)
        val record = CloudMetaDataRecordProxy()
        record.sysRecordId = note.richNote.globalId
        record.fields = gson.toJson(metaData)
        record.setFileInfos(fileInfos)
        record.sysRecordType = Constants.RECORD_TYPE_RICH_NOTE_ITEM
        record.sysVersion = note.richNote.sysVersion ?: 0L
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.operatorType = if (note.isEncryptLocal() && note.richNote.sysVersion != 0L) {
            Constants.OPERATOR_TYPE_DELETE
        } else if (note.isDecryptLocal()) {
            if (note.richNote.sysVersion != null && note.richNote.sysVersion != 0L) {
                when {
                    note.richNote.deleted -> {
                        Constants.OPERATOR_TYPE_DELETE
                    }

                    note.richNote.recycleTime == 0L -> {
                        Constants.OPERATOR_TYPE_RESTORE
                    }

                    else -> {
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                }
            } else {
                when {
                    note.richNote.deleted -> {
                        Constants.OPERATOR_TYPE_DELETE
                    }

                    note.richNote.recycleTime == 0L -> {
                        Constants.OPERATOR_TYPE_CREATE
                    }

                    else -> {
                        Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                    }
                }
            }
        } else {
            when {
                note.richNote.state == RichNote.STATE_RESTORE -> {
                    if (note.richNote.recycleTime == 0L) {
                        Constants.OPERATOR_TYPE_RESTORE
                    } else {
                        //可以从删除状态直接恢复到回收站
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                }
                note.richNote.deleted -> {
                    Constants.OPERATOR_TYPE_DELETE
                }

                note.richNote.state == RichNote.STATE_NEW || record.sysVersion == 0L -> {
                    if (note.richNote.recycleTime == 0L) {
                        Constants.OPERATOR_TYPE_CREATE
                    } else {
                        Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                    }
                }

                note.richNote.recycleTime != 0L && note.richNote.recycleTimePre == 0L -> {
                    Constants.OPERATOR_TYPE_RECYCLE
                }

                note.richNote.recycleTime == 0L && note.richNote.recycleTimePre != 0L -> {
                    Constants.OPERATOR_TYPE_RESUME
                }

                else -> {
                    Constants.OPERATOR_TYPE_REPLACE
                }
            }
        }
        AppLogger.CLOUDKIT.d(
            TAG,
            "operate cloud richNote:${record.sysRecordId} --- status: ${note.richNote.state} --- type:${record.operatorType}"
        )

        return record
    }

    fun buildAttachmentExtraItem(attachment: Attachment): AttachmentExtraItem {
        return AttachmentExtraItem(filePath = attachment.absolutePath(
            MyApplication.appContext
        ),
            oosId = attachment.url.takeIf { it?.startsWith("/") == true }
                ?.apply {
                    this.removePrefix("/")
                },
            url = attachment.url,
            fileHash = attachment.md5,
            fileMediaType = attachment.type.toString(),
            fileSize = attachment.getFileSize(MyApplication.appContext),
            fileMetaData = attachment.cloudMetaData?.apply {
                this.id = attachment.attachmentId
                this.type = attachment.type
                this.fileName = attachment.fileName
                this.relatedId =
                    attachment.subAttachment?.associateAttachmentId
                this.imgWidth = attachment.picture?.width
                this.imgHeight = attachment.picture?.height
                attachment.extra?.let {
                    this.asrAttachId = it.asrAttachId
                    this.audioDuration = it.audioDuration
                    this.addToNoteTime = it.createTime
                    this.cardData = it.cardData
                }
            })
    }

    @Suppress("LongMethod")
    fun convertEncryptedRichNoteToRecordFrom(note: RichNoteWithAttachments): CloudMetaDataRecordProxy? {
        // check valid
        val hasUnSyncAttachment = hasUnSyncAttachmentAndFileValid(note.attachments)
        if (hasUnSyncAttachment && !note.richNote.deleted) {
            AppLogger.CLOUDKIT.d(TAG, "invalid data when convert to record: $hasUnSyncAttachment")
            return null
        }
        if (!ConfigUtils.isNeedToSyncPrivateNote && FolderInfo.FOLDER_GUID_ENCRYPTED == note.richNote.folderGuid) {
            AppLogger.CLOUDKIT.d(TAG, "private not support sync to cloud")
            return null
        }

        val metaData = RichNoteMetaData()
        metaData.localId = note.richNote.localId
        metaData.rawText = note.richNote.rawText
        metaData.rawTitle = note.richNote.rawTitle
        metaData.folderGuid = note.richNote.folderGuid
        metaData.createTime = note.richNote.createTime
        metaData.updateTime = note.richNote.updateTime
        metaData.topTime = note.richNote.topTime
        metaData.recycleTime = note.richNote.recycleTime
        metaData.alarmTime = note.richNote.alarmTime
        metaData.skinId = note.richNote.skinId
        metaData.version = note.richNote.version
        metaData.category = RichNoteConstants.RICH_NOTE_CATEGORY_TYPE
        metaData.dataVersion = RichNoteConstants.RICH_NOTE_DATA_VERSION
        metaData.itemId = note.richNote.localId
        metaData.speechLog =
            RichNoteRepository.querySpeechLogByRichNoteId(note.richNote.localId)?.let {
                Gson().toJson(it)
            } ?: ""
        metaData.encrypted = note.isDecryptLocal().not()

        val fileInfos = mutableListOf<CloudMetaDataFileInfoProxy>()
        val attachments = mutableListOf<AttachmentMetaData>()
        val paints = mutableListOf<CloudSyncSubAttachmentItem>()
        val voices = mutableListOf<CloudSyncSubAttachmentItem>()
        val coverPaints = mutableListOf<CloudSyncSubAttachmentItem>()
        val coverPics = mutableListOf<CloudSyncSubAttachmentItem>()
        val cardContacts = mutableListOf<CardContact>()
        val cardSchedules = mutableListOf<CardSchedule>()
        var nlpHandled = false
        val attachmentsTempExtra = mutableListOf<AttachmentExtraItem>()
        if (!note.richNote.deleted) {
            note.attachments?.forEach { attachment ->
                //联系人和日程这2类的附件类型本身就没有url，不应该过滤
                if (attachment.url.isNullOrEmpty() && attachment.type !in AttachmentTransformer.noFileAttachmentTypeList) {
                    return@forEach
                }

                fileInfos.add(CloudMetaDataFileInfoProxy(attachment.url, attachment.checkPayload))

                when (attachment.type) {
                    Attachment.TYPE_VOICE -> {
                        voices.add(
                            CloudSyncSubAttachmentItem(
                                id = attachment.attachmentId,
                                url = attachment.url!!,
                                relateId = attachment.subAttachment!!.associateAttachmentId
                            )
                        )
                    }

                    Attachment.TYPE_PAINT -> {
                        paints.add(
                            CloudSyncSubAttachmentItem(
                                id = attachment.attachmentId,
                                url = attachment.url!!,
                                relateId = attachment.subAttachment!!.associateAttachmentId
                            )
                        )
                    }

                    Attachment.TYPE_COVER_PAINT -> {
                        coverPaints.add(
                            CloudSyncSubAttachmentItem(
                                id = attachment.attachmentId,
                                url = attachment.url!!,
                                relateId = attachment.subAttachment!!.associateAttachmentId
                            )
                        )
                    }

                    Attachment.TYPE_COVER_PICTURE -> {
                        coverPics.add(
                            CloudSyncSubAttachmentItem(
                                id = attachment.attachmentId,
                                url = attachment.url!!,
                                relateId = attachment.subAttachment?.associateAttachmentId
                            )
                        )
                    }

                    Attachment.TYPE_SPEECH_AUDIO -> {
                        //do nothing,just update speechLogInfo
                        runCatching {
                            val speechLogInfo =
                                Gson().fromJson(metaData.speechLog, SpeechLogInfo::class.java)
                            speechLogInfo.voiceAttUrl = attachment.url
                            speechLogInfo.voiceAttId = attachment.attachmentId
                            metaData.speechLog = Gson().toJson(speechLogInfo)
                        }.onFailure {
                            AppLogger.CLOUDKIT.e(TAG, "convert speech_audio error")
                        }

                        /**
                         * non-MP3 format file has been added to the recording summary.
                         * You need to transfer a copy of this type of attachment to AttachmentExtra.
                         */
                        if (attachment.getFileFormat()
                                .isNotEmpty() && attachment.getFileFormat() != "mp3"
                        ) {
                            attachmentsTempExtra.add(buildAttachmentExtraItem(attachment))
                        }
                    }

                    Attachment.TYPE_SPEECH_LRC -> {
                        //do nothing,just update speechLogInfo
                        runCatching {
                            val speechLogInfo =
                                Gson().fromJson(metaData.speechLog, SpeechLogInfo::class.java)
                            speechLogInfo.voiceLrcUrl = attachment.url
                            speechLogInfo.voiceLrcId = attachment.attachmentId
                            metaData.speechLog = Gson().toJson(speechLogInfo)
                        }.onFailure {
                            AppLogger.CLOUDKIT.e(TAG, "convert speech_lrc error")
                        }
                    }

                    Attachment.TYPE_SPEECH_CONTACT -> {
                        val cardObj: CardContact? =
                            attachment.extra?.getCardDataObject(CardContact::class.java)
                        cardObj?.let {
                            //当extra中存有复制粘贴的extra信息的时候 需要添加到attachmentsTempExtra
                            AppLogger.BASIC.d(TAG, "attach Extra combined card contract not upload attachment")
                            attachmentsTempExtra.add(buildAttachmentExtraItem(attachment))
                        } ?: let {
                            AppLogger.BASIC.d(TAG, "combined card contract not upload attachment")
                            val speechLogInfo =
                                gson.fromJson(metaData.speechLog, SpeechLogInfo::class.java)

                            gson.fromJson(speechLogInfo.combinedCard, CombinedCard::class.java)?.apply {
                                val card =
                                    this.cardContacts?.find { it.attId == attachment.attachmentId }
                                if (card != null) {
                                    cardContacts.add(card)
                                }
                                nlpHandled = this.nlpHandled
                            }
                        }
                    }

                    Attachment.TYPE_SPEECH_SCHEDULE -> {
                        val cardObj: CardSchedule? =
                            attachment.extra?.getCardDataObject(CardSchedule::class.java)
                        cardObj?.let {
                            //当extra中存有复制粘贴的extra信息的时候 需要添加到attachmentsTempExtra
                            AppLogger.BASIC.d(TAG, "attach Extra combined card schedule not upload attachment")
                            attachmentsTempExtra.add(buildAttachmentExtraItem(attachment))
                        } ?: let {
                            AppLogger.BASIC.d(TAG, "combined card schedule not upload attachment")
                            val speechLogInfo =
                                gson.fromJson(metaData.speechLog, SpeechLogInfo::class.java)

                            gson.fromJson(speechLogInfo.combinedCard, CombinedCard::class.java)?.apply {
                                val card =
                                    this.cardSchedules?.find { it.attId == attachment.attachmentId }
                                if (card != null) {
                                    cardSchedules.add(card)
                                }
                                nlpHandled = this.nlpHandled
                            }
                        }
                    }

                    Attachment.TYPE_LRC_PICTURE,
                    Attachment.TYPE_FILE_CARD,
                    Attachment.TYPE_IDENTIFY_VOICE,
                    Attachment.TYPE_IDENTIFY_VOICE_LRC,
                    Attachment.TYPE_VIDEO,
                    Attachment.TYPE_EXTERNAL_AUDIO -> {
                        attachmentsTempExtra.add(buildAttachmentExtraItem(attachment))
                    }

                    Attachment.TYPE_PICTURE -> {
                        attachmentsTempExtra.add(buildAttachmentExtraItem(attachment))
                        attachments.add(
                            AttachmentMetaData(
                                type = attachment.type,
                                id = attachment.attachmentId,
                                url = attachment.url!!  // TYPE_SPEECH_CONTACT和TYPE_SPEECH_SCHEDULE 无Url
                            )
                        )
                    }

                    else -> {
                        attachments.add(
                            AttachmentMetaData(
                                type = attachment.type,
                                id = attachment.attachmentId,
                                url = attachment.url!!
                            )
                        )
                    }
                }
            }
        }
        metaData.attachments = attachments
        AppLogger.CLOUDKIT.d(TAG, "metaData.attachments:${attachments.map { "type:${it.type}" }}")

        note.richNote.extra?.paints = paints
        note.richNote.extra?.voices = voices
        note.richNote.extra?.coverPaints = coverPaints
        note.richNote.extra?.coverPictures = coverPics
        note.richNote.extra?.packageName = note.richNote.packageName
        note.richNote.extra?.setSpeechMarkInfo(note.speechLogInfo?.speechMark)
        note.richNote.extra?.setAudioInfo(note.speechLogInfo?.extraInfo?.audioInfo)
        note.richNote.extra?.setAppInfo(note.speechLogInfo?.extraInfo?.appInfo)
        note.richNote.extra?.updateCombineCards(cardContacts, cardSchedules, nlpHandled)
        note.richNote.extra?.encryptStatus = if (note.isEncrypted()) Folder.FOLDER_ENCRYPTED else Folder.FOLDER_UNENCRYPTED
        metaData.extra = note.richNote.extra?.upToDate()
        metaData.attachmentExtra =
            note.richNote.attachmentExtra?.updateAttachmentExtraList(Attachment.SUPPORT_TYPE_MAX, attachmentsTempExtra)

        val record = CloudMetaDataRecordProxy()
        record.sysRecordId = note.richNote.globalId
        record.fields = gson.toJson(metaData)
        record.setFileInfos(fileInfos)
        record.sysRecordType = Constants.RECORD_TYPE_ENCRYPT_RICH_NOTE_ITEM
        record.sysVersion = note.richNote.encryptSysVersion ?: 0L
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.operatorType = if (note.isDecryptLocal()) {
            Constants.OPERATOR_TYPE_DELETE
        } else if (note.isEncryptLocal()) {
            if (note.richNote.encryptSysVersion != null && note.richNote.encryptSysVersion != 0L) {
                when {
                    note.richNote.deleted -> {
                        Constants.OPERATOR_TYPE_DELETE
                    }

                    note.richNote.recycleTime == 0L -> {
                        Constants.OPERATOR_TYPE_RESTORE
                    }

                    else -> {
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                }
            } else {
                when {
                    note.richNote.deleted -> {
                        Constants.OPERATOR_TYPE_DELETE
                    }

                    note.richNote.recycleTime == 0L -> {
                        Constants.OPERATOR_TYPE_CREATE
                    }

                    else -> {
                        Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                    }
                }
            }
        } else {
            when {
                note.richNote.state == RichNote.STATE_RESTORE -> {
                    if (note.richNote.recycleTime == 0L) {
                        Constants.OPERATOR_TYPE_RESTORE
                    } else {
                        //可以从删除状态直接恢复到回收站
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                }
                note.richNote.deleted -> {
                    Constants.OPERATOR_TYPE_DELETE
                }

                note.richNote.state == RichNote.STATE_NEW || record.sysVersion == 0L -> {
                    if (note.richNote.recycleTime == 0L) {
                        Constants.OPERATOR_TYPE_CREATE
                    } else {
                        Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                    }
                }

                note.richNote.recycleTime != 0L && note.richNote.recycleTimePre == 0L -> {
                    Constants.OPERATOR_TYPE_RECYCLE
                }

                note.richNote.recycleTime == 0L && note.richNote.recycleTimePre != 0L -> {
                    Constants.OPERATOR_TYPE_RESUME
                }

                else -> {
                    Constants.OPERATOR_TYPE_REPLACE
                }
            }
        }

        AppLogger.CLOUDKIT.d(
            TAG,
            "operate cloud encrypt richNote:${record.sysRecordId} --- status: ${note.richNote.state} --- type:${record.operatorType}"
        )

        return record
    }


    private fun checkBase64ImageTag(
        rawText: String?,
        globalId: String
    ): String {
        if (TextUtils.isEmpty(rawText)) {
            return ""
        }
        kotlin.runCatching {
            if (rawText?.contains("<img") == true && rawText.contains("data:image")) {
                val document = Jsoup.parse(rawText)
                val removeImgList = ArrayList<Node>()
                document.allElements.forEach {
                    if (it.tagName() == "img" && it.attr("src").startsWith("data:image")) {
                        removeImgList.add(it)
                    }
                }
                if (removeImgList.isNotEmpty()) {
                    removeImgList.forEach { it.remove() }
                    AppLogger.CLOUDKIT.d(
                        TAG, "checkBase64ImageTag note globalId $globalId remove base64 image tag"
                    )
                    document.outputSettings(Document.OutputSettings().prettyPrint(false))
                    return document.toString()
                }
            }
        }.onFailure {
            AppLogger.CLOUDKIT.d(TAG, "checkBase64ImageTag error ${it.message}")
        }
        return rawText ?: ""
    }

    @VisibleForTesting
    fun hasUnSyncAttachmentAndFileValid(attachments: List<Attachment>?): Boolean {
        return attachments?.filter {
            it.type != Attachment.TYPE_SPEECH_CONTACT && it.type != Attachment.TYPE_SPEECH_SCHEDULE
        }?.find {
            it.url.isNullOrEmpty() && !AttachmentTransformer.invalidSizeAttachment(MyApplication.myApplication, it)
        } != null
    }

    data class AttachmentProcessingContext(
        val metaData: RichNoteMetaData,
        val attachments: MutableList<AttachmentMetaData>,
        val voices: MutableList<CloudSyncSubAttachmentItem>,
        val paints: MutableList<CloudSyncSubAttachmentItem>,
        val coverPaints: MutableList<CloudSyncSubAttachmentItem>,
        val coverPics: MutableList<CloudSyncSubAttachmentItem>,
        val attachmentsTempExtra: MutableList<AttachmentExtraItem>,
        val cardContacts: MutableList<CardContact>,
        val cardSchedules: MutableList<CardSchedule>,
        var nlpHandled: Boolean
    )
}