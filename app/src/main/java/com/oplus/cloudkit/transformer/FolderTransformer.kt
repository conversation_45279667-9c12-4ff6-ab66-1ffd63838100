/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: FolderTransformer.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/15
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit.transformer

import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderExtra
import com.nearme.note.db.isDecryptLocal
import com.nearme.note.db.isEncryptLocal
import com.oplus.cloud.agent.BaseSyncAgent
import com.oplus.cloud.protocol.ProtocolTag
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.metadata.FolderMetaData
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger
import java.util.Date

@Suppress("WhenExpressionFormattingRule")
class FolderTransformer {

    companion object {
        private const val TAG = "FolderTransformer"
    }

    @VisibleForTesting
    val gson = Gson()

    fun convertToFolderFrom(record: CloudMetaDataRecordProxy): Folder? {
        if (record.fields == null) {
            AppLogger.CLOUDKIT.e(TAG, "invalid data when convert to folder: ${record.sysRecordId}")
            return null
        }

        val metaData = gson.fromJson(record.fields, FolderMetaData::class.java)

        // check valid
        val isContentValid = metaData.name.isNotEmpty()
        val isSysVersionValid = record.sysVersion > 0
        if (!isContentValid || !isSysVersionValid) {
            AppLogger.CLOUDKIT.e(TAG, "invalid data when convert to folder:${record.sysRecordId}, $isContentValid, $isSysVersionValid")
            return null
        }

        val folder = Folder()
        folder.guid = record.sysRecordId
        folder.name = metaData.name
        folder.createTime = Date(metaData.createTime)
        folder.modifyTime = Date(metaData.modifyTime)
        folder.modifyDevice = metaData.modifyDevice
        folder.encrypted = if (metaData.encrypted) 1 else 0
        folder.extra = FolderExtra.create(metaData.extra)
        folder.sysVersion = record.sysVersion

        return folder
    }

    /**
     * 备份笔记本到folder表时时，本地加密的folder，置为recycle，从云端表folder表新增一条删除record
     * 从旧表删除，隔离低版本
     */
    fun convertFolderToRecordFrom(folder: Folder): CloudMetaDataRecordProxy {
        val record = CloudMetaDataRecordProxy()
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.sysRecordId = folder.guid
        record.sysRecordType = Constants.RECORD_TYPE_FOLDER
        record.sysVersion = folder.sysVersion
        record.operatorType =
            /**
             * Folder本地加密后，需要删除云端folder表中数据
             */
            if (folder.isEncryptLocal()) {
                if (folder.state == FolderInfo.FOLDER_STATE_NEW) {
                    Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                } else {
                    Constants.OPERATOR_TYPE_RECYCLE
                }
            } else if (folder.isDecryptLocal()) {
                /**
                 * 理论上操作不会出现此情况
                 * 但当未上云的数据 换账号登录时，会出现此情况；
                 * 及只有一条加密后的数据上了云，主表没有该条数据
                 */
                if (folder.sysVersion == 0L) {
                    if (folder.state == FolderInfo.FOLDER_STATE_DELETED) {
                        Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                    } else {
                        Constants.OPERATOR_TYPE_CREATE
                    }
                } else {
                    when (folder.state) {
                        FolderInfo.FOLDER_STATE_NEW -> {
                            /**
                             * 搬家到新账号，笔记状态为new，有sysVersion
                             */
                            Constants.OPERATOR_TYPE_CREATE
                        }
                        FolderInfo.FOLDER_STATE_DELETED -> Constants.OPERATOR_TYPE_RECYCLE
                        else -> Constants.OPERATOR_TYPE_RESUME
                    }
                }
            } else {
                when {
                    folder.state == FolderInfo.FOLDER_RESTORE -> Constants.OPERATOR_TYPE_RESUME
                    folder.state == FolderInfo.FOLDER_STATE_NEW || record.sysVersion == 0L -> {
                        Constants.OPERATOR_TYPE_CREATE
                    }
                    // NOTE: 为兼容非CK版本云端旧数据，这里使用“删除到回收站”，模拟删除操作
                    folder.state == FolderInfo.FOLDER_STATE_DELETED -> {
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                    else -> {
                        Constants.OPERATOR_TYPE_REPLACE
                    }
                }
        }

        val meta = FolderMetaData(
                name = folder.name,
                createTime = folder.createTime?.time ?: 0,
                modifyTime = folder.modifyTime?.time ?: 0,
                modifyDevice = folder.modifyDevice,
                encrypted = folder.guid == FolderInfo.FOLDER_GUID_ENCRYPTED,
                extra = folder.extra.upToDate(),
        )

        record.fields = gson.toJson(meta)

        return record
    }

    /**
     * 备份加密笔记本时，需要校验，如果本地解密了笔记本则需要 从加密表删除已解密的笔记本
     *
     */
    fun convertEncryptFolderToRecordFrom(folder: Folder): CloudMetaDataRecordProxy {
        val record = CloudMetaDataRecordProxy()
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.sysRecordId = folder.guid
        record.sysRecordType = Constants.RECORD_TYPE_ENCRYPT_FOLDER
        record.sysVersion = folder.encryptSysVersion
        record.operatorType =
           /**
            * Folder本地解密后，需要删除云端Encrypt_folder表中数据
            */
            if (folder.isDecryptLocal()) {
                if (folder.state == FolderInfo.FOLDER_STATE_NEW) {
                    Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
                } else {
                    Constants.OPERATOR_TYPE_RECYCLE
                }
            } else if (folder.isEncryptLocal()) {
                if (folder.encryptSysVersion != 0L) {
                    when (folder.state) {
                        FolderInfo.FOLDER_STATE_NEW -> {
                            /**
                             * 搬家到新账号，笔记状态为new，有encryptSysVersion
                             */
                            Constants.OPERATOR_TYPE_CREATE
                        }
                        FolderInfo.FOLDER_STATE_DELETED -> Constants.OPERATOR_TYPE_RECYCLE
                        else -> Constants.OPERATOR_TYPE_RESUME
                    }
                } else {
                    if (folder.state == FolderInfo.FOLDER_STATE_DELETED) {
                        Constants.OPERATOR_TYPE_RECYCLE
                    } else {
                        Constants.OPERATOR_TYPE_CREATE
                    }
                }
            } else {
                when {
                    folder.state == FolderInfo.FOLDER_RESTORE -> Constants.OPERATOR_TYPE_RESUME
                    folder.state == FolderInfo.FOLDER_STATE_NEW || record.sysVersion == 0L -> {
                        Constants.OPERATOR_TYPE_CREATE
                    }
                    // NOTE: 为兼容非CK版本云端旧数据，这里使用“删除到回收站”，模拟删除操作
                    folder.state == FolderInfo.FOLDER_STATE_DELETED -> {
                        Constants.OPERATOR_TYPE_RECYCLE
                    }
                    else -> {
                        Constants.OPERATOR_TYPE_REPLACE
                    }
                }
            }

        val meta = FolderMetaData(
            name = folder.name,
            createTime = folder.createTime?.time ?: 0,
            modifyTime = folder.modifyTime?.time ?: 0,
            modifyDevice = folder.modifyDevice,
            encrypted = true,
            extra = folder.extra.upToDate(),
        )

        record.fields = gson.toJson(meta)

        return record
    }

    /**
     * For using legacy folder strategy, make a mapping from record to FolderBean.
     */
    fun convertToFolderBeanFrom(record: CloudMetaDataRecordProxy): BaseSyncAgent.FolderBean? {
        if (record.fields == null) {
            AppLogger.CLOUDKIT.e(TAG, "invalid data when convert to folder bean: ${record.sysRecordId}")
            return null
        }

        val metaData = gson.fromJson(record.fields, FolderMetaData::class.java)

        // check valid
        val isContentValid = metaData.name.isNotEmpty()
        if (!isContentValid) {
            AppLogger.CLOUDKIT.e(TAG, "invalid data when convert to folder bean: $isContentValid")
            return null
        }

        return BaseSyncAgent.FolderBean(
                metaData.name,
                record.sysRecordId,
                metaData.createTime,
                metaData.modifyDevice,
                if (record.sysStatus == Constants.RECORD_STATUS_DELETED || record.sysStatus == Constants.RECORD_STATUS_RECYCLED) {
                    ProtocolTag.Folder.FOLDER_STATE_DELETED
                } else {
                    ProtocolTag.Folder.FOLDER_STATE_UNCHANGE
                },
                if (metaData.encrypted) 1 else 0,
                metaData.modifyTime,
                metaData.extra,
                record.sysVersion,
                record.sysRecordType
        )
    }
}