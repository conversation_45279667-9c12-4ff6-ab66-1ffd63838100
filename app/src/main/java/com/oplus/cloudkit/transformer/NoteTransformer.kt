/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteTransformer.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/26
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit.transformer

import android.text.TextUtils
import com.google.gson.Gson
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.oplus.note.repo.note.entity.Folder
import com.nearme.note.db.extra.NoteExtra
import com.oplus.cloud.protocol.ProtocolTag
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.metadata.NoteMetaData
import org.json.JSONObject
import java.util.*

class NoteTransformer {

    private val gson = Gson()

    fun convertToNoteFrom(record: CloudMetaDataRecordProxy, localFolders: List<Folder>): NoteInfo? {
        val note = NoteInfo()

        note.globalId = record.sysRecordId

        val meta = gson.fromJson(record.fields, NoteMetaData::class.java)

        note.guid = if (TextUtils.isEmpty(meta.guid)) {
            UUID.randomUUID().toString()
        } else {
            meta.guid
        }

        note.sysVersion = record.sysVersion
        note.version = meta.version
        note.updated = meta.updated?.toLongOrNull() ?: 0
        note.topped = meta.topped
        note.createConsole = meta.createdConsole
        note.thumbType = meta.thumbType
        note.content = meta.thumbFileName
        note.owner = meta.uid
        note.created = meta.created?.toLongOrNull() ?: 0
        note.recycled = meta.recycledTime
        note.alarmTime = meta.alarmTime
        note.noteSkin = meta.noteSkin
        note.extra = NoteExtra.create(meta.extra)

        val folderName = meta.folderName
        val folderGuid = meta.folderGuid
        if (!TextUtils.isEmpty(folderName) && !TextUtils.isEmpty(folderGuid)) {
            // check folder info is valid for local
            val localFolder = localFolders.find { it.guid == folderGuid }
            if (localFolder != null) {
                note.folderName = localFolder.name
                note.folderGuid = localFolder.guid
            } else {
                note.state = NoteInfo.STATE_MODIFIED
            }
        }

        // try to parse alarm time from oneplus data info.
        // 旧笔记数据不会再修改内容，这里可以优先使用一加笔记字段，而不需要备份更新一加笔记字段信息
        val redNoteReminderData = meta.oneplusData1
        if (!TextUtils.isEmpty(redNoteReminderData)) {
            val obj = JSONObject(redNoteReminderData)
            val hasRemindTime = obj.getBoolean(ProtocolTag.KEY_RED_NOTE_HAS_REMIND)
            val remindTime = obj.getLong(ProtocolTag.KEY_RED_NOTE_REMIND_TIME)
            if (hasRemindTime) {
                note.alarmTime = remindTime
            }
        }
        // try to parse title from oneplus data info.
        // 旧笔记数据不会再修改内容，这里可以优先使用一加笔记字段，而不需要备份更新一加笔记字段信息
        val redNoteTitle = meta.oneplusTitle
        if (!TextUtils.isEmpty(redNoteTitle)) {
            note.title = redNoteTitle
        }
        // parse attributes
        meta.noteAttributes.forEach { attrMeta ->
            //脏数据过滤，存在附件名，不存在url，type不为2（文本）的数据
            if ((attrMeta.filename.isNotEmpty()) && (attrMeta.attachmentId == null || attrMeta.attachmentId!!.isEmpty()) && (attrMeta.type != 2)) {
                return null
            }
            val type = attrMeta.type
            var attrGuid = attrMeta.filename
            // Compatible with the migration data, need to convert characters
            // "<br>", "\n"
            if (attrMeta.type == NoteAttribute.TYPE_TEXT_CONTENT) {
                attrGuid = attrGuid.replace("<br>", "\n")
            }
            val para = attrMeta.para
            val attachmentId = attrMeta.attachmentId

            val noteAttribute = NoteAttribute.newNoteAttribute(type, attrGuid)
            if (noteAttribute is NoteAttribute.TextAttribute) {
                note.setWholeContentAttribute(noteAttribute)
            } else {
                note.addAttribute(noteAttribute)
            }
            noteAttribute.setOperation(NoteAttribute.OP_ADD)
            noteAttribute.param = para
            noteAttribute.attachmentSyncUrl = attachmentId
            noteAttribute.created = note.updated
        }
        fillContentFromAttribute(note)
        return note
    }

    /**
     * 旧笔记不再修改内容，仅会执行“彻底删除”操作
     */
    fun convertToRecordFrom(note: NoteInfo): CloudMetaDataRecordProxy? {
        if (NoteInfo.STATE_MARK_DELETED == note.delete && note.globalId.isNotEmpty()) {
            val record = CloudMetaDataRecordProxy()
            record.sysRecordId = note.globalId
            record.sysRecordType = Constants.RECORD_TYPE_NOTE_ITEM
            record.operatorType = Constants.OPERATOR_TYPE_DELETE_BY_OLD_NOTE
            record.sysDataType = Constants.DATA_TYPE_NORMAL
            record.sysVersion = note.sysVersion

            return record
        }

        return null
    }

    /**
     * bug 6041683云端 thumbFileName 字段为 null 的时候，会导致旧笔记数据被视为无效被过滤掉。
     * 这里尝试从 Attribute 字段中拿出字段赋值给 content 字段。
     * 本地创建旧笔记确认的规则：
     * 有图片时取第一个图片的id
     * 无图片时取第一个文字内容
     * */
    private fun fillContentFromAttribute(note: NoteInfo) {
        if (TextUtils.isEmpty(note.content)) {
            val firstAlbumId = note.pagedElements?.firstOrNull() { it.type == NoteAttribute.TYPE_ALBUM }?.content
            if (TextUtils.isEmpty(firstAlbumId)) {
                val firstContent = note.pagedElements?.firstOrNull { it.type == NoteAttribute.TYPE_TEXT_CONTENT }?.content
                note.content = firstContent
            } else {
                note.content = firstAlbumId
            }
        }
    }
}