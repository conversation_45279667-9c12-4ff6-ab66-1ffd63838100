package com.oplus.cloudkit.transformer

import com.google.gson.Gson
import com.nearme.note.remind.RepeatDataHelper
import com.oplus.note.repo.todo.ToDoExtra
import com.oplus.note.repo.todo.entity.ToDo
import com.nearme.note.remind.RepeatManage
import com.oplus.note.logger.AppLogger
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.metadata.ToDoMetaData
import java.util.*

/**
 * 代办本地数据与元数据的转换
 */
class TodoTransformer {
    companion object {
        private const val TAG = "TodoTransformer"

        private const val NUMBER_4 = 4
        private const val NUMBER_5 = 5
    }

    private val gson = Gson()

    /**
     * @param verifyContent 是否校验Content字段，当通过清除待办的内容，删除待办时，这种情况下Content为空
     * 如果还校验content，会导致下云时，待办无法删除
     */
    fun convertToToDoFrom(record: CloudMetaDataRecordProxy): ToDo? {
        if (record.fields == null || record.sysStatus == 1) {
            AppLogger.CLOUDKIT.d(TAG, "invalid data when convert to todo: ${record.sysRecordId}, ${record.sysStatus}")
            return null
        }

        val metaData = gson.fromJson(record.fields, ToDoMetaData::class.java)
        // check valid
        val isContentValid = metaData.content?.isNotEmpty() == true
        val isSysVersionValid = record.sysVersion > 0
        if (!isContentValid || !isSysVersionValid) {
            AppLogger.CLOUDKIT.d(
                TAG, "[convertToToDoFrom] invalid data when convert to todo:${record.sysRecordId}, $isContentValid," +
                        "$isSysVersionValid"
            )
            return null
        }

        val toDo = convertToToDoFromInternal(record, metaData)
        return toDo
    }

    fun convertDeletedToToDoFrom(record: CloudMetaDataRecordProxy): ToDo? {
        if (record.fields == null) {
            AppLogger.CLOUDKIT.d(TAG, "[convertDeletedToToDoFrom] invalid data when convert to todo: ${record.sysRecordId}, ${record.sysStatus}")
            return null
        }

        val metaData = gson.fromJson(record.fields, ToDoMetaData::class.java)
        AppLogger.CLOUDKIT.d(TAG, "convertDeleted:$metaData")
        val isSysVersionValid = record.sysVersion > 0
        if (!isSysVersionValid) {
            AppLogger.CLOUDKIT.d(TAG, "invalid data when convert to todo:${record.sysRecordId}, $isSysVersionValid")
            return null
        }

        val toDo = convertToToDoFromInternal(record, metaData)
        return toDo
    }

    private fun convertToToDoFromInternal(record: CloudMetaDataRecordProxy, metaData: ToDoMetaData): ToDo {
        val toDo = ToDo()
        toDo.globalId = UUID.fromString(record.sysRecordId)

        toDo.localId = if (metaData.itemId.isNullOrEmpty()) {
            toDo.status = ToDo.StatusEnum.MODIFIED
            UUID.randomUUID()
        } else {
            toDo.status = ToDo.StatusEnum.UNCHANGE
            UUID.fromString(metaData.itemId)
        }

        toDo.updateTime = if (metaData.updateTime > 0) Date(metaData.updateTime) else Date()
        toDo.createTime = if (metaData.createTime > 0) Date(metaData.createTime) else Date()
        toDo.alarmTime = if (metaData.alarmTime > 0) Date(metaData.alarmTime) else null
        toDo.finishTime = if (metaData.finishTime > 0) Date(metaData.finishTime) else null

        toDo.content = metaData.content
        toDo.extra = ToDoExtra.create(metaData.extra)

        if (RepeatDataHelper.isAlarmTimeRepeatValid(toDo)) {
            toDo.nextAlarmTime = toDo.alarmTime
        } else {
            toDo.nextAlarmTime = null
        }
        val alarmTime = if (toDo.alarmTime != null) toDo.alarmTime.time else -1
        if (RepeatDataHelper.isRepeat(toDo) && alarmTime < System.currentTimeMillis()) {
            val nextTime = RepeatManage.nextAlarmTimeByRepeat(RepeatDataHelper.getRepeatData(toDo), toDo.alarmTime.time)
            if (nextTime > 0) {
                toDo.nextAlarmTime = Date(nextTime)
            }
        }

        toDo.sysVersion = record.sysVersion
        return toDo
    }


    fun convertToRecordFrom(toDo: ToDo): CloudMetaDataRecordProxy? {
        if (toDo.globalId == null) {
            AppLogger.CLOUDKIT.e(TAG, "convertToRecordFrom error: global id is empty.")
            return null
        }

        val toDoMetaData = ToDoMetaData()
        toDoMetaData.itemId = toDo.localId.toString()
        toDoMetaData.createTime = toDo.createTime.time
        toDoMetaData.updateTime = toDo.updateTime.time
        toDoMetaData.alarmTime = toDo.alarmTime?.time ?: 0
        toDoMetaData.finishTime = toDo.finishTime?.time ?: 0
        when (toDo.status) {
            ToDo.StatusEnum.INVALID -> toDoMetaData.status = 0
            ToDo.StatusEnum.NEW -> toDoMetaData.status = 1
            ToDo.StatusEnum.MODIFIED -> toDoMetaData.status = 2
            ToDo.StatusEnum.UNCHANGE -> toDoMetaData.status = 3
            ToDo.StatusEnum.RESTORE -> toDoMetaData.status = NUMBER_4
            ToDo.StatusEnum.ARCHIVED -> toDoMetaData.status = NUMBER_5
            null -> toDoMetaData.status = 0
        }
        toDoMetaData.content = toDo.content
        toDoMetaData.extra = toDo.extra?.toString()

        AppLogger.CLOUDKIT.d(TAG, toDoMetaData.toString())
        val record = CloudMetaDataRecordProxy()
        record.sysRecordId = toDo.globalId.toString()
        record.fields = gson.toJson(toDoMetaData)
        record.sysRecordType = Constants.RECORD_TYPE_TODO_ITEM
        record.sysVersion = toDo.sysVersion
        record.sysDataType = Constants.DATA_TYPE_NORMAL
        record.operatorType = when {
            toDo.isDelete && toDo.status == ToDo.StatusEnum.NEW && record.sysVersion == 0L -> Constants.OPERATOR_TYPE_CREATE_AND_RECYCLE
            // NOTE: 为兼容非CK版本云端旧数据，这里使用“删除到回收站”，模拟删除操作
            toDo.isDelete -> Constants.OPERATOR_TYPE_RECYCLE
            toDo.status == ToDo.StatusEnum.NEW || record.sysVersion == 0L -> Constants.OPERATOR_TYPE_CREATE
            toDo.status == ToDo.StatusEnum.RESTORE -> Constants.OPERATOR_TYPE_RESUME
            else -> Constants.OPERATOR_TYPE_REPLACE
        }

        return record
    }
}