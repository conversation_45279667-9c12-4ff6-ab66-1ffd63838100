/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: AttachmentTransformer.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/21
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit.transformer

import android.app.Application
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.util.ConfigUtils
import com.oplus.cloud.utils.MD5Utils
import com.oplus.cloudkit.lib.CloudIOFileProxy
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger
import java.io.File

class AttachmentTransformer(@get:VisibleForTesting val context: Application) {

    companion object {
        private const val TAG = "AttachmentTransformer"

        /**附件对应的本地文件异常*/
        fun invalidSizeAttachment(context: Application, attachment: Attachment): Boolean {
            val path = attachment.absolutePath(context)
            val file = File(path)
            /**
             * BugFix for #6804885, Filter out files with size 0
             */
            return file.length() <= 0L || !file.exists()
        }

        //没有文件的附件
        val noFileAttachmentTypeList = listOf(
            Attachment.TYPE_SPEECH_CONTACT,
            Attachment.TYPE_SPEECH_SCHEDULE,
        )
    }

    fun convertToCloudIOFileForUploadFrom(attachment: Attachment): CloudIOFileProxy {
        val path = attachment.absolutePath(context)
        val file = File(path)
        val fileUri = Uri.fromFile(file).toString()
        // Do not use Attachment.md5, this field is not defined for this purpose.
        // At the beginning,it indicates whether the file has been downloaded or uploaded.
        // Calculate the md5 of the file directly.
        return CloudIOFileProxy.createUploadFile(Constants.MODULE_NOTE, Constants.ZONE_RICH_NOTE, fileUri, MD5Utils.getMD5(file)).apply {
            filePath = path
        }
    }

    fun convertToCloudIOFileForDownloadFrom(attachment: Attachment): CloudIOFileProxy {
        val cloudId = attachment.url?.removePrefix("/")
        return CloudIOFileProxy.createDownloadFile(null, Constants.MODULE_NOTE, Constants.ZONE_RICH_NOTE, null, cloudId, attachment.absolutePath(context))
    }

    fun fill(
        source: List<RichNoteWithAttachments>,
        uploadList: MutableList<Attachment>,
        downloadList: MutableList<Attachment>,
        nonFileList: MutableList<Attachment>
    ) {
        // TODO 同步os13逻辑-叠写需求
        val types = listOf(
            Attachment.TYPE_PICTURE,
            Attachment.TYPE_PAINT,
            Attachment.TYPE_VOICE,
            Attachment.TYPE_COVER_PAINT,
            Attachment.TYPE_COVER_PICTURE,
            Attachment.TYPE_SPEECH_AUDIO,
            Attachment.TYPE_SPEECH_LRC,
            Attachment.TYPE_LRC_PICTURE,
            Attachment.TYPE_FILE_CARD,
            Attachment.TYPE_IDENTIFY_VOICE,
            Attachment.TYPE_IDENTIFY_VOICE_LRC,
            Attachment.TYPE_VIDEO,
            Attachment.TYPE_EXTERNAL_AUDIO,
        )
        for (note in source) {
            if (!ConfigUtils.isNeedToSyncPrivateNote && note.richNote.folderGuid == FolderInfo.FOLDER_GUID_ENCRYPTED) {
                continue
            }
            note.attachments?.forEach { attachment ->
                if (!types.contains(attachment.type)) {
                    return@forEach
                }

                if (shouldUpload(note, attachment)) {
                    uploadList.add(attachment)
                    AppLogger.CLOUDKIT.d(TAG, "sync attachments upload: $attachment")
                } else if (shouldDownload(attachment)) {
                    downloadList.add(attachment)
                    AppLogger.CLOUDKIT.d(TAG, "sync attachments download: $attachment")
                }
            }
        }

        for (note in source) {
            if (!ConfigUtils.isNeedToSyncPrivateNote && note.richNote.folderGuid == FolderInfo.FOLDER_GUID_ENCRYPTED) {
                continue
            }
            note.attachments?.filter {
                it.type in noFileAttachmentTypeList
            }?.forEach {
                nonFileList.add(it)
            }
        }
    }

    /**
     * If the md5 and the url are null or empty, the attachment needs to be uploaded
     */
    fun shouldUpload(note: RichNoteWithAttachments, attachment: Attachment): Boolean {
        if (invalidSizeAttachment(context, attachment)) {
            /**
             * BugFix for #6804885, Filter out files with size 0
             */
            AppLogger.CLOUDKIT.w(TAG, "invalid file size, attachmentId=${attachment.attachmentId} note localId=${note.richNote.localId}")
            return false
        }
        return attachment.md5.isNullOrEmpty() && attachment.url.isNullOrEmpty() && !note.richNote.deleted
    }

    /**
     * If the md5 is null or empty but the url is not empty, the attachment needs to be downloaded.
     */
    fun shouldDownload(attachment: Attachment): Boolean {
        return attachment.md5.isNullOrEmpty() && !TextUtils.isEmpty(attachment.url)
    }
}