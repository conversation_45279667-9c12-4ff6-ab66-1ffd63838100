/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptFolderSyncManager
 ** Description:
 **         v1.0:  Encrypted folder cloud synchronization
 **
 ** Version: 1.0
 ** Date: 2023/10/25
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote       2023/10/25   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.isDecryptLocal
import com.nearme.note.db.isEncryptFolder
import com.nearme.note.db.isEncryptLocal
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.FolderTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger

class EncryptFolderSyncManager(
    zone: String = Constants.ZONE_ENCRYPT_FOLDER,
    recordTypeVersion: Int = Constants.RECORD_TYPE_VERSION_ENCRYPT_FOLDER,
) : FolderSyncManager(zone, recordTypeVersion) {

    private val transformer = FolderTransformer()
    private val localDecryptRecordIds = mutableSetOf<String>()

    /**
     *当笔记本没有上云过，直接加密后才开启第一次云同步，及笔记本为加密状态，但state=new
     */
    private val localEncryptNewIds = mutableListOf<String>()
    private var mergerHelper: FolderMerger? = null

    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(TAG, "onStartRecovery")
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        // use legacy folder merge strategy.
        data?.mapNotNull { record ->
            transformer.convertToFolderBeanFrom(record)
        }?.let { cloudFolders ->
            mergerHelper?.onRecoveryFolders(cloudFolders)
        }
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        AppLogger.BASIC.d(TAG, "onRecoveryEnd")
        mergerHelper?.onRecoveryEnd(backUp)
    }

    override fun onRecoverError(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(TAG, "onRecoverError")
        onRecoveryEnd(backUp)
    }

    override fun getMetaDataCount(): Int {
        return AppDatabase.getInstance().foldersDao()
                .getEncryptCount(FolderInfo.FOLDER_GUID_ENCRYPTED) - AppDatabase.getInstance()
                .foldersDao().getEncryptCountOf(FolderInfo.FOLDER_STATE_NEW, FolderInfo.FOLDER_GUID_ENCRYPTED)
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(TAG, "onStartBackup")
        localDecryptRecordIds.clear()
        localEncryptNewIds.clear()
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {
        val dirtyFolder = AppDatabase.getInstance().foldersDao().dirtyFolders.filter {
            /**
             * 加密的笔记本 —————————— 备份到云端Encrypt_folder表
             * 本地进行解密的笔记本——————————需要从云端Encrypt_folder表表移除
             */
            it.isEncryptFolder() || it.isDecryptLocal()
        }
        AppLogger.CLOUDKIT.d(TAG, "EncryptFolderSyncManager onQueryDirtyData: ${dirtyFolder.size} }")
        return dirtyFolder.map {
             if (it.isDecryptLocal())  {
                 localDecryptRecordIds.add(it.guid)
                 AppLogger.CLOUDKIT.d(TAG, "localDecryptRecordIds add: ${it.guid}")
             } else if (it.isEncryptLocal() && it.state == FolderInfo.FOLDER_STATE_NEW) {
                 localEncryptNewIds.add(it.guid)
                 AppLogger.CLOUDKIT.d(TAG, "localEncryptNewIds add: ${it.guid}")
             }
            transformer.convertEncryptFolderToRecordFrom(it)
        }
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupStart")
        // do nothing.
    }

    override fun onPagingBackupEnd(successData: List<CloudBackupResponseRecordProxy>?, errorData: List<CloudBackupResponseErrorProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupEnd")

        val deletes = mutableListOf<String>()
        val updates = mutableListOf<Pair<String, Long>>()
        val changes = mutableListOf<Triple<Int, String, Long>>()
        successData?.forEach {
            AppLogger.CLOUDKIT.d(TAG, "successData:${it.operatorType} === ${it.sysRecordId}")
            if (it.operatorType == Constants.OPERATOR_TYPE_DELETE || it.operatorType == Constants.OPERATOR_TYPE_RECYCLE) {
                if (localDecryptRecordIds.contains(it.sysRecordId)) {
                    AppLogger.CLOUDKIT.d(TAG, "decrypt folder ,modify pre")
                    changes.add(Triple(FolderInfo.FOLDER_UNENCRYPTED, it.sysRecordId, it.sysVersion))
                    return@forEach
                }
                deletes.add(it.sysRecordId)
            } else {
                updates.add(Pair(it.sysRecordId, it.sysVersion))
                if (localEncryptNewIds.contains(it.sysRecordId)) {
                    AppLogger.CLOUDKIT.d(TAG, "new encrypt folder ,modify pre")
                    changes.add(Triple(FolderInfo.FOLDER_ENCRYPTED, it.sysRecordId, 0))
                }
            }
        }
        AppDatabase.getInstance().foldersDao().deleteFolderByGuid(deletes)
        AppDatabase.getInstance().foldersDao().updateFolderStateAndEncryptSysVersion(updates)
        mergerHelper?.setFolderEncryptedChanged(changedData = changes)
    }

    override fun setFolderMerger(mergerHelper: FolderMerger) {
        this.mergerHelper = mergerHelper
    }

    companion object {
        private const val TAG = "EncryptFolderSyncManager"
    }
}