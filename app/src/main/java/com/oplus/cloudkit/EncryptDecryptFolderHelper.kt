/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptDecryptFolderManager
 ** Description:
 **         v1.0:   Handling encrypted and decrypted folder policies
 **
 ** Version: 1.0
 ** Date: 2023/11/06
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote       2023/11/6   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.cloud.agent.BaseSyncAgent
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger
import java.util.concurrent.atomic.AtomicInteger

class EncryptDecryptFolderHelper {

    @get:VisibleForTesting
    val folderNewOrDelete = mutableListOf<BaseSyncAgent.FolderBean>()
    @get:VisibleForTesting
    val encryptFolderNewOrDelete = mutableListOf<BaseSyncAgent.FolderBean>()
    @get:VisibleForTesting
    val cacheSize = AtomicInteger(CACHE_LIST_SIZE)
    private val mergerHelper = FolderMerger(MyApplication.appContext)

    /**
     * 由于存在需要等待FolderSyncManager和EncryptedFolderSyncManager recovery后解冲突的时候，需要
     * 暂时将这两个manger的backup暂停，等解完冲突，改好本地数据后，再触发它们的backup
     * 保存未触发的backup
     */
    @get:VisibleForTesting
    val pendingBackup = mutableListOf<(() -> Unit)?>()
    fun init() {
        folderNewOrDelete.clear()
        encryptFolderNewOrDelete.clear()
        cacheSize.set(CACHE_LIST_SIZE)
        pendingBackup.clear()
    }

    /**
     * 将从云端[Constants.ZONE_FOLDER]&[Constants.ZONE_ENCRYPT_FOLDER] recovery下来的数据中，
     * state为[FolderInfo.FOLDER_STATE_UNCHANGE]或[FolderInfo.FOLDER_STATE_DELETED]的数据先缓存到内存，
     *
     * 对比guid后统一merge冲突
     * @param folderBean 云端FolderBean
     */
    fun cache(folderBean: BaseSyncAgent.FolderBean) {
        AppLogger.BASIC.d(TAG, "cache:$folderBean record:${folderBean.mSysRecordType}")
        if (folderBean.mSysRecordType == Constants.RECORD_TYPE_ENCRYPT_FOLDER) {
            encryptFolderNewOrDelete.add(folderBean)
        } else {
            folderNewOrDelete.add(folderBean)
        }
    }


    fun onRecoveryEnd(afterCombine: (() -> Unit)?) {
        synchronized(pendingBackup) {
            if (cacheSize.decrementAndGet() == 0) {
                /**
                 * 缓存完成，开始解冲突逻辑
                 * 解完冲突之后才可以开启FolderSyncManager及EncryptFolderSyncManager的backup
                 */
                AppLogger.BASIC.d(
                    TAG,
                    "cache finished: folderNewOrDelete $folderNewOrDelete" +
                            ",encryptFolderNewOrDelete:$encryptFolderNewOrDelete"
                )
                cacheSize.set(CACHE_LIST_SIZE)
                combineMerge(folderNewOrDelete, encryptFolderNewOrDelete)
                pendingBackup.add(afterCombine)
                pendingBackup.forEach {
                    it?.invoke()
                }
            } else {
                AppLogger.BASIC.d(TAG, "cache waiting:${cacheSize.get()}")
                pendingBackup.add(afterCombine)
            }
        }
    }


    /**
     * 因为加密笔记本是删除原表+加密表新建，因此会有两条记录需要merge处理
     * 解密同样，是一条删除加密表+原表新建
     */
    private fun combineMerge(
        folderNewOrDelete: MutableList<BaseSyncAgent.FolderBean>,
        encryptFolderNewOrDelete: MutableList<BaseSyncAgent.FolderBean>
    ) {

        /**
         * 筛选两个列表中guid不同的记录，他们可以走原有merge逻辑
         */
        AppLogger.BASIC.d(TAG, "combineMerge start")
        val diffFolderRecords = folderNewOrDelete.filter { folderBean ->
            !encryptFolderNewOrDelete.any { encryptFolderBean -> folderBean.mFolderGuid == encryptFolderBean.mFolderGuid }
        }

        val diffEncryptFoldersRecords = encryptFolderNewOrDelete.filter { encryptFolderBean ->
            !folderNewOrDelete.any { folderBean -> encryptFolderBean.mFolderGuid == folderBean.mFolderGuid }
        }
        AppLogger.BASIC.d(TAG, "diffFolderRecords:${diffFolderRecords.map { it.mFolderGuid }}")
        AppLogger.BASIC.d(TAG, "diffEncryptFoldersRecords:${diffEncryptFoldersRecords.map { it.mFolderGuid }}")

        mergerHelper.onRecoveryFolders(diffFolderRecords, true)
        mergerHelper.onRecoveryFolders(diffEncryptFoldersRecords, true)

        folderNewOrDelete.removeAll(diffFolderRecords)
        encryptFolderNewOrDelete.removeAll(diffEncryptFoldersRecords)

        /**
         * 此时剩余两个列表数量应该一致，为需要联合解冲突部分；
         */
        AppLogger.BASIC.d(TAG, "onCombineRecoverFolders:$folderNewOrDelete")
        folderNewOrDelete.forEach { cloudFolder ->
            val matchEncrypt =
                    encryptFolderNewOrDelete.find { it.mFolderGuid == cloudFolder.mFolderGuid }
            if ((cloudFolder.mState == FolderInfo.FOLDER_STATE_UNCHANGE) && (matchEncrypt?.mState == FolderInfo.FOLDER_STATE_DELETED)) {
                AppLogger.BASIC.d(TAG, "decrypt folder :${cloudFolder.mFolderGuid}")
                mergerHelper.onCombineRecoverFolders(cloudFolder, matchEncrypt)
            } else if ((cloudFolder.mState == FolderInfo.FOLDER_STATE_DELETED) && (matchEncrypt?.mState == FolderInfo.FOLDER_STATE_UNCHANGE)) {
                AppLogger.BASIC.d(TAG, "encrypt folder :${cloudFolder.mFolderGuid}")
                mergerHelper.onCombineRecoverFolders(cloudFolder, matchEncrypt)
            } else if ((cloudFolder.mState == FolderInfo.FOLDER_STATE_DELETED) && (matchEncrypt?.mState == FolderInfo.FOLDER_STATE_DELETED)) {
                AppLogger.CLOUDKIT.d(TAG, "delete folder operation")
                mergerHelper.onRecoveryFolders(listOf(cloudFolder), true)
            } else {
                /**
                 * 理论上不可能出现加密表和主表数据都为unChanged状态至少有一端为delete
                 */
                AppLogger.CLOUDKIT.w(TAG, "both unchanged :${cloudFolder.mFolderGuid}")
            }
        }
    }

    fun resetCache() {
        if (cacheSize.get() == 2) {
            cacheSize.set(1)
        }
    }

    companion object {
        const val CACHE_LIST_SIZE = 2
        private const val TAG = "EncryptDecryptFolderHelper"
    }
}