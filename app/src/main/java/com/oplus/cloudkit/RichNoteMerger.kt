/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - RichNoteMerger
 ** Description:
 **         v1.0:   Create RichNoteMerger file
 **
 ** Version: 1.0
 ** Date: 2023/11/11
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/11/11   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import android.text.SpannableStringBuilder
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.isSync
import com.nearme.note.thirdlog.AigcSPUtilHelper
import com.nearme.note.thirdlog.TYPE_NONE
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.cloud.sync.richnote.strategy.RichNoteConditionJudgeStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteConflictStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteDeleteConflictStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteDeleteStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteEncryptJudgeStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteNewStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteSameContentStrategy
import com.oplus.cloud.sync.richnote.strategy.RichNoteUpdateStrategy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.RichNoteTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.view.CloudKitInfoController
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.entity.ImageFormat
import com.oplus.richtext.core.node.MediaNode
import com.oplus.richtext.core.node.SpannedNode
import com.oplus.richtext.core.parser.HtmlParser
import com.oplus.richtext.core.parser.HtmlStandardParser
import com.oplus.richtext.transform.manager.HtmlTransformManagerFactory
import okio.ByteString.Companion.encode
import java.util.concurrent.atomic.AtomicInteger

class RichNoteMerger {

    internal var hasNewData = false

    private val updateMergers by lazy {
        mutableListOf(
            RichNoteConditionJudgeStrategy(),
            RichNoteEncryptJudgeStrategy(),
            RichNoteNewStrategy(),
            RichNoteSameContentStrategy(),
            RichNoteConflictStrategy(),
            RichNoteUpdateStrategy(),
        )
    }

    private val deleteMergers by lazy {
        mutableListOf(
            RichNoteDeleteStrategy(),
            RichNoteDeleteConflictStrategy()
        )
    }

    private val encryptUpdateMergers by lazy {
        mutableListOf(
            RichNoteConditionJudgeStrategy(),
            RichNoteEncryptJudgeStrategy(),
            RichNoteNewStrategy(),
            RichNoteSameContentStrategy(),
            RichNoteConflictStrategy(),
            RichNoteUpdateStrategy(),
        )
    }

    private val encryptDeleteMergers by lazy {
        mutableListOf(
            RichNoteDeleteStrategy(),
            RichNoteDeleteConflictStrategy()
        )
    }

    private var edrHelper: EncryptDecryptRichNoteHelper? = null
    private val richNotesEncryptStateChange = mutableListOf<Triple<Int, String, Long>>()
    private val transformer = RichNoteTransformer()
    private val initBuffer = AtomicInteger(BUFFER_SIZE)

    fun initEncryptDecryptRichNoteHelper() {
        if (initBuffer.decrementAndGet() == 0) {
            AppLogger.BASIC.d(TAG, "startRecovery :initEncryptDecryptRichNoteHelper $initBuffer ")
            edrHelper = EncryptDecryptRichNoteHelper()
            edrHelper?.init()
            richNotesEncryptStateChange.clear()
            initBuffer.set(BUFFER_SIZE)
        } else {
            AppLogger.BASIC.d(TAG, "startRecovery :$initBuffer")
        }
    }

    fun onRecoveryEnd(backUp: () -> Unit) {
        edrHelper?.onRecoveryEnd(backUp)
    }


    @Suppress("LongMethod")
    fun recoverRichNotes(
        data: List<CloudMetaDataRecordProxy>?,
    ) {
        if (!hasNewData) {
            hasNewData = !data.isNullOrEmpty()
        }

        var deleteCount = 0
        var updateCount = 0
        var encryptedCount = 0
        var decryptedCount = 0
        data?.forEach { record ->
            /**
             * First check that the notebook for this note has synchronization turned on
             */
            val remote = transformer.convertToRichNoteFrom(record)
            remote?.richNote?.localId?.let { noteId ->
                if (AigcSPUtilHelper.getRecreateType(noteId) != TYPE_NONE) {
                    AigcSPUtilHelper.removeRecreateType(noteId)
                }
            }
            val sync = remote?.isSync()

            if (sync == false) {
                AppLogger.CLOUDKIT.d(TAG, "Folder ${record.sysRecordId} sync switch is off")
                return@forEach
            }

            val recordType = record.sysRecordType

            if (record.sysStatus == Constants.RECORD_STATUS_DELETED &&
                remote?.richNote?.encrypted == 1 &&
                recordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
                /**
                 * 加密笔记
                 */
                encryptedCount++
                val relate = RichNoteRepository.queryByGlobalId(record.sysRecordId)
                if (relate != null) {
                    mergeWhenEncrypt(remote, relate)
                }
            } else if (record.sysStatus == Constants.RECORD_STATUS_DELETED &&
                remote?.richNote?.encrypted == 0 &&
                recordType == Constants.RECORD_TYPE_ENCRYPT_RICH_NOTE_ITEM) {
                /**
                 * 解密笔记
                 */
                decryptedCount++
                val relate = RichNoteRepository.queryByGlobalId(record.sysRecordId)
                if (relate != null) {
                    mergeWhenDecrypt(remote, relate)
                }
            } else if (shouldDelete(remote, record, recordType)) {
                /**
                 *彻底删除非加密笔记/彻底删除加密笔记
                 */
                deleteCount++
                val relate = RichNoteRepository.queryByGlobalId(record.sysRecordId)
                if (relate != null) {
                    AppLogger.CLOUDKIT.d(TAG, "delete directly: ${remote?.richNote?.globalId}, ${relate.richNote.rawTitle?.encode()?.base64()}")
                    remote?.let { mergeWhenDelete(it, relate, record.sysRecordType) }
                }
            } else {
                updateCount++
                // 新增或修改（包括删除到回收站）
                if (remote != null) {
                    val relate = RichNoteRepository.queryByLocalId(remote.richNote.localId)
                    mergeWhenUpdate(remote, relate, record.sysRecordType)
                    val hasAlarm = remote.richNote.alarmTime > System.currentTimeMillis()
                    val isDelete = remote.richNote.recycleTime != 0L
                    val isCreate = relate == null && hasAlarm
                    val isUpdate = relate != null && !isDelete && hasAlarm
                    AppLogger.CLOUDKIT.d(TAG, " isCreate = $isCreate isUpdate $isUpdate ")
                    if (isCreate || isUpdate) {
                        CloudKitInfoController.isNotifyIgnore = false
                        CloudKitInfoController.isAlarmIgnore = false
                        CloudKitInfoController.isScreenOnIgnore = false
                        CloudKitInfoController.isOverlayIgnore = false
                        CloudKitInfoController.isDiffIgnore = false
                    }
                }
            }
        }
        flush(data?.firstOrNull()?.sysRecordType)

        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd: $updateCount, $deleteCount")
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd: encrypt:$encryptedCount, decrypt:$decryptedCount")
    }

    /**
     * @return true means the given remote data handle by a valid merge strategy.
     * @param sysRecordType record type of remote data
     */
    fun mergeWhenUpdate(
        remote: RichNoteWithAttachments,
        relate: RichNoteWithAttachments?,
        sysRecordType: String
    ): Boolean {
        if (sysRecordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
            updateMergers.forEach { strategy ->
                strategy.setSysRecordType(sysRecordType)
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
        } else {
            encryptUpdateMergers.forEach { strategy ->
                strategy.setSysRecordType(sysRecordType)
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * @return true means the given remote data handle by a valid merge strategy.
     */
    fun mergeWhenDelete(
        remote: RichNoteWithAttachments,
        relate: RichNoteWithAttachments?,
        sysRecordType: String
    ): Boolean {
        if (sysRecordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
            deleteMergers.forEach { strategy ->
                strategy.setSysRecordType(sysRecordType)
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
        } else {
            encryptDeleteMergers.forEach { strategy ->
                strategy.setSysRecordType(sysRecordType)
                if (strategy.merge(remote, relate)) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 主表sysStatus 为 1，且encrypted = 1的数据，为加密数据，
     * 此数据在RichNoteMerger只更新sysVersion
     * 不更新数据，以EncryptedRichNoteSyncManager recover同步的数据为准解冲突
     */
    private fun mergeWhenEncrypt(
        remote: RichNoteWithAttachments,
        relate: RichNoteWithAttachments
    ) {
       relate.richNote.sysVersion = remote.richNote.sysVersion
       RichNoteRepository.update(relate.richNote)
    }
    /**
     * 加密表sysStatus 为 1，且encrypted = 0的数据，为解密数据，
     * 此数据在RichNoteMerger只更新encryptSysVersion
     * 不更新数据，以RichNoteSyncManager recover同步的数据为准解冲突
     */
    private fun mergeWhenDecrypt(
        remote: RichNoteWithAttachments,
        relate: RichNoteWithAttachments
    ) {
        relate.richNote.encryptSysVersion = remote.richNote.encryptSysVersion
        RichNoteRepository.update(relate.richNote)
    }


    fun flush(sysRecordType: String?) {
        if (sysRecordType.isNullOrEmpty()) {
            AppLogger.CLOUDKIT.d(TAG, "flush richNote error, empty sysRecordType.")
            return
        }
        if (sysRecordType == Constants.RECORD_TYPE_RICH_NOTE_ITEM) {
            updateMergers.forEach { it.mergeDataListBuffer() }
            deleteMergers.forEach { it.mergeDataListBuffer() }
        } else {
            encryptUpdateMergers.forEach { it.mergeDataListBuffer() }
            encryptDeleteMergers.forEach { it.mergeDataListBuffer() }
        }
    }

    /**
     * 本地加解密时，同步上云时，会拆解出一条删除的元数据，此条元数据不能直接删除本地的数据，
     * 而是要更新对应的SysVersion并更新encrypted_pre字段
     */
    fun setRichNoteEncryptedChanged(
        isPending: Boolean = true,
        changedData: List<Triple<Int, String, Long>>?
    ) {
        AppLogger.CLOUD.d(TAG, "setRichNoteEncryptedChanged pending:$isPending, changeData:${changedData?.size}")
        if (isPending) {
            if (changedData.isNullOrEmpty()) {
                AppLogger.CLOUD.d(TAG, "no changedData")
            } else {
                richNotesEncryptStateChange.addAll(changedData)
            }
        } else {
            AppLogger.BASIC.d(TAG, "setRichNoteEncryptedChanged $richNotesEncryptStateChange")
            richNotesEncryptStateChange.filter {
                it.first == MAIN_CLOUD_ENCRYPT || it.first == MAIN_CLOUD_DECRYPT
            }.map {
                Pair(it.second, it.third)
            }.let {
                RichNoteRepository.updateRichNoteEncryptPreSysVersion(it)
            }

            richNotesEncryptStateChange.filter {
                it.first == ENCRYPT_CLOUD_DECRYPT || it.first == ENCRYPT_CLOUD_ENCRYPT
            }.map {
                Pair(it.second, it.third)
            }.let {
                RichNoteRepository.updateRichNoteEncryptPreEncryptSysVersion(it)
            }

            richNotesEncryptStateChange.clear()
        }
    }


    private fun updateText(data: RichNoteWithAttachments) {
        val textOut = StringBuilder()
        val htmlText = HtmlTransformManagerFactory.gainHtmlTransformManager().toHtmlText(data.richNote.rawText)
        val itemNodeList = HtmlStandardParser.fromHtml(source = htmlText, shouldSkipTidying = true)
        itemNodeList.forEach { iItemNode ->
            when (iItemNode) {
                is SpannedNode -> {
                    if (iItemNode.type == SpannedNode.TYPE_TEXT) {
                        textOut.append(SpannableStringBuilder(iItemNode.data))
                    } else if (iItemNode.type == SpannedNode.TYPE_CARD) {
                        /**
                         * do nothing
                         */
                        AppLogger.DEBUG.d(TAG) { "TYPE_CARD" }
                    } else {
                        textOut.append(SpannableStringBuilder(iItemNode.data))
                    }
                }

                is MediaNode -> {}
            }
        }
        if (textOut.isNullOrEmpty()) {
            data.richNote.text = HtmlParser.parse(data.richNote.rawText).text.filterNot { it == ImageFormat.PLACE_HOLDER_CHARACTER }
        } else {
            data.richNote.text = textOut.toString()
        }
        if (data.richNote.rawTitle != null) {
            data.richNote.title = HtmlParser.parse(data.richNote.rawTitle!!).text
        }
    }

    private fun shouldDelete(
        remote: RichNoteWithAttachments?,
        record: CloudMetaDataRecordProxy,
        recordType: String
    ): Boolean {
        return (record.sysStatus == Constants.RECORD_STATUS_DELETED && remote?.richNote?.encrypted == 0) ||
                (record.sysStatus == Constants.RECORD_STATUS_DELETED &&
                        remote?.richNote?.encrypted == 1 &&
                        recordType == Constants.RECORD_TYPE_ENCRYPT_RICH_NOTE_ITEM)
    }

    companion object {
        private const val TAG = "RichNoteMerger"
        private const val BUFFER_SIZE = 2
        const val MAIN_CLOUD_ENCRYPT = 0
        const val MAIN_CLOUD_DECRYPT = 1
        const val ENCRYPT_CLOUD_ENCRYPT = 2
        const val ENCRYPT_CLOUD_DECRYPT = 3
    }
}