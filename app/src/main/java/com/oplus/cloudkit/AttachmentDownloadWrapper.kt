/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AttachmentDownloadWrapper
 ** Description:
 **         v1.0:   Create AttachmentDownloadWrapper file
 **
 ** Version: 1.0
 ** Date: 2024/05/15
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2024/5/15   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication
import com.nearme.note.util.ThumbnailUtils
import com.oplus.cloud.agent.note.mkdirsAndGetParent
import com.oplus.cloud.utils.MD5Utils
import com.oplus.cloudkit.lib.CloudIOFileProxy
import com.oplus.cloudkit.lib.CloudKitErrorProxy
import com.oplus.cloudkit.transformer.AttachmentTransformer
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import java.io.File
import java.net.URI

class AttachmentDownloadWrapper(
    val transformer: AttachmentTransformer,
    val updateAttachments: MutableList<Attachment>
) {
    companion object {
        private const val TAG = "AttachmentDownloadWrapper"
    }

    @get:VisibleForTesting
    val downloader by lazy {
        LegacyFileDownloader()
    }

    fun downloadAttachment(
        attachment: Attachment,
        downloadInvoker: ((Boolean) -> Unit)
    ) {
        attachment.url?.let {
            if (checkIsLegacy(it)) {
                downloadLegacyFile(attachment, downloadInvoker)
            } else {
                downloadOFile(attachment, downloadInvoker)
            }
        } ?: kotlin.run {
            AppLogger.CLOUDKIT.w(TAG, "url is empty.")
            downloadInvoker.invoke(false)
            return
        }
    }

    @VisibleForTesting
    fun downloadOFile(
        attachment: Attachment,
        downloadInvoker: (Boolean) -> Unit
    ) {

        // 1.init download environment
        val oFile = transformer.convertToCloudIOFileForDownloadFrom(attachment)
        val folder = mkdirsAndGetParent(oFile.filePath)
        if (folder == null) {
            AppLogger.CLOUDKIT.e(TAG, "Can not create folder:[$folder]!")
            downloadInvoker.invoke(false)
            return
        }
        // 2.make a file download.
        CloudKitSdkManager.transferFile(oFile, object : CloudIOFileListenerProxy {
            override fun onFinish(
                file: CloudIOFileProxy?,
                cloudKitError: CloudKitErrorProxy?
            ) {
                kotlin.runCatching {
                    AppLogger.CLOUDKIT.d(
                        TAG,
                        "download finished with ${attachment.attachmentId}: ${cloudKitError?.isSuccess}"
                    )
                    if (cloudKitError?.isSuccess == true && file != null) {

                        val start = System.currentTimeMillis()
                        File(URI(file.cacheUri)).copyTo(File(file.filePath), overwrite = true)
                        AppLogger.CLOUDKIT.d(
                            TAG, "download finished with: ${System.currentTimeMillis() - start}"
                        )

                        attachment.md5 = MD5Utils.getMD5(File(file.filePath))
                        if (attachment.type == Attachment.TYPE_PICTURE) {
                            val bitmap = ThumbnailUtils.decodeFile(file.filePath)
                            bitmap?.apply {
                                attachment.picture = Picture(this.width, this.height)
                                this.recycle()
                            }
                        }
                        attachment.state = Attachment.STATE_UNCHANGE
                        updateAttachments.add(attachment)
                    }
                }
                downloadInvoker.invoke(true)
            }
        })
    }

    @VisibleForTesting
    fun downloadLegacyFile(
        attachment: Attachment,
        downloadInvoker: (Boolean) -> Unit
    ) {
        if (attachment.url.isNullOrEmpty()) {
            AppLogger.CLOUDKIT.d(TAG, "url is null")
            downloadInvoker.invoke(false)
            return
        }
        val folder = mkdirsAndGetParent(attachment.absolutePath(MyApplication.appContext))
        if (folder == null) {
            AppLogger.CLOUDKIT.e(TAG, "Can not create folder:[$folder]!")
            downloadInvoker.invoke(false)
            return
        }
        downloader.downloadFile(attachment, updateAttachments, downloadInvoker)
    }


    @VisibleForTesting
    fun checkIsLegacy(url: String): Boolean {
        val regexPattern = "^/minus/.*".toRegex()
        return regexPattern.matches(url)
    }
}