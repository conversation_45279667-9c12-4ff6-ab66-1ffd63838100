/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AttachmentDownloader
 ** Description:
 **         v1.0:   download file
 **
 ** Version: 1.0
 ** Date: 2024/05/15
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2024/5/15   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.nearme.note.MyApplication
import com.nearme.note.util.ThumbnailUtils
import com.oplus.cloud.utils.MD5Utils
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.oplus.note.scenecard.BuildConfig.LEGACY_FILE_PREFIX
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.Buffer
import java.io.File
import java.io.FileOutputStream
import java.nio.charset.Charset
import java.util.concurrent.TimeUnit

class LegacyFileDownloader {
    companion object {
        private const val TAG = "LegacyFileDownloader"
        private var client = OkHttpClient.Builder()
            .addInterceptor(Interceptor { chain ->
                val request = chain.request()
                printRequest(request)
                chain.proceed(request)
            })
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        @VisibleForTesting
        @JvmStatic
        fun printRequest(request: Request) {
            var msg =
                "Request\nurl=${request.url}\nmethod=${request.method}\nheaders=${request.headers}body="
            val buffer = Buffer()
            request.body?.let {
                it.writeTo(buffer)
                msg += buffer.readString(Charset.forName("UTF-8"))
            }
        }

        fun getClient(): OkHttpClient {
            return client
        }
    }

    fun downloadFile(
        attachment: Attachment,
        updateAttachments: MutableList<Attachment>,
        downloadInvoker: (Boolean) -> Unit?
    ) {
        val url = attachment.url
        val destPath = attachment.absolutePath(MyApplication.appContext)
        if (url.isNullOrEmpty()) {
            AppLogger.CLOUDKIT.w(TAG, "url is null")
            downloadInvoker.invoke(false)
            return
        }
        val amendUrl = if (TextUtils.isEmpty(LEGACY_FILE_PREFIX) || url.startsWith(LEGACY_FILE_PREFIX)) {
                url
            } else {
                "$LEGACY_FILE_PREFIX$url"
            }
        val request = Request.Builder()
            .url(amendUrl)
            .build()

        val outFile = File(destPath)
        if (outFile.exists()) {
            outFile.delete()
        }
        if (outFile.parentFile?.exists() == false) {
            outFile.mkdirs()
        }
        val start = System.currentTimeMillis()
        kotlin.runCatching {
            getClient().newCall(request)
                .execute()
                .use { response ->
                    if (!response.isSuccessful) {
                        AppLogger.CLOUDKIT.d(TAG, "response failed.${response.message}")
                        downloadInvoker.invoke(false)
                        return
                    }

                    response.body?.byteStream()
                        ?.use { inputStream ->
                            FileOutputStream(outFile).use { outputStream ->
                                inputStream.copyTo(outputStream)
                            }
                        }
                    AppLogger.CLOUDKIT.d(
                        TAG, "download finished with: ${System.currentTimeMillis() - start}"
                    )
                    attachment.md5 = MD5Utils.getMD5(outFile)
                    if (attachment.type == Attachment.TYPE_PICTURE) {
                        val bitmap = ThumbnailUtils.decodeFile(outFile.path)
                        bitmap?.apply {
                            attachment.picture = Picture(this.width, this.height)
                            this.recycle()
                        }
                    }
                    attachment.state = Attachment.STATE_UNCHANGE
                    updateAttachments.add(attachment)
                    downloadInvoker.invoke(true)
                }
        }.onFailure {
            AppLogger.CLOUDKIT.e(TAG, "execute ${request.url.host} failed, ${it.message}")
        }
    }
}