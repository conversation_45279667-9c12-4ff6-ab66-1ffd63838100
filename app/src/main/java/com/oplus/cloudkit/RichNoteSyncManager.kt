/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: NoteSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/6
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import android.text.TextUtils
import com.nearme.note.MyApplication
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.db.FolderUtil
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.isDecryptLocal
import com.nearme.note.model.isDecryptNote
import com.nearme.note.model.isEncryptLocal
import com.nearme.note.model.isSync
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.AttachmentTransformer
import com.oplus.cloudkit.transformer.RichNoteTransformer
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.note.entity.Folder
import java.util.UUID

open class RichNoteSyncManager(
    zone: String = Constants.ZONE_RICH_NOTE,
    recordTypeVersion: Int = Constants.RECORD_TYPE_VERSION_RICH_NOTE_ITEM
) : AbsDataSyncManager(Constants.MODULE_NOTE, zone, recordTypeVersion) {

    companion object {
        //backup时，一次传太多数据会导致包体过大，http直接拒绝
        private const val NOTE_BACKUP_PAGE_SIZE = 10
    }

    open fun tag(): String = "RichNoteSyncManager"
    protected val transformer = RichNoteTransformer()
    private var mergerHelper: RichNoteMerger? = null
    private val localEncryptRecordIds = mutableSetOf<String>()
    private val localDecryptRecordIds = mutableSetOf<String>()

    private val asm by lazy {
        AttachmentSyncManager(AttachmentTransformer(MyApplication.application))
    }

    open fun setRichNoteMerger(mergerHelper: RichNoteMerger) {
        this.mergerHelper = mergerHelper
    }

    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(tag(), "onStartRecovery")

        // sync attachments
        mergerHelper?.initEncryptDecryptRichNoteHelper()
        asm.sync()
    }

    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(tag(), "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(tag(), "onPagingRecoveryEnd：${data?.size}")
        mergerHelper?.recoverRichNotes(data)
    }

    override fun getMetaDataCount(): Int {
        return RichNoteRepository.queryAllNotesCount() - RichNoteRepository.getCountOf(RichNote.STATE_NEW)
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(tag(), "onRecoveryEnd")
        mergerHelper?.onRecoveryEnd(backUp)
    }

    override fun onRecoverError(backUp: () -> Unit) {
        AppLogger.CLOUDKIT.d(tag(), "onRecoverError")
        onRecoveryEnd(backUp)
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(tag(), "onStartBackup")

        localEncryptRecordIds.clear()
        localDecryptRecordIds.clear()
        if (mergerHelper?.hasNewData == true) {
            mergerHelper?.hasNewData = false
            WidgetUtils.sendNoteDataChangedBroadcast(MyApplication.appContext)
            NoteCardWidgetProvider.instance.postUIToCard(false)
        }

        // sync attachments
        asm.sync()

        dirtyRichNotes = null
        startBackupTimestamp = 0L
    }

    internal var dirtyRichNotes: List<RichNoteWithAttachments>? = null
    private var startBackupTimestamp = 0L

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {

        dirtyRichNotes = RichNoteRepository.queryDirtyData().filter {
            /**
             * 非加密笔记及本地加密笔记
             * 非加密笔记 保存更改到主表
             * 加密笔记从主表删除，并保存到加密表
             */

            it.isSync() && (isEncryptNotNew(it) || it.isDecryptNote())
        }
        AppLogger.CLOUDKIT.d(tag(), "onQueryDirtyData:${dirtyRichNotes?.size}")
        correctLegacyGlobalIds(dirtyRichNotes)
        correctEncryptChanges(dirtyRichNotes)
        return dirtyRichNotes?.mapNotNull {
            if (isEncryptNotNew(it)) {
                localEncryptRecordIds.add(it.richNote.localId)
                AppLogger.CLOUDKIT.d(tag(), "localEncryptRecordIds add: ${it.richNote.localId}")
            } else if (it.isDecryptLocal()) {
                localDecryptRecordIds.add(it.richNote.localId)
                AppLogger.CLOUDKIT.d(tag(), "localDecryptRecordIds add: ${it.richNote.localId}")
            }
            transformer.convertRichNoteToRecordFrom(it)
        } ?: emptyList()
    }

    override fun getBackUpPageSize(): Int {
        return NOTE_BACKUP_PAGE_SIZE
    }
    /**
     * 笔记可以直接创键加密的
     */
    private fun isEncryptNotNew(note: RichNoteWithAttachments): Boolean {
        return note.isEncryptLocal() && note.richNote.state != RichNote.STATE_NEW
    }

    /**
     * There are some legacy data has no global id when update to Cloudkit version.
     * Add the global id here
     */
    protected fun correctLegacyGlobalIds(data: List<RichNoteWithAttachments>?) {
        val legacy = data?.filter {
            val emptyGlobalId = it.richNote.globalId.isNullOrBlank()
            if (emptyGlobalId) {
                it.richNote.globalId = UUID.randomUUID().toString()
            }
            emptyGlobalId
        }

        if (!legacy.isNullOrEmpty()) {
            RichNoteRepository.updateList(legacy, false)
        }
    }

    protected fun correctEncryptChanges(data: List<RichNoteWithAttachments>?) {
        /**
         * 这里存在一些极端场景，如一次同步过程中，笔记本在解冲突时，笔记本从加密变为加密，但之前操作笔记时仍为加密到这个笔记本，需要修正，解密同理
         */
        val folderDecrypt = data?.filter {
            val isChanged =
                it.isEncryptLocal() && FolderUtil.getFolderEncrypt(it.richNote.folderGuid) == Folder.FOLDER_UNENCRYPTED
            if (isChanged) {
                it.richNote.encrypted = Folder.FOLDER_UNENCRYPTED
            }
            isChanged
        }
        val folderEncrypt = data?.filter {
            val isChanged =
                it.isDecryptLocal() && FolderUtil.getFolderEncrypt(it.richNote.folderGuid) == Folder.FOLDER_ENCRYPTED
            if (isChanged) {
                it.richNote.encrypted = Folder.FOLDER_ENCRYPTED
            }
            isChanged
        }

        if (!folderEncrypt.isNullOrEmpty()) {
            RichNoteRepository.updateList(folderEncrypt, false)
        }
        if (!folderDecrypt.isNullOrEmpty()) {
            RichNoteRepository.updateList(folderDecrypt, false)
        }

        AppLogger.CLOUDKIT.d(tag(), "correctEncryptChanges: ${folderDecrypt?.size} ${folderEncrypt?.size}")
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(tag(), "onPagingBackupStart:${data?.size}")
        startBackupTimestamp = System.currentTimeMillis()
    }

    override fun onPagingBackupEnd(
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ) {
        /** 监听同步过程中备份数据是否有修改方案：
         * 1、备份开始时记录当前时间：startBackupTimestamp,
         * 2、备份结束时，查询数据库中 updateTime大于 startBackupTimestamp的脏数据：changedDirtyData，
         * 3、备份结束更新数据状态时，过滤掉changedDirtyData
         * 4、加密的数据，需要从云端主表删除，因此删除的数据要区分实际删除or加密，加密的数据不要从本地删除而是更新其version
         **/
        val changedDirtyData = RichNoteRepository.queryChangedDirtyData(startBackupTimestamp)

        val encrypt = mutableListOf<Triple<Int, String, Long>>()
        val decrypt = mutableListOf<Triple<Int, String, Long>>()

        successData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                    record.operatorType == Constants.OPERATOR_TYPE_DELETE
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId }?.apply {
                if (!localEncryptRecordIds.contains(this.richNote.localId)) {
                    RichNoteRepository.delete(this)
                } else {
                    encrypt.add(Triple(RichNoteMerger.MAIN_CLOUD_ENCRYPT, this.richNote.localId, record.sysVersion))
                }
            }
        }

        mergerHelper?.setRichNoteEncryptedChanged(changedData = encrypt)

        //单独处理，本地已经彻底删除的笔记数据第一次同步到云端的场景, 服务端返回 1104 SYS_VERSION_ISNULL
        val deletedNoteList = arrayListOf<String>()
        errorData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                    record.subServerErrorCode == ERROR_CODE_SYS_VERSION_ISNULL
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId }?.apply {
                if (richNote.deleted) {
                    if (!TextUtils.isEmpty(richNote.globalId)) {
                        deletedNoteList.add(richNote.globalId!!)
                    }
                    RichNoteRepository.delete(this)
                }
            }
        }

        //处理 1200 exist 报错，云端已存在但本地数据是 new 状态, 将状态改成 modify 状态
        errorData?.filter { record ->
            record.subServerErrorCode == ERROR_CODE_1200_EXIST
        }?.forEach { record ->
            dirtyRichNotes?.find { record.sysRecordId == it.richNote.globalId && it.richNote.state == RichNote.STATE_NEW }?.let {
                it.richNote.state = RichNote.STATE_MODIFIED
                RichNoteRepository.updateWithNoTimestamp(it.richNote)
            }
        }

        successData?.filter { record ->
            changedDirtyData.find { it.richNote.globalId == record.sysRecordId } == null &&
                    record.operatorType != Constants.OPERATOR_TYPE_DELETE
        }?.mapNotNull { record ->
            dirtyRichNotes?.find {
                record.sysRecordId == it.richNote.globalId
                        && !deletedNoteList.contains(it.richNote.globalId)
            }?.apply {
                if (localDecryptRecordIds.contains(this.richNote.localId)) {
                    decrypt.add(Triple(RichNoteMerger.MAIN_CLOUD_DECRYPT, this.richNote.localId, record.sysVersion))
                    richNote.alarmTimePre = richNote.alarmTime
                    richNote.recycleTimePre = richNote.recycleTime
                    richNote.skinIdPre = richNote.skinId
                } else {
                    richNote.sysVersion = record.sysVersion
                    richNote.state = RichNote.STATE_UNCHANGE

                    richNote.alarmTimePre = richNote.alarmTime
                    richNote.recycleTimePre = richNote.recycleTime
                    richNote.skinIdPre = richNote.skinId
                }
            }
        }?.apply {
            RichNoteRepository.updateList(this, false)
        }
        mergerHelper?.setRichNoteEncryptedChanged(changedData = decrypt)
        // 待处理： resolve error records;彻底删除的数据在云端保存6个月，之后不再下发。假如本地这条数据6个月都没有同步过，backup应该会报错
    }
}