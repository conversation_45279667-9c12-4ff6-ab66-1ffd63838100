/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/6
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import android.app.Application
import android.os.Build
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.nearme.note.MyApplication
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.logic.AccountManager
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.NetworkUtils
import com.nearme.note.util.PrivacyPolicyHelper
import com.oplus.cloud.sync.note.NoteSyncViewModel
import com.oplus.cloudkit.util.CloudKitSyncStatus
import com.oplus.cloudkit.util.Constants
import com.oplus.cloudkit.util.GetSyncSwitchListener
import com.oplus.cloudkit.util.SyncSwitchStateRepository
import com.oplus.cloudkit.view.CloudKitSyncGuidManager
import com.oplus.note.BuildConfig
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.ControlledRunner
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Collections
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Consumer

/**
 * Then entry of note and to-do data sync.
 */
object SyncManager {

    private const val TAG = "SyncManager"
    private const val FOLDER_MANAGER_COUNT = 2

    private val folderMerger by lazy {
        FolderMerger(MyApplication.appContext)
    }

    private val richNoteMerger by lazy {
        RichNoteMerger()
    }

    private val folderSyncFlag = AtomicInteger(FOLDER_MANAGER_COUNT)

    /** Execute 'note' cloud sync serially: folder -> encrypted folder -> rich note -> encrypted note -> note.
     *  必须等两个folderManager同步完，才可以开始richNote的同步，否则会出现时序问题
     */
    private val fsm by lazy {
        FolderSyncManager().apply fsm@{
            setFolderMerger(folderMerger)
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    if (syncStatus == CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS) {
                        recordFsFlag(this@fsm)
                    } else {
                        sendSyncResult(syncStatus)
                    }
                }
            })
        }
    }

    private val efsm by lazy {
        EncryptFolderSyncManager().apply efsm@{
            setFolderMerger(folderMerger)
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    if (syncStatus == CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS) {
                        recordFsFlag(this@efsm)
                    } else {
                        sendSyncResult(syncStatus)
                    }
                }
            })
        }
    }

    private fun recordFsFlag(folderSyncManager: FolderSyncManager) {
        if (folderSyncFlag.decrementAndGet() == 0) {
            rsm.startSync()
            ersm.startSync()
            folderSyncFlag.set(FOLDER_MANAGER_COUNT)
            AppLogger.BASIC.d(TAG, "richNote and encryptNote sync start")
        } else {
            AppLogger.BASIC.d(TAG, "$folderSyncManager sync finished")
        }
    }

    private val rsm by lazy {
        RichNoteSyncManager().apply {
            setRichNoteMerger(richNoteMerger)
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    if (syncStatus == CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS) {
                        nsm.startSync()
                    } else {
                        sendSyncResult(syncStatus)
                    }
                }
            })
        }
    }

    private val ersm by lazy {
        EncryptRichNoteSyncManager().apply {
            setRichNoteMerger(richNoteMerger)
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    sendSyncResult(syncStatus)
                }
            })
        }
    }

    private val nsm by lazy {
        NoteSyncManager(NoteSyncViewModel(MyApplication.appContext)).apply {
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    sendSyncResult(syncStatus)
                }
            })
        }
    }

    private val tsm by lazy {
        TodoSyncManager().apply {
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    sendSyncResult(syncStatus)
                }
            })
        }
    }

    private val ssm by lazy {
        SettingSyncManager(MyApplication.appContext).apply {
            setSyncListener(listener = object : AbsDataSyncManager.SyncListener {
                override fun onSyncFinished(syncStatus: CloudKitSyncStatus) {
                    sendSyncResult(syncStatus)
                }
            })
        }
    }

    /**
     * use join task strategy:
     * https://medium.com/androiddevelopers/coroutines-on-android-part-iii-real-work-2ba8a2ec2f45
     */
    @get:VisibleForTesting
    val controlledRunner = ControlledRunner<Boolean>()

    fun sync(dispatcher: CoroutineDispatcher = Dispatchers.IO) {
        if (!ConfigUtils.isUseCloudKit) {
            return
        }

        if (isRunning()) {
            return
        }

        AppLogger.CLOUDKIT.d(TAG, "start sync isExport=${BuildConfig.isExport} isOnePlus=${BuildConfig.isOnePlus}")
        updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_SYNCING)

        GlobalScope.launch(dispatcher) {
            controlledRunner.joinPreviousOrRun {
                syncInternal(this)
                return@joinPreviousOrRun true
            }
        }
    }

    private fun syncInternal(context: CoroutineScope) {
        resultCodes.clear()
        folderSyncFlag.set(FOLDER_MANAGER_COUNT)

        if (!SyncSwitchStateRepository.checkService()) return

        context.launch {
            startRichNoteSync()
        }
        context.launch {
            startEncryptSync()
        }
        context.launch {
            startTodoSync()
        }
        context.launch {
            startSettingSync()
        }
    }

    private fun startRichNoteSync() {
        // Execute 'note' cloud sync serially: folder -> rich note -> note.
        fsm.startSync()
    }

    private fun startEncryptSync() {
        efsm.startSync()
    }

    private fun startTodoSync() {
        // 待办已废弃，不需要进行云同步
        if (ConfigUtils.isToDoDeprecated) {
            return
        }
        tsm.startSync()
    }

    private fun startSettingSync() {
        ssm.startSync()
    }

    fun isRunning(): Boolean {
        return fsm.isRunning || rsm.isRunning || nsm.isRunning || tsm.isRunning || ssm.isRunning || efsm.isRunning || ersm.isRunning
    }

    fun resetRunningState() {
        if (isRunning()) {
            fsm.isRunning = false
            rsm.isRunning = false
            nsm.isRunning = false
            tsm.isRunning = false
            ssm.isRunning = false
            efsm.isRunning = false
            ersm.isRunning = false

            sendSyncResult(CloudKitSyncStatus.SYNC_CODE_FINISHED_INTERRUPTED)
        }
    }

    private val resultCodes = CopyOnWriteArrayList<Int>()

    fun sendSyncResult(syncStatus: CloudKitSyncStatus) {
        kotlin.runCatching {
            resultCodes.add(syncStatus.priority)
            if (isRunning()) {
                return
            }

            AlarmUtils.resetAllSystemAlarms()
            updateSyncStateTracking(getRealCode())
        }.onFailure {
            AppLogger.CLOUDKIT.e(TAG, "sendSyncResult error: ${it.message}")
            updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL)
        }
    }

    // 获取权重最大的结束状态 code
    private fun getRealCode(): CloudKitSyncStatus {
        var realCode = CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS.priority
        for (code in resultCodes) {
            realCode = maxOf(realCode, code)
        }
        AppLogger.CLOUDKIT.d(TAG, "getRealCode: $realCode")
        return CloudKitSyncStatus.getSyncStatus(realCode)
    }

    private val syncTrackingLiveDataContainer =
        Collections.synchronizedSet(HashSet<MutableLiveData<CloudKitSyncStatus>>())

    fun updateSyncStateTracking(syncStatus: CloudKitSyncStatus) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            when (syncStatus) {
                CloudKitSyncStatus.SYNC_CODE_SYNCING -> {
                    BaseSyncManager.setSyncState(true)
                }

                else -> {
                    BaseSyncManager.setSyncState(false)
                    CoroutineScope(Dispatchers.IO).launch {
                        folderMerger.setFolderEncryptedChanged(false, null)
                        richNoteMerger.setRichNoteEncryptedChanged(false, null)
                        RichNoteRepository.updateSpeechAudioAssociateId()
                    }
                }
            }
        }

        syncTrackingLiveDataContainer.forEach(Consumer {
            if (it.hasActiveObservers()) {
                it.postValue(syncStatus)
            }
        })
    }

    /**
     * The elements in the livedata are one of [Constants].SYNC_CODE_XXX.
     */
    @MainThread
    fun observeSyncTracking(
        owner: LifecycleOwner,
        observer: Observer<CloudKitSyncStatus>
    ) {
        val tracking = SyncTrackingLiveData()
        if (isRunning()) {
            tracking.value = CloudKitSyncStatus.SYNC_CODE_SYNCING
        }
        tracking.observe(owner, observer)
    }

    private class SyncTrackingLiveData : MutableLiveData<CloudKitSyncStatus>() {

        override fun onActive() {
            super.onActive()
            syncTrackingLiveDataContainer.add(this)
            if (isRunning() && value == null) {
                updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_SYNCING)
            }
            if (!isRunning() && CloudKitSyncGuidManager.mLastSyncStatus?.isFinishStatus == false) {
                updateSyncStateTracking(getRealCode())
            }
        }

        override fun onInactive() {
            super.onInactive()
            syncTrackingLiveDataContainer.remove(this)
        }
    }

    /**
     * @param env 0 正式环境， 1 预发布环境， else 测试环境
     */
    fun init(
        context: Application,
        env: Int
    ) {
        if (!ConfigUtils.isUseCloudKit) {
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            BaseSyncManager.init(context, env, AccountManager.NOTE_APP_CODE, BuildConfig.isExport, BuildConfig.isOnePlus)
        }
        BaseSyncManager.setResetStateListener(object : SyncStateChangeListener {
            override fun onResetState() {
                resetRunningState()
            }

            override fun onReceivePushMessage() {
                NoteListHelper.startSynchronizeByCloudkit(false)
            }
        })
    }

    private val clearTaskControlledRunner = ControlledRunner<Unit>()

    fun clearSyncDataByLogout() {
        AppLogger.CLOUDKIT.d(TAG, "clearSyncDataByLogout")
        GlobalScope.launch(Dispatchers.IO) {
            clearTaskControlledRunner.joinPreviousOrRun {
                BaseSyncManager.clearSyncDataByLogout()
            }
        }
    }

    fun startSync(
        directSync: Boolean,
    ) {
        if (!PrivacyPolicyHelper.isAgreeUserNotice(appContext)) {
            return
        }
        if (!CloudKitGlobalStateManager.fetchGlobalStateSucceed()) {
            //未获取到全球一体化信息，直接提示错误
            updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_FINISHED_NO_GLOBAL_STATE)
            return
        }
        if (CloudKitGlobalStateManager.deviceOrAccountDisable()) {
            //设备、账号不可用，禁用云同步功能
            return
        }
        if (directSync) {
            GlobalScope.launch(Dispatchers.IO) {
                if (NetworkUtils.isWifiConnected(appContext)) {
                    sync(Dispatchers.IO)
                }
            }
            return
        }
        SyncSwitchStateRepository.querySwitchState(appContext, object : GetSyncSwitchListener {
            override fun onGetSyncSwitch(switchStateCode: Int) {
                GlobalScope.launch(Dispatchers.IO) {
                    if (switchStateCode == CloudKitSdkManager.OPEN_ALL_CODE) {
                        if (NetworkUtils.isNetworkConnected(appContext)) {
                            sync()
                        } else {
                            updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL)
                        }
                    } else if (switchStateCode == CloudKitSdkManager.OPEN_ONLY_WIFI_CODE) {
                        if (NetworkUtils.isWifiConnected(appContext)) {
                            sync()
                        } else {
                            updateSyncStateTracking(CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL)
                        }
                    }
                }
            }
        })
    }
}