/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - EncryptDecryptRichNoteHelper
 ** Description:
 **         v1.0:   Create EncryptDecryptRichNoteHelper file
 **
 ** Version: 1.0
 ** Date: 2023/11/11
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/11/11   1.0      Create this module
 ********************************************************************************/
package com.oplus.cloudkit

import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.util.Constants
import com.oplus.note.logger.AppLogger
import java.util.concurrent.atomic.AtomicInteger

class EncryptDecryptRichNoteHelper {

    private var cacheSize = AtomicInteger(CACHE_LIST_SIZE)
    private var noteMerger = RichNoteMerger()
    /**
     * 由于存在需要等待RichNoteSyncManager和EncryptRichNoteSyncManager recovery后解冲突的时候，需要
     * 暂时将这两个manger的backup暂停，等解完冲突，改好本地数据后，再触发它们的backup
     * 保存未触发的backup
     */
    private val pendingBackup = mutableListOf<(() -> Unit)?>()
    fun init() {
        AppLogger.BASIC.d(TAG, "init--")
        cacheSize.set(CACHE_LIST_SIZE)
    }


    fun onRecoveryEnd(afterCombine: () -> Unit) {
        synchronized(pendingBackup) {
            if (cacheSize.decrementAndGet() == 0) {
                /**
                 * 缓存完成，开始解冲突逻辑
                 * 解完冲突之后才可以开启FolderSyncManager及EncryptFolderSyncManager的backup
                 */
                cacheSize.set(CACHE_LIST_SIZE)
                pendingBackup.add(afterCombine)
                pendingBackup.forEach {
                    it?.invoke()
                }
                AppLogger.BASIC.d(TAG, "cache finish.")
            } else {
                AppLogger.BASIC.d(TAG, "cache waiting:${cacheSize.get()}")
                pendingBackup.add(afterCombine)
            }
        }
    }


    companion object {
        private const val CACHE_LIST_SIZE = 2
        private const val TAG = "EncryptDecryptRichNoteHelper"
    }
}