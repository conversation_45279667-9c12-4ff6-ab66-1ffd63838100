package com.oplus.cloudkit

import com.oplus.cloudkit.util.CloudKitSyncStatus

abstract class AbsDataSyncManager(module: String, zone: String, recordTypeVersion: Int) :
    AbsSyncManager(module, zone, recordTypeVersion) {

    init {
        setSyncFinishListener(object : SyncFinishListener{
            override fun onSyncFinish(
                isSuccess: <PERSON><PERSON><PERSON>,
                isSpace<PERSON>otEnough: <PERSON><PERSON><PERSON>,
                isInterrupted: <PERSON>olean
            ) {
                if (isSuccess) {
                    syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_SUCCESS)
                } else if (isSpace<PERSON>otEnough) {
                    syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_SPACE_NOT_ENOUGH)
                } else if (isInterrupted) {
                    syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_INTERRUPTED)
                } else {
                    syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_FAIL)
                }
            }

            override fun onSyncFinishWithBizError(code: Int) {
                when (code) {
                    CLOUD_COLD_STORAGE -> syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_COLD_STANDBY)
                    CLOUD_STEAM_LIMIT -> syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_STEAM_LIMIT)
                    LOCAL_SPACE_NOT_ENOUGH -> syncListener?.onSyncFinished(CloudKitSyncStatus.SYNC_CODE_FINISHED_LOCAL_SPACE_NOT_ENOUGH)
                    else -> {}
                }
            }
        })
    }

    private var syncListener: SyncListener? = null

    fun setSyncListener(listener: SyncListener) {
        syncListener = listener
    }

    interface SyncListener {
        fun onSyncFinished(syncStatus: CloudKitSyncStatus)
    }

}