/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SettingSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/25
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import android.content.Context
import android.content.Intent
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.nearme.note.MyApplication
import com.nearme.note.control.list.NoteListHelper
import com.nearme.note.db.FolderSyncSwitchManager
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.cloudkit.lib.CloudBackupResponseErrorProxy
import com.oplus.cloudkit.lib.CloudBackupResponseRecordProxy
import com.oplus.cloudkit.lib.CloudMetaDataRecordProxy
import com.oplus.cloudkit.transformer.FolderSyncData
import com.oplus.cloudkit.transformer.SettingTransformer
import com.oplus.cloudkit.util.Constants
import java.lang.reflect.Type

class SettingSyncManager(@get:VisibleForTesting val context: Context) : AbsDataSyncManager(
    Constants.MODULE_NOTE,
    Constants.ZONE_SETTING,
    Constants.RECORD_TYPE_VERSION_SETTING
) {

    companion object {
        private const val TAG = "SettingSyncManager"
    }

    @get:VisibleForTesting
    val transformer = SettingTransformer()
    private val folderSyncSwitchMerger = FoldSyncSwitchMerger(MyApplication.appContext)

    override fun onStartRecovery() {
        AppLogger.CLOUDKIT.d(TAG, "onStartRecovery")
    }

    override fun onRecoveryEnd(backUp: () -> Unit) {
        backUp.invoke()
        AppLogger.BASIC.d(TAG, "onRecoveryEnd")
    }
    override fun onPagingRecoveryStart() {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryStart")
    }

    override fun onPagingRecoveryEnd(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingRecoveryEnd: ${data?.size}")

        if (data.isNullOrEmpty()) {
            return
        }

        // every user has one setting record.
        val record = data[0]

        // save the record id and sysversion to SP.
        SharedPreferencesUtil.getInstance().putString(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.SETTING_RECORD_KEY,
            record.sysRecordId
        )
        SharedPreferencesUtil.getInstance().putLong(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.SETTING_RECORD_VERSION,
            record.sysVersion
        )

        val modeFlag = transformer.convertToSettingsFrom(record).modeFlag
        val folderSyncSwitch = transformer.convertToSettingsFrom(record).folderSyncSwitch
            ?: SharedPreferencesUtil.DEFAULT_VALUE_STRING

        val currentMode = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_KEY
        )
        val preMode = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_PRE_KEY
        )
        val currentFolderSyncSwitch = FolderSyncSwitchManager.getLocalRememberSyncFolderState()


        val isCloudChange = currentMode != modeFlag && currentMode == preMode
        AppLogger.CLOUDKIT.d(
            TAG,
            "syncModeFlag  ------   currentMode = $currentMode, preMode = $preMode, modeFlag = $modeFlag, isCloudChange = $isCloudChange"
        )

        val foldSyncSwitchChange = folderSyncSwitch != currentFolderSyncSwitch
        AppLogger.CLOUDKIT.d(
            TAG,
            "syncFoldSyncSwitch ---- currentFolderSyncSwitch:$currentFolderSyncSwitch, " +
                    "folderSyncSwitch:$folderSyncSwitch ," +
                    "foldSyncSwitchChange:$foldSyncSwitchChange"
        )
        if (isCloudChange) {
            val intent = Intent()
            intent.action = NoteListHelper.ACTION_MODE_FLAG_CHANGE
            intent.putExtra(NoteListHelper.KEY_MODE_FLAG_CHANGE, modeFlag)
            intent.setPackage(context.packageName)
            context.sendBroadcast(intent)
        }
        /**
         * recover the state of the synchronization switch
         */
        if (foldSyncSwitchChange) {
            AppLogger.CLOUDKIT.d(TAG, "folder sync switch changed.")
            folderSyncSwitchMerger.onRecoverFolderSyncSwitch(
                folderSyncSwitch,
                currentFolderSyncSwitch
            )
        }
    }

    override fun getMetaDataCount(): Int {
        // only one record for setting.
        return 1
    }

    override fun onStartBackup() {
        AppLogger.CLOUDKIT.d(TAG, "onStartBackup")
    }

    override fun onQueryDirtyData(): List<CloudMetaDataRecordProxy> {
        val currentModeFlag = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_KEY
        )
        val preModeFlag = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_PRE_KEY
        )
        val isModeFlagChanged = currentModeFlag != preModeFlag

        val rememberSyncForBackup = FolderSyncSwitchManager.getRememberSyncFolderStateForBackup()

        AppLogger.CLOUDKIT.d(TAG, "rememberSyncForBackup:$rememberSyncForBackup")
        val isSyncSwitchChange = rememberSyncForBackup.isNotEmpty()

        var recordId = SharedPreferencesUtil.getInstance()
            .getString(
                context,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.SETTING_RECORD_KEY
            )
        val isEmptyRecordId = recordId.isNullOrEmpty()

        val hasDirtyModeFlag = isModeFlagChanged || isEmptyRecordId || isSyncSwitchChange


        AppLogger.CLOUDKIT.d(TAG, "onQueryDirtyData: $isModeFlagChanged, $isEmptyRecordId ,$isSyncSwitchChange")
        return if (hasDirtyModeFlag) {
            if (isEmptyRecordId) {
                // 鉴于setting_info已经有用户数据迁移到星链上，其中sysRecordId为userid，现CK版本便签兼容逻辑也设置为userid。
                recordId = CloudKitSdkManager.getAccountUserId()
                SharedPreferencesUtil.getInstance().putString(
                    context,
                    SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.SETTING_RECORD_KEY,
                    recordId
                )
            }
            val sysVersion = SharedPreferencesUtil.getInstance()
                .getLong(
                    context,
                    SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.SETTING_RECORD_VERSION,
                    0
                )
            val record = transformer.convertToRecordFrom(
                recordId,
                sysVersion,
                currentModeFlag,
                rememberSyncForBackup
            )
            if (record != null) {
                listOf(record)
            } else {
                emptyList()
            }
        } else {
            emptyList()
        }
    }

    override fun onPagingBackupStart(data: List<CloudMetaDataRecordProxy>?) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupStart:${data?.size}")
    }

    override fun onPagingBackupEnd(
        successData: List<CloudBackupResponseRecordProxy>?,
        errorData: List<CloudBackupResponseErrorProxy>?
    ) {
        AppLogger.CLOUDKIT.d(TAG, "onPagingBackupEnd")

        val currentMode = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_KEY
        )
        val preMode = SharedPreferencesUtil.getInstance().getInt(
            context,
            SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
            SharedPreferencesUtil.HOME_PAGE_MODE_PRE_KEY
        )
        AppLogger.CLOUDKIT.d(
            TAG,
            "update setting: ------  currentMode = $currentMode, preMode = $preMode"
        )
        val isLocalChange = currentMode != preMode
        if (isLocalChange) {
            SharedPreferencesUtil.getInstance().putInt(
                context,
                SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                SharedPreferencesUtil.HOME_PAGE_MODE_PRE_KEY,
                currentMode
            )
        }
    }


    /**
     * Determine whether to use cloud or local data
     * when restoring the state of the synchronization switch
     */
    class FoldSyncSwitchMerger(private val context: Context) {

        fun onRecoverFolderSyncSwitch(
            cloudFolderSyncSwitch: String,
            currentFolderSyncSwitch: String
        ) {
            if (cloudFolderSyncSwitch == currentFolderSyncSwitch) {
                AppLogger.CLOUDKIT.d(TAG, "sync switch same.")
                return
            }
            runCatching {
                val type: Type = object : TypeToken<List<FolderSyncData>>() {}.type
                val cloudSyncSwitchList: List<FolderSyncData>? =
                    Gson().fromJson(cloudFolderSyncSwitch, type)

                /**
                 * 开关值以云端为准，直接写入数据库
                 */
                FolderSyncSwitchManager.setRememberSyncFolderSyncState(cloudSyncSwitchList)
            }.onFailure {
                AppLogger.BASIC.d(TAG, "onRecoverFolderSyncSwitch error:${it.message}")
            }
        }
    }
}