/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: AttachmentSyncManager.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/4/20
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudkit

import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.nearme.note.db.AppDatabase
import com.nearme.note.model.RichNoteRepository
import com.nearme.note.model.isSync
import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.logger.AppLogger
import com.oplus.cloudkit.lib.CloudHttpStatusCodeProxy
import com.oplus.cloudkit.lib.CloudIOFileProxy
import com.oplus.cloudkit.lib.CloudKitErrorProxy
import com.oplus.cloudkit.transformer.AttachmentTransformer
import com.oplus.cloudkit.util.CloudKitSyncStatus
import java.util.concurrent.CountDownLatch

class AttachmentSyncManager(@get:VisibleForTesting val transformer: AttachmentTransformer) {

    companion object {
        private const val TAG = "AttachmentSyncManager"
    }

    @WorkerThread
    fun sync() {
        // step1: get all upload and download attachments
        val source = AppDatabase.getInstance().richNoteDao().getAllRichNoteWithAttachments().filter {
            it.isSync()
        }
        val uploadList = mutableListOf<Attachment>()
        val downloadList = mutableListOf<Attachment>()
        val nonFileList = mutableListOf<Attachment>()
        transformer.fill(source, uploadList, downloadList, nonFileList)

        // step2: downloads attachments
        download(downloadList)

        // step3: upload attachments
        upload(uploadList)

        // mark attachment has not file
        markNoFile(nonFileList)
    }

    @VisibleForTesting
    internal fun download(downloadList: List<Attachment>) {
        if (downloadList.isEmpty()) {
            AppLogger.CLOUDKIT.d(TAG, "No attachments need to download!")
            return
        }

        val updateAttachments = mutableListOf<Attachment>()
        val lock = CountDownLatch(downloadList.size)
        val downloadWrapper = AttachmentDownloadWrapper(transformer, updateAttachments)
        AppLogger.CLOUDKIT.d(TAG, "download list size:${downloadList.size}")
        for (attachment in downloadList) {
            // 1.init download environment
            downloadWrapper.downloadAttachment(attachment) { success ->
                lock.countDown()
                if (!success) {
                    return@downloadAttachment
                }
            }
        }
        lock.await()
        AppDatabase.getInstance().richNoteDao().update(updateAttachments)
        NoteSearchManagerWrapper.notifyDataChange()
    }

    @VisibleForTesting
    internal fun upload(uploadList: List<Attachment>) {
        if (uploadList.isEmpty()) {
            AppLogger.CLOUDKIT.d(TAG, "No attachments need to upload!")
            return
        }

        var error: CloudKitErrorProxy? = null
        val updateAttachments = mutableListOf<Attachment>()
        val lock = CountDownLatch(uploadList.size)
        for (attachment in uploadList) {
            // 1.init download environment
            val oFile = transformer.convertToCloudIOFileForUploadFrom(attachment)

            // 2.make a file upload.
            CloudKitSdkManager.transferFile(oFile, object : CloudIOFileListenerProxy{
                override fun onFinish(file: CloudIOFileProxy?, cloudKitError: CloudKitErrorProxy?) {
                    if (cloudKitError?.isSuccess == true && file != null) {
                        attachment.md5 = file.md5
                        attachment.url = if (file.cloudId.startsWith("/")) file.cloudId else "/${file.cloudId}"
                        attachment.checkPayload = file.checkPayload
                        attachment.state = Attachment.STATE_UNCHANGE
                        updateAttachments.add(attachment)
                    } else {
                        error = cloudKitError
                    }
                    AppLogger.CLOUDKIT.d(TAG, "upload finished with: $attachment, isSuccess: ${cloudKitError?.isSuccess}")
                    lock.countDown()
                }
            })

        }
        lock.await()
        AppLogger.CLOUDKIT.d(TAG, "upload finish error $error")
        if (error?.subServerErrorCode == CloudHttpStatusCodeProxy.HTTP_NO_CLOUD_SPACE) {
            SyncManager.sendSyncResult(CloudKitSyncStatus.SYNC_CODE_FINISHED_CLOUD_SPACE_NOT_ENOUGH)
        } else if (error != null) {
            SyncManager.sendSyncResult(CloudKitSyncStatus.SYNC_CODE_FINISHED_ATTACHMENT_UPLOAD_FAILED)
        }
        AppDatabase.getInstance().richNoteDao().update(updateAttachments)
    }

    @VisibleForTesting
    fun markNoFile(nonFileList: List<Attachment>) {
        AppLogger.BASIC.d(TAG, "mark no file ${nonFileList.size}")
        RichNoteRepository.markAttachmentAsNonFile(nonFileList.map {
            it.attachmentId
        })
        NoteSearchManagerWrapper.notifyDataChange()
    }
}