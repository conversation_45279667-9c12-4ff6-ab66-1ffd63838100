package com.oplus.cloud.agent.note

import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.oplus.note.repo.note.entity.FolderInfo
import com.oplus.cloud.agent.SyncAgentContants
import com.oplus.cloud.exceptions.ConnectServerException
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.policy.SyncRequest
import com.oplus.cloud.protocol.ProtocolAdapter
import com.oplus.cloud.sync.note.CloudContextImpl
import com.oplus.cloud.utils.MD5Utils
import com.nearme.note.db.AppDatabase
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.Picture
import com.nearme.note.util.ConfigUtils
import com.nearme.note.util.ThumbnailUtils
import java.io.File
import java.util.*

/**
 * @return the parent directory if create success.
 */
fun mkdirsAndGetParent(path: String): File? {
    return File(path).parentFile?.apply {
        delete()
        mkdirs()
    }
}

@Deprecated(message = "legacy cloud sync logic")
class AttachmentSyncManager {

    companion object {
        const val TAG = "AttachmentSyncManager"

        @WorkerThread
        @JvmStatic
        fun sync(cloudContext: CloudContextImpl, cloudFileManager: CloudFileManager) {
            // step1: get all upload and download pic attachments
            val uploadList = mutableListOf<Attachment>()
            val downloadList = mutableListOf<Attachment>()

            val typeArray = intArrayOf(Attachment.TYPE_PICTURE,
                Attachment.TYPE_PAINT,
                Attachment.TYPE_VOICE,
                Attachment.TYPE_COVER_PAINT,
                Attachment.TYPE_COVER_PICTURE)

            val richNoteWithAttachmentList =
                AppDatabase.getInstance().richNoteDao().getAllRichNoteWithAttachments()
            for (richNoteAttachment in richNoteWithAttachmentList) {
                if (cloudContext.isTermination) {
                    return
                }
                if (!ConfigUtils.isNeedToSyncPrivateNote && richNoteAttachment.richNote.folderGuid == FolderInfo.FOLDER_GUID_ENCRYPTED) {
                    continue
                }
             run outside@{
                    richNoteAttachment.attachments?.forEach inside@{ attachment ->
                        if (!TextUtils.isEmpty(attachment.md5)) {
                            return@inside
                        }
                        if (!typeArray.contains(attachment.type)) {
                            return@inside
                        }
                        if (TextUtils.isEmpty(attachment.url)) {
                            uploadList.add(attachment)
                            AppLogger.CLOUD.d(TAG, "sync attachments upload: $attachment")
                        } else {
                            downloadList.add(attachment)
                            AppLogger.CLOUD.d(TAG, "sync attachments download: $attachment")
                        }
                    }

                }
            }

            // step2: downloads attachments
            download(downloadList, cloudContext, cloudFileManager)

            // step3: upload attachments
            upload(uploadList, cloudContext, cloudFileManager)
        }

        private fun download(downloadList: List<Attachment>, cloudContext: CloudContextImpl, cloudFileManager: CloudFileManager) {
            if (downloadList.isEmpty()) {
                AppLogger.BASIC.d(TAG, "No attachments need to download!")
                return
            }

            val updateAttachments = mutableListOf<Attachment>()
            for (attachment in downloadList) {
                if (cloudContext.isTermination) {
                    break
                }
                AppLogger.BASIC.d(TAG, "download attachment attachmentId = " + attachment.attachmentId + " , type = " + attachment.type)

                // 1.init download environment
                val destFilePath = attachment.absolutePath(cloudContext.context)
                val folder = mkdirsAndGetParent(destFilePath)
                if (folder == null) {
                    AppLogger.CLOUD.e(TAG, "Can not create folder:[$folder]!")
                    continue
                }

                // 2.make a file post request
                val url: String = cloudContext.urlFactory.get(
                    ProtocolAdapter.OPERATION_FILE_DOWNLOAD,
                        SyncRequest.REQUEST_SOURCE_AUTO, SyncAgentContants.DataType.NOTE, cloudContext.context) + attachment.url
                val success = try {
                    cloudFileManager.downloadFile(url, destFilePath)
                } catch (e: ConnectServerException) {
                    false
                }

                // 3.update local attachment record if success
                if (success) {
                    val md5 = MD5Utils.getMD5(File(destFilePath))
                    attachment.md5 = md5
                    if (attachment.type == Attachment.TYPE_PICTURE) {
                        val bitmap = ThumbnailUtils.decodeFile(destFilePath)
                        bitmap?.apply {
                            attachment.picture = Picture(this.width, this.height)
                            this.recycle()
                        }
                    }
                    attachment.state = Attachment.STATE_UNCHANGE
                    updateAttachments.add(attachment)
                }
            }
            AppDatabase.getInstance().richNoteDao().update(updateAttachments)
        }

        private fun upload(uploadList: List<Attachment>, cloudContext: CloudContextImpl, cloudFileManager: CloudFileManager) {

            if (uploadList.isEmpty()) {
                AppLogger.BASIC.d(TAG, "No attachments need to upload!")
                return
            }

            val updateAttachments: MutableList<Attachment> = ArrayList()
            for (attachment in uploadList) {
                if (cloudContext.isTermination) {
                    break
                }
                AppLogger.BASIC.d(TAG, "upload attachment attachmentId = " + attachment.attachmentId + " , type = " + attachment.type)

                // 1.init upload environment
                val destFilePath = attachment.absolutePath(cloudContext.context)
                val destFile = File(destFilePath)
                if (!destFile.isFile) {
                    continue
                }

                // 2.make a file post request
                val httpUrl = cloudContext.urlFactory.get(
                    ProtocolAdapter.OPERATION_FILE_UPLOAD,
                        SyncRequest.REQUEST_SOURCE_AUTO, SyncAgentContants.DataType.NOTE, cloudContext.context)
                val fileId = try {
                    cloudFileManager.uploadFile(httpUrl, destFile)
                } catch (e: Exception) {
                    null
                }

                // 3.update local attachment record if success
                if (fileId != null) {
                    AppLogger.CLOUD.d(TAG, "fileIdString:$fileId")
                    attachment.md5 = MD5Utils.getMD5(destFile)
                    attachment.url = fileId
                    attachment.state = Attachment.STATE_UNCHANGE
                    updateAttachments.add(attachment)
                }
            }
            AppDatabase.getInstance().richNoteDao().update(updateAttachments)
        }
    }
}