/***********************************************************
 * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
@file:Suppress("ParameterStyleBracesRule", "LongMethod", "NestedBlockDepth", "PrintStackTrace", "MaximumLineLength", "ParameterListWrapping", "LoopWithTooManyJumpStatements")

package com.oplus.cloud.agent.note

import android.text.TextUtils
import com.google.gson.JsonParser
import com.google.gson.JsonSyntaxException
import com.nearme.note.activity.edit.HandWrittingHelper
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteAttribute.PictureAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.NoteInfoDBUtil
import com.nearme.note.db.entities.Note
import com.nearme.note.db.entities.NotesAttribute
import com.nearme.note.logic.ThumbFileManager
import com.nearme.note.util.ThumbnailUtils
import com.oplus.cloud.agent.SyncAgentContants
import com.oplus.cloud.exceptions.ConnectServerException
import com.oplus.cloud.policy.SyncRequest
import com.oplus.cloud.protocol.ProtocolAdapter
import com.oplus.cloud.sync.note.CloudContextImpl
import com.oplus.cloud.utils.MD5Utils
import com.oplus.note.logger.AppLogger
import java.io.File
import java.io.IOException

object AttributeSyncManager {
    private const val TAG = "AttributeSyncManager"
    const val FILEID = "fileId"

    @JvmStatic
    fun syncAttributes(cloudContext: CloudContextImpl,
                       cloudFileManager: CloudFileManager): Boolean {
        var doAnotherBackup = false
        val uploadOnes = ArrayList<PictureAttribute>()
        val downloadOnes = ArrayList<PictureAttribute>()
        val context = cloudContext.context

        // step1: get all upload and download pictureattributes
        val attrs = AppDatabase.getInstance().noteAttributeDao().findByTypeContentMD5()
        for (attr in attrs) {
            if (cloudContext.isTermination) {
                return false
            }
            val att = NoteAttribute.newPictureAttribute(attr.type)
            att.setOperation(NoteAttribute.OP_NONE)
            att.attrGuid = attr.filename
            val url = attr.attachmentSyncUrl
            att.attachmentSyncUrl = url
            val noteguid = attr.noteGuid
            att.noteGuid = noteguid
            att.id = attr.id.toLong()
            val md5 = attr.attachmentMd5
            var synced = 0
            // 这里有可能为空，导致同步失败
            if (attr.syncData1 != null) {
                AppLogger.CLOUD.d(TAG, "----------attr.syncData1: null")
                synced = attr.syncData1!!.toInt()
            }
            AppLogger.CLOUD.d(TAG, "----------synced: $synced")
            if (synced == 0) {
                if (TextUtils.isEmpty(md5) && !TextUtils.isEmpty(url)) {
                    downloadOnes.add(att)
                    AppLogger.CLOUD.d(TAG, "syncAttributes download: $att")
                } else if (TextUtils.isEmpty(md5) && TextUtils.isEmpty(url)) {
                    uploadOnes.add(att)
                    AppLogger.CLOUD.d(TAG, "syncAttributes upload: $att")
                }
            } else {
                // no-op
            }
        }

        // step2: do downloads attributes
        if (!downloadOnes.isEmpty()) {
            val updateAttrs: MutableList<NotesAttribute> = ArrayList()
            val notes: MutableList<Note> = ArrayList()
            for (picAttribute in downloadOnes) {
                if (cloudContext.isTermination) {
                    break
                }
                val picThumbPath = ThumbFileManager.getThumbFilePathInData(context, picAttribute)
                val picThumbFile = File(picThumbPath)
                val folder = picThumbFile.parentFile
                if (folder != null && folder.exists() && !folder.isDirectory) {
                    val suc = folder.delete()
                    if (!suc) {
                        AppLogger.CLOUD.e(TAG, "delete file failed!")
                    }
                }
                if (folder != null && !folder.exists()) {
                    val success = folder.mkdirs()
                    if (!success) {
                        AppLogger.CLOUD.e(TAG, "Can not create folder:[$folder]!")
                        continue
                    }
                }
                val attrType = picAttribute.type
                val filename = picAttribute.attrGuid
                val url = cloudContext.urlFactory[ProtocolAdapter.OPERATION_FILE_DOWNLOAD, SyncRequest.REQUEST_SOURCE_AUTO, SyncAgentContants.DataType.NOTE, cloudContext.context] + picAttribute.attachmentSyncUrl
                var downloadSuccessPath: String? = null
                downloadSuccessPath = try {
                    cloudFileManager.downloadFile(url, folder!!, filename,
                            true)
                } catch (e: ConnectServerException) {
                    e.printStackTrace()
                    null
                }
                AppLogger.CLOUD.d(TAG, "downloadFile: " + (downloadSuccessPath != null) + ", save :"
                        + downloadSuccessPath)
                if (downloadSuccessPath != null) {
                    val md5 = MD5Utils.getMD5(File(downloadSuccessPath))
                    val attr = AppDatabase.getInstance().noteAttributeDao().findById(picAttribute.id)
                    if (attr != null) {
                        if (TextUtils.isEmpty(attr.para)) {
                            attr.para = downloadSuccessPath
                        }
                        attr.attachmentMd5 = md5
                        attr.syncData1 = "1"
                        if (attrType == NoteAttribute.TYPE_ALBUM
                                || attrType == NoteAttribute.TYPE_PHOTOGRAPH) {
                            val bitmap = ThumbnailUtils.decodeFile(downloadSuccessPath)
                            if (bitmap != null) {
                                attr.width = bitmap.width
                                attr.height = bitmap.height
                                bitmap.recycle()
                            }
                        } else if (attrType == NoteAttribute.TYPE_HANDWRITING) {
                            HandWrittingHelper.loadHWThumbFile(context, picAttribute)
                            val bitmap = ThumbnailUtils.decodeFile(picAttribute.param)
                            if (bitmap != null) {
                                attr.width = bitmap.width
                                attr.height = bitmap.height
                                bitmap.recycle()
                            }
                            attr.attachmentMd5 = null
                            attr.syncData1 = "0"
                            attr.attachmentSyncUrl = null
                            val note = AppDatabase.getInstance().noteDao().findByGuid(picAttribute.noteGuid)
                            note.state = NoteInfo.STATE_MODIFIED
                            notes.add(note)
                        }
                        updateAttrs.add(attr)
                    }
                }
            }
            AppDatabase.getInstance().commonDao().updateNoteAndAttributes(updateAttrs, notes)
        } else {
            AppLogger.CLOUD.d(TAG, "No pics need to download!")
        }

        // step3: do upload attributes
        if (!uploadOnes.isEmpty()) {
            val updateAttributes: MutableList<NotesAttribute> = ArrayList()
            for (picAttribute in uploadOnes) {
                if (cloudContext.isTermination) {
                    AppLogger.CLOUD.e(TAG, "cloudContext isTermination")
                    break
                }
                if (null == picAttribute) {
                    continue
                }
                AppLogger.CLOUD.d(TAG, "picture upload NoteGuid = " + picAttribute.noteGuid + " type = " + picAttribute.type)
                var filePath = ""
                var fileName = ""
                if (NoteAttribute.TYPE_HANDWRITING == picAttribute.type) {
                    HandWrittingHelper.loadHWThumbFile(context, picAttribute)
                }
                fileName = picAttribute.attrGuid
                filePath = ThumbFileManager.getThumbFilePath(context, picAttribute)
                AppLogger.CLOUD.d(TAG, "fileName = $fileName filePath = $filePath")
                if (TextUtils.isEmpty(fileName) || TextUtils.isEmpty(filePath)) {
                    continue
                }
                var foundFile = false
                val targetFile = File(filePath)
                if (targetFile.exists() && targetFile.isFile) {
                    foundFile = true
                }
                val attr = AppDatabase.getInstance().noteAttributeDao().findById(picAttribute.id)
                if (foundFile) {
                    var uploadSuccess = false
                    val response = cloudFileManager.uploadFile(SyncAgentContants.DataType.NOTE,
                            targetFile.absolutePath, fileName)
                    if (response != null && response.isSuccessful) {
                        uploadSuccess = response.isSuccessful
                        AppLogger.CLOUD.d(TAG, "Response Success")
                        val body = response.body
                        if (body != null) {
                            var fileId: String? = null
                            try {
                                fileId = body.string()
                            } catch (e: IOException) {
                                e.printStackTrace()
                            }
                            AppLogger.CLOUD.d(TAG, "fileId: $fileId")
                            if (fileId != null) {
                                var fileIdString: String? = null
                                try {
                                    val jsonElement = JsonParser().parse(fileId)
                                    AppLogger.CLOUD.d(TAG, "JsonElement: $jsonElement")
                                    if (jsonElement != null) {
                                        val jsonObject = jsonElement.asJsonObject
                                        val jsonElementFileId = jsonObject!![FILEID]
                                        if (jsonObject != null && null != jsonElementFileId) {
                                            fileIdString = jsonElementFileId.asString
                                        }
                                    }
                                } catch (e: JsonSyntaxException) {
                                    // Abnormal data from server
                                }
                                if (fileIdString != null) {
                                    AppLogger.CLOUD.d(TAG, "fileIdString:$fileIdString")
                                    attr.attachmentMd5 = MD5Utils.getMD5(targetFile)
                                    attr.attachmentSyncUrl = fileIdString
                                    attr.syncData1 = "1"
                                    attr.state = NoteAttribute.OP_NONE.toInt()
                                    updateAttributes.add(attr)
                                    doAnotherBackup = true
                                    // mark
                                    val factory = cloudContext.packetFactory
                                    val packet = factory.newKv()
                                    packet.putString(NoteSyncAgent.ITEM_ID, picAttribute.noteGuid)
                                    NoteInfoDBUtil.updateNote(context, packet, null, true)
                                }
                            }
                        }
                    }
                    AppLogger.CLOUD.d(TAG, "uploadFile: " + uploadSuccess + ", file :"
                            + targetFile.absolutePath)
                } else {
                    attr.syncData1 = "1"
                    updateAttributes.add(attr)
                }
            }
            AppDatabase.getInstance().noteAttributeDao().updateAttributes(updateAttributes)
        } else {
            AppLogger.CLOUD.d(TAG, "No pics need to upload!")
        }
        return doAnotherBackup
    }
}