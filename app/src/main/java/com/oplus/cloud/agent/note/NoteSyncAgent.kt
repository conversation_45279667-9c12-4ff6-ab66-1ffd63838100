/***********************************************************
 * Copyright (C), 2008-2017, OPLUS Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
@file:Suppress("TooManyFunctions", "LargeClass", "LongMethod", "NestedBlockDepth", "PrintStackTrace", "TooGenericExceptionCaught", "MaximumLineLength", "NoBlankLineBeforeRbrace", "VariableNaming", "CollapsibleIfStatements", "ForbiddenComment")

package com.oplus.cloud.agent.note

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import com.coui.appcompat.version.COUIVersionUtil
import com.google.gson.JsonParser
import com.heytap.usercenter.accountsdk.utils.GsonUtil
import com.nearme.note.MyApplication.Companion.appContext
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.appwidget.notewidget.NoteWidgetInfoMap.Companion.getInstance
import com.nearme.note.common.Constants
import com.oplus.note.repo.todo.entity.UUIDConverters
import com.oplus.note.repo.note.entity.FolderInfo
import com.nearme.note.data.NoteAttribute
import com.nearme.note.data.NoteInfo
import com.nearme.note.db.AppDatabase
import com.nearme.note.db.FolderUtil
import com.nearme.note.db.NoteInfoDBUtil
import com.nearme.note.db.NotesProvider
import com.oplus.note.repo.note.entity.Folder
import com.oplus.note.repo.note.entity.FolderExtra
import com.nearme.note.db.extra.NoteExtra
import com.oplus.note.repo.note.entity.RichNoteExtra
import com.oplus.note.repo.note.entity.RichNoteExtra.Companion.filterInvalid
import com.oplus.note.repo.note.entity.RichNoteExtra.Companion.updateAttachmentProperty
import com.nearme.note.logic.NoteManager
import com.nearme.note.logic.NoteSyncProcess
import com.nearme.note.logic.ThumbFileManager
import com.oplus.note.repo.note.entity.Attachment
import com.oplus.note.repo.note.entity.RichNote
import com.nearme.note.model.RichNoteRepository.deleteAll
import com.nearme.note.model.RichNoteRepository.deleteByGlobalID
import com.nearme.note.model.RichNoteRepository.markAllAsNew
import com.nearme.note.model.RichNoteRepository.update
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.repo.note.entity.SubAttachment
import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.repo.todo.entity.ToDo.StatusEnum
import com.nearme.note.model.ToDoRepository
import com.nearme.note.model.ToDoRepositoryLazyWrapper
import com.nearme.note.remind.RepeatDataHelper
import com.nearme.note.remind.RepeatManage
import com.nearme.note.skin.SkinData
import com.nearme.note.util.AlarmUtils
import com.nearme.note.util.AppExecutors
import com.nearme.note.util.ConfigUtils.isNeedToSyncPrivateNote
import com.nearme.note.util.FileUtil
import com.nearme.note.util.NetworkUtils
import com.nearme.note.util.NetworkUtils.getNetworkType
import com.nearme.note.util.RandomGUID
import com.nearme.note.util.SortRule.resetSortRule
import com.nearme.note.util.StringEncodeDecode.decode
import com.nearme.note.util.ToDoUtils
import com.oplus.cloud.account.Account
import com.oplus.cloud.account.IAccountStatusListener
import com.oplus.cloud.agent.BaseSyncAgent
import com.oplus.cloud.agent.SyncAgentContants
import com.oplus.cloud.agent.SyncData
import com.oplus.cloud.anchor.AbstractAnchorManager
import com.oplus.cloud.data.Packet
import com.oplus.cloud.data.PacketArray
import com.oplus.cloud.policy.AbstractRecurrenceMatchIdManager
import com.oplus.cloud.policy.SyncRequest
import com.oplus.cloud.policy.SyncResult
import com.oplus.cloud.protocol.HttpServiceProtocolAdapter
import com.oplus.cloud.protocol.ProtocolTag
import com.oplus.cloud.status.Device
import com.oplus.cloud.sync.SyncInnerCallbacks
import com.oplus.cloud.sync.note.AccountUtils
import com.oplus.cloud.sync.note.AnchorManager
import com.oplus.cloud.sync.note.CloudContextImpl
import com.oplus.cloud.sync.note.RecurrenceMatchIdManager
import com.oplus.cloud.sync.note.SyncDataProvider
import com.oplus.cloud.sync.note.SyncNoteServive
import com.oplus.cloud.sync.richnote.RichNoteConstants
import com.oplus.cloud.sync.richnote.RichNoteFactory.Companion.createAttachment
import com.oplus.cloud.sync.richnote.RichNoteFactory.Companion.createRichNote
import com.oplus.cloud.utils.CommonUtils
import com.oplus.cloud.utils.PacketUtils
import com.oplus.cloud.utils.PrefUtils
import com.oplus.note.BuildConfig
import com.oplus.note.R
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.ToDoExtra
import com.oplus.note.utils.DateAndTimeUtils
import com.oplus.note.utils.SharedPreferencesUtil
import org.json.JSONException
import org.json.JSONObject
import java.io.File
import java.util.Date
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean

@SuppressLint("Registered")
class NoteSyncAgent private constructor() : BaseSyncAgent(CloudContextImpl(appContext)), IAccountStatusListener, SyncInnerCallbacks {
    private var mToDoRepository: ToDoRepositoryLazyWrapper? = null
    var userName: String? = null
        private set

    private val mIsCancel = AtomicBoolean(false)

    init {
        mProtocolAdapter = HttpServiceProtocolAdapter(mContext,
                mCloudContext.urlFactory)
        mAnchorManager = loadAnchorManager()
        mRecurrenceMatchIdManager = loadRecurrenceMatchIdManager()
    }

    val cloudContext: CloudContextImpl
        get() = mCloudContext as CloudContextImpl

    fun cancelSync() {
        mIsCancel.set(true)
    }

    fun continueSync() {
        mIsCancel.set(false)
    }

    override fun onInitResult(): SyncResult {
        return SyncResult()
    }

    override fun onStartSync(extra: Bundle, syncResult: SyncResult): Boolean {
        //Laixy
        if (!isSyncPerformAllow(extra.getInt(EXTRA_KEY_REQUEST_SOURCE))) {
            return false
        }
        onPreparePerformSync()
        performRecurrenceMatchId(syncResult)
        val type = extra.getInt(EXTRA_KEY_SYNC_TYPE)
        if (type != RECOVERY) {
            AppLogger.CLOUD.d(TAG, "onStartSync: type is not recovery has ignored")
            return false
        }
        return true
    }

    override fun onSyncFinished(extra: Bundle, syncResult: SyncResult) {
        AppLogger.CLOUD.d(TAG, "----------onPerformSyncDone-------------")
        AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.TODO)
        AlarmUtils.resetSystemAlarms(AlarmUtils.ControllerType.NOTE)
    }

    interface OnDeleteFinishListener {
        fun onDeleteFinish()
    }

    override fun isCanceled(): Boolean {
        AppLogger.CLOUD.d(TAG, "isCanceled:" + mIsCancel.get())
        return mIsCancel.get()
    }

    override fun onAutoSyncEnabled(enabled: Boolean) {
        AppLogger.CLOUD.d(TAG, "onAutoSyncEnabled: $enabled")
        if (enabled) {
            val key = PrefUtils.getRecoveryKey(moduleName)
            val hasInited = PrefUtils.getBoolean(mContext, key, false)
            if (!hasInited) {
                AppLogger.CLOUD.d(TAG, "onAutoSyncEnabled "
                        + "Notes sync is set enabled, do sync(recovery and backup)!")
                sendAutoSyncRequest(RECOVERY)
                PrefUtils.putBoolean(mContext, key, true)
            } else {
                AppLogger.CLOUD.d(TAG, "onAutoSyncEnabled "
                        + "Notes sync is set enabled, and it has been inited already, no need to do sync here.")
            }
        }
    }

    override fun onCreateAgent() {
        super.onCreateAgent()
        mToDoRepository = ToDoRepositoryLazyWrapper()
        AppLogger.CLOUD.d(TAG, "NoteSyncAgent onCreateAgent")
    }

    fun hasInit(): Boolean {
        return mToDoRepository != null
    }

    override fun onPreparePerformSync() {
        super.onPreparePerformSync()
        AppLogger.CLOUD.d(TAG, "onPreparePerformSync begin")
        userName = AccountUtils.getOldUserName(mContext)
    }

    override fun onDestroyAgent() {
        super.onDestroyAgent()
        AppLogger.CLOUD.d(TAG, "NoteSyncAgent onDestroyAgent")
    }

    override fun getModuleName(): String {
        return SyncAgentContants.DataType.NOTE
    }

    override fun isSyncPerformAllow(reqSrc: Int): Boolean {
        AppLogger.CLOUD.d(TAG, "isSyncPerformAllow reqSrc = $reqSrc")
        if (SyncRequest.REQUEST_SOURCE_AUTO == reqSrc) {
            if (NetworkUtils.TYPE_NETWORK_WIFI !== getNetworkType(mContext)) {
                return NoteSyncProcess.isMobileNetSupport(mContext)
            }
        }
        return true
    }

    override fun getSubmodules(): Array<String> {
        return arrayOf(SyncAgentContants.DataType.NOTE, SyncAgentContants.DataType.TODO, SyncAgentContants.DataType.RICH_NOTE)
    }

    override fun getDirtyData(submodule: String): SyncData<PacketArray<*>?>? {
        AppLogger.CLOUD.d(TAG, "---------getDirtyData--------, submodule: $submodule")
        return if (SyncAgentContants.DataType.NOTE == submodule) {
            val list = ArrayList<NoteInfo>()
            NoteInfoDBUtil.queryAllNoteInfoOfLoacalDirtyNote(list)
            putNoteInfosToSyncData(list)
        } else if (SyncAgentContants.DataType.TODO == submodule) {
            val list = mToDoRepository!!.get().dirtyData
            putToDosToSyncData(list)
        } else if (SyncAgentContants.DataType.RICH_NOTE == submodule) {
            val richNotes = AppDatabase.getInstance().richNoteDao().getDirtyRichNote()
            putRichNotesToSyncData(richNotes)
        } else {
            AppLogger.CLOUD.e(TAG, "getDirtyData unknown submodule: $submodule")
            null
        }
    }

    override fun getAllData(submodule: String): SyncData<PacketArray<*>?>? {
        AppLogger.CLOUD.d(TAG, "--------getAllData-----------, submodule: $submodule")
        return if (SyncAgentContants.DataType.NOTE == submodule) {
            val list = ArrayList<NoteInfo>()
            NoteInfoDBUtil.queryAllNoteInfo(mContext, list, false)
            putNoteInfosToSyncData(list)
        } else if (SyncAgentContants.DataType.TODO == submodule) {
            val list = mToDoRepository!!.get().allData
            putToDosToSyncData(list)
        } else if (SyncAgentContants.DataType.RICH_NOTE == submodule) {
            val richNotes = AppDatabase.getInstance().richNoteDao().getAllRichNoteWithAttachments()
            putRichNotesToSyncData(richNotes)
        } else {
            AppLogger.CLOUD.e(TAG, "getAllData unknown submodule: $submodule")
            null
        }
    }

    override fun onServerProcessedForBackup(submodule: String, opType: String, packetArray: PacketArray<*>?, updatedDirtyDataSet: Set<*>) {
        execOnServerProcessedForBackup(submodule, opType, packetArray, updatedDirtyDataSet)
    }

    private fun execOnServerProcessedForBackup(submodule: String, opType: String, packetArray: PacketArray<*>?, updatedDirtyDataSet: Set<*>) {
        AppLogger.CLOUD.d(TAG, "onServerProcessedForBackup-- submodule: $submodule, opType: $opType")
        if (packetArray != null && packetArray.toT() != null) {
            AppLogger.CLOUD.d(TAG, "onServerProcessedForBackup--packetArray:"
                    + packetArray.toT().toString())
        } else {
            return
        }
        val packetFactory = mCloudContext.packetFactory
        val result = ArrayList<Packet<*>>()
        if (SyncAgentContants.DataType.NOTE == submodule) {
            when (opType) {
                SyncAgentContants.OperationType.ADD -> {
                    if (isCanceled) {
                        return
                    }
                    val resultAdd = ArrayList<Packet<*>>()
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        val packet = packetArray[i]
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        val itemId = packet.getString(ITEM_ID)
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        resultAdd.add(packet)
                        if (resultAdd.size >= BATCH_LIMIT) {
                            NoteInfoDBUtil.updateNoteList(mContext, resultAdd, userName)
                            resultAdd.clear()
                        }
                        i++
                    }
                    NoteInfoDBUtil.updateNoteList(mContext, resultAdd, userName)
                }
                SyncAgentContants.OperationType.UPDATE -> {
                    if (isCanceled) {
                        return
                    }
                    val resultUpdate = ArrayList<Packet<*>>()
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        val packet = packetArray[i]
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        val itemId = packet.getString(ITEM_ID)
                        if (isItemInNotes(updatedDirtyDataSet as Set<NoteInfo>, itemId)) {
                            i++
                            continue
                        }
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        resultUpdate.add(packet)
                        if (resultUpdate.size >= BATCH_LIMIT) {
                            NoteInfoDBUtil.updateNoteList(mContext, resultUpdate, userName)
                            resultUpdate.clear()
                        }
                        i++
                    }
                    NoteInfoDBUtil.updateNoteList(mContext, resultUpdate, userName)
                }
                SyncAgentContants.OperationType.DELETE -> {
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        if (isCanceled) {
                            return
                        }
                        val packet = packetArray[i]
                        val itemId = packet.getString(ITEM_ID)
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        if (isItemInNotes(updatedDirtyDataSet as Set<NoteInfo>, itemId)) {
                            i++
                            continue
                        }
                        NoteInfoDBUtil.deleteNote(mContext, globalId, true, false)
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        i++
                    }
                }
                else -> {}
            }
            PacketUtils.toArray(result, packetFactory)
        } else if (SyncAgentContants.DataType.TODO == submodule) {
            when (opType) {
                SyncAgentContants.OperationType.ADD -> {
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        if (isCanceled) {
                            return
                        }
                        val packet = packetArray[i]
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        val itemId = packet.getString(ITEM_ID)
                        val count = mToDoRepository!!.get().markToDoAsUnChanged(itemId, globalId)
                        AppLogger.CLOUD.d(TAG, "mark new todo as unchanged localId: " + itemId
                                + ", globalId: " + globalId
                                + ", affect count: " + count)
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        i++
                    }
                }
                SyncAgentContants.OperationType.UPDATE -> {
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        if (isCanceled) {
                            return
                        }
                        val packet = packetArray[i]
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        val itemId = packet.getString(ITEM_ID)
                        if (isItemInToDos(updatedDirtyDataSet as Set<ToDo>, itemId)) {
                            i++
                            continue
                        }
                        val count = mToDoRepository!!.get().markToDoAsUnChanged(itemId, null)
                        AppLogger.CLOUD.d(TAG, "mark modified todo as unchanged localId: $itemId , affect count: $count")
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        i++
                    }
                }
                SyncAgentContants.OperationType.DELETE -> {
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        if (isCanceled) {
                            return
                        }
                        val packet = packetArray[i]
                        val itemId = packet.getString(ITEM_ID)
                        val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                        if (isItemInToDos(updatedDirtyDataSet as Set<ToDo>, itemId)) {
                            i++
                            continue
                        }
                        val count = mToDoRepository!!.get().deleteByGlobalId(UUIDConverters.stringToUUID(globalId))
                        AppLogger.CLOUD.d(TAG, "delete todo globalId: $globalId , affect count: $count")
                        result.add(PacketUtils.packResult(itemId, globalId, packetFactory))
                        i++
                    }
                }
                else -> {}
            }
            PacketUtils.toArray(result, packetFactory)
        } else if (SyncAgentContants.DataType.RICH_NOTE == submodule) {
            when (opType) {
                SyncAgentContants.OperationType.ADD, SyncAgentContants.OperationType.UPDATE -> {
                    if (isCanceled) {
                        return
                    }
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        val packet = packetArray[i]
                        val globalId = packet.getString(RichNoteConstants.KEY_GLOBAL_ID)
                        //新增返回的数据是itemID格式，无法使用localId
                        val localId = packet.getString(ITEM_ID)
                        if (isItemInRichNotes(updatedDirtyDataSet as Set<RichNoteWithAttachments>, localId)) {
                            i++
                            continue
                        }
                        result.add(PacketUtils.packResult(localId, globalId, packetFactory))
                        AppDatabase.getInstance().richNoteDao().updateSyncedRichNote(globalId, localId)
                        i++
                    }
                }
                SyncAgentContants.OperationType.DELETE -> {
                    var i = 0
                    val length = packetArray.size()
                    while (i < length) {
                        if (isCanceled) {
                            return
                        }
                        val packet = packetArray[i]
                        val localId = packet.getString(RichNoteConstants.KEY_LOCAL_ID)
                        val globalId = packet.getString(RichNoteConstants.KEY_GLOBAL_ID)
                        if (isItemInRichNotes(updatedDirtyDataSet as Set<RichNoteWithAttachments>, localId)) {
                            i++
                            continue
                        }
                        deleteByGlobalID(globalId)
                        NoteInfoDBUtil.deleteNote(localId, false)
                        val file = File(FileUtil.getFolderPathInData(appContext, localId))
                        FileUtil.deleteDirectory(file)
                        result.add(PacketUtils.packResult(localId, globalId, packetFactory))
                        i++
                    }
                }
                else -> {}
            }
            PacketUtils.toArray(result, packetFactory)
        }
    }

    private fun isItemInNotes(updatedDirtyDataSet: Set<NoteInfo>, localGuid: String): Boolean {
        for (note in updatedDirtyDataSet) {
            if (note != null && note.guid == localGuid) {
                return true
            }
        }
        return false
    }

    private fun isItemInRichNotes(updatedDirtyDataSet: Set<RichNoteWithAttachments>, localGuid: String): Boolean {
        for (richNoteWithAttachments in updatedDirtyDataSet) {
            if (richNoteWithAttachments != null && richNoteWithAttachments.richNote.localId == localGuid) {
                return true
            }
        }
        return false
    }

    private fun isItemInToDos(updatedDirtyDataSet: Set<ToDo>, localGuid: String): Boolean {
        for (toDo in updatedDirtyDataSet) {
            if (toDo != null && toDo.localId.toString() == localGuid) {
                return true
            }
        }
        return false
    }

    override fun onBackupFolders(submodule: String): List<FolderBean>? {
        return if (SyncAgentContants.DataType.NOTE == submodule || SyncAgentContants.DataType.RICH_NOTE == submodule) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync()
            val tobeBackup: MutableList<FolderBean> = ArrayList()
            for (info in localFolders) {
                tobeBackup.add(FolderBean(info.name, info.guid, info.createTime,
                        info.modifyDevice, ProtocolTag.Folder.FOLDER_STATE_UNCHANGE, info.encrypted, info.modifyTime, info.extra.toString()))
            }
            tobeBackup
        } else {
            null
        }
    }

    override fun onBackupFoldersDone(submodule: String, folderBeans: List<FolderBean>) {
        AppLogger.CLOUD.d(TAG, "onBackupFoldersDone(), submodule: $submodule, folderBeans: $folderBeans")
        if (SyncAgentContants.DataType.NOTE == submodule) {
            if (folderBeans != null) {
                FolderUtil.getInstance().deleteDeletedFoldersSync()
                for (bean in folderBeans) {
                    FolderUtil.getInstance().updateFolderWithValuesSync(bean.mFolderGuid, bean.mFolderName, bean.mModifyDevice, bean.mExtra, FolderInfo.FOLDER_STATE_UNCHANGE)
                }
            }
        } else {
            AppLogger.CLOUD.i(TAG, "onBackupFoldersDone, skip module without folder: $submodule")
        }
    }

    override fun onRecoveryFirstPage(submodule: String, responseContent: String) {
        AppLogger.CLOUD.d(TAG, "onRecoveryFirstPage, submodule: $submodule")
        super.onRecoveryFirstPage(submodule, responseContent)
        if (SyncAgentContants.DataType.NOTE == submodule) {
            onRecoveryFolders(parseFolderBean(responseContent))
            updateRichNotesWithFolders()
        }
    }

    private fun updateRichNotesWithFolders() {
        // check folder info is valid for richNote
        val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
        val richNotes = AppDatabase.getInstance().richNoteDao().getAllRichNotes()
        for (richNote in richNotes) {
            val folderGuid = richNote.folderGuid
            var valid = false
            for (localFolder in localFolders) {
                if (localFolder.guid == folderGuid) {
                    valid = true
                    break
                }
            }
            if (!valid) {
                richNote.folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
                richNote.state = RichNote.STATE_MODIFIED
                update(richNote)
            }
        }
    }

    private fun parseFolderBean(responseContent: String): List<FolderBean> {
        AppLogger.CLOUD.d(TAG, "parseFolderBean()")
        val folderBeans: MutableList<FolderBean> = ArrayList()
        val jsonObject = JsonParser().parse(responseContent)
        val factory = mCloudContext.packetFactory
        val packet = factory.newKv().parse(jsonObject)
        if (null == packet) {
            AppLogger.CLOUD.w(TAG, "parseFolderBean(), packet is null!")
            return folderBeans
        }
        val folderPacketArray = packet.getKVAsArray(ProtocolTag.Folder.CONTENT_FOLDERS)
        if (null != folderPacketArray) {
            var i = 0
            val length = folderPacketArray.size()
            while (i < length) {
                val folderPacket = folderPacketArray[i]
                val folderName = folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_NAME)
                val folderGuid = folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_GUID)
                val folderCreateTime = CommonUtils.parseLong(folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_CREATE_TIME))
                val folderModifyTime = CommonUtils.parseLong(folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_MODIFY_TIME))
                val folderModifyDevice = folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_MODIFY_DEVICE)
                val folderEncrypted = if (folderPacket.getBoolean(ProtocolTag.Folder.CONTENT_FOLDER_ENCRYPTED)) FolderInfo.FOLDER_ENCRYPTED else FolderInfo.FOLDER_UNENCRYPTED
                val folderExtra = folderPacket.getString(ProtocolTag.Folder.CONTENT_FOLDER_EXTRA)
                folderBeans.add(FolderBean(folderName, folderGuid, folderCreateTime, folderModifyDevice, ProtocolTag.Folder.FOLDER_STATE_NEW, folderEncrypted, folderModifyTime, folderExtra))
                i++
            }
        }
        return folderBeans
    }

    private fun onRecoveryFolders(cloudFolders: List<FolderBean>?) {
        AppLogger.CLOUD.d(TAG, "onRecoveryFolders(), cloudFolders: ${cloudFolders?.size}")
        if (cloudFolders == null || cloudFolders.isEmpty()) {
            return
        }
        val localFolders = AppDatabase.getInstance().foldersDao().allFoldersOrderByCreateTime
        for (cloudFolder in cloudFolders) {
            val folderList = AppDatabase.getInstance().foldersDao().getFoldersWithGuidOrName(cloudFolder.mFolderGuid, cloudFolder.mFolderName)
            val folderWithSameGuid = findLocalFolderWithSameGuid(folderList, cloudFolder.mFolderGuid)
            if (null != folderWithSameGuid) {
                val iterator = localFolders.iterator()
                while (iterator.hasNext()) {
                    val folder = iterator.next()
                    if (folder.id == folderWithSameGuid.id) {
                        iterator.remove()
                        break
                    }
                }
                if (TextUtils.equals(folderWithSameGuid.name, cloudFolder.mFolderName)) {
                    handleFolderCase1(cloudFolder, folderWithSameGuid)
                } else {
                    handleFolderCase2(cloudFolder, folderWithSameGuid)
                }
            } else {
                val folderWithSameName = findLocalFolderWithSameName(folderList, cloudFolder.mFolderName)
                if (null != folderWithSameName) {
                    val iterator = localFolders.iterator()
                    while (iterator.hasNext()) {
                        val folder = iterator.next()
                        if (folder.id == folderWithSameName.id) {
                            iterator.remove()
                            break
                        }
                    }
                    handleFolderCase3(cloudFolder, folderWithSameName)
                } else {
                    cloudFolder.mState = FolderInfo.FOLDER_STATE_UNCHANGE
                    handleFolderCase5(cloudFolder)
                }
            }
        }
        val localShouldDeleted: MutableList<String> = ArrayList()
        AppLogger.CLOUD.d(TAG, "onRecoveryFolders(), localFolders: ${localFolders.size}")
        for (folder in localFolders) {
            // Couldn't delete default folder
            if (FolderInfo.FOLDER_GUID_NO_GUID == folder.guid || FolderInfo.FOLDER_GUID_ENCRYPTED == folder.guid) {
                continue
            }
            if (folder.state != FolderInfo.FOLDER_STATE_NEW) {
                localShouldDeleted.add(folder.guid)
            }
        }
        handleFolderCase6(localShouldDeleted)
    }

    /**
     * case 1: 如果guid和name都相同，则比较mModifyDevice，如果mModifyDevice不同，皆以云端为准。
     */
    private fun handleFolderCase1(cloudFolder: FolderBean, localFolder: Folder) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase1(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        if (!TextUtils.equals(localFolder.modifyDevice, cloudFolder.mModifyDevice) || localFolder.extra != null && !TextUtils.equals(localFolder.extra.toString(), cloudFolder.mExtra)) {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
        }
    }

    /**
     * case 2: the guid is the same, but the name is not the same. compare the modify-device.
     */
    private fun handleFolderCase2(cloudFolder: FolderBean, localFolder: Folder) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase2(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        val localDevice = Device.getDeviceIMEI(mContext)
        if (TextUtils.equals(localFolder.modifyDevice, cloudFolder.mModifyDevice) && TextUtils.equals(localDevice, cloudFolder.mModifyDevice)) {
            // do nothing.
        } else {
            localFolder.name = cloudFolder.mFolderName
            localFolder.modifyDevice = cloudFolder.mModifyDevice
            localFolder.modifyTime = Date(cloudFolder.mModifyTime)
            localFolder.createTime = Date(cloudFolder.mCreateTime)
            localFolder.extra = FolderExtra.create(cloudFolder.mExtra)
            localFolder.state = FolderInfo.FOLDER_STATE_UNCHANGE
            AppDatabase.getInstance().foldersDao().updateFolder(localFolder)
        }
    }

    /**
     * case 3: the guid is not the same, but the name is the same. merge the two folders with the same name.
     */
    private fun handleFolderCase3(cloudFolder: FolderBean, localFolder: Folder) {
        if (handleFolderCaseEncrypted(cloudFolder, localFolder)) {
            return
        }
        AppLogger.CLOUD.d(TAG, "handleFolderCase3(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        val info = FolderInfo(localFolder)
        info.guid = cloudFolder.mFolderGuid
        info.modifyDevice = cloudFolder.mModifyDevice
        info.createTime = cloudFolder.mCreateTime
        info.modifyTime = cloudFolder.mModifyTime
        info.state = FolderInfo.FOLDER_STATE_UNCHANGE
        info.extra = FolderExtra.create(cloudFolder.mExtra)
        FolderUtil.getInstance().updateFolderWithFolderInfoSyncForRichNote(localFolder.guid, info)
    }

    /**
     * 处理不支持加密功能版本上的创建的普通“加密笔记”，同步到支持加密版本的冲突
     * 低版本同步上来的普通加密笔记，在高版本上会新建为一个带后缀的普通“加密笔记”文件夹，如“加密笔记1”，“加密笔记2”
     * 以此类推
     *
     * @param cloudFolder 云端文件夹信息
     * @param localFolder 本地文件夹信息
     * @return
     */
    private fun handleFolderCaseEncrypted(cloudFolder: FolderBean, localFolder: Folder): Boolean {
        AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), cloudFolder: $cloudFolder, localFolder: $localFolder")
        if (FolderInfo.FOLDER_GUID_ENCRYPTED == localFolder.guid) {
            val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), localFolders: $localFolders")
            val folderNames: MutableList<String> = ArrayList()
            for (folder in localFolders) {
                if (folder.name.contains(localFolder.name) && folder.name.length > localFolder.name.length) {
                    folderNames.add(folder.name)
                }
            }
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), folderNames: $folderNames")
            var index = 0
            for (name in folderNames) {
                val suffix = name.substring(localFolder.name.length)
                index = Math.max(index, suffix.toInt())
            }
            val newFolderName = cloudFolder.mFolderName + (index + 1)
            AppLogger.CLOUD.d(TAG, "handleFolderCaseEncrypted(), newFolderName: $newFolderName")
            FolderUtil.insertFolderNameSync(mContext, newFolderName, cloudFolder.mFolderGuid,
                    Device.getDeviceIMEI(mContext), cloudFolder.mCreateTime, cloudFolder.mState, cloudFolder.mEncrypted, null)
            return true
        }
        return false
    }

    /**
     * case 5: local folders not in cloud folder list(has cloud new folders)
     */
    private fun handleFolderCase5(cloudFolder: FolderBean) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase5(), cloudNewFolder: $cloudFolder")
        if (TextUtils.isEmpty(cloudFolder.mFolderName)) {
            AppLogger.CLOUD.d(TAG, "handleFolderCase5(), name empty.")
            return
        }
        val folder = Folder()
        folder.name = cloudFolder.mFolderName
        folder.guid = cloudFolder.mFolderGuid
        folder.modifyDevice = cloudFolder.mModifyDevice
        folder.createTime = Date(cloudFolder.mCreateTime)
        folder.modifyTime = Date(cloudFolder.mModifyTime)
        folder.state = cloudFolder.mState
        folder.encrypted = cloudFolder.mEncrypted
        folder.extra = FolderExtra.create(cloudFolder.mExtra)
        AppDatabase.getInstance().foldersDao().insert(folder)
    }

    /**
     * case 6: cloud folders not in local folders list(has local new folders; cloud deleted some folders)
     */
    private fun handleFolderCase6(deletedFolders: List<String>) {
        AppLogger.CLOUD.d(TAG, "handleFolderCase6(), deletedFolders: $deletedFolders")
        FolderUtil.deleteFoldersSyncForRichNote(mContext, deletedFolders, false, false, true)
    }

    private fun findLocalFolderWithSameGuid(folderList: List<Folder>, targetGuid: String): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.guid, targetGuid)) {
                return folder
            }
        }
        return null
    }

    private fun findLocalFolderWithSameName(folderList: List<Folder>, targetName: String): Folder? {
        for (folder in folderList) {
            if (TextUtils.equals(folder.name, targetName)) {
                return folder
            }
        }
        return null
    }

    /**
     * add osSupport/appSupport params to recovery request(added in note sync api v5)
     *
     * @return the extra request params
     */
    override fun getExtraRecoveryRequestParams(): Bundle? {
        val folderEncryptOsSupport = COUIVersionUtil.getOSVersionCode() >= COUIVersionUtil.COUI_5_2
        val folderEncryptAppSupport = BuildConfig.VERSION_CODE >= Constants.FOLDER_ENCRYPT_SUPPORT_APP_VERSION
        val extras = Bundle(2)
        extras.putBoolean(ProtocolTag.CONTENT_NOTE_FOLDER_ENCRYPT_OS_SUPPORT, folderEncryptOsSupport)
        extras.putBoolean(ProtocolTag.CONTENT_NOTE_FOLDER_ENCRYPT_APP_SUPPORT, folderEncryptAppSupport)
        return extras
    }

    override fun convertToToDos(packetArray: PacketArray<*>?, opType: String): List<*>? {
        if (packetArray == null || packetArray.toT() == null) {
            return null
        }
        val size = packetArray.size()
        val toDos: MutableList<ToDo> = ArrayList(size)
        for (i in 0 until size) {
            val packet = packetArray[i]
            val toDo = ToDo()
            val globalId = packet.getString(ToDoUtils.K_GLOBAL_ID)
            toDo.globalId = UUIDConverters.stringToUUID(globalId)
            if (SyncAgentContants.OperationType.DELETE == opType) {
                toDos.add(toDo)
            } else {
                val itemId = packet.getString(ToDoUtils.K_ITEM_ID)
                var localId = UUIDConverters.stringToUUID(itemId)
                if (localId == null) {
                    toDo.status = StatusEnum.MODIFIED
                    localId = UUID.randomUUID()
                    packet.putString(ToDoUtils.K_ITEM_ID, UUIDConverters.UUIDToString(localId))
                } else {
                    toDo.status = StatusEnum.UNCHANGE
                }
                toDo.localId = localId!!
                toDo.updateTime = ToDoUtils.timestampToDate(packet.getLong(ToDoUtils.K_UPDATE_TIME))
                toDo.createTime = ToDoUtils.timestampToDate(packet.getLong(ToDoUtils.K_CREATE_TIME))
                toDo.alarmTime = ToDoUtils.timestampToDate(packet.getLong(ToDoUtils.K_ALARM_TIME))
                toDo.finishTime = ToDoUtils.timestampToDate(packet.getLong(ToDoUtils.K_FINISH_TIME))
                toDo.content = packet.getString(ToDoUtils.K_CONTENT)
                toDo.extra = ToDoExtra.create(packet.getString(ToDoUtils.K_EXTRA))
                if (RepeatDataHelper.isAlarmTimeRepeatValid(toDo)) {
                    toDo.nextAlarmTime = toDo.alarmTime
                } else {
                    toDo.nextAlarmTime = null
                }
                val alarmTime = if (toDo.alarmTime != null) toDo.alarmTime.time else -1
                if (RepeatDataHelper.isRepeat(toDo) && alarmTime < System.currentTimeMillis()) {
                    val nextTime = RepeatManage.nextAlarmTimeByRepeat(RepeatDataHelper.getRepeatData(toDo), toDo.alarmTime.time)
                    if (nextTime > 0) {
                        toDo.nextAlarmTime = Date(nextTime)
                    }
                }
                AppLogger.BASIC.d(TAG, "convertToToDos getNextAlarmTime= " + DateAndTimeUtils.timeInMillis2DateAndTime(mContext, alarmTime, true))
                toDos.add(toDo)
            }
        }
        return toDos
    }

    override fun convertToToDoResults(packetArray: PacketArray<*>?, opType: String): PacketArray<*>? {
        if (packetArray == null || packetArray.toT() == null) {
            return null
        }
        val size = packetArray.size()
        // List of Packet {"itemId", "globalId"}
        val packetFactory = mCloudContext.packetFactory
        val result = packetFactory.newKvArray()
        for (i in 0 until size) {
            val packet = packetArray[i]
            if (SyncAgentContants.OperationType.DELETE != opType) {
                val globalId = packet.getString(ToDoUtils.K_GLOBAL_ID)
                val itemId = packet.getString(ToDoUtils.K_ITEM_ID)
                var localId = UUIDConverters.stringToUUID(itemId)
                if (localId == null) {
                    localId = UUID.randomUUID()
                }
                result.add(PacketUtils.packResult(UUIDConverters.UUIDToString(localId), globalId, packetFactory))
            }
        }
        return result
    }

    override fun convertToNoteInfos(packetArray: PacketArray<*>?, opType: String): List<*>? {
        if (packetArray != null) {
            AppLogger.CLOUD.d(TAG, "onServerProcessedForRecovery--  packetArray size = " + packetArray.size())
        }
        val localFolders = FolderUtil.getInstance().queryAllFoldersSync(false)
        var noteList: ArrayList<NoteInfo?>? = null
        if (packetArray != null && packetArray.toT() != null) {
            noteList = ArrayList()
            var i = 0
            val length = packetArray.size()
            while (i < length) {
                try {
                    val packet = packetArray[i]
                    val noteInfo = NoteInfo()
                    val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
                    noteInfo.globalId = globalId
                    if (SyncAgentContants.OperationType.DELETE == opType) {
                        noteList.add(noteInfo)
                    } else {
                        val guid = packet.getString(NotesProvider.COL_GUID)
                        if (TextUtils.isEmpty(guid)) {
                            noteInfo.guid = RandomGUID.createGuid()
                            packet.putString(ToDoUtils.K_ITEM_ID, noteInfo.guid)
                        } else {
                            noteInfo.guid = guid
                        }
                        val versionVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_VERSION))
                        val version = versionVal?.toInt() ?: 0
                        val updatedVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_UPDATED))
                        val updated = updatedVal?.toLong() ?: 0
                        val toppededVal = CommonUtils.parseLong(packet.getString(NotesProvider.COL_TOPPED))
                        val topped = toppededVal?.toLong() ?: 0
                        val createConsoleVal = CommonUtils.parseLong(
                                packet.getString(NotesProvider.COL_CREATED_CONSOLE))
                        val createConsole = createConsoleVal?.toInt() ?: 0
                        val thumbTypeVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_THUMB_TYPE))
                        val thumbType = thumbTypeVal?.toInt() ?: 0
                        val thumbAttrGuid = packet
                                .getString(NotesProvider.COL_THUMB_ATTR_GUID)
                        val ownerVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_NOTE_OWNER))
                        val owner = ownerVal?.toInt() ?: 0
                        val createdVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_CREATED))
                        val created = createdVal?.toLong() ?: 0
                        val recycledTimeVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_RECYCLED_TIME))
                        val recycledTime = if (createdVal != null) recycledTimeVal.toLong() else 0
                        val alarmTimeVal = CommonUtils
                                .parseLong(packet.getString(NotesProvider.COL_ALARM_TIME))
                        var alarmTime = alarmTimeVal?.toLong() ?: 0
                        val noteSkinVal = packet.getString(NotesProvider.COL_NOTE_SKIN)
                        val noteSkin = noteSkinVal ?: ""
                        val folderName = packet.getString(ProtocolTag.CONTENT_NOTE_FOLDER_NAME)
                        val folderGuid = packet.getString(ProtocolTag.CONTENT_NOTE_FOLDER_GUID)
                        val noteExtra = packet.getString(ProtocolTag.CONTENT_NOTE_EXTRA)
                        val redNoteTitle = packet.getString(decode(ProtocolTag.CONTENT_RED_NOTE_TITLE))
                        alarmTime = getAlarmTimeForRedNote(packet, alarmTime)
                        noteInfo.version = version
                        noteInfo.updated = updated
                        noteInfo.topped = topped
                        noteInfo.createConsole = createConsole
                        noteInfo.thumbType = thumbType
                        noteInfo.content = thumbAttrGuid
                        noteInfo.owner = owner
                        noteInfo.created = created
                        noteInfo.state = NoteInfo.STATE_UNCHANGE
                        noteInfo.recycled = recycledTime
                        noteInfo.alarmTime = alarmTime
                        noteInfo.noteSkin = noteSkin
                        noteInfo.extra = NoteExtra.create(noteExtra)
                        if (!TextUtils.isEmpty(redNoteTitle)) {
                            noteInfo.title = redNoteTitle
                        }
                        if (!TextUtils.isEmpty(folderName) && !TextUtils.isEmpty(folderGuid)) {
                            // check folder info is valid for local
                            var valid = false
                            var isEncryptedCase = false
                            var newFolderName = folderName
                            for (localFolder in localFolders) {
                                if (localFolder.guid == folderGuid) {
                                    valid = true
                                    if (localFolder.name != folderName) {
                                        isEncryptedCase = true
                                        newFolderName = localFolder.name
                                    }
                                    break
                                }
                            }
                            if (valid) {
                                noteInfo.folderName = if (isEncryptedCase) newFolderName else folderName
                                noteInfo.folderGuid = folderGuid
                            } else {
                                noteInfo.folderName = mContext.resources.getString(R.string.memo_all_notes)
                                noteInfo.folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
                                noteInfo.state = NoteInfo.STATE_MODIFIED
                            }
                        }
                        if (!isNeedToSyncPrivateNote && FolderInfo.FOLDER_GUID_ENCRYPTED == noteInfo.folderGuid) {
                            i++
                            continue
                        }
                        val noteAttArray = packet
                                .getKVAsArray(NotesProvider.SYNC_NOTES_ATTRIBUTES)
                        parseAttr(noteInfo, noteAttArray)
                        noteList.add(noteInfo)
                    }
                } catch (e: Exception) {
                    AppLogger.CLOUD.e(TAG, "parse one note has error! -- " + e.message)
                    e.printStackTrace()
                }
                i++
            }
        }
        return noteList
    }

    @Throws(JSONException::class)
    private fun getAlarmTimeForRedNote(packet: Packet<*>, alarmTime: Long): Long {
        var alarmTime = alarmTime
        val redNoteReminderData = packet.getString(decode(ProtocolTag.CONTENT_RED_NOTE_DATA1))
        if (!TextUtils.isEmpty(redNoteReminderData)) {
            val `object` = JSONObject(redNoteReminderData)
            val has_remind_time = `object`.getBoolean(ProtocolTag.KEY_RED_NOTE_HAS_REMIND)
            val remind_time = `object`.getLong(ProtocolTag.KEY_RED_NOTE_REMIND_TIME)
            if (has_remind_time) {
                alarmTime = remind_time
            }
        }
        return alarmTime
    }

    override fun convertToNoteInfoResults(packetArray: PacketArray<*>?, opType: String): PacketArray<*>? {
        if (packetArray == null || packetArray.toT() == null) {
            return null
        }
        val result = ArrayList<Packet<*>>()
        val packetFactory = mCloudContext.packetFactory
        var i = 0
        val length = packetArray.size()
        while (i < length) {
            val packet = packetArray[i]
            val globalId = packet.getString(NotesProvider.COL_GLOBAL_ID)
            val guid = packet.getString(NotesProvider.COL_GUID)
            result.add(PacketUtils.packResult(guid, globalId, packetFactory))
            i++
        }
        return PacketUtils.toArray(result, packetFactory)
    }

    override fun convertToRichNotes(packetArray: PacketArray<*>?, opType: String): List<*>? {
        if (packetArray == null) {
            AppLogger.CLOUD.e(TAG, "packetArray is null")
            return null
        }
        val richNotesList = ArrayList<RichNoteWithAttachments?>()
        if (SyncAgentContants.OperationType.DELETE == opType) {
            var i = 0
            val length = packetArray.size()
            while (i < length) {
                val packet = packetArray[i]
                val globalId = packet.getString(RichNoteConstants.KEY_GLOBAL_ID)
                val richNoteWithAttachments = AppDatabase.getInstance().richNoteDao().getByGlobalId(globalId)
                richNotesList.add(richNoteWithAttachments)
                i++
            }
        } else {
            var i = 0
            val length = packetArray.size()
            while (i < length) {
                val packet = packetArray[i]
                val richNoteId = packet.getString(RichNoteConstants.KEY_LOCAL_ID)
                val richNote = createRichNote(richNoteId, packet.getLong(RichNoteConstants.KEY_CREATE_TIME))
                val attachmentList: MutableList<Attachment> = ArrayList()
                richNote.state = RichNote.STATE_UNCHANGE
                richNote.globalId = packet.getString(RichNoteConstants.KEY_GLOBAL_ID)
                richNote.rawText = packet.getString(RichNoteConstants.KEY_RAW_TEXT)
                richNote.updateTime = packet.getLong(RichNoteConstants.KEY_UPDATE_TIME)
                richNote.topTime = packet.getLong(RichNoteConstants.KEY_TOP_TIME)
                richNote.recycleTime = packet.getLong(RichNoteConstants.KEY_RECYCLE_TIME)
                richNote.alarmTime = packet.getLong(RichNoteConstants.KEY_ALARM_TIME)
                //richNote.setSkinId(packet.getString(RichNoteConstants.KEY_SKIN_ID));
                if (packet.getString(RichNoteConstants.KEY_SKIN_ID) == null) {
                    richNote.skinId = SkinData.COLOR_SKIN_WHITE
                } else {
                    richNote.skinId = packet.getString(RichNoteConstants.KEY_SKIN_ID)
                }
                richNote.rawTitle = packet.getString(RichNoteConstants.KEY_RAW_TITLE)
                richNote.version = packet.getInt(RichNoteConstants.KEY_VERSION)
                var folderGuid = packet.getString(RichNoteConstants.KEY_FOLDER_GUID)
                if (TextUtils.isEmpty(folderGuid)) {
                    folderGuid = FolderInfo.FOLDER_GUID_NO_GUID
                }
                richNote.folderGuid = folderGuid!!
                if (!isNeedToSyncPrivateNote && FolderInfo.FOLDER_GUID_ENCRYPTED == richNote.folderGuid) {
                    i++
                    continue
                }
                val attachmentArray = packet.getKVAsArray(RichNoteConstants.KEY_ATTACHMENTS)
                if (attachmentArray != null) {
                    for (j in 0 until attachmentArray.size()) {
                        val packet_att = attachmentArray[j]
                        val attachment = createAttachment(packet_att.getString(RichNoteConstants.KEY_ATTACHMENT_ID))
                        attachment.type = packet_att.getInt(RichNoteConstants.KEY_ATTACHMENT_TYPE)
                        attachment.url = packet_att.getString(RichNoteConstants.KEY_ATTACHMENT_URL)
                        attachment.richNoteId = richNoteId
                        attachmentList.add(attachment)
                    }
                }
                val extra = RichNoteExtra.create(packet.getString(RichNoteConstants.KEY_EXTRA))
                if (extra.paints != null) {
                    extra.paints = filterInvalid(attachmentList, extra.paints!!)
                    for ((id, url, relateId) in extra.paints!!) {
                        val attachment = createAttachment(id)
                        attachment.type = Attachment.TYPE_PAINT
                        attachment.url = url
                        attachment.richNoteId = richNoteId
                        attachment.subAttachment = SubAttachment(relateId!!)
                        attachmentList.add(attachment)
                    }
                }
                if (extra.voices != null) {
                    extra.voices = filterInvalid(attachmentList, extra.voices!!)
                    for ((id, url, relateId) in extra.voices!!) {
                        val attachment = createAttachment(id)
                        attachment.type = Attachment.TYPE_VOICE
                        attachment.url = url
                        attachment.richNoteId = richNoteId
                        attachment.subAttachment = SubAttachment(relateId!!)
                        attachmentList.add(attachment)
                    }
                }
                if (extra.coverPictures != null) {
                    for ((id, url) in extra.coverPictures!!) {
                        val attachment = createAttachment(id)
                        attachment.type = Attachment.TYPE_COVER_PICTURE
                        attachment.url = url
                        attachment.richNoteId = richNoteId
                        attachmentList.add(attachment)
                    }
                }
                if (extra.coverPaints != null) {
                    for ((id, url, relateId) in extra.coverPaints!!) {
                        val attachment = createAttachment(id)
                        attachment.type = Attachment.TYPE_COVER_PAINT
                        attachment.url = url
                        attachment.richNoteId = richNoteId
                        attachment.subAttachment = SubAttachment(relateId!!)
                        attachmentList.add(attachment)
                    }
                }
                if (extra.packageName != null && TextUtils.isEmpty(richNote.packageName)) {
                    richNote.packageName = extra.packageName
                }
                //复制卡片信息 Laixy
                if (extra.pageResults != null && extra.pageResults!!.size > 0) {
                    Log.i("Laixy", "11111")
                    richNote.web_notes = GsonUtil.toJson(extra.pageResults)
                }
                richNote.extra = extra
                val richNoteWithAttachments = RichNoteWithAttachments(richNote, attachmentList)
                AppLogger.CLOUD.d(TAG, "parse richNoteWithAttachments  = $richNoteWithAttachments")
                richNotesList.add(richNoteWithAttachments)
                i++
            }
        }
        return richNotesList
    }

    override fun convertToRichNoteResults(packetArray: PacketArray<*>?, opType: String): PacketArray<*>? {
        /*
        由于富文本不发送confirm请求，所以这个流程不需要解析
        doNothing
         */
        return null
    }

    override fun onUpdateModeFlag(modeFlag: Int) {
        SyncNoteServive.updateModeFlag(modeFlag)
    }

    private fun parseAttr(noteInfo: NoteInfo, jsonKv: PacketArray<*>?) {
        if (jsonKv != null && jsonKv.toT() != null) {
            if (DEBUG) {
                AppLogger.CLOUD.w(TAG, "notes_attributes:" + jsonKv.toT().toString())
            }
        } else if (jsonKv == null) {
            AppLogger.CLOUD.e(TAG, "notes_attributes is null!!!")
            return
        }
        var j = 0
        val attLength = jsonKv.size()
        while (j < attLength) {
            val packet_att = jsonKv.get(j)
            val typeVal = CommonUtils
                    .parseLong(packet_att.getString(NotesProvider.COL_TYPE /* type */))
            val type = typeVal?.toInt() ?: 0
            var attrGuid = packet_att.getString(NotesProvider.COL_FILENAME /* filename */)
            /*
            Compatible with the migration data, need to convert characters
            "<br>", "\n"
             */
            if (type == NoteAttribute.TYPE_TEXT_CONTENT) {
                attrGuid = attrGuid.replace("<br>", "\n")
            }
            val para = packet_att.getString(NotesProvider.COL_PARA /* para */)
            val attachment_id = packet_att.getString(NotesProvider.COL_ATTACHMENT_ID)
            val attachment_md5 = packet_att.getString(NotesProvider.COL_ATTACHMENT_MD5)
            @SuppressLint("WrongConstant") val noteAttribute = NoteAttribute.newNoteAttribute(type, attrGuid)
            if (noteAttribute is NoteAttribute.TextAttribute) {
                noteInfo.setWholeContentAttribute(noteAttribute)
            } else {
                noteInfo.addAttribute(noteAttribute)
            }
            noteAttribute.setOperation(NoteAttribute.OP_ADD)
            noteAttribute.param = para
            noteAttribute.attachmentSyncUrl = attachment_id
            noteAttribute.attachmentMd5 = attachment_md5
            noteAttribute.created = noteInfo.updated
            if (DEBUG) {
                AppLogger.CLOUD.d(TAG, "parseAttr  noteAttribute  = $noteAttribute")
            }
            j++
        }
        if (DEBUG) {
            AppLogger.CLOUD.d(TAG, "parseAttr  noteInfo  = $noteInfo")
        }
    }

    /**
     * 将待办列表打包为备份数据包
     *
     * @param list 待办列表
     * @return 备份数据包
     */
    private fun putToDosToSyncData(list: List<ToDo>): SyncData<PacketArray<*>?>? {
        val data = SyncData<PacketArray<*>?>()
        val factory = mCloudContext.packetFactory
        val addArray = factory.newKvArray()
        val delArray = factory.newKvArray()
        val updArray = factory.newKvArray()
        for (toDo in list) {
            AppLogger.CLOUD.d(TAG, "putToDosToSyncData: $toDo")
            val packet = ToDoUtils.toPacket(factory, toDo)
            if (toDo.isDelete && toDo.globalId != null) {
                delArray.add(packet)
            } else {
                when (toDo.status) {
                    StatusEnum.NEW -> addArray.add(packet)
                    StatusEnum.MODIFIED -> updArray.add(packet)
                    else -> {}
                }
            }
        }
        if (addArray.size() > 0) {
            data.putAddData(addArray)
        }
        if (delArray.size() > 0) {
            data.putDeletedData(delArray)
        }
        if (updArray.size() > 0) {
            data.putUpdateData(updArray)
        }
        return if (data.addData == null && data.updateData == null && data.deletedData == null) {
            null
        } else data
    }

    /**
     * 将富文本列表打包为备份数据包
     *
     * @param list 富文本列表
     * @return 备份数据包
     */
    private fun putRichNotesToSyncData(list: List<RichNoteWithAttachments>): SyncData<PacketArray<*>?>? {
        val data = SyncData<PacketArray<*>?>()
        val factory = mCloudContext.packetFactory
        val addArray = factory.newKvArray()
        val delArray = factory.newKvArray()
        val updArray = factory.newKvArray()
        AppLogger.CLOUD.d(TAG, "putRichNotesToSyncData: listSize = " + list.size)
        for ((richNote, attachments) in list) {
            val one = factory.newKv()
            one.putString(RichNoteConstants.KEY_LOCAL_ID, richNote.localId)
            one.putString(RichNoteConstants.KEY_GLOBAL_ID, richNote.globalId)
            one.putString(RichNoteConstants.KEY_RAW_TEXT, richNote.rawText)
            one.putString(RichNoteConstants.KEY_RAW_TITLE, richNote.rawTitle)
            one.putString(RichNoteConstants.KEY_FOLDER_GUID, richNote.folderGuid)
            one.putString(RichNoteConstants.KEY_CREATE_TIME, richNote.createTime.toString())
            one.putString(RichNoteConstants.KEY_UPDATE_TIME, richNote.updateTime.toString())
            one.putString(RichNoteConstants.KEY_TOP_TIME, richNote.topTime.toString())
            one.putString(RichNoteConstants.KEY_RECYCLE_TIME, richNote.recycleTime.toString())
            one.putString(RichNoteConstants.KEY_ALARM_TIME, richNote.alarmTime.toString())
            one.putString(RichNoteConstants.KEY_SKIN_ID, richNote.skinId)
            one.putInt(RichNoteConstants.KEY_DATA_VERSION, RichNoteConstants.RICH_NOTE_DATA_VERSION)
            one.putInt(RichNoteConstants.KEY_CATEGORY, RichNoteConstants.RICH_NOTE_CATEGORY_TYPE)
            one.putString(RichNoteConstants.KEY_VERSION, richNote.version.toString())
            one.putString(RichNoteConstants.KEY_ITEM_ID, richNote.localId)
            one.putString(RichNoteConstants.KEY_MD5, richNote.localId)
            val paints: MutableList<Attachment> = ArrayList()
            val voices: MutableList<Attachment> = ArrayList()
            val coverPaints: MutableList<Attachment> = ArrayList()
            val coverPics: MutableList<Attachment> = ArrayList()
            val attachmentArray = factory.newKvArray()
            var isValid = true
            if (attachments != null) {
                AppLogger.CLOUD.d(TAG, "putRichNotesToSyncData: richNoteWithAttachments.getAttachments().size = " + attachments.size)
                for (attachment in attachments) {
                    isValid = !TextUtils.isEmpty(attachment.url)
                    if (!isValid) {
                        break
                    }
                    if (attachment.type == Attachment.TYPE_VOICE) {
                        voices.add(attachment)
                    } else if (attachment.type == Attachment.TYPE_PAINT) {
                        paints.add(attachment)
                        ///todotodo 528
                    } else if (attachment.type == Attachment.TYPE_COVER_PAINT) {
                        coverPaints.add(attachment)
                        //之前没有单独将pic拿出来是因为，为了适配之前的校验逻辑，但是在高版本同步到低版本的时候，pic在array中会被删除，导致在同步到高版本的时候，校验反而失去
                    } else if (attachment.type == Attachment.TYPE_COVER_PICTURE) {
                        coverPics.add(attachment)
                    } else {
                        val oneNA = factory.newKv()
                        oneNA.putString(RichNoteConstants.KEY_ATTACHMENT_TYPE, attachment.type.toString())
                        oneNA.putString(RichNoteConstants.KEY_ATTACHMENT_ID, attachment.attachmentId)
                        oneNA.putString(RichNoteConstants.KEY_ATTACHMENT_URL, attachment.url)
                        attachmentArray.add(oneNA)
                    }
                }
            }
            one.putKVAsArray(RichNoteConstants.KEY_ATTACHMENTS, attachmentArray)
            AppLogger.CLOUD.d(TAG, "putRichNotesToSyncData: richNoteID=  " + richNote.localId + ", paints = " + paints.size + ", voices = " + voices.size)
            //在feature中将次逻辑修改到保存的时候
            val newExtra = updateAttachmentProperty(richNote.extra, paints, voices, coverPaints, coverPics, richNote.packageName)
            richNote.extra = newExtra
            AppDatabase.getInstance().richNoteDao().updateWithOutTimestamp(richNote)
            var extra = ""
            if (richNote.extra != null) {
                extra = richNote.extra.toString()
            }
            one.putString(RichNoteConstants.KEY_EXTRA, extra)
            if (richNote.deleted) {
                delArray.add(one)
            } else {
                if (isValid) {
                    if (TextUtils.isEmpty(richNote.globalId)) {
                        addArray.add(one)
                    } else if (richNote.state == RichNote.STATE_MODIFIED) {
                        updArray.add(one)
                    } else {
                        AppLogger.CLOUD.e(TAG, "richNote state is wrong! please check it")
                        AppLogger.CLOUD.e(TAG, "richNote = $richNote")
                    }
                }
            }
        }
        if (addArray.size() > 0) {
            data.putAddData(addArray)
        }
        if (delArray.size() > 0) {
            data.putDeletedData(delArray)
        }
        if (updArray.size() > 0) {
            data.putUpdateData(updArray)
        }
        return if (data.addData == null && data.updateData == null && data.deletedData == null) {
            null
        } else data
    }

    private fun putNoteInfosToSyncData(list: ArrayList<NoteInfo>): SyncData<PacketArray<*>?>? {
        val data = SyncData<PacketArray<*>?>()
        val factory = mCloudContext.packetFactory
        val addArray = factory.newKvArray()
        val delArray = factory.newKvArray()
        val updArray = factory.newKvArray()
        for (i in list.indices) {
            AppLogger.CLOUD.d(TAG, "putNoteInfosToSyncData:" + list[i].guid)
            val info = list[i]
            val one = factory.newKv()
            one.putString("itemId", info.guid)
            one.putString("guid", info.guid)
            one.putString(NotesProvider.COL_VERSION, "" + info.version)
            one.putString(NotesProvider.COL_UPDATED, "" + info.updated)
            one.putString(NotesProvider.COL_TOPPED, "" + info.topped)
            one.putString(NotesProvider.COL_CREATED_CONSOLE, "" + info.createConsole)
            one.putString(NotesProvider.COL_THUMB_TYPE, "" + info.thumbType)
            one.putString(NotesProvider.COL_THUMB_ATTR_GUID, info.content)
            one.putString(NotesProvider.COL_NOTE_OWNER, "" + info.owner)
            one.putString(NotesProvider.COL_NOTE_SKIN, info.noteSkin)
            one.putString(NotesProvider.COL_CREATED, "" + info.created)
            one.putString(NotesProvider.COL_GLOBAL_ID, info.globalId)
            one.putString(NotesProvider.COL_RECYCLED_TIME, "" + info.recycled)
            one.putString(NotesProvider.COL_ALARM_TIME_PRE, "" + info.alarmTimePre)
            one.putString(NotesProvider.COL_NOTE_SKIN_PRE, info.noteSkinPre)
            one.putString(NotesProvider.COL_RECYCLED_TIME_PRE, "" + info.recycledPre)
            one.putString(NotesProvider.COL_ALARM_TIME, "" + info.alarmTime)
            one.putString(ProtocolTag.CONTENT_NOTE_FOLDER_NAME, info.folderName)
            one.putString(ProtocolTag.CONTENT_NOTE_FOLDER_GUID, info.folderGuid)
            one.putString(ProtocolTag.CONTENT_NOTE_EXTRA, info.extra.toString())
            val list2 = ArrayList<NoteAttribute>()
            NoteInfoDBUtil.queryNoteAttributes(list2, guid = list[i].guid, false,
                    false)
            val noteAttArray = factory.newKvArray()
            var isValid = true
            for (j in list2.indices) {
                AppLogger.CLOUD.d(TAG, "putNoteInfosToSyncData, Attributes:" + list2[j].content)
                val noteAttribute = list2[j]
                val oneNA = factory.newKv()
                oneNA.putString(NotesProvider.COL_TYPE /* type */, "" + noteAttribute.type)
                oneNA.putString(NotesProvider.COL_FILENAME /* filename */,
                        noteAttribute.content)
                oneNA.putString(NotesProvider.COL_PARA /* para */, noteAttribute.param)
                oneNA.putString(NotesProvider.COL_ATTACHMENT_ID,
                        noteAttribute.attachmentSyncUrl)
                noteAttArray.add(oneNA)
                if (noteAttribute.type == NoteAttribute.TYPE_ALBUM && TextUtils.isEmpty(noteAttribute.attachmentSyncUrl)) {
                    isValid = false
                }
            }
            one.putKVAsArray(NotesProvider.SYNC_NOTES_ATTRIBUTES, noteAttArray)
            if (NoteInfo.STATE_MARK_DELETED == info.delete) {
                delArray.add(one)
            } else {
                if (isValid) {
                    when (info.state) {
                        NoteInfo.STATE_NEW -> addArray.add(one)
                        NoteInfo.STATE_MODIFIED -> updArray.add(one)
                        else -> {}
                    }
                }
            }
        }
        if (addArray.size() > 0) {
            data.putAddData(addArray)
        }
        if (delArray.size() > 0) {
            data.putDeletedData(delArray)
        }
        if (updArray.size() > 0) {
            data.putUpdateData(updArray)
        }
        return if (data.addData == null && data.updateData == null && data.deletedData == null) {
            null
        } else data
    }

    override fun onAccountLogOut(account: Account?, cleanData: Boolean, cleanNotesBySelf: Boolean) {
        if (cleanData && cleanNotesBySelf) {
            setNoteIsDeleting(true)
            setTodoIsDeleting(true)
            setRichNoteIsDeleting(true)
        }
        AppLogger.CLOUD.d(TAG, "cleanData = $cleanData,cleanNotes = $cleanNotesBySelf")
        AppExecutors.getInstance().executeCommandInDiskIO {
            if (cleanNotesBySelf) {
                cleanNotes(true)
            }
            deleteSyncStateForNote()
            cleanFolders(cleanData)
            clearSyncStateInfo()
            if (cleanData && cleanNotesBySelf) {
                setNoteIsDeleting(false)
                AppLogger.CLOUD.d(TAG, "delete note over")
                deleteFinish()
            }
            if (cleanData) {
                resetSortRule()
            }
        }
        cleanToDos(cleanData)
        cleanRichNotes(cleanData)
        cleanSetting()
    }

    private fun deleteFinish() {
        if (sDeleteFinishListener != null && !isDeleting) {
            sDeleteFinishListener!!.onDeleteFinish()
        }
    }

    private fun cleanNotes(cleanData: Boolean) {
        if (cleanData) {
            val guids = querySyncedNoteGuids()
            AppDatabase.getInstance().noteDao().deleteNotesGlobalIdNotNull()
            AppDatabase.getInstance().noteAttributeDao().deleteByNoteGuids(guids)
            val richNotesId = AppDatabase.getInstance().richNoteDao().getAllRichNotesId()
            for (guid in guids) {
                if (!TextUtils.isEmpty(guid)) {
                    FileUtil.deleteDirectory(ThumbFileManager.getFolderPathInSD(guid))
                    if (!richNotesId.contains(guid)) {
                        FileUtil.deleteDirectory(FileUtil.getFolderPathInData(mContext, guid))
                    }
                    NoteManager.deleteNoteFiles(mContext, guid)
                }
            }
        } else {
            restoreSyncInfoToDefaultNullValue()
        }
    }

    private fun querySyncedNoteGuids(): List<String> {
        return AppDatabase.getInstance().noteDao().findNoteWhereGlobalIdNotNull()
    }

    private fun deleteSyncStateForNote() {
        try {
            val cr = mContext.contentResolver
            cr.delete(SyncDataProvider.CONTENT_URI_CLEAN_SYNC_STATE, null, null)
        } catch (e: Exception) {
            AppLogger.CLOUD.e(TAG, "deleteSyncStateForNote failed. error = " + e.message)
        }
    }

    private fun restoreSyncInfoToDefaultNullValue() {
        try {
            AppDatabase.getInstance().commonDao().restoreSyncInfo()
        } catch (ignore: Exception) {
            //do noting
        }
    }

    /**
     * delete notes attributes
     */
    private fun deleteNoteAttributes(cr: ContentResolver, guid: String): Int {
        val where = NotesProvider.COL_NOTE_GUID + "=?"
        val selectionAgrs = arrayOf(guid)
        FileUtil.deleteDirectory(FileUtil.getFolderPathInData(mContext, guid))
        return cr.delete(NotesProvider.CONTENT_URI_NOTES_ATTRIBUTES, where, selectionAgrs)
    }

    private fun clearSyncStateInfo() {
        mAnchorManager.clearAnchors(SyncAgentContants.DataType.RICH_NOTE)
        mAnchorManager.clearAnchors(SyncAgentContants.DataType.NOTE)
        mAnchorManager.clearAnchors(SyncAgentContants.DataType.TODO)
    }

    private fun cleanToDos(cleanData: Boolean) {
        if (cleanData) {
            mToDoRepository!!.get().deleteAll(object : ToDoRepository.ResultCallback<Int?> {
                override fun onResult(result: Int?) {
                    AppLogger.CLOUD.d(TAG, "delete todo $result")
                    setTodoIsDeleting(false)
                    deleteFinish()
                }

            })
        } else {
            mToDoRepository!!.get().markAllLocalToDoAsNew(object : ToDoRepository.ResultCallback<Int?> {
                override fun onResult(result: Int?) {
                    AppLogger.CLOUD.d(TAG, "markAllLocalToDoAsNew todo $result")
                }
            })
        }
    }

    private fun cleanRichNotes(cleanData: Boolean) {
        AppExecutors.getInstance().executeCommandInDiskIO {
            if (cleanData) {
                getInstance(appContext).clear()
                WidgetUtils.delAllNoteWidget(appContext)
                deleteAll()
            } else {
                markAllAsNew()
            }
            setRichNoteIsDeleting(false)
            AppLogger.CLOUD.d(TAG, "delete richNote over")
            deleteFinish()
        }
    }

    private fun cleanSetting() {
            /**
             * 清除云端Setting表，SysVersion,否则切换账号后，可能同步不成功
             * 新账号云端没有，本地replace ,报错 1201 NOT_EXISTS
             * 新账号云端有，本地replace ,正常
             * 清除sysVersion后，backup都为 create
             * 云端有时，本地create，触发1202 EXISTS，走全量恢复
             *
             */
            SharedPreferencesUtil.getInstance()
                .putLong(
                    appContext,
                    SharedPreferencesUtil.SHARED_PREFERENCES_NAME,
                    SharedPreferencesUtil.SETTING_RECORD_VERSION,
                    0
                )
    }


    /**
     * when user logout, clean data in folders table.
     */
    private fun cleanFolders(cleanData: Boolean) {
        val folderUtil = FolderUtil.getInstance()
        var result = -1
        if (cleanData) {
            // delete all info in folders table except predefined folders like "全部便签"
            val folders = folderUtil.queryAllFoldersSync(true)
            val guids: MutableList<String> = ArrayList()
            val updateFolders: MutableList<FolderInfo> = ArrayList()
            for (folder in folders) {
                if (FolderInfo.FOLDER_GUID_NO_GUID == folder.guid || FolderInfo.FOLDER_GUID_ENCRYPTED == folder.guid) {
                    updateFolders.add(folder)
                    continue
                }
                guids.add(folder.guid)
            }
            result = folderUtil.deleteFoldersOnlySync(mContext, guids)
            for (folder in updateFolders) {
                folder.state = FolderInfo.FOLDER_STATE_NEW
                val newExtra = FolderExtra.create(null)
                newExtra.setSync(folder.extra.getSyncState())
                folder.extra = newExtra
                folder.sysVersion = 0
                folder.encryptSysVersion = 0
                folder.encryptedPre = folder.encrypted
            }
            val resultCode = folderUtil.updateFoldersWithFolderInfoSyncForLogout(updateFolders)
            AppLogger.CLOUD.i(TAG, "update Folders = $resultCode")
        } else {
            // change folders.state from unchanged and modified into new.
            val folders = folderUtil.queryAllFoldersSync(false)
            for (folder in folders) {
                folder.state = FolderInfo.FOLDER_STATE_NEW
                folder.sysVersion = 0
                folder.encryptSysVersion = 0
                folder.encryptedPre = folder.encrypted
            }
            result = folderUtil.updateFoldersWithFolderInfoSyncForLogout(folders)
            AppLogger.CLOUD.i(TAG, "cleanFolders update result = $result")
            result = folderUtil.deleteDeletedFoldersSync()
        }
        AppLogger.CLOUD.i(TAG, "cleanFolders result = $result")
    }

    override fun loadAnchorManager(): AbstractAnchorManager {
        return AnchorManager(mContext)
    }

    override fun loadRecurrenceMatchIdManager(): AbstractRecurrenceMatchIdManager {
        return RecurrenceMatchIdManager(mCloudContext)
    }

    override fun sendAutoSyncRequest(synctype: Int) {
        // do nothing
    }

    companion object {
        const val ITEM_ID = "itemId"
        private const val TAG = "NoteSyncAgent"
        private const val DEBUG = false
        const val BATCH_LIMIT = 200
        private var sTodoIsDeleting = false
        private var sNoteIsDeleting = false
        private var sRichNoteIsDeleting = false
        private var sDeleteFinishListener: OnDeleteFinishListener? = null
        fun setTodoIsDeleting(mTodoIsDeleting: Boolean) {
            sTodoIsDeleting = mTodoIsDeleting
        }

        fun setNoteIsDeleting(mNoteIsDeleting: Boolean) {
            sNoteIsDeleting = mNoteIsDeleting
        }

        fun setRichNoteIsDeleting(mRichNoteIsDeleting: Boolean) {
            sRichNoteIsDeleting = mRichNoteIsDeleting
        }

        val isDeleting: Boolean
            get() {
                val isDelete = sTodoIsDeleting || sNoteIsDeleting || sRichNoteIsDeleting
                AppLogger.CLOUD.d(TAG, "isDelete = $isDelete")
                return isDelete
            }

        @JvmStatic
        fun setDeleteFinishListener(listener: OnDeleteFinishListener?) {
            sDeleteFinishListener = listener
        }

        @JvmStatic
        val instance = NoteSyncAgent()
    }
}