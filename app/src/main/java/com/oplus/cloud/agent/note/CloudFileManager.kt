/***********************************************************
 * Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.oplus.cloud.agent.note

import androidx.annotation.VisibleForTesting
import com.google.gson.JsonParser
import com.nearme.note.logic.ThumbFileConstants
import com.oplus.cloud.CloudContext
import com.oplus.cloud.exceptions.ConnectServerException
import com.oplus.cloud.policy.SyncRequest
import com.oplus.cloud.protocol.HttpClientHelper
import com.oplus.cloud.protocol.HttpClientHelper.FileParam
import com.oplus.cloud.protocol.ProtocolAdapter
import com.oplus.cloud.utils.MD5Utils
import com.oplus.note.logger.AppLogger
import okhttp3.Response
import java.io.*
import java.util.*

class CloudFileManager(@get:VisibleForTesting val mCloudContext: CloudContext) {

    companion object {
        private const val TAG = "CloudFileManager"
    }

    @Deprecated("use the override one")
    // Common Method
    fun uploadFile(type: String, filePath: String?, name: String?): Response? {
        val file = File(filePath)
        val md5Str = MD5Utils.getMD5(file)
        val fileParams = arrayOf(FileParam(name, File(filePath), "", md5Str))
        return uploadFile(type, fileParams)
    }

    private fun uploadFile(type: String, fileParams: Array<FileParam>): Response? {
        val headers = HttpClientHelper.buildHttpRequestHeaders(mCloudContext)
        val httpUrl = mCloudContext.urlFactory.get(ProtocolAdapter.OPERATION_FILE_UPLOAD, SyncRequest.REQUEST_SOURCE_AUTO, type, mCloudContext.context)
        return uploadFile(httpUrl, type, headers, fileParams)
    }

    // Common Method
    private fun uploadFile(httpUrl: String, type: String, headers: HashMap<String, String>, fileParams: Array<FileParam>): Response? {
        var httpResponse: Response? = null
        try {
            httpResponse = HttpClientHelper.getInstance().postFile(headers, httpUrl, null, fileParams, type)
        } catch (e: ConnectServerException) {
            AppLogger.BASIC.d(TAG, "upload file failed in Deprecated method.")
        }
        return httpResponse
    }

    @Throws(Exception::class)
    fun uploadFile(httpUrl: String, destFile: File): String? {
        val headers = HttpClientHelper.buildHttpRequestHeaders(mCloudContext)
        AppLogger.CLOUD.d(TAG, "uploadFile:$httpUrl")
        val response = HttpClientHelper.getInstance().postFile(headers, httpUrl, destFile)

        if (response.isSuccessful) {
            return response.body?.string()?.let {
                val jsonElement = JsonParser.parseString(it)
                jsonElement.asJsonObject.get(AttributeSyncManager.FILEID)?.asString
            }
        }
        return null
    }

    @Deprecated("use the override one")
    @Throws(ConnectServerException::class)
    fun downloadFile(httpUrl: String, folder: File, fileName: String, needRename: Boolean): String? {
        var destFile: File? = null
        val headers = HttpClientHelper.buildHttpRequestHeaders(mCloudContext)
        AppLogger.CLOUD.d(TAG, "downloadFile:$httpUrl")
        val response = HttpClientHelper.getInstance().getFile(headers, httpUrl, null)
        if (response != null) {
            destFile = File(folder.path + File.separator + fileName + ThumbFileConstants.THUMB)
            if (response.body != null) {
                var inputStream: InputStream? = null
                var outStream: FileOutputStream? = null
                try {
                    inputStream = response.body!!.byteStream()
                    outStream = FileOutputStream(destFile)
                    val buf = ByteArray(1024)
                    var byteRead = 0
                    while (inputStream.read(buf).also { byteRead = it } != -1) {
                        outStream.write(buf, 0, byteRead)
                    }
                    outStream.flush()
                } catch (e: IOException) {
                    return null
                } finally {
                    try {
                        outStream?.close()
                    } catch (e: IOException) {
                        //do nothing
                    }
                    try {
                        inputStream?.close()
                    } catch (e: IOException) {
                        //do nothing
                    }
                }
            } else {
                return null
            }
        } else {
            return null
        }
        return destFile.absolutePath
    }

    @Throws(ConnectServerException::class)
    fun downloadFile(httpUrl: String, destFile: String): Boolean {
        val headers = HttpClientHelper.buildHttpRequestHeaders(mCloudContext)
        AppLogger.CLOUD.d(TAG, "downloadFile:$httpUrl")
        val response = HttpClientHelper.getInstance().getFile(headers, httpUrl, null)

        if (response.isSuccessful) {
            response.body?.byteStream()?.use { input ->
                try {
                    FileOutputStream(destFile).use { output ->
                        input.copyTo(output)
                    }
                } catch (e: FileNotFoundException) {
                    AppLogger.CLOUD.e(TAG, "${e.message}")
                    return false
                }
            }
            return true
        }
        return false
    }
}