package com.oplus.cloud.sync.richnote

import android.os.Bundle
import androidx.lifecycle.LiveData
import com.oplus.cloud.agent.ISyncAgent
import com.oplus.cloud.agent.SyncAgentContants
import com.oplus.cloud.agent.SyncData
import com.oplus.cloud.agent.note.AttachmentSyncManager
import com.oplus.cloud.agent.note.CloudFileManager
import com.oplus.cloud.agent.note.NoteSyncAgent
import com.oplus.cloud.anchor.Anchor
import com.oplus.cloud.data.PacketArray
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.policy.SyncResult
import com.oplus.cloud.sync.SyncViewModel
import com.oplus.cloud.sync.SyncViewModel.RecoverResponse
import com.oplus.cloud.sync.note.AnchorManager
import com.oplus.note.utils.SharedPreferencesUtil
import com.nearme.note.MyApplication
import com.nearme.note.appwidget.WidgetUtils
import com.nearme.note.cardwidget.provider.NoteCardWidgetProvider
import com.nearme.note.db.AppDatabase
import com.nearme.note.model.RichNoteRepository
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.nearme.note.util.FileUtil
import java.io.File
import java.util.*

class RichNoteSyncViewModel : SyncViewModel<RichNoteWithAttachments, Anchor> {

    private var mAnchorManager: AnchorManager? = null
    private var mNoteSyncAgent: NoteSyncAgent? = null
    private var hasNewData = false

    companion object {
        const val TAG = "RichNoteSyncViewModel"
    }

    init {
        mAnchorManager = AnchorManager(MyApplication.appContext)
        mNoteSyncAgent = NoteSyncAgent.instance
    }

    override fun areContentsTheSame(oldData: RichNoteWithAttachments?, newData: RichNoteWithAttachments?): Boolean {
        if (oldData == null && newData == null) {
            return true
        }
        if (oldData == null || newData == null) {
            return false
        }
        if (oldData === newData) {
            return true
        }

        return oldData.richNote.localId == newData.richNote.localId
                && oldData.richNote.globalId == newData.richNote.globalId
                && oldData.richNote.rawText == newData.richNote.rawText
                && oldData.richNote.folderGuid == newData.richNote.folderGuid
                && oldData.richNote.createTime == newData.richNote.createTime
                && oldData.richNote.updateTime == newData.richNote.updateTime
                && oldData.richNote.topTime == newData.richNote.topTime
                && oldData.richNote.recycleTime == newData.richNote.recycleTime
                && oldData.richNote.alarmTime == newData.richNote.alarmTime
                && oldData.richNote.state == newData.richNote.state
                && oldData.richNote.deleted == newData.richNote.deleted
                && oldData.richNote.skinId == newData.richNote.skinId
                && oldData.richNote.rawTitle == newData.richNote.rawTitle
                && oldData.richNote.extra == newData.richNote.extra
    }

    override fun getByData(localData: RichNoteWithAttachments?): RichNoteWithAttachments? {
        return AppDatabase.getInstance().richNoteDao().getByLocalId(localData!!.richNote.localId)
    }

    override fun getByDataAsync(localData: RichNoteWithAttachments?): LiveData<RichNoteWithAttachments?> {
        return AppDatabase.getInstance().richNoteDao().getLiveDataByLocalId(localData!!.richNote.localId)
    }

    override fun clearInvalidDirtyData() {
        val delLocalIdList = AppDatabase.getInstance().richNoteDao().getInvalidDirtyData()
        delLocalIdList.forEach { localId ->
            File(FileUtil.getFolderPathInData(MyApplication.appContext, localId)).deleteRecursively()
        }
//        AppDatabase.getInstance().richNoteDao().clearInvalidDirtyData()
        RichNoteRepository.clearInvalidDirtyData()
    }

    override fun getDirtyData(): SyncData<PacketArray<*>>? {
        return mNoteSyncAgent!!.resolveDirtyDataByAnchor(anchor, SyncAgentContants.DataType.RICH_NOTE)
    }

    override fun getDirtyDataList(): List<RichNoteWithAttachments> {
        return AppDatabase.getInstance().richNoteDao().getDirtyRichNote()
    }

    override fun getRelatedData(remoteData: RichNoteWithAttachments?): RichNoteWithAttachments? {
        return AppDatabase.getInstance().richNoteDao().getByLocalId(remoteData!!.richNote.localId)
    }

    override fun getAnchor(): Anchor? {
        return mAnchorManager!!.getLast(SyncAgentContants.DataType.RICH_NOTE, ISyncAgent.RECOVERY)
    }

    override fun setAnchor(anchor: Anchor?) {
        if (anchor != null) {
            mAnchorManager!!.setLast(SyncAgentContants.DataType.RICH_NOTE, ISyncAgent.RECOVERY, anchor.timestamp)
        }
    }

    override fun recover(extra: Bundle?, syncResult: SyncResult?, anchor: Anchor?, callback: SyncViewModel.ResultCallback<RecoverResponse<RichNoteWithAttachments, Anchor>>?) {
        syncAttributes()
        AppLogger.BASIC.d(TAG, "-----------performRecovery:[ RichNoteWithAttachments ]-----------")
        val syncType = mNoteSyncAgent!!.resolveRecoverSyncTypeByAnchor(anchor)
        hasNewData = mNoteSyncAgent!!.hasNewData(syncType, anchor, syncResult, SyncAgentContants.DataType.RICH_NOTE)
        if (!hasNewData) {
            callback!!.onResult(generateDefaultRecoverResponse(anchor))
            return
        }
        val data = mNoteSyncAgent!!.recover(syncType, anchor, syncResult, SyncAgentContants.DataType.RICH_NOTE)
        val rr = object : RecoverResponse<RichNoteWithAttachments, Anchor> {
            override fun getUpdatedDataList(): List<RichNoteWithAttachments> {
                val list = ArrayList<RichNoteWithAttachments>()
                if (data.addList != null) {
                    list.addAll(data.addList as Collection<RichNoteWithAttachments>)
                }
                if (data.updateList != null) {
                    list.addAll(data.updateList as Collection<RichNoteWithAttachments>)
                }
                return list
            }

            override fun getRemovedDataList(): List<RichNoteWithAttachments> {
                val list: MutableList<RichNoteWithAttachments> = ArrayList<RichNoteWithAttachments>()
                if (data.deleteList != null) {
                    list.addAll(data.deleteList as Collection<RichNoteWithAttachments>)
                }
                return list
            }

            override fun getAnchor(): Anchor {
                val anchor = Anchor()
                anchor.timestamp = data.anchor
                return anchor
            }
        }
        callback!!.onResult(rr)
    }

    override fun backup(extra: Bundle?, syncResult: SyncResult?, anchor: Anchor?, dirtyData: SyncData<PacketArray<*>>?, updatedDirtyDataSet: MutableSet<RichNoteWithAttachments>?, callback: SyncViewModel.ResultCallback<SyncViewModel.BackupResponse<RichNoteWithAttachments, Anchor>>?) {
        AppLogger.BASIC.d(TAG, "-----------performBackup:[ RichNoteWithAttachments ]-----------hasNewData=$hasNewData")
        syncAttributes()

        if (hasNewData) {
            hasNewData = false
            WidgetUtils.sendNoteDataChangedBroadcast(MyApplication.appContext)
            NoteCardWidgetProvider.instance.postUIToCard(false)
        }

        val hasDirtyFolders = false
        val hasDirtySetting = false
        val forceSendRequest = hasDirtyFolders || hasDirtySetting
        AppLogger.BASIC.d(TAG, "backup: -------- hasDirtyFolders: $hasDirtyFolders, hasDirtySetting: $hasDirtySetting")

        if (dirtyData == null && !forceSendRequest) {
            callback!!.onResult(generateDefaultBackupResponse(anchor))
            return
        }

        val syncType = mNoteSyncAgent?.resolveBackupSyncTypeByAnchor(anchor)
        var splitDirtyData = mNoteSyncAgent!!.splitDirtyData(dirtyData)
        if (splitDirtyData == null) {
            if (forceSendRequest) {
                //to solve a problem :
                // when notes has no dirty data, no synchronization request is sent.
                // create a new List object and add a new SyncData object to force sending this synchronization request
                splitDirtyData = ArrayList()
                splitDirtyData.add(SyncData())
            } else {
                callback!!.onResult(generateDefaultBackupResponse(anchor))
                return
            }
        }

        mNoteSyncAgent!!.backup(SyncAgentContants.DataType.RICH_NOTE, splitDirtyData, syncType, extra, syncResult, updatedDirtyDataSet)
        callback!!.onResult(generateDefaultBackupResponse(anchor))
    }

    override fun runInTransaction(runnable: Runnable?) {
        AppDatabase.getInstance().noteDao().runInTransaction(runnable)
    }

    private fun syncAttributes() {
        val cloudFileManager = CloudFileManager(NoteSyncAgent.instance.cloudContext)
        AttachmentSyncManager.sync(NoteSyncAgent.instance.cloudContext, cloudFileManager)
    }

    private fun hasDirtySetting(): Boolean {
        val currentModeFlag = SharedPreferencesUtil.getInstance().getInt(MyApplication.appContext, SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SharedPreferencesUtil.HOME_PAGE_MODE_KEY)
        val preModeFlag = SharedPreferencesUtil.getInstance().getInt(MyApplication.appContext, SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SharedPreferencesUtil.HOME_PAGE_MODE_PRE_KEY)
        val hasDirtyModeFlag = currentModeFlag != preModeFlag
        AppLogger.BASIC.d(TAG, "currentModeFlag = $currentModeFlag, preModeFlag = $preModeFlag")
        return hasDirtyModeFlag
    }

}