/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SyncManager.java
 * * Description: SyncManager
 * * Version: 1.0
 * * Date: 2019/12/17
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/12/17 1.0 build this module
 ****************************************************************/

package com.oplus.cloud.sync;

import android.os.Bundle;

import androidx.arch.core.util.Function;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.oplus.note.logger.AppLogger;
import com.oplus.cloud.policy.SyncResult;
import com.oplus.cloud.sync.note.NoteSyncOperator;
import com.oplus.cloud.sync.richnote.RichNoteOperator;
import com.nearme.note.util.LiveDataUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

public final class SyncManager {
    private static final String TAG = "SyncManager";

    private AtomicBoolean mInitiated = new AtomicBoolean();
    private SyncInnerCallbacks mInnerCallbacks;
    private Set<SyncOperator> mSyncOperators = new HashSet<>();
    private MutableLiveData<Integer> mOperatorsSizeLiveData = new MutableLiveData<>();
    private LiveData<Boolean> mIsRunningLiveData;

    private SyncManager() {
        mIsRunningLiveData = Transformations.switchMap(mOperatorsSizeLiveData, operatorsSize -> {
            List<LiveData<Boolean>> liveDataList = new ArrayList<>();
            for (SyncOperator<?, ?> operator : mSyncOperators) {
                liveDataList.add(operator.isRunningLiveData());
            }
            return LiveDataUtils.listMap(liveDataList, inputList -> {
                for (Boolean input : inputList) {
                    if ((input != null) && input) {
                        return true;
                    }
                }
                return false;
            });
        });
    }

    public static SyncManager getInstance() {
        return SingletonHolder.INSTANCE;
    }

    private static class SingletonHolder {
        private static final SyncManager INSTANCE = new SyncManager();
    }

    public void init(SyncInnerCallbacks innerCallbacks) {
        if (!mInitiated.compareAndSet(false, true)) {
            return;
        }
        mInnerCallbacks = innerCallbacks;
    }

    public SyncInnerCallbacks getInnerCallbacks() {
        return mInnerCallbacks;
    }

    public void registerOperator(SyncOperator operator) {
        if (operator == null) {
            return;
        }
        mSyncOperators.add(operator);
        mOperatorsSizeLiveData.postValue(mSyncOperators.size());
    }

    public Set<SyncOperator> getOperators() {
        return mSyncOperators;
    }

    public boolean isRunning() {
        for (SyncOperator operator : mSyncOperators) {
            if (operator.isRunning()) {
                return true;
            }
        }
        return false;
    }

    public LiveData<Boolean> isRunningLiveData() {
        return mIsRunningLiveData;
    }

    public SyncResult syncAll(Bundle extra) {
        AppLogger.BASIC.d(TAG, "syncAll");
        return doSync(extra, false, null);
    }

    private SyncResult doSync(Bundle extra, boolean async, SyncOperator.SyncCallback callback) {
        List<SyncResult> syncResultList = new ArrayList<>();
        CountDownLatch latch = new CountDownLatch(mSyncOperators.size());
        for (SyncOperator operator : mSyncOperators) {
            operator.syncAsync(extra, syncResult -> {
                if (operator instanceof RichNoteOperator) {
                    AppLogger.CLOUD.d(TAG, "RichNote sync success = " + syncResult.isSuccess() + ", RichNoteSyncResult = " + syncResult.toString());
                    SyncOperator noteOperator = new NoteSyncOperator();
                    SyncResult noteSyncResult = noteOperator.sync(extra);
                    syncResultList.add(noteSyncResult);
                    AppLogger.CLOUD.d(TAG, "Note sync success = " + noteSyncResult.isSuccess() + ", noteSyncResult = " + noteSyncResult.toString());
                } else {
                    AppLogger.CLOUD.d(TAG, "ToDo sync success = " + syncResult.isSuccess() + ", ToDoSyncResult = " + syncResult.toString());
                }
                syncResultList.add(syncResult);
                latch.countDown();

                if (async && (latch.getCount() == 0) && (callback != null)) {
                    callback.onSyncFinished(getFinalResult(syncResultList));
                }

            });
        }

        if (!async) {
            try {
                latch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return getFinalResult(syncResultList);
        } else {
            return null;
        }
    }

    private SyncResult getFinalResult(List<SyncResult> syncResultList) {
        if ((syncResultList == null) || syncResultList.isEmpty()) {
            return getInnerCallbacks().onInitResult();
        }

        for (SyncResult syncResult : syncResultList) {
            if ((syncResult != null) && !syncResult.isSuccess()) {
                return syncResult;
            }
        }

        return syncResultList.get(0);
    }
}
