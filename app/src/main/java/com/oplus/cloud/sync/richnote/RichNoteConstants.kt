package com.oplus.cloud.sync.richnote

class RichNoteConstants {

    companion object {
        const val KEY_LOCAL_ID = "localId"
        const val KEY_GLOBAL_ID = "globalId"
        const val KEY_RAW_TEXT = "rawText"
        const val KEY_RAW_TITLE = "rawTitle"
        const val KEY_FOLDER_GUID = "folderGuid"
        const val KEY_CREATE_TIME = "createTime"
        const val KEY_UPDATE_TIME = "updateTime"
        const val KEY_TOP_TIME = "topTime"
        const val KEY_RECYCLE_TIME = "recycleTime"
        const val KEY_ALARM_TIME = "alarmTime"
        const val KEY_SKIN_ID = "skinId"
        const val KEY_EXTRA = "extra"
        const val KEY_VERSION = "version"
        const val KEY_CATEGORY = "category"
        const val KEY_DATA_VERSION = "dataVersion"

        /**
         * 提供给去云端使用，itemID用以区分设备，md5用于标示唯一性
         */
        const val KEY_ITEM_ID = "itemId"
        const val KEY_MD5 = "md5"
        /**
         * 附件相关
         */
        const val KEY_ATTACHMENTS = "attachments"
        const val KEY_ATTACHMENT_ID = "id"
        const val KEY_ATTACHMENT_URL = "url"
        const val KEY_ATTACHMENT_TYPE = "type"

        const val VERSION_UNCHANGE = 0
        const val RICH_NOTE_CATEGORY_TYPE = 2
        const val RICH_NOTE_DATA_VERSION = 1
    }
}