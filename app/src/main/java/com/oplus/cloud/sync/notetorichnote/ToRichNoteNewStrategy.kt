package com.oplus.cloud.sync.notetorichnote

import com.nearme.note.model.resetToNewState
import com.nearme.note.util.NoteSearchManagerWrapper.notifyDataChange
import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.MergeStrategy
import com.oplus.cloud.sync.richnote.RichNoteStrategy

class ToRichNoteNewStrategy : RichNoteStrategy() {

    private val mRichNoteList = ArrayList<RichNoteWithAttachments>()

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Bo<PERSON>an {
        if (relatedData == null) {
            AppLogger.BASIC.d(TAG, "ToRichNoteNewStrategy merge over, localId = " + remoteData!!.richNote.localId)
            remoteData.resetToNewState()
            mRichNoteList.add(remoteData)
            if (mRichNoteList.size >= MergeStrategy.MERGE_COUNT_LIMIT) {
                repository.insertList(mRichNoteList)
                mRichNoteList.clear()
                notifyDataChange()
            }
            return true
        }
        return false
    }

    override fun mergeDataListBuffer() {
        if (mRichNoteList.size > 0) {
            repository.insertList(mRichNoteList)
            mRichNoteList.clear()
            notifyDataChange()
        }
    }

}