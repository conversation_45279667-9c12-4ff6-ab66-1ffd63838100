/****************************************************************
 * * Copyright (C), 2020-2028, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SyncInnerCallbacks.java
 * * Description: SyncInnerCallbacks
 * * Version: 1.0
 * * Date: 2020/1/6
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2020/1/6 1.0 build this module
 ****************************************************************/

package com.oplus.cloud.sync;

import android.os.Bundle;

import com.oplus.cloud.policy.SyncResult;

public interface SyncInnerCallbacks {
    SyncResult onInitResult();

    boolean onStartSync(Bundle extra, SyncResult syncResult);

    void onSyncFinished(Bundle extra, SyncResult syncResult);
}
