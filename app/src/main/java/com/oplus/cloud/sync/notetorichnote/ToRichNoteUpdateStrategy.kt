package com.oplus.cloud.sync.notetorichnote

import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.MergeStrategy
import com.oplus.cloud.sync.richnote.RichNoteConstants
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class ToRichNoteUpdateStrategy : RichNoteStrategy() {

    private val mRichNoteList = ArrayList<RichNoteWithAttachments>()

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        var isUnchanged = !relatedData!!.richNote.deleted && (relatedData.richNote.version == RichNoteConstants.VERSION_UNCHANGE)
        if (isUnchanged) {
            AppLogger.BASIC.d(TAG, "ToRichNoteUpdateStrategy merge over, update relatedData by remoteData")
            remoteData!!.richNote.state = RichNote.STATE_MODIFIED
            remoteData.richNote.version = RichNoteConstants.VERSION_UNCHANGE
            remoteData.richNote.alarmTimePre = remoteData.richNote.alarmTime
            remoteData.richNote.recycleTimePre = remoteData.richNote.recycleTime
            remoteData.richNote.skinIdPre = remoteData.richNote.skinId
            mRichNoteList.add(remoteData)
            if (mRichNoteList.size >= MergeStrategy.MERGE_COUNT_LIMIT) {
                repository.updateList(mRichNoteList)
                mRichNoteList.clear()
                NoteSearchManagerWrapper.notifyDataChange()
            }
            return true
        }
        AppLogger.BASIC.e(TAG, "add or update remoteData merge failed!")
        return false
    }

    override fun mergeDataListBuffer() {
        if (mRichNoteList.size > 0) {
            repository.updateList(mRichNoteList)
            mRichNoteList.clear()
            NoteSearchManagerWrapper.notifyDataChange()
        }
    }

}