/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SyncViewModel.java
 * * Description: SyncViewModel
 * * Version: 1.0
 * * Date: 2019/12/17
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/12/17 1.0 build this module
 ****************************************************************/

package com.oplus.cloud.sync;

import android.os.Bundle;

import androidx.lifecycle.LiveData;

import com.oplus.cloud.agent.SyncData;
import com.oplus.cloud.data.PacketArray;
import com.oplus.cloud.policy.SyncResult;

import java.util.Collections;
import java.util.List;
import java.util.Set;

public interface SyncViewModel<Data, Anchor> {
    boolean areContentsTheSame(Data oldData, Data newData);

    /**
     * Get the current value of specified local data
     * To implement this method, you may want to query its primary key
     */
    Data getByData(Data localData);

    /**
     * Get a LiveData which would notify observers about the modifications of specified local data if observed
     * To implement this method, you may want to query its primary key
     */
    LiveData<Data> getByDataAsync(Data localData);

    void clearInvalidDirtyData();

    SyncData<PacketArray<?>> getDirtyData();

    List<Data> getDirtyDataList();

    /**
     * Get the related local data from specified remote data
     */
    Data getRelatedData(Data remoteData);

    Anchor getAnchor();

    void setAnchor(Anchor anchor);

    void recover(Bundle extra, SyncResult syncResult, Anchor anchor, ResultCallback<RecoverResponse<Data, Anchor>> callback);

    void backup(Bundle extra, SyncResult syncResult, Anchor anchor, SyncData<PacketArray<?>> dirtyData, Set<Data> updatedDirtyDataSet, ResultCallback<BackupResponse<Data, Anchor>> callback);

    void runInTransaction(Runnable runnable);

    public interface RecoverResponse<Data, Anchor> {
        List<Data> getUpdatedDataList();

        List<Data> getRemovedDataList();

        Anchor getAnchor();
    }

    public interface BackupResponse<Data, Anchor> {
        Anchor getAnchor();
    }

    public interface ResultCallback<Result> {
        void onResult(Result result);
    }

    default RecoverResponse<Data, Anchor> generateDefaultRecoverResponse(Anchor anchor) {
        return new RecoverResponse<Data, Anchor>() {
            @Override
            public List<Data> getUpdatedDataList() {
                return Collections.emptyList();
            }

            @Override
            public List<Data> getRemovedDataList() {
                return Collections.emptyList();
            }

            @Override
            public Anchor getAnchor() {
                return anchor;
            }
        };
    }

    default BackupResponse<Data, Anchor> generateDefaultBackupResponse(Anchor anchor) {
        return () -> anchor;
    }
}
