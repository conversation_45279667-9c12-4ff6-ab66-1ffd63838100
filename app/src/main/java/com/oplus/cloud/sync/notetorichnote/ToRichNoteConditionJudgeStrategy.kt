/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: ToRichNoteConditionJudgeStrategy.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2022/5/21
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloud.sync.notetorichnote

import com.oplus.note.repo.note.entity.RichNoteWithAttachments
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.richnote.RichNoteStrategy

//【方案】
//当前富文本便签拉取旧笔记后，判断该旧笔记是否已转换过（ID判断），如是，则不再转换
//
//【优点】
//解决旧笔记重复问题
//
//【缺点】
//旧笔记的修改（含删除）不再同步到富文本便签
//旧笔记修改后，彻底删除富文本便签会彻底删除旧笔记
class ToRichNoteConditionJudgeStrategy : RichNoteStrategy() {

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        if (remoteData == null || relatedData != null) {
            AppLogger.BASIC.d(TAG, "RichNoteConditionJudgeStrategy merge over, remoteData is null or relatedData is not null means it has been migrated.")
            return true
        }
        return false
    }

}