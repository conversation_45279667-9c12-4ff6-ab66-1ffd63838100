package com.oplus.cloud.sync.notetorichnote

import com.nearme.note.util.NoteSearchManagerWrapper
import com.oplus.note.logger.AppLogger
import com.oplus.cloud.sync.richnote.RichNoteConstants
import com.oplus.cloud.sync.richnote.RichNoteStrategy
import com.oplus.note.repo.note.entity.RichNote
import com.oplus.note.repo.note.entity.RichNoteWithAttachments

class ToRichNoteConflictStrategy : RichNoteStrategy() {

    override fun merge(remoteData: RichNoteWithAttachments?, relatedData: RichNoteWithAttachments?): Boolean {
        if (relatedData!!.richNote.deleted) {
            AppLogger.BASIC.d(TAG, "ToRichNoteConflictStrategy merge over, delete relatedData and insert remoteData")
            repository.delete(relatedData.richNote)
            remoteData!!.richNote.state = RichNote.STATE_UNCHANGE
            repository.insert(remoteData)
            NoteSearchManagerWrapper.notifyDataChange()
            return true
        } else if (relatedData.richNote.version != RichNoteConstants.VERSION_UNCHANGE) {
            AppLogger.BASIC.d(TAG, "ToRichNoteConflictStrategy merge over, insert remoteData and give relatedData a new localID")
            repository.insert(repository.reNewRichNote(relatedData))
            repository.insert(remoteData!!)
            NoteSearchManagerWrapper.notifyDataChange()
            return true
        }
        return false
    }

}