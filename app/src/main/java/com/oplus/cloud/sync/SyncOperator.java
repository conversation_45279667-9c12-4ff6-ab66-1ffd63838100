/****************************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: SyncOperator.java
 * * Description: SyncOperator
 * * Version: 1.0
 * * Date: 2019/12/17
 * * Author: lvwuyou
 * *
 * * ---------------------- Revision History: -------------------
 * * <author> <date> <version> <desc>
 * * lvwuyou 2019/12/17 1.0 build this module
 ****************************************************************/

package com.oplus.cloud.sync;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;

import com.oplus.cloud.agent.SyncData;
import com.oplus.cloud.data.PacketArray;
import com.oplus.note.logger.AppLogger;
import com.oplus.cloud.policy.SyncResult;
import com.nearme.note.util.AppExecutors;
import com.nearme.note.util.LiveAtomicBoolean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class SyncOperator<Data, Anchor> {
    private SyncManager mSyncManager;
    private SyncViewModel<Data, Anchor> mSyncViewModel;
    private List<MergeStrategy<Data>> mUpdateStrategyList = new ArrayList<>();
    private List<MergeStrategy<Data>> mRemoveStrategyList = new ArrayList<>();
    private LiveAtomicBoolean mIsRunning = new LiveAtomicBoolean();
    private AtomicBoolean mIsMerging = new AtomicBoolean();
    private Set<DataObserver<Data>> mDataObservers = Collections.newSetFromMap(new ConcurrentHashMap<>());

    public SyncOperator(SyncViewModel<Data, Anchor> syncViewModel) {
        mSyncManager = SyncManager.getInstance();
        mSyncViewModel = syncViewModel;
    }

    protected abstract String getTag();

    public void registerUpdateStrategy(MergeStrategy<Data> updateStrategy) {
        if (updateStrategy == null) {
            return;
        }
        mUpdateStrategyList.add(updateStrategy);
    }

    public void registerRemoveStrategy(MergeStrategy<Data> removeStrategy) {
        if (removeStrategy == null) {
            return;
        }
        mRemoveStrategyList.add(removeStrategy);
    }

    public boolean isRunning() {
        return mIsRunning.get();
    }

    @NonNull
    public LiveData<Boolean> isRunningLiveData() {
        return mIsRunning.getLiveData();
    }

    public void syncAsync(Bundle extra, SyncCallback callback) {
        AppLogger.BASIC.d(getTag(), "syncAsync");
        doSync(extra, true, callback);
    }

    public SyncResult sync(Bundle extra) {
        AppLogger.BASIC.d(getTag(), "sync");
        return doSync(extra, false, null);
    }

    private SyncResult doSync(Bundle extra, boolean async, SyncCallback callback) {
        AppLogger.BASIC.d(getTag(), "doSync: async=" + async);
        SyncResult syncResult = mSyncManager.getInnerCallbacks().onInitResult();

        if (!mIsRunning.compareAndSet(false, true)) {
            return syncResult;
        }

        CountDownLatch latch = async ? null : new CountDownLatch(1);

        AppExecutors.getInstance().executeCommandInDiskIO(new Runnable() {
            @Override
            public void run() {
                Bundle finalExtra = extra;

                if (mSyncManager.getInnerCallbacks().onStartSync(finalExtra, syncResult)) {
                    AppLogger.BASIC.d(getTag(), "doSync: recover");
                    recover(finalExtra, syncResult, latch, callback);
                } else {
                    AppLogger.BASIC.d(getTag(), "doSync: stopSync");
                    stopSync(latch, syncResult, callback);
                }
            }
        });

        if (latch != null) {
            try {
                latch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return syncResult;
        } else {
            return null;
        }
    }

    private void stopSync(CountDownLatch latch, SyncResult syncResult, SyncCallback callback) {
        AppLogger.BASIC.d(getTag(), "stopSync");
        mIsRunning.set(false);
        if (latch != null) {
            latch.countDown();
        } else {
            if (callback != null) {
                callback.onSyncFinished(syncResult);
            }
        }
    }

    private void recover(Bundle extra, SyncResult syncResult, CountDownLatch latch, SyncCallback callback) {
        AppLogger.BASIC.d(getTag(), "recover");
        Anchor anchor = mSyncViewModel.getAnchor();
        mSyncViewModel.recover(extra, syncResult, anchor,
                new SyncViewModel.ResultCallback<SyncViewModel.RecoverResponse<Data, Anchor>>() {
                    @Override
                    public void onResult(SyncViewModel.RecoverResponse<Data, Anchor> recoverResponse) {
                        merge(recoverResponse, new MergeCallback() {
                            @Override
                            public void onMergeFinished() {
                                mSyncViewModel.setAnchor(recoverResponse.getAnchor());
                                clearDataObservers();
                                mIsMerging.set(false);
                                prepareDirtyData(extra, syncResult, latch, callback);
                            }
                        });
                    }
                });
    }

    private void merge(SyncViewModel.RecoverResponse<Data, Anchor> recoverResponse, MergeCallback callback) {
        AppLogger.BASIC.d(getTag(), "merge");
        // TODO should change thread here? need discuss with lvwuyou
        AppExecutors.getInstance().executeCommandInDiskIO(new Runnable() {
            @Override
            public void run() {
                mIsMerging.set(true);

                //mUpdateStrategyList现在仅剩conflict和删除相关操作没有批处理优化（note和todo都是）
                mergeDataList(recoverResponse.getUpdatedDataList(), mUpdateStrategyList);
                //mRemoveStrategyList都没有批处理优化
                mergeDataList(recoverResponse.getRemovedDataList(), mRemoveStrategyList);

                for (MergeStrategy<Data> mergeStrategy : mUpdateStrategyList) {
                    mergeStrategy.mergeDataListBuffer();
                }
                for (MergeStrategy<Data> mergeStrategy : mRemoveStrategyList) {
                    mergeStrategy.mergeDataListBuffer();
                }

                if (callback != null) {
                    callback.onMergeFinished();
                }
            }
        });
    }

    private void mergeDataList(List<Data> remoteDataList, List<MergeStrategy<Data>> mergeStrategyList) {
        AppLogger.BASIC.d(getTag(), "mergeDataList");
        if (remoteDataList == null || remoteDataList.isEmpty()) {
            return;
        }

        CountDownLatch latch = new CountDownLatch(remoteDataList.size());
        int i = 0;
        for (Data remoteData : remoteDataList) {
            mergeData(remoteData, mergeStrategyList, latch);
            i++;
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void mergeData(Data remoteData, List<MergeStrategy<Data>> mergeStrategyList, CountDownLatch latch) {
        if (remoteData == null) {
            latch.countDown();
            return;
        }
        mSyncViewModel.runInTransaction(new Runnable() {
            @Override
            public void run() {
                Data relatedData = mSyncViewModel.getRelatedData(remoteData);
                doMergeData(remoteData, relatedData, mergeStrategyList);
                latch.countDown();
            }
        });
    }

    private void doMergeData(Data remoteData, Data relatedData, List<MergeStrategy<Data>> mergeStrategyList) {
        if (!mIsMerging.get()) {
            return;
        }
        if (mergeStrategyList.isEmpty()) {
            return;
        }

        for (MergeStrategy<Data> mergeStrategy : mergeStrategyList) {
            if (mergeStrategy.merge(remoteData, relatedData)) {
                break;
            }
        }
    }

    private void observeLocalData(Data localData, LocalDataListener<Data> listener) {
        if (localData == null) {
            return;
        }
        Data data = mSyncViewModel.getByData(localData);
        LiveData<Data> dataAsync = mSyncViewModel.getByDataAsync(localData);
        if (dataAsync == null) {
            return;
        }

        CountDownLatch latch = new CountDownLatch(1);

        AppExecutors.getInstance().mainThread().execute(new Runnable() {
            @Override
            public void run() {
                DataObserver<Data> dataObserver = new DataObserver<Data>(dataAsync) {
                    @Override
                    public void onChanged(Data d) {
                        if (mSyncViewModel.areContentsTheSame(data, d)) {
                            return;
                        }
                        removeDataObserver(this);

                        if (listener != null) {
                            listener.onChanged(d);
                        }
                    }
                };
                addDataObserver(dataObserver);
                dataAsync.observeForever(dataObserver);
                latch.countDown();
            }
        });

        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void addDataObserver(DataObserver<Data> dataObserver) {
        if (dataObserver == null) {
            return;
        }
        mDataObservers.add(dataObserver);
    }

    private void removeDataObserver(DataObserver<Data> dataObserver) {
        if (dataObserver == null) {
            return;
        }
        dataObserver.removeSelf();
        mDataObservers.remove(dataObserver);
    }

    private void clearDataObservers() {
        AppExecutors.getInstance().mainThread().execute(() -> {
            for (DataObserver<Data> dataObserver : mDataObservers) {
                dataObserver.removeSelf();
            }
            mDataObservers.clear();
        });
    }

    private void prepareDirtyData(Bundle extra, SyncResult syncResult, CountDownLatch latch, SyncCallback callback) {
        mSyncViewModel.runInTransaction(new Runnable() {
            @Override
            public void run() {
                mSyncViewModel.clearInvalidDirtyData();
                SyncData<PacketArray<?>> dirtyData = mSyncViewModel.getDirtyData();
                Set<Data> updatedDirtyDataSet = getUpdatedDirtyDataSet();
                AppExecutors.getInstance().executeCommandInDiskIO(new Runnable() {
                    @Override
                    public void run() {
                        backup(extra, syncResult, dirtyData, updatedDirtyDataSet, latch, callback);
                    }
                });
            }
        });
    }

    private Set<Data> getUpdatedDirtyDataSet() {
        Set<Data> updatedDirtyDataSet = Collections.newSetFromMap(new ConcurrentHashMap<>());

        List<Data> dirtyDataList = mSyncViewModel.getDirtyDataList();
        if (dirtyDataList == null || dirtyDataList.isEmpty()) {
            return updatedDirtyDataSet;
        }

        for (Data data : dirtyDataList) {
            observeLocalData(data, new LocalDataListener<Data>() {
                @Override
                public void onChanged(Data d) {
                    if (d != null) {
                        updatedDirtyDataSet.add(d);
                    }
                }
            });
        }

        return updatedDirtyDataSet;
    }

    private void backup(Bundle extra, SyncResult syncResult, SyncData<PacketArray<?>> dirtyData,
                        Set<Data> updatedDirtyDataSet,
                        CountDownLatch latch, SyncCallback callback) {
        AppLogger.BASIC.d(getTag(), "backup");
        if (!syncResult.isSuccess()) {
            AppLogger.BASIC.d(getTag(), "backup: stopSync");
            stopSync(latch, syncResult, callback);
            return;
        }
        mSyncViewModel.backup(extra, syncResult, mSyncViewModel.getAnchor(), dirtyData, updatedDirtyDataSet,
                new SyncViewModel.ResultCallback<SyncViewModel.BackupResponse<Data, Anchor>>() {
                    @Override
                    public void onResult(SyncViewModel.BackupResponse<Data, Anchor> backupResponse) {
                        AppLogger.BASIC.d(getTag(), "backup: onSyncFinished");
                        clearDataObservers();
                        mSyncManager.getInnerCallbacks().onSyncFinished(extra, syncResult);
                        stopSync(latch, syncResult, callback);
                    }
                });
    }

    private interface LocalDataListener<Data> {
        void onChanged(Data data);
    }

    private interface MergeCallback {
        void onMergeFinished();
    }

    public interface SyncCallback {
        void onSyncFinished(SyncResult syncResult);
    }

    private static abstract class DataObserver<Data> implements Observer<Data> {
        private LiveData<Data> mLiveData;

        public DataObserver(LiveData<Data> liveData) {
            mLiveData = liveData;
        }

        public void removeSelf() {
            mLiveData.removeObserver(this);
        }
    }
}
