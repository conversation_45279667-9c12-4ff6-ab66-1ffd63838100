{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.oneplus.note", "background": "@drawable/todo_card_fingerprint", "id": "parent_con", "type": "constraint", "forceDarkAllowed": "false", "child": [{"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_large_empty", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "child": [{"visibility": "visible", "type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingTop": "12dp", "paddingStart": "15dp", "paddingEnd": "12dp", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toEndOf": "tv_todo_large_empty_title", "layout_constraintHorizontal_bias": "0", "id": "todo_large_img_add_parent", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.notice", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "child": [{"type": "image", "layout_width": "18", "layout_height": "18", "background": "@drawable/ic_launcher_nearme_note", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "todo_large_img_add", "contentDescription": "@string/todo_create", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.notice", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}]}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "todo_large_img_add", "layout_constraintTop_toTopOf": "todo_large_img_add", "layout_constraintBottom_toBottomOf": "todo_large_img_add", "layout_marginStart": 5, "id": "tv_todo_title", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_app_name", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart"}]}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "id": "tv_todo_large_empty_size", "textSize": "@dimen/dp_32", "visibility": "gone", "maxLines": "1", "ellipsize": "end", "layout_marginTop": "4dp", "layout_marginStart": "16dp", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "tv_todo_large_empty_size", "layout_constraintBottom_toBottomOf": "tv_todo_large_empty_size", "id": "tv_todo_large_empty_title", "visibility": "gone", "ellipsize": "end", "maxLines": "1", "layout_marginEnd": "16dp", "layout_marginStart": "1.8dp", "paddingBottom": "7dp", "textSize": "@dimen/dp_14", "textColor": "?attr/couiColorPrimaryText", "textAlignment": "viewStart"}, {"type": "image", "layout_width": 70, "layout_height": 70, "background": "@drawable/todo_card_expired", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "tv_todo_large_empty_content", "layout_constraintVertical_chainStyle": "packed", "id": "img_no_todo"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "img_no_todo", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 6, "id": "tv_todo_large_empty_content", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_card_offline_tips", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "tv_todo_large_empty_content", "layout_marginTop": 10, "id": "tv_todo_fill_content2", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_card_view_detail_tips", "textColor": "@color/color_alarm_time_expired", "textAlignment": "viewStart"}, {"type": "constraint", "id": "con_todo_large_privacy", "layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "child": [{"type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintBottom_toTopOf": "img_view_privacy_large", "layout_constraintTop_toBottomOf": "img_todo_large_title_icon", "id": "lay_todo_large_privacy", "child": [{"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "img_todo_card_agree_privacy_large", "id": "tv_view_privacy_large", "textSize": "@dimen/dp_12", "layout_marginTop": "2dp", "textAlignment": "center", "gravity": "center", "text": "@string/agree_statement_use_note", "textColor": "@color/coui_color_primary_neutral"}, {"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "id": "img_todo_card_agree_privacy_large", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "src": "@drawable/todo_card_agree_privacy_large", "layout_marginBottom": "10dp"}]}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "textColor": "@color/coui_color_secondary_neutral", "layout_constraintStart_toEndOf": "img_todo_large_title_icon", "layout_constraintBottom_toBottomOf": "img_todo_large_title_icon", "id": "tv_todo_large_title_text", "layout_marginStart": "6dp", "layout_marginBottom": "1.4dp", "ellipsize": "end", "maxLines": "1", "textSize": "@dimen/dp_12", "text": "@string/app_name", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "textColor": "@color/coui_color_white", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "img_view_privacy_large", "background": "@drawable/view_privacy", "layout_marginBottom": "16dp", "gravity": "center", "textAlignment": "center", "textSize": "@dimen/dp_12", "text": "@string/go_agree"}, {"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "id": "img_todo_large_title_icon", "background": "@drawable/todo_card_icon", "layout_marginStart": "16dp", "layout_marginTop": "16dp"}]}]}]}