{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.oneplus.note", "background": "@drawable/todo_card_fingerprint", "id": "parent_con", "type": "constraint", "forceDarkAllowed": "false", "child": [{"layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_large_empty", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.allnote", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "visibility": "visible", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "paddingStart": "50dp", "layout_constraintStart_toEndOf": "tv_todo_large_empty_title", "paddingEnd": "12dp", "id": "todo_large_img_add_parent", "paddingTop": "12dp", "layout_constraintHorizontal_bias": "1", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.allnote", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "contentDescription": "@string/todo_create", "background": "@drawable/todo_card_add", "id": "todo_large_img_add", "type": "image", "layout_constraintStart_toStartOf": "parent"}]}, {"layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_large_privacy", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintBottom_toTopOf": "img_view_privacy_large", "layout_constraintTop_toBottomOf": "img_todo_large_title_icon", "id": "lay_todo_large_privacy", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "type": "text", "textColor": "@color/coui_color_primary_neutral", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "2dp", "layout_constraintTop_toBottomOf": "img_todo_card_agree_privacy_large", "gravity": "center", "textAlignment": "center", "id": "tv_view_privacy_large", "text": "@string/agree_statement_use_note"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_marginBottom": "10dp", "src": "@drawable/todo_card_agree_privacy_large", "id": "img_todo_card_agree_privacy_large", "type": "image", "layout_constraintStart_toStartOf": "parent"}]}, {"ellipsize": "end", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginStart": "6dp", "type": "text", "textColor": "@color/coui_color_secondary_neutral", "layout_constraintStart_toEndOf": "img_todo_large_title_icon", "layout_constraintBottom_toBottomOf": "img_todo_large_title_icon", "layout_marginBottom": "1.4dp", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_title_text", "text": "@string/app_name"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "type": "text", "textColor": "@color/coui_color_white", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginBottom": "16dp", "background": "@drawable/view_privacy", "gravity": "center", "textAlignment": "center", "id": "img_view_privacy_large", "text": "@string/go_agree"}, {"layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/todo_card_icon", "layout_marginStart": "16dp", "id": "img_todo_large_title_icon", "layout_constraintTop_toTopOf": "parent", "type": "image", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "16dp"}]}, {"ellipsize": "end", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_32", "visibility": "gone", "layout_marginStart": "16dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "textColor": "@color/coui_color_primary_neutral", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "4dp", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_empty_size"}, {"ellipsize": "end", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_14", "visibility": "gone", "layout_marginEnd": "16dp", "layout_marginStart": "1.8dp", "type": "text", "textColor": "?attr/couiColorPrimaryText", "layout_constraintStart_toEndOf": "tv_todo_large_empty_size", "paddingBottom": "7dp", "layout_constraintBottom_toBottomOf": "tv_todo_large_empty_size", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_empty_title"}, {"ellipsize": "end", "layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "type": "text", "textColor": "@color/text_color_alpha_85", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "6dp", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintTop_toBottomOf": "img_no_todo", "textFontWeight": "500", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_empty_content", "text": "@string/todo_sync_toast_no_todo"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "168dp", "layout_height": "120dp", "layout_constraintBottom_toTopOf": "tv_todo_large_empty_content", "background": "@drawable/todo_card_no_todo", "layout_constraintVertical_chainStyle": "packed", "id": "img_no_todo", "layout_constraintTop_toTopOf": "parent", "type": "image", "layout_constraintStart_toStartOf": "parent"}]}, {"layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_large_fill", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.allnote", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "visibility": "visible", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "paddingStart": "46dp", "layout_constraintStart_toEndOf": "tv_todo_large_fill_title", "paddingEnd": "16dp", "id": "todo_large_img_add_parent", "paddingTop": "@dimen/todo_large_img_add_paddingTop", "layout_constraintHorizontal_bias": "1", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.allnote", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "contentDescription": "@string/todo_create", "background": "@drawable/todo_card_add", "id": "todo_large_img_add", "type": "image", "layout_constraintStart_toStartOf": "parent"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "0dp", "visibility": "visible", "layout_marginEnd": "16dp", "layout_marginStart": "16dp", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "-1dp", "layout_constraintBottom_toBottomOf": "con_todo_large_fill", "layout_marginBottom": "8dp", "layout_constraintTop_toBottomOf": "tv_todo_large_fill_size", "id": "con_todo_large_list", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_large_line_one", "layout_constraintTop_toBottomOf": "dividing_large_line_dummy_head", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_one", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_one_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_one_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_one"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_one", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_one_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_one", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_one"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_two", "layout_constraintTop_toBottomOf": "dividing_large_line_one", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_two", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_two_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_two_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_two"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_two", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_two_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_two", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_two"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_three", "layout_constraintTop_toBottomOf": "dividing_line_large_two", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_three", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_three_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_three_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_three"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_three", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_three_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_three", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_three"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_four", "layout_constraintTop_toBottomOf": "dividing_line_large_three", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_four", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_four_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_four_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_four"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_four", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_four_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_four", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_four"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_five", "layout_constraintTop_toBottomOf": "dividing_line_large_four", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_five", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_five_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_five_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_five"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_five", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_five_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_five", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_five"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_six", "layout_constraintTop_toBottomOf": "dividing_line_large_five", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_six", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_six_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_six_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_six"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_six", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_six_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_six", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_six"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_seven", "layout_constraintTop_toBottomOf": "dividing_line_large_six", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_seven", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_seven_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_seven_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_seven"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_seven", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_seven_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_seven", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_seven"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "dividing_line_large_eight", "layout_constraintTop_toBottomOf": "dividing_line_large_seven", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_eight", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_eight_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_eight_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_eight"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_eight", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_eight_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_eight", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_eight"}]}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "visibility": "visible", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line_large_dummy_tail", "layout_constraintTop_toBottomOf": "dividing_line_large_eight", "background": "@drawable/todo_card_item_bg", "id": "con_todo_large_list_item_nine", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "0dp", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "parent", "visibility": "visible", "layout_marginStart": "9dp", "id": "date_time_large_item_nine_ll", "layout_constraintTop_toTopOf": "parent", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color_expired", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_nine_expired"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "visibility": "gone", "layout_marginEnd": "8dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_date_time_color", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "date_time_large_item_nine"}]}, {"button": "@drawable/todo_card_radiobox_checked", "layout_width": "0dp", "layout_height": "@dimen/todo_card_item_height", "layout_constraintBottom_toBottomOf": "parent", "background": "@color/transparent", "layout_marginStart": "3dp", "id": "rdo_btn_large_item_nine", "layout_constraintTop_toTopOf": "parent", "type": "checkBox", "layout_constraintStart_toStartOf": "parent"}, {"ellipsize": "end", "layout_width": "0dp", "layout_height": "wrap_content", "textSize": "@dimen/dp_12", "layout_marginEnd": "8dp", "layout_marginStart": "9dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "singleLine": "true", "textColor": "@color/text_color_alpha_85", "layout_constraintEnd_toStartOf": "date_time_large_item_nine_ll", "layout_constraintStart_toEndOf": "rdo_btn_large_item_nine", "includeFontPadding": "false", "layout_constraintBottom_toBottomOf": "parent", "gravity": "center", "textAlignment": "viewStart", "id": "todo_large_content_item_nine"}]}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_one", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_large_line_dummy_head", "layout_constraintTop_toTopOf": "con_todo_large_list", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_two", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_one", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_large_line_one", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_three", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_two", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_two", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_four", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_three", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_three", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_five", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_four", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_four", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_six", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_five", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_five", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_seven", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_six", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_six", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_eight", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_seven", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_seven", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_marginBottom": "1dp", "layout_constraintBottom_toTopOf": "con_todo_large_list_item_nine", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_eight", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_eight", "type": "text", "layout_marginTop": "1dp"}, {"layout_width": "match_parent", "layout_height": "0dp", "layout_constraintBottom_toBottomOf": "con_todo_large_list", "layout_marginBottom": "1dp", "layout_constraintTop_toBottomOf": "con_todo_large_list_item_nine", "background": "@color/transparent", "layout_marginStart": "16dp", "id": "dividing_line_large_dummy_tail", "type": "text", "layout_marginTop": "1dp"}]}, {"ellipsize": "end", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_28", "layout_marginStart": "21dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "textColor": "@color/coui_color_primary_neutral", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "@dimen/todo_large_layout_marginTop", "textFontWeight": "600", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_fill_size", "text": "18"}, {"ellipsize": "end", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dp_14", "layout_marginEnd": "16dp", "layout_marginStart": "@dimen/todo_card_large_title_marginStart", "type": "text", "textColor": "@color/coui_color_primary_neutral", "layout_constraintStart_toEndOf": "tv_todo_large_fill_size", "paddingBottom": "@dimen/todo_card_large_title_paddingBottom", "layout_constraintBottom_toBottomOf": "tv_todo_large_fill_size", "textFontWeight": "600", "textAlignment": "viewStart", "maxLines": "1", "id": "tv_todo_large_fill_title", "text": "To-dos"}, {"ellipsize": "end", "layout_width": "match_parent", "layout_height": "38dp", "textSize": "@dimen/dp_12", "layout_marginEnd": "16dp", "layout_marginStart": "16dp", "type": "text", "singleLine": "true", "textColor": "?attr/couiColorPrimaryText", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginBottom": "7dp", "gravity": "center_vertical", "textAlignment": "viewStart", "id": "tv_large_other"}]}]}