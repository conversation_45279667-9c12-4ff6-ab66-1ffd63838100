{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.oneplus.note", "background": "@drawable/todo_middle_card_fingerprint", "id": "parent_con", "type": "constraint", "forceDarkAllowed": "false", "child": [{"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_middle_empty", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "child": [{"visibility": "visible", "type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingEnd": "12dp", "paddingStart": "15dp", "paddingTop": "12dp", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toEndOf": "tv_todo_empty_title", "layout_constraintHorizontal_bias": "0", "id": "todo_img_add_parent", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.notice", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}], "child": [{"type": "image", "layout_width": "18", "layout_height": "18", "background": "@drawable/ic_launcher_nearme_note", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "todo_img_add", "contentDescription": "@string/todo_create", "onClick": [{"flag": "67108864|268435456", "action": "action.nearme.note.notice", "packageName": "com.oneplus.note", "type": "activity", "params": {"action_from": "app_todo_card_new"}}]}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "todo_img_add", "layout_constraintTop_toTopOf": "todo_img_add", "layout_constraintBottom_toBottomOf": "todo_img_add", "layout_marginStart": 5, "id": "tv_todo_title", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_app_name", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart"}]}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "textSize": "@dimen/dp_32", "id": "tv_todo_empty_title_size", "visibility": "gone", "ellipsize": "end", "maxLines": "1", "layout_marginTop": "2dp", "layout_marginStart": "16dp", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "tv_todo_empty_title_size", "layout_constraintBottom_toBottomOf": "tv_todo_empty_title_size", "id": "tv_todo_empty_title", "visibility": "gone", "ellipsize": "end", "maxLines": "1", "layout_marginEnd": "16dp", "layout_marginStart": "1.6dp", "paddingBottom": "7dp", "textSize": "@dimen/dp_14", "textColor": "?attr/couiColorPrimaryText", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "tv_todo_fill_content2", "layout_constraintVertical_chainStyle": "packed", "id": "tv_todo_fill_content", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_card_offline_tips", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "tv_todo_fill_content", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 10, "id": "tv_todo_fill_content2", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_card_view_detail_tips", "textColor": "@color/color_alarm_time_expired", "textAlignment": "viewStart"}]}, {"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "visibility": "gone", "id": "con_todo_middle_privacy", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "child": [{"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintBottom_toTopOf": "img_view_privacy_middle", "id": "tv_view_privacy_middle", "gravity": "center", "paddingTop": "14dp", "textAlignment": "center", "textSize": "@dimen/dp_12", "text": "@string/agree_statement_use_note", "textColor": "@color/coui_color_primary_neutral"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "background": "@drawable/view_privacy", "id": "img_view_privacy_middle", "layout_marginBottom": "16dp", "gravity": "center", "textAlignment": "center", "textSize": "@dimen/dp_12", "text": "@string/go_agree", "textColor": "@color/coui_color_white"}]}]}