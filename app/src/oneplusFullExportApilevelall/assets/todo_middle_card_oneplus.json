{"type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "id": "parent_con", "background": "@drawable/todo_middle_card_fingerprint", "forceDarkAllowed": "false", "package": "com.oneplus.note", "child": [{"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "con_todo_middle_empty", "child": [{"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_marginStart": 16, "layout_marginTop": 2, "id": "tv_todo_empty_title_size", "visibility": "gone", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_32", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "tv_todo_empty_title_size", "layout_constraintStart_toEndOf": "tv_todo_empty_title_size", "layout_marginStart": 1.6, "paddingBottom": 7, "layout_marginEnd": 16, "visibility": "gone", "id": "tv_todo_empty_title", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_14", "textColor": "?attr/couiColorPrimaryText", "textAlignment": "viewStart"}, {"visibility": "visible", "type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingTop": 12, "paddingStart": 50, "paddingEnd": 12, "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toEndOf": "tv_todo_empty_title", "layout_constraintHorizontal_bias": "1", "id": "todo_img_add_parent", "onClick": [{"type": "activity", "packageName": "com.oneplus.note", "action": "action.nearme.note.allnote", "flag": "67108864|268435456", "params": {"action_from": "app_todo_card_new"}}], "child": [{"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/todo_card_add", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "id": "todo_img_add", "contentDescription": "@string/todo_create", "onClick": [{"type": "activity", "packageName": "com.oneplus.note", "action": "action.nearme.note.allnote", "flag": "67108864|268435456", "params": {"action_from": "app_todo_card_new"}}]}]}, {"type": "image", "layout_width": 112, "layout_height": 80, "background": "@drawable/todo_card_no_todo", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "tv_todo_fill_content", "layout_constraintVertical_chainStyle": "packed", "id": "img_no_todo"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "img_no_todo", "layout_constraintBottom_toBottomOf": "parent", "layout_marginTop": 2, "id": "tv_todo_fill_content", "textFontWeight": "500", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_12", "text": "@string/todo_sync_toast_no_todo", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart"}]}, {"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "con_todo_middle_fill", "child": [{"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toStartOf": "parent", "layout_marginStart": 16, "layout_marginTop": 6, "id": "tv_todo_fill_title_size", "textFontWeight": "600", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_30", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "viewStart"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintBottom_toBottomOf": "tv_todo_fill_title_size", "layout_constraintStart_toEndOf": "tv_todo_fill_title_size", "layout_marginStart": 3, "paddingBottom": 7, "layout_marginEnd": 16, "id": "tv_todo_fill_title", "textFontWeight": "600", "maxLines": 1, "ellipsize": "end", "textSize": "@dimen/dp_14", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "viewStart"}, {"visibility": "visible", "type": "constraint", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingTop": 16, "paddingStart": 46, "paddingEnd": 16, "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintStart_toEndOf": "tv_todo_fill_title", "layout_constraintHorizontal_bias": "1", "id": "todo_img_add_parent", "onClick": [{"type": "activity", "packageName": "com.oneplus.note", "action": "action.nearme.note.allnote", "flag": "67108864|268435456", "params": {"action_from": "app_todo_card_new"}}], "child": [{"type": "image", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/todo_card_add", "layout_constraintEnd_toEndOf": "parent", "layout_constraintStart_toStartOf": "parent", "id": "todo_img_add", "contentDescription": "@string/todo_create", "onClick": [{"type": "activity", "packageName": "com.oneplus.note", "action": "action.nearme.note.allnote", "flag": "67108864|268435456", "params": {"action_from": "app_todo_card_new"}}]}]}, {"visibility": "visible", "type": "constraint", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_marginStart": 16, "layout_marginEnd": 16, "layout_marginBottom": 16, "layout_constraintTop_toBottomOf": "tv_todo_fill_title_size", "id": "con_todo_middle_list", "child": [{"visibility": "visible", "type": "constraint", "layout_width": "match_parent", "layout_height": 28, "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toTopOf": "dividing_line", "id": "con_todo_middle_list_item_one", "background": "@drawable/todo_card_item_bg", "child": [{"type": "checkBox", "layout_width": "0", "layout_height": 26, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 3, "button": "@drawable/todo_card_radiobox_checked", "background": "@android:color/transparent", "id": "rdo_btn_item_one"}, {"visibility": "visible", "type": "constraint", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "date_time_item_one_ll", "layout_marginStart": 9, "child": [{"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color_expired", "textAlignment": "viewStart", "id": "date_time_item_one_expired"}, {"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color", "textAlignment": "viewStart", "id": "date_time_item_one"}]}, {"type": "text", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "rdo_btn_item_one", "layout_constraintEnd_toStartOf": "date_time_item_one_ll", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 8, "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "ellipsize": "end", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart", "id": "todo_content_item_one"}]}, {"type": "text", "id": "dividing_line", "layout_width": "match_parent", "layout_height": 1, "layout_marginTop": 1, "layout_marginBottom": 1, "layout_marginStart": 16, "layout_constraintTop_toBottomOf": "con_todo_middle_list_item_one", "layout_constraintBottom_toTopOf": "con_todo_middle_list_item_two", "background": "@color/transparent"}, {"visibility": "visible", "type": "constraint", "layout_width": "match_parent", "layout_height": 28, "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "dividing_line", "layout_constraintBottom_toTopOf": "dividing_line_two", "id": "con_todo_middle_list_item_two", "background": "@drawable/todo_card_item_bg", "child": [{"type": "checkBox", "layout_width": "0", "layout_height": 26, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 3, "button": "@drawable/todo_card_radiobox_checked", "background": "@android:color/transparent", "id": "rdo_btn_item_two"}, {"visibility": "visible", "type": "constraint", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "date_time_item_two_ll", "layout_marginStart": 9, "child": [{"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color_expired", "textAlignment": "viewStart", "id": "date_time_item_two_expired"}, {"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color", "textAlignment": "viewStart", "id": "date_time_item_two"}]}, {"type": "text", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "rdo_btn_item_two", "layout_constraintEnd_toStartOf": "date_time_item_two_ll", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 8, "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "ellipsize": "end", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart", "id": "todo_content_item_two"}]}, {"type": "text", "id": "dividing_line_two", "layout_width": "match_parent", "layout_height": 1, "layout_marginTop": 1, "layout_marginBottom": 1, "layout_marginStart": 16, "layout_constraintTop_toBottomOf": "con_todo_middle_list_item_two", "layout_constraintBottom_toTopOf": "con_todo_middle_list_item_three", "background": "@color/transparent"}, {"visibility": "visible", "type": "constraint", "layout_width": "match_parent", "layout_height": 28, "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toBottomOf": "dividing_line_two", "layout_constraintBottom_toBottomOf": "parent", "id": "con_todo_middle_list_item_three", "background": "@drawable/todo_card_item_bg", "child": [{"type": "checkBox", "layout_width": "0", "layout_height": 26, "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 3, "button": "@drawable/todo_card_radiobox_checked", "background": "@android:color/transparent", "id": "rdo_btn_item_three"}, {"visibility": "visible", "type": "constraint", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "date_time_item_three_ll", "layout_marginStart": 9, "child": [{"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color_expired", "textAlignment": "viewStart", "id": "date_time_item_three_expired"}, {"visibility": "gone", "type": "text", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "textColor": "@color/text_date_time_color", "textAlignment": "viewStart", "id": "date_time_item_three"}]}, {"type": "text", "layout_width": "0", "layout_height": "wrap_content", "layout_constraintStart_toEndOf": "rdo_btn_item_three", "layout_constraintEnd_toStartOf": "date_time_item_three_ll", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_marginStart": 8, "layout_marginEnd": 8, "singleLine": "true", "textSize": "@dimen/dp_12", "gravity": "center", "ellipsize": "end", "textColor": "@color/text_color_alpha_85", "textAlignment": "viewStart", "id": "todo_content_item_three"}]}]}, {"type": "text", "layout_width": "match_parent", "layout_height": "37", "layout_marginBottom": 7, "layout_marginStart": 16, "layout_marginEnd": 16, "gravity": "center_vertical", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintStart_toStartOf": "parent", "singleLine": "true", "textSize": "@dimen/dp_12", "ellipsize": "end", "textColor": "?attr/couiColorPrimaryText", "textAlignment": "viewStart", "id": "tv_other"}]}, {"visibility": "gone", "type": "constraint", "layout_width": "match_parent", "layout_height": "match_parent", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "id": "con_todo_middle_privacy", "child": [{"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "paddingTop": 14, "layout_constraintBottom_toTopOf": "img_view_privacy_middle", "id": "tv_view_privacy_middle", "textSize": "@dimen/dp_12", "text": "@string/agree_statement_use_note", "textColor": "@color/coui_color_primary_neutral", "textAlignment": "center", "gravity": "center"}, {"type": "text", "layout_width": "wrap_content", "layout_height": "wrap_content", "background": "@drawable/view_privacy", "layout_constraintStart_toStartOf": "parent", "layout_constraintEnd_toEndOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "text": "@string/go_agree", "textSize": "@dimen/dp_12", "textColor": "@color/coui_color_white", "layout_marginBottom": 16, "id": "img_view_privacy_middle", "textAlignment": "center", "gravity": "center"}]}]}