<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="oppo.permission.OPPO_COMPONENT_SAFE"
        tools:node="remove" />

    <uses-permission android:name="oplus.permission.OPLUS_COMPONENT_SAFE" />

    <uses-feature
        android:name="com.oneplus.software.overseas"
        android:required="true" />

    <queries>
        <package android:name="com.nearme.sync" tools:node="remove" />
        <package android:name="com.coloros.cloud" tools:node="remove" />
        <package android:name="com.coloros.speechassist" tools:node="remove" />
        <package android:name="com.iflytek.speechsuite" tools:node="remove" />
    </queries>

    <application>
        <meta-data
            android:name="OplusPermissionKey"
            android:value="${permission_key_oplus}"
            tools:node="replace" />
        <meta-data
            android:name="OppoPermissionKey"
            android:value="${permission_key_oplus}"
            tools:node="remove"/>

        <!-- ptc auth code -->
        <meta-data
            android:name="com.oplus.ocs.oaf.AUTH_CODE"
            android:value="ATBFAiBgVgkTfRbgMp3bE9nVRJqLQJfqU7LvORp3b92nTWtaUQIhAM7W+UIRsgKnSRqBVO+EjYjIz/4fcZZm27+1FTHL02gMcbCjaQgAAAA="
            tools:node="replace"/>

        <!-- appcard -->
        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBFAiBg6riaYTh/YaR5aL8K8zciFYHHdNC0mG9mIQx568yYcgIhAPwM1HI8hFWZZcin56tbdna05AWeXmIz2lY353iGwT6hbCSc6g=="
            tools:node="replace" />

        <!--    AppPlatform 鉴权码技术方案：https://odocs.myoas.com/docs/R13j8rW7N4FpDek5    -->
        <!--    AppPlatform 鉴权码申请：https://odocs.myoas.com/forms/e1Az4VMPK ghLx2qW/fill    -->
        <!--    外销鉴权码     -->
        <meta-data
            android:name="OsdkSecurityCode"
            android:value="ATBFAiEAl+CKOXbffdSpvu0Py/+KJNAIJmXcjvbUIroVtN9pde0CIC5kW8FeEGAvQTq3xFSc+gakZu8QXJss8fAWlLqwdLOpZolOuk9wbHVzQWN0aXZpdHlNYW5hZ2VyLmdldEN1cnJlbnRVc2VyIwAAAA=="
            tools:node="replace" />

        <service
            android:name="com.oplus.migrate.backuprestore.plugin.NotePluginService"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:exported="true"
            tools:node="replace">

            <meta-data
                android:name="uniqueID"
                android:value="818005">
            </meta-data>

            <meta-data
                android:name="version"
                android:value="1">
            </meta-data>

            <meta-data
                android:name="isVendorSupport"
                android:value="true">
            </meta-data>

            <meta-data
                android:name="backup_name_resId"
                android:value="@string/app_name">
            </meta-data>

            <meta-data
                android:name="servicePriority"
                android:value="2">
            </meta-data>

            <meta-data
                android:name="minSupportOsVersion"
                android:value="19">
            </meta-data>

            <intent-filter>
                <action android:name="com.coloros.br.service" />
                <action android:name="com.heytap.br.service" />
                <action android:name="com.oplus.br.service" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <provider
            android:name="com.nearme.note.db.NotesProvider"
            android:authorities="${applicationId}.notesprovider;com.nearme.note"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace">
            <meta-data
                android:name="com.nearme.note.db.insert_todo"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.insert_text_note"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.insert_pic_text_note"
                android:value="true" />
            <meta-data
                android:name="com.nearme.note.db.repeat_todo"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.db.delete_text_note"
                android:value="true" />
            <meta-data
                android:name="com.oplus.note.update_todo"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.query_notes_no_limit"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_allow_create_time"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_allow_folder_id"
                android:value="true"/>
            <meta-data
                android:name="com.oplus.note.db.insert_note_api_version"
                android:value="3"/>
        </provider>

        <activity-alias
            android:name="com.nearme.note.activity.edit.NoteViewEditActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:resizeableActivity="true"
            android:screenOrientation="behind"
            android:targetActivity="com.nearme.note.activity.richedit.NoteViewRichEditActivity"
            android:theme="@style/AppBaseTheme.NoActionBar"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:node="replace"/>

        <activity
            android:name="com.nearme.note.activity.richedit.NoteViewRichEditActivity"
            android:configChanges="keyboardHidden|keyboard|orientation|screenSize|mcc|mnc|navigation|screenLayout|smallestScreenSize"
            android:resizeableActivity="true"
            android:exported="true"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.DetailPage"
            tools:node="replace">
        </activity>

        <activity
            android:name="com.nearme.note.setting.opensourcelicense.OpenSourceActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"
            tools:node="replace"/>

        <activity
            android:name="com.nearme.note.setting.SettingsAboutActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"
            tools:node="replace"/>

        <activity
            android:name="com.nearme.note.setting.SettingPrivacyActivity"
            android:configChanges="keyboardHidden|navigation|screenSize|screenLayout|smallestScreenSize"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustPan"
            tools:node="replace"/>

        <activity
            android:name="com.nearme.note.appwidget.todowidget.TodoSettingActivity"
            android:configChanges="keyboardHidden|screenSize|mcc|mnc|navigation|screenLayout"
            android:exported="true"
            android:launchMode="singleInstance"
            android:resizeableActivity="false"
            android:noHistory="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppTheme.DayNight.ToDoSettings"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize"
            tools:node="replace">
            <intent-filter>
                <action android:name="action.oplus.note.todo.setting" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- coloros SyncNoteServive -->
        <service
            android:name="com.oplus.cloud.sync.note.SyncNoteServive"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace">
            <intent-filter>
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <!-- heytap SyncNoteServive -->
        <service
            android:name="com.heytap.cloud.sync.note.SyncNoteServive"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:exported="true"
            tools:node="replace">
            <intent-filter>
                <action android:name="com.heytap.cloud.REMOTE_SYNC_MODULE_NOTE" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>

        <receiver
            android:name="com.nearme.note.util.AlarmUtils$AlarmReceiver"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:exported="true"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.nearme.note.appwidget.todowidget.ToDoWidgetProvider"
            android:exported="true"
            android:label="@string/todo_widget"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:readPermission="com.android.launcher.permission.READ_SETTINGS"
            android:writePermission="com.android.launcher.permission.WRITE_SETTINGS"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
                <action android:name="com.oplus.note.action.CLICK_TODO_LIST" />
                <action android:name="com.oplus.note.action.TODO_DATA_CHANGED" />
                <action android:name="com.oplus.note.action.TODO_LAUNCHER_VISIBLE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/oxygen_todo_appwidget" />
        </receiver>

        <receiver
            android:name="com.nearme.note.appwidget.notewidget.NoteWidgetProvider"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:readPermission="com.android.launcher.permission.READ_SETTINGS"
            android:writePermission="com.android.launcher.permission.WRITE_SETTINGS"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.intent.action.LOCALE_CHANGED" />
                <action android:name="com.oplus.note.action.NOTE_DATA_CHANGED" />
                <action android:name="com.oplus.note.action.NOTE_LAUNCHER_VISIBLE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/note_appwidget" />
        </receiver>

        <receiver
            android:name="com.nearme.note.TheLocaleChangeReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace">
            <intent-filter>
                <action android:name="android.intent.action.LOCALE_CHANGED" />
            </intent-filter>
        </receiver>

        <provider
            android:name="com.oplus.migrate.backuprestore.NoteBackupRestoreProvider"
            android:authorities="com.oneplus.provider.Note"
            android:exported="true">
        </provider>

        <activity
            android:name="com.nearme.note.view.TodoModalActivity"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.Translucent"
            android:windowSoftInputMode="adjustResize"
            android:resizeableActivity="false"
            tools:ignore="LockedOrientationActivity"
            android:exported="true"
            tools:node="replace"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.oplus.notes.action.edit_todo"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <receiver
            android:name="com.nearme.note.util.UCReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace">
            <intent-filter>
                <action android:name="oppo.intent.action.usercenter.ACCOUNT_LOGOUT" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.nearme.note.setting.SettingsSyncSwitchActivity"
            android:exported="true"
            android:icon="@drawable/ic_launcher_nearme_note"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppTheme.DayNight.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace">
            <intent-filter>
                <action android:name="oplus.intent.action.NOTE_CLOUD_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>
</manifest>