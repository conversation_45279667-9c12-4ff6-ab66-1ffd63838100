apply plugin: 'com.google.protobuf'

dependencies {
    implementation('com.oplus.pantanal.card:seedling-support-internal:2.0.9') {
        exclude group: 'com.google.android.material'
    }

    implementation 'com.google.protobuf:protobuf-java:3.23.3'
    implementation("com.squareup.retrofit2:converter-protobuf:$retrofit") {
        exclude group: 'com.google.protobuf', module: 'protobuf-java'
    }
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.10.0'
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                remove java
            }
            task.builtins {
                java {}
            }
        }
    }
}
