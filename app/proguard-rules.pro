# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html

# Optimizations: If you don't want to optimize, use the
# proguard-android.txt configuration file instead of this one, which
# turns off the optimization flags.  Adding optimization introduces
# certain risks, since for example not all optimizations performed by
# ProGuard works on all versions of Dalvik.  The following flags turn
# off various optimizations known to have issues, but the list may not
# be complete or up to date. (The "arithmetic" optimization can be
# used if you are only targeting Android 2.0 or later.)  Make sure you
# test thoroughly if you go this route.
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
-ignorewarnings

# The remainder of this file is identical to the non-optimized version
# of the Proguard configuration file (except that the other file has
# flags to turn off optimization).

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable

-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.widget.ListView
-keep public class * extends android.webkit.WebView
-keep public class * extends android.app.Service
-keep public class * extends android.app.Fragment
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class com.oppo.statistics.** { *; }
-keep public class com.oplus.statistics.** { *; }
-keep public class env.** { *; }
-keep public class com.baidu.** { *; }
-keep public class com.oppo.upgrade.** { *; }
-keep public class com.nearme.** { *; }

-keep class com.oplus.cloudkit.metadata.** { *; }
-keep class com.oplus.push.PushService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.

-keepattributes Signature
-keepattributes *Annotation*

-dontwarn com.color.commons.**
-keep class com.color.commons.**{*;}

-dontwarn com.coloros.cloud.**
-keep class com.coloros.cloud.** { *;}

-dontwarn com.heytap.cloud.**
-keep class com.heytap.cloud.** { *;}

-dontwarn com.oplus.cloud.**
-keep class com.oplus.cloud.** { *;}

-dontwarn android.support.**
-keep class android.support.** { *;}

-dontwarn com.baidu.oauth.**
-keep class com.baidu.oauth.**{*;}

-dontwarn com.google.**
-keep class com.google.**{*;}

-dontwarn color.support.**
-keep class color.support.**{*;}

-dontwarn com.squareup.**
-keep class com.squareup.**{*;}

-dontwarn okio.**
-keep class okio.**{*;}

-keep public class com.coloros.speechassist.engine.info.** { *; }

-keep public class com.nearme.note.view.helper.ViewPaddingTopSetter{*;}
-keep public class com.nearme.note.view.helper.ViewHeightSetter{*;}

# 单应用分屏 混淆规则
-dontwarn com.itgsa.opensdk.wm.**
-keep class com.itgsa.opensdk.wm.** {*;}
-keep interface com.itgsa.opensdk.wm.** {*;}

# if do not add this, the to-do icon can not show correctly in the WebView within the share page.
-keepclassmembers class **.R$* {
    public static <fields>;
}
-keep class **.R$*

-keep class com.nearme.patchtool.PatchTool{*;}
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }
-useuniqueclassmembernames

# NoteDrawerLayout
-keepclasseswithmembernames class com.nearme.note.view.NoteDrawerLayout{
    <fields>;
}
-keep class com.heytap.accountsdk.**{*;}
-keep class com.nearme.patchtool.PatchTool{*;}
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }
-useuniqueclassmembernames

# For Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

#---------------------账号sdk--------------------------------------------
-keep class com.nearme.aidl.** { *; }
-keep class * implements com.platform.usercenter.annotation.NoProguard {
  public *;
}
-keep class com.heytap.usercenter.accountsdk.BuildConfig { *; }
-keep class com.platform.usercenter.annotation.Keep
-keep @com.platform.usercenter.annotation.Keep class * {*;}
-keep class com.heytap.uccompiler.annotation.interceptor.** { *; }
-keep class com.accountbase.** { *; }
-keep class com.heytap.usercenter.**{*;}
-keep class com.platform.usercenter.**{*;}
-keep class com.platform.usercenter.basic.annotation.Keep
-keep @com.platform.usercenter.basic.annotation.Keep class * {*;}

-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class *
-keep class android.content.pm.**{*;}
# 校验系统
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# ipc
-keep public abstract interface * extends android.os.IInterface{*;}
-keep public abstract class * extends android.os.Binder{*;}

-keep class com.accountservice.** { *; }
#---------------------账号sdk--------------------------------------------
####stdid
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }

-keep class com.color.support.widget.ColorSearchView** {*;}

# 修改为官方提供的混淆规则
-keep class com.heytap.reflect.** {*;}
-keep class mirror.android.** {*;}
-keepclassmembers class com.heytap.compat.**.*Native {
static com.heytap.reflect.Ref* *;
}
-keepclassmembers class com.heytap.compat.**.*Native$* {
static com.heytap.reflect.Ref* *;
}
-keep class com.heytap.compat.app.ActivityManagerNative$ProcessObserver { *;}
-keep class com.heytap.compat.app.ActivityManagerNative$PackageDataObserver{ *;}

-keep class com.heytap.backup.sdk.** { *; }
-keep class com.oplus.backup.sdk.** { *; }
-keep class com.nearme.note.migration.** { *; }
#keep backuprestore plugin to fix br classloader can not find br plugins
-keep class com.oplus.migrate.backuprestore.plugin.** { *; }

# ------------------------------------------api-adapter---------------------------------------------
-dontwarn com.color.inner.**
-dontwarn com.oplus.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**
-dontwarn com.oplus.compat.**
-dontwarn com.oplus.epona.internal.LoggerSnapshot
-dontwarn com.oplus.epona.internal.LoggerSnapShotOplusCompat
-dontwarn android.telephony.**

-keep class mirror.android.** {*;}
-keep class com.oplus.utils.reflect.** {*;}
-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}
-keep class com.heytap.epona.ExceptionInfo {*;}
-keep class com.oplus.compat.app.ActivityManagerNative$ProcessObserver { *;}
-keep class com.oplus.compat.app.ActivityManagerNative$PackageDataObserver{ *;}
-keepclassmembers class com.oplus.compat.**.*Native {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Native$* {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat$* {
 static com.oplus.utils.reflect.Ref* *;
}
# ------------------------------------------api-adapter---------------------------------------------

# ---------------------------------------------support----------------------------------------------
-dontwarn com.coui.appcompat.**
-dontwarn com.coui.appcompat.widget.**
-dontwarn com.coui.appcompat.dialog.**
-dontwarn com.coui.appcompat.preference.**
-dontwarn com.coui.appcompat.util.**

-keep class androidx.appcompat.widget.ActionMenuPresenter$*{*;}
-keep class com.coui.appcompat.**{*;}
-keep class com.oplus.gaussianblurlib.**{*;}
# ---------------------------------------------support----------------------------------------------


-keep class androidx.recyclerview.widget.ViewBoundsCheck {*;}


#--------------------------------涂鸦SDK start----------------------------------------
-keep class com.oplusos.vfxsdk.doodleengine.** {*;}
#--------------------------------涂鸦SDK end------------------------------------------


#--------------------------------feedback SDK start----------------------------------------
-keep public class com.customer.feedback.sdk.**{*;}
-keep @com.oplus.baselib.database.annotation.DbEntity class * {*;}
-keepclassmembers class * {
    @com.oplus.nearx.cloudconfig.anotation.FieldIndex *;
}
-keep @androidx.anntotation.Keep class **
-keep public class com.customer.feedback.sdk.model.RequestData { *; }
-keep public class com.customer.feedback.sdk.model.RequestData$* { *;}
#--------------------------------feedback SDK end------------------------------------------

#--------------------------------时间解析SDK start----------------------------------------
-keepclasseswithmembernames class * {
    native <methods>;
}
-keep class com.oppo.library.** {*;}
-keep class com.oplus.cu.ssa.** {*;}
-keep class com.slp.**{*;}
#--------------------------------时间解析SDK end----------------------------------------

#--------------------------------融合桌面SDK start----------------------------------------
-keep class com.oplus.cardwidget.proto.** {*;}
-keep class com.google.protobuf.** {*;}
#--------------------------------融合桌面SDK end----------------------------------------

#--------------------------------问卷 start----------------------------------------
#-keep class com.oplus.questionnaire.**{ *; }
-keep class com.oplus.questionnaire.network.InputServiceInfoParams{ *; }
-keep class com.oplus.questionnaire.network.model.QuestionnaireDto{ *; }
-keep class com.oplus.questionnaire.domain.**{ *; }
-keep class com.oplus.questionnaire.presentation.SubmitResult{ *; }
-keep class com.oplus.questionnaireui.data.**{ *; }

-dontwarn com.google.**
-keep class com.google.** {
    *;
}
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

-keep class com.oplus.questionnaire.data.** {
    *;
}
# Retrofit2
-dontwarn okio.**
-dontwarn retrofit2.Platform$Java8

-keep class com.oplus.direct.OplusDirectFindCmd
-keepclasseswithmembers class com.oplus.direct.OplusDirectFindCmd { *; }

-keep class com.oplus.favorite.OplusFavoriteHelper
-keepclasseswithmembers class com.oplus.favorite.OplusFavoriteHelper { *; }

-keep class com.oplus.smartanalysis.rule.http.RuleHttpResponse
-keepclasseswithmembers class com.oplus.smartanalysis.rule.http.RuleHttpResponse { *; }

-keep class com.oplus.smartanalysis.rule.db.Rule
-keepclasseswithmembers class com.oplus.smartanalysis.rule.db.Rule { *; }

#--------------------------------问卷 end----------------------------------------

#---------office sdk start-----

-keep class com.fasterxml.aalto.** { *; }
-dontwarn com.fasterxml.aalto.**
-keep class com.microsoft.schemas.** { *; }
-dontwarn com.microsoft.schemas.**
-keep class com.zaxxer.sparsebits.** { *; }
-dontwarn com.zaxxer.sparsebits.**
-keep class org.apache.** { *; }
-dontwarn org.apache.**
-keep class org.codehaus.stax2.** { *; }
-dontwarn org.codehaus.stax2.**
-keep class org.etsi.uri.x01903.v13.** { *; }
-dontwarn org.etsi.uri.x01903.v13.**
-keep class org.openxmlformats.schemas.** { *; }
-dontwarn org.openxmlformats.schemas.**
-keep class org.w3.x2000.x09.xmldsig.** { *; }
-dontwarn org.w3.x2000.x09.xmldsig.**
-keep class schemaorg_apache_xmlbeans.** { *; }
-dontwarn schemaorg_apache_xmlbeans.**
-keep class com.oplus.office.pdf.pdfmanager.MarkerStyleType {*;}
#------office sdk end---------
#--------------------------------问卷 end----------------------------------------

#--------------------------------CloudKit sdk start----------------------------------------
-keep class com.heytap.env.** { *;}
-keep class com.heytap.cloudkit.libsync.service.** { *;}
#--------------------------------CloudKit sdk end----------------------------------------

#--------------------------------TodoCard ---------------------------------------
-keep class com.oplus.scenecard.** { *; }
-keep class com.oplus.note.todo.repository.** { *; }

-keep class androidx.core.content.FileProvider {*;}
-keep class androidx.core.content.FileProvider$PathStrategy {*;}
-keep class androidx.core.content.FileProvider$SimplePathStrategy {*;}
#--------------------------------TodoCard end---------------------------------------
#--------------------------------搬家 start---------------------------------------
-keep class com.oplus.migrate.backuprestore.plugin.third.NoteMetaInfo { *; }
-keep class com.oplus.migrate.backuprestore.plugin.third.ThirdNoteRestorePlugin { *; }
#--------------------------------搬家 end---------------------------------------
#-----覆盖率测试SDK混淆规则： begin---------
-keep class com.autotest.opasm.**{*;}
#-----覆盖率测试SDK混淆规则：end---------

-keep class com.coui.appcompat.picker.COUITimeLimitPicker { *; }
-keep class java.lang.invoke.StringConcatFactory{*;}

#--------------------------------tbl webview ---------------------------------------
-keep class com.heytap.tbl.**{ *; }
-keep class org.chromium.support_lib_boundary.** { *; }
-keepnames class org.chromium.support_lib_boundary.** { *; }
#--------------------------------tbl webview end---------------------------------------

-keep class com.oplus.note.util.sellmode.** { *; }

#--------------------------------dmp---------------------------------------
-keep class com.oplus.dmp.sdk.** { *; }
-keep class com.oplus.note.search.HighLight { *; }
-keep class com.oplus.note.search.HighLightItem { *; }
-keep class com.oplus.dmp.sdk.analyzer.** { *; }
-keep class com.oplus.dmp.sdk.index.** { *; }
-keep class com.oplus.dmp.sdk.search.** { *; }
-keep class com.oplus.dmp.sdk.common.dict.** { *; }
-keep class com.oplus.dmp.sdk.common.exception.** { *; }
-keep class com.oplus.dmp.sdk.common.log.** { *; }
-keep class com.oplus.dmp.sdk.common.thread.** { *; }
-keep class com.oplus.dmp.sdk.querypreprocess.** { *; }
-keep class com.oplus.dmp.sdk.version.VersionProtocol { *; }
-keep class com.oplus.dmp.sdk.GlobalContext { *; }
-keep class com.oplus.dmp.sdk.callback.** { *; }
-keep class com.oplus.dmp.sdk.control.** { *; }
-keep class com.oplus.dmp.sdk.aisearch.** { *; }
#--------------------------------dmp end---------------------------------------

#--------------------------------AI Graffiti---------------------------------------
-keep class com.oplus.effectengine.** {*;}
#--------------------------------AI Graffiti end---------------------------------------
# TBLWebView插件包中的混淆，要放到主app下，否则编译会出错
-keep public class com.oplus.note.plugin.tblwebview.**{*;}
-keep public class kotlin.jvm.internal.Intrinsics{*;}
-keep public class kotlin.Result{*;}
-keep public class kotlin.Result$Companion{*;}
-keep public class kotlin.ResultKt{*;}
-keep public class kotlin.Unit{*;}
# 保留 TableColorPicker 类及其字段（防止字段名被混淆）
-keep class com.oplus.richtext.editor.TableColorToolPanelViewModel$TablePanelColorPicker {
    <fields>;
}

-keep class com.nearme.note.activity.richedit.h5Share.bean.ApiShareResponse{*;}
-keep class com.nearme.note.activity.richedit.h5Share.bean.ApiShareResponse$* {*;}

# coverage
-keep class com.autotest.opasm.**{*;}

#PTC sdk
-keep class com.oplus.interconnect.share.sdk.data.** {*;}
-keep class com.oplus.interconnect.share.sdk.* {*;}
-keep class com.oplus.interconnect.share.* {*;}
-keep class com.oplus.interconnect.service.** {*;}
#//privacyscan
#必须
-keep class org.apache.poi.** {*;}
#//按需
-keep class org.apache.logging.log4j.** {*;}
-keep class org.openxmlformats.schemas.** { *; }
-keep class com.microsoft.schemas.** { *; }
-keep class org.apache.commons.compress.archivers.** {*;}

# sunia
-keep class com.sunia.penengine.sdk.** { *; }