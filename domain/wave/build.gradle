plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file('scripts/common.gradle')

android {
    namespace 'com.oplus.note.wave'

    defaultConfig {
        minSdk 28

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {

    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "com.google.android.material:material:${material}"
    implementation project(path: ':common:logger:logger-api')
    implementation "com.oplus.appcompat:core:${prop_COUISupportVersion}"
    implementation project(path: ':common:lib_base')
    implementation project(path: ':common:baseres')
    implementation "com.oplus.appcompat:recyclerview:16.0.2"
}
