#--------------------------------日志打捞 sdk start----------------------------------------
-keep class com.heytap.log.dto.** { *;}
-dontshrink
#保留注解参数
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-dontwarn  io.protostuff.Tag

# 外部调用到的类
-keep public class com.heytap.log.dto.TraceConfigDto { *; }
-keep public class com.heytap.log.uploader.UploadManager { *; }
-keep public class com.oplus.log.uploader.UploadManager$* { *; }
-keep public class com.heytap.log.Logger { *; }
-keep public class com.oplus.log.Logger$* { *; }
-keep public class com.heytap.log.Settings { *; }
-keep public class com.oplus.log.Settings* { *; }
-keep public class com.heytap.log.ILog { *; }
-keep public class com.heytap.log.consts.LogLevel { *; }
-keep public class com.oplus.log.log.AndroidLog { *; }
-keep public class com.heytap.log.uploader.IHttpDelegate { *; }
-keep public class com.heytap.log.uploader.ResponseWrapper { *; }
-keep public class com.heytap.log.ISimpleLog { *; }
-keep public class com.heytap.log.consts.BusinessType { *; }
-keep public class com.heytap.log.consts.LogLevel { *; }

# native方法
-keep class com.heytap.log.core.CLoganProtocol { *; }

#--------------------------------日志打捞 end----------------------------------------