/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - LogSalvageInitializer.kt
** Description:
** Version: 1.0
** Date : 2023/1/16
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2023/1/16      1.0     create file
****************************************************************/
package com.oplus.note.logsalvage.impl

import android.content.Context
import androidx.startup.Initializer
import com.oplus.note.logger.AppLogger
import com.oplus.note.logsalvage.api.LogSalvageAgentFactory

internal class LogSalvageInitializer : Initializer<Boolean> {

    companion object {
        private const val TAG = "LogSalvageInitializer"
    }

    override fun create(context: Context): Boolean {
        LogSalvageAgentFactory.register(LogSalvageAgentImpl())
        AppLogger.BASIC.d(TAG, "register LogSalvageAgentImpl")
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}