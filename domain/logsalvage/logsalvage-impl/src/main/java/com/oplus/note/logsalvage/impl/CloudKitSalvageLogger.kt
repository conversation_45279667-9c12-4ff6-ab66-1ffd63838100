/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - CloudKitSalvageLogger.kt
** Description: 日志打捞写入类
** Version: 1.0
** Date : 2022/7/18
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2022/7/18      1.0     create file
****************************************************************/
package com.oplus.note.logsalvage.impl

import android.util.Log
import com.heytap.log.HLog
import com.oplus.note.logger.LinkedLogger

class CloudKitSalvageLogger : LinkedLogger() {

    fun setNext(nextLogger: LinkedLogger) {
        mNext = nextLogger
    }

    override fun log(priority: Int, tag: String?, message: String?) {
        when (priority) {
            Log.VERBOSE -> {
                HLog.v(tag, message)
            }
            Log.DEBUG -> {
                HLog.d(tag, message)
            }
            Log.INFO -> {
                HLog.i(tag, message)
            }
            Log.WARN -> {
                HLog.w(tag, message)
            }
            Log.ERROR -> {
                HLog.e(tag, message)
            }
            else -> {
                HLog.d(tag, message)
            }
        }
        mNext?.log(priority, tag, message)
    }
}