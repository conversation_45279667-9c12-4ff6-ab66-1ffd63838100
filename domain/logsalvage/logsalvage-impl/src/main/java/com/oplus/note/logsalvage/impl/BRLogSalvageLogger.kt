/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - BR<PERSON>ogSalvageLogger
 ** Description:
 **         v1.0:  log salvage of backupAndRestore
 **
 ** Version: 1.0
 ** Date: 2024/10/25
 ** Author: Ji<PERSON><PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.Yan              2024/10/25        1.0      Create this module
 ********************************************************************************/
@file:Suppress("WhenExpressionFormattingRule")
package com.oplus.note.logsalvage.impl

import android.util.Log
import com.heytap.log.HLog
import com.oplus.note.logger.LinkedLogger

class BRLogSalvageLogger : LinkedLogger() {

    fun setNext(nextLogger: LinkedLogger) {
        mNext = nextLogger
    }

    override fun log(priority: Int, tag: String?, message: String?) {
        when (priority) {
            Log.VERBOSE -> {
                HLog.v(tag, message)
            }
            Log.DEBUG -> {
                HLog.d(tag, message)
            }
            Log.INFO -> {
                HLog.i(tag, message)
            }
            Log.WARN -> {
                HLog.w(tag, message)
            }
            Log.ERROR -> {
                HLog.e(tag, message)
            }
            else -> {
                HLog.d(tag, message)
            }
        }
        mNext?.log(priority, tag, message)
    }
}