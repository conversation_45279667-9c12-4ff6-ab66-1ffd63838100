/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - LogSalvageAgentImpl.kt
** Description:
** Version: 1.0
** Date : 2023/1/16
** Author: <EMAIL>
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2023/1/16      1.0     create file
****************************************************************/
package com.oplus.note.logsalvage.impl

import android.content.Context
import android.text.TextUtils
import com.heytap.log.HLog
import com.heytap.log.Settings
import com.heytap.log.consts.LogLevel
import com.oplus.note.logger.AppLogger
import com.oplus.note.logsalvage.api.LogSalvageAgent
import com.oplus.note.push.PushAgentFactory
import com.oplus.note.push.PushMessageReceiver
import com.oplus.stdid.sdk.StdIDSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.File


class LogSalvageAgentImpl : LogSalvageAgent {

    companion object {
        private const val TAG = "LogSalvageAgentImpl"
        private const val MBP_BUSINESS_TYPE_NAME = "note_log_push"
        private const val EXPIRE_DAYS = 30
        private const val ACTION_LOG_UPLOAD = "enable_log_upload"
    }

    override fun init(context: Context) {
        val appContext = context.applicationContext
        val filePath = appContext.filesDir.absolutePath + File.separator + "log"

        AppLogger.BASIC.d(TAG, "init $filePath")
        kotlin.runCatching {
            val duid = StdIDSDK.getDUID(context)
            val ouid = StdIDSDK.getOUID(context)
            val settings = Settings.Builder(
                context,
                MBP_BUSINESS_TYPE_NAME,
                "1612",
                "KuczMfxhAoqwmxfyjkE5YmrjXzB5O9um",
                null,
                object : Settings.IOpenIdProvider {
                    //可传一个或多个,传多个时,服务端会依次匹配
                    override fun getGuid(): String? {
                        return null
                    }

                    override fun getOuid(): String? {
                        return ouid
                    }

                    override fun getDuid(): String? {
                        return duid
                    }
                })
                .fileLogLevel(LogLevel.DEBUG.toInt()) //文件日志级别
                .consoleLogLevel(LogLevel.NONE.toInt()) //控制台日志级别, NONE表示不输出
                .fileExpireDays(EXPIRE_DAYS) //文件过期天数, 过期文件会被删除
                .setTracePkg(context.packageName) //自定义包名,不传时会自动获取
                .setProcessName(context.packageName) //设置进程名，建议设置。规避getTask安规问题
                .build(appContext)
            HLog.initHLog(appContext, settings)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "init error ${it.message}")
        }

        insertSalvageLoggerToCLOUDKIT()
        insertSalvageLoggerToUSEROPS()
        insertSalvageLoggerToTHIRDLOG()
        insertSalvageLoggerToBR()
        registerPushMessage()
    }


    /**日志打捞功能强依赖于 Push 功能，需要通过渠道包配置保证 日志打捞和 Push 都集成到 apk 里 */
    private fun registerPushMessage() {
        if (PushAgentFactory.get() == null) {
            AppLogger.BASIC.e(TAG, "PushAgent is null, Log Salvage is invalid, please check!")
        }
        PushAgentFactory.get()?.addPushMessageReceiver(mPushListener)
    }

    /**将日志打印类，注入 AppLogger.CLOUDKIT 中*/
    private fun insertSalvageLoggerToCLOUDKIT() {
        val nextLog = AppLogger.CLOUDKIT.next
        if (nextLog is CloudKitSalvageLogger) {
            return
        }
        val ckSalvageLogger = CloudKitSalvageLogger()
        AppLogger.CLOUDKIT.next = ckSalvageLogger
        ckSalvageLogger.setNext(nextLog)
    }

    /**将日志打印类，注入 AppLogger.USEROPS 中*/
    private fun insertSalvageLoggerToUSEROPS() {
        val nextLog = AppLogger.OPS.next
        if (nextLog is CloudKitSalvageLogger) {
            return
        }
        val ckSalvageLogger = CloudKitSalvageLogger()
        AppLogger.OPS.next = ckSalvageLogger
        ckSalvageLogger.setNext(nextLog)
    }

    /**将日志打印类，注入 AppLogger.THIRDLOG 中*/
    private fun insertSalvageLoggerToTHIRDLOG() {
        val nextLog = AppLogger.THIRDLOG.next
        if (nextLog is ThirdLogSalvageLogger) {
            return
        }
        val ckSalvageLogger = ThirdLogSalvageLogger()
        AppLogger.THIRDLOG.next = ckSalvageLogger
        ckSalvageLogger.setNext(nextLog)
    }

    /**将日志打印类，注入 AppLogger.BR 中*/
    private fun insertSalvageLoggerToBR() {
        val nextLog = AppLogger.BR.next
        if (nextLog is BRLogSalvageLogger) {
            return
        }
        val ckSalvageLogger = BRLogSalvageLogger()
        AppLogger.BR.next = ckSalvageLogger
        ckSalvageLogger.setNext(nextLog)
    }

    /**接收 push 消息的回调*/
    private val mPushListener = object : PushMessageReceiver {
        override fun processMessage(context: Context?, msgContent: String): Boolean {
            if (msgContent.contains("\"action\":\"$ACTION_LOG_UPLOAD")) {
                kotlin.runCatching {
                    val pushMsg = JSONObject(msgContent)
                    val content = pushMsg.optString("content")
                    val tracePkg = JSONObject(content).optString("tracePkg")
                    if (TextUtils.equals(tracePkg, context?.packageName)
                        || tracePkg.contains(context?.packageName!!)
                    ) {
                        uploadLog(content)
                        return true
                    }
                }.onFailure {
                    AppLogger.BASIC.e(TAG, "update log error ${it.message}")
                }
            }
            return false
        }
    }


    private fun uploadLog(pushJsonConfig: String?) {
        GlobalScope.launch(Dispatchers.IO) {

            if (TextUtils.isEmpty(pushJsonConfig)) {
                AppLogger.BASIC.d(TAG, "uploadLog bean or logger is null please check")
                return@launch
            }

            kotlin.runCatching {
                HLog.flush(true)
                HLog.checkOPushDataContent(pushJsonConfig)
                AppLogger.BASIC.d(TAG, "uploadLog")
            }.onFailure {
                AppLogger.BASIC.e(TAG, "uploadLog error ${it.message}")
            }
        }
    }
}