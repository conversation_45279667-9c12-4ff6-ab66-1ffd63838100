plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.logsalvage.impl'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinx_coroutines_version"

    implementation project(":common:logger:logger-api")
    //日志打捞需要依赖 push
    implementation project(":domain:push:push-api")
//    implementation project(":domain:push:push-opush")
    implementation "com.oplus.stdid.sdk:sdk:${stdId}"
    implementation "com.google.code.gson:gson:${gson}"
    implementation 'com.alibaba:fastjson:2.0.28'

    //日志打捞sdk
    implementation "com.heytap:log:${log_salvage}"
    implementation "com.heytap:log-domain-cn:${log_salvage}"
    //海外 com.oplus:log-domain-oversea:${log_salvage}

    implementation project(":domain:logsalvage:logsalvage-api")
    implementation "androidx.startup:startup-runtime:${startup}"
}