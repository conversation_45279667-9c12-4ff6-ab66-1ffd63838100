/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - TimeParserNormal
 ** Description:
 **         v1.0:   Create TimeParserNormal file
 **
 ** Version: 1.0
 ** Date: 2023/04/21
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/4/21   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.semantic.ssa.timexparser

import com.oplus.note.logger.AppLogger
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

class TimeParserNormal : AbstractParser() {
    private val regex =
        Regex("^(([A-Z\\d]{4})-([A-Z\\d]{2})-([A-Z\\d]{2}))?([A-Z]+)(\\d{0,2})(:(\\d{0,2}))?$")

    override fun parser(
        semanticTime: com.oplus.note.semantic.SemanticTime,
        timexString: String
    ): Boolean {
        val matchResult = regex.matchEntire(timexString)
        AppLogger.BASIC.d(TAG, "start parse ,${matchResult?.groupValues}")

        if (matchResult != null) {
            val prefix = matchResult.groupValues[PREFIX_INDEX]
            val hour = matchResult.groupValues[HOUR_FIELD_INDEX].toIntOrNull() ?: 0
            val minute = matchResult.groupValues[MIN_FIELD_INDEX].toIntOrNull() ?: 0
            val relative: String = matchResult.groupValues[RELATIVE_DATE_FIELD_INDEX]
            /**
             * When relative is not null, it means the current time is relative.
             */
            if (relative.isNotEmpty()) {
                runCatching {
                    val calendar = Calendar.getInstance()
                    val date =
                        SimpleDateFormat(RELATIVE_DATE_PATTERN, Locale.getDefault()).parse(relative)
                    date?.let { calendar.time = it }
                    calendar
                }.onSuccess {
                    semanticTime.setDate(it)
                }.onFailure {
                    AppLogger.BASIC.e(TAG, "parse relative error ${it.message}")
                }
            }

            AppLogger.SPEECH.d(
                TAG,
                "prefix：$prefix h:$hour m:$minute ,relative:$relative"
            )
            val predict = TimePredictor.Builder().setOriginTime(prefix, hour, minute)
                    .setSemanticTime(semanticTime).build()
            predict?.let {
                semanticTime.setTime(it.hour, it.minute)
                semanticTime.plusDate(it.extraDay)
                return true
            } ?: return false
        } else {
            AppLogger.SPEECH.d(TAG, "regex parse error")
            return false
        }
    }

    companion object {
        private const val TAG = "TimeParserNormal"
        private const val PREFIX_INDEX = 5

        //如三分钟后提醒我 会返回2023-08-03T15:53,用这个index取出的是2023-08-03,匹配这段不为空则代表是相对时间
        private const val RELATIVE_DATE_FIELD_INDEX = 1
        private const val HOUR_FIELD_INDEX = 6
        private const val MIN_FIELD_INDEX = 8
        private const val RELATIVE_DATE_PATTERN = "yyyy-MM-dd"
    }
}