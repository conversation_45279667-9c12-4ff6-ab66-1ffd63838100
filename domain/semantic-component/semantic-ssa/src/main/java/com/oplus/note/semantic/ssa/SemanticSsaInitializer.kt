/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SemanticSsaInitializer
 ** Description:
 **         v1.0:   Create SemanticSsaInitializer file
 **
 ** Version: 1.0
 ** Date: 2023/03/09
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/3/9   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.semantic.ssa

import android.content.Context
import androidx.startup.Initializer
import com.nearme.note.util.AppLaunchThreadManger
import com.oplus.note.logger.AppLogger
import com.oplus.note.semantic.api.SemanticFactory

class SemanticSsaInitializer : Initializer<Boolean> {

    companion object {
        const val TAG = "SemanticSsaInitializer"
    }

    override fun create(context: Context): Boolean {
        AppLogger.BASIC.d(TAG, "register SemanticSsa")
        AppLaunchThreadManger.execute {
            val tool = SemanticTool()
            SemanticFactory.register(tool)
            SemanticFactory.get()?.install(context.applicationContext)
        }
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        // No dependencies on other libraries.
        return mutableListOf()
    }
}