/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - DatePredictor
 ** Description:
 **         v1.0:   parse date
 **
 ** Version: 1.0
 ** Date: 2023/04/21
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/4/21   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.semantic.ssa.timexparser

import com.oplus.note.logger.AppLogger
import com.oplus.note.semantic.SemanticTime

class DateParserNormal : AbstractParser() {
    private val regex = "^([A-Z\\d]{4})-([A-Z\\d]{2})-([A-Z\\d]{2})\$".toRegex()

    override fun parser(semanticTime: SemanticTime, timexString: String): Boolean {

        val matchResult = regex.find(timexString)

        return if (matchResult != null && matchResult.groupValues.size >= DATE_MATCH_COUNT) {
            val year = matchResult.groupValues[1]
            val month = matchResult.groupValues[2]
            val day = matchResult.groupValues[DAY_INDEX]
            AppLogger.SPEECH.d(TAG, "origin date :year:$year month:$month day:$day")
            val predictor = DatePredictor.Builder().setOriginDate(year, month, day).build()
            val predictDate = predictor?.toDate()
            AppLogger.SPEECH.d(TAG, "predict date:$predictDate")
            if (predictDate == null) {
                false
            } else {
                semanticTime.setDate(predictDate)
                semanticTime.setOriginDateUncertain(predictor.isUncertainDate)
                true
            }
        } else {
            AppLogger.SPEECH.e(TAG, "regex parse error")
            false
        }
    }

    companion object {
        const val TAG = "DateParserNormal"
        const val DATE_MATCH_COUNT = 4
        const val DAY_INDEX = 3
    }
}