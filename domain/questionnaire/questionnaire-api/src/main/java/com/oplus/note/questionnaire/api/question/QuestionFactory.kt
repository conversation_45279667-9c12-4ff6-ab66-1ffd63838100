/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  NieXiaokang       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.questionnaire.api.question

object QuestionFactory {
    private var mProvider: QuestionProvider? = null

    @JvmStatic
    fun registerProvider(provider: QuestionProvider) {
        mProvider = provider
    }

    @JvmStatic
    fun getProvider(): QuestionProvider? {
        return mProvider
    }
}