/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: Nie<PERSON><EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  NieXiaokang       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.questionnaire.api.feedback

import android.app.Activity
import android.content.Context

interface FeedbackUserHelper {
    fun openFeedback(context: Activity, appVersion: String, guid: String, isDeclareAgree: () -> Boolean)
    fun setRequestMadeListener(callback: (content: String, typeState: String) ->  Unit)
    fun fbLogV(context: Context?, msg: String)
    fun fbLogD(context: Context?, msg: String)
    fun fbLogE(context: Context?, msg: String)
    fun fbLogI(context: Context?, msg: String)
    fun fbLogW(context: Context?, msg: String)
    companion object {
        const val STATE_TYPE_GUID: String = "guId"
        const val STATE_TYPE_BRAND: String = "brand"
        const val STATE_TYPE_MODEL: String = "model"
        const val STATE_TYPE_OS: String = "os"
        const val STATE_TYPE_LOG: String = "log"
        const val STATE_TYPE_FEEDBACK: String = "feedback"
        const val STATE_TYPE_CONTACT: String = "contact"
        const val STATE_TYPE_STATISTICS: String = "statistics"
        const val STATE_TYPE_AI_REWRITE: String = "ai_rewrite"
        const val STATE_TYPE_AI_GRAPHICS: String = "ai_graphics"
    }
}