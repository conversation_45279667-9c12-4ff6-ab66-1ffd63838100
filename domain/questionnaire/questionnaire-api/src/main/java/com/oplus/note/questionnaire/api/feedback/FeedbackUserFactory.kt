/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  NieXiaokang       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.questionnaire.api.feedback

object FeedbackUserFactory {
    private var mHelper: FeedbackUserHelper? = null

    @JvmStatic
    fun registerHelper(helper: FeedbackUserHelper) {
        mHelper = helper
    }

    @JvmStatic
    fun getHelper(): FeedbackUserHelper? {
        return mHelper
    }

    /**
     * Currently only OnePlus oppo realme Export version not support
     */
    @JvmStatic
    val isSupportFeedback: Boolean
        get() = getHelper() != null
}