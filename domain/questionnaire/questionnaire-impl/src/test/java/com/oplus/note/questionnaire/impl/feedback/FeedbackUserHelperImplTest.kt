/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/5/18
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  NieXiaokang       2023/5/18      1.0     create file
 ****************************************************************/
package com.oplus.note.questionnaire.impl.feedback

import android.app.Activity
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [30])
class FeedbackUserHelperImplTest {

    @Test
    fun should_no_exception_when_use_open_feedback() {
        val context = Robolectric.buildActivity(Activity::class.java).create().get()
        val helper = FeedbackUserHelperImpl()
        val version = "1.0.0"
        val guid = "1"
        helper.openFeedback(context, version, guid) { true }
        helper.openFeedback(context, version, guid) { false }
    }
}