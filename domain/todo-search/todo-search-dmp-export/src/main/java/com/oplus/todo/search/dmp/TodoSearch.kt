/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/25
 ** Author: niexiaokang
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ****************************************************************/
package com.oplus.todo.search.dmp

import android.content.Context
import com.oplus.dmp.sdk.InitConfig
import com.oplus.dmp.sdk.SearchManager
import com.oplus.dmp.sdk.analyzer.tokenizer.Normalizer.TO_LOWER
import com.oplus.dmp.sdk.analyzer.tokenizer.Segmenter
import com.oplus.dmp.sdk.index.Document
import com.oplus.dmp.sdk.index.IndexError
import com.oplus.dmp.sdk.index.config.DataType.DATA_TYPE_INT
import com.oplus.dmp.sdk.index.config.DataType.DATA_TYPE_LONG
import com.oplus.dmp.sdk.index.config.DataType.DATA_TYPE_STRING
import com.oplus.dmp.sdk.index.config.FieldConfig
import com.oplus.dmp.sdk.index.config.IndexConfig
import com.oplus.dmp.sdk.search.engine.SearchProxyV4
import com.oplus.dmp.sdk.version.VersionProtocol
import com.oplus.note.logger.AppLogger
import com.oplus.note.repo.todo.TodoRepoFactory
import com.oplus.note.repo.todo.entity.ToDo
import com.oplus.note.utils.SharedPreferencesUtil
import com.oplus.todo.search.ITodoSearch
import com.oplus.todo.search.TodoSearchBuryPointUtil

class TodoSearch(private val context: Context) : ITodoSearch {
    companion object {
        private const val TAG = "TodoSearch-dmp"
        private const val DMP_SP_NAME = "dmp_sp_name"
        private const val SP_KEY_CHECK_DMP_DATA_TIME = "sp_key_check_todo_dmp_data_time"
        private const val TIME_ONE_DAY = 24 * 60 * 60 * 1000
        private const val TODO = "todo"
        private const val INDEX_CONFIG_TODO_VERSION = 2
        private const val LOCAL_ID = "local_id"
        private const val CONTENT = "content"
        private const val CREATE_TIME = "create_time"
        private const val UPDATE_TIME = "update_time"
        private const val FINISH_TIME = "finish_time"
        //待办提醒时间
        private const val ALARM_TIME = "alarm_time"
        private const val ALARM_NEXT_TIME = "alarm_next_time"
        private const val REPEAT_RULE = "repeat_rule"
        //是否强制闹钟提醒（1：是，0：否)
        private const val FORCE_REMINDER = "force_reminder"
    }

    private val indexProxyForTodo by lazy { SearchManager.getInstance().getClient(TODO)?.indexProxy }
    private val searchProxyForTodo by lazy {
        SearchManager.getInstance().getClient(TODO)?.searchProxy as? SearchProxyV4
    }
    private val todoRepo by lazy { TodoRepoFactory.get() }
    private var initPair: Pair<Boolean, Boolean>? = null

    override suspend fun init() {
        kotlin.runCatching {
            val result = SearchManager.getInstance()
                .initSync(context, InitConfig(intArrayOf(VersionProtocol.API_VERSION_4)))
            AppLogger.BASIC.d(TAG, "init dmp result:$result")
            initPair = Pair(true, result)
        }.onFailure {
            AppLogger.BASIC.d(TAG, "init dmp err:$it")
            initPair = Pair(true, false)
        }
    }

    override suspend fun initIndexConfig(force: Boolean) {
        val isUpdateConfigForTodo = initIndexConfigForTodo(force)
        if (isUpdateConfigForTodo) {
            notifyDataChange(true, true)
        } else {
            notifyDataChange(false)
        }
    }

    private fun initIndexConfigForTodo(force: Boolean): Boolean {
        val indexProxy = indexProxyForTodo ?: return false
        val dmpVersion = indexProxy.config?.version ?: -1
        val isNeedClearConfig = force || indexProxy.isRebuildRequired || dmpVersion != INDEX_CONFIG_TODO_VERSION
        AppLogger.BASIC.d(TAG, "initIndexConfigFortTodo dmp $dmpVersion,local $INDEX_CONFIG_TODO_VERSION")
        AppLogger.BASIC.d(TAG, "force $force,isRebuildRequired ${indexProxy.isRebuildRequired}")
        var isUpdateConfig = false
        if (isNeedClearConfig) {
            val tokens = listOf(TO_LOWER, Segmenter.EN_ORIGIN_WORD)
            val indexConfig = IndexConfig.Builder().setVersion(INDEX_CONFIG_TODO_VERSION).apply {
                addField(FieldConfig.Builder().setName(LOCAL_ID).setType(DATA_TYPE_STRING).setStored().build())
                addField(
                    FieldConfig.Builder().setName(CONTENT).setType(DATA_TYPE_STRING).setStored().setSearched(tokens)
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(CREATE_TIME).setType(DATA_TYPE_LONG).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(UPDATE_TIME).setType(DATA_TYPE_LONG).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(FINISH_TIME).setType(DATA_TYPE_LONG).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(ALARM_TIME).setType(DATA_TYPE_LONG).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(ALARM_NEXT_TIME).setType(DATA_TYPE_LONG).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(REPEAT_RULE).setType(DATA_TYPE_STRING).setStored().setStoreDocValue()
                        .build()
                )
                addField(
                    FieldConfig.Builder().setName(FORCE_REMINDER).setType(DATA_TYPE_INT).setStored().setStoreDocValue()
                        .build()
                )
            }.build()
            indexProxy.clear()
            indexProxy.config = indexConfig
            isUpdateConfig = true
        }
        AppLogger.BASIC.d(TAG, "initIndexConfigFortTodo end:$isUpdateConfig")
        return isUpdateConfig
    }

    override fun deleteDmpDataByIds(ids: Set<String>, isAll: Boolean) {
        val remotes = searchProxyForTodo?.queryDocuments()?.toList() ?: emptyList()
        val removes = mutableListOf<Any>()
        if (isAll) {
            removes.addAll(remotes.map { it.identification })
        } else {
            remotes.forEach { remote ->
                if (ids.contains(remote.identification)) {
                    removes.add(remote.identification)
                }
            }
        }
        AppLogger.BASIC.d(TAG, "deleteDmpDataByIds, deleteDocuments:${removes.size}")
        if (removes.isNotEmpty()) {
            val deleteDocuments = indexProxyForTodo?.deleteDocuments(removes)
            checkUploadError(deleteDocuments?.errors, "delete", removes.size)
        }
    }

    override suspend fun notifyDataChange(force: Boolean, isFullQuery: Boolean) {
        val isRefreshData = if (force) {
            true
        } else {
            //控制初始化后，是否需要强制数据更新
            val isUpdateTime = System.currentTimeMillis() - getCheckTime() >= TIME_ONE_DAY
            if (isUpdateTime) {
                setCheckTime()
            }
            isUpdateTime
        }
        if (isRefreshData) {
            val list = if (isFullQuery) {
                // 初始化、手机搬家和云同步过来的数据或其他未知的场景，更新时间可能不在一分钟之内，这个时候，还是和之前逻辑一致，查询全量数据
                todoRepo?.getAllTodoList()
            } else {
                // 一般用户操作，数据更新，则查询一分钟内数据
                val currentTime = System.currentTimeMillis()
                todoRepo?.getAllTodoListInLastMinute(currentTime)
            } ?: return
            AppLogger.BASIC.d(TAG, "notifyDataChange, isFullQuery=$isFullQuery, list.size=${list.size}")
            refreshDataForTodo(list, isFullQuery)
        }
    }

    override fun isDmpAvailable(): Boolean {
        kotlin.runCatching {
            val initSuccess = initPair?.second == true
            if (!initSuccess) {
                AppLogger.BASIC.d(TAG, "initSuccess = false")
                return false
            }
            if (!SearchManager.getInstance().isServiceAvailable) {
                AppLogger.BASIC.d(TAG, "isServiceAvailable = false")
                return false
            }
            if (!SearchManager.getInstance().isRemoteServiceAvailable) {
                AppLogger.BASIC.d(TAG, "isRemoteServiceAvailable = false")
                return false
            }
            if (!SearchManager.getInstance().isAnalyzerAvailable) {
                AppLogger.BASIC.d(TAG, "isAnalyzerAvailable = false")
                return false
            }
            if (indexProxyForTodo == null) {
                AppLogger.BASIC.d(TAG, "indexProxyForTodo == null")
                return false
            }
        }.onFailure {
            AppLogger.BASIC.d(TAG, "isDmpAvailable error: $it")
            initPair?.run {
                AppLogger.BASIC.d(TAG, "dmp init:$first,init result:$second")
            } ?: AppLogger.BASIC.d(TAG, "dmp not init")
            return false
        }
        return true
    }

    private fun refreshDataForTodo(locals: List<ToDo>, isFullQuery: Boolean) {
        val remotes = searchProxyForTodo?.queryDocuments()?.toList() ?: emptyList()
        val adds = locals.filter { local ->
            remotes.none { it.identification == local.getId() }
        }
        //delete需要通过identification
        val removes = mutableListOf<Any>()
        // 全量数据仍走此逻辑，一分钟内数据不走此，走dmp单独删除
        if (isFullQuery) {
            remotes.forEach { remote ->
                if (locals.none { it.getId() == remote.identification }) {
                    removes.add(remote.identification)
                }
            }
        }
        val updates = locals.filter { local ->
            val key = createKeyForTodo(local)
            remotes.any { it.identification == local.getId() && it.checksum != key }
        }
        val totalCount = adds.size + removes.size + updates.size
        if (removes.isNotEmpty()) {
            val deleteDocuments = indexProxyForTodo?.deleteDocuments(removes)
            checkUploadError(deleteDocuments?.errors, "delete", totalCount)
        }
        if (adds.isNotEmpty()) {
            // dmp的update方法会add并且去重的功能
            val documentIndexError = indexProxyForTodo?.updateDocuments(adds.map { createTodoDocument(it) })
            checkUploadError(documentIndexError, "add", totalCount)
        }
        if (updates.isNotEmpty()) {
            val updateDocuments = indexProxyForTodo?.updateDocuments(updates.map { createTodoDocument(it) })
            checkUploadError(updateDocuments, "update", totalCount)
        }
        AppLogger.BASIC.d(
            TAG,
            "refreshDataForTodo:[${remotes.size},${locals.size},${removes.size},${adds.size},${updates.size}]"
        )
        //日志打捞
        AppLogger.THIRDLOG.d(TAG, "52010203,total add:${adds.size},update:${updates.size},remove:${removes.size}")
    }

    private fun checkUploadError(error: IndexError<Document<String>>?, type: String, totalCount: Int) {
        var count = 0
        error?.errors?.forEach {
            val errorCode = it.errorCode
            val errorMessage = it.errorMessage
            it.errorData?.forEach { data ->
                AppLogger.THIRDLOG.d(TAG, "52010203,error $type:code[$errorCode],message[$errorMessage],id[${data.identification ?: ""}]")
                count++
            }
        }
        if (count > 0 && (totalCount > 0)) {
            TodoSearchBuryPointUtil.setDmpSearchFailedPoint(context, count, count * 1F / totalCount)
        }
    }

    private fun checkUploadError(errorList: MutableList<IndexError.Error<Any>>?, type: String, totalCount: Int) {
        var count = 0
        errorList?.forEach {
            val errorCode = it.errorCode
            val errorMessage = it.errorMessage
            it.errorData?.forEach { data ->
                AppLogger.THIRDLOG.d(TAG, "52010203,error $type:code[$errorCode],message[$errorMessage],id[${data ?: ""}]")
                count++
            }
        }
        if (count > 0 && (totalCount > 0)) {
            TodoSearchBuryPointUtil.setDmpSearchFailedPoint(context, count, count * 1F / totalCount)
        }
    }

    private fun createKeyForTodo(todo: ToDo): Long {
        val content = todo.content
        val updateTime = todo.updateTime
        val finishTime = todo.finishTime
        val nextTime = todo.nextAlarmTime
        val isDelete = todo.isDelete
        return "$content+$updateTime+$finishTime+$isDelete+$nextTime".hashCode().toLong()
    }

    private fun createTodoDocument(todo: ToDo): Document<String> {
        return Document(todo.getId(), createKeyForTodo(todo)).apply {
            add(LOCAL_ID, todo.getId())
            add(CONTENT, todo.content)
            add(CREATE_TIME, todo.createTime?.time ?: 0)
            add(UPDATE_TIME, todo.updateTime?.time ?: 0)
            add(FINISH_TIME, todo.finishTime?.time ?: 0)
            add(ALARM_TIME, todo.alarmTime?.time ?: 0)
            add(ALARM_NEXT_TIME, todo.nextAlarmTime?.time ?: 0)
            add(REPEAT_RULE, todo.extra?.repeatRule ?: "")
            add(FORCE_REMINDER, if (todo.forceReminder) 1 else 0)
        }
    }

    private fun ToDo.getId(): String {
        return localId.toString()
    }

    @Synchronized
    private fun setCheckTime() {
        SharedPreferencesUtil.getInstance()
            .putLong(context, DMP_SP_NAME, SP_KEY_CHECK_DMP_DATA_TIME, System.currentTimeMillis())
    }

    private fun getCheckTime(): Long {
        return SharedPreferencesUtil.getInstance().getLong(context, DMP_SP_NAME, SP_KEY_CHECK_DMP_DATA_TIME, -1L)
    }
}