/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/25
 ** Author: niexiaokang
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ****************************************************************/
package com.oplus.todo.search.dmp

import android.content.Context
import androidx.startup.Initializer
import com.nearme.note.util.AppLaunchThreadManger
import com.nearme.note.util.ConfigUtils
import com.oplus.todo.search.TodoSearchManager

class TodoSearchInitializer : Initializer<Boolean> {

    override fun create(context: Context): Bo<PERSON>an {
        if (ConfigUtils.isToDoDeprecated) {
            return false
        }
        AppLaunchThreadManger.execute {
            TodoSearchManager.addTodoSearch(TodoSearchManager.TODO_SEARCH_TYPE_DMP, TodoSearch(context.applicationContext))
        }
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}