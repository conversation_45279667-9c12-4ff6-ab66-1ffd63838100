plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-android'
    id 'kotlin-parcelize'
}
apply from: rootProject.file('scripts/common.gradle')

android {
    namespace 'com.oplus.note.aigc'

    defaultConfig {
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${appcompat}"
    implementation "com.google.android.material:material:${material}"
    implementation project(":common:logger:logger-api")
    implementation project(":common:lib_base")
    implementation project(":common:lib_api")
    implementation project(':data:note-repository')
//    implementation ("com.oplus.appcompat:base:${prop_COUISupportVersion}")
    implementation "com.oplus.aiunit.open:toolkits:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:download:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:note-rewrite:$ai_rewrite_sdk_version"
    implementation "com.oplus.aiunit.open:graphic-abstract:$ai_graphic_sdk_version"
    implementation "com.oplus.aiunit.open:core:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:common:$ai_sdk_before_version"
    implementation "com.oplus.aiunit.open:vision:$ai_sdk_before_version"
    implementation "com.oplus.aiunit.open:aisubsystem:$ai_sub_sdk_version"
    implementation "com.google.code.gson:gson:$gson"
    implementation "com.oplus.statistics:track:$track"
    implementation project(':common:logger:logger-dcs')
}