plugins {
    id 'com.android.library'
    id 'kotlin-android'
}
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.compat'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation project(':common:lib_api')
    implementation project(":common:logger:logger-api")
    implementation project(":common:lib_base")
    implementation project(':library-sync:cloud-sync-support')
    implementation project(path: ':common:osdk-proxy')

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.core:core-ktx:${core_ktx}"

    implementation 'com.heytap.accountsdk:UCAccountSDK_Base_heytap:*******'
}