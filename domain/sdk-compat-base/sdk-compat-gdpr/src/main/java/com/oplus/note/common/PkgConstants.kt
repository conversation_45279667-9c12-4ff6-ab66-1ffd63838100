package com.oplus.note.common

/**
 * <AUTHOR>
 * demandId : ALM-1749654
 * description : 收集引用包名或者action相关的字段
 * updateTime : 2021/7/6
 */
open class PkgConstants {
    companion object {
        const val CLOUD_ACTION_MODULE_SWITCH_STATE_CHANGED = ""

        const val SKIN_AND_PARA_STYLE_DOWN_URL = "https://note-skin-fr.allawnos.com"

        const val ACCOUNT_TYPE = "oneplus"
        const val ACCOUNT_NAME = "oneplus"
        // 日历下载链接
        const val CALENDAR_DOWNLOAD_URL = "http://columbus-file-repo.ocs-cn-south.oppoer.me/baymax-go-static-front" +
                "/test/calendar-todo-h5-pages/calendar-todo-h5-pages/release/dist/index.html"
        // 日历外销
        const val PACKAGE_CALENDAR = "com.oplus.calendar"
    }
}