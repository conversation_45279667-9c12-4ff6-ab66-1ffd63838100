plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.plugin.local'
}

dependencies {
    implementation project(':domain:plugin:plugin-downloader-api')
    implementation project(":common:logger:logger-api")
    // 插件化环境
    implementation "androidx.startup:startup-runtime:$startup"

}
