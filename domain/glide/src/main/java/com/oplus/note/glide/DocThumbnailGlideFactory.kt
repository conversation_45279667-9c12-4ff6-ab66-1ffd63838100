/**
 * ******************************************************************************
 * * Copyright (C), 2008-2025, Oplus, All rights reserved. *
 * * File: - DocumentThumbnailFactory
 * * Description:
 * * v1.0: Create DocumentThumbnailFactory file *
 * * Version: 1.0
 * * Date: 2025/02/20
 * * Author: Ji<PERSON><PERSON>.Yan *
 * * ------------------------------- Revision History:
 * ----------------------------
 * * <author> <date> <version> <desc>
 * * ------------------------------------------------------------------------------
 * * Ji<PERSON><PERSON>.Yan 2025/2/20 1.0 Create this module
 *   ******************************************************************************
 */
package com.oplus.note.glide

import  android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.oplus.note.logger.AppLogger

@Keep
class DocThumbnailGlideFactory(context: Context) : ModelLoaderFactory<DocThumbEntity, Bitmap> {
    private val appContext = context.applicationContext

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<DocThumbEntity, Bitmap> {
        AppLogger.BASIC.d("DocThumbnailGlideFactory", "build")
        return DocThumbnailModelLoader(appContext)
    }

    override fun teardown() {
    }
}