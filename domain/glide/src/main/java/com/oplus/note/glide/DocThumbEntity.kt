/**
 * ******************************************************************************
 * * Copyright (C), 2008-2025, Oplus, All rights reserved. *
 * * File: - DocEntity
 * * Description:
 * * v1.0: Create DocEntity file *
 * * Version: 1.0
 * * Date: 2025/02/20
 * * Author: <PERSON><PERSON><PERSON>.Yan *
 * * ------------------------------- Revision History:
 *   ----------------------------
 * * <author> <date> <version> <desc>
 * * ------------------------------------------------------------------------------
 * * <PERSON><PERSON><PERSON>.Yan 2025/2/20 1.0 Create this module
 *   ******************************************************************************
 */
package com.oplus.note.glide

import android.net.Uri
import androidx.annotation.Keep

@Keep
data class DocThumbEntity(
    val attachId: String,
    val uri: Uri,
    val mimeType: String,
    val fileName: String,
    val fileSize: Long
) {

    override fun equals(other: Any?): Boolean {
        if (other is DocThumbEntity) {
            return (attachId == other.attachId) &&
                    (uri == other.uri) &&
                    (mimeType == other.mimeType) &&
                    (fileName == other.fileName) &&
                    (fileSize == other.fileSize)
        }
        return false
    }

    @Suppress("MagicNumber")
    override fun hashCode(): Int {
        var result = fileSize.hashCode()
        result = 31 * result + attachId.hashCode()
        result = 31 * result + uri.hashCode()
        result = 31 * result + mimeType.hashCode()
        result = 31 * result + fileName.hashCode()
        return result
    }
}

