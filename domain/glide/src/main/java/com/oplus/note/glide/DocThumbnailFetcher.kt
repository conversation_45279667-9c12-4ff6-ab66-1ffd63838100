/**
 * ******************************************************************************
 * * Copyright (C), 2008-2025, Oplus, All rights reserved. *
 * * File: - DocThumbnailFetcher
 * * Description:
 * * v1.0: Create DocThumbnailFetcher file *
 * * Version: 1.0
 * * Date: 2025/02/20
 * * Author: Jiep<PERSON>.Yan *
 * * ------------------------------- Revision History:
 * ----------------------------
 * * <author> <date> <version> <desc>
 * * ------------------------------------------------------------------------------
 * * Ji<PERSON><PERSON>.Yan 2025/2/20 1.0 Create this module
 *   ******************************************************************************
 */
package com.oplus.note.glide

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.oplus.note.doc.thumbnail.api.DocThumbnailFactory
import com.oplus.note.doc.thumbnail.api.IDocThumbResultCallback
import com.oplus.note.doc.thumbnail.api.IDocThumbnailAgent
import com.oplus.note.external.MediaFileInfo
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil

internal class DocThumbnailFetcher(
    private val context: Context,
    private val thumbModel: DocThumbEntity,
    private val width: Int,
    private val height: Int
) : DataFetcher<Bitmap> {

    private val loaderAgent by lazy {
        DocThumbnailFactory.get()?.apply {
            initialize()
        }
    }

    @Volatile
    private var isCancelled = false

    private var firstTimeLoad: Boolean = true


    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in Bitmap>) {
        val loader = loaderAgent ?: run {
            callback.onLoadFailed(UnsupportedOperationException("loader is null"))
            return
        }
        realLoadData(loader, callback)
    }

    override fun cleanup() {
    }

    override fun cancel() {
        AppLogger.BASIC.d(TAG, "cancel")
        isCancelled = true
    }

    override fun getDataClass(): Class<Bitmap> {
        return Bitmap::class.java
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }

    private fun realLoadData(loader: IDocThumbnailAgent, callback: DataFetcher.DataCallback<in Bitmap>) {
        AppLogger.BASIC.d(TAG, "realLoadData: $thumbModel")
        if (isCancelled) {
            AppLogger.BASIC.d(TAG, "loadData: isCancelled: ${thumbModel.uri}")
            //return null when request has been cancelled
            callback.onDataReady(null)
            return
        }
        //非首次获取，则直接从缓存中获取
        if (!onShortcutCheck(thumbModel, callback)) {
            callback.onDataReady(null)
            return
        }

        val fileInfo = MediaFileInfo(thumbModel.uri, thumbModel.mimeType, thumbModel.fileName, thumbModel.fileSize)

        loader.getDocThumbnailAsync(context,
            fileInfo,
            width,
            height,
            false,
            object : IDocThumbResultCallback {
                override fun onDataReady(bitmap: Bitmap) {
                    if (isCancelled) {
                        AppLogger.BASIC.d(TAG, "realLoadData: onDataReady: cancelled")
                        callback.onDataReady(null)
                        return
                    }

                    callback.onDataReady(bitmap)
                }

                override fun onLoadFailed(throwable: Throwable) {
                    if (isCancelled) {
                        AppLogger.BASIC.d(TAG, "realLoadData: onLoadFailed: cancelled")
                        callback.onDataReady(null)
                        return
                    }

                    AppLogger.BASIC.e(TAG, "onLoadFailed: ${throwable.message}")
                    callback.onLoadFailed(Exception(throwable))
                }
            })
    }

    private fun onShortcutCheck(thumbModel: DocThumbEntity, callback: DataFetcher.DataCallback<in Bitmap>): Boolean {
        val onlyRetrieveFromCache = SharedPreferencesUtil.getInstance().getBoolean(
            context,
            ONLY_RETRIEVE_FROM_CACHE,
            thumbModel.hashCode().toString(),
            false
        )
        if (firstTimeLoad.not() || onlyRetrieveFromCache) {
            callback.onDataReady(null)
            return false
        }
        firstTimeLoad = false
        SharedPreferencesUtil.getInstance().putBoolean(
            context,
            ONLY_RETRIEVE_FROM_CACHE,
            thumbModel.hashCode().toString(),
            true
        )
        return true
    }
    companion object {
        private const val TAG = "DocThumbnailFetcher"
        private const val ONLY_RETRIEVE_FROM_CACHE = "OnlyRetrieveFromCache"
    }
}