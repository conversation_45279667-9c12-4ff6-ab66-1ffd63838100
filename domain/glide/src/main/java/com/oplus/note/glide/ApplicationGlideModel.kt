/*********************************************************************************
 ** Copyright (C), 2008-2025, Oplus, All rights reserved.
 **
 ** File: - ${NAME}
 ** Description:
 **         v1.0:    ApplicationGlideModel
 **
 ** Version: 1.0
 ** Date: 2025/2/25
 ** Author: <PERSON><PERSON><PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.Yan              2025/2/25        2.0      convert to kotlin
 ********************************************************************************/
package com.oplus.note.glide

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.bumptech.glide.Glide
import com.bumptech.glide.GlideBuilder
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.load.DecodeFormat
import com.bumptech.glide.load.engine.cache.LruResourceCache
import com.bumptech.glide.module.AppGlideModule
import com.bumptech.glide.request.RequestOptions
import com.oplus.note.logger.AppLogger

@GlideModule
class ApplicationGlideModel : AppGlideModule() {
    override fun applyOptions(context: Context, builder: GlideBuilder) {
        builder.setConnectivityMonitorFactory(NoConnectivityMonitorFactory())
        builder.setMemoryCache(LruResourceCache(DEFAULT_MEMORY_CACHE_SIZE.toLong()))
        builder.setDefaultRequestOptions(
            RequestOptions().format(DecodeFormat.PREFER_ARGB_8888)
        )
        if (BuildConfig.DEBUG) {
            builder.setLogLevel(Log.VERBOSE)
        }
    }

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        super.registerComponents(context, glide, registry)
        AppLogger.BASIC.d(TAG, "registerComponents $glide")
        registry.append(DocThumbEntity::class.java, Bitmap::class.java, DocThumbnailGlideFactory(context))

        //bitmap data encoder
        registry.append(Bitmap::class.java, SourceBitmapEncoder())
    }

    override fun isManifestParsingEnabled(): Boolean {
        return false
    }

    companion object {
        private const val DEFAULT_MEMORY_CACHE_SIZE = 30 * 1024 * 1024
        private const val TAG = "ApplicationGlideModel"
    }
}
