/**
 * ******************************************************************************
 * * Copyright (C), 2008-2025, Oplus, All rights reserved. *
 * * File: - DocumentThumbnailModelLoader
 * * Description:
 * * v1.0: Create DocumentThumbnailModelLoader file *
 * * Version: 1.0
 * * Date: 2025/02/20
 * * Author: Ji<PERSON><PERSON>.Yan *
 * * ------------------------------- Revision History:
 * ----------------------------
 * * <author> <date> <version> <desc>
 * * ------------------------------------------------------------------------------
 * * Ji<PERSON><PERSON>.Yan 2025/2/20 1.0 Create this module
 *   ******************************************************************************
 */
package com.oplus.note.glide

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.signature.ObjectKey

class DocThumbnailModelLoader(private val context: Context) : ModelLoader<DocThumbEntity, Bitmap> {

    override fun buildLoadData(
        model: DocThumbEntity,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Bitmap> = ModelLoader.LoadData(
        ObjectKey(model),
        DocThumbnailFetcher(context, model, width, height)
    )

    override fun handles(model: DocThumbEntity): Boolean = true
}