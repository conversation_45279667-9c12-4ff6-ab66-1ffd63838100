package com.oplus.note.glide;

import android.content.Context;

import androidx.annotation.NonNull;

import com.bumptech.glide.manager.ConnectivityMonitor;
import com.bumptech.glide.manager.ConnectivityMonitorFactory;

/**
 * 不做网络监听
 */
public class NoConnectivityMonitorFactory implements ConnectivityMonitorFactory {
    @NonNull
    @Override
    public ConnectivityMonitor build(@NonNull Context context, @NonNull ConnectivityMonitor.ConnectivityListener listener) {
        return new ConnectivityMonitor() {
            @Override
            public void onStart() {
                //不做处理
            }

            @Override
            public void onStop() {
                //不做处理
            }

            @Override
            public void onDestroy() {
                //不做处理
            }
        };
    }
}
