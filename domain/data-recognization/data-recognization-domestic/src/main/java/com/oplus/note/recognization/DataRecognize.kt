/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - IAiKitState.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/20
 ** Author: W9039628
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ** ------------------------------------------------------------
 **  W9039628      2025/3/20      1.0     create Notes
 ****************************************************************/
package com.oplus.note.recognization

import android.content.Context
import com.oplus.note.api.IDataRecognized
import com.oplus.omes.pcc.ssdk.core.IRecognizedManager
import com.oplus.omes.pcc.ssdk.core.RecognizedBuilder
import com.oplus.omes.pcc.ssdk.entity.MatchedText

class DataRecognize : IDataRecognized {
    companion object {
        const val TAG = "DataRecognize"
    }

    override fun recognize(context: Context, text: String, isMarkDown: Boolean): String {
        val manager: IRecognizedManager = RecognizedBuilder.build(context)
        val senText: List<MatchedText> = manager.recognize(text) ?: return text

        // 按起始位置倒序处理，避免替换影响后续索引
        val sortedMatches = senText.sortedByDescending { it.start }
        val processedText = StringBuilder(text)

        sortedMatches.forEach { match ->
            val start = match.start.coerceIn(0, text.length)  // 确保start在最大最小值之间
            val end = match.end.coerceIn(start, text.length)  // 确保end >= start

            val replacement = if (isMarkDown) "/*" else "*"
            processedText.replace(start, end, replacement)
        }

        return processedText.toString()
    }
}