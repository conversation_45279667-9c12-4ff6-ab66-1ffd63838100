/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - IAiKitState.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/3/20
 ** Author: W9039628
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 ** ------------------------------------------------------------
 **  W9039628      2025/3/20      1.0     create Notes
 ****************************************************************/
package com.oplus.note.recognization

import android.content.Context
import androidx.startup.Initializer
import com.oplus.note.api.RecognizedManager

class DataRecognizeInitializer : Initializer<Boolean> {
    override fun create(context: Context): Boolean {
        RecognizedManager.registerRecognizer(DataRecognize())
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}