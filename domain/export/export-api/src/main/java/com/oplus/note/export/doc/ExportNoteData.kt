/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportNoteData.kt
 ** Description:
 **      note data class for export doc
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.text.Editable

data class ExportNoteData(
    val title: Editable?,
    val localId: String,
    val updateTime: Long,
    val data: List<ExportDocType>? = null,
    val wvExtraData: WVExtraNoteData? = null
)

data class WVExtraNoteData(
    val rawTitle: String? = null,
    val rawText: String? = null,
    val plainText: String? = null,
    val pageResultUrl: List<String>? = null,
    val attachmentRootPath: String,
    val captureData: Map<String, String>, // webview上需要截取节点图片的信息
    val combinedCardData: Map<String, String>, // 卡片信息
)
