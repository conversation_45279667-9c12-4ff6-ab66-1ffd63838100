/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ExportPdfAgent.kt
 * Description    : ExportPdfAgent.kt
 * Version        : 1.0
 * Date           : 2025/7/28
 * Author         : 80262777
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * 80262777     2025/7/28         1.0           create
 */
package com.oplus.note.export.pdf

import android.content.Context
import com.oplus.note.export.ExportAgent
import com.oplus.note.export.doc.ExportDocData

interface ExportPdfAgent : ExportAgent<ExportDocData, Pair<Int, String>> {
    fun updateNoteInfoIfNeed(context: Context, localId: String, updateTime: Long)
    fun isDeduplicateEnable(): Boolean
}