/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportDocAgent.kt
 ** Description:
 **      interface for doc export
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.content.Context
import com.oplus.note.export.ExportAgent

interface ExportDocAgent : ExportAgent<ExportDocData, Pair<Int, String>> {
    fun updateNoteInfoIfNeed(context: Context, localId: String, updateTime: Long)
    fun isDeduplicateEnable(): Boolean
}