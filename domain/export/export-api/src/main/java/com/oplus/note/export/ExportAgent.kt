/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: ExportAgent.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/08/14
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.export

import android.content.Context
import androidx.annotation.WorkerThread
import java.io.File

interface ExportAgent<T, R> {

    /**
     * 导出分享
     */
    fun export(input: T, doc: DocShareNameInfo, exportProcessor: ExportProcessor?): R

    /**
     * 能力是否支持
     */
    @WorkerThread
    suspend fun isSupport(context: Context, isFromNoteListDrag: Boolean = false): Boolean

    /**
     * 能力是否可用，可用一定支持，支持不一定可用
     */
    @WorkerThread
    suspend fun getDetectDataState(context: Context): Boolean
}

interface ExportProcessor {
    fun processExport(targetFile: File): Int
}