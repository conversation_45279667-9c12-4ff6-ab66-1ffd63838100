/***********************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: ExportAgentFactory.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2023/08/14
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.note.export

import com.oplus.note.export.doc.ExportDocAgent
import com.oplus.note.export.pdf.ExportPdfAgent

object ExportAgentFactory {

    private var docAgent: ExportDocAgent? = null
    private var commonAgent: ExportPdfAgent? = null

    /**
     * 注册导出实现
     */
    fun registerDocAgent(agent: ExportDocAgent) {
        docAgent = agent
    }

    /**
     * 获取Doc导出实现
     */
    fun getDocAgent(): ExportDocAgent? {
        return docAgent
    }

    /**
     * 注册导出实现
     */
    fun registerPdfAgent(agent: ExportPdfAgent) {
        commonAgent = agent
    }

    /**
     * 获取Pdf导出实现
     */
    fun getPdfAgent(): ExportPdfAgent? {
        return commonAgent
    }
}