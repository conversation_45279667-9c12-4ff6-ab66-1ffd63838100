/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportData.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.text.Editable

sealed class ExportDocType {
    class ExportDocText(val text: Editable) : ExportDocType()
    class ExportDocCard(val url: String) : ExportDocType()
    class ExportDocAttachment(val path: String, val columns: Int = 1) : ExportDocType()
}
