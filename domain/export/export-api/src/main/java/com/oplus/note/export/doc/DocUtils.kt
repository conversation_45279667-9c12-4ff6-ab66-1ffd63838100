/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - DocUtils.kt
 ** Description:
 **  doc utils
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Environment
import android.os.StatFs
import androidx.annotation.VisibleForTesting
import androidx.collection.ArrayMap
import androidx.core.content.FileProvider
import androidx.core.content.edit
import com.oplus.note.logger.AppLogger
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.math.min

@Suppress("UtilityClassWithPublicConstructor")
object DocUtils {
    private const val TAG = "DocUtils"
    private const val DRAG_DROP_PATH = "note_to_doc"
    const val DOC_FILENAME_PREFFIX = "NOTE"
    private const val FILE_NAME_FINAL_REGEX = "[/\\\\:*?|\"<>\n\r]"
    private const val GRANT_PERMISSION_PACKAGE = "com.oplus.aiunit"
    private const val DOC = ".doc"
    const val DOCX = ".docx"
    private const val EXPORT_MAP = "export_map"
    private const val MAX_TEXT_DOC_SIZE = 1024 * 1024 * 4L// text max size
    private const val EXTRA_SIZE = 1024 * 1024 * 100L
    private const val MAX_SINGLE_ATTACH_SIZE = 1024 * 1024 * 4L //max single attach file
    private const val EXPIRED_TIME = 1000 * 60 * 60 * 24 * 2L //expired time
    private const val TIME_SECOND = 1000L
    private var lastUseTime: Long = 0
    private const val TOP_THIRTY: Int = 30
    private const val NOTE: String = "NOTE"
    /**
     * 笔记列表同一次列表拖拽标题有相同的需要去重
     */
    private val duplicateTitleCountMap = ArrayMap<String, Int>()
    @JvmStatic
    private fun getDocumentTimeName(timeStamp: Long = 0, name: String = "NOTE"): String {
        val format = SimpleDateFormat("yyyyMMddHHmmss")
        val dateStr = format.format(Date(timeStamp))
        return "${name}$dateStr"
    }

    fun getTempDirectory(): String {
        return Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOWNLOADS
        ).absolutePath + File.separator + "Notes/"
    }

    @JvmStatic
    fun getDocxFile(name: String, fileNameSuffix: String = DOCX): File {
        val file = File(
            "${getTempDirectory()}${name.replace(FILE_NAME_FINAL_REGEX.toRegex(), "-")}$fileNameSuffix"
        )
        if (!file.parentFile.exists()) {
            file.parentFile.mkdirs()
        }
        return file
    }

    /**
     * 获取拖拽的文件，并删除历史遗留文件
     */
    @JvmStatic
    fun getDragExportDocFilePath(
        context: Context,
        localId: String,
        name: String,
        getParentUri: Boolean = false
    ): File {
        getExportSp(context).edit(true) {
            putString("${name}$DOC", localId)
        }
        return if (getParentUri) {
            getExportFolder(context)
        } else {
            File(getExportFolder(context), "${name}$DOC")
        }
    }

    fun getLocalIdByName(context: Context, name: String): String? {
        return getExportSp(context).getString(name, null)
    }

    @VisibleForTesting
    fun getExportFolder(context: Context): File = File(context.filesDir, DRAG_DROP_PATH)

    fun getExportSp(context: Context): SharedPreferences {
        return context.getSharedPreferences(EXPORT_MAP, Context.MODE_PRIVATE)
    }

    fun clearDocFileInNeed(context: Context) {
        val needDelFiles = mutableListOf<File>()
        getExportFolder(context).takeIf { it.exists() }?.listFiles()?.forEach {
            if ((System.currentTimeMillis() - it.lastModified()) > EXPIRED_TIME) {
                needDelFiles.add(it)
            }
        }
        getExportSp(context).edit(true) {
            needDelFiles.forEach { df ->
                df.delete()
                remove(df.name)
            }
        }
    }


    /**
     * 给AIUNit读取uri权限
     */
    @JvmStatic
    fun grantUriPermission(context: Context, path: String?, authority: String): Uri {
        val uri = FileProvider.getUriForFile(context, authority, File(path))
        val flag =
            Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
        context.grantUriPermission(GRANT_PERMISSION_PACKAGE, uri, flag)
        return uri
    }

    /**
     * 根据Note生成doc文件名
     * content 正文
     * @param checkDuplicateTitleCount true 列表拖拽doc生成title去重
     */
    @JvmStatic
    fun getDocFileName(
        title: String?,
        content: String = "",
        docPath: File = File(""),
        checkDuplicateTitleCount: Boolean = false,
        fileNameSuffix: String = DOC
    ): String {
        val fileName = if (title.isNullOrBlank()) {
            getContentTopThirty(content)
        } else {
            title
        }
        val mTitle = fileNameFilter(fileName)
        val sb = StringBuilder(mTitle)
        var count = countFilesWithName(docPath, sb.toString(), fileNameSuffix)
        if (checkDuplicateTitleCount) {
            val duplicateCount = duplicateTitleCountMap.getOrDefault(mTitle, 0)
            count += duplicateCount
            duplicateTitleCountMap[mTitle] = duplicateCount + 1
        }
        if (count != 0) {
            sb.append("(")
            sb.append(count)
            sb.append(")")
        }
        AppLogger.BASIC.d(TAG, "count:$count")
        if (title?.isEmpty() == true) {
            return "$NOTE$sb"
        }
        return sb.toString().ifEmpty { NOTE }
    }

    /**
     *  边界场景：当标题中有不支持的字符，将不支持字符全部替换为支持的字符“-”，“：”去掉
     */
    @JvmStatic
    fun fileNameFilter(fileName: String): String {
        return FILE_NAME_FINAL_REGEX.toRegex().replace(
            fileName.replace(":", "")
                .replace("/", "-").replace("\t", "").trim(), "-"
        )
    }

    @VisibleForTesting
    fun getAvailableSize(path: String?): Long {
        return if (isMounted()) {
            val stat = StatFs(path)
            /* 获取block的SIZE */
            val blockSize = stat.blockSizeLong
            /* 空闲的Block的数量 */
            val availableBlocks = stat.availableBlocksLong
            availableBlocks * blockSize // MIB单位
        } else {
            0
        }
    }

    @VisibleForTesting
    fun isMounted(): Boolean {
        return Environment.MEDIA_MOUNTED == Environment.getExternalStorageState()
    }

    fun checkNoteDragStorageEnough(
        context: Context,
        size: Int
    ): Boolean {
        // check attach size
        val availableSize = getAvailableSize(context.applicationContext.filesDir.absolutePath)
        var totalSize = size.toLong()
        AppLogger.BASIC.d(TAG, "startDragNoteItem size start")
        totalSize *= MAX_SINGLE_ATTACH_SIZE
        totalSize += MAX_TEXT_DOC_SIZE
        AppLogger.BASIC.d(TAG, "startDragNoteItem size=$totalSize av=$availableSize")
        totalSize += EXTRA_SIZE
        if (availableSize < size) {
            AppLogger.BASIC.e(TAG, "startDragNoteItem not enough")
            return false
        }
        return true
    }

    fun getExistDocFileAndClearInvalidFile(
        context: Context,
        localId: String,
        updateTime: Long
    ): File? {
        val exportFolder = getExportFolder(context)
        var docFile: File? = null
        val deleteKey = mutableListOf<String>()
        getExportSp(context).all.filter {
            it.value == localId
        }.forEach {
            File(exportFolder, it.key).run {
                if (exists()) {
                    if (lastModified() >= updateTime) {
                        docFile = this
                    } else {
                        delete()
                        deleteKey.add(it.key)
                    }
                }
            }
        }
        if (deleteKey.isNotEmpty()) {
            getExportSp(context).edit(true) {
                deleteKey.forEach { key ->
                    remove(key)
                }
            }
        }

        return docFile
    }

    fun constructFile(path: String): File {
        return File(path)
    }

    /**
     * mContent 原文
     * return 符合条件的内容
     * doc 命名规则：
     * 笔记详情页分享和保存为word、笔记列表页拖拽生成word时文档的默认命名
     * 笔记有标题时：以笔记的标题为文件的默认命名
     * 笔记无标题时：使用正文第一行作为笔记的默认标题，限制最大字符30个字
     * 文件名示例：笔记标题.docx
     */
    @JvmStatic
    fun getContentTopThirty(mContent: String): String {
        var mStr = ""
        val mList = mContent.split("\n")
        mList.firstOrNull { it.isNotBlank() }?.let { firstNonEmpty ->
            val mResult = firstNonEmpty.substring(0 until min(TOP_THIRTY, firstNonEmpty.length))
            mStr = removeHighSurrogate(mResult)
        }
        return mStr
    }

    @JvmStatic
    private fun removeHighSurrogate(result: String): String {
        val lastChar = result.last()
        return if (lastChar.isHighSurrogate()) {
            result.dropLast(1)
        } else {
            result
        }
    }

    /**
     * 判断当前文件夹下是否有重名的文件，如果有则生成唯一的文件名。
     * 文件名规则：如果发现重名，则依次尝试 file(1)、file(2)...直到没有重复为止。
     * 同时，确保已存在的文件内容不改变，并返回最终的重命名次数。
     *
     * @param folder 文件夹路径
     * @param fileName 要检查和生成的文件名
     * @param fileNameSuffix 文件后缀 如.doc .docx
     * @return 最终的重命名次数
     */
    @JvmStatic
    fun countFilesWithName(folder: File, fileName: String, fileNameSuffix: String): Int {
        var count = 0
        var newFileName = fileName + fileNameSuffix
        try {
            // 检查文件夹是否有效
            if (!folder.exists() || !folder.isDirectory) {
                AppLogger.DEBUG.d(TAG, "目标文件夹无效！")
                return 0
            }
            // 循环检查是否存在相同文件名
            while (true) {
                val potentialFile = File(folder, newFileName)
                // 如果文件名不存在，则返回当前唯一文件名
                if (!potentialFile.exists()) {
                    break
                }
                // 文件存在，增加计数器并生成新的文件名
                count++
                newFileName = if (fileNameSuffix.isNotEmpty()) {
                    "$fileName($count)$fileNameSuffix"
                } else {
                    "$fileName($count)"
                }
            }

            // 创建新文件以确保文件名唯一（模拟新文件创建，内容为空）
            val finalFile = File(folder, newFileName)
            if (finalFile.createNewFile()) {
                AppLogger.DEBUG.d(TAG, "新文件已创建")
            } else {
                AppLogger.DEBUG.d(TAG, "文件创建失败")
            }
        } catch (e: IOException) {
            AppLogger.DEBUG.d(TAG, "访问文件夹时出错: ${e.message}")
        }

        return count
    }

    /***
     * 根据uri获取拖拽doc文件路径
     */
    @JvmStatic
    fun getFileForUri(context: Context, uri: Uri): File? {
        val uriPath = uri.path ?: return null
        val filePath = if (uriPath.contains(DRAG_DROP_PATH)) {
            context.filesDir.absolutePath +
                    File.separator +  uriPath.drop(uriPath.indexOf(DRAG_DROP_PATH))
        } else {
            null
        }
        return filePath?.let {
            File(filePath)
        }
    }

    /**
     * 执行拖拽前，必须清空
     * @see NoteListDragHelper#startDragNoteItem
     */
    @JvmStatic
    fun clearDuplicateTitleMap() {
        duplicateTitleCountMap.clear()
    }
}