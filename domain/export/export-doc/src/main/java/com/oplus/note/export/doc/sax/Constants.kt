/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - Constants.kt
 ** Description:
 **
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc.sax

const val DEFAULT_HIGHLIGHT_COLOR = "YELLOW"
const val RED_HIGHLIGHT_COLOR = "RED"
const val BLUE_HIGHLIGHT_COLOR = "BLUE"
const val GREEN_HIGHLIGHT_COLOR = "GREEN"

const val DEFAULT_UNDERLINE_COLOR = "000000"
const val DEFAULT_TABLE = "0"


const val DEFAULT_GRAY = "929292"

const val DEFAULT_GRAY_COLOR = "grayColor"
const val DEFAULT_RED_COLOR = "redColor"
const val DEFAULT_ORANGE_COLOR = "orangeColor"
const val DEFAULT_YELLOW_COLOR = "yellowColor"
const val DEFAULT_GREEN_COLOR = "greenColor"
const val DEFAULT_BLUE_COLOR = "blueColor"
const val DEFAULT_COLOR_F = 0xFFFFFF

const val RAWTEXT_GRAY_COLOR = "color_gray"
const val RAWTEXT_RED_COLOR = "color_red"
const val RAWTEXT_ORANGE_COLOR = "color_orange"
const val RAWTEXT_YELLOW_COLOR = "color_yellow"
const val RAWTEXT_GREEN_COLOR = "color_green"
const val RAWTEXT_BLUE_COLOR = "color_blue"
const val COLOR_HIGHLIGHTED_YELLOW = "color_highlightedYellow"
const val COLOR_HIGHLIGHTED_RED = "color_highlightedRed"
const val COLOR_HIGHLIGHTED_BLUE = "color_highlightedBlue"
const val COLOR_HIGHLIGHTED_GREEN = "color_highlightedGreen"

const val DEFAULT_TEXT_SIZE = 14
const val DEFAULT_LEVEL = "0"
const val LEVEL_ONE = "ql-indent-1"
const val LEVEL_TWO = "ql-indent-2"
const val LEVEL_THREE = "ql-indent-3"
const val LEVEL_FOUR = "ql-indent-4"
const val LEVEL_FIVE = "ql-indent-5"
const val LEVEL_SIX = "ql-indent-6"
const val LEVEL_SEVEN = "ql-indent-7"
const val LEVEL_EIGHT = "ql-indent-8"
const val DEFAULT_VAL = "0"
const val DEFAULT_VAL_ONE = "1"
const val DEFAULT_VAL_TWO = "2"
const val DEFAULT_STYLE_LINE = "list-style-line"
const val DEFAULT_BORDER_SIZE = "4"
const val QUOTE = "quote"
const val CLASS_HALF_START = "inline-1"
const val CLASS_HALF_END = "inline-2"
const val CLASS_HALF = "size-half"
const val DEFAULT_ENCODING = "UTF-16"
const val DEFAULT_INDENT = "yes"

internal object Tag {
    //root tag
    const val TAG_ROOT_DOCUMENT = "w:document"

    //body tag
    const val TAG_BODY = "w:body"

    //paragraph root tag
    const val TAG_PARAGRAPH_ROOT = "w:p"

    //paragraph style
    const val TAG_PARAGRAPH_STYLE = "w:pPr"

    // Paragraph Border
    const val TAG_PARAGRAPH_BORDER = "w:pBdr"

    //Border tage
    const val TAG_BORDER_BOTTOM = "w:bottom"


    //title style
    const val TAG_TITLE_STYLE = "w:pStyle"

    //paragraph align
    const val TAG_PARAGRAPH_ALIGNMENT = "w:jc"

    //text tag
    const val TAG_CONTENT_ROOT = "w:r"

    //text tag
    const val TAG_TEXT_STYLE = "w:rPr"

    //text size
    const val TAG_TEXT_STYLE_SIZE = "w:sz"

    //text bold
    const val TAG_TEXT_STYLE_BOLD = "w:b"

    //text s
    const val TAG_TEXT_STYLE_S = "w:strike"

    //text italic
    const val TAG_TEXT_STYLE_ITALIC = "w:i"

    //text bold
    const val TAG_TEXT_CONTENT = "w:t"

    //text underline
    const val TAG_TEXT_STYLE_UNDERLINE = "w:u"

    //text highlight
    const val TAG_TEXT_STYLE_HIGHLIGHT = "w:highlight"

    //text color
    const val TAG_TEXT_STYLE_COLOR = "w:color"

    //media tag
    const val TAG_MEDIA = "w:media"

    //text hyperlink
    const val TAG_HYPERLINK = "w:hyperlink"

    //numbering root
    const val TAG_NUMBERING_ROOT = "w:ns"

    //numbering style
    const val TAG_NUMBERING_STYLE = "w:numberStyle"

    const val TAG_NUMBERING_LIST = "w:lvl"

    //numbering style
    const val TAG_LIST_LVL = "w:lvl"

    //table type
    const val TAG_TABLE = "w:tbl"
    const val TAG_TABLE_PR = "w:tblPr"
    const val TAG_PR_W = "w:tblW"
    const val TAG_TBL_GRID = "w:tblGrid"
    const val TAG_TR = "w:tr"
    const val TAG_TC = "w:tc"

    //numbering check type
    const val TAG_NUMBERING_TYPE = "w:lvltext"

    //numbering data
    const val TAG_NUMBERING_DATA = "w:numberData"

    //numbering format
    const val TAG_NUMBERING_FORMAT = "w:numformat"

    //indentation format
    const val ATTR_IND = "w:ind"
}

internal object Attr {
    //tag common value
    const val ATTR_COMMON_VALUE = "w:val"
    const val ATTR_ANCHOR_VALUE = "w:anchor"

    //indentation
    const val ATTR_INDENTATION_VALUE = "w:leftChars"

    //document attr
    const val ATTR_DOCUMENT_ATTR_XMLNS_W = "xmlns:w"
    const val ATTR_DOCUMENT_ATTR_XMLNS_R = "xmlns:r"

    const val ATTR_TC_PR = "w:tcPr"
    const val ATTR_W_SHD = "w:shd"
    const val  ATTR_TABLE_COLOR = "w:color"
    const val  ATTR_TABLE_FILL = "w:fill"
    const val  TABLE_BACKGROUND = "background"
    const val  TABLE_VAR = "var(--"
    const val  TABLE_VAR_END = ")"

    //media attr
    const val ATTR_MEDIA_SCALE_PATTERN = "w:scalepattern"
    const val ATTR_MEDIA_ALIGN = "w:align"
    const val ATTR_MEDIA_URL = "w:url"
    const val ATTR_TEXT_UNDERLINE_COLOR = "w:color"
    const val ATTR_NUMBERING_LEVEL = "w:level"
    const val ATTR_WIDTH = "w:width"
    const val ATTR_HEIGHT = "w:height"

    //table
    const val TAG_PR_W_W = "w:w"
    const val TAG_PR_W_TYPE = "w:type"
    const val TAG_PR_W_W_VALUE = "pct"

    const val TAG_PR_LAYOUT = "w:tblLayout"
    const val TAG_PR_W_TYPE_VALUE = "fixed"
    const val TAG_PR_W_W_GRID_VALUE = "900"
    const val TAG_TBL_GRID_COL = "w:gridCol"

    const val ATTR_NUM_FMT = "w:numFmt"
    const val ATTR_HX = "list-style-line"
    const val LVL_TEXT = "w:lvlText"

    const val ATTR_NUM_OL_START = "w:start"

    const val WORD_MAX_WIDTH = 540
}

internal object Value {
    const val FIT = "fit"
    const val NONE = "none"
    const val AUTO = "auto"
    const val SINGLE = "single"
    const val WAVE = "wave"
    const val TRUE = "true"
    const val PNG = "png"
    const val CLEAR = "clear"
}

internal object Alignment {
    const val RIGHT = "right"
    const val LEFT = "left"
    const val CENTER = "center"
}

internal object Color {
    const val COLOR_DEFAULT_TEXT = "000000"
    const val COLOR_UNDERLINE = "0000FF"
}

internal object Text {
    //默认标题字体大小，对应二号字体
    const val TEXT_TITLE_SIZE = "22"
}
