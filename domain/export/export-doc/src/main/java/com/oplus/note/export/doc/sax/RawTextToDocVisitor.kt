/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : Html2DocVisitor.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/1/25
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/1/25        1.0           create
 */
package com.oplus.note.export.doc.sax

import android.annotation.SuppressLint
import android.content.Context
import android.text.Layout
import androidx.core.content.ContextCompat
import com.oplus.note.export.doc.WVExtraNoteData
import com.oplus.note.export.doc.sax.Attr.ATTR_COMMON_VALUE
import com.oplus.note.export.doc.sax.Attr.ATTR_HX
import com.oplus.note.export.doc.sax.Attr.ATTR_NUM_FMT
import com.oplus.note.export.doc.sax.Attr.ATTR_NUM_OL_START
import com.oplus.note.export.doc.sax.Attr.LVL_TEXT
import com.oplus.note.export.doc.sax.SaxManager.DECIMAL
import com.oplus.note.export.doc.sax.SaxManager.TITLE_LEVEL_2
import com.oplus.note.export.doc.sax.SaxManager.TITLE_LEVEL_3
import com.oplus.note.export.doc.sax.SaxManager.TITLE_LEVEL_4
import com.oplus.note.export.doc.sax.SaxManager.TITLE_LEVEL_5
import com.oplus.note.export.doc.sax.Tag.TAG_PR_W
import com.oplus.note.export.doc.sax.Tag.TAG_TITLE_STYLE
import com.oplus.note.logger.AppLogger
import com.oplus.richtext.core.html.CssAttributes
import com.oplus.richtext.core.html.HtmlAttributes
import com.oplus.richtext.core.html.HtmlAttributes.Value.CHECKED
import com.oplus.richtext.core.html.HtmlAttributes.Value.UNCHECKED
import com.oplus.richtext.core.html.HtmlTags
import com.oplus.richtext.core.html.HtmlTags.H1
import com.oplus.richtext.core.html.HtmlTags.H2
import com.oplus.richtext.core.html.HtmlTags.H3
import com.oplus.richtext.core.html.HtmlTags.H4
import com.oplus.richtext.core.html.HtmlTags.TABLE
import com.oplus.richtext.core.html.HtmlTags.TD
import com.oplus.richtext.core.html.HtmlTags.TR
import org.jsoup.Jsoup
import org.jsoup.nodes.Attributes
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode
import org.jsoup.select.Elements
import org.jsoup.select.NodeVisitor
import org.xml.sax.helpers.AttributesImpl
import java.util.Locale
import javax.xml.transform.sax.TransformerHandler

@Suppress("LargeClass")
open class RawTextToDocVisitor(
    open val context: Context,
    open val authority: String,
    open val handler: TransformerHandler,
    open val attr: AttributesImpl,
    open val noteId: String,
    open val wvExtraNoteData: WVExtraNoteData
) : NodeVisitor {
    companion object {
        private const val TAG = "HtmlToDocVisitor"
        private const val DEBUG_WORD_TAG = true
        private const val LEVEL_ONE_VALUE = "250"
        private const val LEVEL_TWO_VALUE = "500"
        private const val LEVEL_THREE_VALUE = "750"
        private const val LEVEL_FOUR_VALUE = "1000"
        private const val LEVEL_FIVE_VALUE = "1250"
        private const val LEVEL_SIX_VALUE = "1500"
        private const val LEVEL_SEVEN_VALUE = "1750"
        private const val LEVEL_EIGHT_VALUE = "2000"
        private const val MAX_FIND_LEVEL = 7
        private const val MAX_FIND_TABLE_LEVEL = 5
        const val DARK_ORANGE = "dark_orange"
        const val DARK_RED = "dark_red"
        const val DARK_BLUE = "dark_blue"
        const val DARK_GREEN = "dark_green"
        const val DARK_GREY = "dark_grey"
        const val LIGHT_ORANGE = "light_orange"
        const val LIGHT_RED = "light_red"
        const val LIGHT_BLUE = "light_blue"
        const val LIGHT_GREEN = "light_green"
        const val LIGHT_GREY = "light_grey"
        const val TABLE_PICK_COLOR_DEFAULT = "default"
        const val START_LIGHT = "light"
        const val START_DARK = "dark"
        // 颜色常量
        private const val COLOR_DARK_ORANGE = "ED9C22"
        private const val COLOR_DARK_RED = "E5603F"
        private const val COLOR_DARK_BLUE = "5171E5"
        private const val COLOR_DARK_GREEN = "45A450"
        private const val COLOR_DARK_GREY = "7F848C"
        private const val COLOR_LIGHT_ORANGE = "fdf5e8"
        private const val COLOR_LIGHT_RED = "fcefeb"
        private const val COLOR_LIGHT_BLUE = "edf1fc"
        private const val COLOR_LIGHT_GREEN = "ecf6ed"
        private const val COLOR_LIGHT_GREY = "f2f2f3"
    }

    private var isWPElementStarted = false
    private var isWpPrElementStarted = false
    private var isWrElementStarted = false
    private var isWrPrElementStarted = false
    private var isWHyperLinkElementStarted = false
    private var isWnsElementStarted = false
    private var isWNumberDataStarted = false

    private var isDivProcessing = false
    private var isOLProcessing = false
    private var isULProcessing = false
    private var isCheckBoxGroupProcessing = false

    private var isCheckBoxItemProcessing = false
    private var isCheckBoxTextProcessing = false
    private var isListItemProcessing = false
    private var isAlignmentProcessing = false
    private var isHProcessing = false
    private var isQuoteProcessing = false

    private val currentTextStyle = mutableMapOf<String, HashMap<String, String>?>()

    private var isHyperTextProcessing = false
    private var hasTextInListItem = false

    private var cachedBrCount = 0
    private var needCacheBr = true
        set(value) {
            field = value
            if (!value) {
                addCachedBrText()
            }
        }
    private var isInDarkTc = false
    private var isInTc = false

    private fun hasHeaderAncestor(node: Element?): Boolean {
        var current = node
        var levelsChecked = 0

        while (current != null && levelsChecked < MAX_FIND_LEVEL) {
            current.attributes()?.get(HtmlAttributes.CLASS)
                ?.takeIf { rawTextIsValidHeader(it) }
                ?.let { return true }

            current = current.parent()
            levelsChecked++
        }
        return false
    }

    override fun head(node: Node, depth: Int) {
        if (node is TextNode) {
            val parentElement = node.parentNode() as Element
            //当且仅当是标题的情况不再加上w:rPr样式
            val classStr = parentElement.attributes()?.get(HtmlAttributes.CLASS)
            val headerFlag = classStr?.let { rawTextIsValidHeader(it) } ?: false
            val hasTableAncestor = hasTableAncestor(parentElement)
            //节点属于H tag的子节点那么不设置默认字体大小
            val isNeedAddTextSizeFlag = !hasHeaderAncestor(parentElement) && !hasTableAncestor
            handleHeadText(node.wholeText, headerFlag, isNeedAddTextSizeFlag, hasTableAncestor)
        } else if (node is Element) {
            handleHeadElement(node)
        }
    }

    private fun findStartValueFromAncestor(node: Element, maxDepth: Int = 5): String {
        var current = node // 从当前节点的父节点开始向上查找祖先
        var depth = 0 // 深度计数器

        while (depth < maxDepth) {
            if (current.tagName() == "ol") { // 检查是否是 <ol>
                val parent = current.parent() // 检查 <ol> 的父节点
                if (parent != null && parent.tagName() == "td") { // 检查父节点是否是 <td>
                    // 找到符合条件的结构后，提取 <ol> 的 start 属性并返回其 Int 值
                    var startValue = current.attr("start") // 获取 start 属性值
                    if (startValue.isNullOrEmpty()) {
                        startValue = "1"
                    }
                    return "$startValue. "
                }
            }
            current = current.parent()!! // 没找到的话继续向上查找
            depth++ // 增加深度计数
        }
        return ""
    }

    private fun hasTableAncestor(node: Element?): Boolean {
        var current = node
        var levelsChecked = 0
        while (current != null && levelsChecked < MAX_FIND_TABLE_LEVEL) {
            if (current.tagName() in listOf(TR, TD)) {
                return true
            }
            current = current.parent()
            levelsChecked++
        }
        return false
    }

    override fun tail(node: Node, depth: Int) {
        if (node is Element) {
            handleTailElement(node)
        } else if (node is TextNode) {
            val parentElement = node.parent() as Element
            val hasTableAncestor = hasTableAncestor(parentElement)
            var calOlText = ""
            if (hasTableAncestor) {
                calOlText = findStartValueFromAncestor(parentElement, MAX_FIND_TABLE_LEVEL)
            }
            val resText = calOlText + node.wholeText
            handleTailText(resText)
        }
    }

    private fun hasClassInUl(html: String, targetClass: String): Boolean {
        val document = Jsoup.parse(html)
        val ulElements = document.select(HtmlTags.UL)

        for (ul in ulElements) {
            if (ul.hasClass(targetClass)) {
                return true
            }
        }
        return false
    }

    @SuppressLint("LongMethod")
    private fun handleHeadElement(node: Element) {
        val tagName = node.tagName()
        if (isParagraphTag(tagName)) {
            decCachedBr()
            needCacheBr = false
        }
        if (tagName == TABLE) {
            addCachedBrText()
        }
        when (tagName) {
            HtmlTags.BR -> {
                if (needCacheBr) {
                    val nodeParentElement = node.parent()
                    if (nodeParentElement != null && nodeParentElement.tagName() == TD) {
                        AppLogger.BASIC.d(TAG, "not need incCachedBr")
                    } else {
                        incCachedBr()
                    }
                } else {
                    handleBr()
                }
            }

            HtmlTags.LI -> {
                val htmlContent = node.toString()
                val headingLevel = findFirstHeadingTag(htmlContent)
                if (isOLProcessing) {
                    handleHeadLI(DECIMAL, DEFAULT_LEVEL, headingLevel, node)
                } else if (isULProcessing) {
                    val classValue = node.attributes()["class"]
                    var level = rawTextCalLevel(classValue)  //缩进
                    val parentNode = node.parent()
                    //当改li属于横线无序列表格式，不应该设置其缩进等级
                    if (parentNode != null && hasClassInUl(
                            parentNode.toString(),
                            DEFAULT_STYLE_LINE
                        )
                    ) {
                        level = DEFAULT_LEVEL
                    }
                    handleHeadLI(SaxManager.BULLET, level, headingLevel, node)
                }
            }

            HtmlTags.DIV -> {
                isDivProcessing = true
                handleHeadDiv(node.attributes(), node)
            }

            HtmlTags.A -> {
                if (!isHyperTextProcessing) {
                    isHyperTextProcessing = true
                }
            }

            HtmlTags.IMG -> {
                /*
                *以前html是通过先解析到了 class="media-attachment" 打一个标志 isMediaProcessing = true
                  现在rawText没有media-attachment  直接解析
                * */
                val attributes = node.attributes()
                if (ElementUtils.matchClasses(
                        attributes.get(HtmlAttributes.CLASS),
                        listOf(CLASS_HALF, CLASS_HALF_END)
                    )
                ) return
                handleHeadImg(attributes)
                // 直接取下一个img标签，检查是否是CLASS_HALF_END
                node.nextSibling()
                    ?.takeIf { it.nodeName() == HtmlTags.IMG }
                    ?.takeIf {
                        ElementUtils.matchClasses(
                            it.attributes().get(HtmlAttributes.CLASS),
                            listOf(CLASS_HALF_END)
                        )
                    }
                    ?.let { handleHeadImg(it.attributes()) }
            }

            HtmlTags.OL -> handleHeadOL(node)
            HtmlTags.UL -> handleHeadUL(node.attributes(), node)
            HtmlTags.SPAN -> handleRawTextSpan(node.attributes(), node)

            HtmlTags.TABLE -> handleTableHead(node.toString())
            TR -> handleTRHead()
            TD -> handleTDHead(node)
            else -> AppLogger.BASIC.d(TAG, "handleHeadElement, no processing:$tagName")
        }
    }

    private fun handleTRHead() {
        attr.clear()
        handler.startElement("", "", Tag.TAG_TR, attr)
    }

    private fun handleTDHead(node: Element) {
        attr.clear()
        isInTc = true
        handler.startElement("", "", Tag.TAG_TC, attr)
        val classStr = node.attr("class")
        val extractedVariable = if (classStr.isNotEmpty()) extractBackgroundVariable(classStr) else null
        isInDarkTc = false
        if (classStr.isNotEmpty() && extractedVariable != null && extractedVariable != "default") {
            // 添加 <w:tcPr> 标签
            attr.clear()
            handler.startElement("", "", Attr.ATTR_TC_PR, attr)
            // 添加 <w:shd> 标签及属性
            attr.clear()
            val shdAttrs = hashMapOf(
                Attr.ATTR_COMMON_VALUE to Value.CLEAR,
                Attr.ATTR_TABLE_COLOR to Value.AUTO,
                Attr.ATTR_TABLE_FILL to getColorValue(extractedVariable)
            )
            // 根据 extractedVariable 的值处理颜色
            if (extractedVariable.startsWith(START_DARK)) {
                isInDarkTc = true
            }
            ElementUtils.createElementAttr(handler, attr, Attr.ATTR_W_SHD, shdAttrs)
            handler.endElement("", "", Attr.ATTR_TC_PR)
        }
        startWpElement()
    }

    /**
     *解析样式 td_bg_dark_red color_white
     *用正则 td_bg_(\w+) 匹配 td_bg_ 后的单词字符（dark_red、light_red 等）。
     *即使 td_bg_ 不在字符串开头也能匹配（如 "some_prefix td_bg_red"）
     */
    private fun extractBackgroundVariable(style: String): String? {
        val regex = """td_bg_(\w+)""".toRegex()
        return regex.find(style)?.groupValues?.get(1)
    }

    private fun getColorValue(variable: String): String {
        val colorMap = mapOf(
            "$DARK_ORANGE" to COLOR_DARK_ORANGE,
            "$DARK_RED" to COLOR_DARK_RED,
            "$DARK_BLUE" to COLOR_DARK_BLUE,
            "$DARK_GREEN" to COLOR_DARK_GREEN,
            "$DARK_GREY" to COLOR_DARK_GREY,
            "$LIGHT_ORANGE" to COLOR_LIGHT_ORANGE,
            "$LIGHT_RED" to COLOR_LIGHT_RED,
            "$LIGHT_BLUE" to COLOR_LIGHT_BLUE,
            "$LIGHT_GREEN" to COLOR_LIGHT_GREEN,
            "$LIGHT_GREY" to COLOR_LIGHT_GREY
        )
        return colorMap[variable] ?: TABLE_PICK_COLOR_DEFAULT
    }
    private fun handleTRTail() {
        handler.endElement("", "", Tag.TAG_TR)
    }

    private fun handleTDTail() {
        isInTc = false
        endWpElement()
        handler.endElement("", "", Tag.TAG_TC)
    }

    private fun handleHeadHR() {
        endWpElement()
        startWpElement()
        attr.clear()
        handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
        attr.clear()
        handler.startElement("", "", Tag.TAG_PARAGRAPH_BORDER, attr)
        attr.clear()
        handler.startElement("", "", Tag.TAG_BORDER_BOTTOM, attr)
        attr.clear()
        ElementUtils.addAttr(
            attr,
            hashMapOf(
                Attr.ATTR_COMMON_VALUE to Value.SINGLE,
                Tag.TAG_TEXT_STYLE_SIZE to DEFAULT_BORDER_SIZE
            )
        )
        handler.endElement("", "", Tag.TAG_BORDER_BOTTOM)
        handler.endElement("", "", Tag.TAG_PARAGRAPH_BORDER)
        handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
        startWrElement()
        attr.clear()
        ElementUtils.createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, " ")
        endWrElement()
        endWpElement()
        //和产品沟通过这里要换行，保证下面文字和其他显示好看，如果不为了显示好看，只需要判断下个节点是hr才占位，其他都不添加占位
        startWpElement()
        startWrElement()
        attr.clear()
        ElementUtils.createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, " ")
        endWrElement()
        endWpElement()
    }


    private fun handleTableHead(html: String) {
        val numTD = calNumTD(html)
        if (numTD < 1) {
            return
        }
        if (isWPElementStarted) {
            endWpElement()
        }
        attr.clear()
        handler.startElement("", "", Tag.TAG_TABLE, attr)
        attr.clear()
        handler.startElement("", "", Tag.TAG_TABLE_PR, attr)
        attr.clear()
        ElementUtils.createElementAttr(
            handler,
            attr,
            TAG_PR_W,
            hashMapOf(
                Attr.TAG_PR_W_W to DEFAULT_TABLE,
                Attr.TAG_PR_W_TYPE to Attr.TAG_PR_W_W_VALUE
            )
        )
        attr.clear()
        ElementUtils.createElementAttr(
            handler,
            attr,
            Attr.TAG_PR_LAYOUT,
            hashMapOf(
                Attr.TAG_PR_W_TYPE to Attr.TAG_PR_W_TYPE_VALUE
            )
        )
        handler.endElement("", "", Tag.TAG_TABLE_PR)
        attr.clear()
        handler.startElement("", "", Tag.TAG_TBL_GRID, attr)
        for (i in 1..numTD) {
            attr.clear()
            ElementUtils.createElementAttr(
                handler,
                attr,
                Attr.TAG_TBL_GRID_COL,
                hashMapOf(
                    Attr.TAG_PR_W_W to Attr.TAG_PR_W_W_GRID_VALUE
                )
            )
        }
        handler.endElement("", "", Tag.TAG_TBL_GRID)
    }

    private fun calNumTD(html: String): Int {
        html.trimIndent()
        // 解析HTML
        val doc: Document = Jsoup.parse(html)
        // 选择第一个tr元素
        val firstTr: Element? = doc.select("tr").first()
        // 选择第一个tr元素中的所有td元素
        val tds: Elements? = firstTr?.select("td")
        val numTD = tds?.size ?: 0
        return numTD
    }

    private fun calLevel(classValue: String): String {
        //这里是无序嵌套列表的等级只有三个等级
        return when (classValue) {
            LEVEL_ONE -> DEFAULT_VAL_ONE
            LEVEL_TWO, LEVEL_THREE, LEVEL_FOUR, LEVEL_FIVE, LEVEL_SIX, LEVEL_SEVEN, LEVEL_EIGHT -> DEFAULT_VAL_TWO
            else -> DEFAULT_VAL
        }
    }

    private fun rawTextCalLevel(levelStr: String?): String {
        val levels = setOf(
            LEVEL_ONE, LEVEL_TWO, LEVEL_THREE, LEVEL_FOUR,
            LEVEL_FIVE, LEVEL_SIX, LEVEL_SEVEN, LEVEL_EIGHT
        ).map { it.lowercase(Locale.ROOT) }

        val lowerLevelStr = levelStr?.lowercase(Locale.ROOT) ?: ""

        val classValue = levels.firstOrNull { level ->
            lowerLevelStr.contains(level)
        } ?: "0"
        return calLevel(classValue)
    }

    private var lastClosedTag: String? = null
    private fun handleTailElement(node: Element) {
        val tagName = node.tagName()
        when (tagName) {
            HtmlTags.BODY -> {
                if (lastClosedTag == Tag.TAG_TABLE) {
                    addEmptyParagraph()
                }
                endWpElement()
                dumpNoCloseTag()
            }

            HtmlTags.DIV -> {
                isDivProcessing = false
                //li嵌套div时，DIV所有属性已在li上处理了，直接丢弃即可
                node.parent()?.takeIf { it.tagName() == HtmlTags.LI }?.let { return }

                handleTailDiv(node)
            }

            HtmlTags.A -> {
                if (isHyperTextProcessing) {
                    endWHyperLinkElement()
                    isHyperTextProcessing = false
                }
            }

            HtmlTags.SPAN -> handleRawTextTailSpan(node.attributes())
            HtmlTags.OL -> handleTailOL()
            HtmlTags.UL -> handleTailUL()
            HtmlTags.LI -> handleTailLI()
            HtmlTags.TABLE -> handleTableTail()
            TR -> handleTRTail()
            TD -> handleTDTail()
            else -> AppLogger.BASIC.d(TAG, "handleTailElement, no processing:$tagName")
        }
        updateLastClosedTag(tagName)
    }

    private fun addEmptyParagraph() {
        attr.clear()
        endWpElement()
        startWpElement() // 开始 <w:p>
        attr.clear()
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr) // 开始 <w:r>
        attr.clear()
        handler.startElement("", "", Tag.TAG_TEXT_CONTENT, attr) // 开始 <w:t>
        val text = "\ufeff" // 不可见占位符
        handler.characters(text.toCharArray(), 0, text.length)
        handler.endElement("", "", Tag.TAG_TEXT_CONTENT) // 结束 <w:t>
        handler.endElement("", "", Tag.TAG_CONTENT_ROOT) // 结束 <w:r>
        endWpElement() // 结束 <w:p>
    }

    private fun updateLastClosedTag(tag: String) {
        if (tag == TABLE) {
            lastClosedTag = Tag.TAG_TABLE
        } else if (tag != HtmlTags.BR) {
            lastClosedTag = tag
        }
    }

    private fun handleTableTail() {
        handler.endElement("", "", Tag.TAG_TABLE)
    }

    private fun findFirstHeadingTag(html: String): String? {
        extractHTags(html)?.let {  tag ->
            return when (tag) {
                H1 -> TITLE_LEVEL_2
                H2 -> TITLE_LEVEL_3
                H3 -> TITLE_LEVEL_4
                H4 -> TITLE_LEVEL_5
                else -> null
            }
        }
        return null
    }

    private fun rawTextIsValidHeader(input: String): Boolean {
        return Regex("h[1-5]").containsMatchIn(input)
    }

    private fun rawTextIsValidInd(levelStr: String?): Boolean {
        val levels = setOf(
            LEVEL_ONE, LEVEL_TWO, LEVEL_THREE, LEVEL_FOUR,
            LEVEL_FIVE, LEVEL_SIX, LEVEL_SEVEN, LEVEL_EIGHT
        ).map { it.lowercase(Locale.ROOT) }

        val lowerLevelStr = levelStr?.lowercase(Locale.ROOT) ?: return false

        return levels.any { level ->
            lowerLevelStr.contains(level)
        }
    }

    protected fun handleHeadText(
        text: String,
        flag: Boolean = false,
        isNeedAddDefaultTextSize: Boolean = true,
        hasTableAncestor: Boolean = false
    ) {
        if (hasTableAncestor) {
            AppLogger.BASIC.d(TAG, "hasTableAncestor")
        } else {
            addCachedBrText()
        }
        needCacheBr = !isDivProcessing && !isULProcessing && !isOLProcessing
        if (isHyperTextProcessing
            || (isDivProcessing && (wvExtraNoteData.pageResultUrl?.contains(text) == true))
        ) {
            startWHyperLinkElement(text)
        }

        val needAddDefaultTextSize =
            !currentTextStyle.keys.contains(Tag.TAG_TEXT_STYLE_SIZE) && isNeedAddDefaultTextSize
        // 增加默认字体大小
        if (needAddDefaultTextSize) {
            handleHeadTextStyle(
                Tag.TAG_TEXT_STYLE_SIZE,
                hashMapOf(Attr.ATTR_COMMON_VALUE to DEFAULT_TEXT_SIZE.toString())
            )
        }

        // 插入文本前，先尝试添加文本字符属性
        if (flag) {
            startWrElement(true)
        } else if (!addTextStyleElementIfExist()) {
            // 当文本字符属性为空时，不会添加 wr 标签，因此此处需要额外添加 wr 标签，确保文本内容在 wr 标签内
            if (currentTextStyle.isEmpty()) {
                startWrElement(true)
            } else {
                startWrElement()
            }
        }

        // 增加默认字体大小完成后，需从currentTextStyle移除字体大小
        if (needAddDefaultTextSize) {
            handleTailTextStyle(Tag.TAG_TEXT_STYLE_SIZE)
        }
    }

    protected fun handleTailText(text: String) {
        if (isListItemProcessing) {
            hasTextInListItem = true
        }
        attr.clear()
        wordTagLog("<${Tag.TAG_TEXT_CONTENT}>textLength:${text.length}</${Tag.TAG_TEXT_CONTENT}>")
        ElementUtils.createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, text)
        // 字符插入完成后，需要结束 w:r 标签
        endWrElement()
    }

    private fun handleBr() {
        val isConditionMet = !isAlignmentProcessing && !isULProcessing && !isOLProcessing && !isCheckBoxGroupProcessing && !isHProcessing

        if (isConditionMet) {
            addBrText(1)
        }
    }

    private fun startWpElement() {
        if (!isWPElementStarted) {
            isWPElementStarted = true
            attr.clear()
            wordTagLog("<${Tag.TAG_PARAGRAPH_ROOT}>")
            handler.startElement("", "", Tag.TAG_PARAGRAPH_ROOT, attr)
        }
    }

    protected fun endWpElement() {
        if (isWPElementStarted) {
            wordTagLog("</${Tag.TAG_PARAGRAPH_ROOT}>")
            handler.endElement("", "", Tag.TAG_PARAGRAPH_ROOT)
            isWPElementStarted = false
        }
    }

    private fun startWHyperLinkElement(hyperText: String) {
        if (!isWHyperLinkElementStarted) {
            isWHyperLinkElementStarted = true
            startWpElement()
            attr.clear()
            ElementUtils.addAttr(
                attr,
                hashMapOf(Attr.ATTR_COMMON_VALUE to hyperText, Attr.ATTR_ANCHOR_VALUE to hyperText)
            )
            wordTagLog(
                "<${Tag.TAG_HYPERLINK}${
                    mapToString(
                        hashMapOf(
                            Attr.ATTR_COMMON_VALUE to hyperText,
                            Attr.ATTR_ANCHOR_VALUE to hyperText
                        )
                    )
                }>"
            )
            handler.startElement("", "", Tag.TAG_HYPERLINK, attr)
            attr.clear()
            handleHeadTextStyle(
                Tag.TAG_TEXT_STYLE_COLOR,
                hashMapOf(Attr.ATTR_COMMON_VALUE to Color.COLOR_UNDERLINE)
            )
            handleHeadTextStyle(
                Tag.TAG_TEXT_STYLE_UNDERLINE, hashMapOf(
                    Attr.ATTR_TEXT_UNDERLINE_COLOR to Value.AUTO,
                    Attr.ATTR_COMMON_VALUE to Value.SINGLE
                )
            )
        }
    }

    private fun endWHyperLinkElement() {
        if (isWHyperLinkElementStarted) {
            handleTailTextStyle(Tag.TAG_TEXT_STYLE_UNDERLINE)
            handleTailTextStyle(Tag.TAG_TEXT_STYLE_COLOR)
            wordTagLog("</${Tag.TAG_HYPERLINK}>")
            handler.endElement("", "", Tag.TAG_HYPERLINK)
            isWHyperLinkElementStarted = false
        }
    }

    private fun startWrElement(isNeedAddRpr: Boolean = false) {
        if (!isWrElementStarted) {
            startWpElement()
            isWrElementStarted = true
            attr.clear()
            wordTagLog("<${Tag.TAG_CONTENT_ROOT}>")
            handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
            if (isInTc && isInDarkTc && isNeedAddRpr) {
                // 开始 <w:rPr> 标签
                attr.clear()
                handler.startElement("", "", "w:rPr", attr)
                // 添加 <w:color> 标签及属性
                attr.clear()
                val colorAttrs = hashMapOf(
                    ATTR_COMMON_VALUE to getColorCode(context, com.oplus.richtext.core.R.color.pick_color_white)
                )
                ElementUtils.createElementAttr(handler, attr, "w:color", colorAttrs)
                // 结束 <w:rPr> 标签
                handler.endElement("", "", "w:rPr")
            }
        }
    }

    private fun endWrElement() {
        if (isWrElementStarted) {
            wordTagLog("</${Tag.TAG_CONTENT_ROOT}>")
            handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
            isWrElementStarted = false
        }
    }

    private fun startWrPrElement() {
        if (!isWrPrElementStarted) {
            //添加 w:rpr 标签时，需要先确保父标签 w:r 已经添加
            startWrElement()
            isWrPrElementStarted = true
            attr.clear()
            wordTagLog("<${Tag.TAG_TEXT_STYLE}>")
            handler.startElement("", "", Tag.TAG_TEXT_STYLE, attr)
            if (isInTc && isInDarkTc) {
                attr.clear()
                val colorAttrs = hashMapOf(
                    ATTR_COMMON_VALUE to getColorCode(context, com.oplus.richtext.core.R.color.pick_color_white)
                )
                ElementUtils.createElementAttr(handler, attr, Tag.TAG_TEXT_STYLE_COLOR, colorAttrs)
            }
        }
    }

    private fun endWrPrElement() {
        if (isWrPrElementStarted) {
            wordTagLog("</${Tag.TAG_TEXT_STYLE}>")
            handler.endElement("", "", Tag.TAG_TEXT_STYLE)
            isWrPrElementStarted = false
        }
    }

    private fun startWpPrElement() {
        if (!isWpPrElementStarted) {
            startWpElement()
            isWpPrElementStarted = true
            attr.clear()
            wordTagLog("<${Tag.TAG_PARAGRAPH_STYLE}>")
            handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
        }
    }

    private fun endWpPrElement() {
        if (isWpPrElementStarted) {
            wordTagLog("</${Tag.TAG_PARAGRAPH_STYLE}>")
            handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
            isWpPrElementStarted = false
        }
    }

    private fun startWnsElement() {
        if (!isWnsElementStarted) {
            //开始分组列表，需要先结束上一个 w:p 标签
            endWpElement()
            isWnsElementStarted = true
            attr.clear()
            wordTagLog("<${Tag.TAG_NUMBERING_ROOT}>")
            handler.startElement("", "", Tag.TAG_NUMBERING_ROOT, attr)
        }
    }

    private fun endWnsElement() {
        if (isWnsElementStarted) {
            wordTagLog("</${Tag.TAG_NUMBERING_ROOT}>")
            handler.endElement("", "", Tag.TAG_NUMBERING_ROOT)
            isWnsElementStarted = false
        }
    }

    private fun startWNumberData(level: String) {
        if (!isWNumberDataStarted) {
            isWNumberDataStarted = true
            attr.clear()
            ElementUtils.addAttr(attr, hashMapOf(Attr.ATTR_NUMBERING_LEVEL to level))
            wordTagLog("<${Tag.TAG_NUMBERING_DATA} ${hashMapOf(Attr.ATTR_NUMBERING_LEVEL to level)}>")
            handler.startElement("", "", Tag.TAG_NUMBERING_DATA, attr)
        }
    }

    private fun endWNumberData() {
        if (isWNumberDataStarted) {
            endWpElement()
            wordTagLog("</${Tag.TAG_NUMBERING_DATA}>")
            handler.endElement("", "", Tag.TAG_NUMBERING_DATA)
            isWNumberDataStarted = false
        }
    }

    private fun generateNestingList(listCh: String, lvlValue: String) {
        attr.clear()
        ElementUtils.addAttr(attr, hashMapOf(Attr.ATTR_COMMON_VALUE to lvlValue))
        handler.startElement("", "", Tag.TAG_LIST_LVL, attr)
        attr.clear()
        ElementUtils.createElementAttr(
            handler,
            attr,
            ATTR_NUM_FMT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.BULLET)
        )
        ElementUtils.createElementAttr(
            handler,
            attr,
            LVL_TEXT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to listCh)
        )
        handler.endElement("", "", Tag.TAG_NUMBERING_LIST)
    }

    private fun createWNumberStyleElement(
        type: String,
        attrs: Attributes,
        node: Node? = null,
        olNum: String? = null
    ) {
        attr.clear()
        wordTagLog("<${Tag.TAG_NUMBERING_STYLE}>")
        handler.startElement("", "", Tag.TAG_NUMBERING_STYLE, attr)
        wordTagLog("<${Tag.TAG_NUMBERING_TYPE} ${mapToString(hashMapOf(Attr.ATTR_COMMON_VALUE to type))}/>")
        ElementUtils.createElementAttr(
            handler,
            attr,
            Tag.TAG_NUMBERING_TYPE,
            hashMapOf(Attr.ATTR_COMMON_VALUE to type)
        )
        val classValue = attrs["class"]
        var checkIndentClasses = 0
        node?.let { checkIndentClasses = checkIndentClasses(it.toString()) }
        if (checkIndentClasses > 0 && classValue.isNullOrEmpty() && classValue != DEFAULT_STYLE_LINE && !isOLProcessing) {
            /*当且仅当attrs["class"]的值为空时才有可能是嵌套列表
            Nesting Level 0*/
            generateNestingList(SaxManager.BLANK_DOT_CH, DEFAULT_VAL)
            //Nesting Level 1
            generateNestingList(SaxManager.CIRCLE_CH, DEFAULT_VAL_ONE)
            if (checkIndentClasses >= 2) {
                //Nesting Level 2
                generateNestingList(SaxManager.DIAMOND_CH, DEFAULT_VAL_TWO)
            }
        } else {
            createPureList(type, attrs, olNum)
        }
        wordTagLog("</${Tag.TAG_NUMBERING_STYLE}>")
        handler.endElement("", "", Tag.TAG_NUMBERING_STYLE)
    }

    private fun checkIndentClasses(html: String): Int {
        val doc: Document = Jsoup.parse(html)
        val liElements: Elements = doc.select("li") // 选择所有的li元素
        var maxIndent = 0
        for (li in liElements) {
            val classes = li.className().split(" ") // 获取class属性并分割为列表
            for (clazz in classes) {
                if (clazz.startsWith("ql-indent-")) {
                    val number = clazz.substringAfter("ql-indent-").toIntOrNull() // 提取数字并尝试转换为Int
                    number?.let { if (it > maxIndent) maxIndent = it } // 如果数字存在且大于当前最大值，则更新最大值
                }
            }
        }

        return if (maxIndent > 0) maxIndent else 0
    }

    fun containsStartAttribute(xmlString: String): Boolean {
        val pattern = "<ol[^>]*start=\"[0-9]+\"[^>]*>".toRegex()
        return pattern.containsMatchIn(xmlString)
    }

    @Suppress("LongMethod")
    private fun createPureList(type: String, attrs: Attributes, olNum: String? = null) {
        val classValue = attrs["class"]
        attr.clear()
        ElementUtils.addAttr(attr, hashMapOf(Attr.ATTR_COMMON_VALUE to DEFAULT_VAL))
        handler.startElement("", "", Tag.TAG_LIST_LVL, attr)
        if (classValue.equals(ATTR_HX)) {
            ElementUtils.createElementAttr(
                handler,
                attr,
                ATTR_NUM_FMT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.BULLET)
            )
            ElementUtils.createElementAttr(
                handler,
                attr,
                LVL_TEXT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.BULLET_HX)
            )
        } else if (type == HtmlAttributes.Value.CHECKBOX) {
            val isChecked = attrs.hasKey(HtmlAttributes.CHECKED)
            ElementUtils.createElementAttr(
                handler,
                attr,
                ATTR_NUM_FMT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.BULLET)
            )
            if (isChecked) {
                ElementUtils.createElementAttr(
                    handler,
                    attr,
                    LVL_TEXT,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.CHECKED_CH)
                )
            } else {
                ElementUtils.createElementAttr(
                    handler,
                    attr,
                    LVL_TEXT,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to SaxManager.UNCHECK_CH)
                )
            }
        } else {
            attr.clear()
            if (type == DECIMAL && olNum != null) {
                ElementUtils.createElementAttr(
                    handler,
                    attr,
                    ATTR_NUM_OL_START,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to olNum)
                )
            }
            attr.clear()
            ElementUtils.createElementAttr(
                handler,
                attr,
                ATTR_NUM_FMT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to type)
            )
        }
        handler.endElement("", "", Tag.TAG_NUMBERING_LIST)
    }

    private fun createWNumFormat(type: String) {
        attr.clear()
        wordTagLog("<${Tag.TAG_NUMBERING_FORMAT} ${mapToString(hashMapOf(Attr.ATTR_COMMON_VALUE to type))}/>")
        ElementUtils.createElementAttr(
            handler,
            attr,
            Tag.TAG_NUMBERING_FORMAT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to type)
        )
    }

    protected fun startAlignment(alignment: Layout.Alignment) {
        if (!isAlignmentProcessing) {
            isAlignmentProcessing = true
            val alignValue = ElementUtils.getAlignment(alignment)
            wordTagLog("<${Tag.TAG_PARAGRAPH_ALIGNMENT} ${mapToString(hashMapOf(Attr.ATTR_COMMON_VALUE to alignValue))}/>")
            attr.clear()
            ElementUtils.createElementAttr(
                handler,
                attr,
                Tag.TAG_PARAGRAPH_ALIGNMENT,
                hashMapOf(Attr.ATTR_COMMON_VALUE to alignValue)
            )
        }
    }

    protected fun endAlignment() {
        if (isAlignmentProcessing) {
            endWpElement()
            isAlignmentProcessing = false
        }
    }

    protected fun endH() {
        if (isHProcessing) {
            endWpElement()
            isHProcessing = false
        }
    }

    private fun handleRawTextSpan(attrs: Attributes, node: Element) {
        val classValue = attrs.get(HtmlAttributes.CLASS)
        if (classValue.isNotEmpty()) {
            if (classValue.contains("text-weight-bold")) { //加粗
                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_BOLD,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE)
                )
            }
            if (classValue.contains("text-italic")) { //斜体
                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_ITALIC,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE)
                )
            }

            if (classValue.contains("text-decoration-underline")) {
                val styleValue = attrs.get(HtmlAttributes.STYLE)
                var underlineStyle = Value.SINGLE
                var underlineColor = DEFAULT_UNDERLINE_COLOR
                styleValue.split(";").forEach { style ->
                    val parts = style.trim().split(":", limit = 2)
                    if (parts.size == 2) {
                        val propertyName = parts[0].trim()
                        val propertyValue = parts[1].trim()
                        when (propertyName) {
                            "text-decoration-style" -> {
                                underlineStyle = when (propertyValue) {
                                    "wavy" -> Value.WAVE
                                    else -> Value.SINGLE
                                }
                            }
                            "text-decoration-color" -> underlineColor = rawTextcalColor(propertyValue)
                        }
                    }
                }

                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_UNDERLINE,
                    hashMapOf(
                        Attr.ATTR_TEXT_UNDERLINE_COLOR to underlineColor,
                        Attr.ATTR_COMMON_VALUE to underlineStyle
                    )
                )
            }

            if (classValue.contains("text-line-through")) { //删除线
                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_S,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE)
                )
            }

            /*新代码背景高亮是以color_highlighted开头+颜色，后续超感便签开发完成后需要再次适配
            旧代码背景高亮是固定 text-highlight-active*/
            if (classValue.contains("text-highlight-active")) {
                val styleValue = attrs.get(HtmlAttributes.STYLE)
                styleValue.split(";").forEach { style ->
                    val parts = style.trim().split(":", limit = 2)
                    if (parts.size == 2) {
                        val propertyName = parts[0].trim()
                        val propertyValue = parts[1].trim()
                        when (propertyName) {
                            "background-color" -> {
                                val colorType = when (propertyValue) {
                                    RAWTEXT_YELLOW_COLOR -> DEFAULT_HIGHLIGHT_COLOR
                                    RAWTEXT_RED_COLOR -> RED_HIGHLIGHT_COLOR
                                    RAWTEXT_BLUE_COLOR -> BLUE_HIGHLIGHT_COLOR
                                    RAWTEXT_GREEN_COLOR -> GREEN_HIGHLIGHT_COLOR
                                    else -> DEFAULT_HIGHLIGHT_COLOR
                                }
                                handleHeadTextStyle(
                                    Tag.TAG_TEXT_STYLE_HIGHLIGHT,
                                    hashMapOf(Attr.ATTR_COMMON_VALUE to colorType)
                                )
                            }
                        }
                    }
                }

            }

            //字体颜色
            extractColorValues(classValue)?.let { colorStr ->
                val styleColor = rawTextcalColor(colorStr)
                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_COLOR,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to styleColor)
                )
            }

            //字体大小
            extractTextSizeValue(classValue)?.let { fontSize ->
                handleHeadTextStyle(
                    Tag.TAG_TEXT_STYLE_SIZE,
                    hashMapOf(Attr.ATTR_COMMON_VALUE to (fontSize * DEFAULT_TEXT_SIZE).toString())
                )
            }
        }
    }

    private fun rawTextcalColor(colorStr: String): String {
        return when (colorStr) {
            //灰色的特殊处理，亮暗色为同一个颜色
            RAWTEXT_GRAY_COLOR -> DEFAULT_GRAY
            RAWTEXT_RED_COLOR -> {
                getColorCode(
                    context,
                    com.oplus.richtext.core.R.color.pick_color_red
                )
            }

            RAWTEXT_ORANGE_COLOR -> {
                getColorCode(
                    context,
                    com.oplus.richtext.core.R.color.pick_color_orange
                )
            }

            RAWTEXT_YELLOW_COLOR -> {
                getColorCode(
                    context,
                    com.oplus.richtext.core.R.color.pick_color_yellow
                )
            }

            RAWTEXT_GREEN_COLOR -> {
                getColorCode(
                    context,
                    com.oplus.richtext.core.R.color.pick_color_green
                )
            }

            RAWTEXT_BLUE_COLOR -> {
                getColorCode(
                    context,
                    com.oplus.richtext.core.R.color.pick_color_blue
                )
            }

            else -> getColorCode(context, com.oplus.richtext.core.R.color.pick_color_default)
        }
    }

    private fun getColorCode(context: Context, colorInput: Int): String {
        val colorInt = ContextCompat.getColor(context, colorInput)
        return String.format("%06X", colorInt and DEFAULT_COLOR_F)
    }

    private fun handleRawTextTailSpan(attrs: Attributes) {
        val classValue = attrs.get(HtmlAttributes.CLASS)
        if (classValue.isNotEmpty()) {
            if (classValue.contains("text-weight-bold")) { //清除加粗
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_BOLD)
            }
            if (classValue.contains("text-italic")) { //清除斜体
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_ITALIC)
            }
            if (classValue.contains("text-decoration-underline")) { //清除下划线
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_UNDERLINE)
            }
            if (classValue.contains("text-line-through")) { //清除删除线
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_S)
            }

            if (classValue.contains("color_highlighted")
                || classValue.contains("text-highlight-active")) {  //清除背景高亮
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_HIGHLIGHT)
            }

            //清除字体颜色
            extractColorValues(classValue)?.let {
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_COLOR)
            }
            //清除字号
            extractTextSizeValue(classValue)?.let {
                handleTailTextStyle(Tag.TAG_TEXT_STYLE_SIZE)
            }
        }
    }

    private fun getHLevel(tag: String): String {
        val resLevel = when (tag) {
            H1 -> TITLE_LEVEL_2
            H2 -> TITLE_LEVEL_3
            H3 -> TITLE_LEVEL_4
            H4 -> TITLE_LEVEL_5
            else -> "1"
        }
        return resLevel
    }

    private fun handleHeadDiv(attrs: Attributes, node: Element) {
        /*
        * 1、li嵌套div时，缩进属性在li上处理
        * 2、li嵌套div时，H标题在li上处理
        * 3、li嵌套div时，没有对齐属性
        * 直接return
        * */
        node.parent()?.takeIf { it.tagName() == HtmlTags.LI }?.let { return }

        val classValue = attrs.get(HtmlAttributes.CLASS)
        //hr标签和div融合在一起了 ，如果是hr标签 则走原hr逻辑
        if (classValue.contains("hr-style-")) { //hr
            handleHeadHR()
            //不再执行div逻辑
            return
        }

        // 开始新的 div 标签，需要结束上一个 w:p 标签
        endWpElement()
        startWpPrElement()

        getTextAlign(classValue)?.let { alignment ->   //对齐
            startAlignment(alignment)
        }

        if (rawTextIsValidHeader(classValue)) { //标题
            extractHTags(classValue)?.let {  tag ->
                createDivHead(getHLevel(tag))
            }
        }

        if (rawTextIsValidInd(classValue)) { //缩进
            createLeftGroup(classValue)
        }
        if (classValue == QUOTE) {
            handleHeadTextStyle(
                Tag.TAG_TEXT_STYLE_ITALIC,
                hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE)
            )
            handleHeadTextStyle(
                Tag.TAG_TEXT_STYLE_COLOR,
                hashMapOf(Attr.ATTR_COMMON_VALUE to DEFAULT_GRAY)
            )
            isQuoteProcessing = true
        }
        endWpPrElement()
    }

    private fun handleTailDiv(node: Node) {
        endWHyperLinkElement()
        endCheckboxGroup()
        endAlignment()
        endH()
        // div 标签结束，如果还有 w:p 标签未关闭，需要关闭
        endWpElement()
        handleQuote(node)
    }

    private fun handleQuote(node: Node) {
        val attrs = node.attributes()
        val classValue = attrs.get(HtmlAttributes.CLASS)
        if (classValue == QUOTE) {
            handleTailTextStyle(Tag.TAG_TEXT_STYLE_ITALIC)
            handleTailTextStyle(Tag.TAG_TEXT_STYLE_COLOR)
            isQuoteProcessing = false
        }
    }

    private fun startCheckboxGroup(attrs: Attributes) {
        if (!isCheckBoxGroupProcessing) {
            isCheckBoxGroupProcessing = true
            startWnsElement()
            createWNumberStyleElement(SaxManager.UNCHECK, attrs)
        }
    }

    private fun createLeftGroup(classValue: String) {
        attr.clear()
        ElementUtils.createElementAttr(
            handler,
            attr,
            Tag.ATTR_IND,
            hashMapOf(Attr.ATTR_INDENTATION_VALUE to rawTextCalIndentationValue(classValue))
        )
    }

    private fun rawTextCalIndentationValue(indentationStr: String): String {
        val levels = setOf(
            LEVEL_ONE, LEVEL_TWO, LEVEL_THREE, LEVEL_FOUR,
            LEVEL_FIVE, LEVEL_SIX, LEVEL_SEVEN, LEVEL_EIGHT
        ).map { it.lowercase(Locale.ROOT) }

        val lowerLevelStr = indentationStr.lowercase(Locale.ROOT)

        val classValue = levels.firstOrNull { level ->
            lowerLevelStr.contains(level)
        }

        return when (classValue) {
            LEVEL_ONE -> LEVEL_ONE_VALUE
            LEVEL_TWO -> LEVEL_TWO_VALUE
            LEVEL_THREE -> LEVEL_THREE_VALUE
            LEVEL_FOUR -> LEVEL_FOUR_VALUE
            LEVEL_FIVE -> LEVEL_FIVE_VALUE
            LEVEL_SIX -> LEVEL_SIX_VALUE
            LEVEL_SEVEN -> LEVEL_SEVEN_VALUE
            LEVEL_EIGHT -> LEVEL_EIGHT_VALUE
            else -> "0"
        }
    }

    private fun calIndentationValue(indentationStr: String): String {
        return when (indentationStr) {
            LEVEL_ONE -> LEVEL_ONE_VALUE
            LEVEL_TWO -> LEVEL_TWO_VALUE
            LEVEL_THREE -> LEVEL_THREE_VALUE
            LEVEL_FOUR -> LEVEL_FOUR_VALUE
            LEVEL_FIVE -> LEVEL_FIVE_VALUE
            LEVEL_SIX -> LEVEL_SIX_VALUE
            LEVEL_SEVEN -> LEVEL_SEVEN_VALUE
            LEVEL_EIGHT -> LEVEL_EIGHT_VALUE
            else -> "0"
        }
    }

    private fun rawTextEndCheckboxGroup() {
        if (isCheckBoxGroupProcessing) {
            isCheckBoxGroupProcessing = false
        }
    }

    private fun endCheckboxGroup() {
        if (isCheckBoxGroupProcessing) {
            endWnsElement()
            isCheckBoxGroupProcessing = false
        }
    }

    private fun rawTextHandleHeadInput(isUnChecked: Boolean, hLevel: String?, node: Node) {
        if (isCheckBoxGroupProcessing) {
            isCheckBoxItemProcessing = true
            if (!isUnChecked) {
                handleHeadLI(SaxManager.CHECK_FINISH, DEFAULT_LEVEL, hLevel, node)
            } else {
                handleHeadLI(SaxManager.UNCHECK, DEFAULT_LEVEL, hLevel, node)
            }
        }
    }

    private fun handleTailLabel() {
        if (isCheckBoxTextProcessing) {
            isCheckBoxTextProcessing = false
        }
        if (isCheckBoxItemProcessing) {
            isCheckBoxItemProcessing = false
        }
    }

    fun extractStartNumber(xmlString: String): Int? {
        val pattern = "<ol[^>]*start=\"([0-9]+)\"[^>]*>".toRegex()
        val matchResult = pattern.find(xmlString)
        return matchResult?.groups?.get(1)?.value?.toInt()
    }

    private fun handleHeadOL(node: Node) {
        val nodeParent = node.parent() as Element
        if (nodeParent.tagName() == TD) {
            return
        }
        if (!isOLProcessing) {
            isOLProcessing = true
            startWnsElement()
            var agrNum: String? = null
            if (containsStartAttribute(node.toString())) {
                agrNum = extractStartNumber(node.toString()).toString()
            }
            createWNumberStyleElement(DECIMAL, node.attributes(), node, agrNum)
        }
    }

    private fun handleTailOL() {
        if (isOLProcessing) {
            isOLProcessing = false
            endWnsElement()
        }
    }

    private fun handleHeadUL(attrs: Attributes, node: Node? = null) {
        if (!isULProcessing) {
            isULProcessing = true
            startWnsElement()
            createWNumberStyleElement(SaxManager.BULLET, attrs, node)
        }
    }

    private fun handleTailUL() {
        if (isULProcessing) {
            isULProcessing = false
            endWnsElement()
        }
    }

    private fun handleHeadLI(
        type: String,
        nestingLevel: String = DEFAULT_LEVEL,
        hLevel: String? = null,
        node: Node? = null
    ) {
        if (isULProcessing || isOLProcessing || isCheckBoxGroupProcessing) {
            val classValue = node?.attributes()?.get(HtmlAttributes.CLASS)
            classValue?.let {
                if (classValue.contains(CHECKED) && !isCheckBoxGroupProcessing) { //待办
                    startCheckboxGroup(node.attributes())
                    rawTextHandleHeadInput(classValue.contains(UNCHECKED), hLevel, node)
                    return
                }
            }

            isListItemProcessing = true
            startWNumberData(nestingLevel)
            createWNumFormat(type)

            if (rawTextIsValidInd(classValue) || hLevel != null) {
                //兼容列表中存在缩进和列表中存在标题的情况
                startWpPrElement()
                hLevel?.let {
                    attr.clear()
                    ElementUtils.createElementAttr(
                        handler,
                        attr,
                        TAG_TITLE_STYLE,
                        hashMapOf(Attr.ATTR_COMMON_VALUE to hLevel)
                    )
                }
                node?.let {
                    classValue?.let {
                        if (rawTextIsValidInd(classValue)) {  //缩进
                            createLeftGroup(classValue)
                        }
                    }
                }
                endWpPrElement()
            }
        }
    }

    private fun handleTailLI() {
        handleTailLabel()
        rawTextEndCheckboxGroup()
        if (isListItemProcessing) {
            addEmptyTextToListItemIfNeed()
            isListItemProcessing = false
            hasTextInListItem = false
            endWNumberData()
        }
    }

    private fun addEmptyTextToListItemIfNeed() {
        if (!hasTextInListItem) {
            // 存在 list item 标签但是没有文字，此时需要插入一个空字符
            handleHeadText("")
            handleTailText("")
        }
    }

    private fun getTextAlign(style: String): Layout.Alignment? {
        extractRawTextAlign(style)?.let {   alignment ->
            if (alignment.contains(CssAttributes.Value.START, true)) {
                return Layout.Alignment.ALIGN_NORMAL
            } else if (alignment.contains(CssAttributes.Value.CENTER, true)) {
                return Layout.Alignment.ALIGN_CENTER
            } else if (alignment.contains(CssAttributes.Value.END, true)) {
                return Layout.Alignment.ALIGN_OPPOSITE
            }
        }
        return null
    }

    protected fun handleHeadTextStyle(tagName: String, tagAttrs: HashMap<String, String>? = null) {
        currentTextStyle[tagName] = tagAttrs
    }

    private fun handleTailTextStyle(tagName: String) {
        currentTextStyle.remove(tagName)
    }

    private fun addTextStyleElementIfExist(): Boolean {
        var addWrPrElement = false
        currentTextStyle.forEach {
            startWrPrElement()
            addWrPrElement = true
            wordTagLog("<${it.key}${mapToString(it.value)}/>")
            ElementUtils.createElementAttr(
                handler,
                attr,
                it.key,
                it.value
            )
        }
        if (addWrPrElement) {
            endWrPrElement()
        }
        return addWrPrElement
    }

    private fun handleHeadImg(attrs: Attributes) {
        val src = attrs.get(HtmlAttributes.SRC)
        if (src.isNotEmpty()) {
            val combinedCardData = wvExtraNoteData.combinedCardData[src]
            if (!combinedCardData.isNullOrEmpty()) {
                // 当img为联系人和日程卡时，导出doc时需要将卡片中的文本数据添加到doc中。
                handleHeadText(combinedCardData)
                handleTailText(combinedCardData)
                return
            }
            /*
            captureData[src]
	        返回null：说明该节点的不需要截图保存，该img为正常图片附件；
            返回""：说明该节点需要截图导出，但是导出异常，因此该节点不需要导出到doc；
            返回有效字符串：说明该节点需要截图保存，且保存成功，直接使用captureData[src]返回的路径导出
             */
            val path = wvExtraNoteData.captureData[src]
                ?: "${wvExtraNoteData.attachmentRootPath}/$noteId/${src}_thumb.png"
            if (path.isEmpty()) {
                AppLogger.BASIC.w(TAG, "handleHeadImg, empty path for:$src")
                return
            }
            attr.clear()
            if (ElementUtils.matchClasses(attrs.get(HtmlAttributes.CLASS), listOf(CLASS_HALF))) {
                createHalfImageElement(
                    context,
                    handler,
                    attr,
                    path,
                    authority,
                    attrs
                )
            } else {
                endWpElement()
                startWpElement()
                ElementUtils.createImageElement(context, handler, attr, path, authority, 0)
                endWpElement()
            }
        }
    }

    /**
     * create HalfImage element 创建小图 以及平行小图
     */
    private fun createHalfImageElement(
        context: Context,
        handler: TransformerHandler,
        attr: AttributesImpl,
        path: String,
        authority: String,
        attrs: Attributes
    ) {
        attr.clear()
        val imageUri = ElementUtils.getImageUri(context, path, authority)
        val originalWidth = attrs.get(HtmlAttributes.WIDTH).toFloat()
        val originalHeight = attrs.get(HtmlAttributes.HEIGHT).toFloat()
        val targetWidth = Attr.WORD_MAX_WIDTH / 2
        val scaleFactor = targetWidth / originalWidth
        val targetHeight = (originalHeight * scaleFactor).toInt()
        val classAttr = attrs.get(HtmlAttributes.CLASS)
        when {
            ElementUtils.matchClasses(classAttr, listOf(CLASS_HALF, CLASS_HALF_START)) -> {
                endWpElement()
                startWpElement()
                attr.clear()
                ElementUtils.createParagraph(handler, attr)
                ElementUtils.createHalfMediaAttributes(
                    imageUri,
                    targetWidth.toString(),
                    targetHeight.toString(),
                    handler,
                    attr
                )
            }

            ElementUtils.matchClasses(classAttr, listOf(CLASS_HALF, CLASS_HALF_END)) -> {
                handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
                attr.clear()
                handler.startElement("", "", Tag.TAG_TEXT_CONTENT, attr)
                val text = "\ufeff   "
                handler.characters(text.toCharArray(), 0, text.length)
                handler.endElement("", "", Tag.TAG_TEXT_CONTENT)
                handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
                ElementUtils.createHalfMediaAttributes(
                    imageUri,
                    targetWidth.toString(),
                    targetHeight.toString(),
                    handler,
                    attr
                )
                endWpElement()
            }

            else -> {
                endWpElement()
                startWpElement()
                attr.clear()
                ElementUtils.createParagraph(handler, attr)
                ElementUtils.createHalfMediaAttributes(
                    imageUri,
                    targetWidth.toString(),
                    targetHeight.toString(),
                    handler,
                    attr
                )
                endWpElement()
            }
        }
    }

    private fun incCachedBr() {
        if (!isDivProcessing && !isOLProcessing && !isULProcessing) {
            cachedBrCount++
        }
    }

    private fun decCachedBr() {
        if (cachedBrCount > 0) {
            cachedBrCount--
        }
    }

    private fun addCachedBrText() {
        addBrText(cachedBrCount)
        cachedBrCount = 0
    }

    private fun addBrText(brCount: Int) {
        if (brCount > 0) {
            val brText = StringBuilder()
            // 换行符前后各添加一个不可见占位符，避免被识别成空字符而忽略不添加到word中
            brText.append("\ufeff")
            for (i in 0 until brCount) {
                brText.append("\n")
            }
            val brString = brText.toString()
            startWrElement()
            wordTagLog("<${Tag.TAG_TEXT_CONTENT}>$brString</${Tag.TAG_TEXT_CONTENT}>")
            ElementUtils.createElementValue(handler, attr, Tag.TAG_TEXT_CONTENT, brString)
            // 字符插入完成后，需要结束 w:r 标签
            endWrElement()
        }
    }

    private fun isParagraphTag(tagName: String): Boolean {
        return tagName == HtmlTags.DIV || tagName == HtmlTags.OL || tagName == HtmlTags.UL
    }

    private inline fun wordTagLog(msg: String) {
        if (DEBUG_WORD_TAG) {
            AppLogger.BASIC.d(TAG + "_WORD_TAG", msg)
        }
    }

    private inline fun mapToString(map: HashMap<String, String>?): String {
        val stringBuilder = StringBuilder()
        map?.forEach {
            stringBuilder.append(" ${it.key}=${it.value}")
        }
        return stringBuilder.toString()
    }

    private fun dumpNoCloseTag() {
        if (isWrElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:r is missing!")
        }
        if (isWrPrElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:rPr is missing!")
        }
        if (isWpPrElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:pPr is missing!")
        }
        if (isWPElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:p is missing!")
        }
        if (isWHyperLinkElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:hyperlink is missing!")
        }
        if (isWnsElementStarted) {
            AppLogger.BASIC.w(TAG, "close w:ns is missing!")
        }
        if (isWNumberDataStarted) {
            AppLogger.BASIC.w(TAG, "close w:numberData is missing!")
        }
        if (isDivProcessing) {
            AppLogger.BASIC.w(TAG, "close isDivProcessing is missing!")
        }
        if (isAlignmentProcessing) {
            AppLogger.BASIC.w(TAG, "close isAlignmentProcessing is missing!")
        }
        if (isCheckBoxItemProcessing) {
            AppLogger.BASIC.w(TAG, "close isCheckBoxItemProcessing is missing!")
        }
        if (isCheckBoxGroupProcessing) {
            AppLogger.BASIC.w(TAG, "close isCheckBoxGroupProcessing is missing!")
        }
        if (isHyperTextProcessing) {
            AppLogger.BASIC.w(TAG, "close isHyperTextProcessing is missing!")
        }
        if (isOLProcessing) {
            AppLogger.BASIC.w(TAG, "close isOLProcessing is missing!")
        }
        if (isULProcessing) {
            AppLogger.BASIC.w(TAG, "close isULProcessing is missing!")
        }
        if (isListItemProcessing) {
            AppLogger.BASIC.w(TAG, "close isListItemProcessing is missing!")
        }
        if (isCheckBoxTextProcessing) {
            AppLogger.BASIC.w(TAG, "close isCheckBoxTextProcessing is missing!")
        }
    }

    /**
     * 使用正则提取标题属性 h1-5
     */
    fun extractHTags(input: String): String? {
        return Regex("h[1-5]")
            .findAll(input)
            .firstOrNull()
            ?.value
            ?: null
    }

    /**
     * 使用正则提取对齐属性
     */
    fun extractRawTextAlign(input: String): String? {
        return Regex("align-(?:center|end|start)").findAll(input)
            .firstOrNull()
            ?.value
            ?: null
    }

    /**
     * 正文标题
     */
    fun createDivHead(hLevel: String) {
        if (!isHProcessing) {
            isHProcessing = true
        }
        attr.clear()
        ElementUtils.createElementAttr(
            handler,
            attr,
            TAG_TITLE_STYLE,
            hashMapOf(Attr.ATTR_COMMON_VALUE to hLevel)
        )
    }

    /**
     * 使用正则提取数值
     */
    fun extractTextSizeValue(input: String): Float? {
        val regex = Regex("text-size-(\\d+\\.?\\d*)")  // 匹配数字（含小数）
        return regex.find(input)?.groupValues?.get(1)?.toFloatOrNull()  // 返回捕获组1（括号内的部分）
    }

    /**
     * 使用正则提取颜色值
     */
    fun extractColorValues(input: String): String? {
        return Regex("color_(gray|red|orange|yellow|green|blue)").findAll(input)
            .firstOrNull()
            ?.value
            ?: null
    }
}