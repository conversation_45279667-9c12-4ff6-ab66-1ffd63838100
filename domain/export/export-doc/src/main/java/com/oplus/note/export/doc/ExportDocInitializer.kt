/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportDocInitializer.kt
 ** Description:
 **  doc export initializer
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.content.Context
import androidx.startup.Initializer
import com.nearme.note.util.AppLaunchThreadManger
import com.oplus.note.export.ExportAgentFactory
import com.oplus.note.export.pdf.ExportPdfAgentImpl
import com.oplus.note.logger.AppLogger

class ExportDocInitializer : Initializer<Boolean> {
    companion object {
        private const val TAG = "Notes.ExportInitializer"
    }

    override fun create(context: Context): Boolean {
        AppLaunchThreadManger.execute {
            AppLogger.BASIC.d(TAG, "register Export agent")
            ExportAgentFactory.registerDocAgent(ExportDocAgentImpl(context.applicationContext))
            ExportAgentFactory.registerPdfAgent(ExportPdfAgentImpl(context.applicationContext))
        }
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}