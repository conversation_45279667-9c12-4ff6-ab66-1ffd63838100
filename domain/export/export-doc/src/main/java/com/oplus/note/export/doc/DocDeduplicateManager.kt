/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - DocDuplicateManager.kt
 ** Description:
 **  doc duplicate file manager
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.oplus.note.logger.AppLogger
import java.io.File
import java.util.concurrent.CopyOnWriteArraySet
import kotlin.math.max

/***
 * The doc export SharedPreference:
 * key: the note's localId
 * value: doc file path, the note's updateTime, doc file's lastModified, split by comma
 * for example(path,updateTime,lastModified)
 */
internal object DocDeduplicateManager {
    const val DISABLED = true
    const val TAG = "DocDuplicateManager"
    private const val SPLIT_CHARACTER = ","
    private val changedNotes = CopyOnWriteArraySet<String>()

    inline fun checkNoteInfoChanged(
        context: Context,
        localId: String,
        updateTime: Long,
        block: (String) -> Unit
    ) {
        if (DISABLED) {
            return
        }
        val sp = DocUtils.getExportSp(context)
        AppLogger.BASIC.d(TAG, "checkNoteInfoChanged localId: $localId, updateTime: $updateTime")
        sp.getNoteInfo(localId) { path, lastUpdateTime, lastModified ->
            val lastPathModified = File(path).lastModified()
            AppLogger.BASIC.d(
                TAG, "path: $path, lastUpdateTime: $lastUpdateTime, " +
                        "lastModified: $lastModified, lastPathModified: $lastPathModified"
            )
            val isNotChanged = (updateTime == lastUpdateTime) && (lastModified == lastPathModified)
            if (isNotChanged) {
                block(path)
            }
        }
    }

    private inline fun SharedPreferences.getNoteInfo(localId: String, block: (String, Long, Long) -> Unit) {
        if (contains(localId)) {
            getString(localId, "")?.let {
                val value = it.split(SPLIT_CHARACTER)
                val path = runCatching { value.component1() }.getOrDefault("")
                val lastUpdateTime = runCatching { value.component2().toLong() }.getOrDefault(0L)
                val lastModified = runCatching { value.component3().toLong() }.getOrDefault(0L)
                block(path, lastUpdateTime, lastModified)
            }
        }
    }

    fun addChangedNote(localId: String) {
        if (DISABLED) {
            return
        }
        changedNotes.add(localId)
    }

    fun saveNoteInfo(context: Context, localId: String, updateTime: Long, path: String) {
        if (DISABLED) {
            return
        }
        val sp = DocUtils.getExportSp(context)
        val lastPathModified = File(path).lastModified()
        sp.getNoteInfo(localId) { _, lastUpdateTime, _ ->
            /**
             * 可能doc导出过程中，笔记已经保存，缓存的lastUpdateTime已经更新
             * 笔记的更新时间和缓存的更新时间，取最大值
             */
            sp.commit(localId, path, max(updateTime, lastUpdateTime), lastPathModified)
        }
    }

    private fun SharedPreferences.commit(
        localId: String,
        path: String,
        updateTime: Long,
        lastModified: Long
    ) {
        edit {
            putString(localId, "$path$SPLIT_CHARACTER$updateTime$SPLIT_CHARACTER$lastModified")
        }
    }

    fun updateNoteInfoIfNeed(context: Context, localId: String, updateTime: Long) {
        if (DISABLED) {
            return
        }
        AppLogger.BASIC.d(TAG, "updateNoteInfoIfNeed: $localId, updateTime: $updateTime")
        if (changedNotes.contains(localId)) {
            val sp = DocUtils.getExportSp(context)
            sp.getNoteInfo(localId) { path, _, lastModified ->
                AppLogger.BASIC.d(TAG, "path: $path, lastModified: $lastModified")
                sp.commit(localId, path, updateTime, lastModified)
            }
            changedNotes.remove(localId)
        }
    }
}