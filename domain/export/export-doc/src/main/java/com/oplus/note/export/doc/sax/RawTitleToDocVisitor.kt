/**
 * Copyright (C), 2010-2030, OPLUS Mobile Comm Corp., Ltd.
 * File           : RawTitle2DocVisitor.kt
 * Description    : description
 * Version        : 1.0
 * Date           : 2024/2/8
 * Author         : XinYang.Hu
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * XinYang.Hu     2024/2/8        1.0           create
 */
package com.oplus.note.export.doc.sax

import android.content.Context
import android.text.Layout
import com.oplus.note.export.doc.WVExtraNoteData
import com.oplus.richtext.core.html.HtmlAttributes
import com.oplus.richtext.core.html.HtmlTags
import org.jsoup.nodes.Element
import org.jsoup.nodes.Node
import org.jsoup.nodes.TextNode
import org.xml.sax.helpers.AttributesImpl
import javax.xml.transform.sax.TransformerHandler

class RawTitleToDocVisitor(
    override val context: Context,
    override val authority: String,
    override val handler: TransformerHand<PERSON>,
    override val attr: AttributesImpl,
    override val noteId: String,
    override val wvExtraNoteData: WVExtraNoteData
) : RawTextToDocVisitor(context, authority, handler, attr, noteId, wvExtraNoteData) {
    companion object {
        private const val TAG = "RawTitleToDocVisitor"
    }

    override fun head(node: Node, depth: Int) {
        if (node is TextNode) {
            handleHeadText(node.wholeText)
        } else if (node is Element) {
            handleHeadElement(node)
        }
    }

    override fun tail(node: Node, depth: Int) {
        if (node is Element) {
            handleTailElement(node)
        } else if (node is TextNode) {
            handleTailText(node.wholeText)
        }
    }

    private fun handleHeadElement(element: Element) {
        when (element.tagName()) {
            HtmlTags.DIV -> {
                // 开始新的 div 标签，需要结束上一个 w:p 标签
                endWpElement()
                val clsString = element.attributes().get(HtmlAttributes.CLASS)
                getTitleTextAlignFromClass(clsString)?.let { alignment ->
                    startAlignment(alignment)
                }
                attr.clear()
                handleHeadTextStyle(Tag.TAG_TEXT_STYLE_SIZE, hashMapOf(Attr.ATTR_COMMON_VALUE to Text.TEXT_TITLE_SIZE))
                handleHeadTextStyle(Tag.TAG_TEXT_STYLE_BOLD, hashMapOf(Attr.ATTR_COMMON_VALUE to Value.TRUE))
            }
        }
    }

    private fun handleTailElement(element: Element) {
        when (element.tagName()) {
            HtmlTags.DIV -> {
                endAlignment()
                endWpElement()
            }
        }
    }


    private fun getTitleTextAlignFromClass(classValue: String): Layout.Alignment? {
        return when (classValue) {
            "align-center" -> Layout.Alignment.ALIGN_CENTER
            "align-end" -> Layout.Alignment.ALIGN_OPPOSITE
            "align-start" -> Layout.Alignment.ALIGN_NORMAL
            else -> null
        }
    }
}