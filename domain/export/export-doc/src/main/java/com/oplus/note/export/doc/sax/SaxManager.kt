/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - SaxManager.kt
 ** Description:
 **  Sax create xml
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc.sax

import android.content.Context
import com.oplus.note.export.doc.DocUtils
import com.oplus.note.export.doc.ExportNoteData
import com.oplus.note.logger.AppLogger
import org.jsoup.Jsoup
import org.jsoup.select.NodeTraversor
import org.xml.sax.helpers.AttributesImpl
import java.io.File
import java.io.FileOutputStream
import javax.xml.transform.OutputKeys
import javax.xml.transform.TransformerConfigurationException
import javax.xml.transform.TransformerException
import javax.xml.transform.TransformerFactoryConfigurationError
import javax.xml.transform.sax.SAXTransformerFactory
import javax.xml.transform.sax.TransformerHandler
import javax.xml.transform.stream.StreamResult

object SaxManager {
    private const val XMLNS_W = "http://schemas.openxmlformats.org/wordprocessingml/2006/main"
    private const val XMLNS_R =
        "http://schemas.openxmlformats.org/officeDocument/2006/relationships"
    internal const val UNCHECK = "uncheck"
    internal const val CHECK_FINISH = "checkfinish"
    internal const val BULLET = "bullet"
    internal const val DECIMAL = "decimal"

    internal const val BULLET_HX = "\uFFDA"
    internal const val CHECKED_CH = "\u2611"
    internal const val UNCHECK_CH = "\u25A1"

    internal const val BLANK_DOT_CH = "\uF06C"
    internal const val CIRCLE_CH = "\uF06E"
    internal const val DIAMOND_CH = "\uF075"

    internal const val TITLE_LEVEL_2 = "2"
    internal const val TITLE_LEVEL_3 = "3"
    internal const val TITLE_LEVEL_4 = "4"
    internal const val TITLE_LEVEL_5 = "5"

    const val TAG = "SaxManager"
    lateinit var handler: TransformerHandler
    lateinit var attr: AttributesImpl

    fun createXml(context: Context, note: ExportNoteData, path: String? = null, authority: String): String {
        try {
            AppLogger.BASIC.d(TAG, "createXml")
            val tff = SAXTransformerFactory.newInstance() as SAXTransformerFactory
            handler = tff.newTransformerHandler()
            val tf = handler.transformer
            tf.setOutputProperty(OutputKeys.ENCODING, DEFAULT_ENCODING)
            tf.setOutputProperty(OutputKeys.INDENT, DEFAULT_INDENT)
            val file = File(
                path
                    ?: "${DocUtils.getTempDirectory()}xml/${System.currentTimeMillis()}.xml"
            )
            if (!file.parentFile.exists()) {
                file.parentFile.mkdirs()
            }
            val result = StreamResult(FileOutputStream(file))
            handler.setResult(result)
            handler.startDocument()
            attr = AttributesImpl()
            ElementUtils.addAttr(
                attr, hashMapOf(
                    Attr.ATTR_DOCUMENT_ATTR_XMLNS_W to XMLNS_W,
                    Attr.ATTR_DOCUMENT_ATTR_XMLNS_R to XMLNS_R
                )
            )
            handler.startElement("", "", Tag.TAG_ROOT_DOCUMENT, attr)
            attr.clear()
            handler.startElement("", "", Tag.TAG_BODY, attr)

            parseRichData(context, note, authority)

            handler.endElement("", "", Tag.TAG_BODY)
            handler.endElement("", "", Tag.TAG_ROOT_DOCUMENT)
            handler.endDocument()

            return file.path
        } catch (error: TransformerFactoryConfigurationError) {
            AppLogger.BASIC.d(
                TAG,
                "create xml failed, reason[TransformerFactoryConfigurationError: ${error.message}]"
            )
            return ""
        } catch (transException: TransformerConfigurationException) {
            AppLogger.BASIC.d(
                TAG,
                "create xml failed, reason[TransformerConfigurationException: ${transException.message}]"
            )
            return ""
        } catch (exception: TransformerException) {
            AppLogger.BASIC.d(
                TAG,
                "create xml failed, reason[TransformerException: ${exception.message}]"
            )
            return ""
        }
    }

    fun parseRichData(context: Context, note: ExportNoteData, authority: String) {
        val wvExtraData = note.wvExtraData
        if (wvExtraData != null) {
            wvExtraData.rawTitle?.takeIf { it.isNotEmpty() }?.let {
                val titleParser = Jsoup.parse(it)
                val titleVisitor = RawTitleToDocVisitor(context, authority, handler, attr, note.localId, wvExtraData)
                NodeTraversor.traverse(titleVisitor, titleParser)
            }
            attr.clear()
            wvExtraData.rawText?.takeIf { it.isNotEmpty() }?.let {
                val root = Jsoup.parse(it)
                val visitor = RawTextToDocVisitor(context, authority, handler, attr, note.localId, wvExtraData)
                NodeTraversor.traverse(visitor, root.body())
            }
        } else {
            AppLogger.BASIC.e(TAG, "parseRichData failed, wvExtraData is null")
        }
    }
}