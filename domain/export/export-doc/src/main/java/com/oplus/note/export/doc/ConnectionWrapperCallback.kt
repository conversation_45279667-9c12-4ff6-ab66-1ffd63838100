/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ConnectionWrapperCallback.kt
 ** Description:
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

interface ConnectionWrapperCallback {
    fun onServiceConnect()
    fun onServiceDisconnect() {}
    fun onServiceConnectFailed(var1: Int)
}