/*********************************************************************************
 * Copyright (C), 2008-2019, Oplus, All rights reserved.
 *
 * File: - File: - ElementUtils.kt
 ** Description:
 **  Utils for sax element
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc.sax

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Bitmap
import android.net.Uri
import android.text.Layout
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.graphics.drawable.toBitmap
import com.oplus.note.export.doc.AIUNIT_PACKAGE_NAME
import com.oplus.note.logger.AppLogger
import org.xml.sax.helpers.AttributesImpl
import java.io.File
import java.io.FileOutputStream
import javax.xml.transform.sax.TransformerHandler

internal object ElementUtils {
    private const val TAG = "ElementUtils"
    private const val QUALITY = 100
    private const val DEFAULT_FILE_NAME = "/file_not_exist.png"
    private const val DEFAULT_FILE_NAME_NIGHT = "/file_not_exist_night.png"


    /**
     * create image element
     */
    @JvmStatic
    fun createImageElement(
        context: Context,
        handler: TransformerHandler,
        attr: AttributesImpl,
        path: String,
        authority: String,
        columns: Int
    ) {
        attr.clear()
        handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
        //set paragraph style
        createElementAttr(
            handler,
            attr,
            tagName = Tag.TAG_PARAGRAPH_ALIGNMENT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to Alignment.CENTER)
        )
        handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
        attr.clear()
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
        val imageUri = getImageUri(context, path, authority)
        //set image attr
        if (columns <= 1) {
            createElementAttr(
                handler, attr, tagName = Tag.TAG_MEDIA,
                hashMapOf(
                    Attr.TAG_PR_W_TYPE to Value.PNG,
                    Attr.ATTR_MEDIA_SCALE_PATTERN to Value.FIT,
                    Attr.ATTR_MEDIA_ALIGN to Alignment.LEFT,
                    Attr.ATTR_MEDIA_URL to imageUri.toString()
                )
            )
        } else {
            val w = "${Attr.WORD_MAX_WIDTH / columns}"
            createElementAttr(
                handler, attr, tagName = Tag.TAG_MEDIA,
                hashMapOf(
                    Attr.TAG_PR_W_TYPE to Value.PNG,
                    Attr.ATTR_MEDIA_SCALE_PATTERN to Value.NONE,
                    Attr.ATTR_MEDIA_ALIGN to Alignment.LEFT,
                    Attr.ATTR_MEDIA_URL to imageUri.toString(),
                    Attr.ATTR_WIDTH to w,
                    Attr.ATTR_HEIGHT to w,
                )
            )
        }

        handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
    }

    /**
     * 创建段落
     */
    @JvmStatic
    fun createParagraph(handler: TransformerHandler, attr: AttributesImpl) {
        handler.startElement("", "", Tag.TAG_PARAGRAPH_STYLE, attr)
        createElementAttr(
            handler, attr, tagName = Tag.TAG_PARAGRAPH_ALIGNMENT,
            hashMapOf(Attr.ATTR_COMMON_VALUE to Alignment.CENTER)
        )
        handler.endElement("", "", Tag.TAG_PARAGRAPH_STYLE)
    }

    /**
     * 创建小图媒体
     * @param imageUri 图片URI
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @return 包含媒体属性的HashMap
     */
    @JvmStatic
    fun createHalfMediaAttributes(
        imageUri: Uri,
        targetWidth: String,
        targetHeight: String,
        handler: TransformerHandler,
        attr: AttributesImpl
    ) {
        handler.startElement("", "", Tag.TAG_CONTENT_ROOT, attr)
        attr.clear()
        createElementAttr(
            handler, attr, tagName = Tag.TAG_MEDIA,
            hashMapOf(
                Attr.TAG_PR_W_TYPE to Value.PNG,
                Attr.ATTR_MEDIA_SCALE_PATTERN to Value.NONE,
                Attr.ATTR_MEDIA_ALIGN to Alignment.LEFT,
                Attr.ATTR_MEDIA_URL to imageUri.toString(),
                Attr.ATTR_WIDTH to targetWidth,
                Attr.ATTR_HEIGHT to targetHeight,
            )
        )
        handler.endElement("", "", Tag.TAG_CONTENT_ROOT)
    }

    @JvmStatic
    fun getImageUri(
        context: Context,
        path: String,
        authority: String
    ): Uri {
        val file = File(path)
        val imageUri = FileProvider.getUriForFile(
            context,
            authority,
            if (file.exists()) file else getDefaultFileIfAttachmentLoss(context)
        )
        AppLogger.BASIC.d(TAG, "createImageElement imageUri: $imageUri")

        val flag = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
        context.grantUriPermission(AIUNIT_PACKAGE_NAME, imageUri, flag)

        return imageUri
    }

    @JvmStatic
    private fun getDefaultFileIfAttachmentLoss(context: Context): File {
        val isNightMode = context.resources.configuration.uiMode and
                Configuration.UI_MODE_NIGHT_MASK == Configuration.UI_MODE_NIGHT_YES
        val destPath = context.filesDir.absolutePath + if (isNightMode) {
            DEFAULT_FILE_NAME_NIGHT
        } else {
            DEFAULT_FILE_NAME
        }
        return File(destPath).also {
            if (it.exists()) return@also
            val drawable = ContextCompat.getDrawable(context, com.oplus.note.baseres.R.drawable.file_not_exist)
            val bitmap = drawable?.toBitmap()
            runCatching {
                val fileOutputStream = FileOutputStream(File(destPath))
                fileOutputStream.use { fos ->
                    bitmap?.compress(Bitmap.CompressFormat.PNG, QUALITY, fos)
                    fos.flush()
                }
            }.onFailure { tr ->
                AppLogger.BASIC.e(TAG, "copy default file error: ${tr.message}")
            }
            bitmap?.recycle()
        }
    }


    @JvmStatic
    fun createElementAttr(
        handler: TransformerHandler,
        attr: AttributesImpl,
        tagName: String,
        map: HashMap<String, String>? = null
    ) {
        addAttr(attr, map)
        handler.startElement("", "", tagName, attr)
        handler.endElement("", "", tagName)
    }

    @JvmStatic
    fun createElementValue(
        handler: TransformerHandler,
        attr: AttributesImpl,
        tagName: String,
        value: String
    ) {
        attr.clear()
        handler.startElement("", "", tagName, attr)
        handler.characters(value.toCharArray(), 0, value.length)
        handler.endElement("", "", tagName)
    }

    @JvmStatic
    fun addAttr(attr: AttributesImpl, map: HashMap<String, String>? = null) {
        attr.clear()
        map?.forEach { attr.addAttribute("", "", it.key, "", it.value) }
    }

    @JvmStatic
    fun getAlignment(alignment: Layout.Alignment): String {
        return when (alignment) {
            Layout.Alignment.ALIGN_OPPOSITE -> Alignment.RIGHT
            Layout.Alignment.ALIGN_CENTER -> Alignment.CENTER
            else -> Alignment.LEFT
        }
    }

    @JvmStatic
    fun matchClasses(classString: String, requiredClasses: List<String>): Boolean {
        val classes = classString.split(" ")
        // 如果列表中任意一个不存在，返回 false
        return requiredClasses.all { it in classes }
    }
}