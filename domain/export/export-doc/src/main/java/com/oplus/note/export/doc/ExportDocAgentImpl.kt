/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportDocAgentImpl.kt
 ** Description:
 **  doc export agent implement
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/
package com.oplus.note.export.doc

import android.content.Context
import android.text.TextUtils
import com.oplus.note.export.DocShareNameInfo
import com.oplus.note.export.ExportProcessor
import com.oplus.note.export.doc.DocUtils.DOCX
import com.oplus.note.export.doc.DocUtils.constructFile
import com.oplus.note.export.doc.DocUtils.grantUriPermission
import com.oplus.note.export.doc.sax.SaxManager
import com.oplus.note.logger.AppLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

internal class ExportDocAgentImpl(val context: Context) : ExportDocAgent {
    companion object {
        private const val TAG = "ExportDocManager"
        private const val TEMP = ".tmp"
    }


    override suspend fun isSupport(context: Context, isFromNoteListDrag: Boolean): Boolean = withContext(Dispatchers.IO) {
        return@withContext ExportUnit.isSupportExport(context.applicationContext, isFromNoteListDrag)
    }

    override suspend fun getDetectDataState(context: Context): Boolean = withContext(Dispatchers.IO) {
        return@withContext ExportUnit.getDetectDataState(context.applicationContext) == AIUnitState.STATE_AVAILABLE
    }

    override fun updateNoteInfoIfNeed(context: Context, localId: String, updateTime: Long) {
        DocDeduplicateManager.updateNoteInfoIfNeed(context, localId, updateTime)
    }

    override fun isDeduplicateEnable(): Boolean {
        return !DocDeduplicateManager.DISABLED
    }

    /**
     * 将Note导出doc
     */
    @Synchronized
    override fun export(input: ExportDocData, doc: DocShareNameInfo, exportProcessor: ExportProcessor?): Pair<Int, String> {
        AppLogger.BASIC.d(TAG, "createDocFile start")
        if (input.isChanged) {
            DocDeduplicateManager.addChangedNote(input.data.localId)
        } else {
            DocDeduplicateManager.checkNoteInfoChanged(context, input.data.localId, input.data.updateTime) {
                return Pair(RESULT_SUCCESS, it)
            }
        }
        val docPath = docPathHandle(doc, input)
        val newestDocFile = constructFile(docPath)
        // 定义一个以NOTE+时间戳为名称作为xml名称和临时文件名称，避免名称中含有特殊字符，导出端找不到文件
        val dir = newestDocFile.parent?.let {
            it + File.separator
        } ?: DocUtils.getTempDirectory()
        val tempName = "${dir}${DocUtils.DOC_FILENAME_PREFFIX}_${System.currentTimeMillis()}"
        //1.create xml
        val path = SaxManager.createXml(
            context,
            input.data,
            "$tempName.xml",
            input.authority
        )
        if (path.isEmpty()) {
            AppLogger.BASIC.e(TAG, "createDocFile path empty")
            return Pair(RESULT_SAX_FAILURE, "")
        }

        val xmlFile = File(path)
        if (!xmlFile.exists()) {
            AppLogger.BASIC.e(TAG, "createDocFile xmlFile not exist")
            return Pair(RESULT_SAX_FAILURE, "")
        }

        val tmpDocFilePath = File("$tempName${DocUtils.DOCX}$TEMP")
        AppLogger.BASIC.d(TAG, "createDocFile export start")
        val result = ExportUnit.export(
            context,
            grantUriPermission(context, path, input.authority).toString(),
            grantUriPermission(context, tmpDocFilePath.absolutePath, input.authority).toString()
        )
        AppLogger.BASIC.d(TAG, "createDocFile export rs=$result")
        xmlFile.delete()
        return when (result) {
            ExportUnit.EXPORT_SUCCESS -> {
                val renameTo = tmpDocFilePath.renameTo(newestDocFile)
                AppLogger.BASIC.d(TAG, "createDocFile rename=$renameTo")
                DocDeduplicateManager.saveNoteInfo(context, input.data.localId, input.data.updateTime, docPath)
                Pair(RESULT_SUCCESS, docPath)
            }

            ExportUnit.EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD -> Pair(RESULT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD, "")
            else -> Pair(RESULT_FAILURE, "")
        }
    }

    private fun docPathHandle(
        doc: DocShareNameInfo,
        input: ExportDocData
    ): String = if (doc.isFromThird) {
        /**
         * 通话内容页分享和保存为word文档默认标题
         * 笔记有标题时：以笔记的标题和“通话内容”文案为默认命名；标题和通话内容间用空格分隔，格式为：默认标题 通话内容
         * 文件名示例：2023-11-17 1509 许飞 通话内容
         * 笔记无标题时：以文案“ 年-月-日 时分 联系人 通话内容 ”为笔记的默认命名
         * 文件名示例：2023-11-17 1509 许飞 通话内容
         * 通话内容支持多语言翻译
         * 联系人、年月日、通话内容3种元素间用空格分隔
         * 年月日时分的时间等于笔记生成的时间/-待定
         */
        input.docPath.ifEmpty {
            val mOldTitle = input.data.title.toString()
            val mNewTitle = if (mOldTitle.isNotBlank()) {
                mOldTitle
            } else {
                doc.personName + " " + doc.createTime
            }
            // 拼接标题
            val sb = StringBuilder(mNewTitle)
            sb.append(" ")
            sb.append(doc.nameStr)
            sb.toString()
            // 有重复文件时 拼接数字处理
            getDocPath(sb)
        }
    } else {
        // 笔记详情页doc分享命名处理
        input.docPath.ifEmpty {
            val title =  if (input.data.title.isNullOrBlank()) {
                DocUtils.getContentTopThirty(getFirstLineContent(input))
            } else {
                input.data.title.toString()
            }
            getDocPath(StringBuilder(title))
        }
    }

    private fun getDocPath(sb: StringBuilder): String {
        val name = DocUtils.getDocFileName(title = sb.toString(), fileNameSuffix = DOCX, docPath = File(DocUtils.getTempDirectory()))
        AppLogger.BASIC.d(TAG, "getDocPath return name=$name")
        val docxFile = DocUtils.getDocxFile(name)
        return docxFile.absolutePath
    }

    /**
     * 获得笔记内容第一行
     */
    private fun getFirstLineContent(input: ExportDocData): String {
        var content = ""
        val wvExtraData = input.data.wvExtraData
        if (wvExtraData != null) {
            content = wvExtraData.plainText?.split('\n')?.firstOrNull { it.isNotEmpty() } ?: ""
        } else {
            input.data.data?.let { data ->
                for ((_, exportDocType) in data.withIndex()) {
                    when (exportDocType) {
                        is ExportDocType.ExportDocText -> {
                            if (!TextUtils.isEmpty(exportDocType.text)) {
                                content = exportDocType.text.toString()
                                break
                            }
                        }

                        else -> {}
                    }
                }
            }
        }
        return content
    }
}