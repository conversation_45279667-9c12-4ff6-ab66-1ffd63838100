/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - File: - ExportUnit.kt
 ** Description:
 *
 * Version: 1.0
 * Date: 2023-08-17
 * Author: W9005794
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9005794                        2023-08-17    1.0    Create this module
 **********************************************************************************/

package com.oplus.note.export.doc

import android.content.Context
import androidx.annotation.WorkerThread
import com.oplus.aiunit.common.client.NoteExportClient
import com.oplus.aiunit.core.data.DetectName
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.download.api.AIDownload
import com.oplus.aiunit.download.api.CustomTerms
import com.oplus.aiunit.download.api.DownloadRequest
import com.oplus.aiunit.download.core.DownloadListener
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.note.logger.AppLogger

/**
 * Doc导出插件修改和集成的方式
 * https://odocs.myoas.com/docs/e1Az4VB5BrtROvqW/ 《DOC导出插件修改和更新流程》访问密码 xapdvy
 */
object ExportUnit {
    private const val TAG = "ExportUnit"
    const val EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD = -2
    private const val EXPORT_FAILURE = -1
    const val EXPORT_SUCCESS = 0
    private val downloadListener = object : DownloadListener {
        override fun onCancel() {
            AppLogger.BASIC.d(TAG, "AIDownload onCancel")
        }

        override fun onFail(err: Int) {
            AppLogger.BASIC.d(TAG, "AIDownload onFail err = $err")
        }

        override fun onInstall() {
            AppLogger.BASIC.d(TAG, "AIDownload onInstall")
        }

        override fun onPrepare(fullSize: Long, offsetSize: Long) {
            AppLogger.BASIC.d(TAG, "AIDownload onPrepare")
        }

        override fun onProgress(fullSize: Long, offsetSize: Long, speed: Long) {
            AppLogger.BASIC.d(TAG, "AIDownload onProgress")
        }

        override fun onStart(fullSize: Long, offsetSize: Long) {
            AppLogger.BASIC.d(TAG, "AIDownload onStart")
        }

        override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
            AppLogger.BASIC.d(TAG, "AIDownload onSuccess")
        }
    }

    /**
     * AIUnit技术文档
     * https://odocs.myoas.com/docs/loqeWm01PpUog1An?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
     * https://odocs.myoas.com/docs/m5kv9RJr0GIJd7qX?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
     */
    @Synchronized
    fun export(context: Context, xmlPath: String, docPath: String): Int {
        AppLogger.BASIC.w(TAG, "export star")
        val state = getDetectDataState(context)
        if (state == AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD) {
            AppLogger.BASIC.e(TAG, "createDocFile not available to support")
            startForAIDownload(context, false)
            return EXPORT_FAILURE_UNAVAILABLE_NEED_DOWNLOAD
        } else if (state == AIUnitState.STATE_UNAVAILABLE) {
            AppLogger.BASIC.e(TAG, "createDocFile not STATE_UNAVAILABLE")
            return EXPORT_FAILURE
        }
        val noteExportClient = NoteExportClient(context)
        val resultCode = noteExportClient.process(xmlPath, docPath)?.resultCode
        AppLogger.BASIC.d(TAG, "NoteExportClient.process resultCode = $resultCode")
        if (resultCode != 0) {
            return EXPORT_FAILURE
        }
        return EXPORT_SUCCESS
    }

    /**
     * 能力是否支持，能力支持也可能不可用
     */
    @WorkerThread
    fun isSupportExport(context: Context, isFromNoteListDrag: Boolean): Boolean {
        //根据能力状态获取本地定义的状态
        val detectDataState = getDetectDataState(context)
        AppLogger.BASIC.d(TAG, "detectDataState = $detectDataState")
        //判断能力是否需要下载
        if (detectDataState == AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD) {
            if (isFromNoteListDrag) {
                startForAIDownload(context, true)
                return false
            } else {
                return true
            }
        }
        return detectDataState == AIUnitState.STATE_AVAILABLE
    }

    @WorkerThread
    fun getDetectDataState(context: Context): Int {
        val detectData = AISettings.getDetectData(context, DetectName.NOTE_EXPORT_DOC)
        /**
         *     //能力不可用：设备不支持等原因，业务侧功能按需启用
         *     const val STATE_UNAVAILABLE = 0
         *     //能力可用
         *     const val STATE_AVAILABLE = 1
         *     //能力可用且是本地能力
         *     const val STATE_AVAILABLE_LOCAL = 2
         *     //能力可用且是云侧能力
         *     const val STATE_AVAILABLE_INTERNET = 3
         *     //能力可用且云侧有新版本可下载，后续下载版本会用到
         *     const val STATE_AVAILABLE_AND_NEW_DOWNLOAD = 4
         *     //能力不可用，原因是未联网
         *     const val STATE_UNAVAILABLE_NO_INTERNET = 5
         *     //能力不可用，原因是未下载
         *     const val STATE_UNAVAILABLE_NEED_DOWNLOAD = 6
         *     //能力不可用，原因是增强开关关闭，业务侧需按需调用AISettings
         *     const val STATE_UNAVAILABLE_USER_SWITCH_CLOSE = 7
         *     //能力不可用，原因是用户未申请，对应大模型未排队
         *     const val STATE_UNAVAILABLE_USER_NO_APPLY = 8
         *     //能力不可用，原因是申请失败，对应大模型排队审核未通过
         *     const val STATE_UNAVAILABLE_USER_APPLY_FAILED = 10
         *     //能力不用，原因是用户未授权，对应账号未登录
         *     const val STATE_UNAVAILABLE_USER_NO_AUTHORIZE = 11
         *     //能力不可用，原因是已下线，业务侧按需调用AISettings
         *     const val STATE_UNAVAILABLE_OFFLINE = 12
         *     //能力常规禁用
         *     const val STATE_UNAVAILABLE_DISABLE = 13
         *     //本地大模型开关未开启
         *     const val STATE_UNAVAILABLE_USER_SWITCH_CLOSE_LOCAL_LLM = 14
         *     //内存不足导致不可用
         *     const val STATE_UNAVAILABLE_LOW_MEMORY = 900
         *     //电量不足导致不可用
         *     const val STATE_UNAVAILABLE_LOW_BATTERY = 901
         *     //省电模式导致不可用
         *     const val STATE_UNAVAILABLE_POWER_SAVE_MODEL = 902
         *     //高负载导致不可用，如进入游戏场景
         *     const val STATE_UNAVAILABLE_OVERLOAD = 903
         *     //温度高导致不可用
         *     const val STATE_UNAVAILABLE_HIGH_TEMPERATURE = 904
         *     //云侧可用但用户强制设置了端侧导致云侧不可用
         *     const val STATE_UNAVAILABLE_WITH_INTERNET_BY_FORCE_LOCAL = 905
         *     //其他异常导致不可用
         *     const val STATE_UNAVAILABLE_EXCEPTION = 910
         */
        AppLogger.BASIC.d(TAG, "detectData.state = ${detectData.state}, configSize=${detectData.configList?.size}")
        detectData.configList?.forEach {
            AppLogger.BASIC.d(TAG, "config: $it")
        }
        return when (detectData.state) {
            UnitState.STATE_AVAILABLE,
            UnitState.STATE_AVAILABLE_LOCAL,
            UnitState.STATE_AVAILABLE_INTERNET -> AIUnitState.STATE_AVAILABLE

            UnitState.STATE_UNAVAILABLE_NEED_DOWNLOAD,
            UnitState.STATE_AVAILABLE_AND_NEW_DOWNLOAD -> AIUnitState.STATE_AVAILABLE_NEED_DOWNLOAD

            else -> AIUnitState.STATE_UNAVAILABLE
        }
    }

    /**
     * 下载AIUnit云端配置
     */
    private fun startForAIDownload(context: Context, isFromNoteListDrag: Boolean) {
        AIDownload.cancelByName(DetectName.NOTE_EXPORT_DOC)
        AIDownload.start(context.applicationContext, DownloadRequest().apply {
            detectName = DetectName.NOTE_EXPORT_DOC
            enableProgressUI = true
            enableProgressCallback = true
            downloadListener = <EMAIL>
            if (isFromNoteListDrag) {
                terms = CustomTerms().apply {
                    description = context.getString(com.oplus.note.baseres.R.string.note_list_drag_note_for_doc)
                }
            }
        })
    }
}

object AIUnitState {
    const val STATE_AVAILABLE = 0
    const val STATE_AVAILABLE_NEED_DOWNLOAD = 1
    const val STATE_UNAVAILABLE = 2
}