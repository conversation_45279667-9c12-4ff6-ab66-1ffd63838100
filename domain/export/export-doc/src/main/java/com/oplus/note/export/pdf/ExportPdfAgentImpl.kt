/**
 * Copyright (C), 2010-2023, OPLUS Mobile Comm Corp., Ltd.
 * File           : ExportPdfAgentImpl.kt
 * Description    : ExportPdfAgentImpl.kt
 * Version        : 1.0
 * Date           : 2025/7/28
 * Author         : 80262777
 *
 * ---------------------Revision History: ---------------------
 * <author>       <date>        <version >       <desc>
 * 80262777     2025/7/28         1.0           create
 */
package com.oplus.note.export.pdf

import android.content.Context
import android.text.TextUtils
import com.oplus.note.export.DocShareNameInfo
import com.oplus.note.export.ExportProcessor
import com.oplus.note.export.doc.DocDeduplicateManager
import com.oplus.note.export.doc.DocUtils
import com.oplus.note.export.doc.DocUtils.constructFile
import com.oplus.note.export.doc.ExportDocData
import com.oplus.note.export.doc.ExportDocType
import com.oplus.note.export.doc.ExportUnit
import com.oplus.note.export.doc.RESULT_FAILURE
import com.oplus.note.export.doc.RESULT_SUCCESS
import com.oplus.note.logger.AppLogger
import java.io.File

class ExportPdfAgentImpl(val context: Context) : ExportPdfAgent {
    companion object {
        const val TAG = "ExportPdfAgentImpl"
        const val PDF = ".pdf"
    }

    override fun updateNoteInfoIfNeed(
        context: Context,
        localId: String,
        updateTime: Long
    ) {
        DocDeduplicateManager.updateNoteInfoIfNeed(context, localId, updateTime)
    }

    override fun isDeduplicateEnable(): Boolean = false

    override fun export(
        input: ExportDocData,
        doc: DocShareNameInfo,
        exportProcessor: ExportProcessor?
    ): Pair<Int, String> {
        AppLogger.BASIC.d(TAG, "createPdfFile start")
        if (input.isChanged) {
            DocDeduplicateManager.addChangedNote(input.data.localId)
        } else {
            DocDeduplicateManager.checkNoteInfoChanged(
                context,
                input.data.localId,
                input.data.updateTime
            ) {
                return Pair(RESULT_SUCCESS, it)
            }
        }
        val pdfPath = pdfPathHandle(doc, input)
        val newestDocFile = constructFile(pdfPath)

        AppLogger.BASIC.d(TAG, "createPdfFile export start")
        val result = exportProcessor?.processExport(newestDocFile)
        AppLogger.BASIC.d(TAG, "createPdfFile export rs=$result")
        return when (result) {
            ExportUnit.EXPORT_SUCCESS -> {
                DocDeduplicateManager.saveNoteInfo(
                    context,
                    input.data.localId,
                    input.data.updateTime,
                    pdfPath
                )
                Pair(RESULT_SUCCESS, pdfPath)
            }
            else -> Pair(RESULT_FAILURE, "")
        }
    }

    override suspend fun isSupport(
        context: Context,
        isFromNoteListDrag: Boolean
    ): Boolean = true

    override suspend fun getDetectDataState(context: Context): Boolean = true


    private fun pdfPathHandle(
        doc: DocShareNameInfo,
        input: ExportDocData
    ): String = if (doc.isFromThird) {
        /**
         * 通话内容页分享和保存为word文档默认标题
         * 笔记有标题时：以笔记的标题和“通话内容”文案为默认命名；标题和通话内容间用空格分隔，格式为：默认标题 通话内容
         * 文件名示例：2023-11-17 1509 许飞 通话内容
         * 笔记无标题时：以文案“ 年-月-日 时分 联系人 通话内容 ”为笔记的默认命名
         * 文件名示例：2023-11-17 1509 许飞 通话内容
         * 通话内容支持多语言翻译
         * 联系人、年月日、通话内容3种元素间用空格分隔
         * 年月日时分的时间等于笔记生成的时间/-待定
         */
        input.docPath.ifEmpty {
            val mOldTitle = input.data.title.toString()
            val mNewTitle = if (mOldTitle.isNotBlank()) {
                mOldTitle
            } else {
                doc.personName + " " + doc.createTime
            }
            // 拼接标题
            val sb = StringBuilder(mNewTitle)
            sb.append(" ")
            sb.append(doc.nameStr)
            sb.toString()
            // 有重复文件时 拼接数字处理
            getPdfPath(sb)
        }
    } else {
        // 笔记详情页doc分享命名处理
        input.docPath.ifEmpty {
            val title =  if (input.data.title.isNullOrBlank()) {
                DocUtils.getContentTopThirty(getFirstLineContent(input))
            } else {
                input.data.title.toString()
            }
            getPdfPath(StringBuilder(title))
        }
    }

    /**
     * 获得笔记内容第一行
     */
    private fun getFirstLineContent(input: ExportDocData): String {
        var content = ""
        val wvExtraData = input.data.wvExtraData
        if (wvExtraData != null) {
            content = wvExtraData.plainText?.split('\n')?.firstOrNull { it.isNotEmpty() } ?: ""
        } else {
            input.data.data?.let { data ->
                for ((_, exportDocType) in data.withIndex()) {
                    when (exportDocType) {
                        is ExportDocType.ExportDocText -> {
                            if (!TextUtils.isEmpty(exportDocType.text)) {
                                content = exportDocType.text.toString()
                                break
                            }
                        }

                        else -> {}
                    }
                }
            }
        }
        return content
    }

    private fun getPdfPath(sb: StringBuilder): String {
        val name = DocUtils.getDocFileName(title = sb.toString(), fileNameSuffix = PDF, docPath = File(DocUtils.getTempDirectory()))
        AppLogger.BASIC.d(TAG, "getDocPath return name=$name")
        val docxFile = DocUtils.getDocxFile(name, PDF)
        return docxFile.absolutePath
    }
}