/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - GoogleApiInitializer
 ** Description:
 **         v1.0:   Create GoogleApiInitializer file
 **
 ** Version: 1.0
 ** Date: 2023/06/29
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/6/29   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.speech.google.api

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.startup.Initializer
import com.oplus.note.asr.ISpeechService
import com.oplus.note.logger.AppLogger

@RequiresApi(Build.VERSION_CODES.S)
class GoogleApiInitializer : Initializer<Boolean> {

    companion object {
        private const val TAG = "Notes.GoogleApiInitializer"
    }

    override fun create(context: Context): Boolean {
        AppLogger.BASIC.d(TAG, "register google client")
        com.oplus.note.asr.SpeechServiceAgentFactory.register(ISpeechService.SPEECH_GOOGLE, GoogleSpeechServiceImpl())
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        // No dependencies on other libraries.
        return mutableListOf()
    }
}