/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - LanguageConfigHelper
 ** Description:
 **         v1.0:  Read the language configured in the settings
 **
 ** Version: 1.0
 ** Date: 2023/07/04
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<EMAIL>       2023/7/4   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.speech.google.helper

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager.MATCH_DEFAULT_ONLY
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import com.oplus.note.logger.AppLogger
import com.oplus.note.osdk.adapter.OplusIntentAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object LanguageConfigHelper {

    private const val CHOSEN_LANGUAGE = "speech_to_text_chosen_language"
    private const val TAG = "LanguageConfigHelper"
    private const val CHOSEN_LANGUAGE_ACTION = "com.oplus.systemui.action_SPEECH_TO_TEXT_PAGE"
    private const val OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED = 0x10000000
    private const val OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY = 0x20000000
    private const val LANGUAGE_DEFAULT = "en-US"
    private const val EXTRA_DESCRIPTION = "oplus.intent.extra.DESCRIPTION"

    /**
     * Get the language configured in Settings-OutScreen Voice Input
     */
    @JvmStatic
    @VisibleForTesting
    suspend fun getSettingLanguage(context: Context): String? = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            val lang = Settings.Global.getString(context.contentResolver, CHOSEN_LANGUAGE)
            lang
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "getSettingLanguage :$it")
        }.onFailure {
            AppLogger.BASIC.d(TAG, "getSettingLanguage error.")
        }.getOrNull()
    }

    /**
     * Get the language set by the phone
     */
    @JvmStatic
    private fun getDefaultLanguage(context: Context): String {
        /**
         * val localeLang = Resources.getSystem().configuration.locales.get(0).toString()
         * AppLogger.BASIC.d(TAG, "localeLanguage:$localeLang")
         * */
        return LANGUAGE_DEFAULT
    }

    @JvmStatic
    suspend fun getSpeechLanguage(context: Context): String? {
        return if (isLanguageConfigAvailable(context)) {
            getSettingLanguage(context)
        } else {
            getDefaultLanguage(context)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    @JvmStatic
    fun startLanguageConfigActivity(context: Context, settingPrompt: String?) {
        runCatching {
            val intent = Intent().apply {
                action = CHOSEN_LANGUAGE_ACTION
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                OplusIntentAdapter.setOplusFlags(
                    this,
                    OPLUS_FLAG_ACTIVITY_CONTINUE_REQUIRED or OPLUS_FLAG_ACTIVITY_CONTINUE_PRIVACY
                )
                putExtra(EXTRA_DESCRIPTION, settingPrompt)
            }
            context.startActivity(intent)
        }.onFailure {
            AppLogger.BASIC.e(TAG, "start activity error:${it.message}")
        }
    }

    @JvmStatic
    fun isLanguageConfigAvailable(context: Context): Boolean {
        val intent = Intent(CHOSEN_LANGUAGE_ACTION)
        val resolveInfo = context.packageManager.resolveActivity(intent, MATCH_DEFAULT_ONLY)
        return resolveInfo != null
    }
}