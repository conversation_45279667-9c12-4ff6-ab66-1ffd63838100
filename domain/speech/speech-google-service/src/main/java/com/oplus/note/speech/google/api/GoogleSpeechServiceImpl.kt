/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - GoogleSpeechServiceImpl
 ** Description:
 **         v1.0:   Create GoogleSpeechServiceImpl file
 **
 ** Version: 1.0
 ** Date: 2023/06/20
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/6/20   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.speech.google.api

import android.content.Context
import android.os.Build
import android.speech.SpeechRecognizer
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleCoroutineScope
import com.nearme.note.util.AudioStreamWriter
import com.oplus.note.asr.ISpeechService
import com.oplus.note.asr.ISpeechServiceCallback
import com.oplus.note.logger.AppLogger
import com.oplus.note.speech.google.helper.LanguageConfigHelper
import com.oplus.osmartvoiceservicelib.asr.OSmartVoiceASRServiceCallback
import com.oplus.osmartvoiceservicelib.asr.OSmartVoiceASRServiceCallback.ASR_FINAL_RESULT
import com.oplus.osmartvoiceservicelib.asr.OSmartVoiceASRServiceSDK

@RequiresApi(Build.VERSION_CODES.S)
class GoogleSpeechServiceImpl : ISpeechService {

    @VisibleForTesting
    var oSmartVoiceServiceSDK: OSmartVoiceASRServiceSDK? = null

    @VisibleForTesting
    var speechServiceCallback: ISpeechServiceCallback? = null

    override fun getType(): Int {
        return ISpeechService.SPEECH_GOOGLE
    }

    override fun initService(
        context: Context,
        lifecycleScope: LifecycleCoroutineScope?,
        callback: ISpeechServiceCallback?,
        lang: String?,
        settingPrompt: String?,
        audioPaht: String?,
        streamWriter: AudioStreamWriter?
    ) {
        this.speechServiceCallback = callback
        AppLogger.BASIC.d(TAG, "speechLanguage:$lang")
        if (lang.isNullOrEmpty()) {
            LanguageConfigHelper.startLanguageConfigActivity(context, settingPrompt)
            AppLogger.BASIC.d(TAG, "not support $lang ,go to settings")
            return
        }
        kotlin.runCatching {
            oSmartVoiceServiceSDK =
                OSmartVoiceASRServiceSDK.createASRRecognizer(context) { type, value, result ->
                    handleStateChanged(speechServiceCallback, type, value, result)
                }
            oSmartVoiceServiceSDK?.setASRParam(LANGUAGE, lang)
            oSmartVoiceServiceSDK?.setAutoStop(false) //5s静音检测不生效
            oSmartVoiceServiceSDK?.startService()
        }.onFailure {
            AppLogger.BASIC.e(TAG, "init sst config error, ${it.message}")
            speechServiceCallback?.onStartServiceFailed(it.message)
        }.onSuccess {
            AppLogger.BASIC.d(TAG, "init service success")
            speechServiceCallback?.onStartServiceSuccess()
        }
    }

    override fun cancel(context: Context) {
        oSmartVoiceServiceSDK?.stopService()
    }

    override fun stop(context: Context) {
        oSmartVoiceServiceSDK?.stopService()
        speechServiceCallback?.onStopService()
    }

    override fun release(context: Context, lifecycleScope: LifecycleCoroutineScope?) {
        OSmartVoiceASRServiceSDK.releaseASRRecognizer()
        speechServiceCallback = null
    }

    override fun isEnable(context: Context): Boolean {
        //If this module is integrated, it is enable
        val isRecognitionAvailable = SpeechRecognizer.isRecognitionAvailable(context)
        AppLogger.BASIC.d(TAG, "isRecognitionAvailable:$isRecognitionAvailable")
        return isRecognitionAvailable
    }

    override fun isRecorderRunning(context: Context): Boolean {
        return false
    }

    override fun hasConnect(): Boolean {
        return false
    }

    override suspend fun getLanguage(context: Context): String? {
       return LanguageConfigHelper.getSpeechLanguage(context)
    }

    /**
     * @param callback callback to UI layer
     * @param type asr connect status
     * @param value asr content type
     * @param result text
     */
    @VisibleForTesting
    fun handleStateChanged(
        callback: ISpeechServiceCallback?,
        type: Int,
        value: Int,
        result: String?
    ) {

        AppLogger.DEBUG.d(TAG) { "handleStateChanged:$type , $value, $result" }
        when (type) {
            OSmartVoiceASRServiceCallback.ASR_RESULT_RECEIVED -> {
                if (value == ASR_FINAL_RESULT) {
                    callback?.onResult(result, true)
                    callback?.onFinish()
                } else {
                    callback?.onResult(result, false)
                }
            }

            OSmartVoiceASRServiceCallback.ASR_READY_FOR_SPEECH -> {
                AppLogger.SPEECH.d(TAG, "ASR_READY_FOR_SPEECH")
                callback?.onStartListen()
            }

            OSmartVoiceASRServiceCallback.ASR_WEB_CONNECTION_CLOSED -> {
                AppLogger.SPEECH.d(TAG, "ASR_WEB_CONNECTION_CLOSED")
                callback?.onError("web_connection_closed")
            }

            OSmartVoiceASRServiceCallback.ASR_WEB_CONNECTION_FAILED -> {
                AppLogger.SPEECH.d(TAG, "ASR_WEB_CONNECTION_FAILED")
                callback?.onError("connection_failed")
            }

            OSmartVoiceASRServiceCallback.ASR_SERVICE_CONNECTION_CLOSED -> {
                AppLogger.SPEECH.d(TAG, "ASR_SERVICE_CONNECTION_CLOSED")
                callback?.onError("service_connection_closed")
            }

            OSmartVoiceASRServiceCallback.ERROR_OCCURS -> {
                AppLogger.SPEECH.d(TAG, "ERROR_OCCURS")
                callback?.onError("error_occurs")
            }
        }
    }

    companion object {
        private const val TAG = "GoogleSpeechServiceImpl"
        private const val LANGUAGE = "stt.language"
    }
}