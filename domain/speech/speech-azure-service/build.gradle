plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.projectDir.path + '/config.gradle'
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note.speech.azure.api'

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {

    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.appcompat:appcompat:${appcompat}"

//    implementation project(path: ':speech-wrapper')
    implementation project(path: ':common:lib_base')
    implementation project(path: ':common:logger:logger-api')
    implementation project(path: ':common:lib_api')
    implementation project(path: ':common:osdk-proxy')

    //Dependencies need to be reconfigured after componentization
//    implementation project(path: ':speech-wrapper:speech-azure')
    implementation project(':domain:speech:speech-api')
    implementation "androidx.startup:startup-runtime:$startup"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "com.oplus.coreapp.appfeature:AppFeatureHelper:$app_feature_version"

    implementation 'com.squareup.okhttp3:okhttp:4.9.0'
    implementation "com.google.code.gson:gson:${gson}"
    implementation 'commons-codec:commons-codec:1.10'
}