/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - ISpeechService
 ** Description:
 **         v1.0:   Speech recognition interface without UI
 **
 ** Version: 1.0
 ** Date: 2023/02/15
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/15   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.asr

import android.content.Context
import androidx.annotation.WorkerThread
import androidx.lifecycle.LifecycleCoroutineScope
import com.nearme.note.util.AudioStreamWriter

interface ISpeechService {

    fun getType(): Int

    fun initService(
        context: Context,
        lifecycleScope: LifecycleCoroutineScope?,
        callback: ISpeechServiceCallback?,
        lang: String?,
        settingPrompt: String?,
        audioPath: String?,
        streamWriter: AudioStreamWriter?
    )

    fun cancel(context: Context)

    fun stop(context: Context)

    fun release(context: Context, lifecycleScope: LifecycleCoroutineScope?)

    fun isEnable(context: Context): Boolean

    fun isRecorderRunning(context: Context): Boolean

    fun hasConnect(): Boolean

    suspend fun getLanguage(context: Context): String?

    companion object {
        const val SPEECH_BREENO = 1
        const val SPEECH_AZURE = 2
        const val SPEECH_GOOGLE = 3
        const val SPEECH_LANGUAGE = "language"
        const val SPEECH_LANGUAGE_CHINESE = "chinese"
        const val SPEECH_LANGUAGE_ENGLISH = "english"
    }
}