/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - `SpeechServiceCallback()`
 ** Description:
 **         v1.0:   Create `SpeechServiceCallback()` file
 **
 ** Version: 1.0
 ** Date: 2023/02/16
 ** Author: Jiep<PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/16   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.asr

interface ISpeechServiceCallback {

    fun onStartService()

    //Binding service error or exception
    fun onStartServiceFailed(msg: String? = null)

    //start recording or ars return start
    fun onStartServiceSuccess()

    fun onStartListen()

    fun onError(msg: String?, isNetworkError: Boolean = false)

    fun onResult(result: String?, isFinal: Boolean)

    fun onFinish()

    fun onStopService()

    fun onResultAmplitudes(amplitude: Int)
}