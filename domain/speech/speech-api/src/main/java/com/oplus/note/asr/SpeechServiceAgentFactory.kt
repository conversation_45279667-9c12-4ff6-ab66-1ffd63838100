/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - SpeechServiceAgentFactory
 ** Description:
 **         v1.0:   Create SpeechServiceAgentFactory file
 **
 ** Version: 1.0
 ** Date: 2023/02/16
 ** Author: Ji<PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/16   1.0      Create this module
 ********************************************************************************/
package com.oplus.note.asr

import android.content.Context
import com.oplus.note.logger.AppLogger

object SpeechServiceAgentFactory {

    private const val TAG = "SpeechServiceAgentFactory"
    private var agent: ISpeechService? = null
    private val agents: HashMap<Int, ISpeechService> = HashMap()

    /**
     * initServices后的调用方法
     */
    fun get(): ISpeechService? {
        return agent
    }


    /**
     * 获取外屏初始化的google服务
     */
    fun getGoogleService(): ISpeechService? {
        agent = agents[ISpeechService.SPEECH_GOOGLE]
        if (agent == null) {
            AppLogger.BASIC.d(TAG, "agent is null")
        }
        return agent
    }

    /**
     * initService的调用方法
     */
    fun get(type: Int): ISpeechService? {
        agent = agents[type]
        AppLogger.BASIC.d(TAG, "agent is :$agent")
        return agent
    }

    fun register(type: Int, agent: ISpeechService) {
        if (agent != null) {
            agents[type] = agent
        }
    }

    fun isSupportGoogle(context: Context?): Boolean {
        agents.forEach {
            context?.let { mContext ->
                if (it.key == ISpeechService.SPEECH_GOOGLE) {
                    val isEnable = it.value.isEnable(mContext)
                    AppLogger.BASIC.d(TAG, "isSupportGoogle isEnable:$isEnable")
                    return isEnable
                }
            }
        }
        return false
    }

    fun isAzure(): Boolean {
        agents.forEach {
            if (it.key == ISpeechService.SPEECH_AZURE) {
                AppLogger.BASIC.d(TAG, "agents key:${it.key}")
                return true
            }
        }
        return false
    }

    fun isBreeno(): Boolean {
        agents.forEach {
            if (it.key == ISpeechService.SPEECH_BREENO) {
                AppLogger.BASIC.d(TAG, "agents key:${it.key}")
                return true
            }
        }
        return false
    }

    fun isGoogle(): Boolean {
        agents.forEach {
            if (it.key == ISpeechService.SPEECH_GOOGLE) {
                AppLogger.BASIC.d(TAG, "agents key:${it.key}")
                return true
            }
        }
        return false
    }

    /**
     * breeno和Azure互斥。次判断只会有一个在其中生效
     */
    fun isSupportBreenoOrAzure(context: Context?): Boolean {
        agents.forEach {
            context?.let { mContext ->
                if (it.key == ISpeechService.SPEECH_BREENO || it.key == ISpeechService.SPEECH_AZURE) {
                    val isEnable = it.value.isEnable(mContext)
                    AppLogger.BASIC.d(TAG, "isEnable:$isEnable")
                    return isEnable
                }
            }
        }
        return false
    }

    /**
     * 判断当前breeno或者azure是否已连接
     */
    fun hasConnectOfBreenoOrAzure(): Boolean {
        agents.forEach {
            if (it.key == ISpeechService.SPEECH_BREENO || it.key == ISpeechService.SPEECH_AZURE) {
                val hasConnect = it.value.hasConnect()
                AppLogger.BASIC.d(TAG, "hasConnect:$hasConnect")
                return hasConnect
            }
        }
        return false
    }
}