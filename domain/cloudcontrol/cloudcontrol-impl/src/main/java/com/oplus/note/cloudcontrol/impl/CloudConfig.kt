/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudConfig.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/10
 ** Author: W9028045
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2024/4/10      1.0     create file
 ****************************************************************/
package com.oplus.note.cloudcontrol.impl

import com.oplus.note.cloudcontrol.repository.AiConfigDataOneplusExportService
import com.oplus.note.cloudcontrol.repository.AiConfigDataService
import com.oplus.note.cloudcontrol.repository.AppConfigDataOneplusExportService
import com.oplus.note.cloudcontrol.repository.AppConfigDataService

internal object CloudConfig {
    const val RETRY_COUNT = 3
    const val RETRY_FREQUENCY = 5000L
    const val CONFIG_ID = "ai_config"
    const val CONFIG_APP_ID = "app_config"
    const val CONFIG_ID_OP_EXPORT = "ai_config_oneplus_export"
    const val CONFIG_APP_ID_OP_EXPORT = "app_config_oneplus_export"
    private const val PRODUCT_ID = "mdp_1612"
    private const val PRODUCT_ID_OP_EXPORT = "mdp_1727"

    @JvmStatic
    fun getProductId(isExport: Boolean, isOnePlus: Boolean): String {
        return if (isExport && isOnePlus) {
            PRODUCT_ID_OP_EXPORT
        } else {
            PRODUCT_ID
        }
    }

    @JvmStatic
    fun getAiConfig(isExport: Boolean, isOnePlus: Boolean): Class<out Any> {
        return if (isExport && isOnePlus) {
            AiConfigDataOneplusExportService::class.java
        } else {
            AiConfigDataService::class.java
        }
    }

    @JvmStatic
    fun getAppConfig(isExport: Boolean, isOnePlus: Boolean): Class<out Any> {
        return if (isExport && isOnePlus) {
            AppConfigDataOneplusExportService::class.java
        } else {
            AppConfigDataService::class.java
        }
    }
}