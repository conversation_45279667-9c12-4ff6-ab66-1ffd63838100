/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - RetryLimitCodeEntity
 ** Description: RetryLimitCodeEntity
 ** Version: 1.0
 ** Date : 2024/5/10
 ** Author: W9028045
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9028045    2024/5/10    1.0     build this module
 ****************************************************************/
package com.oplus.note.cloudcontrol.repository;

import androidx.annotation.NonNull;

import com.heytap.nearx.cloudconfig.anotation.FieldIndex;

public class AiConfigEntity {
    @FieldIndex(index = 1)
    public String key = "";

    @FieldIndex(index = 2)
    public String value = "";

    @NonNull
    @Override
    public String toString() {
        return "AiConfigEntity{"
                + "key=" + key
                + ", value=" + value
                + '}';
    }
}
