/***********************************************************
** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
** File: - LogSalvageInitializer.kt
** Description:
** Version: 1.0
** Date : 2024/6/10
** Author: W9028045
**
** ---------------------Revision History: ---------------------
**  <author> <data>   <version >    <desc>
**  AAA       2024/4/10      1.0     create file
****************************************************************/
package com.oplus.note.cloudcontrol.impl

import android.content.Context
import androidx.startup.Initializer
import com.oplus.note.cloudcontrol.api.CloudControlAgentFactory
import com.oplus.note.logger.AppLogger

internal class CloudControlInitializer : Initializer<Boolean> {

    companion object {
        private const val TAG = "CloudControlInitializer"
    }

    override fun create(context: Context): Boolean {
        CloudControlAgentFactory.register(CloudControlAgentImpl())
        AppLogger.BASIC.d(TAG, "register CloudControlAgentImpl")
        return true
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> {
        return mutableListOf()
    }
}