/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - AiConfigDataOneplusExportService
 ** Description:
 **         v1.0:   Create AiConfigDataOneplusExportService file
 **
 ** Version: 1.0
 ** Date: 2024/08/14
 ** Author: <PERSON><PERSON><PERSON>.Yan
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON><PERSON>.Yan              2024/8/14        1.0      Create this module
 ********************************************************************************/
package com.oplus.note.cloudcontrol.repository;

import com.heytap.nearx.cloudconfig.anotation.Config;
import com.heytap.nearx.cloudconfig.observable.Observable;

import java.util.List;

@Config(configCode = "app_config_oneplus_export")
public interface AppConfigDataOneplusExportService {
    public Observable<List<AppConfigEntity>> fetchData();

}