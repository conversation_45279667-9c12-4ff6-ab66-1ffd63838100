/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - CloudControlAgentImpl.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/4/10
 ** Author: W9028045
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  AAA       2024/4/10      1.0     create file
 ****************************************************************/
package com.oplus.note.cloudcontrol.impl

import android.content.Context
import com.heytap.common.LogLevel
import com.heytap.nearx.cloudconfig.CloudConfigCtrl
import com.heytap.nearx.cloudconfig.DynamicAreaHost
import com.heytap.nearx.cloudconfig.Env
import com.heytap.nearx.cloudconfig.api.ConfigParser
import com.heytap.nearx.cloudconfig.device.ApkBuildInfo
import com.heytap.nearx.cloudconfig.observable.Observable
import com.heytap.nearx.cloudconfig.observable.Scheduler
import com.heytap.nearx.cloudconfig.retry.CustomRetryPolicy
import com.heytap.nearx.cloudconfig.stat.Const
import com.oplus.note.base.BuildConfig
import com.oplus.note.cloudcontrol.api.CloudControlAgent
import com.oplus.note.cloudcontrol.repository.AiConfigDataOneplusExportService
import com.oplus.note.cloudcontrol.repository.AiConfigDataService
import com.oplus.note.cloudcontrol.repository.AiConfigEntity
import com.oplus.note.cloudcontrol.repository.AppConfigDataOneplusExportService
import com.oplus.note.cloudcontrol.repository.AppConfigDataService
import com.oplus.note.cloudcontrol.repository.AppConfigEntity
import com.oplus.note.logger.AppLogger
import com.oplus.note.utils.SharedPreferencesUtil
import java.util.concurrent.TimeUnit
import kotlin.math.abs

/**
 * 云控那边更新数据的流程：
 * （1）在init的时候会主动去云端拉一次最新的配置数据。如果有多个配置项，则在init的时候通过defaultConfigs一次性配置，
 *     这样可以只用一次请求就拉到所有配置项的最新数据。
 * （2）调用fetchData()的时候只会去本地拿缓存数据，不会请求云端
 * （3）调用fetchData()的时候如果不需要随时关心数据的更新，则subscribe后要dispose，避免被意外的拉起
 *
 * 云控的配置信息细节参考：https://odocs.myoas.com/docs/0l3NVK6pPBUl2O3R/ 《便签云控配置细节》访问密码 ccptvu
 */
internal class CloudControlAgentImpl : CloudControlAgent, ConfigParser {

    companion object {
        private const val TAG = "CloudControlAgentImpl"
        private const val SP_CLOUD_CONFIG_LAST_REQUEST_TIME = "cloud_config_last_request_time"
        private const val ONE_DAY = 1L
    }

    private var mCloudConfigCtrl: CloudConfigCtrl? = null
    private var aiDataLoadCompleted = false
    private var appDataLoadCompleted = false

    override fun configInfo(service: Class<*>): Pair<String, Int> {
        return when (service) {
            AiConfigDataService::class.java -> CloudConfig.CONFIG_ID to Const.CONFIG_TYPE_DATABASE
            AppConfigDataService::class.java -> CloudConfig.CONFIG_APP_ID to Const.CONFIG_TYPE_DATABASE
            AiConfigDataOneplusExportService::class.java -> CloudConfig.CONFIG_ID_OP_EXPORT to Const.CONFIG_TYPE_DATABASE
            AppConfigDataOneplusExportService::class.java -> CloudConfig.CONFIG_APP_ID_OP_EXPORT to Const.CONFIG_TYPE_DATABASE
            else -> Pair("", Const.CONFIG_TYPE_DATABASE)
        }
    }

    /**
     * 初始化时会从云端拉一次最新的数据
     */
    private fun init(context: Context, isExport: Boolean, isOnePlus: Boolean) {
        // 日志控制台输出级别，应用发布release版本得设置为LogLevel.NONE
        AppLogger.BASIC.d(TAG, "init export:$isExport, oneplus:$isOnePlus")
        if (mCloudConfigCtrl == null) {
            var env = Env.RELEASE
            var logLevel = LogLevel.LEVEL_NONE
            if (BuildConfig.DEBUG) {
                env = Env.TEST
                logLevel = LogLevel.LEVEL_VERBOSE
            }
            mCloudConfigCtrl = CloudConfigCtrl.Builder()
                .productId(CloudConfig.getProductId(isExport, isOnePlus)) // 是否测试环境  or 开发环境Env.RELEASE
                .apiEnv(env)
                .setBuildInfo(
                    ApkBuildInfo("", "", "")
                ) // 设置日志级别
                .logLevel(logLevel)
                //设置动态域名
                .areaHost(DynamicAreaHost())
                .setRetryPolicy(
                    CustomRetryPolicy(
                        CloudConfig.RETRY_COUNT,
                        CloudConfig.RETRY_FREQUENCY
                    )
                )
                .setProcessName(context.packageName)
                .defaultConfigs(this, CloudConfig.getAiConfig(isExport, isOnePlus), CloudConfig.getAppConfig(isExport, isOnePlus)) // 在这儿配置多个Service
                .build(context)
        }
    }

    override fun fetchCloudDataIfNeed(context: Context, isExport: Boolean, isOnePlus: Boolean, listener: ((data: MutableList<*>) -> Unit)?) {
        val lastCloudControlRequestTime = SharedPreferencesUtil.getInstance().getLong(
            context.applicationContext, SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SP_CLOUD_CONFIG_LAST_REQUEST_TIME, 0
        )
        val currentTime = System.currentTimeMillis()
        val timeInterval = currentTime - lastCloudControlRequestTime
        val timeIntervalToDay = TimeUnit.MILLISECONDS.toDays(abs(timeInterval))
        val intervalOneDay = timeIntervalToDay >= ONE_DAY
        if (intervalOneDay) {
            init(context, isExport, isOnePlus)
            aiDataLoadCompleted = false
            appDataLoadCompleted = false
            fetchAiCloudData(context, isExport, isOnePlus, listener)
            fetchAppCloudData(context, isExport, isOnePlus, listener)
        }
    }

    /**
     * 只会从本地缓存中拉数据，不会请求云端
     */
    private fun fetchAiCloudData(context: Context, isExport: Boolean, isOnePlus: Boolean, listener: ((data: MutableList<*>) -> Unit)?) {
        val appContext = context.applicationContext
        mCloudConfigCtrl?.let { cloudConfigCtrl ->
            val configClass = CloudConfig.getAiConfig(isExport, isOnePlus)
            AppLogger.BASIC.d(TAG, "cloud data configClass:$configClass")
            var observable: Observable<List<AiConfigEntity>>? = null
            if (configClass == AiConfigDataService::class.java) {
                observable = cloudConfigCtrl.create(
                    AiConfigDataService::class.java, CloudConfig.CONFIG_ID, Const.CONFIG_TYPE_DATABASE
                ).fetchData()
            } else if (configClass == AiConfigDataOneplusExportService::class.java) {
                observable = cloudConfigCtrl.create(
                    AiConfigDataOneplusExportService::class.java, CloudConfig.CONFIG_ID_OP_EXPORT, Const.CONFIG_TYPE_DATABASE
                ).fetchData()
            }

            observable?.observeOn(Scheduler.io())?.subscribe({ dataList ->
                AppLogger.BASIC.d(TAG, "cloud data ai result:${dataList.size}")
                aiDataLoadCompleted = true
                checkSaveTime(appContext)
                listener?.invoke(dataList.toMutableList())
            }, { throwable ->
                AppLogger.BASIC.d(TAG, "cloud data ai error:$throwable")
            })?.dispose() // 不需要一直订阅数据变化，subscribe结束后就dispose
        }
    }

    /**
     * 拉取app相关的配置信息
     */
    private fun fetchAppCloudData(context: Context, isExport: Boolean, isOnePlus: Boolean, listener: ((data: MutableList<*>) -> Unit)?) {
        val appContext = context.applicationContext
        mCloudConfigCtrl?.let { cloudConfigCtrl ->
            val appConfigClass = CloudConfig.getAppConfig(isExport, isOnePlus)
            AppLogger.BASIC.d(TAG, "cloud data appConfigClass:$appConfigClass")
            var appObservable: Observable<List<AppConfigEntity>>? = null
            if (appConfigClass == AppConfigDataService::class.java) {
                appObservable = cloudConfigCtrl.create(
                    AppConfigDataService::class.java, CloudConfig.CONFIG_APP_ID, Const.CONFIG_TYPE_DATABASE
                ).fetchData()
            } else if (appConfigClass == AppConfigDataOneplusExportService::class.java) {
                appObservable = cloudConfigCtrl.create(
                    AppConfigDataOneplusExportService::class.java, CloudConfig.CONFIG_APP_ID_OP_EXPORT, Const.CONFIG_TYPE_DATABASE
                ).fetchData()
            }

            appObservable?.observeOn(Scheduler.io())?.subscribe({ dataList ->
                AppLogger.BASIC.d(TAG, "cloud data app result:${dataList.size}")
                appDataLoadCompleted = true
                checkSaveTime(appContext)
                listener?.invoke(dataList.toMutableList())
            }, { throwable ->
                AppLogger.BASIC.d(TAG, "cloud data app error:$throwable")
            })?.dispose() // 不需要一直订阅数据变化，subscribe结束后就dispose
        }
    }

    private fun checkSaveTime(context: Context) {
        AppLogger.BASIC.d(TAG, "checkSaveTime aiData=$aiDataLoadCompleted,appData=$appDataLoadCompleted")

        if (aiDataLoadCompleted && appDataLoadCompleted) {
            SharedPreferencesUtil.getInstance().putLong(
                context, SharedPreferencesUtil.SHARED_PREFERENCES_NAME, SP_CLOUD_CONFIG_LAST_REQUEST_TIME, System.currentTimeMillis()
            )
        }
    }
}