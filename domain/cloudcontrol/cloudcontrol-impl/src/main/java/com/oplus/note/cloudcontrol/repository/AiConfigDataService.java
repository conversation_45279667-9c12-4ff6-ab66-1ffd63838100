/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - RetryLimitCodeDataService
 ** Description: RetryLimitCodeDataService
 ** Version: 1.0
 ** Date : 2024/5/10
 ** Author: W9028045
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9028045    2024/5/10    1.0     build this module
 ****************************************************************/
package com.oplus.note.cloudcontrol.repository;

import com.heytap.nearx.cloudconfig.anotation.Config;
import com.heytap.nearx.cloudconfig.observable.Observable;

import java.util.List;

@Config(configCode = "ai_config")
public interface AiConfigDataService {
    public Observable<List<AiConfigEntity>> fetchData();
}
